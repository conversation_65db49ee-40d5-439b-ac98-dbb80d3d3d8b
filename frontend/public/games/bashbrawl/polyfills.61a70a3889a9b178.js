"use strict";(self.webpackChunk_jggoebel_bashbrawl=self.webpackChunk_jggoebel_bashbrawl||[]).push([[461],{50:(ie,Ee,ce)=>{ce(876),ce(935)},876:()=>{window.__Zone_disable_addEventListener=!0,window.__Zone_disable_on_property=!0,window.__zone_symbol__UNPATCHED_EVENTS=["scroll","mousemove","click","load"],window.__Zone_enable_cross_context_check=!0},935:()=>{!function(e){const n=e.performance;function i(L){n&&n.mark&&n.mark(L)}function o(L,T){n&&n.measure&&n.measure(L,T)}i("Zone");const c=e.__Zone_symbol_prefix||"__zone_symbol__";function a(L){return c+L}const y=!0===e[a("forceDuplicateZoneCheck")];if(e.Zone){if(y||"function"!=typeof e.Zone.__symbol__)throw new Error("Zone already loaded.");return e.Zone}let d=(()=>{class L{static{this.__symbol__=a}static assertZonePatched(){if(e.Promise!==oe.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let t=L.current;for(;t.parent;)t=t.parent;return t}static get current(){return F.zone}static get currentTask(){return re}static __load_patch(t,r,k=!1){if(oe.hasOwnProperty(t)){if(!k&&y)throw Error("Already loaded patch: "+t)}else if(!e["__Zone_disable_"+t]){const C="Zone:"+t;i(C),oe[t]=r(e,L,q),o(C,C)}}get parent(){return this._parent}get name(){return this._name}constructor(t,r){this._parent=t,this._name=r?r.name||"unnamed":"<root>",this._properties=r&&r.properties||{},this._zoneDelegate=new v(this,this._parent&&this._parent._zoneDelegate,r)}get(t){const r=this.getZoneWith(t);if(r)return r._properties[t]}getZoneWith(t){let r=this;for(;r;){if(r._properties.hasOwnProperty(t))return r;r=r._parent}return null}fork(t){if(!t)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,t)}wrap(t,r){if("function"!=typeof t)throw new Error("Expecting function got: "+t);const k=this._zoneDelegate.intercept(this,t,r),C=this;return function(){return C.runGuarded(k,this,arguments,r)}}run(t,r,k,C){F={parent:F,zone:this};try{return this._zoneDelegate.invoke(this,t,r,k,C)}finally{F=F.parent}}runGuarded(t,r=null,k,C){F={parent:F,zone:this};try{try{return this._zoneDelegate.invoke(this,t,r,k,C)}catch($){if(this._zoneDelegate.handleError(this,$))throw $}}finally{F=F.parent}}runTask(t,r,k){if(t.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(t.zone||K).name+"; Execution: "+this.name+")");if(t.state===x&&(t.type===Q||t.type===w))return;const C=t.state!=E;C&&t._transitionTo(E,A),t.runCount++;const $=re;re=t,F={parent:F,zone:this};try{t.type==w&&t.data&&!t.data.isPeriodic&&(t.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,t,r,k)}catch(l){if(this._zoneDelegate.handleError(this,l))throw l}}finally{t.state!==x&&t.state!==h&&(t.type==Q||t.data&&t.data.isPeriodic?C&&t._transitionTo(A,E):(t.runCount=0,this._updateTaskCount(t,-1),C&&t._transitionTo(x,E,x))),F=F.parent,re=$}}scheduleTask(t){if(t.zone&&t.zone!==this){let k=this;for(;k;){if(k===t.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${t.zone.name}`);k=k.parent}}t._transitionTo(X,x);const r=[];t._zoneDelegates=r,t._zone=this;try{t=this._zoneDelegate.scheduleTask(this,t)}catch(k){throw t._transitionTo(h,X,x),this._zoneDelegate.handleError(this,k),k}return t._zoneDelegates===r&&this._updateTaskCount(t,1),t.state==X&&t._transitionTo(A,X),t}scheduleMicroTask(t,r,k,C){return this.scheduleTask(new p(I,t,r,k,C,void 0))}scheduleMacroTask(t,r,k,C,$){return this.scheduleTask(new p(w,t,r,k,C,$))}scheduleEventTask(t,r,k,C,$){return this.scheduleTask(new p(Q,t,r,k,C,$))}cancelTask(t){if(t.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(t.zone||K).name+"; Execution: "+this.name+")");if(t.state===A||t.state===E){t._transitionTo(V,A,E);try{this._zoneDelegate.cancelTask(this,t)}catch(r){throw t._transitionTo(h,V),this._zoneDelegate.handleError(this,r),r}return this._updateTaskCount(t,-1),t._transitionTo(x,V),t.runCount=0,t}}_updateTaskCount(t,r){const k=t._zoneDelegates;-1==r&&(t._zoneDelegates=null);for(let C=0;C<k.length;C++)k[C]._updateTaskCount(t.type,r)}}return L})();const b={name:"",onHasTask:(L,T,t,r)=>L.hasTask(t,r),onScheduleTask:(L,T,t,r)=>L.scheduleTask(t,r),onInvokeTask:(L,T,t,r,k,C)=>L.invokeTask(t,r,k,C),onCancelTask:(L,T,t,r)=>L.cancelTask(t,r)};class v{constructor(T,t,r){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this.zone=T,this._parentDelegate=t,this._forkZS=r&&(r&&r.onFork?r:t._forkZS),this._forkDlgt=r&&(r.onFork?t:t._forkDlgt),this._forkCurrZone=r&&(r.onFork?this.zone:t._forkCurrZone),this._interceptZS=r&&(r.onIntercept?r:t._interceptZS),this._interceptDlgt=r&&(r.onIntercept?t:t._interceptDlgt),this._interceptCurrZone=r&&(r.onIntercept?this.zone:t._interceptCurrZone),this._invokeZS=r&&(r.onInvoke?r:t._invokeZS),this._invokeDlgt=r&&(r.onInvoke?t:t._invokeDlgt),this._invokeCurrZone=r&&(r.onInvoke?this.zone:t._invokeCurrZone),this._handleErrorZS=r&&(r.onHandleError?r:t._handleErrorZS),this._handleErrorDlgt=r&&(r.onHandleError?t:t._handleErrorDlgt),this._handleErrorCurrZone=r&&(r.onHandleError?this.zone:t._handleErrorCurrZone),this._scheduleTaskZS=r&&(r.onScheduleTask?r:t._scheduleTaskZS),this._scheduleTaskDlgt=r&&(r.onScheduleTask?t:t._scheduleTaskDlgt),this._scheduleTaskCurrZone=r&&(r.onScheduleTask?this.zone:t._scheduleTaskCurrZone),this._invokeTaskZS=r&&(r.onInvokeTask?r:t._invokeTaskZS),this._invokeTaskDlgt=r&&(r.onInvokeTask?t:t._invokeTaskDlgt),this._invokeTaskCurrZone=r&&(r.onInvokeTask?this.zone:t._invokeTaskCurrZone),this._cancelTaskZS=r&&(r.onCancelTask?r:t._cancelTaskZS),this._cancelTaskDlgt=r&&(r.onCancelTask?t:t._cancelTaskDlgt),this._cancelTaskCurrZone=r&&(r.onCancelTask?this.zone:t._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;const k=r&&r.onHasTask;(k||t&&t._hasTaskZS)&&(this._hasTaskZS=k?r:b,this._hasTaskDlgt=t,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=T,r.onScheduleTask||(this._scheduleTaskZS=b,this._scheduleTaskDlgt=t,this._scheduleTaskCurrZone=this.zone),r.onInvokeTask||(this._invokeTaskZS=b,this._invokeTaskDlgt=t,this._invokeTaskCurrZone=this.zone),r.onCancelTask||(this._cancelTaskZS=b,this._cancelTaskDlgt=t,this._cancelTaskCurrZone=this.zone))}fork(T,t){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,T,t):new d(T,t)}intercept(T,t,r){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,T,t,r):t}invoke(T,t,r,k,C){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,T,t,r,k,C):t.apply(r,k)}handleError(T,t){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,T,t)}scheduleTask(T,t){let r=t;if(this._scheduleTaskZS)this._hasTaskZS&&r._zoneDelegates.push(this._hasTaskDlgtOwner),r=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,T,t),r||(r=t);else if(t.scheduleFn)t.scheduleFn(t);else{if(t.type!=I)throw new Error("Task is missing scheduleFn.");R(t)}return r}invokeTask(T,t,r,k){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,T,t,r,k):t.callback.apply(r,k)}cancelTask(T,t){let r;if(this._cancelTaskZS)r=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,T,t);else{if(!t.cancelFn)throw Error("Task is not cancelable");r=t.cancelFn(t)}return r}hasTask(T,t){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,T,t)}catch(r){this.handleError(T,r)}}_updateTaskCount(T,t){const r=this._taskCounts,k=r[T],C=r[T]=k+t;if(C<0)throw new Error("More tasks executed then were scheduled.");0!=k&&0!=C||this.hasTask(this.zone,{microTask:r.microTask>0,macroTask:r.macroTask>0,eventTask:r.eventTask>0,change:T})}}class p{constructor(T,t,r,k,C,$){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=T,this.source=t,this.data=k,this.scheduleFn=C,this.cancelFn=$,!r)throw new Error("callback is not defined");this.callback=r;const l=this;this.invoke=T===Q&&k&&k.useG?p.invokeTask:function(){return p.invokeTask.call(e,l,this,arguments)}}static invokeTask(T,t,r){T||(T=this),ee++;try{return T.runCount++,T.zone.runTask(T,t,r)}finally{1==ee&&_(),ee--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(x,X)}_transitionTo(T,t,r){if(this._state!==t&&this._state!==r)throw new Error(`${this.type} '${this.source}': can not transition to '${T}', expecting state '${t}'${r?" or '"+r+"'":""}, was '${this._state}'.`);this._state=T,T==x&&(this._zoneDelegates=null)}toString(){return this.data&&typeof this.data.handleId<"u"?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}const M=a("setTimeout"),Z=a("Promise"),N=a("then");let J,U=[],H=!1;function z(L){if(J||e[Z]&&(J=e[Z].resolve(0)),J){let T=J[N];T||(T=J.then),T.call(J,L)}else e[M](L,0)}function R(L){0===ee&&0===U.length&&z(_),L&&U.push(L)}function _(){if(!H){for(H=!0;U.length;){const L=U;U=[];for(let T=0;T<L.length;T++){const t=L[T];try{t.zone.runTask(t,null,null)}catch(r){q.onUnhandledError(r)}}}q.microtaskDrainDone(),H=!1}}const K={name:"NO ZONE"},x="notScheduled",X="scheduling",A="scheduled",E="running",V="canceling",h="unknown",I="microTask",w="macroTask",Q="eventTask",oe={},q={symbol:a,currentZoneFrame:()=>F,onUnhandledError:W,microtaskDrainDone:W,scheduleMicroTask:R,showUncaughtError:()=>!d[a("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:W,patchMethod:()=>W,bindArguments:()=>[],patchThen:()=>W,patchMacroTask:()=>W,patchEventPrototype:()=>W,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>W,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>W,wrapWithCurrentZone:()=>W,filterProperties:()=>[],attachOriginToPatched:()=>W,_redefineProperty:()=>W,patchCallbacks:()=>W,nativeScheduleMicroTask:z};let F={parent:null,zone:new d(null,null)},re=null,ee=0;function W(){}o("Zone","Zone"),e.Zone=d}(typeof window<"u"&&window||typeof self<"u"&&self||global);const ie=Object.getOwnPropertyDescriptor,Ee=Object.defineProperty,ce=Object.getPrototypeOf,ge=Object.create,Ge=Array.prototype.slice,ke="addEventListener",Pe="removeEventListener",Ze=Zone.__symbol__(ke),Ne=Zone.__symbol__(Pe),ae="true",le="false",ve=Zone.__symbol__("");function Ie(e,n){return Zone.current.wrap(e,n)}function Me(e,n,i,o,c){return Zone.current.scheduleMacroTask(e,n,i,o,c)}const j=Zone.__symbol__,Re=typeof window<"u",Te=Re?window:void 0,Y=Re&&Te||"object"==typeof self&&self||global,ct="removeAttribute";function Le(e,n){for(let i=e.length-1;i>=0;i--)"function"==typeof e[i]&&(e[i]=Ie(e[i],n+"_"+i));return e}function Be(e){return!e||!1!==e.writable&&!("function"==typeof e.get&&typeof e.set>"u")}const Ue=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,Ce=!("nw"in Y)&&typeof Y.process<"u"&&"[object process]"==={}.toString.call(Y.process),Ae=!Ce&&!Ue&&!(!Re||!Te.HTMLElement),Fe=typeof Y.process<"u"&&"[object process]"==={}.toString.call(Y.process)&&!Ue&&!(!Re||!Te.HTMLElement),De={},We=function(e){if(!(e=e||Y.event))return;let n=De[e.type];n||(n=De[e.type]=j("ON_PROPERTY"+e.type));const i=this||e.target||Y,o=i[n];let c;return Ae&&i===Te&&"error"===e.type?(c=o&&o.call(this,e.message,e.filename,e.lineno,e.colno,e.error),!0===c&&e.preventDefault()):(c=o&&o.apply(this,arguments),null!=c&&!c&&e.preventDefault()),c};function ze(e,n,i){let o=ie(e,n);if(!o&&i&&ie(i,n)&&(o={enumerable:!0,configurable:!0}),!o||!o.configurable)return;const c=j("on"+n+"patched");if(e.hasOwnProperty(c)&&e[c])return;delete o.writable,delete o.value;const a=o.get,y=o.set,d=n.slice(2);let b=De[d];b||(b=De[d]=j("ON_PROPERTY"+d)),o.set=function(v){let p=this;!p&&e===Y&&(p=Y),p&&("function"==typeof p[b]&&p.removeEventListener(d,We),y&&y.call(p,null),p[b]=v,"function"==typeof v&&p.addEventListener(d,We,!1))},o.get=function(){let v=this;if(!v&&e===Y&&(v=Y),!v)return null;const p=v[b];if(p)return p;if(a){let M=a.call(this);if(M)return o.set.call(this,M),"function"==typeof v[ct]&&v.removeAttribute(n),M}return null},Ee(e,n,o),e[c]=!0}function Xe(e,n,i){if(n)for(let o=0;o<n.length;o++)ze(e,"on"+n[o],i);else{const o=[];for(const c in e)"on"==c.slice(0,2)&&o.push(c);for(let c=0;c<o.length;c++)ze(e,o[c],i)}}const ne=j("originalInstance");function be(e){const n=Y[e];if(!n)return;Y[j(e)]=n,Y[e]=function(){const c=Le(arguments,e);switch(c.length){case 0:this[ne]=new n;break;case 1:this[ne]=new n(c[0]);break;case 2:this[ne]=new n(c[0],c[1]);break;case 3:this[ne]=new n(c[0],c[1],c[2]);break;case 4:this[ne]=new n(c[0],c[1],c[2],c[3]);break;default:throw new Error("Arg list too long.")}},fe(Y[e],n);const i=new n(function(){});let o;for(o in i)"XMLHttpRequest"===e&&"responseBlob"===o||function(c){"function"==typeof i[c]?Y[e].prototype[c]=function(){return this[ne][c].apply(this[ne],arguments)}:Ee(Y[e].prototype,c,{set:function(a){"function"==typeof a?(this[ne][c]=Ie(a,e+"."+c),fe(this[ne][c],a)):this[ne][c]=a},get:function(){return this[ne][c]}})}(o);for(o in n)"prototype"!==o&&n.hasOwnProperty(o)&&(Y[e][o]=n[o])}function ue(e,n,i){let o=e;for(;o&&!o.hasOwnProperty(n);)o=ce(o);!o&&e[n]&&(o=e);const c=j(n);let a=null;if(o&&(!(a=o[c])||!o.hasOwnProperty(c))&&(a=o[c]=o[n],Be(o&&ie(o,n)))){const d=i(a,c,n);o[n]=function(){return d(this,arguments)},fe(o[n],a)}return a}function lt(e,n,i){let o=null;function c(a){const y=a.data;return y.args[y.cbIdx]=function(){a.invoke.apply(this,arguments)},o.apply(y.target,y.args),a}o=ue(e,n,a=>function(y,d){const b=i(y,d);return b.cbIdx>=0&&"function"==typeof d[b.cbIdx]?Me(b.name,d[b.cbIdx],b,c):a.apply(y,d)})}function fe(e,n){e[j("OriginalDelegate")]=n}let qe=!1,je=!1;function ft(){if(qe)return je;qe=!0;try{const e=Te.navigator.userAgent;(-1!==e.indexOf("MSIE ")||-1!==e.indexOf("Trident/")||-1!==e.indexOf("Edge/"))&&(je=!0)}catch{}return je}Zone.__load_patch("ZoneAwarePromise",(e,n,i)=>{const o=Object.getOwnPropertyDescriptor,c=Object.defineProperty,y=i.symbol,d=[],b=!0===e[y("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],v=y("Promise"),p=y("then"),M="__creationTrace__";i.onUnhandledError=l=>{if(i.showUncaughtError()){const u=l&&l.rejection;u?console.error("Unhandled Promise rejection:",u instanceof Error?u.message:u,"; Zone:",l.zone.name,"; Task:",l.task&&l.task.source,"; Value:",u,u instanceof Error?u.stack:void 0):console.error(l)}},i.microtaskDrainDone=()=>{for(;d.length;){const l=d.shift();try{l.zone.runGuarded(()=>{throw l.throwOriginal?l.rejection:l})}catch(u){N(u)}}};const Z=y("unhandledPromiseRejectionHandler");function N(l){i.onUnhandledError(l);try{const u=n[Z];"function"==typeof u&&u.call(this,l)}catch{}}function U(l){return l&&l.then}function H(l){return l}function J(l){return t.reject(l)}const z=y("state"),R=y("value"),_=y("finally"),K=y("parentPromiseValue"),x=y("parentPromiseState"),X="Promise.then",A=null,E=!0,V=!1,h=0;function I(l,u){return s=>{try{q(l,u,s)}catch(f){q(l,!1,f)}}}const w=function(){let l=!1;return function(s){return function(){l||(l=!0,s.apply(null,arguments))}}},Q="Promise resolved with itself",oe=y("currentTaskTrace");function q(l,u,s){const f=w();if(l===s)throw new TypeError(Q);if(l[z]===A){let g=null;try{("object"==typeof s||"function"==typeof s)&&(g=s&&s.then)}catch(P){return f(()=>{q(l,!1,P)})(),l}if(u!==V&&s instanceof t&&s.hasOwnProperty(z)&&s.hasOwnProperty(R)&&s[z]!==A)re(s),q(l,s[z],s[R]);else if(u!==V&&"function"==typeof g)try{g.call(s,f(I(l,u)),f(I(l,!1)))}catch(P){f(()=>{q(l,!1,P)})()}else{l[z]=u;const P=l[R];if(l[R]=s,l[_]===_&&u===E&&(l[z]=l[x],l[R]=l[K]),u===V&&s instanceof Error){const m=n.currentTask&&n.currentTask.data&&n.currentTask.data[M];m&&c(s,oe,{configurable:!0,enumerable:!1,writable:!0,value:m})}for(let m=0;m<P.length;)ee(l,P[m++],P[m++],P[m++],P[m++]);if(0==P.length&&u==V){l[z]=h;let m=s;try{throw new Error("Uncaught (in promise): "+function a(l){return l&&l.toString===Object.prototype.toString?(l.constructor&&l.constructor.name||"")+": "+JSON.stringify(l):l?l.toString():Object.prototype.toString.call(l)}(s)+(s&&s.stack?"\n"+s.stack:""))}catch(D){m=D}b&&(m.throwOriginal=!0),m.rejection=s,m.promise=l,m.zone=n.current,m.task=n.currentTask,d.push(m),i.scheduleMicroTask()}}}return l}const F=y("rejectionHandledHandler");function re(l){if(l[z]===h){try{const u=n[F];u&&"function"==typeof u&&u.call(this,{rejection:l[R],promise:l})}catch{}l[z]=V;for(let u=0;u<d.length;u++)l===d[u].promise&&d.splice(u,1)}}function ee(l,u,s,f,g){re(l);const P=l[z],m=P?"function"==typeof f?f:H:"function"==typeof g?g:J;u.scheduleMicroTask(X,()=>{try{const D=l[R],O=!!s&&_===s[_];O&&(s[K]=D,s[x]=P);const S=u.run(m,void 0,O&&m!==J&&m!==H?[]:[D]);q(s,!0,S)}catch(D){q(s,!1,D)}},s)}const L=function(){},T=e.AggregateError;class t{static toString(){return"function ZoneAwarePromise() { [native code] }"}static resolve(u){return q(new this(null),E,u)}static reject(u){return q(new this(null),V,u)}static any(u){if(!u||"function"!=typeof u[Symbol.iterator])return Promise.reject(new T([],"All promises were rejected"));const s=[];let f=0;try{for(let m of u)f++,s.push(t.resolve(m))}catch{return Promise.reject(new T([],"All promises were rejected"))}if(0===f)return Promise.reject(new T([],"All promises were rejected"));let g=!1;const P=[];return new t((m,D)=>{for(let O=0;O<s.length;O++)s[O].then(S=>{g||(g=!0,m(S))},S=>{P.push(S),f--,0===f&&(g=!0,D(new T(P,"All promises were rejected")))})})}static race(u){let s,f,g=new this((D,O)=>{s=D,f=O});function P(D){s(D)}function m(D){f(D)}for(let D of u)U(D)||(D=this.resolve(D)),D.then(P,m);return g}static all(u){return t.allWithCallback(u)}static allSettled(u){return(this&&this.prototype instanceof t?this:t).allWithCallback(u,{thenCallback:f=>({status:"fulfilled",value:f}),errorCallback:f=>({status:"rejected",reason:f})})}static allWithCallback(u,s){let f,g,P=new this((S,G)=>{f=S,g=G}),m=2,D=0;const O=[];for(let S of u){U(S)||(S=this.resolve(S));const G=D;try{S.then(B=>{O[G]=s?s.thenCallback(B):B,m--,0===m&&f(O)},B=>{s?(O[G]=s.errorCallback(B),m--,0===m&&f(O)):g(B)})}catch(B){g(B)}m++,D++}return m-=2,0===m&&f(O),P}constructor(u){const s=this;if(!(s instanceof t))throw new Error("Must be an instanceof Promise.");s[z]=A,s[R]=[];try{const f=w();u&&u(f(I(s,E)),f(I(s,V)))}catch(f){q(s,!1,f)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return t}then(u,s){let f=this.constructor?.[Symbol.species];(!f||"function"!=typeof f)&&(f=this.constructor||t);const g=new f(L),P=n.current;return this[z]==A?this[R].push(P,g,u,s):ee(this,P,g,u,s),g}catch(u){return this.then(null,u)}finally(u){let s=this.constructor?.[Symbol.species];(!s||"function"!=typeof s)&&(s=t);const f=new s(L);f[_]=_;const g=n.current;return this[z]==A?this[R].push(g,f,u,u):ee(this,g,f,u,u),f}}t.resolve=t.resolve,t.reject=t.reject,t.race=t.race,t.all=t.all;const r=e[v]=e.Promise;e.Promise=t;const k=y("thenPatched");function C(l){const u=l.prototype,s=o(u,"then");if(s&&(!1===s.writable||!s.configurable))return;const f=u.then;u[p]=f,l.prototype.then=function(g,P){return new t((D,O)=>{f.call(this,D,O)}).then(g,P)},l[k]=!0}return i.patchThen=C,r&&(C(r),ue(e,"fetch",l=>function $(l){return function(u,s){let f=l.apply(u,s);if(f instanceof t)return f;let g=f.constructor;return g[k]||C(g),f}}(l))),Promise[n.__symbol__("uncaughtPromiseErrors")]=d,t}),Zone.__load_patch("toString",e=>{const n=Function.prototype.toString,i=j("OriginalDelegate"),o=j("Promise"),c=j("Error"),a=function(){if("function"==typeof this){const v=this[i];if(v)return"function"==typeof v?n.call(v):Object.prototype.toString.call(v);if(this===Promise){const p=e[o];if(p)return n.call(p)}if(this===Error){const p=e[c];if(p)return n.call(p)}}return n.call(this)};a[i]=n,Function.prototype.toString=a;const y=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":y.call(this)}});let ye=!1;if(typeof window<"u")try{const e=Object.defineProperty({},"passive",{get:function(){ye=!0}});window.addEventListener("test",e,e),window.removeEventListener("test",e,e)}catch{ye=!1}const ht={useG:!0},te={},Ye={},$e=new RegExp("^"+ve+"(\\w+)(true|false)$"),Ke=j("propagationStopped");function Je(e,n){const i=(n?n(e):e)+le,o=(n?n(e):e)+ae,c=ve+i,a=ve+o;te[e]={},te[e][le]=c,te[e][ae]=a}function dt(e,n,i,o){const c=o&&o.add||ke,a=o&&o.rm||Pe,y=o&&o.listeners||"eventListeners",d=o&&o.rmAll||"removeAllListeners",b=j(c),v="."+c+":",p="prependListener",M="."+p+":",Z=function(R,_,K){if(R.isRemoved)return;const x=R.callback;let X;"object"==typeof x&&x.handleEvent&&(R.callback=E=>x.handleEvent(E),R.originalDelegate=x);try{R.invoke(R,_,[K])}catch(E){X=E}const A=R.options;return A&&"object"==typeof A&&A.once&&_[a].call(_,K.type,R.originalDelegate?R.originalDelegate:R.callback,A),X};function N(R,_,K){if(!(_=_||e.event))return;const x=R||_.target||e,X=x[te[_.type][K?ae:le]];if(X){const A=[];if(1===X.length){const E=Z(X[0],x,_);E&&A.push(E)}else{const E=X.slice();for(let V=0;V<E.length&&(!_||!0!==_[Ke]);V++){const h=Z(E[V],x,_);h&&A.push(h)}}if(1===A.length)throw A[0];for(let E=0;E<A.length;E++){const V=A[E];n.nativeScheduleMicroTask(()=>{throw V})}}}const U=function(R){return N(this,R,!1)},H=function(R){return N(this,R,!0)};function J(R,_){if(!R)return!1;let K=!0;_&&void 0!==_.useG&&(K=_.useG);const x=_&&_.vh;let X=!0;_&&void 0!==_.chkDup&&(X=_.chkDup);let A=!1;_&&void 0!==_.rt&&(A=_.rt);let E=R;for(;E&&!E.hasOwnProperty(c);)E=ce(E);if(!E&&R[c]&&(E=R),!E||E[b])return!1;const V=_&&_.eventNameToString,h={},I=E[b]=E[c],w=E[j(a)]=E[a],Q=E[j(y)]=E[y],oe=E[j(d)]=E[d];let q;_&&_.prepend&&(q=E[j(_.prepend)]=E[_.prepend]);const t=K?function(s){if(!h.isExisting)return I.call(h.target,h.eventName,h.capture?H:U,h.options)}:function(s){return I.call(h.target,h.eventName,s.invoke,h.options)},r=K?function(s){if(!s.isRemoved){const f=te[s.eventName];let g;f&&(g=f[s.capture?ae:le]);const P=g&&s.target[g];if(P)for(let m=0;m<P.length;m++)if(P[m]===s){P.splice(m,1),s.isRemoved=!0,0===P.length&&(s.allRemoved=!0,s.target[g]=null);break}}if(s.allRemoved)return w.call(s.target,s.eventName,s.capture?H:U,s.options)}:function(s){return w.call(s.target,s.eventName,s.invoke,s.options)},C=_&&_.diff?_.diff:function(s,f){const g=typeof f;return"function"===g&&s.callback===f||"object"===g&&s.originalDelegate===f},$=Zone[j("UNPATCHED_EVENTS")],l=e[j("PASSIVE_EVENTS")],u=function(s,f,g,P,m=!1,D=!1){return function(){const O=this||e;let S=arguments[0];_&&_.transferEventName&&(S=_.transferEventName(S));let G=arguments[1];if(!G)return s.apply(this,arguments);if(Ce&&"uncaughtException"===S)return s.apply(this,arguments);let B=!1;if("function"!=typeof G){if(!G.handleEvent)return s.apply(this,arguments);B=!0}if(x&&!x(s,G,O,arguments))return;const he=ye&&!!l&&-1!==l.indexOf(S),se=function F(s,f){return!ye&&"object"==typeof s&&s?!!s.capture:ye&&f?"boolean"==typeof s?{capture:s,passive:!0}:s?"object"==typeof s&&!1!==s.passive?{...s,passive:!0}:s:{passive:!0}:s}(arguments[2],he);if($)for(let _e=0;_e<$.length;_e++)if(S===$[_e])return he?s.call(O,S,G,se):s.apply(this,arguments);const xe=!!se&&("boolean"==typeof se||se.capture),nt=!(!se||"object"!=typeof se)&&se.once,kt=Zone.current;let Ve=te[S];Ve||(Je(S,V),Ve=te[S]);const rt=Ve[xe?ae:le];let Se,me=O[rt],ot=!1;if(me){if(ot=!0,X)for(let _e=0;_e<me.length;_e++)if(C(me[_e],G))return}else me=O[rt]=[];const st=O.constructor.name,it=Ye[st];it&&(Se=it[S]),Se||(Se=st+f+(V?V(S):S)),h.options=se,nt&&(h.options.once=!1),h.target=O,h.capture=xe,h.eventName=S,h.isExisting=ot;const we=K?ht:void 0;we&&(we.taskData=h);const de=kt.scheduleEventTask(Se,G,we,g,P);return h.target=null,we&&(we.taskData=null),nt&&(se.once=!0),!ye&&"boolean"==typeof de.options||(de.options=se),de.target=O,de.capture=xe,de.eventName=S,B&&(de.originalDelegate=G),D?me.unshift(de):me.push(de),m?O:void 0}};return E[c]=u(I,v,t,r,A),q&&(E[p]=u(q,M,function(s){return q.call(h.target,h.eventName,s.invoke,h.options)},r,A,!0)),E[a]=function(){const s=this||e;let f=arguments[0];_&&_.transferEventName&&(f=_.transferEventName(f));const g=arguments[2],P=!!g&&("boolean"==typeof g||g.capture),m=arguments[1];if(!m)return w.apply(this,arguments);if(x&&!x(w,m,s,arguments))return;const D=te[f];let O;D&&(O=D[P?ae:le]);const S=O&&s[O];if(S)for(let G=0;G<S.length;G++){const B=S[G];if(C(B,m))return S.splice(G,1),B.isRemoved=!0,0===S.length&&(B.allRemoved=!0,s[O]=null,"string"==typeof f)&&(s[ve+"ON_PROPERTY"+f]=null),B.zone.cancelTask(B),A?s:void 0}return w.apply(this,arguments)},E[y]=function(){const s=this||e;let f=arguments[0];_&&_.transferEventName&&(f=_.transferEventName(f));const g=[],P=Qe(s,V?V(f):f);for(let m=0;m<P.length;m++){const D=P[m];g.push(D.originalDelegate?D.originalDelegate:D.callback)}return g},E[d]=function(){const s=this||e;let f=arguments[0];if(f){_&&_.transferEventName&&(f=_.transferEventName(f));const g=te[f];if(g){const D=s[g[le]],O=s[g[ae]];if(D){const S=D.slice();for(let G=0;G<S.length;G++){const B=S[G];this[a].call(this,f,B.originalDelegate?B.originalDelegate:B.callback,B.options)}}if(O){const S=O.slice();for(let G=0;G<S.length;G++){const B=S[G];this[a].call(this,f,B.originalDelegate?B.originalDelegate:B.callback,B.options)}}}}else{const g=Object.keys(s);for(let P=0;P<g.length;P++){const D=$e.exec(g[P]);let O=D&&D[1];O&&"removeListener"!==O&&this[d].call(this,O)}this[d].call(this,"removeListener")}if(A)return this},fe(E[c],I),fe(E[a],w),oe&&fe(E[d],oe),Q&&fe(E[y],Q),!0}let z=[];for(let R=0;R<i.length;R++)z[R]=J(i[R],o);return z}function Qe(e,n){if(!n){const a=[];for(let y in e){const d=$e.exec(y);let b=d&&d[1];if(b&&(!n||b===n)){const v=e[y];if(v)for(let p=0;p<v.length;p++)a.push(v[p])}}return a}let i=te[n];i||(Je(n),i=te[n]);const o=e[i[le]],c=e[i[ae]];return o?c?o.concat(c):o.slice():c?c.slice():[]}function _t(e,n){const i=e.Event;i&&i.prototype&&n.patchMethod(i.prototype,"stopImmediatePropagation",o=>function(c,a){c[Ke]=!0,o&&o.apply(c,a)})}function Et(e,n,i,o,c){const a=Zone.__symbol__(o);if(n[a])return;const y=n[a]=n[o];n[o]=function(d,b,v){return b&&b.prototype&&c.forEach(function(p){const M=`${i}.${o}::`+p,Z=b.prototype;try{if(Z.hasOwnProperty(p)){const N=e.ObjectGetOwnPropertyDescriptor(Z,p);N&&N.value?(N.value=e.wrapWithCurrentZone(N.value,M),e._redefineProperty(b.prototype,p,N)):Z[p]&&(Z[p]=e.wrapWithCurrentZone(Z[p],M))}else Z[p]&&(Z[p]=e.wrapWithCurrentZone(Z[p],M))}catch{}}),y.call(n,d,b,v)},e.attachOriginToPatched(n[o],y)}function et(e,n,i){if(!i||0===i.length)return n;const o=i.filter(a=>a.target===e);if(!o||0===o.length)return n;const c=o[0].ignoreProperties;return n.filter(a=>-1===c.indexOf(a))}function tt(e,n,i,o){e&&Xe(e,et(e,n,i),o)}function He(e){return Object.getOwnPropertyNames(e).filter(n=>n.startsWith("on")&&n.length>2).map(n=>n.substring(2))}Zone.__load_patch("util",(e,n,i)=>{const o=He(e);i.patchOnProperties=Xe,i.patchMethod=ue,i.bindArguments=Le,i.patchMacroTask=lt;const c=n.__symbol__("BLACK_LISTED_EVENTS"),a=n.__symbol__("UNPATCHED_EVENTS");e[a]&&(e[c]=e[a]),e[c]&&(n[c]=n[a]=e[c]),i.patchEventPrototype=_t,i.patchEventTarget=dt,i.isIEOrEdge=ft,i.ObjectDefineProperty=Ee,i.ObjectGetOwnPropertyDescriptor=ie,i.ObjectCreate=ge,i.ArraySlice=Ge,i.patchClass=be,i.wrapWithCurrentZone=Ie,i.filterProperties=et,i.attachOriginToPatched=fe,i._redefineProperty=Object.defineProperty,i.patchCallbacks=Et,i.getGlobalObjects=()=>({globalSources:Ye,zoneSymbolEventNames:te,eventNames:o,isBrowser:Ae,isMix:Fe,isNode:Ce,TRUE_STR:ae,FALSE_STR:le,ZONE_SYMBOL_PREFIX:ve,ADD_EVENT_LISTENER_STR:ke,REMOVE_EVENT_LISTENER_STR:Pe})});const Oe=j("zoneTask");function pe(e,n,i,o){let c=null,a=null;i+=o;const y={};function d(v){const p=v.data;return p.args[0]=function(){return v.invoke.apply(this,arguments)},p.handleId=c.apply(e,p.args),v}function b(v){return a.call(e,v.data.handleId)}c=ue(e,n+=o,v=>function(p,M){if("function"==typeof M[0]){const Z={isPeriodic:"Interval"===o,delay:"Timeout"===o||"Interval"===o?M[1]||0:void 0,args:M},N=M[0];M[0]=function(){try{return N.apply(this,arguments)}finally{Z.isPeriodic||("number"==typeof Z.handleId?delete y[Z.handleId]:Z.handleId&&(Z.handleId[Oe]=null))}};const U=Me(n,M[0],Z,d,b);if(!U)return U;const H=U.data.handleId;return"number"==typeof H?y[H]=U:H&&(H[Oe]=U),H&&H.ref&&H.unref&&"function"==typeof H.ref&&"function"==typeof H.unref&&(U.ref=H.ref.bind(H),U.unref=H.unref.bind(H)),"number"==typeof H||H?H:U}return v.apply(e,M)}),a=ue(e,i,v=>function(p,M){const Z=M[0];let N;"number"==typeof Z?N=y[Z]:(N=Z&&Z[Oe],N||(N=Z)),N&&"string"==typeof N.type?"notScheduled"!==N.state&&(N.cancelFn&&N.data.isPeriodic||0===N.runCount)&&("number"==typeof Z?delete y[Z]:Z&&(Z[Oe]=null),N.zone.cancelTask(N)):v.apply(e,M)})}Zone.__load_patch("legacy",e=>{const n=e[Zone.__symbol__("legacyPatch")];n&&n()}),Zone.__load_patch("timers",e=>{const n="set",i="clear";pe(e,n,i,"Timeout"),pe(e,n,i,"Interval"),pe(e,n,i,"Immediate")}),Zone.__load_patch("requestAnimationFrame",e=>{pe(e,"request","cancel","AnimationFrame"),pe(e,"mozRequest","mozCancel","AnimationFrame"),pe(e,"webkitRequest","webkitCancel","AnimationFrame")}),Zone.__load_patch("blocking",(e,n)=>{const i=["alert","prompt","confirm"];for(let o=0;o<i.length;o++)ue(e,i[o],(a,y,d)=>function(b,v){return n.current.run(a,e,v,d)})}),Zone.__load_patch("EventTarget",(e,n,i)=>{(function gt(e,n){n.patchEventPrototype(e,n)})(e,i),function mt(e,n){if(Zone[n.symbol("patchEventTarget")])return;const{eventNames:i,zoneSymbolEventNames:o,TRUE_STR:c,FALSE_STR:a,ZONE_SYMBOL_PREFIX:y}=n.getGlobalObjects();for(let b=0;b<i.length;b++){const v=i[b],Z=y+(v+a),N=y+(v+c);o[v]={},o[v][a]=Z,o[v][c]=N}const d=e.EventTarget;d&&d.prototype&&n.patchEventTarget(e,n,[d&&d.prototype])}(e,i);const o=e.XMLHttpRequestEventTarget;o&&o.prototype&&i.patchEventTarget(e,i,[o.prototype])}),Zone.__load_patch("MutationObserver",(e,n,i)=>{be("MutationObserver"),be("WebKitMutationObserver")}),Zone.__load_patch("IntersectionObserver",(e,n,i)=>{be("IntersectionObserver")}),Zone.__load_patch("FileReader",(e,n,i)=>{be("FileReader")}),Zone.__load_patch("on_property",(e,n,i)=>{!function Tt(e,n){if(Ce&&!Fe||Zone[e.symbol("patchEvents")])return;const i=n.__Zone_ignore_on_properties;let o=[];if(Ae){const c=window;o=o.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);const a=function ut(){try{const e=Te.navigator.userAgent;if(-1!==e.indexOf("MSIE ")||-1!==e.indexOf("Trident/"))return!0}catch{}return!1}()?[{target:c,ignoreProperties:["error"]}]:[];tt(c,He(c),i&&i.concat(a),ce(c))}o=o.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(let c=0;c<o.length;c++){const a=n[o[c]];a&&a.prototype&&tt(a.prototype,He(a.prototype),i)}}(i,e)}),Zone.__load_patch("customElements",(e,n,i)=>{!function pt(e,n){const{isBrowser:i,isMix:o}=n.getGlobalObjects();(i||o)&&e.customElements&&"customElements"in e&&n.patchCallbacks(n,e.customElements,"customElements","define",["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback"])}(e,i)}),Zone.__load_patch("XHR",(e,n)=>{!function b(v){const p=v.XMLHttpRequest;if(!p)return;const M=p.prototype;let N=M[Ze],U=M[Ne];if(!N){const h=v.XMLHttpRequestEventTarget;if(h){const I=h.prototype;N=I[Ze],U=I[Ne]}}const H="readystatechange",J="scheduled";function z(h){const I=h.data,w=I.target;w[a]=!1,w[d]=!1;const Q=w[c];N||(N=w[Ze],U=w[Ne]),Q&&U.call(w,H,Q);const oe=w[c]=()=>{if(w.readyState===w.DONE)if(!I.aborted&&w[a]&&h.state===J){const F=w[n.__symbol__("loadfalse")];if(0!==w.status&&F&&F.length>0){const re=h.invoke;h.invoke=function(){const ee=w[n.__symbol__("loadfalse")];for(let W=0;W<ee.length;W++)ee[W]===h&&ee.splice(W,1);!I.aborted&&h.state===J&&re.call(h)},F.push(h)}else h.invoke()}else!I.aborted&&!1===w[a]&&(w[d]=!0)};return N.call(w,H,oe),w[i]||(w[i]=h),E.apply(w,I.args),w[a]=!0,h}function R(){}function _(h){const I=h.data;return I.aborted=!0,V.apply(I.target,I.args)}const K=ue(M,"open",()=>function(h,I){return h[o]=0==I[2],h[y]=I[1],K.apply(h,I)}),X=j("fetchTaskAborting"),A=j("fetchTaskScheduling"),E=ue(M,"send",()=>function(h,I){if(!0===n.current[A]||h[o])return E.apply(h,I);{const w={target:h,url:h[y],isPeriodic:!1,args:I,aborted:!1},Q=Me("XMLHttpRequest.send",R,w,z,_);h&&!0===h[d]&&!w.aborted&&Q.state===J&&Q.invoke()}}),V=ue(M,"abort",()=>function(h,I){const w=function Z(h){return h[i]}(h);if(w&&"string"==typeof w.type){if(null==w.cancelFn||w.data&&w.data.aborted)return;w.zone.cancelTask(w)}else if(!0===n.current[X])return V.apply(h,I)})}(e);const i=j("xhrTask"),o=j("xhrSync"),c=j("xhrListener"),a=j("xhrScheduled"),y=j("xhrURL"),d=j("xhrErrorBeforeScheduled")}),Zone.__load_patch("geolocation",e=>{e.navigator&&e.navigator.geolocation&&function at(e,n){const i=e.constructor.name;for(let o=0;o<n.length;o++){const c=n[o],a=e[c];if(a){if(!Be(ie(e,c)))continue;e[c]=(d=>{const b=function(){return d.apply(this,Le(arguments,i+"."+c))};return fe(b,d),b})(a)}}}(e.navigator.geolocation,["getCurrentPosition","watchPosition"])}),Zone.__load_patch("PromiseRejectionEvent",(e,n)=>{function i(o){return function(c){Qe(e,o).forEach(y=>{const d=e.PromiseRejectionEvent;if(d){const b=new d(o,{promise:c.promise,reason:c.rejection});y.invoke(b)}})}}e.PromiseRejectionEvent&&(n[j("unhandledPromiseRejectionHandler")]=i("unhandledrejection"),n[j("rejectionHandledHandler")]=i("rejectionhandled"))}),Zone.__load_patch("queueMicrotask",(e,n,i)=>{!function yt(e,n){n.patchMethod(e,"queueMicrotask",i=>function(o,c){Zone.current.scheduleMicroTask("queueMicrotask",c[0])})}(e,i)})}},ie=>{ie(ie.s=50)}]);