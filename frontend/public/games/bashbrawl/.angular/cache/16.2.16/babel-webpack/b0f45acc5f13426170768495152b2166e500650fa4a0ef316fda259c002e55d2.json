{"ast": null, "code": "import _curry1 from \"./_curry1.js\";\nimport _curry2 from \"./_curry2.js\";\nimport _isPlaceholder from \"./_isPlaceholder.js\";\n/**\n * Optimized internal three-arity curry function.\n *\n * @private\n * @category Function\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\n\nexport default function _curry3(fn) {\n  return function f3(a, b, c) {\n    switch (arguments.length) {\n      case 0:\n        return f3;\n      case 1:\n        return _isPlaceholder(a) ? f3 : _curry2(function (_b, _c) {\n          return fn(a, _b, _c);\n        });\n      case 2:\n        return _isPlaceholder(a) && _isPlaceholder(b) ? f3 : _isPlaceholder(a) ? _curry2(function (_a, _c) {\n          return fn(_a, b, _c);\n        }) : _isPlaceholder(b) ? _curry2(function (_b, _c) {\n          return fn(a, _b, _c);\n        }) : _curry1(function (_c) {\n          return fn(a, b, _c);\n        });\n      default:\n        return _isPlaceholder(a) && _isPlaceholder(b) && _isPlaceholder(c) ? f3 : _isPlaceholder(a) && _isPlaceholder(b) ? _curry2(function (_a, _b) {\n          return fn(_a, _b, c);\n        }) : _isPlaceholder(a) && _isPlaceholder(c) ? _curry2(function (_a, _c) {\n          return fn(_a, b, _c);\n        }) : _isPlaceholder(b) && _isPlaceholder(c) ? _curry2(function (_b, _c) {\n          return fn(a, _b, _c);\n        }) : _isPlaceholder(a) ? _curry1(function (_a) {\n          return fn(_a, b, c);\n        }) : _isPlaceholder(b) ? _curry1(function (_b) {\n          return fn(a, _b, c);\n        }) : _isPlaceholder(c) ? _curry1(function (_c) {\n          return fn(a, b, _c);\n        }) : fn(a, b, c);\n    }\n  };\n}", "map": {"version": 3, "names": ["_curry1", "_curry2", "_isPlaceholder", "_curry3", "fn", "f3", "a", "b", "c", "arguments", "length", "_b", "_c", "_a"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_curry3.js"], "sourcesContent": ["import _curry1 from \"./_curry1.js\";\nimport _curry2 from \"./_curry2.js\";\nimport _isPlaceholder from \"./_isPlaceholder.js\";\n/**\n * Optimized internal three-arity curry function.\n *\n * @private\n * @category Function\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\n\nexport default function _curry3(fn) {\n  return function f3(a, b, c) {\n    switch (arguments.length) {\n      case 0:\n        return f3;\n\n      case 1:\n        return _isPlaceholder(a) ? f3 : _curry2(function (_b, _c) {\n          return fn(a, _b, _c);\n        });\n\n      case 2:\n        return _isPlaceholder(a) && _isPlaceholder(b) ? f3 : _isPlaceholder(a) ? _curry2(function (_a, _c) {\n          return fn(_a, b, _c);\n        }) : _isPlaceholder(b) ? _curry2(function (_b, _c) {\n          return fn(a, _b, _c);\n        }) : _curry1(function (_c) {\n          return fn(a, b, _c);\n        });\n\n      default:\n        return _isPlaceholder(a) && _isPlaceholder(b) && _isPlaceholder(c) ? f3 : _isPlaceholder(a) && _isPlaceholder(b) ? _curry2(function (_a, _b) {\n          return fn(_a, _b, c);\n        }) : _isPlaceholder(a) && _isPlaceholder(c) ? _curry2(function (_a, _c) {\n          return fn(_a, b, _c);\n        }) : _isPlaceholder(b) && _isPlaceholder(c) ? _curry2(function (_b, _c) {\n          return fn(a, _b, _c);\n        }) : _isPlaceholder(a) ? _curry1(function (_a) {\n          return fn(_a, b, c);\n        }) : _isPlaceholder(b) ? _curry1(function (_b) {\n          return fn(a, _b, c);\n        }) : _isPlaceholder(c) ? _curry1(function (_c) {\n          return fn(a, b, _c);\n        }) : fn(a, b, c);\n    }\n  };\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,cAAc;AAClC,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,cAAc,MAAM,qBAAqB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,OAAOA,CAACC,EAAE,EAAE;EAClC,OAAO,SAASC,EAAEA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IAC1B,QAAQC,SAAS,CAACC,MAAM;MACtB,KAAK,CAAC;QACJ,OAAOL,EAAE;MAEX,KAAK,CAAC;QACJ,OAAOH,cAAc,CAACI,CAAC,CAAC,GAAGD,EAAE,GAAGJ,OAAO,CAAC,UAAUU,EAAE,EAAEC,EAAE,EAAE;UACxD,OAAOR,EAAE,CAACE,CAAC,EAAEK,EAAE,EAAEC,EAAE,CAAC;QACtB,CAAC,CAAC;MAEJ,KAAK,CAAC;QACJ,OAAOV,cAAc,CAACI,CAAC,CAAC,IAAIJ,cAAc,CAACK,CAAC,CAAC,GAAGF,EAAE,GAAGH,cAAc,CAACI,CAAC,CAAC,GAAGL,OAAO,CAAC,UAAUY,EAAE,EAAED,EAAE,EAAE;UACjG,OAAOR,EAAE,CAACS,EAAE,EAAEN,CAAC,EAAEK,EAAE,CAAC;QACtB,CAAC,CAAC,GAAGV,cAAc,CAACK,CAAC,CAAC,GAAGN,OAAO,CAAC,UAAUU,EAAE,EAAEC,EAAE,EAAE;UACjD,OAAOR,EAAE,CAACE,CAAC,EAAEK,EAAE,EAAEC,EAAE,CAAC;QACtB,CAAC,CAAC,GAAGZ,OAAO,CAAC,UAAUY,EAAE,EAAE;UACzB,OAAOR,EAAE,CAACE,CAAC,EAAEC,CAAC,EAAEK,EAAE,CAAC;QACrB,CAAC,CAAC;MAEJ;QACE,OAAOV,cAAc,CAACI,CAAC,CAAC,IAAIJ,cAAc,CAACK,CAAC,CAAC,IAAIL,cAAc,CAACM,CAAC,CAAC,GAAGH,EAAE,GAAGH,cAAc,CAACI,CAAC,CAAC,IAAIJ,cAAc,CAACK,CAAC,CAAC,GAAGN,OAAO,CAAC,UAAUY,EAAE,EAAEF,EAAE,EAAE;UAC3I,OAAOP,EAAE,CAACS,EAAE,EAAEF,EAAE,EAAEH,CAAC,CAAC;QACtB,CAAC,CAAC,GAAGN,cAAc,CAACI,CAAC,CAAC,IAAIJ,cAAc,CAACM,CAAC,CAAC,GAAGP,OAAO,CAAC,UAAUY,EAAE,EAAED,EAAE,EAAE;UACtE,OAAOR,EAAE,CAACS,EAAE,EAAEN,CAAC,EAAEK,EAAE,CAAC;QACtB,CAAC,CAAC,GAAGV,cAAc,CAACK,CAAC,CAAC,IAAIL,cAAc,CAACM,CAAC,CAAC,GAAGP,OAAO,CAAC,UAAUU,EAAE,EAAEC,EAAE,EAAE;UACtE,OAAOR,EAAE,CAACE,CAAC,EAAEK,EAAE,EAAEC,EAAE,CAAC;QACtB,CAAC,CAAC,GAAGV,cAAc,CAACI,CAAC,CAAC,GAAGN,OAAO,CAAC,UAAUa,EAAE,EAAE;UAC7C,OAAOT,EAAE,CAACS,EAAE,EAAEN,CAAC,EAAEC,CAAC,CAAC;QACrB,CAAC,CAAC,GAAGN,cAAc,CAACK,CAAC,CAAC,GAAGP,OAAO,CAAC,UAAUW,EAAE,EAAE;UAC7C,OAAOP,EAAE,CAACE,CAAC,EAAEK,EAAE,EAAEH,CAAC,CAAC;QACrB,CAAC,CAAC,GAAGN,cAAc,CAACM,CAAC,CAAC,GAAGR,OAAO,CAAC,UAAUY,EAAE,EAAE;UAC7C,OAAOR,EAAE,CAACE,CAAC,EAAEC,CAAC,EAAEK,EAAE,CAAC;QACrB,CAAC,CAAC,GAAGR,EAAE,CAACE,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACpB;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}