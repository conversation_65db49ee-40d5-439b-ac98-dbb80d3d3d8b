{"ast": null, "code": "import _cloneRegExp from \"./_cloneRegExp.js\";\nimport type from \"../type.js\";\n/**\n * Copies an object.\n *\n * @private\n * @param {*} value The value to be copied\n * @param {Boolean} deep Whether or not to perform deep cloning.\n * @return {*} The copied value.\n */\n\nexport default function _clone(value, deep, map) {\n  map || (map = new _ObjectMap()); // this avoids the slower switch with a quick if decision removing some milliseconds in each run.\n\n  if (_isPrimitive(value)) {\n    return value;\n  }\n  var copy = function copy(copiedValue) {\n    // Check for circular and same references on the object graph and return its corresponding clone.\n    var cachedCopy = map.get(value);\n    if (cachedCopy) {\n      return cachedCopy;\n    }\n    map.set(value, copiedValue);\n    for (var key in value) {\n      if (Object.prototype.hasOwnProperty.call(value, key)) {\n        copiedValue[key] = deep ? _clone(value[key], true, map) : value[key];\n      }\n    }\n    return copiedValue;\n  };\n  switch (type(value)) {\n    case 'Object':\n      return copy(Object.create(Object.getPrototypeOf(value)));\n    case 'Array':\n      return copy([]);\n    case 'Date':\n      return new Date(value.valueOf());\n    case 'RegExp':\n      return _cloneRegExp(value);\n    case 'Int8Array':\n    case 'Uint8Array':\n    case 'Uint8ClampedArray':\n    case 'Int16Array':\n    case 'Uint16Array':\n    case 'Int32Array':\n    case 'Uint32Array':\n    case 'Float32Array':\n    case 'Float64Array':\n    case 'BigInt64Array':\n    case 'BigUint64Array':\n      return value.slice();\n    default:\n      return value;\n  }\n}\nfunction _isPrimitive(param) {\n  var type = typeof param;\n  return param == null || type != 'object' && type != 'function';\n}\nvar _ObjectMap = /*#__PURE__*/\nfunction () {\n  function _ObjectMap() {\n    this.map = {};\n    this.length = 0;\n  }\n  _ObjectMap.prototype.set = function (key, value) {\n    const hashedKey = this.hash(key);\n    let bucket = this.map[hashedKey];\n    if (!bucket) {\n      this.map[hashedKey] = bucket = [];\n    }\n    bucket.push([key, value]);\n    this.length += 1;\n  };\n  _ObjectMap.prototype.hash = function (key) {\n    let hashedKey = [];\n    for (var value in key) {\n      hashedKey.push(Object.prototype.toString.call(key[value]));\n    }\n    return hashedKey.join();\n  };\n  _ObjectMap.prototype.get = function (key) {\n    /**\n     * depending on the number of objects to be cloned is faster to just iterate over the items in the map just because the hash function is so costly,\n     * on my tests this number is 180, anything above that using the hash function is faster.\n     */\n    if (this.length <= 180) {\n      for (const p in this.map) {\n        const bucket = this.map[p];\n        for (let i = 0; i < bucket.length; i += 1) {\n          const element = bucket[i];\n          if (element[0] === key) {\n            return element[1];\n          }\n        }\n      }\n      return;\n    }\n    const hashedKey = this.hash(key);\n    const bucket = this.map[hashedKey];\n    if (!bucket) {\n      return;\n    }\n    for (let i = 0; i < bucket.length; i += 1) {\n      const element = bucket[i];\n      if (element[0] === key) {\n        return element[1];\n      }\n    }\n  };\n  return _ObjectMap;\n}();", "map": {"version": 3, "names": ["_cloneRegExp", "type", "_clone", "value", "deep", "map", "_ObjectMap", "_isPrimitive", "copy", "copiedValue", "cachedCopy", "get", "set", "key", "Object", "prototype", "hasOwnProperty", "call", "create", "getPrototypeOf", "Date", "valueOf", "slice", "param", "length", "hashed<PERSON><PERSON>", "hash", "bucket", "push", "toString", "join", "p", "i", "element"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_clone.js"], "sourcesContent": ["import _cloneRegExp from \"./_cloneRegExp.js\";\nimport type from \"../type.js\";\n/**\n * Copies an object.\n *\n * @private\n * @param {*} value The value to be copied\n * @param {Boolean} deep Whether or not to perform deep cloning.\n * @return {*} The copied value.\n */\n\nexport default function _clone(value, deep, map) {\n  map || (map = new _ObjectMap()); // this avoids the slower switch with a quick if decision removing some milliseconds in each run.\n\n  if (_isPrimitive(value)) {\n    return value;\n  }\n\n  var copy = function copy(copiedValue) {\n    // Check for circular and same references on the object graph and return its corresponding clone.\n    var cachedCopy = map.get(value);\n\n    if (cachedCopy) {\n      return cachedCopy;\n    }\n\n    map.set(value, copiedValue);\n\n    for (var key in value) {\n      if (Object.prototype.hasOwnProperty.call(value, key)) {\n        copiedValue[key] = deep ? _clone(value[key], true, map) : value[key];\n      }\n    }\n\n    return copiedValue;\n  };\n\n  switch (type(value)) {\n    case 'Object':\n      return copy(Object.create(Object.getPrototypeOf(value)));\n\n    case 'Array':\n      return copy([]);\n\n    case 'Date':\n      return new Date(value.valueOf());\n\n    case 'RegExp':\n      return _cloneRegExp(value);\n\n    case 'Int8Array':\n    case 'Uint8Array':\n    case 'Uint8ClampedArray':\n    case 'Int16Array':\n    case 'Uint16Array':\n    case 'Int32Array':\n    case 'Uint32Array':\n    case 'Float32Array':\n    case 'Float64Array':\n    case 'BigInt64Array':\n    case 'BigUint64Array':\n      return value.slice();\n\n    default:\n      return value;\n  }\n}\n\nfunction _isPrimitive(param) {\n  var type = typeof param;\n  return param == null || type != 'object' && type != 'function';\n}\n\nvar _ObjectMap =\n/*#__PURE__*/\nfunction () {\n  function _ObjectMap() {\n    this.map = {};\n    this.length = 0;\n  }\n\n  _ObjectMap.prototype.set = function (key, value) {\n    const hashedKey = this.hash(key);\n    let bucket = this.map[hashedKey];\n\n    if (!bucket) {\n      this.map[hashedKey] = bucket = [];\n    }\n\n    bucket.push([key, value]);\n    this.length += 1;\n  };\n\n  _ObjectMap.prototype.hash = function (key) {\n    let hashedKey = [];\n\n    for (var value in key) {\n      hashedKey.push(Object.prototype.toString.call(key[value]));\n    }\n\n    return hashedKey.join();\n  };\n\n  _ObjectMap.prototype.get = function (key) {\n    /**\n     * depending on the number of objects to be cloned is faster to just iterate over the items in the map just because the hash function is so costly,\n     * on my tests this number is 180, anything above that using the hash function is faster.\n     */\n    if (this.length <= 180) {\n      for (const p in this.map) {\n        const bucket = this.map[p];\n\n        for (let i = 0; i < bucket.length; i += 1) {\n          const element = bucket[i];\n\n          if (element[0] === key) {\n            return element[1];\n          }\n        }\n      }\n\n      return;\n    }\n\n    const hashedKey = this.hash(key);\n    const bucket = this.map[hashedKey];\n\n    if (!bucket) {\n      return;\n    }\n\n    for (let i = 0; i < bucket.length; i += 1) {\n      const element = bucket[i];\n\n      if (element[0] === key) {\n        return element[1];\n      }\n    }\n  };\n\n  return _ObjectMap;\n}();"], "mappings": "AAAA,OAAOA,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,IAAI,MAAM,YAAY;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,MAAMA,CAACC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAE;EAC/CA,GAAG,KAAKA,GAAG,GAAG,IAAIC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEjC,IAAIC,YAAY,CAACJ,KAAK,CAAC,EAAE;IACvB,OAAOA,KAAK;EACd;EAEA,IAAIK,IAAI,GAAG,SAASA,IAAIA,CAACC,WAAW,EAAE;IACpC;IACA,IAAIC,UAAU,GAAGL,GAAG,CAACM,GAAG,CAACR,KAAK,CAAC;IAE/B,IAAIO,UAAU,EAAE;MACd,OAAOA,UAAU;IACnB;IAEAL,GAAG,CAACO,GAAG,CAACT,KAAK,EAAEM,WAAW,CAAC;IAE3B,KAAK,IAAII,GAAG,IAAIV,KAAK,EAAE;MACrB,IAAIW,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACd,KAAK,EAAEU,GAAG,CAAC,EAAE;QACpDJ,WAAW,CAACI,GAAG,CAAC,GAAGT,IAAI,GAAGF,MAAM,CAACC,KAAK,CAACU,GAAG,CAAC,EAAE,IAAI,EAAER,GAAG,CAAC,GAAGF,KAAK,CAACU,GAAG,CAAC;MACtE;IACF;IAEA,OAAOJ,WAAW;EACpB,CAAC;EAED,QAAQR,IAAI,CAACE,KAAK,CAAC;IACjB,KAAK,QAAQ;MACX,OAAOK,IAAI,CAACM,MAAM,CAACI,MAAM,CAACJ,MAAM,CAACK,cAAc,CAAChB,KAAK,CAAC,CAAC,CAAC;IAE1D,KAAK,OAAO;MACV,OAAOK,IAAI,CAAC,EAAE,CAAC;IAEjB,KAAK,MAAM;MACT,OAAO,IAAIY,IAAI,CAACjB,KAAK,CAACkB,OAAO,CAAC,CAAC,CAAC;IAElC,KAAK,QAAQ;MACX,OAAOrB,YAAY,CAACG,KAAK,CAAC;IAE5B,KAAK,WAAW;IAChB,KAAK,YAAY;IACjB,KAAK,mBAAmB;IACxB,KAAK,YAAY;IACjB,KAAK,aAAa;IAClB,KAAK,YAAY;IACjB,KAAK,aAAa;IAClB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,eAAe;IACpB,KAAK,gBAAgB;MACnB,OAAOA,KAAK,CAACmB,KAAK,CAAC,CAAC;IAEtB;MACE,OAAOnB,KAAK;EAChB;AACF;AAEA,SAASI,YAAYA,CAACgB,KAAK,EAAE;EAC3B,IAAItB,IAAI,GAAG,OAAOsB,KAAK;EACvB,OAAOA,KAAK,IAAI,IAAI,IAAItB,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,UAAU;AAChE;AAEA,IAAIK,UAAU,GACd;AACA,YAAY;EACV,SAASA,UAAUA,CAAA,EAAG;IACpB,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC;IACb,IAAI,CAACmB,MAAM,GAAG,CAAC;EACjB;EAEAlB,UAAU,CAACS,SAAS,CAACH,GAAG,GAAG,UAAUC,GAAG,EAAEV,KAAK,EAAE;IAC/C,MAAMsB,SAAS,GAAG,IAAI,CAACC,IAAI,CAACb,GAAG,CAAC;IAChC,IAAIc,MAAM,GAAG,IAAI,CAACtB,GAAG,CAACoB,SAAS,CAAC;IAEhC,IAAI,CAACE,MAAM,EAAE;MACX,IAAI,CAACtB,GAAG,CAACoB,SAAS,CAAC,GAAGE,MAAM,GAAG,EAAE;IACnC;IAEAA,MAAM,CAACC,IAAI,CAAC,CAACf,GAAG,EAAEV,KAAK,CAAC,CAAC;IACzB,IAAI,CAACqB,MAAM,IAAI,CAAC;EAClB,CAAC;EAEDlB,UAAU,CAACS,SAAS,CAACW,IAAI,GAAG,UAAUb,GAAG,EAAE;IACzC,IAAIY,SAAS,GAAG,EAAE;IAElB,KAAK,IAAItB,KAAK,IAAIU,GAAG,EAAE;MACrBY,SAAS,CAACG,IAAI,CAACd,MAAM,CAACC,SAAS,CAACc,QAAQ,CAACZ,IAAI,CAACJ,GAAG,CAACV,KAAK,CAAC,CAAC,CAAC;IAC5D;IAEA,OAAOsB,SAAS,CAACK,IAAI,CAAC,CAAC;EACzB,CAAC;EAEDxB,UAAU,CAACS,SAAS,CAACJ,GAAG,GAAG,UAAUE,GAAG,EAAE;IACxC;AACJ;AACA;AACA;IACI,IAAI,IAAI,CAACW,MAAM,IAAI,GAAG,EAAE;MACtB,KAAK,MAAMO,CAAC,IAAI,IAAI,CAAC1B,GAAG,EAAE;QACxB,MAAMsB,MAAM,GAAG,IAAI,CAACtB,GAAG,CAAC0B,CAAC,CAAC;QAE1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAACH,MAAM,EAAEQ,CAAC,IAAI,CAAC,EAAE;UACzC,MAAMC,OAAO,GAAGN,MAAM,CAACK,CAAC,CAAC;UAEzB,IAAIC,OAAO,CAAC,CAAC,CAAC,KAAKpB,GAAG,EAAE;YACtB,OAAOoB,OAAO,CAAC,CAAC,CAAC;UACnB;QACF;MACF;MAEA;IACF;IAEA,MAAMR,SAAS,GAAG,IAAI,CAACC,IAAI,CAACb,GAAG,CAAC;IAChC,MAAMc,MAAM,GAAG,IAAI,CAACtB,GAAG,CAACoB,SAAS,CAAC;IAElC,IAAI,CAACE,MAAM,EAAE;MACX;IACF;IAEA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAACH,MAAM,EAAEQ,CAAC,IAAI,CAAC,EAAE;MACzC,MAAMC,OAAO,GAAGN,MAAM,CAACK,CAAC,CAAC;MAEzB,IAAIC,OAAO,CAAC,CAAC,CAAC,KAAKpB,GAAG,EAAE;QACtB,OAAOoB,OAAO,CAAC,CAAC,CAAC;MACnB;IACF;EACF,CAAC;EAED,OAAO3B,UAAU;AACnB,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}