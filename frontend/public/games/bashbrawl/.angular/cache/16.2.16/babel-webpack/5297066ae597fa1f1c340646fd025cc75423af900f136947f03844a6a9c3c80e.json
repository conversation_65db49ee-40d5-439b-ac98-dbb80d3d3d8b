{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"usb\",\n  L = [\"usb\", C({\n    outline: '<path d=\"M33.5098 17.1498L27.648 13.788C27.3379 13.609 26.9478 13.609 26.6377 13.788C26.3276 13.967 26.1375 14.2952 26.1375 14.6533V17.0006H9.90247C9.45233 15.2799 7.89184 13.9969 6.02126 13.9969C3.80056 13.9969 2 15.7872 2 17.9952C2 20.2032 3.80056 21.9935 6.02126 21.9935C7.89184 21.9935 9.45233 20.7105 9.90247 18.9898H14.3739L17.1447 26.34C17.2948 26.7279 17.6649 26.9865 18.085 26.9865H20.0957V28.9857C20.0957 29.5427 20.5458 29.9902 21.106 29.9902H25.1272C25.6874 29.9902 26.1375 29.5427 26.1375 28.9857V24.9873C26.1375 24.4303 25.6874 23.9828 25.1272 23.9828H21.106C20.5458 23.9828 20.0957 24.4303 20.0957 24.9873H18.7852L16.5245 18.9898H26.1375V21.3669C26.1375 21.725 26.3276 22.0532 26.6377 22.2323C26.7977 22.3218 26.9678 22.3616 27.1379 22.3616C27.3079 22.3616 27.488 22.3118 27.638 22.2323L33.4998 18.8705C33.8099 18.6914 34 18.3632 34 18.0052C34 17.6471 33.8099 17.3189 33.4998 17.1398L33.5098 17.1498ZM6.02126 20.0043C4.91091 20.0043 4.01063 19.1092 4.01063 18.0052C4.01063 16.9011 4.91091 16.006 6.02126 16.006C7.1316 16.006 8.03189 16.9011 8.03189 18.0052C8.03189 19.1092 7.1316 20.0043 6.02126 20.0043ZM22.1163 26.0018H24.1269V28.001H22.1163V26.0018ZM28.1482 19.6463V16.3839L30.9891 18.0151L28.1482 19.6463ZM16.6746 9.99852H18.2751C18.6952 11.1622 19.7956 11.9977 21.106 11.9977C22.7665 11.9977 24.1269 10.655 24.1269 8.99396C24.1269 7.33296 22.7765 5.99023 21.106 5.99023C19.7956 5.99023 18.6952 6.82571 18.2751 7.98941H16.0744C15.7043 7.98941 15.3742 8.18833 15.1941 8.5066L11.573 14.9915H13.8737L16.6646 9.98858L16.6746 9.99852ZM21.106 7.99935C21.6561 7.99935 22.1163 8.44693 22.1163 9.00391C22.1163 9.56089 21.6661 10.0085 21.106 10.0085C20.5458 10.0085 20.0957 9.56089 20.0957 9.00391C20.0957 8.44693 20.5458 7.99935 21.106 7.99935Z\"/>',\n    solid: '<path d=\"M33.4874 17.15L27.6281 13.78C27.3166 13.6 26.9347 13.6 26.6231 13.78C26.3116 13.96 26.1206 14.29 26.1206 14.65V17H9.8995C9.44724 15.28 7.88945 14 6.0201 14C3.799 14 2 15.79 2 18C2 20.21 3.799 22 6.0201 22C7.88945 22 9.44724 20.72 9.8995 19H14.3719L17.1457 26.35C17.2965 26.74 17.6683 27 18.0905 27H20.1005V29C20.1005 29.55 20.5528 30 21.1055 30H25.1256C25.6784 30 26.1307 29.55 26.1307 29V25C26.1307 24.45 25.6784 24 25.1256 24H21.1055C20.5528 24 20.1005 24.45 20.1005 25H18.7839L16.5226 19H26.1307V21.38C26.1307 21.74 26.3216 22.07 26.6332 22.25C26.7839 22.34 26.9648 22.38 27.1357 22.38C27.3065 22.38 27.4874 22.34 27.6382 22.25L33.4975 18.88C33.809 18.7 34 18.37 34 18.01C34 17.65 33.809 17.32 33.4975 17.14L33.4874 17.15ZM16.6633 10H18.2714C18.6834 11.16 19.7889 12 21.1055 12C22.7638 12 24.1206 10.65 24.1206 9C24.1206 7.35 22.7638 6 21.1055 6C19.799 6 18.6935 6.84 18.2714 8H16.0804C15.7186 8 15.3769 8.2 15.206 8.51L11.5879 15H13.8894L16.6834 10H16.6633Z\"/>'\n  })];\nexport { L as usbIcon, H as usbIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "L", "outline", "solid", "usbIcon", "usbIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/usb.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"usb\",L=[\"usb\",C({outline:'<path d=\"M33.5098 17.1498L27.648 13.788C27.3379 13.609 26.9478 13.609 26.6377 13.788C26.3276 13.967 26.1375 14.2952 26.1375 14.6533V17.0006H9.90247C9.45233 15.2799 7.89184 13.9969 6.02126 13.9969C3.80056 13.9969 2 15.7872 2 17.9952C2 20.2032 3.80056 21.9935 6.02126 21.9935C7.89184 21.9935 9.45233 20.7105 9.90247 18.9898H14.3739L17.1447 26.34C17.2948 26.7279 17.6649 26.9865 18.085 26.9865H20.0957V28.9857C20.0957 29.5427 20.5458 29.9902 21.106 29.9902H25.1272C25.6874 29.9902 26.1375 29.5427 26.1375 28.9857V24.9873C26.1375 24.4303 25.6874 23.9828 25.1272 23.9828H21.106C20.5458 23.9828 20.0957 24.4303 20.0957 24.9873H18.7852L16.5245 18.9898H26.1375V21.3669C26.1375 21.725 26.3276 22.0532 26.6377 22.2323C26.7977 22.3218 26.9678 22.3616 27.1379 22.3616C27.3079 22.3616 27.488 22.3118 27.638 22.2323L33.4998 18.8705C33.8099 18.6914 34 18.3632 34 18.0052C34 17.6471 33.8099 17.3189 33.4998 17.1398L33.5098 17.1498ZM6.02126 20.0043C4.91091 20.0043 4.01063 19.1092 4.01063 18.0052C4.01063 16.9011 4.91091 16.006 6.02126 16.006C7.1316 16.006 8.03189 16.9011 8.03189 18.0052C8.03189 19.1092 7.1316 20.0043 6.02126 20.0043ZM22.1163 26.0018H24.1269V28.001H22.1163V26.0018ZM28.1482 19.6463V16.3839L30.9891 18.0151L28.1482 19.6463ZM16.6746 9.99852H18.2751C18.6952 11.1622 19.7956 11.9977 21.106 11.9977C22.7665 11.9977 24.1269 10.655 24.1269 8.99396C24.1269 7.33296 22.7765 5.99023 21.106 5.99023C19.7956 5.99023 18.6952 6.82571 18.2751 7.98941H16.0744C15.7043 7.98941 15.3742 8.18833 15.1941 8.5066L11.573 14.9915H13.8737L16.6646 9.98858L16.6746 9.99852ZM21.106 7.99935C21.6561 7.99935 22.1163 8.44693 22.1163 9.00391C22.1163 9.56089 21.6661 10.0085 21.106 10.0085C20.5458 10.0085 20.0957 9.56089 20.0957 9.00391C20.0957 8.44693 20.5458 7.99935 21.106 7.99935Z\"/>',solid:'<path d=\"M33.4874 17.15L27.6281 13.78C27.3166 13.6 26.9347 13.6 26.6231 13.78C26.3116 13.96 26.1206 14.29 26.1206 14.65V17H9.8995C9.44724 15.28 7.88945 14 6.0201 14C3.799 14 2 15.79 2 18C2 20.21 3.799 22 6.0201 22C7.88945 22 9.44724 20.72 9.8995 19H14.3719L17.1457 26.35C17.2965 26.74 17.6683 27 18.0905 27H20.1005V29C20.1005 29.55 20.5528 30 21.1055 30H25.1256C25.6784 30 26.1307 29.55 26.1307 29V25C26.1307 24.45 25.6784 24 25.1256 24H21.1055C20.5528 24 20.1005 24.45 20.1005 25H18.7839L16.5226 19H26.1307V21.38C26.1307 21.74 26.3216 22.07 26.6332 22.25C26.7839 22.34 26.9648 22.38 27.1357 22.38C27.3065 22.38 27.4874 22.34 27.6382 22.25L33.4975 18.88C33.809 18.7 34 18.37 34 18.01C34 17.65 33.809 17.32 33.4975 17.14L33.4874 17.15ZM16.6633 10H18.2714C18.6834 11.16 19.7889 12 21.1055 12C22.7638 12 24.1206 10.65 24.1206 9C24.1206 7.35 22.7638 6 21.1055 6C19.799 6 18.6935 6.84 18.2714 8H16.0804C15.7186 8 15.3769 8.2 15.206 8.51L11.5879 15H13.8894L16.6834 10H16.6633Z\"/>'})];export{L as usbIcon,H as usbIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,CAAC,KAAK,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,+tDAA+tD;IAACC,KAAK,EAAC;EAA+8B,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,OAAO,EAACJ,CAAC,IAAIK,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}