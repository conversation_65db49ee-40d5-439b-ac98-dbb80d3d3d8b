{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst V = \"gym\",\n  e = [\"gym\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M9.9375 26C8.2875 26 6.9375 24.65 6.9375 23V22.8276C6.62437 22.9392 6.28769 23 5.9375 23C4.2875 23 2.9375 21.65 2.9375 20V16C2.9375 14.35 4.2875 13 5.9375 13C6.28769 13 6.62437 13.0608 6.9375 13.1724V13C6.9375 11.35 8.2875 10 9.9375 10C11.5875 10 12.9375 11.35 12.9375 13V17H24.9375V13C24.9375 11.35 26.2875 10 27.9375 10C29.5875 10 30.9375 11.35 30.9375 13V13.1724C31.2506 13.0608 31.5873 13 31.9375 13C33.5875 13 34.9375 14.35 34.9375 16V20C34.9375 21.65 33.5875 23 31.9375 23C31.5873 23 31.2506 22.9392 30.9375 22.8276V23C30.9375 24.65 29.5875 26 27.9375 26C26.2875 26 24.9375 24.65 24.9375 23V19H12.9375V23C12.9375 24.65 11.5875 26 9.9375 26ZM6.9375 16V20C6.9375 20.55 6.4875 21 5.9375 21C5.3875 21 4.9375 20.55 4.9375 20V16C4.9375 15.45 5.3875 15 5.9375 15C6.4875 15 6.9375 15.45 6.9375 16ZM8.9375 23V13C8.9375 12.45 9.3875 12 9.9375 12C10.4875 12 10.9375 12.45 10.9375 13V23C10.9375 23.55 10.4875 24 9.9375 24C9.3875 24 8.9375 23.55 8.9375 23ZM28.9375 23C28.9375 23.55 28.4875 24 27.9375 24C27.3875 24 26.9375 23.55 26.9375 23V13C26.9375 12.45 27.3875 12 27.9375 12C28.4875 12 28.9375 12.45 28.9375 13V23ZM31.9375 21C31.3875 21 30.9375 20.55 30.9375 20V16C30.9375 15.45 31.3875 15 31.9375 15C32.4875 15 32.9375 15.45 32.9375 16V20C32.9375 20.55 32.4875 21 31.9375 21Z\"/>',\n    solid: '<path d=\"M9.9375 26C8.2875 26 6.9375 24.65 6.9375 23V22.8276C6.62437 22.9392 6.28769 23 5.9375 23C4.2875 23 2.9375 21.65 2.9375 20V16C2.9375 14.35 4.2875 13 5.9375 13C6.28769 13 6.62437 13.0608 6.9375 13.1724V13C6.9375 11.35 8.2875 10 9.9375 10C11.5875 10 12.9375 11.35 12.9375 13V17H24.9375V13C24.9375 11.35 26.2875 10 27.9375 10C29.5875 10 30.9375 11.35 30.9375 13V13.1724C31.2506 13.0608 31.5873 13 31.9375 13C33.5875 13 34.9375 14.35 34.9375 16V20C34.9375 21.65 33.5875 23 31.9375 23C31.5873 23 31.2506 22.9392 30.9375 22.8276V23C30.9375 24.65 29.5875 26 27.9375 26C26.2875 26 24.9375 24.65 24.9375 23V19H12.9375V23C12.9375 24.65 11.5875 26 9.9375 26Z\"/>'\n  })];\nexport { e as gymIcon, V as gymIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "V", "e", "outline", "solid", "gymIcon", "gymIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/gym.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const V=\"gym\",e=[\"gym\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M9.9375 26C8.2875 26 6.9375 24.65 6.9375 23V22.8276C6.62437 22.9392 6.28769 23 5.9375 23C4.2875 23 2.9375 21.65 2.9375 20V16C2.9375 14.35 4.2875 13 5.9375 13C6.28769 13 6.62437 13.0608 6.9375 13.1724V13C6.9375 11.35 8.2875 10 9.9375 10C11.5875 10 12.9375 11.35 12.9375 13V17H24.9375V13C24.9375 11.35 26.2875 10 27.9375 10C29.5875 10 30.9375 11.35 30.9375 13V13.1724C31.2506 13.0608 31.5873 13 31.9375 13C33.5875 13 34.9375 14.35 34.9375 16V20C34.9375 21.65 33.5875 23 31.9375 23C31.5873 23 31.2506 22.9392 30.9375 22.8276V23C30.9375 24.65 29.5875 26 27.9375 26C26.2875 26 24.9375 24.65 24.9375 23V19H12.9375V23C12.9375 24.65 11.5875 26 9.9375 26ZM6.9375 16V20C6.9375 20.55 6.4875 21 5.9375 21C5.3875 21 4.9375 20.55 4.9375 20V16C4.9375 15.45 5.3875 15 5.9375 15C6.4875 15 6.9375 15.45 6.9375 16ZM8.9375 23V13C8.9375 12.45 9.3875 12 9.9375 12C10.4875 12 10.9375 12.45 10.9375 13V23C10.9375 23.55 10.4875 24 9.9375 24C9.3875 24 8.9375 23.55 8.9375 23ZM28.9375 23C28.9375 23.55 28.4875 24 27.9375 24C27.3875 24 26.9375 23.55 26.9375 23V13C26.9375 12.45 27.3875 12 27.9375 12C28.4875 12 28.9375 12.45 28.9375 13V23ZM31.9375 21C31.3875 21 30.9375 20.55 30.9375 20V16C30.9375 15.45 31.3875 15 31.9375 15C32.4875 15 32.9375 15.45 32.9375 16V20C32.9375 20.55 32.4875 21 31.9375 21Z\"/>',solid:'<path d=\"M9.9375 26C8.2875 26 6.9375 24.65 6.9375 23V22.8276C6.62437 22.9392 6.28769 23 5.9375 23C4.2875 23 2.9375 21.65 2.9375 20V16C2.9375 14.35 4.2875 13 5.9375 13C6.28769 13 6.62437 13.0608 6.9375 13.1724V13C6.9375 11.35 8.2875 10 9.9375 10C11.5875 10 12.9375 11.35 12.9375 13V17H24.9375V13C24.9375 11.35 26.2875 10 27.9375 10C29.5875 10 30.9375 11.35 30.9375 13V13.1724C31.2506 13.0608 31.5873 13 31.9375 13C33.5875 13 34.9375 14.35 34.9375 16V20C34.9375 21.65 33.5875 23 31.9375 23C31.5873 23 31.2506 22.9392 30.9375 22.8276V23C30.9375 24.65 29.5875 26 27.9375 26C26.2875 26 24.9375 24.65 24.9375 23V19H12.9375V23C12.9375 24.65 11.5875 26 9.9375 26Z\"/>'})];export{e as gymIcon,V as gymIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,CAAC,KAAK,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,+yCAA+yC;IAACC,KAAK,EAAC;EAAopB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,OAAO,EAACJ,CAAC,IAAIK,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}