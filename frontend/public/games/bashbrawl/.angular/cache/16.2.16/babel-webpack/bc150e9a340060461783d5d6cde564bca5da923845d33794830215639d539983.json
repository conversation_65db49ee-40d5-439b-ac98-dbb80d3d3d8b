{"ast": null, "code": "export default function _isTransformer(obj) {\n  return obj != null && typeof obj['@@transducer/step'] === 'function';\n}", "map": {"version": 3, "names": ["_isTransformer", "obj"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_isTransformer.js"], "sourcesContent": ["export default function _isTransformer(obj) {\n  return obj != null && typeof obj['@@transducer/step'] === 'function';\n}"], "mappings": "AAAA,eAAe,SAASA,cAAcA,CAACC,GAAG,EAAE;EAC1C,OAAOA,GAAG,IAAI,IAAI,IAAI,OAAOA,GAAG,CAAC,mBAAmB,CAAC,KAAK,UAAU;AACtE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}