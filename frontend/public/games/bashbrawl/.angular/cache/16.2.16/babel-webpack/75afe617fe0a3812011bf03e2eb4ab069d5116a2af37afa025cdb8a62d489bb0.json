{"ast": null, "code": "import { renderIcon as o } from \"../icon.renderer.js\";\nconst e = \"node\",\n  n = [\"node\", o({\n    outline: '<path d=\"M18,30.66,7,24.33V11.67L18,5.34l11,6.33V24.33ZM9,23.18l9,5.17,9-5.17V12.82L18,7.65,9,12.82Z\"/>'\n  })];\nexport { n as nodeIcon, e as nodeIconName };", "map": {"version": 3, "names": ["renderIcon", "o", "e", "n", "outline", "nodeIcon", "nodeIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/node.js"], "sourcesContent": ["import{renderIcon as o}from\"../icon.renderer.js\";const e=\"node\",n=[\"node\",o({outline:'<path d=\"M18,30.66,7,24.33V11.67L18,5.34l11,6.33V24.33ZM9,23.18l9,5.17,9-5.17V12.82L18,7.65,9,12.82Z\"/>'})];export{n as nodeIcon,e as nodeIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,MAAM;EAACC,CAAC,GAAC,CAAC,MAAM,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAyG,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,QAAQ,EAACH,CAAC,IAAII,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}