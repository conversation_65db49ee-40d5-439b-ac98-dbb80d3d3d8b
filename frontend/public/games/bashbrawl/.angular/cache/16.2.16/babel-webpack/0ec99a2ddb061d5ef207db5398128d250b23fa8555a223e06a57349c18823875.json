{"ast": null, "code": "const a = \"cds-navigation-open\",\n  i = [{\n    target: \"cds-navigation\",\n    animation: [{\n      width: \"var(--collapsed-width)\"\n    }, {\n      width: \"var(--expanded-width)\"\n    }],\n    options: {\n      duration: \"--animation-duration\",\n      easing: \"--animation-easing\",\n      fill: \"forwards\"\n    }\n  }];\nexport { i as AnimationNavigationOpenConfig, a as AnimationNavigationOpenName };", "map": {"version": 3, "names": ["a", "i", "target", "animation", "width", "options", "duration", "easing", "fill", "AnimationNavigationOpenConfig", "AnimationNavigationOpenName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/motion/animations/cds-navigation-open.js"], "sourcesContent": ["const a=\"cds-navigation-open\",i=[{target:\"cds-navigation\",animation:[{width:\"var(--collapsed-width)\"},{width:\"var(--expanded-width)\"}],options:{duration:\"--animation-duration\",easing:\"--animation-easing\",fill:\"forwards\"}}];export{i as AnimationNavigationOpenConfig,a as AnimationNavigationOpenName};\n"], "mappings": "AAAA,MAAMA,CAAC,GAAC,qBAAqB;EAACC,CAAC,GAAC,CAAC;IAACC,MAAM,EAAC,gBAAgB;IAACC,SAAS,EAAC,CAAC;MAACC,KAAK,EAAC;IAAwB,CAAC,EAAC;MAACA,KAAK,EAAC;IAAuB,CAAC,CAAC;IAACC,OAAO,EAAC;MAACC,QAAQ,EAAC,sBAAsB;MAACC,MAAM,EAAC,oBAAoB;MAACC,IAAI,EAAC;IAAU;EAAC,CAAC,CAAC;AAAC,SAAOP,CAAC,IAAIQ,6BAA6B,EAACT,CAAC,IAAIU,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}