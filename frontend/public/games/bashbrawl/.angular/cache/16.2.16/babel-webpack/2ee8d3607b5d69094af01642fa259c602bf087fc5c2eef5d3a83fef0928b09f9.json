{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst standardProperty = (options, element) => {\n  // When decorating an accessor, pass it through and add property metadata.\n  // Note, the `hasOwnProperty` check in `createProperty` ensures we don't\n  // stomp over the user's accessor.\n  if (element.kind === 'method' && element.descriptor && !('value' in element.descriptor)) {\n    return {\n      ...element,\n      finisher(clazz) {\n        clazz.createProperty(element.key, options);\n      }\n    };\n  } else {\n    // createProperty() takes care of defining the property, but we still\n    // must return some kind of descriptor, so return a descriptor for an\n    // unused prototype field. The finisher calls createProperty().\n    return {\n      kind: 'field',\n      key: Symbol(),\n      placement: 'own',\n      descriptor: {},\n      // store the original key so subsequent decorators have access to it.\n      originalKey: element.key,\n      // When @babel/plugin-proposal-decorators implements initializers,\n      // do this instead of the initializer below. See:\n      // https://github.com/babel/babel/issues/9260 extras: [\n      //   {\n      //     kind: 'initializer',\n      //     placement: 'own',\n      //     initializer: descriptor.initializer,\n      //   }\n      // ],\n      initializer() {\n        if (typeof element.initializer === 'function') {\n          this[element.key] = element.initializer.call(this);\n        }\n      },\n      finisher(clazz) {\n        clazz.createProperty(element.key, options);\n      }\n    };\n  }\n};\nconst legacyProperty = (options, proto, name) => {\n  proto.constructor.createProperty(name, options);\n};\n/**\n * A property decorator which creates a reactive property that reflects a\n * corresponding attribute value. When a decorated property is set\n * the element will update and render. A {@linkcode PropertyDeclaration} may\n * optionally be supplied to configure property features.\n *\n * This decorator should only be used for public fields. As public fields,\n * properties should be considered as primarily settable by element users,\n * either via attribute or the property itself.\n *\n * Generally, properties that are changed by the element should be private or\n * protected fields and should use the {@linkcode state} decorator.\n *\n * However, sometimes element code does need to set a public property. This\n * should typically only be done in response to user interaction, and an event\n * should be fired informing the user; for example, a checkbox sets its\n * `checked` property when clicked and fires a `changed` event. Mutating public\n * properties should typically not be done for non-primitive (object or array)\n * properties. In other cases when an element needs to manage state, a private\n * property decorated via the {@linkcode state} decorator should be used. When\n * needed, state properties can be initialized via public properties to\n * facilitate complex interactions.\n *\n * ```ts\n * class MyElement {\n *   @property({ type: Boolean })\n *   clicked = false;\n * }\n * ```\n * @category Decorator\n * @ExportDecoratedItems\n */\nexport function property(options) {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return (protoOrDescriptor, name) => name !== undefined ? legacyProperty(options, protoOrDescriptor, name) : standardProperty(options, protoOrDescriptor);\n}", "map": {"version": 3, "names": ["standardProperty", "options", "element", "kind", "descriptor", "finisher", "clazz", "createProperty", "key", "Symbol", "placement", "original<PERSON>ey", "initializer", "call", "legacyProperty", "proto", "name", "constructor", "property", "protoOrDescriptor", "undefined"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@lit/reactive-element/development/decorators/property.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst standardProperty = (options, element) => {\n    // When decorating an accessor, pass it through and add property metadata.\n    // Note, the `hasOwnProperty` check in `createProperty` ensures we don't\n    // stomp over the user's accessor.\n    if (element.kind === 'method' &&\n        element.descriptor &&\n        !('value' in element.descriptor)) {\n        return {\n            ...element,\n            finisher(clazz) {\n                clazz.createProperty(element.key, options);\n            },\n        };\n    }\n    else {\n        // createProperty() takes care of defining the property, but we still\n        // must return some kind of descriptor, so return a descriptor for an\n        // unused prototype field. The finisher calls createProperty().\n        return {\n            kind: 'field',\n            key: Symbol(),\n            placement: 'own',\n            descriptor: {},\n            // store the original key so subsequent decorators have access to it.\n            originalKey: element.key,\n            // When @babel/plugin-proposal-decorators implements initializers,\n            // do this instead of the initializer below. See:\n            // https://github.com/babel/babel/issues/9260 extras: [\n            //   {\n            //     kind: 'initializer',\n            //     placement: 'own',\n            //     initializer: descriptor.initializer,\n            //   }\n            // ],\n            initializer() {\n                if (typeof element.initializer === 'function') {\n                    this[element.key] = element.initializer.call(this);\n                }\n            },\n            finisher(clazz) {\n                clazz.createProperty(element.key, options);\n            },\n        };\n    }\n};\nconst legacyProperty = (options, proto, name) => {\n    proto.constructor.createProperty(name, options);\n};\n/**\n * A property decorator which creates a reactive property that reflects a\n * corresponding attribute value. When a decorated property is set\n * the element will update and render. A {@linkcode PropertyDeclaration} may\n * optionally be supplied to configure property features.\n *\n * This decorator should only be used for public fields. As public fields,\n * properties should be considered as primarily settable by element users,\n * either via attribute or the property itself.\n *\n * Generally, properties that are changed by the element should be private or\n * protected fields and should use the {@linkcode state} decorator.\n *\n * However, sometimes element code does need to set a public property. This\n * should typically only be done in response to user interaction, and an event\n * should be fired informing the user; for example, a checkbox sets its\n * `checked` property when clicked and fires a `changed` event. Mutating public\n * properties should typically not be done for non-primitive (object or array)\n * properties. In other cases when an element needs to manage state, a private\n * property decorated via the {@linkcode state} decorator should be used. When\n * needed, state properties can be initialized via public properties to\n * facilitate complex interactions.\n *\n * ```ts\n * class MyElement {\n *   @property({ type: Boolean })\n *   clicked = false;\n * }\n * ```\n * @category Decorator\n * @ExportDecoratedItems\n */\nexport function property(options) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return (protoOrDescriptor, name) => name !== undefined\n        ? legacyProperty(options, protoOrDescriptor, name)\n        : standardProperty(options, protoOrDescriptor);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,MAAMA,gBAAgB,GAAGA,CAACC,OAAO,EAAEC,OAAO,KAAK;EAC3C;EACA;EACA;EACA,IAAIA,OAAO,CAACC,IAAI,KAAK,QAAQ,IACzBD,OAAO,CAACE,UAAU,IAClB,EAAE,OAAO,IAAIF,OAAO,CAACE,UAAU,CAAC,EAAE;IAClC,OAAO;MACH,GAAGF,OAAO;MACVG,QAAQA,CAACC,KAAK,EAAE;QACZA,KAAK,CAACC,cAAc,CAACL,OAAO,CAACM,GAAG,EAAEP,OAAO,CAAC;MAC9C;IACJ,CAAC;EACL,CAAC,MACI;IACD;IACA;IACA;IACA,OAAO;MACHE,IAAI,EAAE,OAAO;MACbK,GAAG,EAAEC,MAAM,CAAC,CAAC;MACbC,SAAS,EAAE,KAAK;MAChBN,UAAU,EAAE,CAAC,CAAC;MACd;MACAO,WAAW,EAAET,OAAO,CAACM,GAAG;MACxB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAI,WAAWA,CAAA,EAAG;QACV,IAAI,OAAOV,OAAO,CAACU,WAAW,KAAK,UAAU,EAAE;UAC3C,IAAI,CAACV,OAAO,CAACM,GAAG,CAAC,GAAGN,OAAO,CAACU,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC;QACtD;MACJ,CAAC;MACDR,QAAQA,CAACC,KAAK,EAAE;QACZA,KAAK,CAACC,cAAc,CAACL,OAAO,CAACM,GAAG,EAAEP,OAAO,CAAC;MAC9C;IACJ,CAAC;EACL;AACJ,CAAC;AACD,MAAMa,cAAc,GAAGA,CAACb,OAAO,EAAEc,KAAK,EAAEC,IAAI,KAAK;EAC7CD,KAAK,CAACE,WAAW,CAACV,cAAc,CAACS,IAAI,EAAEf,OAAO,CAAC;AACnD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASiB,QAAQA,CAACjB,OAAO,EAAE;EAC9B;EACA,OAAO,CAACkB,iBAAiB,EAAEH,IAAI,KAAKA,IAAI,KAAKI,SAAS,GAChDN,cAAc,CAACb,OAAO,EAAEkB,iBAAiB,EAAEH,IAAI,CAAC,GAChDhB,gBAAgB,CAACC,OAAO,EAAEkB,iBAAiB,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}