{"ast": null, "code": "import { render as e } from \"lit\";\nfunction r(e) {\n  return Promise.all(Array.from(e).map(e => e.updateComplete));\n}\nfunction n(e, r, n) {\n  e.forEach((e, o) => n.filter(e => e && void 0 !== e[o]).forEach(e => e[o] = r[o]));\n}\nfunction o(e, r, n) {\n  Object.keys(n).filter(e => n[e]).forEach(n => e[n] = r[n]);\n}\nfunction t(e, r, n) {\n  e.forEach(e => o(e, r, n));\n}\nfunction i(e, r, n) {\n  return r.has(n) && e[n] !== r.get(n);\n}\nfunction c(r, n, o) {\n  const t = document.createElement(\"div\");\n  n.prepend(t), e(r, n, {\n    renderBefore: t,\n    ...o\n  });\n  const i = t.previousSibling;\n  return t.remove(), i;\n}\nfunction f(r, n, o) {\n  const t = document.createElement(\"div\");\n  n.appendChild(t), e(r, n, {\n    renderBefore: t,\n    ...o\n  });\n  const i = t.previousSibling;\n  return t.remove(), i;\n}\nexport { r as childrenUpdateComplete, i as propUpdated, f as renderAfter, c as renderBefore, n as syncDefinedProps, o as syncProps, t as syncPropsForAllItems };", "map": {"version": 3, "names": ["render", "e", "r", "Promise", "all", "Array", "from", "map", "updateComplete", "n", "for<PERSON>ach", "o", "filter", "Object", "keys", "t", "i", "has", "get", "c", "document", "createElement", "prepend", "renderBefore", "previousSibling", "remove", "f", "append<PERSON><PERSON><PERSON>", "childrenUpdateComplete", "propUpdated", "renderAfter", "syncDefinedProps", "syncProps", "syncPropsForAllItems"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/lit.js"], "sourcesContent": ["import{render as e}from\"lit\";function r(e){return Promise.all(Array.from(e).map((e=>e.updateComplete)))}function n(e,r,n){e.forEach(((e,o)=>n.filter((e=>e&&void 0!==e[o])).forEach((e=>e[o]=r[o]))))}function o(e,r,n){Object.keys(n).filter((e=>n[e])).forEach((n=>e[n]=r[n]))}function t(e,r,n){e.forEach((e=>o(e,r,n)))}function i(e,r,n){return r.has(n)&&e[n]!==r.get(n)}function c(r,n,o){const t=document.createElement(\"div\");n.prepend(t),e(r,n,{renderBefore:t,...o});const i=t.previousSibling;return t.remove(),i}function f(r,n,o){const t=document.createElement(\"div\");n.appendChild(t),e(r,n,{renderBefore:t,...o});const i=t.previousSibling;return t.remove(),i}export{r as childrenUpdateComplete,i as propUpdated,f as renderAfter,c as renderBefore,n as syncDefinedProps,o as syncProps,t as syncPropsForAllItems};\n"], "mappings": "AAAA,SAAOA,MAAM,IAAIC,CAAC,QAAK,KAAK;AAAC,SAASC,CAACA,CAACD,CAAC,EAAC;EAAC,OAAOE,OAAO,CAACC,GAAG,CAACC,KAAK,CAACC,IAAI,CAACL,CAAC,CAAC,CAACM,GAAG,CAAEN,CAAC,IAAEA,CAAC,CAACO,cAAe,CAAC,CAAC;AAAA;AAAC,SAASC,CAACA,CAACR,CAAC,EAACC,CAAC,EAACO,CAAC,EAAC;EAACR,CAAC,CAACS,OAAO,CAAE,CAACT,CAAC,EAACU,CAAC,KAAGF,CAAC,CAACG,MAAM,CAAEX,CAAC,IAAEA,CAAC,IAAE,KAAK,CAAC,KAAGA,CAAC,CAACU,CAAC,CAAE,CAAC,CAACD,OAAO,CAAET,CAAC,IAAEA,CAAC,CAACU,CAAC,CAAC,GAACT,CAAC,CAACS,CAAC,CAA<PERSON>,CAAE,CAAC;AAAA;AAAC,SAASA,CAACA,CAACV,CAAC,EAACC,CAAC,EAACO,CAAC,EAAC;EAACI,MAAM,CAACC,IAAI,CAACL,CAAC,CAAC,CAACG,MAAM,CAAEX,CAAC,IAAEQ,CAAC,CAACR,CAAC,CAAE,CAAC,CAACS,OAAO,CAAED,CAAC,IAAER,CAAC,CAACQ,CAAC,CAAC,GAACP,CAAC,CAACO,CAAC,CAAE,CAAC;AAAA;AAAC,SAASM,CAACA,CAACd,CAAC,EAACC,CAAC,EAACO,CAAC,EAAC;EAACR,CAAC,CAACS,OAAO,CAAET,CAAC,IAAEU,CAAC,CAACV,CAAC,EAACC,CAAC,EAACO,CAAC,CAAE,CAAC;AAAA;AAAC,SAASO,CAACA,CAACf,CAAC,EAACC,CAAC,EAACO,CAAC,EAAC;EAAC,OAAOP,CAAC,CAACe,GAAG,CAACR,CAAC,CAAC,IAAER,CAAC,CAACQ,CAAC,CAAC,KAAGP,CAAC,CAACgB,GAAG,CAACT,CAAC,CAAC;AAAA;AAAC,SAASU,CAACA,CAACjB,CAAC,EAACO,CAAC,EAACE,CAAC,EAAC;EAAC,MAAMI,CAAC,GAACK,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAACZ,CAAC,CAACa,OAAO,CAACP,CAAC,CAAC,EAACd,CAAC,CAACC,CAAC,EAACO,CAAC,EAAC;IAACc,YAAY,EAACR,CAAC;IAAC,GAAGJ;EAAC,CAAC,CAAC;EAAC,MAAMK,CAAC,GAACD,CAAC,CAACS,eAAe;EAAC,OAAOT,CAAC,CAACU,MAAM,CAAC,CAAC,EAACT,CAAC;AAAA;AAAC,SAASU,CAACA,CAACxB,CAAC,EAACO,CAAC,EAACE,CAAC,EAAC;EAAC,MAAMI,CAAC,GAACK,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAACZ,CAAC,CAACkB,WAAW,CAACZ,CAAC,CAAC,EAACd,CAAC,CAACC,CAAC,EAACO,CAAC,EAAC;IAACc,YAAY,EAACR,CAAC;IAAC,GAAGJ;EAAC,CAAC,CAAC;EAAC,MAAMK,CAAC,GAACD,CAAC,CAACS,eAAe;EAAC,OAAOT,CAAC,CAACU,MAAM,CAAC,CAAC,EAACT,CAAC;AAAA;AAAC,SAAOd,CAAC,IAAI0B,sBAAsB,EAACZ,CAAC,IAAIa,WAAW,EAACH,CAAC,IAAII,WAAW,EAACX,CAAC,IAAII,YAAY,EAACd,CAAC,IAAIsB,gBAAgB,EAACpB,CAAC,IAAIqB,SAAS,EAACjB,CAAC,IAAIkB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}