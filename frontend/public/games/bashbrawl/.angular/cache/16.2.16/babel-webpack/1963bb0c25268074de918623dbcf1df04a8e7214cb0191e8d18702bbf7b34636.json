{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst V = \"cursor-hand-click\",\n  r = [\"cursor-hand-click\", C({\n    outline: '<path d=\"M30.5379 17.9798C27.0518 15.7798 23.0634 14.4598 18.9444 14.1398V13.1898C20.7929 12.1298 21.9583 10.1598 21.9583 8.00977C21.9583 4.69977 19.2558 2.00977 15.9304 2.00977C12.6051 2.00977 9.90263 4.69977 9.90263 8.00977C9.90263 10.1698 11.058 12.1198 12.9165 13.1898V17.0598L10.0835 14.9498C8.90804 13.8398 7.03941 13.8698 5.88408 15.0098C5.31144 15.5798 5 16.3298 5 17.1298C5 17.9298 5.31144 18.6898 5.88408 19.2498L10.9977 24.3398C11.3493 26.9998 12.7658 29.3798 14.9258 30.9998V33.0098C14.9258 33.5598 15.3779 34.0098 15.9304 34.0098H25.9768C26.5294 34.0098 26.9815 33.5598 26.9815 33.0098V31.0098C29.5031 29.1198 31 26.1698 31 23.0098V18.8298C31 18.4898 30.8292 18.1698 30.5379 17.9898V17.9798ZM11.9119 7.99977C11.9119 5.78977 13.7102 3.99977 15.9304 3.99977C18.1507 3.99977 19.949 5.78977 19.949 7.99977C19.949 8.98977 19.5773 9.92976 18.9444 10.6498V8.99977C18.9444 7.34977 17.5881 5.99977 15.9304 5.99977C14.2728 5.99977 12.9165 7.34977 12.9165 8.99977V10.6398C12.2836 9.91977 11.9119 8.99977 11.9119 7.99977ZM28.9907 22.9998C28.9907 25.6698 27.6546 28.1598 25.4142 29.6498C25.1329 29.8398 24.9722 30.1498 24.9722 30.4798V31.9998H16.9351V30.4698C16.9351 30.1398 16.7643 29.8298 16.493 29.6398C14.4737 28.2898 13.1878 26.1498 12.9567 23.7698C12.9366 23.5398 12.8362 23.3198 12.6654 23.1598L7.30062 17.8198C7.10974 17.6298 7.00927 17.3798 7.00927 17.1098C7.00927 16.8398 7.10974 16.5898 7.30062 16.3998C7.69243 16.0098 8.32535 16.0098 8.71716 16.3998C8.75734 16.4398 8.79753 16.4698 8.83771 16.4998L12.9065 19.4898V20.9898L14.9158 19.6598V8.99977C14.9158 8.44977 15.3679 7.99977 15.9204 7.99977C16.473 7.99977 16.925 8.44977 16.925 8.99977V19.2498L18.9343 19.7498V16.1498C19.6074 16.2098 20.2805 16.2898 20.9436 16.3998V20.2498L22.9529 20.7498V16.8498C23.636 17.0298 24.2991 17.2298 24.9621 17.4698V21.2498L26.9714 21.7498V18.3098C27.6546 18.6298 28.3277 18.9798 28.9807 19.3698V22.9898L28.9907 22.9998Z\"/>'\n  })];\nexport { r as cursorHandClickIcon, V as cursorHandClickIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "V", "r", "outline", "cursorHandClickIcon", "cursorHandClickIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/cursor-hand-click.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const V=\"cursor-hand-click\",r=[\"cursor-hand-click\",C({outline:'<path d=\"M30.5379 17.9798C27.0518 15.7798 23.0634 14.4598 18.9444 14.1398V13.1898C20.7929 12.1298 21.9583 10.1598 21.9583 8.00977C21.9583 4.69977 19.2558 2.00977 15.9304 2.00977C12.6051 2.00977 9.90263 4.69977 9.90263 8.00977C9.90263 10.1698 11.058 12.1198 12.9165 13.1898V17.0598L10.0835 14.9498C8.90804 13.8398 7.03941 13.8698 5.88408 15.0098C5.31144 15.5798 5 16.3298 5 17.1298C5 17.9298 5.31144 18.6898 5.88408 19.2498L10.9977 24.3398C11.3493 26.9998 12.7658 29.3798 14.9258 30.9998V33.0098C14.9258 33.5598 15.3779 34.0098 15.9304 34.0098H25.9768C26.5294 34.0098 26.9815 33.5598 26.9815 33.0098V31.0098C29.5031 29.1198 31 26.1698 31 23.0098V18.8298C31 18.4898 30.8292 18.1698 30.5379 17.9898V17.9798ZM11.9119 7.99977C11.9119 5.78977 13.7102 3.99977 15.9304 3.99977C18.1507 3.99977 19.949 5.78977 19.949 7.99977C19.949 8.98977 19.5773 9.92976 18.9444 10.6498V8.99977C18.9444 7.34977 17.5881 5.99977 15.9304 5.99977C14.2728 5.99977 12.9165 7.34977 12.9165 8.99977V10.6398C12.2836 9.91977 11.9119 8.99977 11.9119 7.99977ZM28.9907 22.9998C28.9907 25.6698 27.6546 28.1598 25.4142 29.6498C25.1329 29.8398 24.9722 30.1498 24.9722 30.4798V31.9998H16.9351V30.4698C16.9351 30.1398 16.7643 29.8298 16.493 29.6398C14.4737 28.2898 13.1878 26.1498 12.9567 23.7698C12.9366 23.5398 12.8362 23.3198 12.6654 23.1598L7.30062 17.8198C7.10974 17.6298 7.00927 17.3798 7.00927 17.1098C7.00927 16.8398 7.10974 16.5898 7.30062 16.3998C7.69243 16.0098 8.32535 16.0098 8.71716 16.3998C8.75734 16.4398 8.79753 16.4698 8.83771 16.4998L12.9065 19.4898V20.9898L14.9158 19.6598V8.99977C14.9158 8.44977 15.3679 7.99977 15.9204 7.99977C16.473 7.99977 16.925 8.44977 16.925 8.99977V19.2498L18.9343 19.7498V16.1498C19.6074 16.2098 20.2805 16.2898 20.9436 16.3998V20.2498L22.9529 20.7498V16.8498C23.636 17.0298 24.2991 17.2298 24.9621 17.4698V21.2498L26.9714 21.7498V18.3098C27.6546 18.6298 28.3277 18.9798 28.9807 19.3698V22.9898L28.9907 22.9998Z\"/>'})];export{r as cursorHandClickIcon,V as cursorHandClickIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,mBAAmB;EAACC,CAAC,GAAC,CAAC,mBAAmB,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAA+3D,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,mBAAmB,EAACH,CAAC,IAAII,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}