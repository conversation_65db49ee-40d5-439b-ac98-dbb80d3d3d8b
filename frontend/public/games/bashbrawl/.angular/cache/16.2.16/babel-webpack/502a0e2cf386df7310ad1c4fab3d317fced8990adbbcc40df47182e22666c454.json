{"ast": null, "code": "import { Directive as e, directive as r } from \"lit/directive.js\";\nclass t extends e {\n  render() {\n    return \"\";\n  }\n  update(e, r) {\n    return Object.entries(r[0]).filter(([r, t]) => t !== e.element[r]).forEach(([r, t]) => e.element[r] = t), this.render();\n  }\n}\nconst n = r(t);\nexport { t as SpreadProps, n as spreadProps };", "map": {"version": 3, "names": ["Directive", "e", "directive", "r", "t", "render", "update", "Object", "entries", "filter", "element", "for<PERSON>ach", "n", "SpreadProps", "spreadProps"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/directives/spread-props.js"], "sourcesContent": ["import{Directive as e,directive as r}from\"lit/directive.js\";class t extends e{render(){return\"\"}update(e,r){return Object.entries(r[0]).filter((([r,t])=>t!==e.element[r])).forEach((([r,t])=>e.element[r]=t)),this.render()}}const n=r(t);export{t as SpreadProps,n as spreadProps};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,QAAK,kBAAkB;AAAC,MAAMC,CAAC,SAASH,CAAC;EAACI,MAAMA,CAAA,EAAE;IAAC,OAAM,EAAE;EAAA;EAACC,MAAMA,CAACL,CAAC,EAACE,CAAC,EAAC;IAAC,OAAOI,MAAM,CAACC,OAAO,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAACM,MAAM,CAAE,CAAC,CAACN,CAAC,EAACC,CAAC,CAAC,KAAGA,CAAC,KAAGH,CAAC,CAACS,OAAO,CAACP,CAAC,CAAE,CAAC,CAACQ,OAAO,CAAE,CAAC,CAACR,CAAC,EAACC,CAAC,CAAC,KAAGH,CAAC,CAACS,OAAO,CAACP,CAAC,CAAC,GAA<PERSON>,CAAE,CAAC,EAAC,IAAI,CAACC,MAAM,CAAC,CAAC;EAAA;AAAC;AAAC,<PERSON><PERSON><PERSON>,<PERSON>AC,GAACT,CAAC,CAACC,CAAC,CAAC;AAAC,SAAOA,CAAC,IAAIS,WAAW,EAACD,CAAC,IAAIE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}