{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"wand\",\n  n = [\"wand\", C({\n    outline: '<path d=\"M4.26254 15.7174C4.46275 15.9176 4.71303 16.0076 4.97331 16.0076C5.2336 16.0076 5.48387 15.9076 5.68409 15.7174L6.9755 14.4267L8.26692 15.7174C8.46713 15.9176 8.71741 16.0076 8.97769 16.0076C9.23798 16.0076 9.48825 15.9076 9.68847 15.7174C10.0789 15.3272 10.0789 14.6969 9.68847 14.3067L8.39706 13.016L9.68847 11.7253C10.0789 11.3351 10.0789 10.7047 9.68847 10.3145C9.29804 9.92428 8.66735 9.92428 8.27693 10.3145L6.98551 11.6052L5.6941 10.3145C5.30367 9.92428 4.67298 9.92428 4.28256 10.3145C3.89213 10.7047 3.89213 11.3351 4.28256 11.7253L5.57397 13.016L4.28256 14.3067C3.89213 14.6969 3.89213 15.3272 4.28256 15.7174H4.26254ZM33.7047 6.29229L29.7304 2.32012C29.34 1.92991 28.7093 1.92991 28.3188 2.32012L21.4714 9.16386C21.4013 9.20389 21.3312 9.2339 21.2812 9.29394C21.2311 9.35397 21.1911 9.42401 21.151 9.48404L2.29038 28.3444C1.89995 28.7346 1.89995 29.3649 2.29038 29.7551L6.26473 33.7273C6.46494 33.9274 6.71522 34.0175 6.9755 34.0175C7.23579 34.0175 7.48606 33.9174 7.68628 33.7273L33.7047 7.71307C34.0952 7.32286 34.0952 6.69251 33.7047 6.3023V6.29229ZM6.9755 31.6061L4.42271 29.0547L22.022 11.4551L24.5748 14.0065L6.9755 31.6061ZM25.9963 12.5957L23.4435 10.0443L29.0396 4.45129L31.5924 7.00268L25.9963 12.5957ZM14.2735 7.71307C14.4737 7.91318 14.724 8.00323 14.9843 8.00323C15.2445 8.00323 15.4948 7.90317 15.695 7.71307L16.9865 6.42237L18.2779 7.71307C18.4781 7.91318 18.7284 8.00323 18.9886 8.00323C19.2489 8.00323 19.4992 7.90317 19.6994 7.71307C20.0898 7.32286 20.0898 6.69251 19.6994 6.3023L18.408 5.01159L19.6994 3.72089C20.0898 3.33067 20.0898 2.70033 19.6994 2.31012C19.309 1.9199 18.6783 1.9199 18.2879 2.31012L16.9965 3.60082L15.7051 2.31012C15.3146 1.9199 14.6839 1.9199 14.2935 2.31012C13.9031 2.70033 13.9031 3.33067 14.2935 3.72089L15.5849 5.01159L14.2935 6.3023C13.9031 6.69251 13.9031 7.32286 14.2935 7.71307H14.2735ZM31.7025 22.3111C31.3121 21.9208 30.6814 21.9208 30.291 22.3111L27.9985 24.6023L25.706 22.3111C25.3156 21.9208 24.6849 21.9208 24.2944 22.3111C23.904 22.7013 23.904 23.3316 24.2944 23.7218L26.587 26.0131L24.2944 28.3043C23.904 28.6945 23.904 29.3249 24.2944 29.7151C24.4947 29.9152 24.7449 30.0053 25.0052 30.0053C25.2655 30.0053 25.5158 29.9052 25.716 29.7151L28.0085 27.4238L30.301 29.7151C30.5012 29.9152 30.7515 30.0053 31.0118 30.0053C31.2721 30.0053 31.5224 29.9052 31.7226 29.7151C32.113 29.3249 32.113 28.6945 31.7226 28.3043L29.4301 26.0131L31.7226 23.7218C32.113 23.3316 32.113 22.7013 31.7226 22.3111H31.7025Z\"/>'\n  })];\nexport { n as wandIcon, L as wandIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "n", "outline", "wandIcon", "wandIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/wand.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"wand\",n=[\"wand\",C({outline:'<path d=\"M4.26254 15.7174C4.46275 15.9176 4.71303 16.0076 4.97331 16.0076C5.2336 16.0076 5.48387 15.9076 5.68409 15.7174L6.9755 14.4267L8.26692 15.7174C8.46713 15.9176 8.71741 16.0076 8.97769 16.0076C9.23798 16.0076 9.48825 15.9076 9.68847 15.7174C10.0789 15.3272 10.0789 14.6969 9.68847 14.3067L8.39706 13.016L9.68847 11.7253C10.0789 11.3351 10.0789 10.7047 9.68847 10.3145C9.29804 9.92428 8.66735 9.92428 8.27693 10.3145L6.98551 11.6052L5.6941 10.3145C5.30367 9.92428 4.67298 9.92428 4.28256 10.3145C3.89213 10.7047 3.89213 11.3351 4.28256 11.7253L5.57397 13.016L4.28256 14.3067C3.89213 14.6969 3.89213 15.3272 4.28256 15.7174H4.26254ZM33.7047 6.29229L29.7304 2.32012C29.34 1.92991 28.7093 1.92991 28.3188 2.32012L21.4714 9.16386C21.4013 9.20389 21.3312 9.2339 21.2812 9.29394C21.2311 9.35397 21.1911 9.42401 21.151 9.48404L2.29038 28.3444C1.89995 28.7346 1.89995 29.3649 2.29038 29.7551L6.26473 33.7273C6.46494 33.9274 6.71522 34.0175 6.9755 34.0175C7.23579 34.0175 7.48606 33.9174 7.68628 33.7273L33.7047 7.71307C34.0952 7.32286 34.0952 6.69251 33.7047 6.3023V6.29229ZM6.9755 31.6061L4.42271 29.0547L22.022 11.4551L24.5748 14.0065L6.9755 31.6061ZM25.9963 12.5957L23.4435 10.0443L29.0396 4.45129L31.5924 7.00268L25.9963 12.5957ZM14.2735 7.71307C14.4737 7.91318 14.724 8.00323 14.9843 8.00323C15.2445 8.00323 15.4948 7.90317 15.695 7.71307L16.9865 6.42237L18.2779 7.71307C18.4781 7.91318 18.7284 8.00323 18.9886 8.00323C19.2489 8.00323 19.4992 7.90317 19.6994 7.71307C20.0898 7.32286 20.0898 6.69251 19.6994 6.3023L18.408 5.01159L19.6994 3.72089C20.0898 3.33067 20.0898 2.70033 19.6994 2.31012C19.309 1.9199 18.6783 1.9199 18.2879 2.31012L16.9965 3.60082L15.7051 2.31012C15.3146 1.9199 14.6839 1.9199 14.2935 2.31012C13.9031 2.70033 13.9031 3.33067 14.2935 3.72089L15.5849 5.01159L14.2935 6.3023C13.9031 6.69251 13.9031 7.32286 14.2935 7.71307H14.2735ZM31.7025 22.3111C31.3121 21.9208 30.6814 21.9208 30.291 22.3111L27.9985 24.6023L25.706 22.3111C25.3156 21.9208 24.6849 21.9208 24.2944 22.3111C23.904 22.7013 23.904 23.3316 24.2944 23.7218L26.587 26.0131L24.2944 28.3043C23.904 28.6945 23.904 29.3249 24.2944 29.7151C24.4947 29.9152 24.7449 30.0053 25.0052 30.0053C25.2655 30.0053 25.5158 29.9052 25.716 29.7151L28.0085 27.4238L30.301 29.7151C30.5012 29.9152 30.7515 30.0053 31.0118 30.0053C31.2721 30.0053 31.5224 29.9052 31.7226 29.7151C32.113 29.3249 32.113 28.6945 31.7226 28.3043L29.4301 26.0131L31.7226 23.7218C32.113 23.3316 32.113 22.7013 31.7226 22.3111H31.7025Z\"/>'})];export{n as wandIcon,L as wandIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,MAAM;EAACC,CAAC,GAAC,CAAC,MAAM,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAA+6E,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,QAAQ,EAACH,CAAC,IAAII,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}