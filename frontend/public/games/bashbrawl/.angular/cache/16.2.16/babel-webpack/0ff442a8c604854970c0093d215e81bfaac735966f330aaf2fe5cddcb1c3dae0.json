{"ast": null, "code": "import { renderIcon as L } from \"../icon.renderer.js\";\nconst C = \"pinboard\",\n  H = [\"pinboard\", L({\n    outline: '<path d=\"M32.6495 10.3517L25.6307 3.33078C25.2329 2.98999 24.6398 3.0129 24.2695 3.38337C23.8991 3.75385 23.8762 4.34705 24.2169 4.745L31.2357 11.766C31.4808 12.0522 31.8655 12.1769 32.2318 12.0887C32.5981 12.0006 32.8841 11.7145 32.9722 11.3481C33.0603 10.9817 32.9356 10.5969 32.6495 10.3517Z\"/><path d=\"M29.0699 31.0836H5.00538V7.01177H21.0484V5.00578H5.00538C3.89784 5.00578 3 5.90389 3 7.01177V31.0836C3 32.1915 3.89784 33.0896 5.00538 33.0896H29.0699C30.1774 33.0896 31.0753 32.1915 31.0753 31.0836V15.0357H29.0699V31.0836Z\"/><path d=\"M21.8505 12.7188L21.1486 12.2173C20.1961 11.5252 18.8826 11.8863 17.8097 12.6285L23.3445 18.1651C24.0063 17.2022 24.5177 15.8281 23.7556 14.8251L23.2643 14.133L26.9442 10.4019L28.368 11.8061L25.8212 14.3838C26.6935 16.3998 25.6808 18.7568 24.1367 20.3315L23.4247 21.0537L19.8652 17.5131L16.1853 21.1941L14.7715 19.7799L18.4514 16.0989L14.9019 12.5483L15.6238 11.8362C17.1981 10.2916 19.5544 9.27853 21.5597 10.1511L24.0564 7.57344L25.5204 9.01775L21.8505 12.7188Z\"/>',\n    solid: '<path d=\"M25.6458 3.33078L32.6693 10.3517H32.6493C32.9356 10.5969 33.0603 10.9817 32.9722 11.3481C32.884 11.7145 32.5978 12.0006 32.2313 12.0887C31.8648 12.1769 31.4797 12.0522 31.2345 11.766L29.8499 10.3818L25.8365 14.3938C26.7094 16.4098 25.696 18.7568 24.1508 20.3315L23.4384 21.0537L19.9066 17.5031L16.2243 21.1841L14.8096 19.7698L18.4919 16.0889L14.94 12.5383L15.6624 11.8261C17.2377 10.2815 19.5956 9.2685 21.6023 10.1411L25.6157 6.12913L24.2311 4.745C23.8902 4.34705 23.9131 3.75385 24.2837 3.38337C24.6543 3.0129 25.2477 2.98999 25.6458 3.33078Z\"/><path d=\"M5.00672 31.0836H29.0873V15.0357H31.094V31.0836C31.094 32.1915 30.1956 33.0896 29.0873 33.0896H5.00672C3.89844 33.0896 3 32.1915 3 31.0836V7.01177C3 5.90389 3.89844 5.00578 5.00672 5.00578H21.0605V7.01177H5.00672V31.0836Z\"/>'\n  })];\nexport { H as pinboardIcon, C as pinboardIconName };", "map": {"version": 3, "names": ["renderIcon", "L", "C", "H", "outline", "solid", "pinboardIcon", "pinboardIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/pinboard.js"], "sourcesContent": ["import{renderIcon as L}from\"../icon.renderer.js\";const C=\"pinboard\",H=[\"pinboard\",L({outline:'<path d=\"M32.6495 10.3517L25.6307 3.33078C25.2329 2.98999 24.6398 3.0129 24.2695 3.38337C23.8991 3.75385 23.8762 4.34705 24.2169 4.745L31.2357 11.766C31.4808 12.0522 31.8655 12.1769 32.2318 12.0887C32.5981 12.0006 32.8841 11.7145 32.9722 11.3481C33.0603 10.9817 32.9356 10.5969 32.6495 10.3517Z\"/><path d=\"M29.0699 31.0836H5.00538V7.01177H21.0484V5.00578H5.00538C3.89784 5.00578 3 5.90389 3 7.01177V31.0836C3 32.1915 3.89784 33.0896 5.00538 33.0896H29.0699C30.1774 33.0896 31.0753 32.1915 31.0753 31.0836V15.0357H29.0699V31.0836Z\"/><path d=\"M21.8505 12.7188L21.1486 12.2173C20.1961 11.5252 18.8826 11.8863 17.8097 12.6285L23.3445 18.1651C24.0063 17.2022 24.5177 15.8281 23.7556 14.8251L23.2643 14.133L26.9442 10.4019L28.368 11.8061L25.8212 14.3838C26.6935 16.3998 25.6808 18.7568 24.1367 20.3315L23.4247 21.0537L19.8652 17.5131L16.1853 21.1941L14.7715 19.7799L18.4514 16.0989L14.9019 12.5483L15.6238 11.8362C17.1981 10.2916 19.5544 9.27853 21.5597 10.1511L24.0564 7.57344L25.5204 9.01775L21.8505 12.7188Z\"/>',solid:'<path d=\"M25.6458 3.33078L32.6693 10.3517H32.6493C32.9356 10.5969 33.0603 10.9817 32.9722 11.3481C32.884 11.7145 32.5978 12.0006 32.2313 12.0887C31.8648 12.1769 31.4797 12.0522 31.2345 11.766L29.8499 10.3818L25.8365 14.3938C26.7094 16.4098 25.696 18.7568 24.1508 20.3315L23.4384 21.0537L19.9066 17.5031L16.2243 21.1841L14.8096 19.7698L18.4919 16.0889L14.94 12.5383L15.6624 11.8261C17.2377 10.2815 19.5956 9.2685 21.6023 10.1411L25.6157 6.12913L24.2311 4.745C23.8902 4.34705 23.9131 3.75385 24.2837 3.38337C24.6543 3.0129 25.2477 2.98999 25.6458 3.33078Z\"/><path d=\"M5.00672 31.0836H29.0873V15.0357H31.094V31.0836C31.094 32.1915 30.1956 33.0896 29.0873 33.0896H5.00672C3.89844 33.0896 3 32.1915 3 31.0836V7.01177C3 5.90389 3.89844 5.00578 5.00672 5.00578H21.0605V7.01177H5.00672V31.0836Z\"/>'})];export{H as pinboardIcon,C as pinboardIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,UAAU;EAACC,CAAC,GAAC,CAAC,UAAU,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,i/BAAi/B;IAACC,KAAK,EAAC;EAAuxB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,YAAY,EAACJ,CAAC,IAAIK,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}