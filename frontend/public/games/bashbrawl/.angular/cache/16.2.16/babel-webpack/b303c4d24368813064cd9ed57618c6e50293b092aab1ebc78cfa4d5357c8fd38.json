{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"fuel\",\n  V = [\"fuel\", C({\n    outline: '<path d=\"M18.9906 10.9468H7.00313C6.73296 10.9468 6.4828 11.0568 6.29268 11.2367C6.10256 11.4266 6.0025 11.6765 6.0025 11.9464C6.0025 12.2163 6.11257 12.4662 6.29268 12.6562C6.4828 12.8461 6.73296 12.9461 7.00313 12.9461H19.0006C19.2708 12.9461 19.521 12.8361 19.7111 12.6562C19.9012 12.4662 20.0012 12.2163 20.0012 11.9464C20.0012 11.6765 19.8912 11.4266 19.7111 11.2367C19.521 11.0468 19.2708 10.9468 19.0006 10.9468H18.9906ZM33.93 16.6646L30.7179 9.49738C30.3777 8.73767 29.8274 8.08791 29.127 7.63808C28.4866 7.21824 27.7461 6.99833 26.9856 6.95834V5.01908C26.9856 4.74918 26.8755 4.49928 26.6954 4.30935C26.5053 4.11942 26.2552 4.01946 25.985 4.01946C25.7148 4.01946 25.4647 4.12942 25.2745 4.30935C25.0844 4.49928 24.9844 4.74918 24.9844 5.01908V11.0268C24.9844 11.2967 25.0944 11.5466 25.2745 11.7365C25.4647 11.9264 25.7148 12.0264 25.985 12.0264C26.2552 12.0264 26.5053 11.9165 26.6954 11.7365C26.8856 11.5466 26.9856 11.2967 26.9856 11.0268V8.96758C27.3458 8.99757 27.6961 9.09753 28.0063 9.28746C28.3765 9.51737 28.6667 9.84724 28.8568 10.2371L31.9887 17.2444V30.4694C31.9887 30.8692 31.8286 31.2491 31.5485 31.529C31.2683 31.8089 30.8881 31.9688 30.4878 31.9688C30.0876 31.9688 29.7073 31.8089 29.4271 31.529C29.147 31.2491 28.9869 30.8692 28.9869 30.4694V22.9523C28.9869 21.8927 28.5666 20.873 27.8161 20.1233C27.0657 19.3736 26.045 18.9538 24.9844 18.9538H23.9837V4.79916C23.9837 4.42931 23.9237 4.05945 23.7936 3.71958C23.6535 3.37971 23.4534 3.06982 23.1932 2.79993C22.9331 2.54002 22.6229 2.3301 22.2827 2.20015C21.9425 2.06021 21.5722 2.00023 21.212 2.00023H5.00188C4.23139 1.97024 3.49093 2.25014 2.92058 2.77993C2.35022 3.30973 2.03002 4.02946 2 4.79916V31.1791C2 31.559 2.08005 31.9288 2.22014 32.2787C2.37023 32.6286 2.58036 32.9384 2.85053 33.1983C3.1207 33.4583 3.4409 33.6682 3.79112 33.7981C4.14134 33.9381 4.52158 33.998 4.89181 33.9881H21.1019C21.4722 33.9881 21.8524 33.9281 22.1926 33.7881C22.5428 33.6482 22.863 33.4483 23.1232 33.1784C23.3934 32.9185 23.6035 32.5986 23.7536 32.2587C23.9037 31.9088 23.9737 31.539 23.9737 31.1691V20.963H24.9744C25.5047 20.963 26.015 21.1729 26.3852 21.5528C26.7555 21.9326 26.9756 22.4424 26.9756 22.9722V30.4894C26.9756 30.9492 27.0657 31.409 27.2458 31.8389C27.4259 32.2687 27.6861 32.6586 28.0163 32.9784C28.3465 33.3083 28.7367 33.5582 29.157 33.7381C29.5872 33.9081 30.0475 33.998 30.5078 33.998C31.4384 33.998 32.329 33.6282 32.9794 32.9684C33.6398 32.3087 34 31.419 34 30.4894V17.0845C34 16.9445 33.97 16.8046 33.9099 16.6746L33.93 16.6646ZM22.0625 31.1691C22.0425 31.409 21.9325 31.6289 21.7624 31.7889C21.5822 31.9488 21.3521 32.0388 21.1119 32.0388H4.87179C4.64165 32.0388 4.42151 31.9488 4.26141 31.7789C4.10131 31.619 4.01126 31.389 4.01126 31.1591V4.79916C4.01126 4.67921 4.04128 4.56925 4.0813 4.45929C4.13133 4.34934 4.19137 4.25937 4.28143 4.1794C4.37148 4.09943 4.46154 4.03945 4.57161 3.99947C4.68168 3.95948 4.79174 3.93949 4.91182 3.93949H21.122C21.3621 3.93949 21.5922 4.01946 21.7624 4.1794C21.9425 4.33934 22.0525 4.55926 22.0725 4.79916V31.1791L22.0625 31.1691ZM18.9906 6.93835H7.00313C6.73296 6.93835 6.4828 7.04831 6.29268 7.22824C6.10256 7.41817 6.0025 7.66807 6.0025 7.93797C6.0025 8.20787 6.11257 8.45777 6.29268 8.6477C6.4828 8.83763 6.73296 8.93759 7.00313 8.93759H19.0006C19.2708 8.93759 19.521 8.82763 19.7111 8.6477C19.9012 8.45777 20.0012 8.20787 20.0012 7.93797C20.0012 7.66807 19.8912 7.41817 19.7111 7.22824C19.521 7.03831 19.2708 6.93835 19.0006 6.93835H18.9906Z\"/>'\n  })];\nexport { V as fuelIcon, H as fuelIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "V", "outline", "fuelIcon", "fuelIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/fuel.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"fuel\",V=[\"fuel\",C({outline:'<path d=\"M18.9906 10.9468H7.00313C6.73296 10.9468 6.4828 11.0568 6.29268 11.2367C6.10256 11.4266 6.0025 11.6765 6.0025 11.9464C6.0025 12.2163 6.11257 12.4662 6.29268 12.6562C6.4828 12.8461 6.73296 12.9461 7.00313 12.9461H19.0006C19.2708 12.9461 19.521 12.8361 19.7111 12.6562C19.9012 12.4662 20.0012 12.2163 20.0012 11.9464C20.0012 11.6765 19.8912 11.4266 19.7111 11.2367C19.521 11.0468 19.2708 10.9468 19.0006 10.9468H18.9906ZM33.93 16.6646L30.7179 9.49738C30.3777 8.73767 29.8274 8.08791 29.127 7.63808C28.4866 7.21824 27.7461 6.99833 26.9856 6.95834V5.01908C26.9856 4.74918 26.8755 4.49928 26.6954 4.30935C26.5053 4.11942 26.2552 4.01946 25.985 4.01946C25.7148 4.01946 25.4647 4.12942 25.2745 4.30935C25.0844 4.49928 24.9844 4.74918 24.9844 5.01908V11.0268C24.9844 11.2967 25.0944 11.5466 25.2745 11.7365C25.4647 11.9264 25.7148 12.0264 25.985 12.0264C26.2552 12.0264 26.5053 11.9165 26.6954 11.7365C26.8856 11.5466 26.9856 11.2967 26.9856 11.0268V8.96758C27.3458 8.99757 27.6961 9.09753 28.0063 9.28746C28.3765 9.51737 28.6667 9.84724 28.8568 10.2371L31.9887 17.2444V30.4694C31.9887 30.8692 31.8286 31.2491 31.5485 31.529C31.2683 31.8089 30.8881 31.9688 30.4878 31.9688C30.0876 31.9688 29.7073 31.8089 29.4271 31.529C29.147 31.2491 28.9869 30.8692 28.9869 30.4694V22.9523C28.9869 21.8927 28.5666 20.873 27.8161 20.1233C27.0657 19.3736 26.045 18.9538 24.9844 18.9538H23.9837V4.79916C23.9837 4.42931 23.9237 4.05945 23.7936 3.71958C23.6535 3.37971 23.4534 3.06982 23.1932 2.79993C22.9331 2.54002 22.6229 2.3301 22.2827 2.20015C21.9425 2.06021 21.5722 2.00023 21.212 2.00023H5.00188C4.23139 1.97024 3.49093 2.25014 2.92058 2.77993C2.35022 3.30973 2.03002 4.02946 2 4.79916V31.1791C2 31.559 2.08005 31.9288 2.22014 32.2787C2.37023 32.6286 2.58036 32.9384 2.85053 33.1983C3.1207 33.4583 3.4409 33.6682 3.79112 33.7981C4.14134 33.9381 4.52158 33.998 4.89181 33.9881H21.1019C21.4722 33.9881 21.8524 33.9281 22.1926 33.7881C22.5428 33.6482 22.863 33.4483 23.1232 33.1784C23.3934 32.9185 23.6035 32.5986 23.7536 32.2587C23.9037 31.9088 23.9737 31.539 23.9737 31.1691V20.963H24.9744C25.5047 20.963 26.015 21.1729 26.3852 21.5528C26.7555 21.9326 26.9756 22.4424 26.9756 22.9722V30.4894C26.9756 30.9492 27.0657 31.409 27.2458 31.8389C27.4259 32.2687 27.6861 32.6586 28.0163 32.9784C28.3465 33.3083 28.7367 33.5582 29.157 33.7381C29.5872 33.9081 30.0475 33.998 30.5078 33.998C31.4384 33.998 32.329 33.6282 32.9794 32.9684C33.6398 32.3087 34 31.419 34 30.4894V17.0845C34 16.9445 33.97 16.8046 33.9099 16.6746L33.93 16.6646ZM22.0625 31.1691C22.0425 31.409 21.9325 31.6289 21.7624 31.7889C21.5822 31.9488 21.3521 32.0388 21.1119 32.0388H4.87179C4.64165 32.0388 4.42151 31.9488 4.26141 31.7789C4.10131 31.619 4.01126 31.389 4.01126 31.1591V4.79916C4.01126 4.67921 4.04128 4.56925 4.0813 4.45929C4.13133 4.34934 4.19137 4.25937 4.28143 4.1794C4.37148 4.09943 4.46154 4.03945 4.57161 3.99947C4.68168 3.95948 4.79174 3.93949 4.91182 3.93949H21.122C21.3621 3.93949 21.5922 4.01946 21.7624 4.1794C21.9425 4.33934 22.0525 4.55926 22.0725 4.79916V31.1791L22.0625 31.1691ZM18.9906 6.93835H7.00313C6.73296 6.93835 6.4828 7.04831 6.29268 7.22824C6.10256 7.41817 6.0025 7.66807 6.0025 7.93797C6.0025 8.20787 6.11257 8.45777 6.29268 8.6477C6.4828 8.83763 6.73296 8.93759 7.00313 8.93759H19.0006C19.2708 8.93759 19.521 8.82763 19.7111 8.6477C19.9012 8.45777 20.0012 8.20787 20.0012 7.93797C20.0012 7.66807 19.8912 7.41817 19.7111 7.22824C19.521 7.03831 19.2708 6.93835 19.0006 6.93835H18.9906Z\"/>'})];export{V as fuelIcon,H as fuelIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,MAAM;EAACC,CAAC,GAAC,CAAC,MAAM,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAg5G,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,QAAQ,EAACH,CAAC,IAAII,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}