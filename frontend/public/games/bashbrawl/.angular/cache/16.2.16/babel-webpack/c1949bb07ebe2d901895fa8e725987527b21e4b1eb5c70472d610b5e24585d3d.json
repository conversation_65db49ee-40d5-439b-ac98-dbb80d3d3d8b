{"ast": null, "code": "export default function _filter(fn, list) {\n  var idx = 0;\n  var len = list.length;\n  var result = [];\n  while (idx < len) {\n    if (fn(list[idx])) {\n      result[result.length] = list[idx];\n    }\n    idx += 1;\n  }\n  return result;\n}", "map": {"version": 3, "names": ["_filter", "fn", "list", "idx", "len", "length", "result"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_filter.js"], "sourcesContent": ["export default function _filter(fn, list) {\n  var idx = 0;\n  var len = list.length;\n  var result = [];\n\n  while (idx < len) {\n    if (fn(list[idx])) {\n      result[result.length] = list[idx];\n    }\n\n    idx += 1;\n  }\n\n  return result;\n}"], "mappings": "AAAA,eAAe,SAASA,OAAOA,CAACC,EAAE,EAAEC,IAAI,EAAE;EACxC,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIC,GAAG,GAAGF,IAAI,CAACG,MAAM;EACrB,IAAIC,MAAM,GAAG,EAAE;EAEf,OAAOH,<PERSON>G,GAAGC,GAAG,EAAE;IAChB,IAAIH,EAAE,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACjBG,MAAM,CAACA,MAAM,CAACD,MAAM,CAAC,GAAGH,IAAI,CAACC,GAAG,CAAC;IACnC;IAEAA,GAAG,IAAI,CAAC;EACV;EAEA,OAAOG,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}