{"ast": null, "code": "/**\n * Determine if the passed argument is an integer.\n *\n * @private\n * @param {*} n\n * @category Type\n * @return {Boolean}\n */\nexport default Number.isInteger || function _isInteger(n) {\n  return n << 0 === n;\n};", "map": {"version": 3, "names": ["Number", "isInteger", "_isInteger", "n"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_isInteger.js"], "sourcesContent": ["/**\n * Determine if the passed argument is an integer.\n *\n * @private\n * @param {*} n\n * @category Type\n * @return {Boolean}\n */\nexport default Number.isInteger || function _isInteger(n) {\n  return n << 0 === n;\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeA,MAAM,CAACC,SAAS,IAAI,SAASC,UAAUA,CAACC,CAAC,EAAE;EACxD,OAAOA,CAAC,IAAI,CAAC,KAAKA,CAAC;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}