{"ast": null, "code": "import { renderIcon as e } from \"../icon.renderer.js\";\nconst C = \"circle\",\n  r = [\"circle\", e({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4 18C4 10.268 10.268 4 18 4C25.732 4 32 10.268 32 18C32 25.732 25.732 32 18 32C10.268 32 4 25.732 4 18ZM6 18C6 24.6274 11.3726 30 18 30C21.1826 30 24.2348 28.7357 26.4853 26.4853C28.7357 24.2348 30 21.1826 30 18C30 11.3726 24.6274 6 18 6C11.3726 6 6 11.3726 6 18Z\"/>',\n    solid: '<path d=\"M18 4C10.268 4 4 10.268 4 18C4 25.732 10.268 32 18 32C25.732 32 32 25.732 32 18C32 10.268 25.732 4 18 4Z\"/>'\n  })];\nexport { r as circleIcon, C as circleIconName };", "map": {"version": 3, "names": ["renderIcon", "e", "C", "r", "outline", "solid", "circleIcon", "circleIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/circle.js"], "sourcesContent": ["import{renderIcon as e}from\"../icon.renderer.js\";const C=\"circle\",r=[\"circle\",e({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4 18C4 10.268 10.268 4 18 4C25.732 4 32 10.268 32 18C32 25.732 25.732 32 18 32C10.268 32 4 25.732 4 18ZM6 18C6 24.6274 11.3726 30 18 30C21.1826 30 24.2348 28.7357 26.4853 26.4853C28.7357 24.2348 30 21.1826 30 18C30 11.3726 24.6274 6 18 6C11.3726 6 6 11.3726 6 18Z\"/>',solid:'<path d=\"M18 4C10.268 4 4 10.268 4 18C4 25.732 10.268 32 18 32C25.732 32 32 25.732 32 18C32 10.268 25.732 4 18 4Z\"/>'})];export{r as circleIcon,C as circleIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,8TAA8T;IAACC,KAAK,EAAC;EAAsH,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,UAAU,EAACJ,CAAC,IAAIK,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}