{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"cpu\",\n  V = [\"cpu\", C({\n    outline: '<path d=\"M22 22H12V24H23C23.55 24 24 23.55 24 23V12H22V22ZM33 19C33.55 19 34 18.55 34 18C34 17.45 33.55 17 33 17H30V12H33C33.55 12 34 11.55 34 11C34 10.45 33.55 10 33 10H30V8.63C30 7.18 28.82 6 27.37 6H26V3C26 2.45 25.55 2 25 2C24.45 2 24 2.45 24 3V6H19V3C19 2.45 18.55 2 18 2C17.45 2 17 2.45 17 3V6H12V3C12 2.45 11.55 2 11 2C10.45 2 10 2.45 10 3V6H8.63C7.18 6 6 7.18 6 8.63V10H3C2.45 10 2 10.45 2 11C2 11.55 2.45 12 3 12H6V17H3C2.45 17 2 17.45 2 18C2 18.55 2.45 19 3 19H6V24H3C2.45 24 2 24.45 2 25C2 25.55 2.45 26 3 26H6V27.37C6 28.82 7.18 30 8.63 30H10V33C10 33.55 10.45 34 11 34C11.55 34 12 33.55 12 33V30H17V33C17 33.55 17.45 34 18 34C18.55 34 19 33.55 19 33V30H24V33C24 33.55 24.45 34 25 34C25.55 34 26 33.55 26 33V30H27.37C28.82 30 30 28.82 30 27.37V26H33C33.55 26 34 25.55 34 25C34 24.45 33.55 24 33 24H30V19H33ZM28 27.37C28 27.72 27.72 28 27.37 28H8.63C8.28 28 8 27.72 8 27.37V8.63C8 8.28 8.28 8 8.63 8H27.37C27.72 8 28 8.28 28 8.63V27.37Z\"/>',\n    outlineAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M21.9594 6H19V3C19 2.45 18.55 2 18 2C17.45 2 17 2.45 17 3V6H12V3C12 2.45 11.55 2 11 2C10.45 2 10 2.45 10 3V6H8.63C7.18 6 6 7.18 6 8.63V10H3C2.45 10 2 10.45 2 11C2 11.55 2.45 12 3 12H6V17H3C2.45 17 2 17.45 2 18C2 18.55 2.45 19 3 19H6V24H3C2.45 24 2 24.45 2 25C2 25.55 2.45 26 3 26H6V27.37C6 28.82 7.18 30 8.63 30H10V33C10 33.55 10.45 34 11 34C11.55 34 12 33.55 12 33V30H17V33C17 33.55 17.45 34 18 34C18.55 34 19 33.55 19 33V30H24V33C24 33.55 24.45 34 25 34C25.55 34 26 33.55 26 33V30H27.37C28.82 30 30 28.82 30 27.37V26H33C33.55 26 34 25.55 34 25C34 24.45 33.55 24 33 24H30V19H33C33.55 19 34 18.55 34 18C34 17.45 33.55 17 33 17H30V15.0367H28V27.37C28 27.72 27.72 28 27.37 28H8.63C8.28 28 8 27.72 8 27.37V8.63C8 8.28 8.28 8 8.63 8H20.7594L21.9594 6Z\"/><path d=\"M22 15.026V22H12V24H23C23.55 24 24 23.55 24 23V15.0367H22.3395C22.2258 15.039 22.1124 15.0354 22 15.026Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M23 6H19V3C19 2.45 18.55 2 18 2C17.45 2 17 2.45 17 3V6H12V3C12 2.45 11.55 2 11 2C10.45 2 10 2.45 10 3V6H8.63C7.18 6 6 7.18 6 8.63V10H3C2.45 10 2 10.45 2 11C2 11.55 2.45 12 3 12H6V17H3C2.45 17 2 17.45 2 18C2 18.55 2.45 19 3 19H6V24H3C2.45 24 2 24.45 2 25C2 25.55 2.45 26 3 26H6V27.37C6 28.82 7.18 30 8.63 30H10V33C10 33.55 10.45 34 11 34C11.55 34 12 33.55 12 33V30H17V33C17 33.55 17.45 34 18 34C18.55 34 19 33.55 19 33V30H24V33C24 33.55 24.45 34 25 34C25.55 34 26 33.55 26 33V30H27.37C28.82 30 30 28.82 30 27.37V26H33C33.55 26 34 25.55 34 25C34 24.45 33.55 24 33 24H30V19H33C33.55 19 34 18.55 34 18C34 17.45 33.55 17 33 17H30V13C29.305 13 28.6337 12.8987 28 12.7101V27.37C28 27.72 27.72 28 27.37 28H8.63C8.28 28 8 27.72 8 27.37V8.63C8 8.28 8.28 8 8.63 8H23.2899C23.1013 7.36629 23 6.69497 23 6Z\"/><path d=\"M12 24V22H22V12H24V23C24 23.55 23.55 24 23 24H12Z\"/>',\n    solid: '<path d=\"M33 19C33.55 19 34 18.55 34 18C34 17.45 33.55 17 33 17H30V12H33C33.55 12 34 11.55 34 11C34 10.45 33.55 10 33 10H30V8.63C30 7.18 28.82 6 27.37 6H26V3C26 2.45 25.55 2 25 2C24.45 2 24 2.45 24 3V6H19V3C19 2.45 18.55 2 18 2C17.45 2 17 2.45 17 3V6H12V3C12 2.45 11.55 2 11 2C10.45 2 10 2.45 10 3V6H8.63C7.18 6 6 7.18 6 8.63V10H3C2.45 10 2 10.45 2 11C2 11.55 2.45 12 3 12H6V17H3C2.45 17 2 17.45 2 18C2 18.55 2.45 19 3 19H6V24H3C2.45 24 2 24.45 2 25C2 25.55 2.45 26 3 26H6V27.37C6 28.82 7.18 30 8.63 30H10V33C10 33.55 10.45 34 11 34C11.55 34 12 33.55 12 33V30H17V33C17 33.55 17.45 34 18 34C18.55 34 19 33.55 19 33V30H24V33C24 33.55 24.45 34 25 34C25.55 34 26 33.55 26 33V30H27.37C28.82 30 30 28.82 30 27.37V26H33C33.55 26 34 25.55 34 25C34 24.45 33.55 24 33 24H30V19H33ZM24.1 23C24.1 23.61 23.61 24.1 23 24.1H11.9V21.9H21.9V11.9H24.1V23Z\"/>',\n    solidAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M21.9594 6H19V3C19 2.45 18.55 2 18 2C17.45 2 17 2.45 17 3V6H12V3C12 2.45 11.55 2 11 2C10.45 2 10 2.45 10 3V6H8.63C7.18 6 6 7.18 6 8.63V10H3C2.45 10 2 10.45 2 11C2 11.55 2.45 12 3 12H6V17H3C2.45 17 2 17.45 2 18C2 18.55 2.45 19 3 19H6V24H3C2.45 24 2 24.45 2 25C2 25.55 2.45 26 3 26H6V27.37C6 28.82 7.18 30 8.63 30H10V33C10 33.55 10.45 34 11 34C11.55 34 12 33.55 12 33V30H17V33C17 33.55 17.45 34 18 34C18.55 34 19 33.55 19 33V30H24V33C24 33.55 24.45 34 25 34C25.55 34 26 33.55 26 33V30H27.37C28.82 30 30 28.82 30 27.37V26H33C33.55 26 34 25.55 34 25C34 24.45 33.55 24 33 24H30V19H33C33.55 19 34 18.55 34 18C34 17.45 33.55 17 33 17H30V15.0367H24.1V23C24.1 23.61 23.61 24.1 23 24.1H11.9V21.9H21.9V15.0162C20.8827 14.9007 19.948 14.3166 19.4206 13.3893C18.8204 12.3342 18.8703 11.0423 19.5362 10.0387L21.9594 6Z\"/>',\n    solidBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M23 6H19V3C19 2.45 18.55 2 18 2C17.45 2 17 2.45 17 3V6H12V3C12 2.45 11.55 2 11 2C10.45 2 10 2.45 10 3V6H8.63C7.18 6 6 7.18 6 8.63V10H3C2.45 10 2 10.45 2 11C2 11.55 2.45 12 3 12H6V17H3C2.45 17 2 17.45 2 18C2 18.55 2.45 19 3 19H6V24H3C2.45 24 2 24.45 2 25C2 25.55 2.45 26 3 26H6V27.37C6 28.82 7.18 30 8.63 30H10V33C10 33.55 10.45 34 11 34C11.55 34 12 33.55 12 33V30H17V33C17 33.55 17.45 34 18 34C18.55 34 19 33.55 19 33V30H24V33C24 33.55 24.45 34 25 34C25.55 34 26 33.55 26 33V30H27.37C28.82 30 30 28.82 30 27.37V26H33C33.55 26 34 25.55 34 25C34 24.45 33.55 24 33 24H30V19H33C33.55 19 34 18.55 34 18C34 17.45 33.55 17 33 17H30V13C26.134 13 23 9.86599 23 6ZM23 24.1C23.61 24.1 24.1 23.61 24.1 23V11.9H21.9V21.9H11.9V24.1H23Z\"/>'\n  })];\nexport { V as cpuIcon, H as cpuIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "V", "outline", "outlineAlerted", "outlineBadged", "solid", "solidAlerted", "solidBadged", "cpuIcon", "cpuIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/cpu.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"cpu\",V=[\"cpu\",C({outline:'<path d=\"M22 22H12V24H23C23.55 24 24 23.55 24 23V12H22V22ZM33 19C33.55 19 34 18.55 34 18C34 17.45 33.55 17 33 17H30V12H33C33.55 12 34 11.55 34 11C34 10.45 33.55 10 33 10H30V8.63C30 7.18 28.82 6 27.37 6H26V3C26 2.45 25.55 2 25 2C24.45 2 24 2.45 24 3V6H19V3C19 2.45 18.55 2 18 2C17.45 2 17 2.45 17 3V6H12V3C12 2.45 11.55 2 11 2C10.45 2 10 2.45 10 3V6H8.63C7.18 6 6 7.18 6 8.63V10H3C2.45 10 2 10.45 2 11C2 11.55 2.45 12 3 12H6V17H3C2.45 17 2 17.45 2 18C2 18.55 2.45 19 3 19H6V24H3C2.45 24 2 24.45 2 25C2 25.55 2.45 26 3 26H6V27.37C6 28.82 7.18 30 8.63 30H10V33C10 33.55 10.45 34 11 34C11.55 34 12 33.55 12 33V30H17V33C17 33.55 17.45 34 18 34C18.55 34 19 33.55 19 33V30H24V33C24 33.55 24.45 34 25 34C25.55 34 26 33.55 26 33V30H27.37C28.82 30 30 28.82 30 27.37V26H33C33.55 26 34 25.55 34 25C34 24.45 33.55 24 33 24H30V19H33ZM28 27.37C28 27.72 27.72 28 27.37 28H8.63C8.28 28 8 27.72 8 27.37V8.63C8 8.28 8.28 8 8.63 8H27.37C27.72 8 28 8.28 28 8.63V27.37Z\"/>',outlineAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M21.9594 6H19V3C19 2.45 18.55 2 18 2C17.45 2 17 2.45 17 3V6H12V3C12 2.45 11.55 2 11 2C10.45 2 10 2.45 10 3V6H8.63C7.18 6 6 7.18 6 8.63V10H3C2.45 10 2 10.45 2 11C2 11.55 2.45 12 3 12H6V17H3C2.45 17 2 17.45 2 18C2 18.55 2.45 19 3 19H6V24H3C2.45 24 2 24.45 2 25C2 25.55 2.45 26 3 26H6V27.37C6 28.82 7.18 30 8.63 30H10V33C10 33.55 10.45 34 11 34C11.55 34 12 33.55 12 33V30H17V33C17 33.55 17.45 34 18 34C18.55 34 19 33.55 19 33V30H24V33C24 33.55 24.45 34 25 34C25.55 34 26 33.55 26 33V30H27.37C28.82 30 30 28.82 30 27.37V26H33C33.55 26 34 25.55 34 25C34 24.45 33.55 24 33 24H30V19H33C33.55 19 34 18.55 34 18C34 17.45 33.55 17 33 17H30V15.0367H28V27.37C28 27.72 27.72 28 27.37 28H8.63C8.28 28 8 27.72 8 27.37V8.63C8 8.28 8.28 8 8.63 8H20.7594L21.9594 6Z\"/><path d=\"M22 15.026V22H12V24H23C23.55 24 24 23.55 24 23V15.0367H22.3395C22.2258 15.039 22.1124 15.0354 22 15.026Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M23 6H19V3C19 2.45 18.55 2 18 2C17.45 2 17 2.45 17 3V6H12V3C12 2.45 11.55 2 11 2C10.45 2 10 2.45 10 3V6H8.63C7.18 6 6 7.18 6 8.63V10H3C2.45 10 2 10.45 2 11C2 11.55 2.45 12 3 12H6V17H3C2.45 17 2 17.45 2 18C2 18.55 2.45 19 3 19H6V24H3C2.45 24 2 24.45 2 25C2 25.55 2.45 26 3 26H6V27.37C6 28.82 7.18 30 8.63 30H10V33C10 33.55 10.45 34 11 34C11.55 34 12 33.55 12 33V30H17V33C17 33.55 17.45 34 18 34C18.55 34 19 33.55 19 33V30H24V33C24 33.55 24.45 34 25 34C25.55 34 26 33.55 26 33V30H27.37C28.82 30 30 28.82 30 27.37V26H33C33.55 26 34 25.55 34 25C34 24.45 33.55 24 33 24H30V19H33C33.55 19 34 18.55 34 18C34 17.45 33.55 17 33 17H30V13C29.305 13 28.6337 12.8987 28 12.7101V27.37C28 27.72 27.72 28 27.37 28H8.63C8.28 28 8 27.72 8 27.37V8.63C8 8.28 8.28 8 8.63 8H23.2899C23.1013 7.36629 23 6.69497 23 6Z\"/><path d=\"M12 24V22H22V12H24V23C24 23.55 23.55 24 23 24H12Z\"/>',solid:'<path d=\"M33 19C33.55 19 34 18.55 34 18C34 17.45 33.55 17 33 17H30V12H33C33.55 12 34 11.55 34 11C34 10.45 33.55 10 33 10H30V8.63C30 7.18 28.82 6 27.37 6H26V3C26 2.45 25.55 2 25 2C24.45 2 24 2.45 24 3V6H19V3C19 2.45 18.55 2 18 2C17.45 2 17 2.45 17 3V6H12V3C12 2.45 11.55 2 11 2C10.45 2 10 2.45 10 3V6H8.63C7.18 6 6 7.18 6 8.63V10H3C2.45 10 2 10.45 2 11C2 11.55 2.45 12 3 12H6V17H3C2.45 17 2 17.45 2 18C2 18.55 2.45 19 3 19H6V24H3C2.45 24 2 24.45 2 25C2 25.55 2.45 26 3 26H6V27.37C6 28.82 7.18 30 8.63 30H10V33C10 33.55 10.45 34 11 34C11.55 34 12 33.55 12 33V30H17V33C17 33.55 17.45 34 18 34C18.55 34 19 33.55 19 33V30H24V33C24 33.55 24.45 34 25 34C25.55 34 26 33.55 26 33V30H27.37C28.82 30 30 28.82 30 27.37V26H33C33.55 26 34 25.55 34 25C34 24.45 33.55 24 33 24H30V19H33ZM24.1 23C24.1 23.61 23.61 24.1 23 24.1H11.9V21.9H21.9V11.9H24.1V23Z\"/>',solidAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M21.9594 6H19V3C19 2.45 18.55 2 18 2C17.45 2 17 2.45 17 3V6H12V3C12 2.45 11.55 2 11 2C10.45 2 10 2.45 10 3V6H8.63C7.18 6 6 7.18 6 8.63V10H3C2.45 10 2 10.45 2 11C2 11.55 2.45 12 3 12H6V17H3C2.45 17 2 17.45 2 18C2 18.55 2.45 19 3 19H6V24H3C2.45 24 2 24.45 2 25C2 25.55 2.45 26 3 26H6V27.37C6 28.82 7.18 30 8.63 30H10V33C10 33.55 10.45 34 11 34C11.55 34 12 33.55 12 33V30H17V33C17 33.55 17.45 34 18 34C18.55 34 19 33.55 19 33V30H24V33C24 33.55 24.45 34 25 34C25.55 34 26 33.55 26 33V30H27.37C28.82 30 30 28.82 30 27.37V26H33C33.55 26 34 25.55 34 25C34 24.45 33.55 24 33 24H30V19H33C33.55 19 34 18.55 34 18C34 17.45 33.55 17 33 17H30V15.0367H24.1V23C24.1 23.61 23.61 24.1 23 24.1H11.9V21.9H21.9V15.0162C20.8827 14.9007 19.948 14.3166 19.4206 13.3893C18.8204 12.3342 18.8703 11.0423 19.5362 10.0387L21.9594 6Z\"/>',solidBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M23 6H19V3C19 2.45 18.55 2 18 2C17.45 2 17 2.45 17 3V6H12V3C12 2.45 11.55 2 11 2C10.45 2 10 2.45 10 3V6H8.63C7.18 6 6 7.18 6 8.63V10H3C2.45 10 2 10.45 2 11C2 11.55 2.45 12 3 12H6V17H3C2.45 17 2 17.45 2 18C2 18.55 2.45 19 3 19H6V24H3C2.45 24 2 24.45 2 25C2 25.55 2.45 26 3 26H6V27.37C6 28.82 7.18 30 8.63 30H10V33C10 33.55 10.45 34 11 34C11.55 34 12 33.55 12 33V30H17V33C17 33.55 17.45 34 18 34C18.55 34 19 33.55 19 33V30H24V33C24 33.55 24.45 34 25 34C25.55 34 26 33.55 26 33V30H27.37C28.82 30 30 28.82 30 27.37V26H33C33.55 26 34 25.55 34 25C34 24.45 33.55 24 33 24H30V19H33C33.55 19 34 18.55 34 18C34 17.45 33.55 17 33 17H30V13C26.134 13 23 9.86599 23 6ZM23 24.1C23.61 24.1 24.1 23.61 24.1 23V11.9H21.9V21.9H11.9V24.1H23Z\"/>'})];export{V as cpuIcon,H as cpuIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,CAAC,KAAK,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,w7BAAw7B;IAACC,cAAc,EAAC,gtCAAgtC;IAACC,aAAa,EAAC,k+BAAk+B;IAACC,KAAK,EAAC,00BAA00B;IAACC,YAAY,EAAC,qpCAAqpC;IAACC,WAAW,EAAC;EAAq4B,CAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,OAAO,EAACR,CAAC,IAAIS,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}