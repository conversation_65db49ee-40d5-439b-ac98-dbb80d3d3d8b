{"ast": null, "code": "import _curry2 from \"./internal/_curry2.js\";\nimport paths from \"./paths.js\";\n/**\n * Retrieves the value at a given path. The nodes of the path can be arbitrary strings or non-negative integers.\n * For anything else, the value is unspecified. Integer paths are meant to index arrays, strings are meant for objects.\n *\n * @func\n * @memberOf R\n * @since v0.2.0\n * @category Object\n * @typedefn Idx = String | Int | Symbol\n * @sig [Idx] -> {a} -> a | Undefined\n * @sig Idx = String | NonNegativeInt\n * @param {Array} path The path to use.\n * @param {Object} obj The object or array to retrieve the nested property from.\n * @return {*} The data at `path`.\n * @see R.prop, R.nth, R.assocPath, R.dissoc<PERSON>ath\n * @example\n *\n *      R.path(['a', 'b'], {a: {b: 2}}); //=> 2\n *      R.path(['a', 'b'], {c: {b: 2}}); //=> undefined\n *      R.path(['a', 'b', 0], {a: {b: [1, 2, 3]}}); //=> 1\n *      R.path(['a', 'b', -2], {a: {b: [1, 2, 3]}}); //=> 2\n *      R.path([2], {'2': 2}); //=> 2\n *      R.path([-2], {'-2': 'a'}); //=> undefined\n */\n\nvar path = /*#__PURE__*/\n_curry2(function path(pathAr, obj) {\n  return paths([pathAr], obj)[0];\n});\nexport default path;", "map": {"version": 3, "names": ["_curry2", "paths", "path", "pathAr", "obj"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/path.js"], "sourcesContent": ["import _curry2 from \"./internal/_curry2.js\";\nimport paths from \"./paths.js\";\n/**\n * Retrieves the value at a given path. The nodes of the path can be arbitrary strings or non-negative integers.\n * For anything else, the value is unspecified. Integer paths are meant to index arrays, strings are meant for objects.\n *\n * @func\n * @memberOf R\n * @since v0.2.0\n * @category Object\n * @typedefn Idx = String | Int | Symbol\n * @sig [Idx] -> {a} -> a | Undefined\n * @sig Idx = String | NonNegativeInt\n * @param {Array} path The path to use.\n * @param {Object} obj The object or array to retrieve the nested property from.\n * @return {*} The data at `path`.\n * @see R.prop, R.nth, R.assocPath, R.dissoc<PERSON>ath\n * @example\n *\n *      R.path(['a', 'b'], {a: {b: 2}}); //=> 2\n *      R.path(['a', 'b'], {c: {b: 2}}); //=> undefined\n *      R.path(['a', 'b', 0], {a: {b: [1, 2, 3]}}); //=> 1\n *      R.path(['a', 'b', -2], {a: {b: [1, 2, 3]}}); //=> 2\n *      R.path([2], {'2': 2}); //=> 2\n *      R.path([-2], {'-2': 'a'}); //=> undefined\n */\n\nvar path =\n/*#__PURE__*/\n_curry2(function path(pathAr, obj) {\n  return paths([pathAr], obj)[0];\n});\n\nexport default path;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,KAAK,MAAM,YAAY;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,IAAI,GACR;AACAF,OAAO,CAAC,SAASE,IAAIA,CAACC,MAAM,EAAEC,GAAG,EAAE;EACjC,OAAOH,KAAK,CAAC,CAACE,MAAM,CAAC,EAAEC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC,CAAC,CAAC;AAEF,eAAeF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}