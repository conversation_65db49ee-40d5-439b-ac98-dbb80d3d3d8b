{"ast": null, "code": "import { shakeKeyframes as e } from \"./keyframes/shake.js\";\nconst o = \"cds-component-shake\",\n  t = [{\n    target: \".private-host\",\n    animation: e,\n    options: {\n      duration: 1200,\n      easing: \"ease-in-out\",\n      endDelay: 50\n    }\n  }];\nexport { t as AnimationShakeConfig, o as AnimationShakeName };", "map": {"version": 3, "names": ["shakeKeyframes", "e", "o", "t", "target", "animation", "options", "duration", "easing", "endDelay", "AnimationShakeConfig", "AnimationShakeName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/motion/animations/cds-component-shake.js"], "sourcesContent": ["import{shakeKeyframes as e}from\"./keyframes/shake.js\";const o=\"cds-component-shake\",t=[{target:\".private-host\",animation:e,options:{duration:1200,easing:\"ease-in-out\",endDelay:50}}];export{t as AnimationShakeConfig,o as AnimationShakeName};\n"], "mappings": "AAAA,SAAOA,cAAc,IAAIC,CAAC,QAAK,sBAAsB;AAAC,MAAMC,CAAC,GAAC,qBAAqB;EAACC,CAAC,GAAC,CAAC;IAACC,MAAM,EAAC,eAAe;IAACC,SAAS,EAACJ,CAAC;IAACK,OAAO,EAAC;MAACC,QAAQ,EAAC,IAAI;MAACC,MAAM,EAAC,aAAa;MAACC,QAAQ,EAAC;IAAE;EAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,oBAAoB,EAACR,CAAC,IAAIS,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}