{"ast": null, "code": "import { LogService as t } from \"../services/log.service.js\";\nimport { isString as e } from \"./identity.js\";\nfunction n(t, ...e) {\n  return e.filter(e => t.classList.contains(e)).length === e.length;\n}\nfunction r(t, ...e) {\n  return e.forEach(e => {\n    t.classList.add(e);\n  }), t;\n}\nfunction o(t, ...e) {\n  return e.forEach(e => {\n    t.classList.remove(e);\n  }), t;\n}\nfunction i(t, e, n) {\n  return o(t, ...e.filter(t => n.indexOf(t) < 0));\n}\nfunction u(t, ...e) {\n  return e.forEach(([e, n]) => {\n    t.style[e] = n;\n  }), t;\n}\nfunction a(t, ...e) {\n  return e.forEach(e => {\n    t.style[e] = \"\";\n  }), t;\n}\nfunction c(t) {\n  return `calc((${t} / var(--cds-global-base)) * 1rem)`;\n}\nfunction s(e, n = document.body, r = null) {\n  try {\n    return getComputedStyle(n, r).getPropertyValue(e).trim();\n  } catch (e) {\n    return t.warn(\"Container element passed to getCustomPropertyValue must be an element.\"), \"\";\n  }\n}\nfunction l(e, n, r = document.body) {\n  try {\n    \"\" === n || null === n || !1 === n ? r.style.removeProperty(e) : r.style.setProperty(e, n);\n  } catch (e) {\n    t.warn(\"Container element passed to getCustomPropertyValue must be an element.\");\n  }\n}\nfunction d(t) {\n  return !!t && e(t) && \"--\" === t.slice(0, 2);\n}\nfunction y() {\n  return window.ShadowRoot && (void 0 === window.ShadyCSS || window.ShadyCSS.nativeShadow) && \"adoptedStyleSheets\" in Document.prototype && \"replace\" in CSSStyleSheet.prototype;\n}\nexport { r as addClassnames, s as getCssPropertyValue, n as hasClassnames, d as isCssPropertyName, c as pxToRem, o as removeClassnames, i as removeClassnamesUnless, l as setCssPropertyValue, y as supportsAdoptingStyleSheets, a as unsetElementStyles, u as updateElementStyles };", "map": {"version": 3, "names": ["LogService", "t", "isString", "e", "n", "filter", "classList", "contains", "length", "r", "for<PERSON>ach", "add", "o", "remove", "i", "indexOf", "u", "style", "a", "c", "s", "document", "body", "getComputedStyle", "getPropertyValue", "trim", "warn", "l", "removeProperty", "setProperty", "d", "slice", "y", "window", "ShadowRoot", "ShadyCSS", "nativeShadow", "Document", "prototype", "CSSStyleSheet", "addClassnames", "getCssPropertyValue", "hasClassnames", "isCssPropertyName", "pxToRem", "removeClassnames", "removeClassnamesUnless", "setCssPropertyValue", "supportsAdoptingStyleSheets", "unsetElementStyles", "updateElementStyles"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/css.js"], "sourcesContent": ["import{LogService as t}from\"../services/log.service.js\";import{isString as e}from\"./identity.js\";function n(t,...e){return e.filter((e=>t.classList.contains(e))).length===e.length}function r(t,...e){return e.forEach((e=>{t.classList.add(e)})),t}function o(t,...e){return e.forEach((e=>{t.classList.remove(e)})),t}function i(t,e,n){return o(t,...e.filter((t=>n.indexOf(t)<0)))}function u(t,...e){return e.forEach((([e,n])=>{t.style[e]=n})),t}function a(t,...e){return e.forEach((e=>{t.style[e]=\"\"})),t}function c(t){return`calc((${t} / var(--cds-global-base)) * 1rem)`}function s(e,n=document.body,r=null){try{return getComputedStyle(n,r).getPropertyValue(e).trim()}catch(e){return t.warn(\"Container element passed to getCustomPropertyValue must be an element.\"),\"\"}}function l(e,n,r=document.body){try{\"\"===n||null===n||!1===n?r.style.removeProperty(e):r.style.setProperty(e,n)}catch(e){t.warn(\"Container element passed to getCustomPropertyValue must be an element.\")}}function d(t){return!!t&&e(t)&&\"--\"===t.slice(0,2)}function y(){return window.ShadowRoot&&(void 0===window.ShadyCSS||window.ShadyCSS.nativeShadow)&&\"adoptedStyleSheets\"in Document.prototype&&\"replace\"in CSSStyleSheet.prototype}export{r as addClassnames,s as getCssPropertyValue,n as hasClassnames,d as isCssPropertyName,c as pxToRem,o as removeClassnames,i as removeClassnamesUnless,l as setCssPropertyValue,y as supportsAdoptingStyleSheets,a as unsetElementStyles,u as updateElementStyles};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,eAAe;AAAC,SAASC,CAACA,CAACH,CAAC,EAAC,GAAGE,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACE,MAAM,CAAEF,CAAC,IAAEF,CAAC,CAACK,SAAS,CAACC,QAAQ,CAACJ,CAAC,CAAE,CAAC,CAACK,MAAM,KAAGL,CAAC,CAACK,MAAM;AAAA;AAAC,SAASC,CAACA,CAACR,CAAC,EAAC,GAAGE,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACO,OAAO,CAAEP,CAAC,IAAE;IAACF,CAAC,CAACK,SAAS,CAACK,GAAG,CAACR,CAAC,CAAC;EAAA,CAAE,CAAC,EAACF,CAAC;AAAA;AAAC,SAASW,CAACA,CAACX,CAAC,EAAC,GAAGE,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACO,OAAO,CAAEP,CAAC,IAAE;IAACF,CAAC,CAACK,SAAS,CAACO,MAAM,CAACV,CAAC,CAAC;EAAA,CAAE,CAAC,EAACF,CAAC;AAAA;AAAC,SAASa,CAACA,CAACb,CAAC,EAACE,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOQ,CAAC,CAACX,CAAC,EAAC,GAAGE,CAAC,CAACE,MAAM,CAAEJ,CAAC,IAAEG,CAAC,CAACW,OAAO,CAACd,CAAC,CAAC,GAAC,CAAE,CAAC,CAAC;AAAA;AAAC,SAASe,CAACA,CAACf,CAAC,EAAC,GAAGE,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACO,OAAO,CAAE,CAAC,CAACP,CAAC,EAACC,CAAC,CAAC,KAAG;IAACH,CAAC,CAACgB,KAAK,CAACd,CAAC,CAAC,GAACC,CAAC;EAAA,CAAE,CAAC,EAACH,CAAC;AAAA;AAAC,SAASiB,CAACA,CAACjB,CAAC,EAAC,GAAGE,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACO,OAAO,CAAEP,CAAC,IAAE;IAACF,CAAC,CAACgB,KAAK,CAACd,CAAC,CAAC,GAAC,EAAE;EAAA,CAAE,CAAC,EAACF,CAAC;AAAA;AAAC,SAASkB,CAACA,CAAClB,CAAC,EAAC;EAAC,OAAO,SAAQA,CAAE,oCAAmC;AAAA;AAAC,SAASmB,CAACA,CAACjB,CAAC,EAACC,CAAC,GAACiB,QAAQ,CAACC,IAAI,EAACb,CAAC,GAAC,IAAI,EAAC;EAAC,IAAG;IAAC,OAAOc,gBAAgB,CAACnB,CAAC,EAACK,CAAC,CAAC,CAACe,gBAAgB,CAACrB,CAAC,CAAC,CAACsB,IAAI,CAAC,CAAC;EAAA,CAAC,QAAMtB,CAAC,EAAC;IAAC,OAAOF,CAAC,CAACyB,IAAI,CAAC,wEAAwE,CAAC,EAAC,EAAE;EAAA;AAAC;AAAC,SAASC,CAACA,CAACxB,CAAC,EAACC,CAAC,EAACK,CAAC,GAACY,QAAQ,CAACC,IAAI,EAAC;EAAC,IAAG;IAAC,EAAE,KAAGlB,CAAC,IAAE,IAAI,KAAGA,CAAC,IAAE,CAAC,CAAC,KAAGA,CAAC,GAACK,CAAC,CAACQ,KAAK,CAACW,cAAc,CAACzB,CAAC,CAAC,GAACM,CAAC,CAACQ,KAAK,CAACY,WAAW,CAAC1B,CAAC,EAACC,CAAC,CAAC;EAAA,CAAC,QAAMD,CAAC,EAAC;IAACF,CAAC,CAACyB,IAAI,CAAC,wEAAwE,CAAC;EAAA;AAAC;AAAC,SAASI,CAACA,CAAC7B,CAAC,EAAC;EAAC,OAAM,CAAC,CAACA,CAAC,IAAEE,CAAC,CAACF,CAAC,CAAC,IAAE,IAAI,KAAGA,CAAC,CAAC8B,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC;AAAA;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,OAAOC,MAAM,CAACC,UAAU,KAAG,KAAK,CAAC,KAAGD,MAAM,CAACE,QAAQ,IAAEF,MAAM,CAACE,QAAQ,CAACC,YAAY,CAAC,IAAE,oBAAoB,IAAGC,QAAQ,CAACC,SAAS,IAAE,SAAS,IAAGC,aAAa,CAACD,SAAS;AAAA;AAAC,SAAO7B,CAAC,IAAI+B,aAAa,EAACpB,CAAC,IAAIqB,mBAAmB,EAACrC,CAAC,IAAIsC,aAAa,EAACZ,CAAC,IAAIa,iBAAiB,EAACxB,CAAC,IAAIyB,OAAO,EAAChC,CAAC,IAAIiC,gBAAgB,EAAC/B,CAAC,IAAIgC,sBAAsB,EAACnB,CAAC,IAAIoB,mBAAmB,EAACf,CAAC,IAAIgB,2BAA2B,EAAC9B,CAAC,IAAI+B,kBAAkB,EAACjC,CAAC,IAAIkC,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}