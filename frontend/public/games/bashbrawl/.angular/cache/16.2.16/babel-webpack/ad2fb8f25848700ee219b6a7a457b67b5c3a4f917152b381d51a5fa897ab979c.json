{"ast": null, "code": "let n, e, o, t;\nfunction r(e = !0) {\n  if (!e || !n) {\n    const e = document && document.querySelector(\"[ng-version]\");\n    n = e ? \"\" + e.getAttribute(\"ng-version\") : void 0;\n  }\n  return n;\n}\nfunction i(o = !0) {\n  return o && n || (e = window?.angular?.version?.full), e;\n}\nfunction u(n = !0) {\n  return n && o || (o = window?.CDS?._react?.version ? window.CDS._react.version : document.querySelector(\"[data-reactroot], [data-reactid]\") ? \"unknown version\" : void 0), o;\n}\nfunction c(n = !0) {\n  if (!n || !t) {\n    const n = document.querySelectorAll(\"*\");\n    let e;\n    for (let o = 0; o < n.length; o++) if (n[o].__vue__) {\n      e = n[o];\n      break;\n    }\n    t = e ? \"unknown version\" : void 0;\n  }\n  return t;\n}\nfunction l() {\n  return window?.location?.href?.includes(\"localhost:6006\");\n}\nexport { i as getAngularJSVersion, r as getAngularVersion, u as getReactVersion, c as getVueVersion, l as isStorybook };", "map": {"version": 3, "names": ["n", "e", "o", "t", "r", "document", "querySelector", "getAttribute", "i", "window", "angular", "version", "full", "u", "CDS", "_react", "c", "querySelectorAll", "length", "__vue__", "l", "location", "href", "includes", "getAngularJSVersion", "getAngularVersion", "getReactVersion", "getVueVersion", "isStorybook"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/framework.js"], "sourcesContent": ["let n,e,o,t;function r(e=!0){if(!e||!n){const e=document&&document.querySelector(\"[ng-version]\");n=e?\"\"+e.getAttribute(\"ng-version\"):void 0}return n}function i(o=!0){return o&&n||(e=window?.angular?.version?.full),e}function u(n=!0){return n&&o||(o=window?.CDS?._react?.version?window.CDS._react.version:document.querySelector(\"[data-reactroot], [data-reactid]\")?\"unknown version\":void 0),o}function c(n=!0){if(!n||!t){const n=document.querySelectorAll(\"*\");let e;for(let o=0;o<n.length;o++)if(n[o].__vue__){e=n[o];break}t=e?\"unknown version\":void 0}return t}function l(){return window?.location?.href?.includes(\"localhost:6006\")}export{i as getAngularJSVersion,r as getAngularVersion,u as getReactVersion,c as getVueVersion,l as isStorybook};\n"], "mappings": "AAAA,IAAIA,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC;AAAC,SAASC,CAACA,CAACH,CAAC,GAAC,CAAC,CAAC,EAAC;EAAC,IAAG,CAACA,CAAC,IAAE,CAACD,CAAC,EAAC;IAAC,MAAMC,CAAC,GAACI,QAAQ,IAAEA,QAAQ,CAACC,aAAa,CAAC,cAAc,CAAC;IAACN,CAAC,GAACC,CAAC,GAAC,EAAE,GAACA,CAAC,CAACM,YAAY,CAAC,YAAY,CAAC,GAAC,KAAK,CAAC;EAAA;EAAC,OAAOP,CAAC;AAAA;AAAC,SAASQ,CAACA,CAACN,CAAC,GAAC,CAAC,CAAC,EAAC;EAAC,OAAOA,CAAC,IAAEF,CAAC,KAAGC,CAAC,GAACQ,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,IAAI,CAAC,EAACX,CAAC;AAAA;AAAC,SAASY,CAACA,CAACb,CAAC,GAAC,CAAC,CAAC,EAAC;EAAC,OAAOA,CAAC,IAAEE,CAAC,KAAGA,CAAC,GAACO,MAAM,EAAEK,GAAG,EAAEC,MAAM,EAAEJ,OAAO,GAACF,MAAM,CAACK,GAAG,CAACC,MAAM,CAACJ,OAAO,GAACN,QAAQ,CAACC,aAAa,CAAC,kCAAkC,CAAC,GAAC,iBAAiB,GAAC,KAAK,CAAC,CAAC,EAACJ,CAAC;AAAA;AAAC,SAASc,CAACA,CAAChB,CAAC,GAAC,CAAC,CAAC,EAAC;EAAC,IAAG,CAACA,CAAC,IAAE,CAACG,CAAC,EAAC;IAAC,MAAMH,CAAC,GAACK,QAAQ,CAACY,gBAAgB,CAAC,GAAG,CAAC;IAAC,IAAIhB,CAAC;IAAC,KAAI,IAAIC,CAAC,GAAC,CAAC,EAACA,CAAC,GAACF,CAAC,CAACkB,MAAM,EAAChB,CAAC,EAAE,EAAC,IAAGF,CAAC,CAACE,CAAC,CAAC,CAACiB,OAAO,EAAC;MAAClB,CAAC,GAACD,CAAC,CAACE,CAAC,CAAC;MAAC;IAAK;IAACC,CAAC,GAACF,CAAC,GAAC,iBAAiB,GAAC,KAAK,CAAC;EAAA;EAAC,OAAOE,CAAC;AAAA;AAAC,SAASiB,CAACA,CAAA,EAAE;EAAC,OAAOX,MAAM,EAAEY,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,CAAC,gBAAgB,CAAC;AAAA;AAAC,SAAOf,CAAC,IAAIgB,mBAAmB,EAACpB,CAAC,IAAIqB,iBAAiB,EAACZ,CAAC,IAAIa,eAAe,EAACV,CAAC,IAAIW,aAAa,EAACP,CAAC,IAAIQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}