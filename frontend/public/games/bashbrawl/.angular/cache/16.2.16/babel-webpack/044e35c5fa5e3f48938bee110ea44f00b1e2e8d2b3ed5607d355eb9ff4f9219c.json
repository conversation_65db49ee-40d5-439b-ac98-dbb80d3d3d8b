{"ast": null, "code": "import { renderIcon as h } from \"../icon.renderer.js\";\nconst r = \"pause\",\n  t = [\"pause\", h({\n    outline: '<path d=\"M12.93,32H6.07A2.07,2.07,0,0,1,4,29.93V6.07A2.07,2.07,0,0,1,6.07,4h6.87A2.07,2.07,0,0,1,15,6.07V29.93A2.07,2.07,0,0,1,12.93,32ZM13,6H6V30h7Z\"/><path d=\"M29.93,32H23.07A2.07,2.07,0,0,1,21,29.93V6.07A2.07,2.07,0,0,1,23.07,4h6.87A2.07,2.07,0,0,1,32,6.07V29.93A2.07,2.07,0,0,1,29.93,32ZM30,6H23V30h7Z\"/>',\n    solid: '<rect x=\"3.95\" y=\"4\" width=\"11\" height=\"28\" rx=\"2.07\" ry=\"2.07\"/><rect x=\"20.95\" y=\"4\" width=\"11\" height=\"28\" rx=\"2.07\" ry=\"2.07\"/>'\n  })];\nexport { t as pauseIcon, r as pauseIconName };", "map": {"version": 3, "names": ["renderIcon", "h", "r", "t", "outline", "solid", "pauseIcon", "pauseIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/pause.js"], "sourcesContent": ["import{renderIcon as h}from\"../icon.renderer.js\";const r=\"pause\",t=[\"pause\",h({outline:'<path d=\"M12.93,32H6.07A2.07,2.07,0,0,1,4,29.93V6.07A2.07,2.07,0,0,1,6.07,4h6.87A2.07,2.07,0,0,1,15,6.07V29.93A2.07,2.07,0,0,1,12.93,32ZM13,6H6V30h7Z\"/><path d=\"M29.93,32H23.07A2.07,2.07,0,0,1,21,29.93V6.07A2.07,2.07,0,0,1,23.07,4h6.87A2.07,2.07,0,0,1,32,6.07V29.93A2.07,2.07,0,0,1,29.93,32ZM30,6H23V30h7Z\"/>',solid:'<rect x=\"3.95\" y=\"4\" width=\"11\" height=\"28\" rx=\"2.07\" ry=\"2.07\"/><rect x=\"20.95\" y=\"4\" width=\"11\" height=\"28\" rx=\"2.07\" ry=\"2.07\"/>'})];export{t as pauseIcon,r as pauseIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,OAAO;EAACC,CAAC,GAAC,CAAC,OAAO,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,sTAAsT;IAACC,KAAK,EAAC;EAAqI,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,SAAS,EAACJ,CAAC,IAAIK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}