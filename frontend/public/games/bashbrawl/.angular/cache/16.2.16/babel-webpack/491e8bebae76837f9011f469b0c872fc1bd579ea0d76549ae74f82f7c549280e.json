{"ast": null, "code": "import { renderIcon as a } from \"../icon.renderer.js\";\nconst H = \"align-middle\",\n  V = [\"align-middle\", a({\n    outline: '<path d=\"M34,17H30V11a1,1,0,0,0-1-1H21a1,1,0,0,0-1,1v6H16V5a1,1,0,0,0-1-1H7A1,1,0,0,0,6,5V17H2a1,1,0,0,0,0,2H6V31a1,1,0,0,0,1,1h8a1,1,0,0,0,1-1V19h4v6a1,1,0,0,0,1,1h8a1,1,0,0,0,1-1V19h4a1,1,0,0,0,0-2ZM14,30H8V6h6Zm14-6H22V12h6Z\"/>'\n  })];\nexport { V as alignMiddleIcon, H as alignMiddleIconName };", "map": {"version": 3, "names": ["renderIcon", "a", "H", "V", "outline", "alignMiddleIcon", "alignMiddleIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/align-middle.js"], "sourcesContent": ["import{renderIcon as a}from\"../icon.renderer.js\";const H=\"align-middle\",V=[\"align-middle\",a({outline:'<path d=\"M34,17H30V11a1,1,0,0,0-1-1H21a1,1,0,0,0-1,1v6H16V5a1,1,0,0,0-1-1H7A1,1,0,0,0,6,5V17H2a1,1,0,0,0,0,2H6V31a1,1,0,0,0,1,1h8a1,1,0,0,0,1-1V19h4v6a1,1,0,0,0,1,1h8a1,1,0,0,0,1-1V19h4a1,1,0,0,0,0-2ZM14,30H8V6h6Zm14-6H22V12h6Z\"/>'})];export{V as alignMiddleIcon,H as alignMiddleIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,cAAc;EAACC,CAAC,GAAC,CAAC,cAAc,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAwO,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,eAAe,EAACH,CAAC,IAAII,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}