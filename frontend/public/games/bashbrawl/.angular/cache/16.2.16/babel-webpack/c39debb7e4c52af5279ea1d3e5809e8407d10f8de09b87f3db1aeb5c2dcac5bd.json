{"ast": null, "code": "export const csharpConfig = {\n  name: 'C#',\n  cmds: [['abstract'], ['as'], ['base'], ['bool'], ['break'], ['byte'], ['case'], ['catch'], ['char'], ['checked'], ['class'], ['const'], ['continue'], ['decimal'], ['default'], ['delegate'], ['do'], ['double'], ['else'], ['enum'], ['event'], ['explicit'], ['extern'], ['false'], ['finally'], ['fixed'], ['float'], ['for'], ['foreach'], ['goto'], ['if'], ['implicit'], ['in'], ['int'], ['interface'], ['internal'], ['is'], ['lock'], ['long'], ['namespace'], ['new'], ['null'], ['object'], ['operator'], ['out'], ['override'], ['params'], ['private'], ['protected'], ['public'], ['readonly'], ['ref'], ['return'], ['sbyte'], ['sealed'], ['short'], ['sizeof'], ['stackalloc'], ['static'], ['string'], ['struct'], ['switch'], ['this'], ['throw'], ['true'], ['try'], ['typeof'], ['uint'], ['ulong'], ['unchecked'], ['unsafe'], ['ushort'], ['using'], ['virtual'], ['void'], ['volatile'], ['while'], ['add'], ['allows'], ['alias'], ['and'], ['ascending'], ['args'], ['async'], ['await'], ['by'], ['descending'], ['dynamic'], ['equals'], ['field'], ['file'], ['from'], ['get'], ['global'], ['group'], ['init'], ['into'], ['join'], ['let'], ['managed'], ['nameof'], ['nint'], ['not'], ['notnull'], ['nuint'], ['on'], ['or'], ['orderby'], ['partial'], ['record'], ['remove'], ['required'], ['scoped'], ['select'], ['set'], ['unmanaged'], ['value'], ['var'], ['when'], ['where'], ['with'], ['yield'], ['&'], ['|'], ['+'], ['-'], ['*'], ['/'], ['++'], ['&&'], ['||'], ['^'], ['??'], ['%'], ['?'], [':'], ['=>'], ['<'], ['>'], ['=='], ['+='], ['-='], ['*='], ['/='], ['^='], ['>>='], ['>>>='], ['??='], ['&='], ['|='], ['%='], ['..'], ['<<'], ['>>'], ['>>>'], ['?.'], ['!'], ['::'], ['$'], ['@'], ['\"\"\"'], ['///'], ['#']]\n};", "map": {"version": 3, "names": ["csharpConfig", "name", "cmds"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/src/app/terminals/bashbrawl/languages/csharp.ts"], "sourcesContent": ["/**\n * Taken from https://learn.microsoft.com/en-us/dotnet/csharp/language-reference/keywords/\n * C# reserved keywords, operators and special characters\n */\nimport { LanguageConfig } from './language-config.interface';\nexport const csharpConfig: LanguageConfig = {\n  name: 'C#',\n  cmds: [\n    ['abstract'],\n    ['as'],\n    ['base'],\n    ['bool'],\n    ['break'],\n    ['byte'],\n    ['case'],\n    ['catch'],\n    ['char'],\n    ['checked'],\n    ['class'],\n    ['const'],\n    ['continue'],\n    ['decimal'],\n    ['default'],\n    ['delegate'],\n    ['do'],\n    ['double'],\n    ['else'],\n    ['enum'],\n    ['event'],\n    ['explicit'],\n    ['extern'],\n    ['false'],\n    ['finally'],\n    ['fixed'],\n    ['float'],\n    ['for'],\n    ['foreach'],\n    ['goto'],\n    ['if'],\n    ['implicit'],\n    ['in'],\n    ['int'],\n    ['interface'],\n    ['internal'],\n    ['is'],\n    ['lock'],\n    ['long'],\n    ['namespace'],\n    ['new'],\n    ['null'],\n    ['object'],\n    ['operator'],\n    ['out'],\n    ['override'],\n    ['params'],\n    ['private'],\n    ['protected'],\n    ['public'],\n    ['readonly'],\n    ['ref'],\n    ['return'],\n    ['sbyte'],\n    ['sealed'],\n    ['short'],\n    ['sizeof'],\n    ['stackalloc'],\n    ['static'],\n    ['string'],\n    ['struct'],\n    ['switch'],\n    ['this'],\n    ['throw'],\n    ['true'],\n    ['try'],\n    ['typeof'],\n    ['uint'],\n    ['ulong'],\n    ['unchecked'],\n    ['unsafe'],\n    ['ushort'],\n    ['using'],\n    ['virtual'],\n    ['void'],\n    ['volatile'],\n    ['while'],\n    ['add'],\n    ['allows'],\n    ['alias'],\n    ['and'],\n    ['ascending'],\n    ['args'],\n    ['async'],\n    ['await'],\n    ['by'],\n    ['descending'],\n    ['dynamic'],\n    ['equals'],\n    ['field'],\n    ['file'],\n    ['from'],\n    ['get'],\n    ['global'],\n    ['group'],\n    ['init'],\n    ['into'],\n    ['join'],\n    ['let'],\n    ['managed'],\n    ['nameof'],\n    ['nint'],\n    ['not'],\n    ['notnull'],\n    ['nuint'],\n    ['on'],\n    ['or'],\n    ['orderby'],\n    ['partial'],\n    ['record'],\n    ['remove'],\n    ['required'],\n    ['scoped'],\n    ['select'],\n    ['set'],\n    ['unmanaged'],\n    ['value'],\n    ['var'],\n    ['when'],\n    ['where'],\n    ['with'],\n    ['yield'],\n    ['&'],\n    ['|'],\n    ['+'],\n    ['-'],\n    ['*'],\n    ['/'],\n    ['++'],\n    ['&&'],\n    ['||'],\n    ['^'],\n    ['??'],\n    ['%'],\n    ['?'],\n    [':'],\n    ['=>'],\n    ['<'],\n    ['>'],\n    ['=='],\n    ['+='],\n    ['-='],\n    ['*='],\n    ['/='],\n    ['^='],\n    ['>>='],\n    ['>>>='],\n    ['??='],\n    ['&='],\n    ['|='],\n    ['%='],\n    ['..'],\n    ['<<'],\n    ['>>'],\n    ['>>>'],\n    ['?.'],\n    ['!'],\n    ['::'],\n    ['$'],\n    ['@'],\n    ['\"\"\"'],\n    ['///'],\n    ['#'],\n  ],\n};\n"], "mappings": "AAKA,OAAO,MAAMA,YAAY,GAAmB;EAC1CC,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,CACJ,CAAC,UAAU,CAAC,EACZ,CAAC,IAAI,CAAC,EACN,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,OAAO,CAAC,EACT,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,OAAO,CAAC,EACT,CAAC,MAAM,CAAC,EACR,CAAC,SAAS,CAAC,EACX,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,UAAU,CAAC,EACZ,CAAC,SAAS,CAAC,EACX,CAAC,SAAS,CAAC,EACX,CAAC,UAAU,CAAC,EACZ,CAAC,IAAI,CAAC,EACN,CAAC,QAAQ,CAAC,EACV,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,OAAO,CAAC,EACT,CAAC,UAAU,CAAC,EACZ,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,SAAS,CAAC,EACX,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,KAAK,CAAC,EACP,CAAC,SAAS,CAAC,EACX,CAAC,MAAM,CAAC,EACR,CAAC,IAAI,CAAC,EACN,CAAC,UAAU,CAAC,EACZ,CAAC,IAAI,CAAC,EACN,CAAC,KAAK,CAAC,EACP,CAAC,WAAW,CAAC,EACb,CAAC,UAAU,CAAC,EACZ,CAAC,IAAI,CAAC,EACN,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,WAAW,CAAC,EACb,CAAC,KAAK,CAAC,EACP,CAAC,MAAM,CAAC,EACR,CAAC,QAAQ,CAAC,EACV,CAAC,UAAU,CAAC,EACZ,CAAC,KAAK,CAAC,EACP,CAAC,UAAU,CAAC,EACZ,CAAC,QAAQ,CAAC,EACV,CAAC,SAAS,CAAC,EACX,CAAC,WAAW,CAAC,EACb,CAAC,QAAQ,CAAC,EACV,CAAC,UAAU,CAAC,EACZ,CAAC,KAAK,CAAC,EACP,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,QAAQ,CAAC,EACV,CAAC,YAAY,CAAC,EACd,CAAC,QAAQ,CAAC,EACV,CAAC,QAAQ,CAAC,EACV,CAAC,QAAQ,CAAC,EACV,CAAC,QAAQ,CAAC,EACV,CAAC,MAAM,CAAC,EACR,CAAC,OAAO,CAAC,EACT,CAAC,MAAM,CAAC,EACR,CAAC,KAAK,CAAC,EACP,CAAC,QAAQ,CAAC,EACV,CAAC,MAAM,CAAC,EACR,CAAC,OAAO,CAAC,EACT,CAAC,WAAW,CAAC,EACb,CAAC,QAAQ,CAAC,EACV,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,SAAS,CAAC,EACX,CAAC,MAAM,CAAC,EACR,CAAC,UAAU,CAAC,EACZ,CAAC,OAAO,CAAC,EACT,CAAC,KAAK,CAAC,EACP,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,KAAK,CAAC,EACP,CAAC,WAAW,CAAC,EACb,CAAC,MAAM,CAAC,EACR,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,IAAI,CAAC,EACN,CAAC,YAAY,CAAC,EACd,CAAC,SAAS,CAAC,EACX,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,KAAK,CAAC,EACP,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,KAAK,CAAC,EACP,CAAC,SAAS,CAAC,EACX,CAAC,QAAQ,CAAC,EACV,CAAC,MAAM,CAAC,EACR,CAAC,KAAK,CAAC,EACP,CAAC,SAAS,CAAC,EACX,CAAC,OAAO,CAAC,EACT,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,SAAS,CAAC,EACX,CAAC,SAAS,CAAC,EACX,CAAC,QAAQ,CAAC,EACV,CAAC,QAAQ,CAAC,EACV,CAAC,UAAU,CAAC,EACZ,CAAC,QAAQ,CAAC,EACV,CAAC,QAAQ,CAAC,EACV,CAAC,KAAK,CAAC,EACP,CAAC,WAAW,CAAC,EACb,CAAC,OAAO,CAAC,EACT,CAAC,KAAK,CAAC,EACP,CAAC,MAAM,CAAC,EACR,CAAC,OAAO,CAAC,EACT,CAAC,MAAM,CAAC,EACR,CAAC,OAAO,CAAC,EACT,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC,EACL,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,GAAG,CAAC,EACL,CAAC,IAAI,CAAC,EACN,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC,EACL,CAAC,IAAI,CAAC,EACN,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC,EACL,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,KAAK,CAAC,EACP,CAAC,MAAM,CAAC,EACR,CAAC,KAAK,CAAC,EACP,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,KAAK,CAAC,EACP,CAAC,IAAI,CAAC,EACN,CAAC,GAAG,CAAC,EACL,CAAC,IAAI,CAAC,EACN,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC,EACL,CAAC,KAAK,CAAC,EACP,CAAC,KAAK,CAAC,EACP,CAAC,GAAG,CAAC;CAER", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}