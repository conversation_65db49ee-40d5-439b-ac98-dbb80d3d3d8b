{"ast": null, "code": "import { property as e } from \"./property.js\";\nimport { GlobalStateService as t } from \"../services/global.service.js\";\nimport { I18nService as i } from \"../services/i18n.service.js\";\nimport { mergeObjects as s, isNilOrEmpty as n, objectNaiveDeepEquals as r } from \"../utils/identity.js\";\nimport { LogService as c } from \"../services/log.service.js\";\nfunction o() {\n  return (n, r) => {\n    const c = n.connectedCallback,\n      o = n.disconnectedCallback;\n    n.connectedCallback = function () {\n      n.__i18nSub = t.stateUpdates.subscribe(e => {\n        \"i18nRegistry\" === e.key && this.requestUpdate(r);\n      }), c && c.apply(this);\n    }, n.disconnectedCallback = function () {\n      n.__i18nSub.unsubscribe(), o && o.apply(this);\n    };\n    const d = {\n      get() {\n        const e = s(i.keys[this.__i18nKey], this.__i18n || {});\n        return i.hydrate(e, this);\n      },\n      set(e) {\n        const t = u(e, this),\n          s = a(i.findKey(t) || \"\", this.__i18nKey, t, this.__i18n);\n        void 0 !== s.key && (this.__i18nKey = s.key + \"\"), void 0 !== s.values && (this.__i18n = {\n          ...s.values\n        }), !0 === s.update && this.requestUpdate(), this.requestUpdate(r);\n      },\n      enumerable: !0,\n      configurable: !0\n    };\n    return void 0 !== r ? function (t, i, s) {\n      const n = Object.defineProperty(i, s, t);\n      return e({\n        type: Object,\n        attribute: \"cds-i18n\"\n      })(n, s);\n    }(d, n, r) : function (t, i) {\n      const s = {\n        kind: \"method\",\n        placement: \"prototype\",\n        key: i.key,\n        descriptor: t\n      };\n      return e({\n        type: Object\n      })(s);\n    }(d, n);\n  };\n}\nfunction u(e, t) {\n  if (n(e)) {\n    let e = {};\n    if (t.hasAttribute(\"cds-i18n\")) {\n      const i = t.getAttribute(\"cds-i18n\") + \"\";\n      if (n(i)) e = {};else try {\n        e = JSON.parse(i);\n      } catch {\n        c.warn(\"Clarity i18n: Invalid JSON passed to cds-i18n\"), e = {};\n      }\n    }\n    return e;\n  }\n  return e;\n}\nfunction a(e, t, i, s) {\n  return n(e) ? r(i, s) ? {\n    update: !1\n  } : {\n    update: !0,\n    values: i\n  } : e === t ? {\n    update: !1,\n    values: {}\n  } : {\n    update: !0,\n    key: e,\n    values: {}\n  };\n}\nexport { a as getI18nUpdateStrategy, u as getI18nValues, o as i18n };", "map": {"version": 3, "names": ["property", "e", "GlobalStateService", "t", "I18nService", "i", "mergeObjects", "s", "isNilOrEmpty", "n", "objectNaiveDeepEquals", "r", "LogService", "c", "o", "connectedCallback", "disconnectedCallback", "__i18nSub", "stateUpdates", "subscribe", "key", "requestUpdate", "apply", "unsubscribe", "d", "get", "keys", "__i18n<PERSON>ey", "__i18n", "hydrate", "set", "u", "a", "<PERSON><PERSON><PERSON>", "values", "update", "enumerable", "configurable", "Object", "defineProperty", "type", "attribute", "kind", "placement", "descriptor", "hasAttribute", "getAttribute", "JSON", "parse", "warn", "getI18nUpdateStrategy", "getI18nValues", "i18n"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/decorators/i18n.js"], "sourcesContent": ["import{property as e}from\"./property.js\";import{GlobalStateService as t}from\"../services/global.service.js\";import{I18nService as i}from\"../services/i18n.service.js\";import{mergeObjects as s,isNilOrEmpty as n,objectNaiveDeepEquals as r}from\"../utils/identity.js\";import{LogService as c}from\"../services/log.service.js\";function o(){return(n,r)=>{const c=n.connectedCallback,o=n.disconnectedCallback;n.connectedCallback=function(){n.__i18nSub=t.stateUpdates.subscribe((e=>{\"i18nRegistry\"===e.key&&this.requestUpdate(r)})),c&&c.apply(this)},n.disconnectedCallback=function(){n.__i18nSub.unsubscribe(),o&&o.apply(this)};const d={get(){const e=s(i.keys[this.__i18nKey],this.__i18n||{});return i.hydrate(e,this)},set(e){const t=u(e,this),s=a(i.findKey(t)||\"\",this.__i18nKey,t,this.__i18n);void 0!==s.key&&(this.__i18nKey=s.key+\"\"),void 0!==s.values&&(this.__i18n={...s.values}),!0===s.update&&this.requestUpdate(),this.requestUpdate(r)},enumerable:!0,configurable:!0};return void 0!==r?function(t,i,s){const n=Object.defineProperty(i,s,t);return e({type:Object,attribute:\"cds-i18n\"})(n,s)}(d,n,r):function(t,i){const s={kind:\"method\",placement:\"prototype\",key:i.key,descriptor:t};return e({type:Object})(s)}(d,n)}}function u(e,t){if(n(e)){let e={};if(t.hasAttribute(\"cds-i18n\")){const i=t.getAttribute(\"cds-i18n\")+\"\";if(n(i))e={};else try{e=JSON.parse(i)}catch{c.warn(\"Clarity i18n: Invalid JSON passed to cds-i18n\"),e={}}}return e}return e}function a(e,t,i,s){return n(e)?r(i,s)?{update:!1}:{update:!0,values:i}:e===t?{update:!1,values:{}}:{update:!0,key:e,values:{}}}export{a as getI18nUpdateStrategy,u as getI18nValues,o as i18n};\n"], "mappings": "AAAA,SAAOA,QAAQ,IAAIC,CAAC,QAAK,eAAe;AAAC,SAAOC,kBAAkB,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAAOC,YAAY,IAAIC,CAAC,EAACC,YAAY,IAAIC,CAAC,EAACC,qBAAqB,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,OAAM,CAACL,CAAC,EAACE,CAAC,KAAG;IAAC,MAAME,CAAC,GAACJ,CAAC,CAACM,iBAAiB;MAACD,CAAC,GAACL,CAAC,CAACO,oBAAoB;IAACP,CAAC,CAACM,iBAAiB,GAAC,YAAU;MAACN,CAAC,CAACQ,SAAS,GAACd,CAAC,CAACe,YAAY,CAACC,SAAS,CAAElB,CAAC,IAAE;QAAC,cAAc,KAAGA,CAAC,CAACmB,GAAG,IAAE,IAAI,CAACC,aAAa,CAACV,CAAC,CAAC;MAAA,CAAE,CAAC,EAACE,CAAC,IAAEA,CAAC,CAACS,KAAK,CAAC,IAAI,CAAC;IAAA,CAAC,EAACb,CAAC,CAACO,oBAAoB,GAAC,YAAU;MAACP,CAAC,CAACQ,SAAS,CAACM,WAAW,CAAC,CAAC,EAACT,CAAC,IAAEA,CAAC,CAACQ,KAAK,CAAC,IAAI,CAAC;IAAA,CAAC;IAAC,MAAME,CAAC,GAAC;MAACC,GAAGA,CAAA,EAAE;QAAC,MAAMxB,CAAC,GAACM,CAAC,CAACF,CAAC,CAACqB,IAAI,CAAC,IAAI,CAACC,SAAS,CAAC,EAAC,IAAI,CAACC,MAAM,IAAE,CAAC,CAAC,CAAC;QAAC,OAAOvB,CAAC,CAACwB,OAAO,CAAC5B,CAAC,EAAC,IAAI,CAAC;MAAA,CAAC;MAAC6B,GAAGA,CAAC7B,CAAC,EAAC;QAAC,MAAME,CAAC,GAAC4B,CAAC,CAAC9B,CAAC,EAAC,IAAI,CAAC;UAACM,CAAC,GAACyB,CAAC,CAAC3B,CAAC,CAAC4B,OAAO,CAAC9B,CAAC,CAAC,IAAE,EAAE,EAAC,IAAI,CAACwB,SAAS,EAACxB,CAAC,EAAC,IAAI,CAACyB,MAAM,CAAC;QAAC,KAAK,CAAC,KAAGrB,CAAC,CAACa,GAAG,KAAG,IAAI,CAACO,SAAS,GAACpB,CAAC,CAACa,GAAG,GAAC,EAAE,CAAC,EAAC,KAAK,CAAC,KAAGb,CAAC,CAAC2B,MAAM,KAAG,IAAI,CAACN,MAAM,GAAC;UAAC,GAAGrB,CAAC,CAAC2B;QAAM,CAAC,CAAC,EAAC,CAAC,CAAC,KAAG3B,CAAC,CAAC4B,MAAM,IAAE,IAAI,CAACd,aAAa,CAAC,CAAC,EAAC,IAAI,CAACA,aAAa,CAACV,CAAC,CAAC;MAAA,CAAC;MAACyB,UAAU,EAAC,CAAC,CAAC;MAACC,YAAY,EAAC,CAAC;IAAC,CAAC;IAAC,OAAO,KAAK,CAAC,KAAG1B,CAAC,GAAC,UAASR,CAAC,EAACE,CAAC,EAACE,CAAC,EAAC;MAAC,MAAME,CAAC,GAAC6B,MAAM,CAACC,cAAc,CAAClC,CAAC,EAACE,CAAC,EAACJ,CAAC,CAAC;MAAC,OAAOF,CAAC,CAAC;QAACuC,IAAI,EAACF,MAAM;QAACG,SAAS,EAAC;MAAU,CAAC,CAAC,CAAChC,CAAC,EAACF,CAAC,CAAC;IAAA,CAAC,CAACiB,CAAC,EAACf,CAAC,EAACE,CAAC,CAAC,GAAC,UAASR,CAAC,EAACE,CAAC,EAAC;MAAC,MAAME,CAAC,GAAC;QAACmC,IAAI,EAAC,QAAQ;QAACC,SAAS,EAAC,WAAW;QAACvB,GAAG,EAACf,CAAC,CAACe,GAAG;QAACwB,UAAU,EAACzC;MAAC,CAAC;MAAC,OAAOF,CAAC,CAAC;QAACuC,IAAI,EAACF;MAAM,CAAC,CAAC,CAAC/B,CAAC,CAAC;IAAA,CAAC,CAACiB,CAAC,EAACf,CAAC,CAAC;EAAA,CAAC;AAAA;AAAC,SAASsB,CAACA,CAAC9B,CAAC,EAACE,CAAC,EAAC;EAAC,IAAGM,CAAC,CAACR,CAAC,CAAC,EAAC;IAAC,IAAIA,CAAC,GAAC,CAAC,CAAC;IAAC,IAAGE,CAAC,CAAC0C,YAAY,CAAC,UAAU,CAAC,EAAC;MAAC,MAAMxC,CAAC,GAACF,CAAC,CAAC2C,YAAY,CAAC,UAAU,CAAC,GAAC,EAAE;MAAC,IAAGrC,CAAC,CAACJ,CAAC,CAAC,EAACJ,CAAC,GAAC,CAAC,CAAC,CAAC,KAAK,IAAG;QAACA,CAAC,GAAC8C,IAAI,CAACC,KAAK,CAAC3C,CAAC,CAAC;MAAA,CAAC,OAAK;QAACQ,CAAC,CAACoC,IAAI,CAAC,+CAA+C,CAAC,EAAChD,CAAC,GAAC,CAAC,CAAC;MAAA;IAAC;IAAC,OAAOA,CAAC;EAAA;EAAC,OAAOA,CAAC;AAAA;AAAC,SAAS+B,CAACA,CAAC/B,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAAC;EAAC,OAAOE,CAAC,CAACR,CAAC,CAAC,GAACU,CAAC,CAACN,CAAC,EAACE,CAAC,CAAC,GAAC;IAAC4B,MAAM,EAAC,CAAC;EAAC,CAAC,GAAC;IAACA,MAAM,EAAC,CAAC,CAAC;IAACD,MAAM,EAAC7B;EAAC,CAAC,GAACJ,CAAC,KAAGE,CAAC,GAAC;IAACgC,MAAM,EAAC,CAAC,CAAC;IAACD,MAAM,EAAC,CAAC;EAAC,CAAC,GAAC;IAACC,MAAM,EAAC,CAAC,CAAC;IAACf,GAAG,EAACnB,CAAC;IAACiC,MAAM,EAAC,CAAC;EAAC,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAIkB,qBAAqB,EAACnB,CAAC,IAAIoB,aAAa,EAACrC,CAAC,IAAIsC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}