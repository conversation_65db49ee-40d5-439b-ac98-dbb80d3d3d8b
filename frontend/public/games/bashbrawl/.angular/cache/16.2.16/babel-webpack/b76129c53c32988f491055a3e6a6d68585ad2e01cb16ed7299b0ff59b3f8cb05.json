{"ast": null, "code": "import _curry2 from \"./internal/_curry2.js\";\n/**\n * Adds two values.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Math\n * @sig Number -> Number -> Number\n * @param {Number} a\n * @param {Number} b\n * @return {Number}\n * @see R.subtract\n * @example\n *\n *      R.add(2, 3);       //=>  5\n *      R.add(7)(10);      //=> 17\n */\n\nvar add = /*#__PURE__*/\n_curry2(function add(a, b) {\n  return Number(a) + Number(b);\n});\nexport default add;", "map": {"version": 3, "names": ["_curry2", "add", "a", "b", "Number"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/add.js"], "sourcesContent": ["import _curry2 from \"./internal/_curry2.js\";\n/**\n * Adds two values.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Math\n * @sig Number -> Number -> Number\n * @param {Number} a\n * @param {Number} b\n * @return {Number}\n * @see R.subtract\n * @example\n *\n *      R.add(2, 3);       //=>  5\n *      R.add(7)(10);      //=> 17\n */\n\nvar add =\n/*#__PURE__*/\n_curry2(function add(a, b) {\n  return Number(a) + Number(b);\n});\n\nexport default add;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,uBAAuB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,GAAG,GACP;AACAD,OAAO,CAAC,SAASC,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAOC,MAAM,CAACF,CAAC,CAAC,GAAGE,MAAM,CAACD,CAAC,CAAC;AAC9B,CAAC,CAAC;AAEF,eAAeF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}