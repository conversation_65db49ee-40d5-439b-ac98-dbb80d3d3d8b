{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst o = \"atom\",\n  M = [\"atom\", C({\n    outline: '<path d=\"M18.086 13C15.3167 13 13.0572 15.24 13.0572 18C13.0572 20.76 15.3167 23 18.086 23C20.8553 23 23.1147 20.76 23.1147 18C23.1147 15.24 20.8553 13 18.086 13ZM18.086 21C16.4264 21 15.0667 19.65 15.0667 18C15.0667 16.35 16.4164 15 18.086 15C19.7555 15 21.1052 16.35 21.1052 18C21.1052 19.65 19.7555 21 18.086 21ZM19.9055 6.86C20.6453 6.42 21.3751 6.04 22.0849 5.71C22.3849 5.89 22.7348 6 23.1147 6C24.0145 6 24.7643 5.41 25.0242 4.61C26.0639 4.32 27.0237 4.17 27.8735 4.17C29.2032 4.17 30.2629 4.52 30.9527 5.21C32.5223 6.77 32.3124 10.21 30.2929 14.35C30.7528 14.96 31.1927 15.56 31.6026 16.17C34.3919 10.99 34.8717 6.27 32.3824 3.8C30.6528 2.09 27.8635 1.79 24.6043 2.66C24.2344 2.25 23.7045 2 23.1147 2C22.015 2 21.1152 2.89 21.1052 3.98C20.1154 4.45 19.1057 5 18.086 5.64C12.1774 1.95 6.57885 1.03 3.79955 3.79C2.23994 5.36 1.86004 7.82 2.47988 10.72C2.17995 11.07 2 11.51 2 12C2 12.93 2.63984 13.71 3.50962 13.93C4.05948 15.25 4.7893 16.62 5.66908 18.01C1.95001 23.88 1.03024 29.44 3.79955 32.21C4.89927 33.3 6.43888 33.82 8.26842 33.82C11.0677 33.82 14.5268 32.6 18.1059 30.36C19.1157 30.99 20.1154 31.55 21.1052 32.01C21.1052 33.11 22.005 34 23.1147 34C23.7145 34 24.2444 33.74 24.6043 33.33C25.774 33.65 26.8837 33.82 27.9035 33.82C29.733 33.82 31.2726 33.3 32.3724 32.21C36.2314 28.37 32.9822 19.12 24.9742 11.16C23.3146 9.51 21.6151 8.08 19.9055 6.86ZM30.9527 30.8C29.813 31.93 27.6835 32.13 25.0242 31.39C24.7643 30.59 24.0145 30.01 23.1247 30.01C22.7448 30.01 22.4049 30.12 22.1049 30.3C21.3951 29.97 20.6753 29.59 19.9255 29.15C21.6251 27.93 23.3346 26.5 24.9842 24.86C26.6338 23.21 28.0834 21.52 29.3031 19.82C32.2424 24.77 32.7523 29.02 30.9627 30.81L30.9527 30.8ZM23.5546 23.44C15.8665 31.09 7.86852 33.42 5.22919 30.8C3.43964 29.02 3.95951 24.76 6.88877 19.82C8.11846 21.51 9.5481 23.21 11.2077 24.85C12.2474 25.89 13.3172 26.84 14.3869 27.72C14.9867 27.38 15.5966 27.01 16.2064 26.61C15.0167 25.68 13.817 24.63 12.6273 23.44C9.1482 19.98 6.7788 16.46 5.44913 13.4C5.79904 13.04 6.01899 12.55 6.01899 12C6.01899 11.02 5.30917 10.2 4.3694 10.04C3.95951 7.89 4.24943 6.19 5.22919 5.21C5.91901 4.52 6.97875 4.17 8.30841 4.17C10.4279 4.17 13.2172 5.06 16.2764 6.85C14.5668 8.07 12.8573 9.5 11.2077 11.15C10.098 12.26 9.07822 13.39 8.15845 14.53C8.48837 15.13 8.85827 15.74 9.26817 16.35C10.2479 15.09 11.3576 13.82 12.6273 12.56C14.4569 10.74 16.3064 9.23 18.086 8C19.8755 9.22 21.715 10.74 23.5446 12.56C25.3741 14.38 26.8937 16.22 28.1234 18C26.8937 19.78 25.3741 21.61 23.5446 23.43L23.5546 23.44Z\"/>',\n    solid: '<path d=\"M18.086 13C15.3167 13 13.0572 15.24 13.0572 18C13.0572 20.76 15.3167 23 18.086 23C20.8553 23 23.1147 20.76 23.1147 18C23.1147 15.24 20.8553 13 18.086 13ZM19.9055 6.86C20.6453 6.42 21.3751 6.04 22.0849 5.71C22.3849 5.89 22.7348 6 23.1147 6C24.0145 6 24.7643 5.41 25.0242 4.61C26.0639 4.32 27.0237 4.17 27.8735 4.17C29.2032 4.17 30.2629 4.52 30.9527 5.21C32.5223 6.77 32.3124 10.21 30.2929 14.35C30.7528 14.96 31.1927 15.56 31.6026 16.17C34.3919 10.99 34.8717 6.27 32.3824 3.8C30.6528 2.09 27.8635 1.79 24.6043 2.66C24.2344 2.25 23.7045 2 23.1147 2C22.015 2 21.1152 2.89 21.1052 3.98C20.1154 4.45 19.1057 5 18.086 5.64C12.1774 1.95 6.57885 1.03 3.79955 3.79C2.23994 5.36 1.86004 7.82 2.47988 10.72C2.17995 11.07 2 11.51 2 12C2 12.93 2.63984 13.71 3.50962 13.93C4.05948 15.25 4.7893 16.62 5.66908 18.01C1.95001 23.88 1.03024 29.44 3.79955 32.21C4.89927 33.3 6.43888 33.82 8.26842 33.82C11.0677 33.82 14.5268 32.6 18.1059 30.36C19.1157 30.99 20.1154 31.55 21.1052 32.01C21.1052 33.11 22.005 34 23.1147 34C23.7145 34 24.2444 33.74 24.6043 33.33C25.774 33.65 26.8837 33.82 27.9035 33.82C29.733 33.82 31.2726 33.3 32.3724 32.21C36.2314 28.37 32.9822 19.12 24.9742 11.16C23.3146 9.51 21.6151 8.08 19.9055 6.86ZM30.9527 30.8C29.813 31.93 27.6835 32.13 25.0242 31.39C24.7643 30.59 24.0145 30.01 23.1247 30.01C22.7448 30.01 22.4049 30.12 22.1049 30.3C21.3951 29.97 20.6753 29.59 19.9255 29.15C21.6251 27.93 23.3346 26.5 24.9842 24.86C26.6338 23.21 28.0834 21.52 29.3031 19.82C32.2424 24.77 32.7523 29.02 30.9627 30.81L30.9527 30.8ZM23.5546 23.44C15.8665 31.09 7.86852 33.42 5.22919 30.8C3.43964 29.02 3.95951 24.76 6.88877 19.82C8.11846 21.51 9.5481 23.21 11.2077 24.85C12.2474 25.89 13.3172 26.84 14.3869 27.72C14.9867 27.38 15.5966 27.01 16.2064 26.61C15.0167 25.68 13.817 24.63 12.6273 23.44C9.1482 19.98 6.7788 16.46 5.44913 13.4C5.79904 13.04 6.01899 12.55 6.01899 12C6.01899 11.02 5.30917 10.2 4.3694 10.04C3.95951 7.89 4.24943 6.19 5.22919 5.21C5.91901 4.52 6.97875 4.17 8.30841 4.17C10.4279 4.17 13.2172 5.06 16.2764 6.85C14.5668 8.07 12.8573 9.5 11.2077 11.15C10.098 12.26 9.07822 13.39 8.15845 14.53C8.48837 15.13 8.85827 15.74 9.26817 16.35C10.2479 15.09 11.3576 13.82 12.6273 12.56C14.4569 10.74 16.3064 9.23 18.086 8C19.8755 9.22 21.715 10.74 23.5446 12.56C25.3741 14.38 26.8937 16.22 28.1234 18C26.8937 19.78 25.3741 21.61 23.5446 23.43L23.5546 23.44Z\"/>'\n  })];\nexport { M as atomIcon, o as atomIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "o", "M", "outline", "solid", "atomIcon", "atomIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/atom.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const o=\"atom\",M=[\"atom\",C({outline:'<path d=\"M18.086 13C15.3167 13 13.0572 15.24 13.0572 18C13.0572 20.76 15.3167 23 18.086 23C20.8553 23 23.1147 20.76 23.1147 18C23.1147 15.24 20.8553 13 18.086 13ZM18.086 21C16.4264 21 15.0667 19.65 15.0667 18C15.0667 16.35 16.4164 15 18.086 15C19.7555 15 21.1052 16.35 21.1052 18C21.1052 19.65 19.7555 21 18.086 21ZM19.9055 6.86C20.6453 6.42 21.3751 6.04 22.0849 5.71C22.3849 5.89 22.7348 6 23.1147 6C24.0145 6 24.7643 5.41 25.0242 4.61C26.0639 4.32 27.0237 4.17 27.8735 4.17C29.2032 4.17 30.2629 4.52 30.9527 5.21C32.5223 6.77 32.3124 10.21 30.2929 14.35C30.7528 14.96 31.1927 15.56 31.6026 16.17C34.3919 10.99 34.8717 6.27 32.3824 3.8C30.6528 2.09 27.8635 1.79 24.6043 2.66C24.2344 2.25 23.7045 2 23.1147 2C22.015 2 21.1152 2.89 21.1052 3.98C20.1154 4.45 19.1057 5 18.086 5.64C12.1774 1.95 6.57885 1.03 3.79955 3.79C2.23994 5.36 1.86004 7.82 2.47988 10.72C2.17995 11.07 2 11.51 2 12C2 12.93 2.63984 13.71 3.50962 13.93C4.05948 15.25 4.7893 16.62 5.66908 18.01C1.95001 23.88 1.03024 29.44 3.79955 32.21C4.89927 33.3 6.43888 33.82 8.26842 33.82C11.0677 33.82 14.5268 32.6 18.1059 30.36C19.1157 30.99 20.1154 31.55 21.1052 32.01C21.1052 33.11 22.005 34 23.1147 34C23.7145 34 24.2444 33.74 24.6043 33.33C25.774 33.65 26.8837 33.82 27.9035 33.82C29.733 33.82 31.2726 33.3 32.3724 32.21C36.2314 28.37 32.9822 19.12 24.9742 11.16C23.3146 9.51 21.6151 8.08 19.9055 6.86ZM30.9527 30.8C29.813 31.93 27.6835 32.13 25.0242 31.39C24.7643 30.59 24.0145 30.01 23.1247 30.01C22.7448 30.01 22.4049 30.12 22.1049 30.3C21.3951 29.97 20.6753 29.59 19.9255 29.15C21.6251 27.93 23.3346 26.5 24.9842 24.86C26.6338 23.21 28.0834 21.52 29.3031 19.82C32.2424 24.77 32.7523 29.02 30.9627 30.81L30.9527 30.8ZM23.5546 23.44C15.8665 31.09 7.86852 33.42 5.22919 30.8C3.43964 29.02 3.95951 24.76 6.88877 19.82C8.11846 21.51 9.5481 23.21 11.2077 24.85C12.2474 25.89 13.3172 26.84 14.3869 27.72C14.9867 27.38 15.5966 27.01 16.2064 26.61C15.0167 25.68 13.817 24.63 12.6273 23.44C9.1482 19.98 6.7788 16.46 5.44913 13.4C5.79904 13.04 6.01899 12.55 6.01899 12C6.01899 11.02 5.30917 10.2 4.3694 10.04C3.95951 7.89 4.24943 6.19 5.22919 5.21C5.91901 4.52 6.97875 4.17 8.30841 4.17C10.4279 4.17 13.2172 5.06 16.2764 6.85C14.5668 8.07 12.8573 9.5 11.2077 11.15C10.098 12.26 9.07822 13.39 8.15845 14.53C8.48837 15.13 8.85827 15.74 9.26817 16.35C10.2479 15.09 11.3576 13.82 12.6273 12.56C14.4569 10.74 16.3064 9.23 18.086 8C19.8755 9.22 21.715 10.74 23.5446 12.56C25.3741 14.38 26.8937 16.22 28.1234 18C26.8937 19.78 25.3741 21.61 23.5446 23.43L23.5546 23.44Z\"/>',solid:'<path d=\"M18.086 13C15.3167 13 13.0572 15.24 13.0572 18C13.0572 20.76 15.3167 23 18.086 23C20.8553 23 23.1147 20.76 23.1147 18C23.1147 15.24 20.8553 13 18.086 13ZM19.9055 6.86C20.6453 6.42 21.3751 6.04 22.0849 5.71C22.3849 5.89 22.7348 6 23.1147 6C24.0145 6 24.7643 5.41 25.0242 4.61C26.0639 4.32 27.0237 4.17 27.8735 4.17C29.2032 4.17 30.2629 4.52 30.9527 5.21C32.5223 6.77 32.3124 10.21 30.2929 14.35C30.7528 14.96 31.1927 15.56 31.6026 16.17C34.3919 10.99 34.8717 6.27 32.3824 3.8C30.6528 2.09 27.8635 1.79 24.6043 2.66C24.2344 2.25 23.7045 2 23.1147 2C22.015 2 21.1152 2.89 21.1052 3.98C20.1154 4.45 19.1057 5 18.086 5.64C12.1774 1.95 6.57885 1.03 3.79955 3.79C2.23994 5.36 1.86004 7.82 2.47988 10.72C2.17995 11.07 2 11.51 2 12C2 12.93 2.63984 13.71 3.50962 13.93C4.05948 15.25 4.7893 16.62 5.66908 18.01C1.95001 23.88 1.03024 29.44 3.79955 32.21C4.89927 33.3 6.43888 33.82 8.26842 33.82C11.0677 33.82 14.5268 32.6 18.1059 30.36C19.1157 30.99 20.1154 31.55 21.1052 32.01C21.1052 33.11 22.005 34 23.1147 34C23.7145 34 24.2444 33.74 24.6043 33.33C25.774 33.65 26.8837 33.82 27.9035 33.82C29.733 33.82 31.2726 33.3 32.3724 32.21C36.2314 28.37 32.9822 19.12 24.9742 11.16C23.3146 9.51 21.6151 8.08 19.9055 6.86ZM30.9527 30.8C29.813 31.93 27.6835 32.13 25.0242 31.39C24.7643 30.59 24.0145 30.01 23.1247 30.01C22.7448 30.01 22.4049 30.12 22.1049 30.3C21.3951 29.97 20.6753 29.59 19.9255 29.15C21.6251 27.93 23.3346 26.5 24.9842 24.86C26.6338 23.21 28.0834 21.52 29.3031 19.82C32.2424 24.77 32.7523 29.02 30.9627 30.81L30.9527 30.8ZM23.5546 23.44C15.8665 31.09 7.86852 33.42 5.22919 30.8C3.43964 29.02 3.95951 24.76 6.88877 19.82C8.11846 21.51 9.5481 23.21 11.2077 24.85C12.2474 25.89 13.3172 26.84 14.3869 27.72C14.9867 27.38 15.5966 27.01 16.2064 26.61C15.0167 25.68 13.817 24.63 12.6273 23.44C9.1482 19.98 6.7788 16.46 5.44913 13.4C5.79904 13.04 6.01899 12.55 6.01899 12C6.01899 11.02 5.30917 10.2 4.3694 10.04C3.95951 7.89 4.24943 6.19 5.22919 5.21C5.91901 4.52 6.97875 4.17 8.30841 4.17C10.4279 4.17 13.2172 5.06 16.2764 6.85C14.5668 8.07 12.8573 9.5 11.2077 11.15C10.098 12.26 9.07822 13.39 8.15845 14.53C8.48837 15.13 8.85827 15.74 9.26817 16.35C10.2479 15.09 11.3576 13.82 12.6273 12.56C14.4569 10.74 16.3064 9.23 18.086 8C19.8755 9.22 21.715 10.74 23.5446 12.56C25.3741 14.38 26.8937 16.22 28.1234 18C26.8937 19.78 25.3741 21.61 23.5446 23.43L23.5546 23.44Z\"/>'})];export{M as atomIcon,o as atomIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,MAAM;EAACC,CAAC,GAAC,CAAC,MAAM,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,y9EAAy9E;IAACC,KAAK,EAAC;EAAg0E,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,QAAQ,EAACJ,CAAC,IAAIK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}