{"ast": null, "code": "/**\n * Tests whether or not an object is an array.\n *\n * @private\n * @param {*} val The object to test.\n * @return {Boolean} `true` if `val` is an array, `false` otherwise.\n * @example\n *\n *      _isArray([]); //=> true\n *      _isArray(null); //=> false\n *      _isArray({}); //=> false\n */\nexport default Array.isArray || function _isArray(val) {\n  return val != null && val.length >= 0 && Object.prototype.toString.call(val) === '[object Array]';\n};", "map": {"version": 3, "names": ["Array", "isArray", "_isArray", "val", "length", "Object", "prototype", "toString", "call"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_isArray.js"], "sourcesContent": ["/**\n * Tests whether or not an object is an array.\n *\n * @private\n * @param {*} val The object to test.\n * @return {Boolean} `true` if `val` is an array, `false` otherwise.\n * @example\n *\n *      _isArray([]); //=> true\n *      _isArray(null); //=> false\n *      _isArray({}); //=> false\n */\nexport default Array.isArray || function _isArray(val) {\n  return val != null && val.length >= 0 && Object.prototype.toString.call(val) === '[object Array]';\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeA,KAAK,CAACC,OAAO,IAAI,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrD,OAAOA,GAAG,IAAI,IAAI,IAAIA,GAAG,CAACC,MAAM,IAAI,CAAC,IAAIC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,GAAG,CAAC,KAAK,gBAAgB;AACnG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}