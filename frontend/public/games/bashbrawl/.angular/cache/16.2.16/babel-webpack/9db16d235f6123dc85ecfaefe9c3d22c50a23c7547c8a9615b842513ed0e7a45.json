{"ast": null, "code": "import { ClarityIcons as s } from \"../icon.service.js\";\nimport { bookmarkIcon as r } from \"../shapes/bookmark.js\";\nimport { calendarIcon as o, calendarIconName as p } from \"../shapes/calendar.js\";\nimport { chatBubbleIcon as m } from \"../shapes/chat-bubble.js\";\nimport { contractIcon as a } from \"../shapes/contract.js\";\nimport { crownIcon as e } from \"../shapes/crown.js\";\nimport { envelopeIcon as t, envelopeIconName as h } from \"../shapes/envelope.js\";\nimport { eventIcon as i } from \"../shapes/event.js\";\nimport { flagIcon as f } from \"../shapes/flag.js\";\nimport { halfStarIcon as j } from \"../shapes/half-star.js\";\nimport { happyFaceIcon as c } from \"../shapes/happy-face.js\";\nimport { hashtagIcon as n } from \"../shapes/hashtag.js\";\nimport { heartBrokenIcon as b } from \"../shapes/heart-broken.js\";\nimport { heartIcon as l } from \"../shapes/heart.js\";\nimport { inboxIcon as d } from \"../shapes/inbox.js\";\nimport { neutralFaceIcon as u } from \"../shapes/neutral-face.js\";\nimport { pictureIcon as k } from \"../shapes/picture.js\";\nimport { sadFaceIcon as v } from \"../shapes/sad-face.js\";\nimport { shareIcon as g } from \"../shapes/share.js\";\nimport { starIcon as w, starIconName as x } from \"../shapes/star.js\";\nimport { talkBubblesIcon as y } from \"../shapes/talk-bubbles.js\";\nimport { tasksIcon as A } from \"../shapes/tasks.js\";\nimport { thumbsDownIcon as I } from \"../shapes/thumbs-down.js\";\nimport { thumbsUpIcon as q } from \"../shapes/thumbs-up.js\";\nconst z = [r, o, m, a, e, t, i, f, j, c, n, l, b, d, u, k, v, g, w, y, A, I, q],\n  B = [[x, [\"favorite\"]], [h, [\"email\"]], [p, [\"date\"]]];\nfunction C() {\n  s.addIcons(...z), s.addAliases(...B);\n}\nexport { C as loadSocialIconSet, B as socialCollectionAliases, z as socialCollectionIcons };", "map": {"version": 3, "names": ["ClarityIcons", "s", "bookmarkIcon", "r", "calendarIcon", "o", "calendarIconName", "p", "chatBubbleIcon", "m", "contractIcon", "a", "crownIcon", "e", "envelopeIcon", "t", "envelopeIconName", "h", "eventIcon", "i", "flagIcon", "f", "halfStarIcon", "j", "happyFaceIcon", "c", "hashtagIcon", "n", "heartBrokenIcon", "b", "heartIcon", "l", "inboxIcon", "d", "neutralFaceIcon", "u", "pictureIcon", "k", "sadFaceIcon", "v", "shareIcon", "g", "starIcon", "w", "starIconName", "x", "talkBubblesIcon", "y", "tasksIcon", "A", "thumbsDownIcon", "I", "thumbsUpIcon", "q", "z", "B", "C", "addIcons", "addAliases", "loadSocialIconSet", "socialCollectionAliases", "socialCollectionIcons"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/collections/social.js"], "sourcesContent": ["import{ClarityIcons as s}from\"../icon.service.js\";import{bookmarkIcon as r}from\"../shapes/bookmark.js\";import{calendarIcon as o,calendarIconName as p}from\"../shapes/calendar.js\";import{chatBubbleIcon as m}from\"../shapes/chat-bubble.js\";import{contractIcon as a}from\"../shapes/contract.js\";import{crownIcon as e}from\"../shapes/crown.js\";import{envelopeIcon as t,envelopeIconName as h}from\"../shapes/envelope.js\";import{eventIcon as i}from\"../shapes/event.js\";import{flagIcon as f}from\"../shapes/flag.js\";import{halfStarIcon as j}from\"../shapes/half-star.js\";import{happyFaceIcon as c}from\"../shapes/happy-face.js\";import{hashtagIcon as n}from\"../shapes/hashtag.js\";import{heartBrokenIcon as b}from\"../shapes/heart-broken.js\";import{heartIcon as l}from\"../shapes/heart.js\";import{inboxIcon as d}from\"../shapes/inbox.js\";import{neutralFaceIcon as u}from\"../shapes/neutral-face.js\";import{pictureIcon as k}from\"../shapes/picture.js\";import{sadFaceIcon as v}from\"../shapes/sad-face.js\";import{shareIcon as g}from\"../shapes/share.js\";import{starIcon as w,starIconName as x}from\"../shapes/star.js\";import{talkBubblesIcon as y}from\"../shapes/talk-bubbles.js\";import{tasksIcon as A}from\"../shapes/tasks.js\";import{thumbsDownIcon as I}from\"../shapes/thumbs-down.js\";import{thumbsUpIcon as q}from\"../shapes/thumbs-up.js\";const z=[r,o,m,a,e,t,i,f,j,c,n,l,b,d,u,k,v,g,w,y,A,I,q],B=[[x,[\"favorite\"]],[h,[\"email\"]],[p,[\"date\"]]];function C(){s.addIcons(...z),s.addAliases(...B)}export{C as loadSocialIconSet,B as socialCollectionAliases,z as socialCollectionIcons};\n"], "mappings": "AAAA,SAAOA,YAAY,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,YAAY,IAAIC,CAAC,EAACC,gBAAgB,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,YAAY,IAAIC,CAAC,EAACC,gBAAgB,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,wBAAwB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,EAACC,YAAY,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,wBAAwB;AAAC,MAAMC,CAAC,GAAC,CAACnD,CAAC,EAACE,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACF,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,CAAC;EAACE,CAAC,GAAC,CAAC,CAACV,CAAC,EAAC,CAAC,UAAU,CAAC,CAAC,EAAC,CAAC5B,CAAC,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAACV,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AAAC,SAASiD,CAACA,CAAA,EAAE;EAACvD,CAAC,CAACwD,QAAQ,CAAC,GAAGH,CAAC,CAAC,EAACrD,CAAC,CAACyD,UAAU,CAAC,GAAGH,CAAC,CAAC;AAAA;AAAC,SAAOC,CAAC,IAAIG,iBAAiB,EAACJ,CAAC,IAAIK,uBAAuB,EAACN,CAAC,IAAIO,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}