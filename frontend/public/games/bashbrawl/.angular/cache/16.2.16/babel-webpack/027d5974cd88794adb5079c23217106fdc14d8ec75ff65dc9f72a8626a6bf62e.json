{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"cursor-hand-open\",\n  V = [\"cursor-hand-open\", C({\n    outline: '<path d=\"M29.6235 6.99688H29.374C28.8851 6.99688 28.4161 7.10681 27.997 7.29669C27.957 5.46783 26.4103 3.99875 24.5143 3.99875C23.9156 3.99875 23.3568 4.15865 22.8678 4.41849C22.4487 3.02936 21.1814 2 19.6547 2H19.4052C17.5691 2 16.0822 3.47908 16.0423 5.29794C15.6232 5.10806 15.1542 4.99813 14.6653 4.99813H14.4158C12.5597 4.99813 11.0429 6.50718 11.0429 8.37602V17.4304L8.76777 14.9519C7.76989 13.6327 5.94377 13.2829 4.53675 14.1424C3.1497 14.9819 2.60086 16.7308 3.29938 18.2998L6.60237 24.2161C6.86182 25.2055 8.45843 30.842 12.44 33.8001C12.6096 33.93 12.8192 34 13.0387 34H26.57C26.7895 34 27.009 33.92 27.1887 33.7901C30.8808 30.9019 32.9963 26.5646 32.9963 21.8876V10.3848C32.9963 8.52592 31.4895 7.00687 29.6235 7.00687V6.99688ZM31.0006 11.0344V21.8776C31.0006 25.8151 29.2643 29.4828 26.2207 31.9913H13.378C9.8854 29.183 8.52828 23.6665 8.5183 23.6065C8.49834 23.5166 8.45843 23.4266 8.41851 23.3467L5.08559 17.4004C4.83612 16.8407 5.04567 16.1711 5.57455 15.8513C6.1134 15.5215 6.80194 15.6615 7.24101 16.2311L11.3024 20.6683C11.482 20.8582 11.7415 20.9881 12.0308 20.9881C12.5797 20.9881 13.0287 20.5384 13.0287 19.9888V8.37602C13.0287 7.61649 13.6474 6.99688 14.4058 6.99688H14.6553C15.4137 6.99688 16.0323 7.61649 16.0323 8.37602V15.9913H18.0281V5.37789C18.0281 4.61836 18.6468 3.99875 19.4052 3.99875H19.6547C20.4131 3.99875 21.0317 4.61836 21.0317 5.37789V15.9913H23.0275V7.37664C23.0275 6.61711 23.6961 5.9975 24.5243 5.9975C25.3526 5.9975 26.0211 6.61711 26.0211 7.37664V15.9913H28.0169V10.3748C28.0169 9.61524 28.6356 8.99563 29.394 8.99563H29.6435C30.4018 8.99563 31.0205 9.61524 31.0205 10.3748V11.0344H31.0006Z\"/>'\n  })];\nexport { V as cursorHandOpenIcon, H as cursorHandOpenIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "V", "outline", "cursorHandOpenIcon", "cursorHandOpenIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/cursor-hand-open.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"cursor-hand-open\",V=[\"cursor-hand-open\",C({outline:'<path d=\"M29.6235 6.99688H29.374C28.8851 6.99688 28.4161 7.10681 27.997 7.29669C27.957 5.46783 26.4103 3.99875 24.5143 3.99875C23.9156 3.99875 23.3568 4.15865 22.8678 4.41849C22.4487 3.02936 21.1814 2 19.6547 2H19.4052C17.5691 2 16.0822 3.47908 16.0423 5.29794C15.6232 5.10806 15.1542 4.99813 14.6653 4.99813H14.4158C12.5597 4.99813 11.0429 6.50718 11.0429 8.37602V17.4304L8.76777 14.9519C7.76989 13.6327 5.94377 13.2829 4.53675 14.1424C3.1497 14.9819 2.60086 16.7308 3.29938 18.2998L6.60237 24.2161C6.86182 25.2055 8.45843 30.842 12.44 33.8001C12.6096 33.93 12.8192 34 13.0387 34H26.57C26.7895 34 27.009 33.92 27.1887 33.7901C30.8808 30.9019 32.9963 26.5646 32.9963 21.8876V10.3848C32.9963 8.52592 31.4895 7.00687 29.6235 7.00687V6.99688ZM31.0006 11.0344V21.8776C31.0006 25.8151 29.2643 29.4828 26.2207 31.9913H13.378C9.8854 29.183 8.52828 23.6665 8.5183 23.6065C8.49834 23.5166 8.45843 23.4266 8.41851 23.3467L5.08559 17.4004C4.83612 16.8407 5.04567 16.1711 5.57455 15.8513C6.1134 15.5215 6.80194 15.6615 7.24101 16.2311L11.3024 20.6683C11.482 20.8582 11.7415 20.9881 12.0308 20.9881C12.5797 20.9881 13.0287 20.5384 13.0287 19.9888V8.37602C13.0287 7.61649 13.6474 6.99688 14.4058 6.99688H14.6553C15.4137 6.99688 16.0323 7.61649 16.0323 8.37602V15.9913H18.0281V5.37789C18.0281 4.61836 18.6468 3.99875 19.4052 3.99875H19.6547C20.4131 3.99875 21.0317 4.61836 21.0317 5.37789V15.9913H23.0275V7.37664C23.0275 6.61711 23.6961 5.9975 24.5243 5.9975C25.3526 5.9975 26.0211 6.61711 26.0211 7.37664V15.9913H28.0169V10.3748C28.0169 9.61524 28.6356 8.99563 29.394 8.99563H29.6435C30.4018 8.99563 31.0205 9.61524 31.0205 10.3748V11.0344H31.0006Z\"/>'})];export{V as cursorHandOpenIcon,H as cursorHandOpenIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,kBAAkB;EAACC,CAAC,GAAC,CAAC,kBAAkB,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAumD,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,kBAAkB,EAACH,CAAC,IAAII,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}