{"ast": null, "code": "import { renderIcon as e } from \"../icon.renderer.js\";\nconst o = \"view-columns\",\n  l = [\"view-columns\", e({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M31 5H5C3.89543 5 3 5.89543 3 7V29C3 30.1046 3.89543 31 5 31H31C32.1046 31 33 30.1046 33 29V7C33 5.89543 32.1046 5 31 5ZM13 29H5V7H13V29ZM15 29H23V7H15V29Z\"/>'\n  })];\nexport { l as viewColumnsIcon, o as viewColumnsIconName };", "map": {"version": 3, "names": ["renderIcon", "e", "o", "l", "outline", "viewColumnsIcon", "viewColumnsIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/view-columns.js"], "sourcesContent": ["import{renderIcon as e}from\"../icon.renderer.js\";const o=\"view-columns\",l=[\"view-columns\",e({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M31 5H5C3.89543 5 3 5.89543 3 7V29C3 30.1046 3.89543 31 5 31H31C32.1046 31 33 30.1046 33 29V7C33 5.89543 32.1046 5 31 5ZM13 29H5V7H13V29ZM15 29H23V7H15V29Z\"/>'})];export{l as viewColumnsIcon,o as viewColumnsIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,cAAc;EAACC,CAAC,GAAC,CAAC,cAAc,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAiN,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,eAAe,EAACH,CAAC,IAAII,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}