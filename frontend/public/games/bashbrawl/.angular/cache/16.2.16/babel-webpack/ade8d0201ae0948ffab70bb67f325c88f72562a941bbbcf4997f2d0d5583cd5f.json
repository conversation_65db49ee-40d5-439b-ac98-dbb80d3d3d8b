{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"yen\",\n  e = [\"yen\", C({\n    outline: '<path d=\"M29.8184 4.56313C30.0308 4.26495 30.0589 3.88015 29.8919 3.5565C29.7249 3.23286 29.3888 3.02067 29.0126 3.00143C28.6364 2.98218 28.2785 3.15887 28.0765 3.46364L17.9905 18.2368L7.90455 3.46364C7.58355 3.01307 6.94483 2.8924 6.4694 3.1925C5.99397 3.4926 5.85747 4.10261 6.1627 4.56313L16.9475 20.3558V22.205H11.7324C11.2716 22.205 10.898 22.563 10.898 23.0046C10.898 23.4462 11.2716 23.8042 11.7324 23.8042H16.9475V26.2031H11.7324C11.2716 26.2031 10.898 26.5611 10.898 27.0028C10.898 27.4444 11.2716 27.8024 11.7324 27.8024H16.9475V32.0005C16.9475 32.5525 17.4145 33 17.9905 33C18.5666 33 19.0336 32.5525 19.0336 32.0005V27.8024H24.2487C24.7095 27.8024 25.0831 27.4444 25.0831 27.0028C25.0831 26.5611 24.7095 26.2031 24.2487 26.2031H19.0336V23.8042H24.2487C24.7095 23.8042 25.0831 23.4462 25.0831 23.0046C25.0831 22.563 24.7095 22.205 24.2487 22.205H19.0336V20.3558L29.8184 4.56313Z\"/>',\n    solid: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 18C2 9.16344 9.16344 2 18 2C22.2435 2 26.3131 3.68571 29.3137 6.68629C32.3143 9.68687 34 13.7565 34 18C34 26.8366 26.8366 34 18 34C9.16344 34 2 26.8366 2 18ZM19.74 20L26.07 9.91V9.92C26.3076 9.5413 26.3251 9.06462 26.1159 8.66951C25.9067 8.27441 25.5027 8.02091 25.0559 8.00451C24.6091 7.98811 24.1876 8.2113 23.95 8.59L18 18.08L12.05 8.58C11.8124 8.2013 11.3908 7.97811 10.9441 7.99451C10.4973 8.01091 10.0933 8.26441 9.88409 8.65951C9.67492 9.05462 9.69242 9.5313 9.93 9.91L16.26 20H14C13.4477 20 13 20.4477 13 21C13 21.5523 13.4477 22 14 22H16.75V24H14C13.4477 24 13 24.4477 13 25C13 25.5523 13.4477 26 14 26H16.75V28.75C16.75 29.4404 17.3096 30 18 30C18.6904 30 19.25 29.4404 19.25 28.75V26H22C22.5523 26 23 25.5523 23 25C23 24.4477 22.5523 24 22 24H19.25V22H22C22.5523 22 23 21.5523 23 21C23 20.4477 22.5523 20 22 20H19.74Z\"/>'\n  })];\nexport { e as yenIcon, H as yenIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "e", "outline", "solid", "yenIcon", "yenIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/yen.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"yen\",e=[\"yen\",C({outline:'<path d=\"M29.8184 4.56313C30.0308 4.26495 30.0589 3.88015 29.8919 3.5565C29.7249 3.23286 29.3888 3.02067 29.0126 3.00143C28.6364 2.98218 28.2785 3.15887 28.0765 3.46364L17.9905 18.2368L7.90455 3.46364C7.58355 3.01307 6.94483 2.8924 6.4694 3.1925C5.99397 3.4926 5.85747 4.10261 6.1627 4.56313L16.9475 20.3558V22.205H11.7324C11.2716 22.205 10.898 22.563 10.898 23.0046C10.898 23.4462 11.2716 23.8042 11.7324 23.8042H16.9475V26.2031H11.7324C11.2716 26.2031 10.898 26.5611 10.898 27.0028C10.898 27.4444 11.2716 27.8024 11.7324 27.8024H16.9475V32.0005C16.9475 32.5525 17.4145 33 17.9905 33C18.5666 33 19.0336 32.5525 19.0336 32.0005V27.8024H24.2487C24.7095 27.8024 25.0831 27.4444 25.0831 27.0028C25.0831 26.5611 24.7095 26.2031 24.2487 26.2031H19.0336V23.8042H24.2487C24.7095 23.8042 25.0831 23.4462 25.0831 23.0046C25.0831 22.563 24.7095 22.205 24.2487 22.205H19.0336V20.3558L29.8184 4.56313Z\"/>',solid:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 18C2 9.16344 9.16344 2 18 2C22.2435 2 26.3131 3.68571 29.3137 6.68629C32.3143 9.68687 34 13.7565 34 18C34 26.8366 26.8366 34 18 34C9.16344 34 2 26.8366 2 18ZM19.74 20L26.07 9.91V9.92C26.3076 9.5413 26.3251 9.06462 26.1159 8.66951C25.9067 8.27441 25.5027 8.02091 25.0559 8.00451C24.6091 7.98811 24.1876 8.2113 23.95 8.59L18 18.08L12.05 8.58C11.8124 8.2013 11.3908 7.97811 10.9441 7.99451C10.4973 8.01091 10.0933 8.26441 9.88409 8.65951C9.67492 9.05462 9.69242 9.5313 9.93 9.91L16.26 20H14C13.4477 20 13 20.4477 13 21C13 21.5523 13.4477 22 14 22H16.75V24H14C13.4477 24 13 24.4477 13 25C13 25.5523 13.4477 26 14 26H16.75V28.75C16.75 29.4404 17.3096 30 18 30C18.6904 30 19.25 29.4404 19.25 28.75V26H22C22.5523 26 23 25.5523 23 25C23 24.4477 22.5523 24 22 24H19.25V22H22C22.5523 22 23 21.5523 23 21C23 20.4477 22.5523 20 22 20H19.74Z\"/>'})];export{e as yenIcon,H as yenIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,CAAC,KAAK,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,83BAA83B;IAACC,KAAK,EAAC;EAAq3B,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,OAAO,EAACJ,CAAC,IAAIK,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}