{"ast": null, "code": "import { renderIcon as H } from \"../icon.renderer.js\";\nconst V = \"tools\",\n  C = [\"tools\", H({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M33.71 13.38L29.62 9.29C29.4338 9.10526 29.1824 9.0011 28.92 9H23.92V7.05C23.9482 5.95663 23.0929 5.04339 22 5H13.84C13.3215 5.0204 12.8333 5.24984 12.4867 5.63603C12.14 6.02222 11.9645 6.53228 12 7.05V9H7.08002C6.81421 8.99846 6.55874 9.10281 6.37002 9.29L2.29002 13.38C2.10283 13.5687 1.99848 13.8242 2.00002 14.09V29C2.00002 30.1046 2.89545 31 4.00002 31H32C33.1046 31 34 30.1046 34 29V14.08C33.9989 13.8177 33.8948 13.5663 33.71 13.38ZM14 7H22V9H14V7ZM22 18H32V14.5L28.5 11H7.50002L4.00002 14.5V18H14V19.93H4.00002V29H32V19.93H22V18ZM20 15H16C15.4477 15 15 15.4477 15 16V22C15 22.5523 15.4477 23 16 23H20C20.5523 23 21 22.5523 21 22V16C21 15.4477 20.5523 15 20 15ZM16.4 21.6H19.6V16.4H16.4V21.6Z\"/>',\n    solid: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M29.62 9.29L33.71 13.38C33.8948 13.5663 33.9989 13.8177 34 14.08V18H21V16C21 15.4477 20.5523 15 20 15H16C15.4477 15 15 15.4477 15 16V18H2.00002V14.09C1.99848 13.8242 2.10283 13.5687 2.29002 13.38L6.37002 9.29C6.55874 9.10281 6.81421 8.99846 7.08002 9H12V7.05C11.9645 6.53228 12.14 6.02222 12.4867 5.63603C12.8333 5.24984 13.3215 5.0204 13.84 5H22C23.0929 5.04339 23.9482 5.95663 23.92 7.05V9H28.92C29.1824 9.0011 29.4338 9.10526 29.62 9.29ZM14 9H22V7H14V9Z\"/><path d=\"M21 22C21 22.5523 20.5523 23 20 23H16C15.4477 23 15 22.5523 15 22V20H2.00002V29C2.00002 30.1046 2.89545 31 4.00002 31H32C33.1046 31 34 30.1046 34 29V20H21V22Z\"/><path d=\"M19.6 16.4H16.4V21.6H19.6V16.4Z\"/>'\n  })];\nexport { C as toolsIcon, V as toolsIconName };", "map": {"version": 3, "names": ["renderIcon", "H", "V", "C", "outline", "solid", "toolsIcon", "toolsIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/tools.js"], "sourcesContent": ["import{renderIcon as H}from\"../icon.renderer.js\";const V=\"tools\",C=[\"tools\",H({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M33.71 13.38L29.62 9.29C29.4338 9.10526 29.1824 9.0011 28.92 9H23.92V7.05C23.9482 5.95663 23.0929 5.04339 22 5H13.84C13.3215 5.0204 12.8333 5.24984 12.4867 5.63603C12.14 6.02222 11.9645 6.53228 12 7.05V9H7.08002C6.81421 8.99846 6.55874 9.10281 6.37002 9.29L2.29002 13.38C2.10283 13.5687 1.99848 13.8242 2.00002 14.09V29C2.00002 30.1046 2.89545 31 4.00002 31H32C33.1046 31 34 30.1046 34 29V14.08C33.9989 13.8177 33.8948 13.5663 33.71 13.38ZM14 7H22V9H14V7ZM22 18H32V14.5L28.5 11H7.50002L4.00002 14.5V18H14V19.93H4.00002V29H32V19.93H22V18ZM20 15H16C15.4477 15 15 15.4477 15 16V22C15 22.5523 15.4477 23 16 23H20C20.5523 23 21 22.5523 21 22V16C21 15.4477 20.5523 15 20 15ZM16.4 21.6H19.6V16.4H16.4V21.6Z\"/>',solid:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M29.62 9.29L33.71 13.38C33.8948 13.5663 33.9989 13.8177 34 14.08V18H21V16C21 15.4477 20.5523 15 20 15H16C15.4477 15 15 15.4477 15 16V18H2.00002V14.09C1.99848 13.8242 2.10283 13.5687 2.29002 13.38L6.37002 9.29C6.55874 9.10281 6.81421 8.99846 7.08002 9H12V7.05C11.9645 6.53228 12.14 6.02222 12.4867 5.63603C12.8333 5.24984 13.3215 5.0204 13.84 5H22C23.0929 5.04339 23.9482 5.95663 23.92 7.05V9H28.92C29.1824 9.0011 29.4338 9.10526 29.62 9.29ZM14 9H22V7H14V9Z\"/><path d=\"M21 22C21 22.5523 20.5523 23 20 23H16C15.4477 23 15 22.5523 15 22V20H2.00002V29C2.00002 30.1046 2.89545 31 4.00002 31H32C33.1046 31 34 30.1046 34 29V20H21V22Z\"/><path d=\"M19.6 16.4H16.4V21.6H19.6V16.4Z\"/>'})];export{C as toolsIcon,V as toolsIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,OAAO;EAACC,CAAC,GAAC,CAAC,OAAO,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,ivBAAivB;IAACC,KAAK,EAAC;EAAmtB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,SAAS,EAACJ,CAAC,IAAIK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}