{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst e = \"volume-down\",\n  o = [\"volume-down\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M18.1039 31.9672C17.5674 31.9676 17.0529 31.7555 16.6747 31.3779L9.18638 23.9765H4.01299C2.90125 23.9765 2 23.0822 2 21.9789V13.9883C2 12.885 2.90125 11.9906 4.01299 11.9906H9.2669L16.6445 4.58929C17.2158 3.99225 18.0977 3.80422 18.8658 4.11571C19.6338 4.4272 20.1305 5.17439 20.1169 5.99764V29.9695C20.1169 30.7796 19.6239 31.5095 18.8689 31.8173C18.6262 31.9163 18.3663 31.9672 18.1039 31.9672ZM4.013 13.9883V21.9789H9.60912C9.87521 21.9747 10.1321 22.0752 10.3237 22.2586L18.1039 29.9695V5.99764L10.4042 13.6686C10.216 13.8593 9.95862 13.9672 9.68963 13.9683L4.013 13.9883Z\"/><path d=\"M22.5695 24.3664C22.6962 24.7972 23.0969 25.0912 23.5491 25.0852C23.7337 25.098 23.9183 25.06 24.0826 24.9754C26.5639 23.4527 28.0524 20.7444 27.9986 17.8497C27.9449 14.955 26.3571 12.3029 23.8209 10.8719C23.5089 10.6751 23.1132 10.6639 22.7904 10.8427C22.4676 11.0215 22.2695 11.3615 22.2745 11.7284C22.2795 12.0952 22.4868 12.4298 22.8144 12.5999C24.7176 13.6872 25.9063 15.6859 25.9454 17.8649C25.9846 20.0439 24.8686 22.0835 23.0056 23.2374C22.6212 23.4738 22.4428 23.9356 22.5695 24.3664Z\"/>',\n    solid: '<path d=\"M9.04547 12.1101L18.4461 4.24078C18.7429 3.99003 19.1648 3.92944 19.5248 4.08587C19.8849 4.2423 20.1164 4.58681 20.1169 4.96673V31.0333C20.1164 31.4132 19.8849 31.7577 19.5248 31.9141C19.1648 32.0706 18.7429 32.01 18.4461 31.7592L8.87436 23.7253H3.0065C2.45062 23.7253 2 23.292 2 22.7574V13.078C2 12.5435 2.45062 12.1101 3.0065 12.1101H9.04547Z\"/><path d=\"M22.5695 24.1031C22.6962 24.5206 23.0969 24.8056 23.5491 24.7998C23.7337 24.8121 23.9183 24.7753 24.0826 24.6933C26.5639 23.2177 28.0524 20.5932 27.9986 17.788C27.9449 14.9828 26.3571 12.4127 23.8209 11.026C23.5089 10.8353 23.1132 10.8244 22.7904 10.9977C22.4676 11.1709 22.2695 11.5005 22.2745 11.856C22.2795 12.2115 22.4868 12.5357 22.8144 12.7005C24.7176 13.7542 25.9063 15.6911 25.9454 17.8027C25.9846 19.9143 24.8686 21.8908 23.0056 23.0091C22.6212 23.2381 22.4428 23.6856 22.5695 24.1031Z\"/>'\n  })];\nexport { o as volumeDownIcon, e as volumeDownIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "e", "o", "outline", "solid", "volumeDownIcon", "volumeDownIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/volume-down.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const e=\"volume-down\",o=[\"volume-down\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M18.1039 31.9672C17.5674 31.9676 17.0529 31.7555 16.6747 31.3779L9.18638 23.9765H4.01299C2.90125 23.9765 2 23.0822 2 21.9789V13.9883C2 12.885 2.90125 11.9906 4.01299 11.9906H9.2669L16.6445 4.58929C17.2158 3.99225 18.0977 3.80422 18.8658 4.11571C19.6338 4.4272 20.1305 5.17439 20.1169 5.99764V29.9695C20.1169 30.7796 19.6239 31.5095 18.8689 31.8173C18.6262 31.9163 18.3663 31.9672 18.1039 31.9672ZM4.013 13.9883V21.9789H9.60912C9.87521 21.9747 10.1321 22.0752 10.3237 22.2586L18.1039 29.9695V5.99764L10.4042 13.6686C10.216 13.8593 9.95862 13.9672 9.68963 13.9683L4.013 13.9883Z\"/><path d=\"M22.5695 24.3664C22.6962 24.7972 23.0969 25.0912 23.5491 25.0852C23.7337 25.098 23.9183 25.06 24.0826 24.9754C26.5639 23.4527 28.0524 20.7444 27.9986 17.8497C27.9449 14.955 26.3571 12.3029 23.8209 10.8719C23.5089 10.6751 23.1132 10.6639 22.7904 10.8427C22.4676 11.0215 22.2695 11.3615 22.2745 11.7284C22.2795 12.0952 22.4868 12.4298 22.8144 12.5999C24.7176 13.6872 25.9063 15.6859 25.9454 17.8649C25.9846 20.0439 24.8686 22.0835 23.0056 23.2374C22.6212 23.4738 22.4428 23.9356 22.5695 24.3664Z\"/>',solid:'<path d=\"M9.04547 12.1101L18.4461 4.24078C18.7429 3.99003 19.1648 3.92944 19.5248 4.08587C19.8849 4.2423 20.1164 4.58681 20.1169 4.96673V31.0333C20.1164 31.4132 19.8849 31.7577 19.5248 31.9141C19.1648 32.0706 18.7429 32.01 18.4461 31.7592L8.87436 23.7253H3.0065C2.45062 23.7253 2 23.292 2 22.7574V13.078C2 12.5435 2.45062 12.1101 3.0065 12.1101H9.04547Z\"/><path d=\"M22.5695 24.1031C22.6962 24.5206 23.0969 24.8056 23.5491 24.7998C23.7337 24.8121 23.9183 24.7753 24.0826 24.6933C26.5639 23.2177 28.0524 20.5932 27.9986 17.788C27.9449 14.9828 26.3571 12.4127 23.8209 11.026C23.5089 10.8353 23.1132 10.8244 22.7904 10.9977C22.4676 11.1709 22.2695 11.5005 22.2745 11.856C22.2795 12.2115 22.4868 12.5357 22.8144 12.7005C24.7176 13.7542 25.9063 15.6911 25.9454 17.8027C25.9846 19.9143 24.8686 21.8908 23.0056 23.0091C22.6212 23.2381 22.4428 23.6856 22.5695 24.1031Z\"/>'})];export{o as volumeDownIcon,e as volumeDownIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,aAAa;EAACC,CAAC,GAAC,CAAC,aAAa,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,+mCAA+mC;IAACC,KAAK,EAAC;EAAg2B,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,cAAc,EAACJ,CAAC,IAAIK,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}