{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"thumbs-down\",\n  V = [\"thumbs-down\", C({\n    outline: '<path d=\"M17.67 31.1309C17.83 32.7604 19.19 33.99 20.85 33.99H20.98C22.64 33.99 24 32.6504 24 31.0009V21.9937H31C31.85 21.9937 32.7 21.6139 33.28 20.9741C33.81 20.3942 34.06 19.6545 33.99 18.8847L33.02 10.1774C32.99 9.87753 32.91 9.59761 32.8 9.3277L30.44 3.80942C29.97 2.70977 28.88 1.98999 27.68 1.98999H16.67C16.02 1.98999 15.39 2.19992 14.87 2.5898L12 4.73913V6.98843C12.22 6.98843 12.43 6.91845 12.6 6.78849L16.07 4.1893C16.24 4.05934 16.45 3.98937 16.67 3.98937H27.68C28.08 3.98937 28.44 4.22929 28.6 4.59917L30.96 10.1175C31 10.2074 31.02 10.2974 31.03 10.3974L31.99 19.0847C32.01 19.2846 31.94 19.4645 31.79 19.6245C31.59 19.8444 31.27 19.9944 30.98 19.9944H22.98C22.43 19.9944 21.98 20.4442 21.98 20.9941V31.0009C21.98 31.5608 21.53 31.9906 20.96 31.9906H20.83C20.22 31.9906 19.69 31.5308 19.64 30.9409C18.9 23.1734 12.77 19.3046 12.51 19.1446C12.35 19.0447 12.17 18.9947 11.98 18.9947V21.224C13.38 22.2836 17.12 25.6226 17.65 31.1309H17.67ZM9 22.0437C9.55 22.0437 10 21.5939 10 21.044V5.04903C10 4.49921 9.55 4.04935 9 4.04935H2V22.0437H9ZM4 6.04872H8V20.0443H4V6.04872Z\"/>',\n    solid: '<path d=\"M17.67 31.1309C17.83 32.7604 19.19 33.99 20.85 33.99H20.98C22.64 33.99 24 32.6504 24 31.0009V21.9937H31C31.85 21.9937 32.7 21.6139 33.28 20.9741C33.81 20.3942 34.06 19.6545 33.99 18.8847L33.02 10.1774C32.99 9.87753 32.91 9.59761 32.8 9.3277L30.44 3.80942C29.97 2.70977 28.88 1.98999 27.68 1.98999H16.67C16.02 1.98999 15.39 2.19992 14.87 2.5898L12 4.73913V21.214C13.4 22.2737 17.14 25.6126 17.67 31.1209V31.1309ZM9 22.0437C9.55 22.0437 10 21.5939 10 21.044V5.04903C10 4.49921 9.55 4.04935 9 4.04935H2V22.0437H9Z\"/>'\n  })];\nexport { V as thumbsDownIcon, H as thumbsDownIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "V", "outline", "solid", "thumbsDownIcon", "thumbsDownIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/thumbs-down.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"thumbs-down\",V=[\"thumbs-down\",C({outline:'<path d=\"M17.67 31.1309C17.83 32.7604 19.19 33.99 20.85 33.99H20.98C22.64 33.99 24 32.6504 24 31.0009V21.9937H31C31.85 21.9937 32.7 21.6139 33.28 20.9741C33.81 20.3942 34.06 19.6545 33.99 18.8847L33.02 10.1774C32.99 9.87753 32.91 9.59761 32.8 9.3277L30.44 3.80942C29.97 2.70977 28.88 1.98999 27.68 1.98999H16.67C16.02 1.98999 15.39 2.19992 14.87 2.5898L12 4.73913V6.98843C12.22 6.98843 12.43 6.91845 12.6 6.78849L16.07 4.1893C16.24 4.05934 16.45 3.98937 16.67 3.98937H27.68C28.08 3.98937 28.44 4.22929 28.6 4.59917L30.96 10.1175C31 10.2074 31.02 10.2974 31.03 10.3974L31.99 19.0847C32.01 19.2846 31.94 19.4645 31.79 19.6245C31.59 19.8444 31.27 19.9944 30.98 19.9944H22.98C22.43 19.9944 21.98 20.4442 21.98 20.9941V31.0009C21.98 31.5608 21.53 31.9906 20.96 31.9906H20.83C20.22 31.9906 19.69 31.5308 19.64 30.9409C18.9 23.1734 12.77 19.3046 12.51 19.1446C12.35 19.0447 12.17 18.9947 11.98 18.9947V21.224C13.38 22.2836 17.12 25.6226 17.65 31.1309H17.67ZM9 22.0437C9.55 22.0437 10 21.5939 10 21.044V5.04903C10 4.49921 9.55 4.04935 9 4.04935H2V22.0437H9ZM4 6.04872H8V20.0443H4V6.04872Z\"/>',solid:'<path d=\"M17.67 31.1309C17.83 32.7604 19.19 33.99 20.85 33.99H20.98C22.64 33.99 24 32.6504 24 31.0009V21.9937H31C31.85 21.9937 32.7 21.6139 33.28 20.9741C33.81 20.3942 34.06 19.6545 33.99 18.8847L33.02 10.1774C32.99 9.87753 32.91 9.59761 32.8 9.3277L30.44 3.80942C29.97 2.70977 28.88 1.98999 27.68 1.98999H16.67C16.02 1.98999 15.39 2.19992 14.87 2.5898L12 4.73913V21.214C13.4 22.2737 17.14 25.6126 17.67 31.1209V31.1309ZM9 22.0437C9.55 22.0437 10 21.5939 10 21.044V5.04903C10 4.49921 9.55 4.04935 9 4.04935H2V22.0437H9Z\"/>'})];export{V as thumbsDownIcon,H as thumbsDownIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,aAAa;EAACC,CAAC,GAAC,CAAC,aAAa,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,6jCAA6jC;IAACC,KAAK,EAAC;EAA4gB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,cAAc,EAACJ,CAAC,IAAIK,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}