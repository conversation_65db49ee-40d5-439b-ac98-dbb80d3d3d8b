{"ast": null, "code": "import { renderIcon as H } from \"../icon.renderer.js\";\nconst V = \"suitcase2\",\n  C = [\"suitcase2\", H({\n    outline: '<path d=\"M30.72 32H5.28C3.47 32 2 30.53 2 28.72V11.28C2 9.47 3.47 8 5.28 8H30.72C32.53 8 34 9.47 34 11.28V28.72C34 30.53 32.53 32 30.72 32ZM5.28 10C4.57 10 4 10.58 4 11.28V28.72C4 29.43 4.58 30 5.28 30H30.72C31.43 30 32 29.42 32 28.72V11.28C32 10.57 31.42 10 30.72 10H5.28Z\"/><path d=\"M23 9H21V6H15V9H13V5C13 4.45 13.45 4 14 4H22C22.55 4 23 4.45 23 5V9Z\"/><path d=\"M10 9H8V30H10V9Z\"/><path d=\"M28 9H26V30H28V9Z\"/>',\n    solid: '<path d=\"M23 9H21V6H15V9H13V5C13 4.45 13.45 4 14 4H22C22.55 4 23 4.45 23 5V9Z\"/><path d=\"M26 8H10V32H26V8Z\"/><path d=\"M30.72 8H28V32H30.72C32.53 32 34 30.53 34 28.72V11.28C34 9.47 32.53 8 30.72 8Z\"/><path d=\"M8 8H5.28C3.47 8 2 9.47 2 11.28V28.72C2 30.53 3.47 32 5.28 32H8V8Z\"/>'\n  })];\nexport { C as suitcase2Icon, V as suitcase2IconName };", "map": {"version": 3, "names": ["renderIcon", "H", "V", "C", "outline", "solid", "suitcase2Icon", "suitcase2IconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/suitcase-2.js"], "sourcesContent": ["import{renderIcon as H}from\"../icon.renderer.js\";const V=\"suitcase2\",C=[\"suitcase2\",H({outline:'<path d=\"M30.72 32H5.28C3.47 32 2 30.53 2 28.72V11.28C2 9.47 3.47 8 5.28 8H30.72C32.53 8 34 9.47 34 11.28V28.72C34 30.53 32.53 32 30.72 32ZM5.28 10C4.57 10 4 10.58 4 11.28V28.72C4 29.43 4.58 30 5.28 30H30.72C31.43 30 32 29.42 32 28.72V11.28C32 10.57 31.42 10 30.72 10H5.28Z\"/><path d=\"M23 9H21V6H15V9H13V5C13 4.45 13.45 4 14 4H22C22.55 4 23 4.45 23 5V9Z\"/><path d=\"M10 9H8V30H10V9Z\"/><path d=\"M28 9H26V30H28V9Z\"/>',solid:'<path d=\"M23 9H21V6H15V9H13V5C13 4.45 13.45 4 14 4H22C22.55 4 23 4.45 23 5V9Z\"/><path d=\"M26 8H10V32H26V8Z\"/><path d=\"M30.72 8H28V32H30.72C32.53 32 34 30.53 34 28.72V11.28C34 9.47 32.53 8 30.72 8Z\"/><path d=\"M8 8H5.28C3.47 8 2 9.47 2 11.28V28.72C2 30.53 3.47 32 5.28 32H8V8Z\"/>'})];export{C as suitcase2Icon,V as suitcase2IconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,WAAW;EAACC,CAAC,GAAC,CAAC,WAAW,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,+ZAA+Z;IAACC,KAAK,EAAC;EAAuR,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,aAAa,EAACJ,CAAC,IAAIK,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}