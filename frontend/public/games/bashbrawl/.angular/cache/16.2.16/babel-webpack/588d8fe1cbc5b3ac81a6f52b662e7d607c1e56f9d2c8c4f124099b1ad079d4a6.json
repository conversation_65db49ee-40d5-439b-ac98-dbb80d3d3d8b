{"ast": null, "code": "import { isBrowser as e } from \"./environment.js\";\nimport { getAngularVersion as n, getAngularJSVersion as o, getReactVersion as t, getVueVersion as i } from \"./framework.js\";\nimport { browserFeatures as s } from \"./supports.js\";\nimport { LogService as r } from \"../services/log.service.js\";\nfunction w() {\n  e() && (window.CDS = window.CDS || {\n    _version: [],\n    _react: {\n      version: void 0\n    },\n    _supports: s.supports,\n    _isStateProxied: !1,\n    _state: {\n      focusTrapItems: [],\n      layerElements: [],\n      i18nRegistry: {},\n      elementRegistry: {},\n      iconRegistry: {},\n      motionRegistry: {}\n    },\n    environment: {\n      production: !1\n    },\n    getDetails: a,\n    logDetails: d\n  }, function () {\n    const e = \"6.16.0\";\n    window.CDS._version.indexOf(e) < 0 && (window.CDS._version.push(e), document.querySelector(\"body\")?.setAttribute(\"cds-version\", window.CDS._version.join(\" \"))), window.CDS._version.length > 1 && r.warn(\"Running more than one version of Clarity can cause unexpected issues. Please ensure only one version is loaded.\");\n  }(), window.CDS._isStateProxied || (window.CDS._isStateProxied = !0, window.CDS._state = new Proxy(window.CDS._state, {\n    set: (e, n, o) => {\n      const t = {\n        key: n,\n        prev: window.CDS._state[n],\n        current: o\n      };\n      return e[n] = o, document.dispatchEvent(new CustomEvent(\"CDS_STATE_UPDATE\", {\n        detail: t\n      })), !0;\n    }\n  })));\n}\nfunction a() {\n  return {\n    versions: window.CDS._version,\n    environment: window.CDS.environment,\n    userAgent: navigator.userAgent,\n    supports: window.CDS._supports,\n    angularVersion: n(!1),\n    angularJSVersion: o(!1),\n    reactVersion: t(!1),\n    vueVersion: i(!1),\n    state: {\n      ...window.CDS._state,\n      iconRegistry: Object.keys(window.CDS._state.iconRegistry),\n      motionRegistry: Object.keys(window.CDS._state.motionRegistry),\n      focusTrapRegistry: Object.keys(window.CDS._state.focusTrapItems.map(e => e.focusTrapId))\n    }\n  };\n}\nfunction d() {\n  r.log(JSON.stringify(a(), null, 2));\n}\nexport { w as setupCDSGlobal };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "e", "getAngularVersion", "n", "getAngularJSVersion", "o", "getReactVersion", "t", "getVueVersion", "i", "browserFeatures", "s", "LogService", "r", "w", "window", "CDS", "_version", "_react", "version", "_supports", "supports", "_isStateProxied", "_state", "focusTrapItems", "layerElements", "i18nRegistry", "elementRegistry", "iconRegistry", "motionRegistry", "environment", "production", "getDetails", "a", "logDetails", "d", "indexOf", "push", "document", "querySelector", "setAttribute", "join", "length", "warn", "Proxy", "set", "key", "prev", "current", "dispatchEvent", "CustomEvent", "detail", "versions", "userAgent", "navigator", "angularVersion", "angularJSVersion", "reactVersion", "vueVersion", "state", "Object", "keys", "focusTrapRegistry", "map", "focusTrapId", "log", "JSON", "stringify", "setupCDSGlobal"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/global.js"], "sourcesContent": ["import{isBrowser as e}from\"./environment.js\";import{getAngularVersion as n,getAngularJSVersion as o,getReactVersion as t,getVueVersion as i}from\"./framework.js\";import{browserFeatures as s}from\"./supports.js\";import{LogService as r}from\"../services/log.service.js\";function w(){e()&&(window.CDS=window.CDS||{_version:[],_react:{version:void 0},_supports:s.supports,_isStateProxied:!1,_state:{focusTrapItems:[],layerElements:[],i18nRegistry:{},elementRegistry:{},iconRegistry:{},motionRegistry:{}},environment:{production:!1},getDetails:a,logDetails:d},function(){const e=\"6.16.0\";window.CDS._version.indexOf(e)<0&&(window.CDS._version.push(e),document.querySelector(\"body\")?.setAttribute(\"cds-version\",window.CDS._version.join(\" \"))),window.CDS._version.length>1&&r.warn(\"Running more than one version of Clarity can cause unexpected issues. Please ensure only one version is loaded.\")}(),window.CDS._isStateProxied||(window.CDS._isStateProxied=!0,window.CDS._state=new Proxy(window.CDS._state,{set:(e,n,o)=>{const t={key:n,prev:window.CDS._state[n],current:o};return e[n]=o,document.dispatchEvent(new CustomEvent(\"CDS_STATE_UPDATE\",{detail:t})),!0}})))}function a(){return{versions:window.CDS._version,environment:window.CDS.environment,userAgent:navigator.userAgent,supports:window.CDS._supports,angularVersion:n(!1),angularJSVersion:o(!1),reactVersion:t(!1),vueVersion:i(!1),state:{...window.CDS._state,iconRegistry:Object.keys(window.CDS._state.iconRegistry),motionRegistry:Object.keys(window.CDS._state.motionRegistry),focusTrapRegistry:Object.keys(window.CDS._state.focusTrapItems.map((e=>e.focusTrapId)))}}}function d(){r.log(JSON.stringify(a(),null,2))}export{w as setupCDSGlobal};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAAOC,iBAAiB,IAAIC,CAAC,EAACC,mBAAmB,IAAIC,CAAC,EAACC,eAAe,IAAIC,CAAC,EAACC,aAAa,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,eAAe;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAASC,CAACA,CAAA,EAAE;EAACb,CAAC,CAAC,CAAC,KAAGc,MAAM,CAACC,GAAG,GAACD,MAAM,CAACC,GAAG,IAAE;IAACC,QAAQ,EAAC,EAAE;IAACC,MAAM,EAAC;MAACC,OAAO,EAAC,KAAK;IAAC,CAAC;IAACC,SAAS,EAACT,CAAC,CAACU,QAAQ;IAACC,eAAe,EAAC,CAAC,CAAC;IAACC,MAAM,EAAC;MAACC,cAAc,EAAC,EAAE;MAACC,aAAa,EAAC,EAAE;MAACC,YAAY,EAAC,CAAC,CAAC;MAACC,eAAe,EAAC,CAAC,CAAC;MAACC,YAAY,EAAC,CAAC,CAAC;MAACC,cAAc,EAAC,CAAC;IAAC,CAAC;IAACC,WAAW,EAAC;MAACC,UAAU,EAAC,CAAC;IAAC,CAAC;IAACC,UAAU,EAACC,CAAC;IAACC,UAAU,EAACC;EAAC,CAAC,EAAC,YAAU;IAAC,MAAMlC,CAAC,GAAC,QAAQ;IAACc,MAAM,CAACC,GAAG,CAACC,QAAQ,CAACmB,OAAO,CAACnC,CAAC,CAAC,GAAC,CAAC,KAAGc,MAAM,CAACC,GAAG,CAACC,QAAQ,CAACoB,IAAI,CAACpC,CAAC,CAAC,EAACqC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC,EAAEC,YAAY,CAAC,aAAa,EAACzB,MAAM,CAACC,GAAG,CAACC,QAAQ,CAACwB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAC1B,MAAM,CAACC,GAAG,CAACC,QAAQ,CAACyB,MAAM,GAAC,CAAC,IAAE7B,CAAC,CAAC8B,IAAI,CAAC,iHAAiH,CAAC;EAAA,CAAC,CAAC,CAAC,EAAC5B,MAAM,CAACC,GAAG,CAACM,eAAe,KAAGP,MAAM,CAACC,GAAG,CAACM,eAAe,GAAC,CAAC,CAAC,EAACP,MAAM,CAACC,GAAG,CAACO,MAAM,GAAC,IAAIqB,KAAK,CAAC7B,MAAM,CAACC,GAAG,CAACO,MAAM,EAAC;IAACsB,GAAG,EAACA,CAAC5C,CAAC,EAACE,CAAC,EAACE,CAAC,KAAG;MAAC,MAAME,CAAC,GAAC;QAACuC,GAAG,EAAC3C,CAAC;QAAC4C,IAAI,EAAChC,MAAM,CAACC,GAAG,CAACO,MAAM,CAACpB,CAAC,CAAC;QAAC6C,OAAO,EAAC3C;MAAC,CAAC;MAAC,OAAOJ,CAAC,CAACE,CAAC,CAAC,GAACE,CAAC,EAACiC,QAAQ,CAACW,aAAa,CAAC,IAAIC,WAAW,CAAC,kBAAkB,EAAC;QAACC,MAAM,EAAC5C;MAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;IAAA;EAAC,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,SAAS0B,CAACA,CAAA,EAAE;EAAC,OAAM;IAACmB,QAAQ,EAACrC,MAAM,CAACC,GAAG,CAACC,QAAQ;IAACa,WAAW,EAACf,MAAM,CAACC,GAAG,CAACc,WAAW;IAACuB,SAAS,EAACC,SAAS,CAACD,SAAS;IAAChC,QAAQ,EAACN,MAAM,CAACC,GAAG,CAACI,SAAS;IAACmC,cAAc,EAACpD,CAAC,CAAC,CAAC,CAAC,CAAC;IAACqD,gBAAgB,EAACnD,CAAC,CAAC,CAAC,CAAC,CAAC;IAACoD,YAAY,EAAClD,CAAC,CAAC,CAAC,CAAC,CAAC;IAACmD,UAAU,EAACjD,CAAC,CAAC,CAAC,CAAC,CAAC;IAACkD,KAAK,EAAC;MAAC,GAAG5C,MAAM,CAACC,GAAG,CAACO,MAAM;MAACK,YAAY,EAACgC,MAAM,CAACC,IAAI,CAAC9C,MAAM,CAACC,GAAG,CAACO,MAAM,CAACK,YAAY,CAAC;MAACC,cAAc,EAAC+B,MAAM,CAACC,IAAI,CAAC9C,MAAM,CAACC,GAAG,CAACO,MAAM,CAACM,cAAc,CAAC;MAACiC,iBAAiB,EAACF,MAAM,CAACC,IAAI,CAAC9C,MAAM,CAACC,GAAG,CAACO,MAAM,CAACC,cAAc,CAACuC,GAAG,CAAE9D,CAAC,IAAEA,CAAC,CAAC+D,WAAY,CAAC;IAAC;EAAC,CAAC;AAAA;AAAC,SAAS7B,CAACA,CAAA,EAAE;EAACtB,CAAC,CAACoD,GAAG,CAACC,IAAI,CAACC,SAAS,CAAClC,CAAC,CAAC,CAAC,EAAC,IAAI,EAAC,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOnB,CAAC,IAAIsD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}