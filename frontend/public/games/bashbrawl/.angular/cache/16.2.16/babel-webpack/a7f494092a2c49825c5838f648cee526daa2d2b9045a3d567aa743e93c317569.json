{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst o = \"inductor\",\n  r = [\"inductor\", C({\n    outline: '<path d=\"M33.0485 9H32.718C31.5562 7.01 30.2241 6 28.7518 6C27.2795 6 26.1477 6.96 25.2363 8.46C24.2848 6.96 23.093 6 21.6006 6C20.1584 6 18.9966 6.96 18.0851 8.46C17.1437 6.96 15.9418 6 14.4495 6C13.0072 6 11.8454 6.96 10.934 8.46C9.98247 6.96 8.79061 6 7.29828 6C5.80595 6 4.4939 7.01 3.33208 9H3.00156C2.4507 9 2 9.45 2 10C2 10.55 2.4507 11 3.00156 11H4.52394L4.80438 10.46C5.63568 8.87 6.51706 8 7.28826 8C8.18967 8 9.1011 8.97 9.86229 10.68C8.48013 14.36 7.9493 19.35 7.9493 22.45C7.9493 24.43 8.11956 26.13 8.45008 27.36C9.03099 29.54 10.0826 30 10.8538 30C12.0056 30 12.8369 29.07 13.3277 27.24C13.6782 25.93 13.8585 24.14 13.8585 22.07C13.8585 18.63 13.3077 14.06 12.0056 10.68C12.7568 8.98 13.6182 8 14.4294 8C15.3308 8 16.2423 8.97 17.0034 10.68C15.6113 14.36 15.0905 19.35 15.0905 22.45C15.0905 24.43 15.2607 26.13 15.5912 27.36C16.1721 29.54 17.2238 30 17.995 30C19.1468 30 19.9781 29.07 20.4689 27.24C20.8194 25.93 20.9997 24.15 20.9997 22.07C20.9997 18.63 20.4488 14.06 19.1468 10.68C19.898 8.98 20.7593 8 21.5706 8C22.472 8 23.3834 8.97 24.1446 10.68C22.7624 14.36 22.2316 19.35 22.2316 22.45C22.2316 24.43 22.4019 26.13 22.7324 27.36C23.3133 29.54 24.3649 30 25.1362 30C26.288 30 27.1192 29.07 27.61 27.24C27.9606 25.93 28.1408 24.14 28.1408 22.07C28.1408 18.63 27.59 14.06 26.288 10.68C27.0391 8.98 27.9005 8 28.7117 8C29.523 8 30.3643 8.88 31.1956 10.46L31.4761 11H32.9984C33.5493 11 34 10.55 34 10C34 9.45 33.5493 9 32.9984 9H33.0485ZM10.8739 27.97C9.91236 27.11 9.41158 20.27 10.954 13.97C12.4764 20.25 11.8554 27.13 10.8739 27.97ZM18.0351 27.97C17.0736 27.11 16.5628 20.27 18.1152 13.97C19.6376 20.25 19.0266 27.13 18.0351 27.97ZM25.1862 27.97C24.2247 27.11 23.7239 20.27 25.2664 13.97C26.7887 20.25 26.1678 27.13 25.1862 27.97Z\"/>'\n  })];\nexport { r as inductorIcon, o as inductorIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "o", "r", "outline", "inductorIcon", "inductorIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/inductor.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const o=\"inductor\",r=[\"inductor\",C({outline:'<path d=\"M33.0485 9H32.718C31.5562 7.01 30.2241 6 28.7518 6C27.2795 6 26.1477 6.96 25.2363 8.46C24.2848 6.96 23.093 6 21.6006 6C20.1584 6 18.9966 6.96 18.0851 8.46C17.1437 6.96 15.9418 6 14.4495 6C13.0072 6 11.8454 6.96 10.934 8.46C9.98247 6.96 8.79061 6 7.29828 6C5.80595 6 4.4939 7.01 3.33208 9H3.00156C2.4507 9 2 9.45 2 10C2 10.55 2.4507 11 3.00156 11H4.52394L4.80438 10.46C5.63568 8.87 6.51706 8 7.28826 8C8.18967 8 9.1011 8.97 9.86229 10.68C8.48013 14.36 7.9493 19.35 7.9493 22.45C7.9493 24.43 8.11956 26.13 8.45008 27.36C9.03099 29.54 10.0826 30 10.8538 30C12.0056 30 12.8369 29.07 13.3277 27.24C13.6782 25.93 13.8585 24.14 13.8585 22.07C13.8585 18.63 13.3077 14.06 12.0056 10.68C12.7568 8.98 13.6182 8 14.4294 8C15.3308 8 16.2423 8.97 17.0034 10.68C15.6113 14.36 15.0905 19.35 15.0905 22.45C15.0905 24.43 15.2607 26.13 15.5912 27.36C16.1721 29.54 17.2238 30 17.995 30C19.1468 30 19.9781 29.07 20.4689 27.24C20.8194 25.93 20.9997 24.15 20.9997 22.07C20.9997 18.63 20.4488 14.06 19.1468 10.68C19.898 8.98 20.7593 8 21.5706 8C22.472 8 23.3834 8.97 24.1446 10.68C22.7624 14.36 22.2316 19.35 22.2316 22.45C22.2316 24.43 22.4019 26.13 22.7324 27.36C23.3133 29.54 24.3649 30 25.1362 30C26.288 30 27.1192 29.07 27.61 27.24C27.9606 25.93 28.1408 24.14 28.1408 22.07C28.1408 18.63 27.59 14.06 26.288 10.68C27.0391 8.98 27.9005 8 28.7117 8C29.523 8 30.3643 8.88 31.1956 10.46L31.4761 11H32.9984C33.5493 11 34 10.55 34 10C34 9.45 33.5493 9 32.9984 9H33.0485ZM10.8739 27.97C9.91236 27.11 9.41158 20.27 10.954 13.97C12.4764 20.25 11.8554 27.13 10.8739 27.97ZM18.0351 27.97C17.0736 27.11 16.5628 20.27 18.1152 13.97C19.6376 20.25 19.0266 27.13 18.0351 27.97ZM25.1862 27.97C24.2247 27.11 23.7239 20.27 25.2664 13.97C26.7887 20.25 26.1678 27.13 25.1862 27.97Z\"/>'})];export{r as inductorIcon,o as inductorIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,UAAU;EAACC,CAAC,GAAC,CAAC,UAAU,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAA0tD,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,YAAY,EAACH,CAAC,IAAII,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}