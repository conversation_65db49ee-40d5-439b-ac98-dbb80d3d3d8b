{"ast": null, "code": "class s {\n  constructor(s, i) {\n    this.fn = s, this.subscriptions = i;\n  }\n  unsubscribe() {\n    const s = this.subscriptions.indexOf(this);\n    -1 !== s && this.subscriptions.splice(s, 1);\n  }\n}\nclass i {\n  constructor() {\n    this.subscriptions = [];\n  }\n  subscribe(i) {\n    const t = new s(i, this.subscriptions);\n    return this.subscriptions.push(t), t;\n  }\n  emit(s) {\n    this.subscriptions.forEach(i => i.fn(s));\n  }\n  toEventObservable() {\n    return this;\n  }\n}\nexport { i as EventSubject };", "map": {"version": 3, "names": ["s", "constructor", "i", "fn", "subscriptions", "unsubscribe", "indexOf", "splice", "subscribe", "t", "push", "emit", "for<PERSON>ach", "toEventObservable", "EventSubject"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/event-subject.js"], "sourcesContent": ["class s{constructor(s,i){this.fn=s,this.subscriptions=i}unsubscribe(){const s=this.subscriptions.indexOf(this);-1!==s&&this.subscriptions.splice(s,1)}}class i{constructor(){this.subscriptions=[]}subscribe(i){const t=new s(i,this.subscriptions);return this.subscriptions.push(t),t}emit(s){this.subscriptions.forEach((i=>i.fn(s)))}toEventObservable(){return this}}export{i as EventSubject};\n"], "mappings": "AAAA,MAAMA,CAAC;EAACC,WAAWA,CAACD,CAAC,EAACE,CAAC,EAAC;IAAC,IAAI,CAACC,EAAE,GAACH,CAAC,EAAC,IAAI,CAACI,aAAa,GAACF,CAAC;EAAA;EAACG,WAAWA,CAAA,EAAE;IAAC,MAAML,CAAC,GAAC,IAAI,CAACI,aAAa,CAACE,OAAO,CAAC,IAAI,CAAC;IAAC,CAAC,CAAC,KAAGN,CAAC,IAAE,IAAI,CAACI,aAAa,CAACG,MAAM,CAACP,CAAC,EAAC,CAAC,CAAC;EAAA;AAAC;AAAC,MAAME,CAAC;EAACD,WAAWA,CAAA,EAAE;IAAC,IAAI,CAACG,aAAa,GAAC,EAAE;EAAA;EAACI,SAASA,CAACN,CAAC,EAAC;IAAC,MAAMO,CAAC,GAAC,IAAIT,CAAC,CAACE,CAAC,EAAC,IAAI,CAACE,aAAa,CAAC;IAAC,OAAO,IAAI,CAACA,aAAa,CAACM,IAAI,CAACD,CAAC,CAAC,EAACA,CAAC;EAAA;EAACE,IAAIA,CAACX,CAAC,EAAC;IAAC,IAAI,CAACI,aAAa,CAACQ,OAAO,CAAEV,CAAC,IAAEA,CAAC,CAACC,EAAE,CAACH,CAAC,CAAE,CAAC;EAAA;EAACa,iBAAiBA,CAAA,EAAE;IAAC,OAAO,IAAI;EAAA;AAAC;AAAC,SAAOX,CAAC,IAAIY,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}