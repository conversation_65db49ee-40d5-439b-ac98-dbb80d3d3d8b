{"ast": null, "code": "import _curry1 from \"./internal/_curry1.js\";\n/**\n * Checks if the input value is `null` or `undefined`.\n *\n * @func\n * @memberOf R\n * @since v0.9.0\n * @category Type\n * @sig * -> Boolean\n * @param {*} x The value to test.\n * @return {<PERSON><PERSON>an} `true` if `x` is `undefined` or `null`, otherwise `false`.\n * @example\n *\n *      R.isNil(null); //=> true\n *      R.isNil(undefined); //=> true\n *      R.isNil(0); //=> false\n *      R.isNil([]); //=> false\n */\n\nvar isNil = /*#__PURE__*/\n_curry1(function isNil(x) {\n  return x == null;\n});\nexport default isNil;", "map": {"version": 3, "names": ["_curry1", "isNil", "x"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/isNil.js"], "sourcesContent": ["import _curry1 from \"./internal/_curry1.js\";\n/**\n * Checks if the input value is `null` or `undefined`.\n *\n * @func\n * @memberOf R\n * @since v0.9.0\n * @category Type\n * @sig * -> Boolean\n * @param {*} x The value to test.\n * @return {<PERSON><PERSON>an} `true` if `x` is `undefined` or `null`, otherwise `false`.\n * @example\n *\n *      R.isNil(null); //=> true\n *      R.isNil(undefined); //=> true\n *      R.isNil(0); //=> false\n *      R.isNil([]); //=> false\n */\n\nvar isNil =\n/*#__PURE__*/\n_curry1(function isNil(x) {\n  return x == null;\n});\n\nexport default isNil;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,uBAAuB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,KAAK,GACT;AACAD,OAAO,CAAC,SAASC,KAAKA,CAACC,CAAC,EAAE;EACxB,OAAOA,CAAC,IAAI,IAAI;AAClB,CAAC,CAAC;AAEF,eAAeD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}