{"ast": null, "code": "import { renderIcon as t } from \"../icon.renderer.js\";\nconst V = \"tablet\",\n  e = [\"tablet\", t({\n    outline: '<rect x=\"17\" y=\"29\" width=\"2\" height=\"2\"/><path d=\"M30,2H6A2,2,0,0,0,4,4V32a2,2,0,0,0,2,2H30a2,2,0,0,0,2-2V4A2,2,0,0,0,30,2Zm0,2V26.38H6V4ZM6,32V28H30v4Z\"/>',\n    solid: '<path d=\"M30,2H6A2,2,0,0,0,4,4V32a2,2,0,0,0,2,2H30a2,2,0,0,0,2-2V4A2,2,0,0,0,30,2ZM19,32H17V30h2ZM6,28V4H30V28Z\"/>'\n  })];\nexport { e as tabletIcon, V as tabletIconName };", "map": {"version": 3, "names": ["renderIcon", "t", "V", "e", "outline", "solid", "tabletIcon", "tabletIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/tablet.js"], "sourcesContent": ["import{renderIcon as t}from\"../icon.renderer.js\";const V=\"tablet\",e=[\"tablet\",t({outline:'<rect x=\"17\" y=\"29\" width=\"2\" height=\"2\"/><path d=\"M30,2H6A2,2,0,0,0,4,4V32a2,2,0,0,0,2,2H30a2,2,0,0,0,2-2V4A2,2,0,0,0,30,2Zm0,2V26.38H6V4ZM6,32V28H30v4Z\"/>',solid:'<path d=\"M30,2H6A2,2,0,0,0,4,4V32a2,2,0,0,0,2,2H30a2,2,0,0,0,2-2V4A2,2,0,0,0,30,2ZM19,32H17V30h2ZM6,28V4H30V28Z\"/>'})];export{e as tabletIcon,V as tabletIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,8JAA8J;IAACC,KAAK,EAAC;EAAoH,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,UAAU,EAACJ,CAAC,IAAIK,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}