{"ast": null, "code": "/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst NODE_MODE = false;\nconst global = NODE_MODE ? globalThis : window;\n/**\n * Whether the current browser supports `adoptedStyleSheets`.\n */\nexport const supportsAdoptingStyleSheets = global.ShadowRoot && (global.ShadyCSS === undefined || global.ShadyCSS.nativeShadow) && 'adoptedStyleSheets' in Document.prototype && 'replace' in CSSStyleSheet.prototype;\nconst constructionToken = Symbol();\nconst cssTagCache = new WeakMap();\n/**\n * A container for a string of CSS text, that may be used to create a CSSStyleSheet.\n *\n * CSSResult is the return value of `css`-tagged template literals and\n * `unsafeCSS()`. In order to ensure that CSSResults are only created via the\n * `css` tag and `unsafeCSS()`, CSSResult cannot be constructed directly.\n */\nexport class CSSResult {\n  constructor(cssText, strings, safeToken) {\n    // This property needs to remain unminified.\n    this['_$cssResult$'] = true;\n    if (safeToken !== constructionToken) {\n      throw new Error('CSSResult is not constructable. Use `unsafeCSS` or `css` instead.');\n    }\n    this.cssText = cssText;\n    this._strings = strings;\n  }\n  // This is a getter so that it's lazy. In practice, this means stylesheets\n  // are not created until the first element instance is made.\n  get styleSheet() {\n    // If `supportsAdoptingStyleSheets` is true then we assume CSSStyleSheet is\n    // constructable.\n    let styleSheet = this._styleSheet;\n    const strings = this._strings;\n    if (supportsAdoptingStyleSheets && styleSheet === undefined) {\n      const cacheable = strings !== undefined && strings.length === 1;\n      if (cacheable) {\n        styleSheet = cssTagCache.get(strings);\n      }\n      if (styleSheet === undefined) {\n        (this._styleSheet = styleSheet = new CSSStyleSheet()).replaceSync(this.cssText);\n        if (cacheable) {\n          cssTagCache.set(strings, styleSheet);\n        }\n      }\n    }\n    return styleSheet;\n  }\n  toString() {\n    return this.cssText;\n  }\n}\nconst textFromCSSResult = value => {\n  // This property needs to remain unminified.\n  if (value['_$cssResult$'] === true) {\n    return value.cssText;\n  } else if (typeof value === 'number') {\n    return value;\n  } else {\n    throw new Error(`Value passed to 'css' function must be a 'css' function result: ` + `${value}. Use 'unsafeCSS' to pass non-literal values, but take care ` + `to ensure page security.`);\n  }\n};\n/**\n * Wrap a value for interpolation in a {@linkcode css} tagged template literal.\n *\n * This is unsafe because untrusted CSS text can be used to phone home\n * or exfiltrate data to an attacker controlled site. Take care to only use\n * this with trusted input.\n */\nexport const unsafeCSS = value => new CSSResult(typeof value === 'string' ? value : String(value), undefined, constructionToken);\n/**\n * A template literal tag which can be used with LitElement's\n * {@linkcode LitElement.styles} property to set element styles.\n *\n * For security reasons, only literal string values and number may be used in\n * embedded expressions. To incorporate non-literal values {@linkcode unsafeCSS}\n * may be used inside an expression.\n */\nexport const css = (strings, ...values) => {\n  const cssText = strings.length === 1 ? strings[0] : values.reduce((acc, v, idx) => acc + textFromCSSResult(v) + strings[idx + 1], strings[0]);\n  return new CSSResult(cssText, strings, constructionToken);\n};\n/**\n * Applies the given styles to a `shadowRoot`. When Shadow DOM is\n * available but `adoptedStyleSheets` is not, styles are appended to the\n * `shadowRoot` to [mimic spec behavior](https://wicg.github.io/construct-stylesheets/#using-constructed-stylesheets).\n * Note, when shimming is used, any styles that are subsequently placed into\n * the shadowRoot should be placed *before* any shimmed adopted styles. This\n * will match spec behavior that gives adopted sheets precedence over styles in\n * shadowRoot.\n */\nexport const adoptStyles = (renderRoot, styles) => {\n  if (supportsAdoptingStyleSheets) {\n    renderRoot.adoptedStyleSheets = styles.map(s => s instanceof CSSStyleSheet ? s : s.styleSheet);\n  } else {\n    styles.forEach(s => {\n      const style = document.createElement('style');\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const nonce = global['litNonce'];\n      if (nonce !== undefined) {\n        style.setAttribute('nonce', nonce);\n      }\n      style.textContent = s.cssText;\n      renderRoot.appendChild(style);\n    });\n  }\n};\nconst cssResultFromStyleSheet = sheet => {\n  let cssText = '';\n  for (const rule of sheet.cssRules) {\n    cssText += rule.cssText;\n  }\n  return unsafeCSS(cssText);\n};\nexport const getCompatibleStyle = supportsAdoptingStyleSheets || NODE_MODE && global.CSSStyleSheet === undefined ? s => s : s => s instanceof CSSStyleSheet ? cssResultFromStyleSheet(s) : s;", "map": {"version": 3, "names": ["NODE_MODE", "global", "globalThis", "window", "supportsAdoptingStyleSheets", "ShadowRoot", "ShadyCSS", "undefined", "nativeShadow", "Document", "prototype", "CSSStyleSheet", "constructionToken", "Symbol", "cssTagCache", "WeakMap", "CSSResult", "constructor", "cssText", "strings", "safeToken", "Error", "_strings", "styleSheet", "_styleSheet", "cacheable", "length", "get", "replaceSync", "set", "toString", "textFromCSSResult", "value", "unsafeCSS", "String", "css", "values", "reduce", "acc", "v", "idx", "adoptStyles", "renderRoot", "styles", "adoptedStyleSheets", "map", "s", "for<PERSON>ach", "style", "document", "createElement", "nonce", "setAttribute", "textContent", "append<PERSON><PERSON><PERSON>", "cssResultFromStyleSheet", "sheet", "rule", "cssRules", "getCompatibleStyle"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@lit/reactive-element/development/css-tag.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst NODE_MODE = false;\nconst global = NODE_MODE ? globalThis : window;\n/**\n * Whether the current browser supports `adoptedStyleSheets`.\n */\nexport const supportsAdoptingStyleSheets = global.ShadowRoot &&\n    (global.ShadyCSS === undefined || global.ShadyCSS.nativeShadow) &&\n    'adoptedStyleSheets' in Document.prototype &&\n    'replace' in CSSStyleSheet.prototype;\nconst constructionToken = Symbol();\nconst cssTagCache = new WeakMap();\n/**\n * A container for a string of CSS text, that may be used to create a CSSStyleSheet.\n *\n * CSSResult is the return value of `css`-tagged template literals and\n * `unsafeCSS()`. In order to ensure that CSSResults are only created via the\n * `css` tag and `unsafeCSS()`, CSSResult cannot be constructed directly.\n */\nexport class CSSResult {\n    constructor(cssText, strings, safeToken) {\n        // This property needs to remain unminified.\n        this['_$cssResult$'] = true;\n        if (safeToken !== constructionToken) {\n            throw new Error('CSSResult is not constructable. Use `unsafeCSS` or `css` instead.');\n        }\n        this.cssText = cssText;\n        this._strings = strings;\n    }\n    // This is a getter so that it's lazy. In practice, this means stylesheets\n    // are not created until the first element instance is made.\n    get styleSheet() {\n        // If `supportsAdoptingStyleSheets` is true then we assume CSSStyleSheet is\n        // constructable.\n        let styleSheet = this._styleSheet;\n        const strings = this._strings;\n        if (supportsAdoptingStyleSheets && styleSheet === undefined) {\n            const cacheable = strings !== undefined && strings.length === 1;\n            if (cacheable) {\n                styleSheet = cssTagCache.get(strings);\n            }\n            if (styleSheet === undefined) {\n                (this._styleSheet = styleSheet = new CSSStyleSheet()).replaceSync(this.cssText);\n                if (cacheable) {\n                    cssTagCache.set(strings, styleSheet);\n                }\n            }\n        }\n        return styleSheet;\n    }\n    toString() {\n        return this.cssText;\n    }\n}\nconst textFromCSSResult = (value) => {\n    // This property needs to remain unminified.\n    if (value['_$cssResult$'] === true) {\n        return value.cssText;\n    }\n    else if (typeof value === 'number') {\n        return value;\n    }\n    else {\n        throw new Error(`Value passed to 'css' function must be a 'css' function result: ` +\n            `${value}. Use 'unsafeCSS' to pass non-literal values, but take care ` +\n            `to ensure page security.`);\n    }\n};\n/**\n * Wrap a value for interpolation in a {@linkcode css} tagged template literal.\n *\n * This is unsafe because untrusted CSS text can be used to phone home\n * or exfiltrate data to an attacker controlled site. Take care to only use\n * this with trusted input.\n */\nexport const unsafeCSS = (value) => new CSSResult(typeof value === 'string' ? value : String(value), undefined, constructionToken);\n/**\n * A template literal tag which can be used with LitElement's\n * {@linkcode LitElement.styles} property to set element styles.\n *\n * For security reasons, only literal string values and number may be used in\n * embedded expressions. To incorporate non-literal values {@linkcode unsafeCSS}\n * may be used inside an expression.\n */\nexport const css = (strings, ...values) => {\n    const cssText = strings.length === 1\n        ? strings[0]\n        : values.reduce((acc, v, idx) => acc + textFromCSSResult(v) + strings[idx + 1], strings[0]);\n    return new CSSResult(cssText, strings, constructionToken);\n};\n/**\n * Applies the given styles to a `shadowRoot`. When Shadow DOM is\n * available but `adoptedStyleSheets` is not, styles are appended to the\n * `shadowRoot` to [mimic spec behavior](https://wicg.github.io/construct-stylesheets/#using-constructed-stylesheets).\n * Note, when shimming is used, any styles that are subsequently placed into\n * the shadowRoot should be placed *before* any shimmed adopted styles. This\n * will match spec behavior that gives adopted sheets precedence over styles in\n * shadowRoot.\n */\nexport const adoptStyles = (renderRoot, styles) => {\n    if (supportsAdoptingStyleSheets) {\n        renderRoot.adoptedStyleSheets = styles.map((s) => s instanceof CSSStyleSheet ? s : s.styleSheet);\n    }\n    else {\n        styles.forEach((s) => {\n            const style = document.createElement('style');\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const nonce = global['litNonce'];\n            if (nonce !== undefined) {\n                style.setAttribute('nonce', nonce);\n            }\n            style.textContent = s.cssText;\n            renderRoot.appendChild(style);\n        });\n    }\n};\nconst cssResultFromStyleSheet = (sheet) => {\n    let cssText = '';\n    for (const rule of sheet.cssRules) {\n        cssText += rule.cssText;\n    }\n    return unsafeCSS(cssText);\n};\nexport const getCompatibleStyle = supportsAdoptingStyleSheets ||\n    (NODE_MODE && global.CSSStyleSheet === undefined)\n    ? (s) => s\n    : (s) => s instanceof CSSStyleSheet ? cssResultFromStyleSheet(s) : s;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,MAAMA,SAAS,GAAG,KAAK;AACvB,MAAMC,MAAM,GAAGD,SAAS,GAAGE,UAAU,GAAGC,MAAM;AAC9C;AACA;AACA;AACA,OAAO,MAAMC,2BAA2B,GAAGH,MAAM,CAACI,UAAU,KACvDJ,MAAM,CAACK,QAAQ,KAAKC,SAAS,IAAIN,MAAM,CAACK,QAAQ,CAACE,YAAY,CAAC,IAC/D,oBAAoB,IAAIC,QAAQ,CAACC,SAAS,IAC1C,SAAS,IAAIC,aAAa,CAACD,SAAS;AACxC,MAAME,iBAAiB,GAAGC,MAAM,CAAC,CAAC;AAClC,MAAMC,WAAW,GAAG,IAAIC,OAAO,CAAC,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,SAAS,CAAC;EACnBC,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAEC,SAAS,EAAE;IACrC;IACA,IAAI,CAAC,cAAc,CAAC,GAAG,IAAI;IAC3B,IAAIA,SAAS,KAAKR,iBAAiB,EAAE;MACjC,MAAM,IAAIS,KAAK,CAAC,mEAAmE,CAAC;IACxF;IACA,IAAI,CAACH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACI,QAAQ,GAAGH,OAAO;EAC3B;EACA;EACA;EACA,IAAII,UAAUA,CAAA,EAAG;IACb;IACA;IACA,IAAIA,UAAU,GAAG,IAAI,CAACC,WAAW;IACjC,MAAML,OAAO,GAAG,IAAI,CAACG,QAAQ;IAC7B,IAAIlB,2BAA2B,IAAImB,UAAU,KAAKhB,SAAS,EAAE;MACzD,MAAMkB,SAAS,GAAGN,OAAO,KAAKZ,SAAS,IAAIY,OAAO,CAACO,MAAM,KAAK,CAAC;MAC/D,IAAID,SAAS,EAAE;QACXF,UAAU,GAAGT,WAAW,CAACa,GAAG,CAACR,OAAO,CAAC;MACzC;MACA,IAAII,UAAU,KAAKhB,SAAS,EAAE;QAC1B,CAAC,IAAI,CAACiB,WAAW,GAAGD,UAAU,GAAG,IAAIZ,aAAa,CAAC,CAAC,EAAEiB,WAAW,CAAC,IAAI,CAACV,OAAO,CAAC;QAC/E,IAAIO,SAAS,EAAE;UACXX,WAAW,CAACe,GAAG,CAACV,OAAO,EAAEI,UAAU,CAAC;QACxC;MACJ;IACJ;IACA,OAAOA,UAAU;EACrB;EACAO,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACZ,OAAO;EACvB;AACJ;AACA,MAAMa,iBAAiB,GAAIC,KAAK,IAAK;EACjC;EACA,IAAIA,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;IAChC,OAAOA,KAAK,CAACd,OAAO;EACxB,CAAC,MACI,IAAI,OAAOc,KAAK,KAAK,QAAQ,EAAE;IAChC,OAAOA,KAAK;EAChB,CAAC,MACI;IACD,MAAM,IAAIX,KAAK,CAAE,kEAAiE,GAC7E,GAAEW,KAAM,8DAA6D,GACrE,0BAAyB,CAAC;EACnC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,SAAS,GAAID,KAAK,IAAK,IAAIhB,SAAS,CAAC,OAAOgB,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGE,MAAM,CAACF,KAAK,CAAC,EAAEzB,SAAS,EAAEK,iBAAiB,CAAC;AAClI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMuB,GAAG,GAAGA,CAAChB,OAAO,EAAE,GAAGiB,MAAM,KAAK;EACvC,MAAMlB,OAAO,GAAGC,OAAO,CAACO,MAAM,KAAK,CAAC,GAC9BP,OAAO,CAAC,CAAC,CAAC,GACViB,MAAM,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,EAAEC,GAAG,KAAKF,GAAG,GAAGP,iBAAiB,CAACQ,CAAC,CAAC,GAAGpB,OAAO,CAACqB,GAAG,GAAG,CAAC,CAAC,EAAErB,OAAO,CAAC,CAAC,CAAC,CAAC;EAC/F,OAAO,IAAIH,SAAS,CAACE,OAAO,EAAEC,OAAO,EAAEP,iBAAiB,CAAC;AAC7D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM6B,WAAW,GAAGA,CAACC,UAAU,EAAEC,MAAM,KAAK;EAC/C,IAAIvC,2BAA2B,EAAE;IAC7BsC,UAAU,CAACE,kBAAkB,GAAGD,MAAM,CAACE,GAAG,CAAEC,CAAC,IAAKA,CAAC,YAAYnC,aAAa,GAAGmC,CAAC,GAAGA,CAAC,CAACvB,UAAU,CAAC;EACpG,CAAC,MACI;IACDoB,MAAM,CAACI,OAAO,CAAED,CAAC,IAAK;MAClB,MAAME,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MAC7C;MACA,MAAMC,KAAK,GAAGlD,MAAM,CAAC,UAAU,CAAC;MAChC,IAAIkD,KAAK,KAAK5C,SAAS,EAAE;QACrByC,KAAK,CAACI,YAAY,CAAC,OAAO,EAAED,KAAK,CAAC;MACtC;MACAH,KAAK,CAACK,WAAW,GAAGP,CAAC,CAAC5B,OAAO;MAC7BwB,UAAU,CAACY,WAAW,CAACN,KAAK,CAAC;IACjC,CAAC,CAAC;EACN;AACJ,CAAC;AACD,MAAMO,uBAAuB,GAAIC,KAAK,IAAK;EACvC,IAAItC,OAAO,GAAG,EAAE;EAChB,KAAK,MAAMuC,IAAI,IAAID,KAAK,CAACE,QAAQ,EAAE;IAC/BxC,OAAO,IAAIuC,IAAI,CAACvC,OAAO;EAC3B;EACA,OAAOe,SAAS,CAACf,OAAO,CAAC;AAC7B,CAAC;AACD,OAAO,MAAMyC,kBAAkB,GAAGvD,2BAA2B,IACxDJ,SAAS,IAAIC,MAAM,CAACU,aAAa,KAAKJ,SAAU,GAC9CuC,CAAC,IAAKA,CAAC,GACPA,CAAC,IAAKA,CAAC,YAAYnC,aAAa,GAAG4C,uBAAuB,CAACT,CAAC,CAAC,GAAGA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}