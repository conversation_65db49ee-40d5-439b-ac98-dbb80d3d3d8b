{"ast": null, "code": "import { renderIcon as c } from \"../icon.renderer.js\";\nconst h = \"microphone\",\n  o = [\"microphone\", c({\n    outline: '<path d=\"M18,24c3.9,0,7-3.1,7-7V9c0-3.9-3.1-7-7-7s-7,3.1-7,7v8C11,20.9,14.1,24,18,24z M13,9c0-2.8,2.2-5,5-5s5,2.2,5,5v8c0,2.8-2.2,5-5,5s-5-2.2-5-5V9z\"/><path d=\"M30,17h-2c0,5.5-4.5,10-10,10S8,22.5,8,17H6c0,6.3,4.8,11.4,11,11.9V32h-3c-0.6,0-1,0.4-1,1s0.4,1,1,1h8c0.6,0,1-0.4,1-1s-0.4-1-1-1h-3v-3.1C25.2,28.4,30,23.3,30,17z\"/>',\n    solid: '<path d=\"M18,24c3.9,0,7-3.1,7-7V9c0-3.9-3.1-7-7-7s-7,3.1-7,7v8C11,20.9,14.1,24,18,24z\"/><path d=\"M30,17h-2c0,5.5-4.5,10-10,10S8,22.5,8,17H6c0,6.3,4.8,11.4,11,11.9V32h-3c-0.6,0-1,0.4-1,1s0.4,1,1,1h8c0.6,0,1-0.4,1-1s-0.4-1-1-1h-3v-3.1C25.2,28.4,30,23.3,30,17z\"/>'\n  })];\nexport { o as microphoneIcon, h as microphoneIconName };", "map": {"version": 3, "names": ["renderIcon", "c", "h", "o", "outline", "solid", "microphoneIcon", "microphoneIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/microphone.js"], "sourcesContent": ["import{renderIcon as c}from\"../icon.renderer.js\";const h=\"microphone\",o=[\"microphone\",c({outline:'<path d=\"M18,24c3.9,0,7-3.1,7-7V9c0-3.9-3.1-7-7-7s-7,3.1-7,7v8C11,20.9,14.1,24,18,24z M13,9c0-2.8,2.2-5,5-5s5,2.2,5,5v8c0,2.8-2.2,5-5,5s-5-2.2-5-5V9z\"/><path d=\"M30,17h-2c0,5.5-4.5,10-10,10S8,22.5,8,17H6c0,6.3,4.8,11.4,11,11.9V32h-3c-0.6,0-1,0.4-1,1s0.4,1,1,1h8c0.6,0,1-0.4,1-1s-0.4-1-1-1h-3v-3.1C25.2,28.4,30,23.3,30,17z\"/>',solid:'<path d=\"M18,24c3.9,0,7-3.1,7-7V9c0-3.9-3.1-7-7-7s-7,3.1-7,7v8C11,20.9,14.1,24,18,24z\"/><path d=\"M30,17h-2c0,5.5-4.5,10-10,10S8,22.5,8,17H6c0,6.3,4.8,11.4,11,11.9V32h-3c-0.6,0-1,0.4-1,1s0.4,1,1,1h8c0.6,0,1-0.4,1-1s-0.4-1-1-1h-3v-3.1C25.2,28.4,30,23.3,30,17z\"/>'})];export{o as microphoneIcon,h as microphoneIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,YAAY;EAACC,CAAC,GAAC,CAAC,YAAY,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,sUAAsU;IAACC,KAAK,EAAC;EAAsQ,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,cAAc,EAACJ,CAAC,IAAIK,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}