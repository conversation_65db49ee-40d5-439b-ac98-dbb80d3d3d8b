{"ast": null, "code": "export default {\n  background: '#292b2e',\n  foreground: '#ffffff',\n  cursor: '#ffffff',\n  cursorAccent: '#505459',\n  black: '#2e3436',\n  brightBlack: '#555753',\n  red: '#cc0000',\n  brightRed: '#ef2929',\n  green: '#4e9a06',\n  brightGreen: '#8ae234',\n  yellow: '#c4a000',\n  brightYellow: '#fce94f',\n  blue: '#3465a4',\n  brightBlue: '#729fcf',\n  magenta: '#75507b',\n  brightMagenta: '#ad7fa8',\n  cyan: '#06989a',\n  brightCyan: '#34e2e2',\n  white: '#d3d7cf',\n  brightWhite: '#eeeeec'\n};", "map": {"version": 3, "names": ["background", "foreground", "cursor", "cursorAccent", "black", "brightBlack", "red", "brightRed", "green", "bright<PERSON><PERSON>", "yellow", "<PERSON><PERSON><PERSON><PERSON>", "blue", "brightBlue", "magenta", "brightMagenta", "cyan", "bright<PERSON>yan", "white", "bright<PERSON><PERSON>e"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/src/app/terminals/terminal-themes/Hobbyfarm_Default.ts"], "sourcesContent": ["export default {\n  background: '#292b2e',\n  foreground: '#ffffff',\n  cursor: '#ffffff',\n  cursorAccent: '#505459',\n\n  black: '#2e3436',\n  brightBlack: '#555753',\n\n  red: '#cc0000',\n  brightRed: '#ef2929',\n\n  green: '#4e9a06',\n  brightGreen: '#8ae234',\n\n  yellow: '#c4a000',\n  brightYellow: '#fce94f',\n\n  blue: '#3465a4',\n  brightBlue: '#729fcf',\n\n  magenta: '#75507b',\n  brightMagenta: '#ad7fa8',\n\n  cyan: '#06989a',\n  brightCyan: '#34e2e2',\n\n  white: '#d3d7cf',\n  brightWhite: '#eeeeec',\n};\n"], "mappings": "AAAA,eAAe;EACbA,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,SAAS;EACrBC,MAAM,EAAE,SAAS;EACjBC,YAAY,EAAE,SAAS;EAEvBC,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE,SAAS;EAEtBC,GAAG,EAAE,SAAS;EACdC,SAAS,EAAE,SAAS;EAEpBC,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE,SAAS;EAEtBC,MAAM,EAAE,SAAS;EACjBC,YAAY,EAAE,SAAS;EAEvBC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,SAAS;EAErBC,OAAO,EAAE,SAAS;EAClBC,aAAa,EAAE,SAAS;EAExBC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,SAAS;EAErBC,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE;CACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}