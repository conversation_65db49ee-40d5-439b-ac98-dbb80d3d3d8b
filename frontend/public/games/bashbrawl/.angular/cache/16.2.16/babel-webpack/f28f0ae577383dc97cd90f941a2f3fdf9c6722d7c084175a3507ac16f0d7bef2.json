{"ast": null, "code": "/** Taken from https://github.com/CommandLineHeroes/clh-bash/tree/master/assets/cmds **/\nexport const htmlConfig = {\n  name: 'HTML5',\n  cmds: [['html'], ['head'], ['title'], ['base'], ['link'], ['meta'], ['style'], ['body'], ['article'], ['section'], ['nav'], ['aside'], ['h1'], ['h2'], ['h3'], ['h4'], ['h5'], ['h6'], ['header'], ['footer'], ['p'], ['address'], ['hr'], ['pre'], ['blockquote'], ['ol'], ['ul'], ['li'], ['dl'], ['dt'], ['dd'], ['figure'], ['figcaption'], ['main'], ['div'], ['a'], ['em'], ['strong'], ['small'], ['s'], ['cite'], ['q'], ['dfn'], ['abbr'], ['ruby'], ['rb'], ['rt'], ['rtc'], ['rp'], ['data'], ['time'], ['code'], ['var'], ['samp'], ['kbd'], ['sub'], ['sup'], ['i'], ['b'], ['u'], ['mark'], ['bdi'], ['bdo'], ['span'], ['br'], ['wbr'], ['ins'], ['del'], ['picture'], ['source'], ['img'], ['iframe'], ['embed'], ['object'], ['param'], ['video'], ['audio'], ['track'], ['map'], ['area'], ['table'], ['caption'], ['colgroup'], ['col'], ['tbody'], ['thead'], ['tfoot'], ['tr'], ['td'], ['th'], ['form'], ['label'], ['input'], ['button'], ['select'], ['datalist'], ['optgroup'], ['option'], ['textarea'], ['output'], ['progress'], ['meter'], ['fieldset'], ['legend'], ['details'], ['summary'], ['dialog'], ['script'], ['noscript'], ['template'], ['canvas'], ['slot'], ['hr'], ['fieldset'], ['legend'], ['button'], ['details'], ['summary'], ['marquee'], ['meter'], ['progress'], ['select'], ['textarea'], ['marquee']]\n};", "map": {"version": 3, "names": ["htmlConfig", "name", "cmds"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/src/app/terminals/bashbrawl/languages/html.ts"], "sourcesContent": ["/** Taken from https://github.com/CommandLineHeroes/clh-bash/tree/master/assets/cmds **/\n\nimport { LanguageConfig } from './language-config.interface';\nexport const htmlConfig: LanguageConfig = {\n  name: 'HTML5',\n  cmds: [\n    ['html'],\n    ['head'],\n    ['title'],\n    ['base'],\n    ['link'],\n    ['meta'],\n    ['style'],\n    ['body'],\n    ['article'],\n    ['section'],\n    ['nav'],\n    ['aside'],\n    ['h1'],\n    ['h2'],\n    ['h3'],\n    ['h4'],\n    ['h5'],\n    ['h6'],\n    ['header'],\n    ['footer'],\n    ['p'],\n    ['address'],\n    ['hr'],\n    ['pre'],\n    ['blockquote'],\n    ['ol'],\n    ['ul'],\n    ['li'],\n    ['dl'],\n    ['dt'],\n    ['dd'],\n    ['figure'],\n    ['figcaption'],\n    ['main'],\n    ['div'],\n    ['a'],\n    ['em'],\n    ['strong'],\n    ['small'],\n    ['s'],\n    ['cite'],\n    ['q'],\n    ['dfn'],\n    ['abbr'],\n    ['ruby'],\n    ['rb'],\n    ['rt'],\n    ['rtc'],\n    ['rp'],\n    ['data'],\n    ['time'],\n    ['code'],\n    ['var'],\n    ['samp'],\n    ['kbd'],\n    ['sub'],\n    ['sup'],\n    ['i'],\n    ['b'],\n    ['u'],\n    ['mark'],\n    ['bdi'],\n    ['bdo'],\n    ['span'],\n    ['br'],\n    ['wbr'],\n    ['ins'],\n    ['del'],\n    ['picture'],\n    ['source'],\n    ['img'],\n    ['iframe'],\n    ['embed'],\n    ['object'],\n    ['param'],\n    ['video'],\n    ['audio'],\n    ['track'],\n    ['map'],\n    ['area'],\n    ['table'],\n    ['caption'],\n    ['colgroup'],\n    ['col'],\n    ['tbody'],\n    ['thead'],\n    ['tfoot'],\n    ['tr'],\n    ['td'],\n    ['th'],\n    ['form'],\n    ['label'],\n    ['input'],\n    ['button'],\n    ['select'],\n    ['datalist'],\n    ['optgroup'],\n    ['option'],\n    ['textarea'],\n    ['output'],\n    ['progress'],\n    ['meter'],\n    ['fieldset'],\n    ['legend'],\n    ['details'],\n    ['summary'],\n    ['dialog'],\n    ['script'],\n    ['noscript'],\n    ['template'],\n    ['canvas'],\n    ['slot'],\n    ['hr'],\n    ['fieldset'],\n    ['legend'],\n    ['button'],\n    ['details'],\n    ['summary'],\n    ['marquee'],\n    ['meter'],\n    ['progress'],\n    ['select'],\n    ['textarea'],\n    ['marquee'],\n  ],\n};\n"], "mappings": "AAAA;AAGA,OAAO,MAAMA,UAAU,GAAmB;EACxCC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,CACJ,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,OAAO,CAAC,EACT,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,OAAO,CAAC,EACT,CAAC,MAAM,CAAC,EA<PERSON>,CAAC,SAAS,CAAC,EACX,CAAC,SAAS,CAAC,EACX,CAAC,KAAK,CAAC,EACP,CAAC,OAAO,CAAC,EACT,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,QAAQ,CAAC,EACV,CAAC,QAAQ,CAAC,EACV,CAAC,GAAG,CAAC,EACL,CAAC,SAAS,CAAC,EACX,CAAC,IAAI,CAAC,EACN,CAAC,KAAK,CAAC,EACP,CAAC,YAAY,CAAC,EACd,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,QAAQ,CAAC,EACV,CAAC,YAAY,CAAC,EACd,CAAC,MAAM,CAAC,EACR,CAAC,KAAK,CAAC,EACP,CAAC,GAAG,CAAC,EACL,CAAC,IAAI,CAAC,EACN,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,GAAG,CAAC,EACL,CAAC,MAAM,CAAC,EACR,CAAC,GAAG,CAAC,EACL,CAAC,KAAK,CAAC,EACP,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,KAAK,CAAC,EACP,CAAC,IAAI,CAAC,EACN,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,KAAK,CAAC,EACP,CAAC,MAAM,CAAC,EACR,CAAC,KAAK,CAAC,EACP,CAAC,KAAK,CAAC,EACP,CAAC,KAAK,CAAC,EACP,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC,EACL,CAAC,MAAM,CAAC,EACR,CAAC,KAAK,CAAC,EACP,CAAC,KAAK,CAAC,EACP,CAAC,MAAM,CAAC,EACR,CAAC,IAAI,CAAC,EACN,CAAC,KAAK,CAAC,EACP,CAAC,KAAK,CAAC,EACP,CAAC,KAAK,CAAC,EACP,CAAC,SAAS,CAAC,EACX,CAAC,QAAQ,CAAC,EACV,CAAC,KAAK,CAAC,EACP,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,KAAK,CAAC,EACP,CAAC,MAAM,CAAC,EACR,CAAC,OAAO,CAAC,EACT,CAAC,SAAS,CAAC,EACX,CAAC,UAAU,CAAC,EACZ,CAAC,KAAK,CAAC,EACP,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,MAAM,CAAC,EACR,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,QAAQ,CAAC,EACV,CAAC,QAAQ,CAAC,EACV,CAAC,UAAU,CAAC,EACZ,CAAC,UAAU,CAAC,EACZ,CAAC,QAAQ,CAAC,EACV,CAAC,UAAU,CAAC,EACZ,CAAC,QAAQ,CAAC,EACV,CAAC,UAAU,CAAC,EACZ,CAAC,OAAO,CAAC,EACT,CAAC,UAAU,CAAC,EACZ,CAAC,QAAQ,CAAC,EACV,CAAC,SAAS,CAAC,EACX,CAAC,SAAS,CAAC,EACX,CAAC,QAAQ,CAAC,EACV,CAAC,QAAQ,CAAC,EACV,CAAC,UAAU,CAAC,EACZ,CAAC,UAAU,CAAC,EACZ,CAAC,QAAQ,CAAC,EACV,CAAC,MAAM,CAAC,EACR,CAAC,IAAI,CAAC,EACN,CAAC,UAAU,CAAC,EACZ,CAAC,QAAQ,CAAC,EACV,CAAC,QAAQ,CAAC,EACV,CAAC,SAAS,CAAC,EACX,CAAC,SAAS,CAAC,EACX,CAAC,SAAS,CAAC,EACX,CAAC,OAAO,CAAC,EACT,CAAC,UAAU,CAAC,EACZ,CAAC,QAAQ,CAAC,EACV,CAAC,UAAU,CAAC,EACZ,CAAC,SAAS,CAAC;CAEd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}