{"ast": null, "code": "import { renderIcon as L } from \"../icon.renderer.js\";\nconst C = \"cursor-arrow\",\n  r = [\"cursor-arrow\", L({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M13.7111 31.3497C13.8578 31.7419 14.2335 32.0014 14.6527 32H14.6727C15.1039 31.9883 15.4793 31.7021 15.6042 31.2897L18.2085 22.836L26.5521 31.1696C26.7411 31.3569 26.997 31.4613 27.2633 31.4598C27.5295 31.4613 27.7854 31.3569 27.9744 31.1696L31.2398 27.9082C31.6281 27.518 31.6281 26.8878 31.2398 26.4976L22.9262 18.194L31.2999 15.6228C31.7073 15.4944 31.9883 15.1219 31.9996 14.6952C32.0109 14.2686 31.75 13.8818 31.3499 13.732L5.30739 4.04777C4.946 3.93208 4.55019 4.03031 4.28505 4.30149C4.01991 4.57267 3.93097 4.97022 4.05534 5.32833L13.7111 31.3497ZM18.4589 20.2849C18.2698 20.0976 18.0139 19.9932 17.7477 19.9947L17.4973 20.0047C17.1573 20.0914 16.8875 20.3495 16.7861 20.685L14.5625 27.8882L6.65958 6.6789L27.8742 14.5724L20.7326 16.7733C20.3985 16.8777 20.1432 17.1488 20.0592 17.4882C19.9753 17.8277 20.0749 18.1863 20.3219 18.434L29.0962 27.1979L27.2632 29.0787L18.4589 20.2849Z\"/>',\n    solid: '<path d=\"M31.2277 13.7177L5.38953 4.06559C5.01327 3.92425 4.58936 4.0155 4.30413 4.29923C4.01891 4.58296 3.92459 5.00723 4.06265 5.38554L13.7417 31.3307C13.8957 31.7427 14.2934 32.0113 14.732 31.9996C15.1707 31.988 15.5537 31.6987 15.6857 31.2792L18.4526 22.2561L27.8334 31.4339C28.2346 31.8337 28.8825 31.8337 29.2837 31.4339L31.7009 29.0105C32.0997 28.6083 32.0997 27.9587 31.7009 27.5565L22.351 18.42L31.2277 15.6667C31.642 15.5229 31.9199 15.1317 31.9199 14.6922C31.9199 14.2527 31.642 13.8615 31.2277 13.7177Z\"/>'\n  })];\nexport { r as cursorArrowIcon, C as cursorArrowIconName };", "map": {"version": 3, "names": ["renderIcon", "L", "C", "r", "outline", "solid", "cursorArrowIcon", "cursorArrowIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/cursor-arrow.js"], "sourcesContent": ["import{renderIcon as L}from\"../icon.renderer.js\";const C=\"cursor-arrow\",r=[\"cursor-arrow\",L({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M13.7111 31.3497C13.8578 31.7419 14.2335 32.0014 14.6527 32H14.6727C15.1039 31.9883 15.4793 31.7021 15.6042 31.2897L18.2085 22.836L26.5521 31.1696C26.7411 31.3569 26.997 31.4613 27.2633 31.4598C27.5295 31.4613 27.7854 31.3569 27.9744 31.1696L31.2398 27.9082C31.6281 27.518 31.6281 26.8878 31.2398 26.4976L22.9262 18.194L31.2999 15.6228C31.7073 15.4944 31.9883 15.1219 31.9996 14.6952C32.0109 14.2686 31.75 13.8818 31.3499 13.732L5.30739 4.04777C4.946 3.93208 4.55019 4.03031 4.28505 4.30149C4.01991 4.57267 3.93097 4.97022 4.05534 5.32833L13.7111 31.3497ZM18.4589 20.2849C18.2698 20.0976 18.0139 19.9932 17.7477 19.9947L17.4973 20.0047C17.1573 20.0914 16.8875 20.3495 16.7861 20.685L14.5625 27.8882L6.65958 6.6789L27.8742 14.5724L20.7326 16.7733C20.3985 16.8777 20.1432 17.1488 20.0592 17.4882C19.9753 17.8277 20.0749 18.1863 20.3219 18.434L29.0962 27.1979L27.2632 29.0787L18.4589 20.2849Z\"/>',solid:'<path d=\"M31.2277 13.7177L5.38953 4.06559C5.01327 3.92425 4.58936 4.0155 4.30413 4.29923C4.01891 4.58296 3.92459 5.00723 4.06265 5.38554L13.7417 31.3307C13.8957 31.7427 14.2934 32.0113 14.732 31.9996C15.1707 31.988 15.5537 31.6987 15.6857 31.2792L18.4526 22.2561L27.8334 31.4339C28.2346 31.8337 28.8825 31.8337 29.2837 31.4339L31.7009 29.0105C32.0997 28.6083 32.0997 27.9587 31.7009 27.5565L22.351 18.42L31.2277 15.6667C31.642 15.5229 31.9199 15.1317 31.9199 14.6922C31.9199 14.2527 31.642 13.8615 31.2277 13.7177Z\"/>'})];export{r as cursorArrowIcon,C as cursorArrowIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,cAAc;EAACC,CAAC,GAAC,CAAC,cAAc,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,+6BAA+6B;IAACC,KAAK,EAAC;EAAugB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,eAAe,EAACJ,CAAC,IAAIK,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}