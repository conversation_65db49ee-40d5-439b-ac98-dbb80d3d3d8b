{"ast": null, "code": "// Based on https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\nfunction _objectIs(a, b) {\n  // SameValue algorithm\n  if (a === b) {\n    // Steps 1-5, 7-10\n    // Steps 6.b-6.e: +0 != -0\n    return a !== 0 || 1 / a === 1 / b;\n  } else {\n    // Step 6.a: NaN == NaN\n    return a !== a && b !== b;\n  }\n}\nexport default typeof Object.is === 'function' ? Object.is : _objectIs;", "map": {"version": 3, "names": ["_objectIs", "a", "b", "Object", "is"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_objectIs.js"], "sourcesContent": ["// Based on https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\nfunction _objectIs(a, b) {\n  // SameValue algorithm\n  if (a === b) {\n    // Steps 1-5, 7-10\n    // Steps 6.b-6.e: +0 != -0\n    return a !== 0 || 1 / a === 1 / b;\n  } else {\n    // Step 6.a: NaN == NaN\n    return a !== a && b !== b;\n  }\n}\n\nexport default typeof Object.is === 'function' ? Object.is : _objectIs;"], "mappings": "AAAA;AACA,SAASA,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACvB;EACA,IAAID,CAAC,KAAKC,CAAC,EAAE;IACX;IACA;IACA,OAAOD,CAAC,KAAK,CAAC,IAAI,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGC,CAAC;EACnC,CAAC,MAAM;IACL;IACA,OAAOD,CAAC,KAAKA,CAAC,IAAIC,CAAC,KAAKA,CAAC;EAC3B;AACF;AAEA,eAAe,OAAOC,MAAM,CAACC,EAAE,KAAK,UAAU,GAAGD,MAAM,CAACC,EAAE,GAAGJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}