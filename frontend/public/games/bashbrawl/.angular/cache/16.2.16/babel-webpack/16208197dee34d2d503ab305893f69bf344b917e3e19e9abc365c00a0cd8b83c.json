{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst V = \"scroll\",\n  H = [\"scroll\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M34 6.59136V11.1427L16 11.1026V6.55126C16 5.16708 14.8807 4.04499 13.5 4.04499C12.1193 4.04499 11 5.16708 11 6.55126V29.6089C11.0013 30.5016 10.7367 31.3743 10.24 32.1152H25.58C26.9607 32.1152 28.08 30.9931 28.08 29.6089V13.1578H30.08V29.5688C30.08 32.0603 28.0653 34.0801 25.58 34.0801H6.5C4.01472 34.0801 2 32.0603 2 29.5688V24.055H7.19V26.06H4V29.649C4 31.0332 5.11929 32.1553 6.5 32.1553C7.88071 32.1553 9 31.0332 9 29.649V6.59136C9 4.09985 11.0147 2.08008 13.5 2.08008H29.5C31.9853 2.08008 34 4.09985 34 6.59136ZM32 6.59136C31.9781 5.22287 30.8652 4.12502 29.5 4.12519L17.24 4.08509C17.7361 4.82624 18.0007 5.69879 18 6.59136V9.13772H32V6.59136Z\"/>',\n    outlineAlerted: '<path d=\"M26.9039 1.64597L21.2222 11.1156C20.9526 11.4981 20.9281 11.9946 21.1588 12.4002C21.3896 12.8058 21.8363 13.0517 22.3148 13.0364H33.6881C34.1666 13.0517 34.6134 12.8058 34.8441 12.4002C35.0748 11.9946 35.0503 11.4981 34.7808 11.1156L29.0991 1.64597C28.8711 1.26889 28.4532 1.03711 28.0015 1.03711C27.5497 1.03711 27.1319 1.26889 26.9039 1.64597Z\"/><path d=\"M24.2478 2.08008H13.4672C10.989 2.08008 8.98006 4.09985 8.98006 6.59136V29.649C8.98006 31.0332 7.86396 32.1553 6.48718 32.1553C5.1104 32.1553 3.9943 31.0332 3.9943 29.649V26.06H7.17521V24.055H2V29.5688C2 32.0603 4.00898 34.0801 6.48718 34.0801H25.5128C27.991 34.0801 30 32.0603 30 29.5688V15.0367H28.0057V29.6089C28.0057 30.9931 26.8896 32.1152 25.5128 32.1152H10.2165C10.7118 31.3743 10.9757 30.5016 10.9744 29.6089V6.55126C10.9744 5.16708 12.0905 4.04499 13.4672 4.04499C14.844 4.04499 15.9601 5.16708 15.9601 6.55126V11.1026L19.0304 11.1095C19.1144 10.7337 19.2667 10.3705 19.4862 10.0387L20.0253 9.13772H17.9544V6.59136C17.9551 5.69879 17.6913 4.82624 17.1966 4.08509L23.0367 4.10425L24.2478 2.08008Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M24.1997 2.08008H13.5C11.0147 2.08008 9 4.09985 9 6.59136V29.649C9 31.0332 7.88071 32.1553 6.5 32.1553C5.11929 32.1553 4 31.0332 4 29.649V26.06H7.19V24.055H2V29.5688C2 32.0603 4.01472 34.0801 6.5 34.0801H25.58C28.0653 34.0801 30.08 32.0603 30.08 29.5688V13.1578H28.08V29.6089C28.08 30.9931 26.9607 32.1152 25.58 32.1152H10.24C10.7367 31.3743 11.0013 30.5016 11 29.6089V6.55126C11 5.16708 12.1193 4.04499 13.5 4.04499C14.8807 4.04499 16 5.16708 16 6.55126V11.1026L25.23 11.1232C24.6232 10.558 24.1169 9.88622 23.7409 9.13772H18V6.59136C18.0007 5.69879 17.7361 4.82624 17.24 4.08509L23.2596 4.10478C23.4645 3.37461 23.7848 2.69274 24.1997 2.08008Z\"/>',\n    solid: '<path d=\"M34 11.1427V6.59136C34 4.09985 31.9853 2.08008 29.5 2.08008H13.5C11.0147 2.08008 9 4.09985 9 6.59136V24.055H2V29.5688C2 32.0603 4.01472 34.0801 6.5 34.0801H25.58C28.0653 34.0801 30.08 32.0603 30.08 29.5688V13.1578H28.08V29.6089C28.08 30.9931 26.9607 32.1152 25.58 32.1152H10.24C10.7367 31.3743 11.0013 30.5016 11 29.6089V6.55126C11 5.16708 12.1193 4.04499 13.5 4.04499C14.8807 4.04499 16 5.16708 16 6.55126V11.1026L34 11.1427Z\"/>',\n    solidAlerted: '<path d=\"M26.9039 1.64597L21.2222 11.1156C20.9526 11.4981 20.9281 11.9946 21.1588 12.4002C21.3896 12.8058 21.8363 13.0517 22.3148 13.0364H33.6881C34.1666 13.0517 34.6134 12.8058 34.8441 12.4002C35.0748 11.9946 35.0503 11.4981 34.7808 11.1156L29.0991 1.64597C28.8711 1.26889 28.4532 1.03711 28.0015 1.03711C27.5497 1.03711 27.1319 1.26889 26.9039 1.64597Z\"/><path d=\"M24.3113 2.08008H13.5C11.0147 2.08008 9 4.09985 9 6.59136V24.055H2V29.5688C2 32.0603 4.01472 34.0801 6.5 34.0801H25.58C28.0653 34.0801 30.08 32.0603 30.08 29.5688V15.0367H28.08V29.6089C28.08 30.9931 26.9607 32.1152 25.58 32.1152H10.24C10.7367 31.3743 11.0013 30.5016 11 29.6089V6.55126C11 5.16708 12.1193 4.04499 13.5 4.04499C14.8807 4.04499 16 5.16708 16 6.55126V11.1026L19.079 11.1095C19.1633 10.7337 19.3161 10.3705 19.5362 10.0387L24.3113 2.08008Z\"/>',\n    solidBadged: '<path d=\"M30 13.5001C29.3514 13.4969 28.706 13.4095 28.08 13.2401V29.5401C28.08 30.9208 26.9607 32.0401 25.58 32.0401H10.24C10.7367 31.301 11.0013 30.4305 11 29.5401V6.54008C11 5.15937 12.1193 4.04008 13.5 4.04008C14.8807 4.04008 16 5.15937 16 6.54008V11.0801H24.54C22.2417 8.62951 21.8651 4.94498 23.62 2.08008H13.5C12.3065 2.08008 11.1619 2.55418 10.318 3.3981C9.47411 4.24201 9 5.3866 9 6.58008V24.0001H2V29.5001C2 31.9854 4.01472 34.0001 6.5 34.0001H25.58C28.0653 34.0001 30.08 31.9854 30.08 29.5001V13.5001H30Z\"/><path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/>'\n  })];\nexport { H as scrollIcon, V as scrollIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "V", "H", "outline", "outlineAlerted", "outlineBadged", "solid", "solidAlerted", "solidBadged", "scrollIcon", "scrollIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/scroll.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const V=\"scroll\",H=[\"scroll\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M34 6.59136V11.1427L16 11.1026V6.55126C16 5.16708 14.8807 4.04499 13.5 4.04499C12.1193 4.04499 11 5.16708 11 6.55126V29.6089C11.0013 30.5016 10.7367 31.3743 10.24 32.1152H25.58C26.9607 32.1152 28.08 30.9931 28.08 29.6089V13.1578H30.08V29.5688C30.08 32.0603 28.0653 34.0801 25.58 34.0801H6.5C4.01472 34.0801 2 32.0603 2 29.5688V24.055H7.19V26.06H4V29.649C4 31.0332 5.11929 32.1553 6.5 32.1553C7.88071 32.1553 9 31.0332 9 29.649V6.59136C9 4.09985 11.0147 2.08008 13.5 2.08008H29.5C31.9853 2.08008 34 4.09985 34 6.59136ZM32 6.59136C31.9781 5.22287 30.8652 4.12502 29.5 4.12519L17.24 4.08509C17.7361 4.82624 18.0007 5.69879 18 6.59136V9.13772H32V6.59136Z\"/>',outlineAlerted:'<path d=\"M26.9039 1.64597L21.2222 11.1156C20.9526 11.4981 20.9281 11.9946 21.1588 12.4002C21.3896 12.8058 21.8363 13.0517 22.3148 13.0364H33.6881C34.1666 13.0517 34.6134 12.8058 34.8441 12.4002C35.0748 11.9946 35.0503 11.4981 34.7808 11.1156L29.0991 1.64597C28.8711 1.26889 28.4532 1.03711 28.0015 1.03711C27.5497 1.03711 27.1319 1.26889 26.9039 1.64597Z\"/><path d=\"M24.2478 2.08008H13.4672C10.989 2.08008 8.98006 4.09985 8.98006 6.59136V29.649C8.98006 31.0332 7.86396 32.1553 6.48718 32.1553C5.1104 32.1553 3.9943 31.0332 3.9943 29.649V26.06H7.17521V24.055H2V29.5688C2 32.0603 4.00898 34.0801 6.48718 34.0801H25.5128C27.991 34.0801 30 32.0603 30 29.5688V15.0367H28.0057V29.6089C28.0057 30.9931 26.8896 32.1152 25.5128 32.1152H10.2165C10.7118 31.3743 10.9757 30.5016 10.9744 29.6089V6.55126C10.9744 5.16708 12.0905 4.04499 13.4672 4.04499C14.844 4.04499 15.9601 5.16708 15.9601 6.55126V11.1026L19.0304 11.1095C19.1144 10.7337 19.2667 10.3705 19.4862 10.0387L20.0253 9.13772H17.9544V6.59136C17.9551 5.69879 17.6913 4.82624 17.1966 4.08509L23.0367 4.10425L24.2478 2.08008Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M24.1997 2.08008H13.5C11.0147 2.08008 9 4.09985 9 6.59136V29.649C9 31.0332 7.88071 32.1553 6.5 32.1553C5.11929 32.1553 4 31.0332 4 29.649V26.06H7.19V24.055H2V29.5688C2 32.0603 4.01472 34.0801 6.5 34.0801H25.58C28.0653 34.0801 30.08 32.0603 30.08 29.5688V13.1578H28.08V29.6089C28.08 30.9931 26.9607 32.1152 25.58 32.1152H10.24C10.7367 31.3743 11.0013 30.5016 11 29.6089V6.55126C11 5.16708 12.1193 4.04499 13.5 4.04499C14.8807 4.04499 16 5.16708 16 6.55126V11.1026L25.23 11.1232C24.6232 10.558 24.1169 9.88622 23.7409 9.13772H18V6.59136C18.0007 5.69879 17.7361 4.82624 17.24 4.08509L23.2596 4.10478C23.4645 3.37461 23.7848 2.69274 24.1997 2.08008Z\"/>',solid:'<path d=\"M34 11.1427V6.59136C34 4.09985 31.9853 2.08008 29.5 2.08008H13.5C11.0147 2.08008 9 4.09985 9 6.59136V24.055H2V29.5688C2 32.0603 4.01472 34.0801 6.5 34.0801H25.58C28.0653 34.0801 30.08 32.0603 30.08 29.5688V13.1578H28.08V29.6089C28.08 30.9931 26.9607 32.1152 25.58 32.1152H10.24C10.7367 31.3743 11.0013 30.5016 11 29.6089V6.55126C11 5.16708 12.1193 4.04499 13.5 4.04499C14.8807 4.04499 16 5.16708 16 6.55126V11.1026L34 11.1427Z\"/>',solidAlerted:'<path d=\"M26.9039 1.64597L21.2222 11.1156C20.9526 11.4981 20.9281 11.9946 21.1588 12.4002C21.3896 12.8058 21.8363 13.0517 22.3148 13.0364H33.6881C34.1666 13.0517 34.6134 12.8058 34.8441 12.4002C35.0748 11.9946 35.0503 11.4981 34.7808 11.1156L29.0991 1.64597C28.8711 1.26889 28.4532 1.03711 28.0015 1.03711C27.5497 1.03711 27.1319 1.26889 26.9039 1.64597Z\"/><path d=\"M24.3113 2.08008H13.5C11.0147 2.08008 9 4.09985 9 6.59136V24.055H2V29.5688C2 32.0603 4.01472 34.0801 6.5 34.0801H25.58C28.0653 34.0801 30.08 32.0603 30.08 29.5688V15.0367H28.08V29.6089C28.08 30.9931 26.9607 32.1152 25.58 32.1152H10.24C10.7367 31.3743 11.0013 30.5016 11 29.6089V6.55126C11 5.16708 12.1193 4.04499 13.5 4.04499C14.8807 4.04499 16 5.16708 16 6.55126V11.1026L19.079 11.1095C19.1633 10.7337 19.3161 10.3705 19.5362 10.0387L24.3113 2.08008Z\"/>',solidBadged:'<path d=\"M30 13.5001C29.3514 13.4969 28.706 13.4095 28.08 13.2401V29.5401C28.08 30.9208 26.9607 32.0401 25.58 32.0401H10.24C10.7367 31.301 11.0013 30.4305 11 29.5401V6.54008C11 5.15937 12.1193 4.04008 13.5 4.04008C14.8807 4.04008 16 5.15937 16 6.54008V11.0801H24.54C22.2417 8.62951 21.8651 4.94498 23.62 2.08008H13.5C12.3065 2.08008 11.1619 2.55418 10.318 3.3981C9.47411 4.24201 9 5.3866 9 6.58008V24.0001H2V29.5001C2 31.9854 4.01472 34.0001 6.5 34.0001H25.58C28.0653 34.0001 30.08 31.9854 30.08 29.5001V13.5001H30Z\"/><path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/>'})];export{H as scrollIcon,V as scrollIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,gsBAAgsB;IAACC,cAAc,EAAC,mjCAAmjC;IAACC,aAAa,EAAC,ixBAAixB;IAACC,KAAK,EAAC,wbAAwb;IAACC,YAAY,EAAC,szBAAszB;IAACC,WAAW,EAAC;EAAsoB,CAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,UAAU,EAACR,CAAC,IAAIS,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}