{"ast": null, "code": "import { renderIcon as a } from \"../icon.renderer.js\";\nconst r = \"shrink\",\n  n = [\"shrink\", a({\n    outline: '<path d=\"M32,15H22.41l9.25-9.25a1,1,0,0,0-1.41-1.41L21,13.59V4a1,1,0,0,0-2,0V17H32a1,1,0,0,0,0-2Z\"/><path d=\"M4,19a1,1,0,0,0,0,2h9.59L4.33,30.25a1,1,0,1,0,1.41,1.41L15,22.41V32a1,1,0,0,0,2,0V19Z\"/>'\n  })];\nexport { n as shrinkIcon, r as shrinkIconName };", "map": {"version": 3, "names": ["renderIcon", "a", "r", "n", "outline", "shrinkIcon", "shrinkIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/shrink.js"], "sourcesContent": ["import{renderIcon as a}from\"../icon.renderer.js\";const r=\"shrink\",n=[\"shrink\",a({outline:'<path d=\"M32,15H22.41l9.25-9.25a1,1,0,0,0-1.41-1.41L21,13.59V4a1,1,0,0,0-2,0V17H32a1,1,0,0,0,0-2Z\"/><path d=\"M4,19a1,1,0,0,0,0,2h9.59L4.33,30.25a1,1,0,1,0,1.41,1.41L15,22.41V32a1,1,0,0,0,2,0V19Z\"/>'})];export{n as shrinkIcon,r as shrinkIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAuM,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,UAAU,EAACH,CAAC,IAAII,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}