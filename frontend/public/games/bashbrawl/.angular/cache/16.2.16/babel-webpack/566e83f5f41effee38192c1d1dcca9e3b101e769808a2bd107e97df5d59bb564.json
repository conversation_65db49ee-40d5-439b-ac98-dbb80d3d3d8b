{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"tags\",\n  d = [\"tags\", C({\n    outline: '<path d=\"M8.46848 9.92189C7.61868 9.92189 6.93884 10.6022 6.93884 11.4325C6.93884 12.2628 7.62868 12.9431 8.46848 12.9431C9.30829 12.9431 9.99813 12.2628 9.99813 11.4325C9.99813 10.6022 9.30829 9.92189 8.46848 9.92189ZM27.3341 18.5952L14.5471 5.93036C13.9672 5.35013 13.1874 5.03001 12.3576 5.03001H2.99977C2.44989 5.04002 2 5.48018 2 6.0304V15.294C2 16.1143 2.31993 16.8846 2.90979 17.4548L15.6968 30.1097C16.2966 30.7099 17.0865 31 17.8863 31C18.6861 31 19.4759 30.6999 20.0758 30.1097L27.3441 22.9169C28.5538 21.7264 28.5538 19.7857 27.3441 18.5852L27.3341 18.5952ZM25.9244 21.5264L18.6561 28.7191C18.2362 29.1393 17.5364 29.1393 17.1065 28.7191L4.31946 16.0543C4.10951 15.8542 3.99953 15.5741 3.99953 15.284V7.01077H12.3576C12.6475 7.01077 12.9274 7.12082 13.1274 7.3309L25.9144 19.9858C26.3443 20.4059 26.3443 21.0962 25.9144 21.5164L25.9244 21.5264ZM33.0927 18.5652L20.2957 5.90035C19.7158 5.32012 18.936 5 18.1062 5H16.5666L31.733 19.9858C32.1629 20.4059 32.1029 21.0662 31.673 21.4863L22.3652 30.6999C22.7851 30.9 23.175 30.98 23.6349 30.98C24.4247 30.98 25.2246 30.6799 25.8244 30.0897L33.0927 22.8969C34.3024 21.7064 34.3024 19.7657 33.0927 18.5652Z\"/>',\n    outlineAlerted: '<path d=\"M21.3745 6.96801L20.2957 5.90035C19.7158 5.32012 18.936 5 18.1062 5H16.5666L20.3268 8.71537L21.3745 6.96801Z\"/><path d=\"M26.7282 15.0405H29.5313L33.0927 18.5652C34.3024 19.7657 34.3024 21.7064 33.0927 22.8969L25.8244 30.0897C25.2246 30.6799 24.4247 30.98 23.6349 30.98C23.175 30.98 22.7851 30.9 22.3652 30.6999L31.673 21.4863C32.1029 21.0662 32.1629 20.4059 31.733 19.9858L26.7282 15.0405Z\"/><path d=\"M19.2433 10.5818L14.5471 5.93036C13.9672 5.35013 13.1874 5.03001 12.3576 5.03001H2.99977C2.44989 5.04002 2 5.48018 2 6.0304V15.294C2 16.1143 2.31993 16.8846 2.90979 17.4548L15.6968 30.1097C16.2966 30.7099 17.0865 31 17.8863 31C18.6861 31 19.4759 30.6999 20.0758 30.1097L27.3441 22.9169C28.5538 21.7264 28.5538 19.7857 27.3441 18.5852L27.3341 18.5952L23.7451 15.0405H22.3348C21.5062 15.0572 20.7008 14.7613 20.0858 14.2174L25.9144 19.9858C26.3443 20.4059 26.3443 21.0962 25.9144 21.5164L25.9244 21.5264L18.6561 28.7191C18.2362 29.1393 17.5364 29.1393 17.1065 28.7191L4.31946 16.0543C4.10951 15.8542 3.99953 15.5741 3.99953 15.284V7.01077H12.3576C12.6475 7.01077 12.9274 7.12082 13.1274 7.3309L19.8277 13.962C19.6727 13.7903 19.5344 13.6 19.4165 13.3926C18.9197 12.5187 18.8684 11.4823 19.2433 10.5818Z\"/><path d=\"M6.93884 11.4325C6.93884 10.6022 7.61868 9.92189 8.46848 9.92189C9.30829 9.92189 9.99813 10.6022 9.99813 11.4325C9.99813 12.2628 9.30829 12.9431 8.46848 12.9431C7.62868 12.9431 6.93884 12.2628 6.93884 11.4325Z\"/><path d=\"M26.9039 1.64597L21.2222 11.1156C20.9526 11.4981 20.9281 11.9946 21.1588 12.4002C21.3896 12.8058 21.8363 13.0517 22.3148 13.0364H33.6881C34.1666 13.0517 34.6134 12.8058 34.8441 12.4002C35.0748 11.9946 35.0503 11.4981 34.7808 11.1156L29.0991 1.64597C28.8711 1.26889 28.4532 1.03711 28.0015 1.03711C27.5497 1.03711 27.1319 1.26889 26.9039 1.64597Z\"/>',\n    outlineBadged: '<path d=\"M8.46848 9.92189C7.61868 9.92189 6.93884 10.6022 6.93884 11.4325C6.93884 12.2628 7.62868 12.9431 8.46848 12.9431C9.30829 12.9431 9.99813 12.2628 9.99813 11.4325C9.99813 10.6022 9.30829 9.92189 8.46848 9.92189ZM27.3341 18.5952L14.5471 5.93036C13.9672 5.35013 13.1874 5.03001 12.3576 5.03001H2.99977C2.44989 5.04002 2 5.48018 2 6.0304V15.294C2 16.1143 2.31993 16.8846 2.90979 17.4548L15.6968 30.1097C16.2966 30.7099 17.0865 31 17.8863 31C18.6861 31 19.4759 30.6999 20.0758 30.1097L27.3441 22.9169C28.5538 21.7264 28.5538 19.7857 27.3441 18.5852L27.3341 18.5952ZM25.9244 21.5264L18.6561 28.7191C18.2362 29.1393 17.5364 29.1393 17.1065 28.7191L4.31946 16.0543C4.10951 15.8542 3.99953 15.5741 3.99953 15.284V7.01077H12.3576C12.6475 7.01077 12.9274 7.12082 13.1274 7.3309L25.9144 19.9858C26.3443 20.4059 26.3443 21.0962 25.9144 21.5164L25.9244 21.5264ZM33.0927 18.5652L20.2957 5.90035C19.7158 5.32012 18.936 5 18.1062 5H16.5666L31.733 19.9858C32.1629 20.4059 32.1029 21.0662 31.673 21.4863L22.3652 30.6999C22.7851 30.9 23.175 30.98 23.6349 30.98C24.4247 30.98 25.2246 30.6799 25.8244 30.0897L33.0927 22.8969C34.3024 21.7064 34.3024 19.7657 33.0927 18.5652Z\"/><path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/>',\n    solid: '<path d=\"M27.3341 18.57L14.5471 5.9C13.9672 5.32 13.1874 5 12.3576 5H2.99977C2.44989 5 2 5.44 2 5.99V15.26C2 16.08 2.31993 16.85 2.90979 17.43L15.6968 30.1C16.2966 30.7 17.0865 31 17.8863 31C18.6861 31 19.4759 30.7 20.0758 30.1L27.3441 22.9C28.5538 21.71 28.5538 19.76 27.3441 18.57H27.3341ZM8.52847 13.18C7.57869 13.18 6.79888 12.41 6.79888 11.47C6.79888 10.53 7.57869 9.76 8.52847 9.76C9.47825 9.76 10.2581 10.53 10.2581 11.47C10.2581 12.41 9.47825 13.18 8.52847 13.18ZM33.0827 18.57L20.2957 5.9C19.7158 5.32 18.936 5 18.1062 5H16.5666L31.733 20C32.1629 20.42 32.1029 21.08 31.673 21.5L22.3652 30.72C22.7851 30.92 23.175 31 23.6349 31C24.4247 31 25.2246 30.7 25.8244 30.1L33.0927 22.9C34.3024 21.71 34.3024 19.76 33.0927 18.57H33.0827Z\"/>',\n    solidAlerted: '<path d=\"M21.2331 6.82885L20.2957 5.9C19.7158 5.32 18.936 5 18.1062 5H16.5666L20.2061 8.5996L21.2331 6.82885Z\"/><path d=\"M26.6776 15.0001H29.4798L33.0827 18.57H33.0927C34.3024 19.76 34.3024 21.71 33.0927 22.9L25.8244 30.1C25.2246 30.7 24.4247 31 23.6349 31C23.175 31 22.7851 30.92 22.3652 30.72L31.673 21.5C32.1029 21.08 32.1629 20.42 31.733 20L26.6776 15.0001Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M19.1538 10.4646C18.7769 11.3737 18.8242 12.4183 19.3098 13.3014C19.8983 14.3717 21.0319 15.0252 22.2507 15.0001H23.7312L27.3341 18.57H27.3441C28.5538 19.76 28.5538 21.71 27.3441 22.9L20.0758 30.1C19.4759 30.7 18.6861 31 17.8863 31C17.0865 31 16.2966 30.7 15.6968 30.1L2.90979 17.43C2.31993 16.85 2 16.08 2 15.26V5.99C2 5.44 2.44989 5 2.99977 5H12.3576C13.1874 5 13.9672 5.32 14.5471 5.9L19.1538 10.4646ZM6.79888 11.47C6.79888 12.41 7.57869 13.18 8.52847 13.18C9.47825 13.18 10.2581 12.41 10.2581 11.47C10.2581 10.53 9.47825 9.76 8.52847 9.76C7.57869 9.76 6.79888 10.53 6.79888 11.47Z\"/><path d=\"M26.8499 1.13981L21.1299 10.9998C20.8585 11.3981 20.8339 11.9151 21.0662 12.3374C21.2984 12.7597 21.7482 13.0157 22.2299 12.9998H33.6799C34.1616 13.0157 34.6114 12.7597 34.8436 12.3374C35.0759 11.9151 35.0512 11.3981 34.7799 10.9998L29.0599 1.13981C28.8303 0.747191 28.4097 0.505859 27.9549 0.505859C27.5001 0.505859 27.0794 0.747191 26.8499 1.13981Z\"/>',\n    solidBadged: '<path d=\"M27.3341 18.57L14.5471 5.9C13.9672 5.32 13.1874 5 12.3576 5H2.99977C2.44989 5 2 5.44 2 5.99V15.26C2 16.08 2.31993 16.85 2.90979 17.43L15.6968 30.1C16.2966 30.7 17.0865 31 17.8863 31C18.6861 31 19.4759 30.7 20.0758 30.1L27.3441 22.9C28.5538 21.71 28.5538 19.76 27.3441 18.57H27.3341ZM8.52847 13.18C7.57869 13.18 6.79888 12.41 6.79888 11.47C6.79888 10.53 7.57869 9.76 8.52847 9.76C9.47825 9.76 10.2581 10.53 10.2581 11.47C10.2581 12.41 9.47825 13.18 8.52847 13.18ZM33.0827 18.57L20.2957 5.9C19.7158 5.32 18.936 5 18.1062 5H16.5666L31.733 20C32.1629 20.42 32.1029 21.08 31.673 21.5L22.3652 30.72C22.7851 30.92 23.175 31 23.6349 31C24.4247 31 25.2246 30.7 25.8244 30.1L33.0927 22.9C34.3024 21.71 34.3024 19.76 33.0927 18.57H33.0827Z\"/><path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/>'\n  })];\nexport { d as tagsIcon, L as tagsIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "d", "outline", "outlineAlerted", "outlineBadged", "solid", "solidAlerted", "solidBadged", "tagsIcon", "tagsIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/tags.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"tags\",d=[\"tags\",C({outline:'<path d=\"M8.46848 9.92189C7.61868 9.92189 6.93884 10.6022 6.93884 11.4325C6.93884 12.2628 7.62868 12.9431 8.46848 12.9431C9.30829 12.9431 9.99813 12.2628 9.99813 11.4325C9.99813 10.6022 9.30829 9.92189 8.46848 9.92189ZM27.3341 18.5952L14.5471 5.93036C13.9672 5.35013 13.1874 5.03001 12.3576 5.03001H2.99977C2.44989 5.04002 2 5.48018 2 6.0304V15.294C2 16.1143 2.31993 16.8846 2.90979 17.4548L15.6968 30.1097C16.2966 30.7099 17.0865 31 17.8863 31C18.6861 31 19.4759 30.6999 20.0758 30.1097L27.3441 22.9169C28.5538 21.7264 28.5538 19.7857 27.3441 18.5852L27.3341 18.5952ZM25.9244 21.5264L18.6561 28.7191C18.2362 29.1393 17.5364 29.1393 17.1065 28.7191L4.31946 16.0543C4.10951 15.8542 3.99953 15.5741 3.99953 15.284V7.01077H12.3576C12.6475 7.01077 12.9274 7.12082 13.1274 7.3309L25.9144 19.9858C26.3443 20.4059 26.3443 21.0962 25.9144 21.5164L25.9244 21.5264ZM33.0927 18.5652L20.2957 5.90035C19.7158 5.32012 18.936 5 18.1062 5H16.5666L31.733 19.9858C32.1629 20.4059 32.1029 21.0662 31.673 21.4863L22.3652 30.6999C22.7851 30.9 23.175 30.98 23.6349 30.98C24.4247 30.98 25.2246 30.6799 25.8244 30.0897L33.0927 22.8969C34.3024 21.7064 34.3024 19.7657 33.0927 18.5652Z\"/>',outlineAlerted:'<path d=\"M21.3745 6.96801L20.2957 5.90035C19.7158 5.32012 18.936 5 18.1062 5H16.5666L20.3268 8.71537L21.3745 6.96801Z\"/><path d=\"M26.7282 15.0405H29.5313L33.0927 18.5652C34.3024 19.7657 34.3024 21.7064 33.0927 22.8969L25.8244 30.0897C25.2246 30.6799 24.4247 30.98 23.6349 30.98C23.175 30.98 22.7851 30.9 22.3652 30.6999L31.673 21.4863C32.1029 21.0662 32.1629 20.4059 31.733 19.9858L26.7282 15.0405Z\"/><path d=\"M19.2433 10.5818L14.5471 5.93036C13.9672 5.35013 13.1874 5.03001 12.3576 5.03001H2.99977C2.44989 5.04002 2 5.48018 2 6.0304V15.294C2 16.1143 2.31993 16.8846 2.90979 17.4548L15.6968 30.1097C16.2966 30.7099 17.0865 31 17.8863 31C18.6861 31 19.4759 30.6999 20.0758 30.1097L27.3441 22.9169C28.5538 21.7264 28.5538 19.7857 27.3441 18.5852L27.3341 18.5952L23.7451 15.0405H22.3348C21.5062 15.0572 20.7008 14.7613 20.0858 14.2174L25.9144 19.9858C26.3443 20.4059 26.3443 21.0962 25.9144 21.5164L25.9244 21.5264L18.6561 28.7191C18.2362 29.1393 17.5364 29.1393 17.1065 28.7191L4.31946 16.0543C4.10951 15.8542 3.99953 15.5741 3.99953 15.284V7.01077H12.3576C12.6475 7.01077 12.9274 7.12082 13.1274 7.3309L19.8277 13.962C19.6727 13.7903 19.5344 13.6 19.4165 13.3926C18.9197 12.5187 18.8684 11.4823 19.2433 10.5818Z\"/><path d=\"M6.93884 11.4325C6.93884 10.6022 7.61868 9.92189 8.46848 9.92189C9.30829 9.92189 9.99813 10.6022 9.99813 11.4325C9.99813 12.2628 9.30829 12.9431 8.46848 12.9431C7.62868 12.9431 6.93884 12.2628 6.93884 11.4325Z\"/><path d=\"M26.9039 1.64597L21.2222 11.1156C20.9526 11.4981 20.9281 11.9946 21.1588 12.4002C21.3896 12.8058 21.8363 13.0517 22.3148 13.0364H33.6881C34.1666 13.0517 34.6134 12.8058 34.8441 12.4002C35.0748 11.9946 35.0503 11.4981 34.7808 11.1156L29.0991 1.64597C28.8711 1.26889 28.4532 1.03711 28.0015 1.03711C27.5497 1.03711 27.1319 1.26889 26.9039 1.64597Z\"/>',outlineBadged:'<path d=\"M8.46848 9.92189C7.61868 9.92189 6.93884 10.6022 6.93884 11.4325C6.93884 12.2628 7.62868 12.9431 8.46848 12.9431C9.30829 12.9431 9.99813 12.2628 9.99813 11.4325C9.99813 10.6022 9.30829 9.92189 8.46848 9.92189ZM27.3341 18.5952L14.5471 5.93036C13.9672 5.35013 13.1874 5.03001 12.3576 5.03001H2.99977C2.44989 5.04002 2 5.48018 2 6.0304V15.294C2 16.1143 2.31993 16.8846 2.90979 17.4548L15.6968 30.1097C16.2966 30.7099 17.0865 31 17.8863 31C18.6861 31 19.4759 30.6999 20.0758 30.1097L27.3441 22.9169C28.5538 21.7264 28.5538 19.7857 27.3441 18.5852L27.3341 18.5952ZM25.9244 21.5264L18.6561 28.7191C18.2362 29.1393 17.5364 29.1393 17.1065 28.7191L4.31946 16.0543C4.10951 15.8542 3.99953 15.5741 3.99953 15.284V7.01077H12.3576C12.6475 7.01077 12.9274 7.12082 13.1274 7.3309L25.9144 19.9858C26.3443 20.4059 26.3443 21.0962 25.9144 21.5164L25.9244 21.5264ZM33.0927 18.5652L20.2957 5.90035C19.7158 5.32012 18.936 5 18.1062 5H16.5666L31.733 19.9858C32.1629 20.4059 32.1029 21.0662 31.673 21.4863L22.3652 30.6999C22.7851 30.9 23.175 30.98 23.6349 30.98C24.4247 30.98 25.2246 30.6799 25.8244 30.0897L33.0927 22.8969C34.3024 21.7064 34.3024 19.7657 33.0927 18.5652Z\"/><path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/>',solid:'<path d=\"M27.3341 18.57L14.5471 5.9C13.9672 5.32 13.1874 5 12.3576 5H2.99977C2.44989 5 2 5.44 2 5.99V15.26C2 16.08 2.31993 16.85 2.90979 17.43L15.6968 30.1C16.2966 30.7 17.0865 31 17.8863 31C18.6861 31 19.4759 30.7 20.0758 30.1L27.3441 22.9C28.5538 21.71 28.5538 19.76 27.3441 18.57H27.3341ZM8.52847 13.18C7.57869 13.18 6.79888 12.41 6.79888 11.47C6.79888 10.53 7.57869 9.76 8.52847 9.76C9.47825 9.76 10.2581 10.53 10.2581 11.47C10.2581 12.41 9.47825 13.18 8.52847 13.18ZM33.0827 18.57L20.2957 5.9C19.7158 5.32 18.936 5 18.1062 5H16.5666L31.733 20C32.1629 20.42 32.1029 21.08 31.673 21.5L22.3652 30.72C22.7851 30.92 23.175 31 23.6349 31C24.4247 31 25.2246 30.7 25.8244 30.1L33.0927 22.9C34.3024 21.71 34.3024 19.76 33.0927 18.57H33.0827Z\"/>',solidAlerted:'<path d=\"M21.2331 6.82885L20.2957 5.9C19.7158 5.32 18.936 5 18.1062 5H16.5666L20.2061 8.5996L21.2331 6.82885Z\"/><path d=\"M26.6776 15.0001H29.4798L33.0827 18.57H33.0927C34.3024 19.76 34.3024 21.71 33.0927 22.9L25.8244 30.1C25.2246 30.7 24.4247 31 23.6349 31C23.175 31 22.7851 30.92 22.3652 30.72L31.673 21.5C32.1029 21.08 32.1629 20.42 31.733 20L26.6776 15.0001Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M19.1538 10.4646C18.7769 11.3737 18.8242 12.4183 19.3098 13.3014C19.8983 14.3717 21.0319 15.0252 22.2507 15.0001H23.7312L27.3341 18.57H27.3441C28.5538 19.76 28.5538 21.71 27.3441 22.9L20.0758 30.1C19.4759 30.7 18.6861 31 17.8863 31C17.0865 31 16.2966 30.7 15.6968 30.1L2.90979 17.43C2.31993 16.85 2 16.08 2 15.26V5.99C2 5.44 2.44989 5 2.99977 5H12.3576C13.1874 5 13.9672 5.32 14.5471 5.9L19.1538 10.4646ZM6.79888 11.47C6.79888 12.41 7.57869 13.18 8.52847 13.18C9.47825 13.18 10.2581 12.41 10.2581 11.47C10.2581 10.53 9.47825 9.76 8.52847 9.76C7.57869 9.76 6.79888 10.53 6.79888 11.47Z\"/><path d=\"M26.8499 1.13981L21.1299 10.9998C20.8585 11.3981 20.8339 11.9151 21.0662 12.3374C21.2984 12.7597 21.7482 13.0157 22.2299 12.9998H33.6799C34.1616 13.0157 34.6114 12.7597 34.8436 12.3374C35.0759 11.9151 35.0512 11.3981 34.7799 10.9998L29.0599 1.13981C28.8303 0.747191 28.4097 0.505859 27.9549 0.505859C27.5001 0.505859 27.0794 0.747191 26.8499 1.13981Z\"/>',solidBadged:'<path d=\"M27.3341 18.57L14.5471 5.9C13.9672 5.32 13.1874 5 12.3576 5H2.99977C2.44989 5 2 5.44 2 5.99V15.26C2 16.08 2.31993 16.85 2.90979 17.43L15.6968 30.1C16.2966 30.7 17.0865 31 17.8863 31C18.6861 31 19.4759 30.7 20.0758 30.1L27.3441 22.9C28.5538 21.71 28.5538 19.76 27.3441 18.57H27.3341ZM8.52847 13.18C7.57869 13.18 6.79888 12.41 6.79888 11.47C6.79888 10.53 7.57869 9.76 8.52847 9.76C9.47825 9.76 10.2581 10.53 10.2581 11.47C10.2581 12.41 9.47825 13.18 8.52847 13.18ZM33.0827 18.57L20.2957 5.9C19.7158 5.32 18.936 5 18.1062 5H16.5666L31.733 20C32.1629 20.42 32.1029 21.08 31.673 21.5L22.3652 30.72C22.7851 30.92 23.175 31 23.6349 31C24.4247 31 25.2246 30.7 25.8244 30.1L33.0927 22.9C34.3024 21.71 34.3024 19.76 33.0927 18.57H33.0827Z\"/><path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/>'})];export{d as tagsIcon,L as tagsIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,MAAM;EAACC,CAAC,GAAC,CAAC,MAAM,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,4oCAA4oC;IAACC,cAAc,EAAC,iwDAAiwD;IAACC,aAAa,EAAC,0wCAA0wC;IAACC,KAAK,EAAC,suBAAsuB;IAACC,YAAY,EAAC,o1CAAo1C;IAACC,WAAW,EAAC;EAAo2B,CAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,QAAQ,EAACR,CAAC,IAAIS,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}