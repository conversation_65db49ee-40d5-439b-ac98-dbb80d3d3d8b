{"ast": null, "code": "import { GlobalStateService as t } from \"@cds/core/internal\";\nimport { unknownIcon as e } from \"./shapes/unknown.js\";\nclass r {\n  static get registry() {\n    return {\n      unknown: e[1],\n      ...t.state.iconRegistry\n    };\n  }\n  static addIcons(...e) {\n    t.state.iconRegistry = {\n      ...t.state.iconRegistry,\n      ...Object.fromEntries(e.filter(([t]) => !r.registry[t]))\n    };\n  }\n  static addAliases(...e) {\n    const s = e.filter(([t]) => r.registry[t]).flatMap(([t, e]) => e.map(e => [e, r.registry[t]]));\n    t.state.iconRegistry = {\n      ...t.state.iconRegistry,\n      ...Object.fromEntries(s)\n    };\n  }\n  static getIconNameFromShape(t) {\n    return t[0];\n  }\n}\nexport { r as ClarityIcons };", "map": {"version": 3, "names": ["GlobalStateService", "t", "unknownIcon", "e", "r", "registry", "unknown", "state", "iconRegistry", "addIcons", "Object", "fromEntries", "filter", "addAliases", "s", "flatMap", "map", "getIconNameFromShape", "ClarityIcons"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/icon.service.js"], "sourcesContent": ["import{GlobalStateService as t}from\"@cds/core/internal\";import{unknownIcon as e}from\"./shapes/unknown.js\";class r{static get registry(){return{unknown:e[1],...t.state.iconRegistry}}static addIcons(...e){t.state.iconRegistry={...t.state.iconRegistry,...Object.fromEntries(e.filter((([t])=>!r.registry[t])))}}static addAliases(...e){const s=e.filter((([t])=>r.registry[t])).flatMap((([t,e])=>e.map((e=>[e,r.registry[t]]))));t.state.iconRegistry={...t.state.iconRegistry,...Object.fromEntries(s)}}static getIconNameFromShape(t){return t[0]}}export{r as ClarityIcons};\n"], "mappings": "AAAA,SAAOA,kBAAkB,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC;EAAC,WAAWC,QAAQA,CAAA,EAAE;IAAC,OAAM;MAACC,OAAO,EAACH,CAAC,CAAC,CAAC,CAAC;MAAC,GAAGF,CAAC,CAACM,KAAK,CAACC;IAAY,CAAC;EAAA;EAAC,OAAOC,QAAQA,CAAC,GAAGN,CAAC,EAAC;IAACF,CAAC,CAACM,KAAK,CAACC,YAAY,GAAC;MAAC,GAAGP,CAAC,CAACM,KAAK,CAACC,YAAY;MAAC,GAAGE,MAAM,CAACC,WAAW,CAACR,CAAC,CAACS,MAAM,CAAE,CAAC,CAACX,CAAC,CAAC,KAAG,CAACG,CAAC,CAACC,QAAQ,CAACJ,CAAC,CAAE,CAAC;IAAC,CAAC;EAAA;EAAC,OAAOY,UAAUA,CAAC,GAAGV,CAAC,EAAC;IAAC,MAAMW,CAAC,GAACX,CAAC,CAACS,MAAM,CAAE,CAAC,CAACX,CAAC,CAAC,KAAGG,CAAC,CAACC,QAAQ,CAACJ,CAAC,CAAE,CAAC,CAACc,OAAO,CAAE,CAAC,CAACd,CAAC,EAACE,CAAC,CAAC,KAAGA,CAAC,CAACa,GAAG,CAAEb,CAAC,IAAE,CAACA,CAAC,EAACC,CAAC,CAACC,QAAQ,CAACJ,CAAC,CAAC,CAAE,CAAE,CAAC;IAACA,CAAC,CAACM,KAAK,CAACC,YAAY,GAAC;MAAC,GAAGP,CAAC,CAACM,KAAK,CAACC,YAAY;MAAC,GAAGE,MAAM,CAACC,WAAW,CAACG,CAAC;IAAC,CAAC;EAAA;EAAC,OAAOG,oBAAoBA,CAAChB,CAAC,EAAC;IAAC,OAAOA,CAAC,CAAC,CAAC,CAAC;EAAA;AAAC;AAAC,SAAOG,CAAC,IAAIc,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}