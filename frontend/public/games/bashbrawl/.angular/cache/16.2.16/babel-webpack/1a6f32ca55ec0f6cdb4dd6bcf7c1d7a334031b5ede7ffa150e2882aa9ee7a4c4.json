{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"alarm-off\",\n  M = [\"alarm-off\", C({\n    outline: '<path d=\"M18.0001 6.09102C24.6201 6.09102 30.0001 11.4723 30.0001 18.0938C30.0001 20.2943 29.3901 22.3548 28.3601 24.1252L29.8101 25.5756C31.1901 23.4051 32.0001 20.8445 32.0001 18.0838C32.0001 10.362 25.7201 4.08055 18.0001 4.08055C15.2501 4.08055 12.6801 4.89074 10.5101 6.27106L11.9601 7.7214C13.7401 6.68116 15.7901 6.08102 17.9901 6.08102L18.0001 6.09102ZM31.6401 9.77188C31.8701 9.27176 32.0001 8.71163 32.0001 8.1315C32.0001 5.92098 30.2101 4.13056 28.0001 4.13056C27.4201 4.13056 26.8701 4.26059 26.3701 4.49064C28.5201 5.81095 30.3201 7.63138 31.6401 9.78188V9.77188ZM33.7101 32.3072L3.71006 2.29013C3.32006 1.90004 2.68006 1.90004 2.29006 2.29013C1.90006 2.68022 1.90006 3.32037 2.29006 3.71046L4.61006 6.031C4.23006 6.64115 4.00006 7.36132 4.00006 8.1315C4.00006 8.72163 4.13006 9.27176 4.36006 9.77188C4.86006 8.95169 5.44006 8.20151 6.08006 7.50135L7.48006 8.90168C5.32006 11.3623 4.00006 14.573 4.00006 18.0938C4.00006 21.6147 5.24006 24.6554 7.29006 27.0859L4.26006 30.4167C3.89006 30.8268 3.92006 31.457 4.33006 31.827C4.52006 31.9971 4.76006 32.0871 5.00006 32.0871C5.27006 32.0871 5.54006 31.9771 5.74006 31.757L8.69006 28.5063C11.1701 30.7268 14.4301 32.0871 18.0001 32.0871C21.5701 32.0871 24.7301 30.7568 27.1901 28.6063L32.2901 33.7075C32.4901 33.9075 32.7401 33.9976 33.0001 33.9976C33.2601 33.9976 33.5101 33.8975 33.7101 33.7075C34.1001 33.3174 34.1001 32.6873 33.7101 32.2972V32.3072ZM18.0001 30.1066C11.3801 30.1066 6.00006 24.7254 6.00006 18.1038C6.00006 15.1331 7.10006 12.4225 8.90006 10.322L25.7801 27.206C23.6801 29.0064 20.9701 30.1066 18.0001 30.1066ZM9.63006 4.48064C9.25006 4.3106 8.83006 4.21058 8.40006 4.17057L9.09006 4.86073C9.27006 4.7407 9.44006 4.60067 9.63006 4.48064ZM17.0001 10.092V12.7726L19.0001 14.7731V10.092C19.0001 9.54183 18.5501 9.09172 18.0001 9.09172C17.4501 9.09172 17.0001 9.54183 17.0001 10.092Z\"/>',\n    solid: '<path d=\"M16.8001 10.092C16.8001 9.4318 17.3401 8.89167 18.0001 8.89167C18.6601 8.89167 19.2001 9.4318 19.2001 10.092V14.9731L29.8101 25.5856C31.1901 23.4151 32.0001 20.8545 32.0001 18.0938C32.0001 10.372 25.7201 4.09055 18.0001 4.09055C15.2501 4.09055 12.6801 4.90074 10.5101 6.28106L16.8001 12.5725V10.092ZM31.6401 9.77188C31.8701 9.27176 32.0001 8.71163 32.0001 8.1315C32.0001 5.92098 30.2101 4.13056 28.0001 4.13056C27.4201 4.13056 26.8701 4.26059 26.3701 4.49064C28.5201 5.81095 30.3201 7.63138 31.6401 9.78188V9.77188ZM9.63006 4.48064C9.25006 4.3106 8.83006 4.21058 8.40006 4.17057L9.09006 4.86073C9.27006 4.7407 9.44006 4.60067 9.63006 4.48064ZM33.7001 32.2972L3.71006 2.29013C3.32006 1.90004 2.68006 1.90004 2.29006 2.29013C1.90006 2.68022 1.90006 3.32037 2.29006 3.71046L4.61006 6.031C4.23006 6.64115 4.00006 7.36132 4.00006 8.1315C4.00006 8.72163 4.13006 9.27176 4.36006 9.77188C4.86006 8.95169 5.44006 8.20151 6.08006 7.50135L7.48006 8.90168C5.32006 11.3623 4.00006 14.573 4.00006 18.0938C4.00006 21.6147 5.24006 24.6554 7.29006 27.0859L4.26006 30.4167C3.89006 30.8268 3.92006 31.457 4.33006 31.827C4.52006 31.9971 4.76006 32.0871 5.00006 32.0871C5.27006 32.0871 5.54006 31.9771 5.74006 31.757L8.69006 28.5063C11.1701 30.7268 14.4301 32.0871 18.0001 32.0871C21.5701 32.0871 24.7301 30.7568 27.1901 28.6063L32.2901 33.7075C32.4901 33.9075 32.7401 33.9976 33.0001 33.9976C33.2601 33.9976 33.5101 33.8975 33.7101 33.7075C34.1001 33.3174 34.1001 32.6873 33.7101 32.2972H33.7001Z\"/>'\n  })];\nexport { M as alarmOffIcon, L as alarmOffIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "M", "outline", "solid", "alarmOffIcon", "alarmOffIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/alarm-off.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"alarm-off\",M=[\"alarm-off\",C({outline:'<path d=\"M18.0001 6.09102C24.6201 6.09102 30.0001 11.4723 30.0001 18.0938C30.0001 20.2943 29.3901 22.3548 28.3601 24.1252L29.8101 25.5756C31.1901 23.4051 32.0001 20.8445 32.0001 18.0838C32.0001 10.362 25.7201 4.08055 18.0001 4.08055C15.2501 4.08055 12.6801 4.89074 10.5101 6.27106L11.9601 7.7214C13.7401 6.68116 15.7901 6.08102 17.9901 6.08102L18.0001 6.09102ZM31.6401 9.77188C31.8701 9.27176 32.0001 8.71163 32.0001 8.1315C32.0001 5.92098 30.2101 4.13056 28.0001 4.13056C27.4201 4.13056 26.8701 4.26059 26.3701 4.49064C28.5201 5.81095 30.3201 7.63138 31.6401 9.78188V9.77188ZM33.7101 32.3072L3.71006 2.29013C3.32006 1.90004 2.68006 1.90004 2.29006 2.29013C1.90006 2.68022 1.90006 3.32037 2.29006 3.71046L4.61006 6.031C4.23006 6.64115 4.00006 7.36132 4.00006 8.1315C4.00006 8.72163 4.13006 9.27176 4.36006 9.77188C4.86006 8.95169 5.44006 8.20151 6.08006 7.50135L7.48006 8.90168C5.32006 11.3623 4.00006 14.573 4.00006 18.0938C4.00006 21.6147 5.24006 24.6554 7.29006 27.0859L4.26006 30.4167C3.89006 30.8268 3.92006 31.457 4.33006 31.827C4.52006 31.9971 4.76006 32.0871 5.00006 32.0871C5.27006 32.0871 5.54006 31.9771 5.74006 31.757L8.69006 28.5063C11.1701 30.7268 14.4301 32.0871 18.0001 32.0871C21.5701 32.0871 24.7301 30.7568 27.1901 28.6063L32.2901 33.7075C32.4901 33.9075 32.7401 33.9976 33.0001 33.9976C33.2601 33.9976 33.5101 33.8975 33.7101 33.7075C34.1001 33.3174 34.1001 32.6873 33.7101 32.2972V32.3072ZM18.0001 30.1066C11.3801 30.1066 6.00006 24.7254 6.00006 18.1038C6.00006 15.1331 7.10006 12.4225 8.90006 10.322L25.7801 27.206C23.6801 29.0064 20.9701 30.1066 18.0001 30.1066ZM9.63006 4.48064C9.25006 4.3106 8.83006 4.21058 8.40006 4.17057L9.09006 4.86073C9.27006 4.7407 9.44006 4.60067 9.63006 4.48064ZM17.0001 10.092V12.7726L19.0001 14.7731V10.092C19.0001 9.54183 18.5501 9.09172 18.0001 9.09172C17.4501 9.09172 17.0001 9.54183 17.0001 10.092Z\"/>',solid:'<path d=\"M16.8001 10.092C16.8001 9.4318 17.3401 8.89167 18.0001 8.89167C18.6601 8.89167 19.2001 9.4318 19.2001 10.092V14.9731L29.8101 25.5856C31.1901 23.4151 32.0001 20.8545 32.0001 18.0938C32.0001 10.372 25.7201 4.09055 18.0001 4.09055C15.2501 4.09055 12.6801 4.90074 10.5101 6.28106L16.8001 12.5725V10.092ZM31.6401 9.77188C31.8701 9.27176 32.0001 8.71163 32.0001 8.1315C32.0001 5.92098 30.2101 4.13056 28.0001 4.13056C27.4201 4.13056 26.8701 4.26059 26.3701 4.49064C28.5201 5.81095 30.3201 7.63138 31.6401 9.78188V9.77188ZM9.63006 4.48064C9.25006 4.3106 8.83006 4.21058 8.40006 4.17057L9.09006 4.86073C9.27006 4.7407 9.44006 4.60067 9.63006 4.48064ZM33.7001 32.2972L3.71006 2.29013C3.32006 1.90004 2.68006 1.90004 2.29006 2.29013C1.90006 2.68022 1.90006 3.32037 2.29006 3.71046L4.61006 6.031C4.23006 6.64115 4.00006 7.36132 4.00006 8.1315C4.00006 8.72163 4.13006 9.27176 4.36006 9.77188C4.86006 8.95169 5.44006 8.20151 6.08006 7.50135L7.48006 8.90168C5.32006 11.3623 4.00006 14.573 4.00006 18.0938C4.00006 21.6147 5.24006 24.6554 7.29006 27.0859L4.26006 30.4167C3.89006 30.8268 3.92006 31.457 4.33006 31.827C4.52006 31.9971 4.76006 32.0871 5.00006 32.0871C5.27006 32.0871 5.54006 31.9771 5.74006 31.757L8.69006 28.5063C11.1701 30.7268 14.4301 32.0871 18.0001 32.0871C21.5701 32.0871 24.7301 30.7568 27.1901 28.6063L32.2901 33.7075C32.4901 33.9075 32.7401 33.9976 33.0001 33.9976C33.2601 33.9976 33.5101 33.8975 33.7101 33.7075C34.1001 33.3174 34.1001 32.6873 33.7101 32.2972H33.7001Z\"/>'})];export{M as alarmOffIcon,L as alarmOffIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,WAAW;EAACC,CAAC,GAAC,CAAC,WAAW,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,m0DAAm0D;IAACC,KAAK,EAAC;EAAk9C,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,YAAY,EAACJ,CAAC,IAAIK,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}