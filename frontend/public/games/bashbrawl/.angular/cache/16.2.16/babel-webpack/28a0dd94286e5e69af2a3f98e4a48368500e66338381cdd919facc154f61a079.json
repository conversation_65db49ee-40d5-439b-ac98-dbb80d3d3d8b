{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst e = \"bubble-exclamation\",\n  l = [\"bubble-exclamation\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 16.4524C2 8.48293 9.19118 2 18.0249 2C26.8586 2 34.0498 8.48293 33.9997 16.4524C33.9697 20.0683 32.5252 23.5187 29.9935 26.0219V32.9694C29.9929 33.3466 29.7928 33.6933 29.4716 33.874C29.1503 34.0546 28.7591 34.0402 28.4511 33.8365L22.9125 30.2028C21.3222 30.6666 19.6776 30.9028 18.0249 30.9048C9.19118 30.9048 2 24.4218 2 16.4524ZM19.0265 19.6835C19.0265 20.2537 18.578 20.7159 18.0249 20.7159C17.7558 20.7159 17.4979 20.6043 17.3095 20.4062C17.1211 20.2081 17.018 19.9402 17.0233 19.6629V8.17324C17.0233 7.60311 17.4718 7.14093 18.0249 7.14093C18.578 7.14093 19.0265 7.60311 19.0265 8.17324V19.6835ZM28.3309 24.8245C28.1274 25.019 28.0113 25.2919 28.0104 25.578V31.0906L23.6236 28.2104C23.376 28.0491 23.0722 28.0076 22.7923 28.0969C21.2486 28.5911 19.6413 28.8416 18.0249 28.8402C10.2929 28.8402 4.00311 23.2863 4.00311 16.4524C4.00311 9.61848 10.2929 4.06464 18.0249 4.06464C25.7569 4.06464 32.0467 9.61848 32.0467 16.4524C32.0005 19.6563 30.6526 22.6934 28.3309 24.8245ZM19.357 24.236C19.357 24.9943 18.7606 25.609 18.0249 25.609C17.2892 25.609 16.6929 24.9943 16.6929 24.236C16.6929 23.4777 17.2892 22.863 18.0249 22.863C18.7606 22.863 19.357 23.4777 19.357 24.236Z\"/>',\n    solid: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M18.0249 2.5C9.19118 2.5 2 8.98293 2 16.9524C2 24.9218 9.19118 31.4048 18.0249 31.4048C19.6776 31.4028 21.3222 31.1666 22.9125 30.7028L28.4511 34.3365C28.7591 34.5402 29.1503 34.5546 29.4716 34.374C29.7928 34.1933 29.9929 33.8466 29.9935 33.4694V26.5219C32.5252 24.0187 33.9697 20.5683 33.9997 16.9524C34.0498 8.98293 26.8586 2.5 18.0249 2.5ZM16.9533 9.34422C16.9084 8.79683 17.1664 8.27005 17.6203 7.98213C18.0742 7.69421 18.6467 7.69421 19.1006 7.98213C19.5545 8.27005 19.8125 8.79683 19.7676 9.34422V19.4299C19.8125 19.9773 19.5545 20.5041 19.1006 20.792C18.6467 21.0799 18.0742 21.0799 17.6203 20.792C17.1664 20.5041 16.9084 19.9773 16.9533 19.4299V9.34422ZM16.4925 25.3244C16.4925 26.3906 17.331 27.2549 18.3654 27.2549V27.2445C19.3959 27.2446 20.2328 26.3866 20.2383 25.3244C20.2383 24.2583 19.3998 23.394 18.3654 23.394C17.331 23.394 16.4925 24.2583 16.4925 25.3244Z\"/>'\n  })];\nexport { l as bubbleExclamationIcon, e as bubbleExclamationIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "e", "l", "outline", "solid", "bubbleExclamationIcon", "bubbleExclamationIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/bubble-exclamation.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const e=\"bubble-exclamation\",l=[\"bubble-exclamation\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 16.4524C2 8.48293 9.19118 2 18.0249 2C26.8586 2 34.0498 8.48293 33.9997 16.4524C33.9697 20.0683 32.5252 23.5187 29.9935 26.0219V32.9694C29.9929 33.3466 29.7928 33.6933 29.4716 33.874C29.1503 34.0546 28.7591 34.0402 28.4511 33.8365L22.9125 30.2028C21.3222 30.6666 19.6776 30.9028 18.0249 30.9048C9.19118 30.9048 2 24.4218 2 16.4524ZM19.0265 19.6835C19.0265 20.2537 18.578 20.7159 18.0249 20.7159C17.7558 20.7159 17.4979 20.6043 17.3095 20.4062C17.1211 20.2081 17.018 19.9402 17.0233 19.6629V8.17324C17.0233 7.60311 17.4718 7.14093 18.0249 7.14093C18.578 7.14093 19.0265 7.60311 19.0265 8.17324V19.6835ZM28.3309 24.8245C28.1274 25.019 28.0113 25.2919 28.0104 25.578V31.0906L23.6236 28.2104C23.376 28.0491 23.0722 28.0076 22.7923 28.0969C21.2486 28.5911 19.6413 28.8416 18.0249 28.8402C10.2929 28.8402 4.00311 23.2863 4.00311 16.4524C4.00311 9.61848 10.2929 4.06464 18.0249 4.06464C25.7569 4.06464 32.0467 9.61848 32.0467 16.4524C32.0005 19.6563 30.6526 22.6934 28.3309 24.8245ZM19.357 24.236C19.357 24.9943 18.7606 25.609 18.0249 25.609C17.2892 25.609 16.6929 24.9943 16.6929 24.236C16.6929 23.4777 17.2892 22.863 18.0249 22.863C18.7606 22.863 19.357 23.4777 19.357 24.236Z\"/>',solid:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M18.0249 2.5C9.19118 2.5 2 8.98293 2 16.9524C2 24.9218 9.19118 31.4048 18.0249 31.4048C19.6776 31.4028 21.3222 31.1666 22.9125 30.7028L28.4511 34.3365C28.7591 34.5402 29.1503 34.5546 29.4716 34.374C29.7928 34.1933 29.9929 33.8466 29.9935 33.4694V26.5219C32.5252 24.0187 33.9697 20.5683 33.9997 16.9524C34.0498 8.98293 26.8586 2.5 18.0249 2.5ZM16.9533 9.34422C16.9084 8.79683 17.1664 8.27005 17.6203 7.98213C18.0742 7.69421 18.6467 7.69421 19.1006 7.98213C19.5545 8.27005 19.8125 8.79683 19.7676 9.34422V19.4299C19.8125 19.9773 19.5545 20.5041 19.1006 20.792C18.6467 21.0799 18.0742 21.0799 17.6203 20.792C17.1664 20.5041 16.9084 19.9773 16.9533 19.4299V9.34422ZM16.4925 25.3244C16.4925 26.3906 17.331 27.2549 18.3654 27.2549V27.2445C19.3959 27.2446 20.2328 26.3866 20.2383 25.3244C20.2383 24.2583 19.3998 23.394 18.3654 23.394C17.331 23.394 16.4925 24.2583 16.4925 25.3244Z\"/>'})];export{l as bubbleExclamationIcon,e as bubbleExclamationIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,oBAAoB;EAACC,CAAC,GAAC,CAAC,oBAAoB,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,4sCAA4sC;IAACC,KAAK,EAAC;EAA+5B,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,qBAAqB,EAACJ,CAAC,IAAIK,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}