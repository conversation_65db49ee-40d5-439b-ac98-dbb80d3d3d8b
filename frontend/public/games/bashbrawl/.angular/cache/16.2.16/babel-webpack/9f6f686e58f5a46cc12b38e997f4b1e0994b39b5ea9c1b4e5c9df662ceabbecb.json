{"ast": null, "code": "function s() {\n  return s => s.addInitializer(s => new t(s));\n}\nclass t {\n  constructor(s) {\n    this.host = s, this.host.addController(this);\n  }\n  hostUpdated() {\n    null !== this.host.pressed && void 0 !== this.host.pressed && (this.host.ariaPressed = this.host.pressed ? \"true\" : \"false\"), this.host.readonly && (this.host.ariaPressed = null);\n  }\n}\nexport { t as AriaPressedController, s as ariaPressed };", "map": {"version": 3, "names": ["s", "addInitializer", "t", "constructor", "host", "addController", "hostUpdated", "pressed", "ariaPressed", "readonly", "AriaPressedController"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/aria-pressed.controller.js"], "sourcesContent": ["function s(){return s=>s.addInitializer((s=>new t(s)))}class t{constructor(s){this.host=s,this.host.addController(this)}hostUpdated(){null!==this.host.pressed&&void 0!==this.host.pressed&&(this.host.ariaPressed=this.host.pressed?\"true\":\"false\"),this.host.readonly&&(this.host.ariaPressed=null)}}export{t as AriaPressedController,s as ariaPressed};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAOA,CAAC,IAAEA,CAAC,CAACC,cAAc,CAAED,CAAC,IAAE,IAAIE,CAAC,CAACF,CAAC,CAAE,CAAC;AAAA;AAAC,MAAME,CAAC;EAACC,WAAWA,CAACH,CAAC,EAAC;IAAC,IAAI,CAACI,IAAI,GAACJ,CAAC,EAAC,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;EAAA;EAACC,WAAWA,CAAA,EAAE;IAAC,IAAI,KAAG,IAAI,CAACF,IAAI,CAACG,OAAO,IAAE,KAAK,CAAC,KAAG,IAAI,CAACH,IAAI,CAACG,OAAO,KAAG,IAAI,CAACH,IAAI,CAACI,WAAW,GAAC,IAAI,CAACJ,IAAI,CAACG,OAAO,GAAC,MAAM,GAAC,OAAO,CAAC,EAAC,IAAI,CAACH,IAAI,CAACK,QAAQ,KAAG,IAAI,CAACL,IAAI,CAACI,WAAW,GAAC,IAAI,CAAC;EAAA;AAAC;AAAC,SAAON,CAAC,IAAIQ,qBAAqB,EAACV,CAAC,IAAIQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}