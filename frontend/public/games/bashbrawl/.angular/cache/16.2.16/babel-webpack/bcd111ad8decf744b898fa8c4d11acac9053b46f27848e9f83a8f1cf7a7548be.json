{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst r = \"heart\",\n  t = [\"heart\", C({\n    outline: '<path d=\"M25 3.99999C22.26 3.99999 19.69 5.24999 18 7.34999C16.31 5.25999 13.74 3.99999 11 3.99999C6.04 3.99999 2 8.03999 2 13.01C2 13.01 2 13.15 2.02 13.37C2.05 14.18 2.18 14.96 2.39 15.61C3.34 19.49 6.57 26.76 17.58 31.9C17.71 31.96 17.86 31.99 18 31.99C18.14 31.99 18.29 31.96 18.42 31.9C29.43 26.76 32.66 19.49 33.59 15.67C33.81 14.95 33.94 14.19 33.98 13.38C34 13.13 34 13 34 12.99C34 8.02999 29.96 3.98999 25 3.98999V3.99999ZM31.99 13.26C31.99 13.26 31.99 13.28 31.99 13.29C31.96 13.92 31.86 14.53 31.67 15.14C30.82 18.62 27.92 25.12 18 29.89C8.09 25.13 5.18 18.62 4.31 15.08C4.14 14.53 4.04 13.92 4.01 13.26C4 13.1 4 13.01 4 13C4 9.13999 7.14 5.99999 11 5.99999C13.54 5.99999 15.89 7.37999 17.12 9.60999C17.47 10.25 18.52 10.25 18.87 9.60999C20.11 7.37999 22.45 5.99999 24.99 5.99999C28.85 5.99999 31.99 9.13999 31.99 12.99C31.99 13 31.99 13.1 31.98 13.26H31.99Z\"/>',\n    solid: '<path d=\"M25 3.99999C22.26 3.99999 19.69 5.24999 18 7.34999C16.31 5.25999 13.74 3.99999 11 3.99999C6.04 3.99999 2 8.03999 2 13.01C2 13.01 2 13.15 2.02 13.37C2.05 14.18 2.18 14.96 2.39 15.61C3.34 19.49 6.57 26.76 17.58 31.9C17.71 31.96 17.86 31.99 18 31.99C18.14 31.99 18.29 31.96 18.42 31.9C29.43 26.76 32.66 19.49 33.59 15.67C33.81 14.95 33.94 14.19 33.98 13.38C34 13.13 34 13 34 12.99C34 8.02999 29.96 3.98999 25 3.98999V3.99999Z\"/>'\n  })];\nexport { t as heartIcon, r as heartIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "r", "t", "outline", "solid", "heartIcon", "heartIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/heart.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const r=\"heart\",t=[\"heart\",C({outline:'<path d=\"M25 3.99999C22.26 3.99999 19.69 5.24999 18 7.34999C16.31 5.25999 13.74 3.99999 11 3.99999C6.04 3.99999 2 8.03999 2 13.01C2 13.01 2 13.15 2.02 13.37C2.05 14.18 2.18 14.96 2.39 15.61C3.34 19.49 6.57 26.76 17.58 31.9C17.71 31.96 17.86 31.99 18 31.99C18.14 31.99 18.29 31.96 18.42 31.9C29.43 26.76 32.66 19.49 33.59 15.67C33.81 14.95 33.94 14.19 33.98 13.38C34 13.13 34 13 34 12.99C34 8.02999 29.96 3.98999 25 3.98999V3.99999ZM31.99 13.26C31.99 13.26 31.99 13.28 31.99 13.29C31.96 13.92 31.86 14.53 31.67 15.14C30.82 18.62 27.92 25.12 18 29.89C8.09 25.13 5.18 18.62 4.31 15.08C4.14 14.53 4.04 13.92 4.01 13.26C4 13.1 4 13.01 4 13C4 9.13999 7.14 5.99999 11 5.99999C13.54 5.99999 15.89 7.37999 17.12 9.60999C17.47 10.25 18.52 10.25 18.87 9.60999C20.11 7.37999 22.45 5.99999 24.99 5.99999C28.85 5.99999 31.99 9.13999 31.99 12.99C31.99 13 31.99 13.1 31.98 13.26H31.99Z\"/>',solid:'<path d=\"M25 3.99999C22.26 3.99999 19.69 5.24999 18 7.34999C16.31 5.25999 13.74 3.99999 11 3.99999C6.04 3.99999 2 8.03999 2 13.01C2 13.01 2 13.15 2.02 13.37C2.05 14.18 2.18 14.96 2.39 15.61C3.34 19.49 6.57 26.76 17.58 31.9C17.71 31.96 17.86 31.99 18 31.99C18.14 31.99 18.29 31.96 18.42 31.9C29.43 26.76 32.66 19.49 33.59 15.67C33.81 14.95 33.94 14.19 33.98 13.38C34 13.13 34 13 34 12.99C34 8.02999 29.96 3.98999 25 3.98999V3.99999Z\"/>'})];export{t as heartIcon,r as heartIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,OAAO;EAACC,CAAC,GAAC,CAAC,OAAO,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,02BAA02B;IAACC,KAAK,EAAC;EAAob,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,SAAS,EAACJ,CAAC,IAAIK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}