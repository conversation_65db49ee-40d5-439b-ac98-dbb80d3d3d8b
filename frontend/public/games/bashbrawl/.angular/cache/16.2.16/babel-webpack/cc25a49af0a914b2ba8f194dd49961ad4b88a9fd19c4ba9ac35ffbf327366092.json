{"ast": null, "code": "export default function _complement(f) {\n  return function () {\n    return !f.apply(this, arguments);\n  };\n}", "map": {"version": 3, "names": ["_complement", "f", "apply", "arguments"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_complement.js"], "sourcesContent": ["export default function _complement(f) {\n  return function () {\n    return !f.apply(this, arguments);\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,WAAWA,CAACC,CAAC,EAAE;EACrC,OAAO,YAAY;IACjB,OAAO,CAACA,CAAC,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAClC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}