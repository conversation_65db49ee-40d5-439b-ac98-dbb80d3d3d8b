{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst e = \"landscape\",\n  L = [\"landscape\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4 6H32C33.1046 6 34 6.89543 34 8V28C34 29.1046 33.1046 30 32 30H4C2.89543 30 2 29.1046 2 28V8C2 6.89543 2.89543 6 4 6ZM27.04 22.2C27.3172 22.1991 27.5815 22.0832 27.77 21.88L31.36 18L27.77 14.12C27.5271 13.8574 27.1625 13.7444 26.8136 13.8235C26.4647 13.9026 26.1845 14.1618 26.0786 14.5035C25.9727 14.8452 26.0571 15.2174 26.3 15.48L27.71 17H8.29L9.7 15.48C9.94294 15.2174 10.0273 14.8452 9.9214 14.5035C9.81546 14.1618 9.53528 13.9026 9.1864 13.8235C8.83752 13.7444 8.47294 13.8574 8.23 14.12L4.64 18L8.27 21.88C8.45847 22.0832 8.72284 22.1991 9 22.2C9.23874 22.1894 9.46578 22.0936 9.64 21.93C10.0448 21.5567 10.0716 20.9264 9.7 20.52L8.29 19H27.71L26.3 20.52C25.9284 20.9264 25.9552 21.5567 26.36 21.93C26.5444 22.1027 26.7874 22.1991 27.04 22.2Z\"/>',\n    solid: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 6H4C2.89543 6 2 6.89543 2 8V28C2 29.1046 2.89543 30 4 30H32C33.1046 30 34 29.1046 34 28V8C34 6.89543 33.1046 6 32 6ZM32 28H4V8H32V28ZM9.55 21.79C9.39898 21.9257 9.20301 22.0005 9 22C8.76467 22.0114 8.53679 21.9159 8.38 21.74L4.91 18L8.42 14.26C8.56864 14.1079 8.77233 14.0222 8.985 14.0222C9.19767 14.0222 9.40136 14.1079 9.55 14.26C9.86151 14.5723 9.86151 15.0777 9.55 15.39L7.83 17.25H28.21L26.49 15.39C26.1785 15.0777 26.1785 14.5723 26.49 14.26C26.6386 14.1079 26.8423 14.0222 27.055 14.0222C27.2677 14.0222 27.4714 14.1079 27.62 14.26L31.09 18L27.58 21.74C27.4323 21.9051 27.2215 21.9996 27 22C26.797 22.0005 26.601 21.9257 26.45 21.79C26.1385 21.4777 26.1385 20.9723 26.45 20.66L28.17 18.8H7.83L9.55 20.66C9.86151 20.9723 9.86151 21.4777 9.55 21.79Z\"/>'\n  })];\nexport { L as landscapeIcon, e as landscapeIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "e", "L", "outline", "solid", "landscapeIcon", "landscapeIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/landscape.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const e=\"landscape\",L=[\"landscape\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4 6H32C33.1046 6 34 6.89543 34 8V28C34 29.1046 33.1046 30 32 30H4C2.89543 30 2 29.1046 2 28V8C2 6.89543 2.89543 6 4 6ZM27.04 22.2C27.3172 22.1991 27.5815 22.0832 27.77 21.88L31.36 18L27.77 14.12C27.5271 13.8574 27.1625 13.7444 26.8136 13.8235C26.4647 13.9026 26.1845 14.1618 26.0786 14.5035C25.9727 14.8452 26.0571 15.2174 26.3 15.48L27.71 17H8.29L9.7 15.48C9.94294 15.2174 10.0273 14.8452 9.9214 14.5035C9.81546 14.1618 9.53528 13.9026 9.1864 13.8235C8.83752 13.7444 8.47294 13.8574 8.23 14.12L4.64 18L8.27 21.88C8.45847 22.0832 8.72284 22.1991 9 22.2C9.23874 22.1894 9.46578 22.0936 9.64 21.93C10.0448 21.5567 10.0716 20.9264 9.7 20.52L8.29 19H27.71L26.3 20.52C25.9284 20.9264 25.9552 21.5567 26.36 21.93C26.5444 22.1027 26.7874 22.1991 27.04 22.2Z\"/>',solid:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 6H4C2.89543 6 2 6.89543 2 8V28C2 29.1046 2.89543 30 4 30H32C33.1046 30 34 29.1046 34 28V8C34 6.89543 33.1046 6 32 6ZM32 28H4V8H32V28ZM9.55 21.79C9.39898 21.9257 9.20301 22.0005 9 22C8.76467 22.0114 8.53679 21.9159 8.38 21.74L4.91 18L8.42 14.26C8.56864 14.1079 8.77233 14.0222 8.985 14.0222C9.19767 14.0222 9.40136 14.1079 9.55 14.26C9.86151 14.5723 9.86151 15.0777 9.55 15.39L7.83 17.25H28.21L26.49 15.39C26.1785 15.0777 26.1785 14.5723 26.49 14.26C26.6386 14.1079 26.8423 14.0222 27.055 14.0222C27.2677 14.0222 27.4714 14.1079 27.62 14.26L31.09 18L27.58 21.74C27.4323 21.9051 27.2215 21.9996 27 22C26.797 22.0005 26.601 21.9257 26.45 21.79C26.1385 21.4777 26.1385 20.9723 26.45 20.66L28.17 18.8H7.83L9.55 20.66C9.86151 20.9723 9.86151 21.4777 9.55 21.79Z\"/>'})];export{L as landscapeIcon,e as landscapeIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,WAAW;EAACC,CAAC,GAAC,CAAC,WAAW,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,qyBAAqyB;IAACC,KAAK,EAAC;EAA6yB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,aAAa,EAACJ,CAAC,IAAIK,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}