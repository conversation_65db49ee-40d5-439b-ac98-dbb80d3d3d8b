{"ast": null, "code": "import { renderIcon as e } from \"../icon.renderer.js\";\nconst o = \"window-max\",\n  d = [\"window-max\", e({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.89014 9H27.8901C28.9947 9 29.8901 9.89543 29.8901 11V25C29.8901 26.1046 28.9947 27 27.8901 27H7.89014C6.78557 27 5.89014 26.1046 5.89014 25V11C5.89014 9.89543 6.78557 9 7.89014 9ZM7.89014 11V25H27.8901V11H7.89014Z\"/>'\n  })];\nexport { d as windowMaxIcon, o as windowMaxIconName };", "map": {"version": 3, "names": ["renderIcon", "e", "o", "d", "outline", "windowMaxIcon", "windowMaxIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/window-max.js"], "sourcesContent": ["import{renderIcon as e}from\"../icon.renderer.js\";const o=\"window-max\",d=[\"window-max\",e({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.89014 9H27.8901C28.9947 9 29.8901 9.89543 29.8901 11V25C29.8901 26.1046 28.9947 27 27.8901 27H7.89014C6.78557 27 5.89014 26.1046 5.89014 25V11C5.89014 9.89543 6.78557 9 7.89014 9ZM7.89014 11V25H27.8901V11H7.89014Z\"/>'})];export{d as windowMaxIcon,o as windowMaxIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,YAAY;EAACC,CAAC,GAAC,CAAC,YAAY,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAA8Q,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,aAAa,EAACH,CAAC,IAAII,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}