{"ast": null, "code": "import { ClarityIcons as s } from \"../icon.service.js\";\nimport { bankIcon as o } from \"../shapes/bank.js\";\nimport { bitcoinIcon as r } from \"../shapes/bitcoin.js\";\nimport { calculatorIcon as p } from \"../shapes/calculator.js\";\nimport { coinBagIcon as m } from \"../shapes/coin-bag.js\";\nimport { creditCardIcon as e } from \"../shapes/credit-card.js\";\nimport { dollarBillIcon as a } from \"../shapes/dollar-bill.js\";\nimport { dollarIcon as i } from \"../shapes/dollar.js\";\nimport { eCheckIcon as t } from \"../shapes/e-check.js\";\nimport { employeeGroupIcon as f } from \"../shapes/employee-group.js\";\nimport { employeeIcon as h } from \"../shapes/employee.js\";\nimport { euroIcon as j } from \"../shapes/euro.js\";\nimport { factoryIcon as c } from \"../shapes/factory.js\";\nimport { pesoIcon as n } from \"../shapes/peso.js\";\nimport { piggyBankIcon as l, piggyBankIconName as d } from \"../shapes/piggy-bank.js\";\nimport { poundIcon as g } from \"../shapes/pound.js\";\nimport { rubleIcon as b } from \"../shapes/ruble.js\";\nimport { rupeeIcon as u } from \"../shapes/rupee.js\";\nimport { shoppingBagIcon as y } from \"../shapes/shopping-bag.js\";\nimport { shoppingCartIcon as k } from \"../shapes/shopping-cart.js\";\nimport { storeIcon as v } from \"../shapes/store.js\";\nimport { walletIcon as w } from \"../shapes/wallet.js\";\nimport { wonIcon as x } from \"../shapes/won.js\";\nimport { yenIcon as A } from \"../shapes/yen.js\";\nconst I = [o, r, p, e, m, i, a, t, f, h, j, c, n, l, g, b, u, y, k, v, w, x, A],\n  q = [[d, [\"savings\"]]];\nfunction z() {\n  s.addIcons(...I), s.addAliases(...q);\n}\nexport { q as commerceCollectionAliases, I as commerceCollectionIcons, z as loadCommerceIconSet };", "map": {"version": 3, "names": ["ClarityIcons", "s", "bankIcon", "o", "bitcoinIcon", "r", "calculatorIcon", "p", "coinBagIcon", "m", "creditCardIcon", "e", "dollarBillIcon", "a", "dollarIcon", "i", "eCheckIcon", "t", "employeeGroupIcon", "f", "employeeIcon", "h", "euroIcon", "j", "factoryIcon", "c", "pesoIcon", "n", "piggyBankIcon", "l", "piggyBankIconName", "d", "poundIcon", "g", "rubleIcon", "b", "rupeeIcon", "u", "shoppingBagIcon", "y", "shoppingCartIcon", "k", "storeIcon", "v", "walletIcon", "w", "wonIcon", "x", "yenIcon", "A", "I", "q", "z", "addIcons", "addAliases", "commerceCollectionAliases", "commerceCollectionIcons", "loadCommerceIconSet"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/collections/commerce.js"], "sourcesContent": ["import{ClarityIcons as s}from\"../icon.service.js\";import{bankIcon as o}from\"../shapes/bank.js\";import{bitcoinIcon as r}from\"../shapes/bitcoin.js\";import{calculatorIcon as p}from\"../shapes/calculator.js\";import{coinBagIcon as m}from\"../shapes/coin-bag.js\";import{creditCardIcon as e}from\"../shapes/credit-card.js\";import{dollarBillIcon as a}from\"../shapes/dollar-bill.js\";import{dollarIcon as i}from\"../shapes/dollar.js\";import{eCheckIcon as t}from\"../shapes/e-check.js\";import{employeeGroupIcon as f}from\"../shapes/employee-group.js\";import{employeeIcon as h}from\"../shapes/employee.js\";import{euroIcon as j}from\"../shapes/euro.js\";import{factoryIcon as c}from\"../shapes/factory.js\";import{pesoIcon as n}from\"../shapes/peso.js\";import{piggyBankIcon as l,piggyBankIconName as d}from\"../shapes/piggy-bank.js\";import{poundIcon as g}from\"../shapes/pound.js\";import{rubleIcon as b}from\"../shapes/ruble.js\";import{rupeeIcon as u}from\"../shapes/rupee.js\";import{shoppingBagIcon as y}from\"../shapes/shopping-bag.js\";import{shoppingCartIcon as k}from\"../shapes/shopping-cart.js\";import{storeIcon as v}from\"../shapes/store.js\";import{walletIcon as w}from\"../shapes/wallet.js\";import{wonIcon as x}from\"../shapes/won.js\";import{yenIcon as A}from\"../shapes/yen.js\";const I=[o,r,p,e,m,i,a,t,f,h,j,c,n,l,g,b,u,y,k,v,w,x,A],q=[[d,[\"savings\"]]];function z(){s.addIcons(...I),s.addAliases(...q)}export{q as commerceCollectionAliases,I as commerceCollectionIcons,z as loadCommerceIconSet};\n"], "mappings": "AAAA,SAAOA,YAAY,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,iBAAiB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,aAAa,IAAIC,CAAC,EAACC,iBAAiB,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,OAAO,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAAOC,OAAO,IAAIC,CAAC,QAAK,kBAAkB;AAAC,MAAMC,CAAC,GAAC,CAAC/C,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACF,CAAC,EAACM,CAAC,EAACF,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,CAAC;EAACE,CAAC,GAAC,CAAC,CAACpB,CAAC,EAAC,CAAC,SAAS,CAAC,CAAC,CAAC;AAAC,SAASqB,CAACA,CAAA,EAAE;EAACnD,CAAC,CAACoD,QAAQ,CAAC,GAAGH,CAAC,CAAC,EAACjD,CAAC,CAACqD,UAAU,CAAC,GAAGH,CAAC,CAAC;AAAA;AAAC,SAAOA,CAAC,IAAII,yBAAyB,EAACL,CAAC,IAAIM,uBAAuB,EAACJ,CAAC,IAAIK,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}