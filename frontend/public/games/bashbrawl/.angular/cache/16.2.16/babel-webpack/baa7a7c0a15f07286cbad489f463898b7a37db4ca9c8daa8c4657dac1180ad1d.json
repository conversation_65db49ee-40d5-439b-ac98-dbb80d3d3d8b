{"ast": null, "code": "import _isPlaceholder from \"./_isPlaceholder.js\";\n/**\n * Optimized internal one-arity curry function.\n *\n * @private\n * @category Function\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\n\nexport default function _curry1(fn) {\n  return function f1(a) {\n    if (arguments.length === 0 || _isPlaceholder(a)) {\n      return f1;\n    } else {\n      return fn.apply(this, arguments);\n    }\n  };\n}", "map": {"version": 3, "names": ["_isPlaceholder", "_curry1", "fn", "f1", "a", "arguments", "length", "apply"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_curry1.js"], "sourcesContent": ["import _isPlaceholder from \"./_isPlaceholder.js\";\n/**\n * Optimized internal one-arity curry function.\n *\n * @private\n * @category Function\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\n\nexport default function _curry1(fn) {\n  return function f1(a) {\n    if (arguments.length === 0 || _isPlaceholder(a)) {\n      return f1;\n    } else {\n      return fn.apply(this, arguments);\n    }\n  };\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,qBAAqB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,OAAOA,CAACC,EAAE,EAAE;EAClC,OAAO,SAASC,EAAEA,CAACC,CAAC,EAAE;IACpB,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,IAAIN,cAAc,CAACI,CAAC,CAAC,EAAE;MAC/C,OAAOD,EAAE;IACX,CAAC,MAAM;MACL,OAAOD,EAAE,CAACK,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;IAClC;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}