{"ast": null, "code": "import { renderIcon as L } from \"../icon.renderer.js\";\nconst C = \"star\",\n  r = [\"star\", L({\n    outline: '<path d=\"M27.2583 33.99C27.0884 33.99 26.9284 33.95 26.7785 33.87L17.9926 29.0116L9.20663 33.87C8.87678 34.06 8.45697 34.03 8.14712 33.8101C7.83726 33.5901 7.67733 33.2102 7.73731 32.8304L9.42652 22.4736L2.27983 15.1559C2.01995 14.886 1.92999 14.4961 2.04994 14.1362C2.16988 13.7763 2.47974 13.5264 2.84957 13.4664L12.685 11.9669L17.083 2.57982C17.2529 2.22993 17.6027 2 17.9926 2C18.3824 2 18.7322 2.21993 18.9021 2.57982L23.3101 11.9669L33.1456 13.4664C33.5154 13.5264 33.8252 13.7863 33.9452 14.1362C34.0651 14.4861 33.9752 14.886 33.7153 15.1559L26.5686 22.4836L28.2578 32.8404C28.3178 33.2202 28.1579 33.6001 27.848 33.8201C27.6781 33.94 27.4782 34 27.2683 34L27.2583 33.99ZM17.9926 26.8722C18.1625 26.8722 18.3224 26.9122 18.4723 26.9922L25.9389 31.1209L24.4996 22.3037C24.4496 21.9938 24.5495 21.6739 24.7694 21.4439L30.9066 15.1559L22.4705 13.8663C22.1406 13.8163 21.8608 13.6064 21.7208 13.3065L17.9926 5.35895L14.2643 13.3065C14.1244 13.6064 13.8445 13.8163 13.5146 13.8663L5.07854 15.1559L11.2157 21.4439C11.4356 21.6739 11.5355 21.9938 11.4856 22.3037L10.0462 31.1209L17.5128 26.9922C17.6627 26.9122 17.8326 26.8722 17.9926 26.8722Z\"/>',\n    solid: '<path d=\"M33.95 14.1356C33.83 13.7758 33.52 13.5259 33.15 13.4659L23.31 11.9664L18.91 2.57979C18.74 2.22992 18.39 2 18 2C17.61 2 17.26 2.21992 17.09 2.57979L12.68 11.9664L2.84996 13.4659C2.47996 13.5259 2.16996 13.7858 2.04996 14.1356C1.92996 14.4855 2.01996 14.8854 2.27996 15.1553L9.42996 22.4826L7.73996 32.8389C7.67996 33.2188 7.83996 33.5986 8.14996 33.8186C8.45996 34.0385 8.87996 34.0585 9.20996 33.8785L18 29.0203L26.79 33.8785C26.94 33.9585 27.11 33.9985 27.27 33.9985C27.47 33.9985 27.67 33.9385 27.85 33.8186C28.16 33.5986 28.32 33.2188 28.26 32.8389L26.57 22.4826L33.72 15.1553C33.98 14.8854 34.07 14.4955 33.95 14.1356Z\"/>'\n  })];\nexport { r as starIcon, C as starIconName };", "map": {"version": 3, "names": ["renderIcon", "L", "C", "r", "outline", "solid", "starIcon", "starIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/star.js"], "sourcesContent": ["import{renderIcon as L}from\"../icon.renderer.js\";const C=\"star\",r=[\"star\",L({outline:'<path d=\"M27.2583 33.99C27.0884 33.99 26.9284 33.95 26.7785 33.87L17.9926 29.0116L9.20663 33.87C8.87678 34.06 8.45697 34.03 8.14712 33.8101C7.83726 33.5901 7.67733 33.2102 7.73731 32.8304L9.42652 22.4736L2.27983 15.1559C2.01995 14.886 1.92999 14.4961 2.04994 14.1362C2.16988 13.7763 2.47974 13.5264 2.84957 13.4664L12.685 11.9669L17.083 2.57982C17.2529 2.22993 17.6027 2 17.9926 2C18.3824 2 18.7322 2.21993 18.9021 2.57982L23.3101 11.9669L33.1456 13.4664C33.5154 13.5264 33.8252 13.7863 33.9452 14.1362C34.0651 14.4861 33.9752 14.886 33.7153 15.1559L26.5686 22.4836L28.2578 32.8404C28.3178 33.2202 28.1579 33.6001 27.848 33.8201C27.6781 33.94 27.4782 34 27.2683 34L27.2583 33.99ZM17.9926 26.8722C18.1625 26.8722 18.3224 26.9122 18.4723 26.9922L25.9389 31.1209L24.4996 22.3037C24.4496 21.9938 24.5495 21.6739 24.7694 21.4439L30.9066 15.1559L22.4705 13.8663C22.1406 13.8163 21.8608 13.6064 21.7208 13.3065L17.9926 5.35895L14.2643 13.3065C14.1244 13.6064 13.8445 13.8163 13.5146 13.8663L5.07854 15.1559L11.2157 21.4439C11.4356 21.6739 11.5355 21.9938 11.4856 22.3037L10.0462 31.1209L17.5128 26.9922C17.6627 26.9122 17.8326 26.8722 17.9926 26.8722Z\"/>',solid:'<path d=\"M33.95 14.1356C33.83 13.7758 33.52 13.5259 33.15 13.4659L23.31 11.9664L18.91 2.57979C18.74 2.22992 18.39 2 18 2C17.61 2 17.26 2.21992 17.09 2.57979L12.68 11.9664L2.84996 13.4659C2.47996 13.5259 2.16996 13.7858 2.04996 14.1356C1.92996 14.4855 2.01996 14.8854 2.27996 15.1553L9.42996 22.4826L7.73996 32.8389C7.67996 33.2188 7.83996 33.5986 8.14996 33.8186C8.45996 34.0385 8.87996 34.0585 9.20996 33.8785L18 29.0203L26.79 33.8785C26.94 33.9585 27.11 33.9985 27.27 33.9985C27.47 33.9985 27.67 33.9385 27.85 33.8186C28.16 33.5986 28.32 33.2188 28.26 32.8389L26.57 22.4826L33.72 15.1553C33.98 14.8854 34.07 14.4955 33.95 14.1356Z\"/>'})];export{r as starIcon,C as starIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,MAAM;EAACC,CAAC,GAAC,CAAC,MAAM,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,6nCAA6nC;IAACC,KAAK,EAAC;EAA6nB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,QAAQ,EAACJ,CAAC,IAAIK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}