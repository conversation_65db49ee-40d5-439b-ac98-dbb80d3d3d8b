{"ast": null, "code": "export default {\n  foreground: '#708284',\n  background: '#001e27',\n  cursor: '#708284',\n  black: '#002831',\n  brightBlack: '#001e27',\n  red: '#d11c24',\n  brightRed: '#bd3613',\n  green: '#738a05',\n  brightGreen: '#475b62',\n  yellow: '#a57706',\n  brightYellow: '#536870',\n  blue: '#2176c7',\n  brightBlue: '#708284',\n  magenta: '#c61c6f',\n  brightMagenta: '#5956ba',\n  cyan: '#259286',\n  brightCyan: '#819090',\n  white: '#eae3cb',\n  brightWhite: '#fcf4dc'\n};", "map": {"version": 3, "names": ["foreground", "background", "cursor", "black", "brightBlack", "red", "brightRed", "green", "bright<PERSON><PERSON>", "yellow", "<PERSON><PERSON><PERSON><PERSON>", "blue", "brightBlue", "magenta", "brightMagenta", "cyan", "bright<PERSON>yan", "white", "bright<PERSON><PERSON>e"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/src/app/terminals/terminal-themes/Solarized_Dark.ts"], "sourcesContent": ["export default {\n  foreground: '#708284',\n  background: '#001e27',\n  cursor: '#708284',\n\n  black: '#002831',\n  brightBlack: '#001e27',\n\n  red: '#d11c24',\n  brightRed: '#bd3613',\n\n  green: '#738a05',\n  brightGreen: '#475b62',\n\n  yellow: '#a57706',\n  brightYellow: '#536870',\n\n  blue: '#2176c7',\n  brightBlue: '#708284',\n\n  magenta: '#c61c6f',\n  brightMagenta: '#5956ba',\n\n  cyan: '#259286',\n  brightCyan: '#819090',\n\n  white: '#eae3cb',\n  brightWhite: '#fcf4dc',\n};\n"], "mappings": "AAAA,eAAe;EACbA,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,SAAS;EACrBC,MAAM,EAAE,SAAS;EAEjBC,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE,SAAS;EAEtBC,GAAG,EAAE,SAAS;EACdC,SAAS,EAAE,SAAS;EAEpBC,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE,SAAS;EAEtBC,MAAM,EAAE,SAAS;EACjBC,YAAY,EAAE,SAAS;EAEvBC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,SAAS;EAErBC,OAAO,EAAE,SAAS;EAClBC,aAAa,EAAE,SAAS;EAExBC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,SAAS;EAErBC,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE;CACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}