{"ast": null, "code": "import _curry2 from \"./internal/_curry2.js\";\nimport _Set from \"./internal/_Set.js\";\nimport reject from \"./reject.js\";\n/**\n * Returns a new list without values in the first argument.\n * [`<PERSON>.equals`](#equals) is used to determine equality.\n *\n * Acts as a transducer if a transformer is given in list position.\n *\n * @func\n * @memberOf R\n * @since v0.19.0\n * @category List\n * @sig [a] -> [a] -> [a]\n * @param {Array} list1 The values to be removed from `list2`.\n * @param {Array} list2 The array to remove values from.\n * @return {Array} The new array without values in `list1`.\n * @see R.transduce, R.difference, R.remove\n * @example\n *\n *      R.without([1, 2], [1, 2, 1, 3, 4]); //=> [3, 4]\n */\n\nvar without = /*#__PURE__*/\n_curry2(function without(xs, list) {\n  var toRemove = new _Set();\n  for (var i = 0; i < xs.length; i += 1) {\n    toRemove.add(xs[i]);\n  }\n  return reject(toRemove.has.bind(toRemove), list);\n});\nexport default without;", "map": {"version": 3, "names": ["_curry2", "_Set", "reject", "without", "xs", "list", "toRemove", "i", "length", "add", "has", "bind"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/without.js"], "sourcesContent": ["import _curry2 from \"./internal/_curry2.js\";\nimport _Set from \"./internal/_Set.js\";\nimport reject from \"./reject.js\";\n/**\n * Returns a new list without values in the first argument.\n * [`<PERSON>.equals`](#equals) is used to determine equality.\n *\n * Acts as a transducer if a transformer is given in list position.\n *\n * @func\n * @memberOf R\n * @since v0.19.0\n * @category List\n * @sig [a] -> [a] -> [a]\n * @param {Array} list1 The values to be removed from `list2`.\n * @param {Array} list2 The array to remove values from.\n * @return {Array} The new array without values in `list1`.\n * @see R.transduce, R.difference, R.remove\n * @example\n *\n *      R.without([1, 2], [1, 2, 1, 3, 4]); //=> [3, 4]\n */\n\nvar without =\n/*#__PURE__*/\n_curry2(function without(xs, list) {\n  var toRemove = new _Set();\n\n  for (var i = 0; i < xs.length; i += 1) {\n    toRemove.add(xs[i]);\n  }\n\n  return reject(toRemove.has.bind(toRemove), list);\n});\n\nexport default without;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,MAAM,MAAM,aAAa;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,OAAO,GACX;AACAH,OAAO,CAAC,SAASG,OAAOA,CAACC,EAAE,EAAEC,IAAI,EAAE;EACjC,IAAIC,QAAQ,GAAG,IAAIL,IAAI,CAAC,CAAC;EAEzB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,EAAE,CAACI,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACrCD,QAAQ,CAACG,GAAG,CAACL,EAAE,CAACG,CAAC,CAAC,CAAC;EACrB;EAEA,OAAOL,MAAM,CAACI,QAAQ,CAACI,GAAG,CAACC,IAAI,CAACL,QAAQ,CAAC,EAAED,IAAI,CAAC;AAClD,CAAC,CAAC;AAEF,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}