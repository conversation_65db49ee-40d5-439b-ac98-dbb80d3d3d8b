{"ast": null, "code": "import { createId as t } from \"./identity.js\";\nimport { setAttributes as i } from \"./dom.js\";\nfunction r(r, a) {\n  i(r, [\"aria-describedby\", !!a.length && a.map(i => i.id = t()).join(\" \")]);\n}\nfunction a(t) {\n  return t.hasAttribute(\"aria-label\") || t.hasAttribute(\"aria-labelledby\");\n}\nexport { r as describeElementByElements, a as hasAriaLabelTypeAttr };", "map": {"version": 3, "names": ["createId", "t", "setAttributes", "i", "r", "a", "length", "map", "id", "join", "hasAttribute", "describeElementByElements", "hasAriaLabelTypeAttr"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/a11y.js"], "sourcesContent": ["import{createId as t}from\"./identity.js\";import{setAttributes as i}from\"./dom.js\";function r(r,a){i(r,[\"aria-describedby\",!!a.length&&a.map((i=>i.id=t())).join(\" \")])}function a(t){return t.hasAttribute(\"aria-label\")||t.hasAttribute(\"aria-labelledby\")}export{r as describeElementByElements,a as hasAriaLabelTypeAttr};\n"], "mappings": "AAAA,SAAOA,QAAQ,IAAIC,CAAC,QAAK,eAAe;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,UAAU;AAAC,SAASC,CAACA,CAACA,CAAC,EAACC,CAAC,EAAC;EAACF,CAAC,CAACC,CAAC,EAAC,CAAC,kBAAkB,EAAC,CAAC,CAACC,CAAC,CAACC,MAAM,IAAED,CAAC,CAACE,GAAG,CAAEJ,CAAC,IAAEA,CAAC,CAACK,EAAE,GAACP,CAAC,CAAC,CAAE,CAAC,CAACQ,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAAA;AAAC,SAASJ,CAACA,CAACJ,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACS,YAAY,CAAC,YAAY,CAAC,IAAET,CAAC,CAACS,YAAY,CAAC,iBAAiB,CAAC;AAAA;AAAC,SAAON,CAAC,IAAIO,yBAAyB,EAACN,CAAC,IAAIO,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}