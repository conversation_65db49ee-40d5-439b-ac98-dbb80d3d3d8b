{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"shield-x\",\n  e = [\"shield-x\", C({\n    outline: '<path d=\"M31.2597 7.81C28.9889 7.21 26.7781 6.51 24.6374 5.55C22.5266 4.64 20.4959 3.56 18.5552 2.34L18.005 2L17.4648 2.35C15.5241 3.57 13.4934 4.65 11.3826 5.56C9.24187 6.52 7.02108 7.21 4.75027 7.81L4 8V15.43C4 28.71 17.3248 33.75 17.6549 33.88L17.995 34L18.3351 33.88C18.4752 33.88 32 28.83 32 15.43V8L31.2497 7.81H31.2597ZM30.0093 15.43C30.0093 26.43 20.0057 31.04 18.005 31.86C16.0043 31.04 6.00071 26.42 6.00071 15.43V9.55C8.11147 8.94 10.1722 8.28 12.1829 7.39C14.1936 6.52 16.1343 5.52 18.005 4.39C19.8757 5.52 21.8164 6.52 23.8271 7.39C25.8378 8.28 27.8985 8.94 30.0093 9.55V15.43ZM23.717 11.29C23.3269 10.9 22.6967 10.9 22.3065 11.29L18.015 15.58L13.7235 11.29C13.3333 10.9 12.7031 10.9 12.313 11.29C11.9228 11.68 11.9228 12.31 12.313 12.7L16.6045 16.99L12.313 21.28C11.9228 21.67 11.9228 22.3 12.313 22.69C12.513 22.89 12.7631 22.98 13.0232 22.98C13.2833 22.98 13.5334 22.88 13.7335 22.69L18.025 18.4L22.3165 22.69C22.5166 22.89 22.7667 22.98 23.0268 22.98C23.2869 22.98 23.537 22.88 23.737 22.69C24.1272 22.3 24.1272 21.67 23.737 21.28L19.4455 16.99L23.737 12.7C24.1272 12.31 24.1272 11.68 23.737 11.29H23.717Z\"/>',\n    solid: '<path d=\"M31.2597 7.81C28.9889 7.21 26.7781 6.51 24.6374 5.55C22.5266 4.64 20.4959 3.56 18.5552 2.34L18.005 2L17.4648 2.35C15.5241 3.57 13.4934 4.65 11.3826 5.56C9.24187 6.52 7.02108 7.21 4.75027 7.81L4 8V15.43C4 28.71 17.3248 33.75 17.6549 33.88L17.995 34L18.3351 33.88C18.4752 33.88 32 28.83 32 15.43V8L31.2497 7.81H31.2597ZM23.8571 21.15C24.0872 21.38 24.2072 21.68 24.2072 22C24.2072 22.32 24.0872 22.62 23.8571 22.85C23.627 23.08 23.3269 23.2 23.0068 23.2C22.6867 23.2 22.3866 23.08 22.1565 22.85L18.005 18.7L13.8535 22.85C13.6234 23.08 13.3233 23.2 13.0032 23.2C12.6831 23.2 12.383 23.08 12.1529 22.85C11.9228 22.62 11.8028 22.32 11.8028 22C11.8028 21.68 11.9228 21.38 12.1529 21.15L16.3044 17L12.1529 12.85C11.9228 12.62 11.8028 12.32 11.8028 12C11.8028 11.68 11.9228 11.38 12.1529 11.15C12.383 10.92 12.6831 10.8 13.0032 10.8C13.3233 10.8 13.6234 10.92 13.8535 11.15L18.005 15.3L22.1565 11.15C22.3866 10.92 22.6867 10.8 23.0068 10.8C23.3269 10.8 23.627 10.92 23.8571 11.15C24.0872 11.38 24.2072 11.68 24.2072 12C24.2072 12.32 24.0872 12.62 23.8571 12.85L19.7056 17L23.8571 21.15Z\"/>'\n  })];\nexport { e as shieldXIcon, L as shieldXIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "e", "outline", "solid", "shieldXIcon", "shieldXIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/shield-x.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"shield-x\",e=[\"shield-x\",C({outline:'<path d=\"M31.2597 7.81C28.9889 7.21 26.7781 6.51 24.6374 5.55C22.5266 4.64 20.4959 3.56 18.5552 2.34L18.005 2L17.4648 2.35C15.5241 3.57 13.4934 4.65 11.3826 5.56C9.24187 6.52 7.02108 7.21 4.75027 7.81L4 8V15.43C4 28.71 17.3248 33.75 17.6549 33.88L17.995 34L18.3351 33.88C18.4752 33.88 32 28.83 32 15.43V8L31.2497 7.81H31.2597ZM30.0093 15.43C30.0093 26.43 20.0057 31.04 18.005 31.86C16.0043 31.04 6.00071 26.42 6.00071 15.43V9.55C8.11147 8.94 10.1722 8.28 12.1829 7.39C14.1936 6.52 16.1343 5.52 18.005 4.39C19.8757 5.52 21.8164 6.52 23.8271 7.39C25.8378 8.28 27.8985 8.94 30.0093 9.55V15.43ZM23.717 11.29C23.3269 10.9 22.6967 10.9 22.3065 11.29L18.015 15.58L13.7235 11.29C13.3333 10.9 12.7031 10.9 12.313 11.29C11.9228 11.68 11.9228 12.31 12.313 12.7L16.6045 16.99L12.313 21.28C11.9228 21.67 11.9228 22.3 12.313 22.69C12.513 22.89 12.7631 22.98 13.0232 22.98C13.2833 22.98 13.5334 22.88 13.7335 22.69L18.025 18.4L22.3165 22.69C22.5166 22.89 22.7667 22.98 23.0268 22.98C23.2869 22.98 23.537 22.88 23.737 22.69C24.1272 22.3 24.1272 21.67 23.737 21.28L19.4455 16.99L23.737 12.7C24.1272 12.31 24.1272 11.68 23.737 11.29H23.717Z\"/>',solid:'<path d=\"M31.2597 7.81C28.9889 7.21 26.7781 6.51 24.6374 5.55C22.5266 4.64 20.4959 3.56 18.5552 2.34L18.005 2L17.4648 2.35C15.5241 3.57 13.4934 4.65 11.3826 5.56C9.24187 6.52 7.02108 7.21 4.75027 7.81L4 8V15.43C4 28.71 17.3248 33.75 17.6549 33.88L17.995 34L18.3351 33.88C18.4752 33.88 32 28.83 32 15.43V8L31.2497 7.81H31.2597ZM23.8571 21.15C24.0872 21.38 24.2072 21.68 24.2072 22C24.2072 22.32 24.0872 22.62 23.8571 22.85C23.627 23.08 23.3269 23.2 23.0068 23.2C22.6867 23.2 22.3866 23.08 22.1565 22.85L18.005 18.7L13.8535 22.85C13.6234 23.08 13.3233 23.2 13.0032 23.2C12.6831 23.2 12.383 23.08 12.1529 22.85C11.9228 22.62 11.8028 22.32 11.8028 22C11.8028 21.68 11.9228 21.38 12.1529 21.15L16.3044 17L12.1529 12.85C11.9228 12.62 11.8028 12.32 11.8028 12C11.8028 11.68 11.9228 11.38 12.1529 11.15C12.383 10.92 12.6831 10.8 13.0032 10.8C13.3233 10.8 13.6234 10.92 13.8535 11.15L18.005 15.3L22.1565 11.15C22.3866 10.92 22.6867 10.8 23.0068 10.8C23.3269 10.8 23.627 10.92 23.8571 11.15C24.0872 11.38 24.2072 11.68 24.2072 12C24.2072 12.32 24.0872 12.62 23.8571 12.85L19.7056 17L23.8571 21.15Z\"/>'})];export{e as shieldXIcon,L as shieldXIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,UAAU;EAACC,CAAC,GAAC,CAAC,UAAU,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,umCAAumC;IAACC,KAAK,EAAC;EAAokC,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,WAAW,EAACJ,CAAC,IAAIK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}