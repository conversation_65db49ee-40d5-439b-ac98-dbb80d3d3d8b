{"ast": null, "code": "import { GlobalStateService as t } from \"../services/global.service.js\";\nclass r {\n  static get registry() {\n    return {\n      ...t.state.motionRegistry\n    };\n  }\n  static has(t) {\n    return !!t && !!r.registry[t];\n  }\n  static get(t) {\n    return r.registry[t] || [];\n  }\n  static add(r, s) {\n    r && s && (t.state.motionRegistry = {\n      ...t.state.motionRegistry,\n      [r]: s\n    });\n  }\n}\nexport { r as ClarityMotion };", "map": {"version": 3, "names": ["GlobalStateService", "t", "r", "registry", "state", "motionRegistry", "has", "get", "add", "s", "ClarityMotion"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/motion/motion.service.js"], "sourcesContent": ["import{GlobalStateService as t}from\"../services/global.service.js\";class r{static get registry(){return{...t.state.motionRegistry}}static has(t){return!!t&&!!r.registry[t]}static get(t){return r.registry[t]||[]}static add(r,s){r&&s&&(t.state.motionRegistry={...t.state.motionRegistry,[r]:s})}}export{r as ClarityMotion};\n"], "mappings": "AAAA,SAAOA,kBAAkB,IAAIC,CAAC,QAAK,+BAA+B;AAAC,MAAMC,CAAC;EAAC,WAAWC,QAAQA,CAAA,EAAE;IAAC,OAAM;MAAC,GAAGF,CAAC,CAACG,KAAK,CAACC;IAAc,CAAC;EAAA;EAAC,OAAOC,GAAGA,CAACL,CAAC,EAAC;IAAC,OAAM,CAAC,CAACA,CAAC,IAAE,CAAC,CAACC,CAAC,CAACC,QAAQ,CAACF,CAAC,CAAC;EAAA;EAAC,OAAOM,GAAGA,CAACN,CAAC,EAAC;IAAC,OAAOC,CAAC,CAACC,QAAQ,CAACF,CAAC,CAAC,IAAE,EAAE;EAAA;EAAC,OAAOO,GAAGA,CAACN,CAAC,EAACO,CAAC,EAAC;IAACP,CAAC,IAAEO,CAAC,KAAGR,CAAC,CAACG,KAAK,CAACC,cAAc,GAAC;MAAC,GAAGJ,CAAC,CAACG,KAAK,CAAC<PERSON>,cAAc;MAAC,CAACH,CAAC,GAAEO;IAAC,CAAC,CAAC;EAAA;AAAC;AAAC,SAAOP,CAAC,IAAIQ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}