{"ast": null, "code": "import { renderIcon as H } from \"../icon.renderer.js\";\nconst V = \"box-plot\",\n  d = [\"box-plot\", H({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4 5H32C33.1046 5 34 5.89543 34 7V29C34 30.1046 33.1046 31 32 31H4C2.89543 31 2 30.1046 2 29V7C2 5.89543 2.89543 5 4 5ZM4 7V29H32V7H4ZM7 12H17V26H7V12ZM15.4 24.4H8.6V18.8H15.4V24.4ZM15.4 13.6H8.6V17.2H15.4V13.6ZM29 24H19V10H29V24ZM20.6 11.6H27.4V17.2H20.6V11.6ZM20.6 22.4H27.4V18.8H20.6V22.4Z\"/>',\n    outlineAlerted: '<path d=\"M26.9352 1.59934L21.2535 11.069C20.984 11.4515 20.9595 11.948 21.1902 12.3536C21.4209 12.7592 21.8677 13.0051 22.3462 12.9898H33.7195C34.198 13.0051 34.6447 12.7592 34.8755 12.3536C35.1062 11.948 35.0817 11.4515 34.8121 11.069L29.1304 1.59934C28.9024 1.22226 28.4846 0.990479 28.0328 0.990479C27.5811 0.990479 27.1632 1.22226 26.9352 1.59934Z\"/><path d=\"M4 5H22.5625L21.3625 7H4V29H32V14.9898H33.6949C33.7971 14.9919 33.8989 14.9892 34 14.9818V29C34 30.1046 33.1046 31 32 31H4C2.89543 31 2 30.1046 2 29V7C2 5.89543 2.89543 5 4 5Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M19 12.0078V24H29V14.9898H27.4V17.2H20.6V14.5178C20.1337 14.2374 19.7343 13.8392 19.4518 13.3425C19.2137 12.9239 19.0779 12.4681 19.0423 12.0078H19ZM20.6 22.4H27.4V18.8H20.6V22.4Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7 12H17V26H7V12ZM15.4 24.4H8.6V18.8H15.4V24.4ZM15.4 13.6H8.6V17.2H15.4V13.6Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.2547 10H19V24H29V12.9291C28.4428 12.8494 27.9068 12.7041 27.4 12.5012V17.2H20.6V11.6H25.7993C25.2035 11.1524 24.6815 10.6119 24.2547 10ZM27.4 22.4H20.6V18.8H27.4V22.4Z\"/><path d=\"M32 12.7101V29H4V7H23.0709C23.0242 6.6734 23 6.33952 23 6C23 5.66048 23.0242 5.3266 23.0709 5H4C2.89543 5 2 5.89543 2 7V29C2 30.1046 2.89543 31 4 31H32C33.1046 31 34 30.1046 34 29V11.7453C33.396 12.1666 32.7224 12.4951 32 12.7101Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M17 12H7V26H17V12ZM8.6 24.4H15.4V18.8H8.6V24.4ZM8.6 13.6H15.4V17.2H8.6V13.6Z\"/>',\n    solid: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M34 7V29C34 30.105 33.105 31 32 31H4C2.895 31 2 30.105 2 29V7C2 5.895 2.895 5 4 5H32C33.105 5 34 5.895 34 7ZM7 26H17V12H7V26ZM15 19H9V24H15V19ZM15 17H9V14H15V17ZM29 24H19V10H29V24ZM21 12H27V17H21V12ZM21 22H27V19H21V22Z\"/>',\n    solidAlerted: '<path d=\"M26.904 1.61496L21.2223 11.0846C20.9527 11.4671 20.9283 11.9636 21.159 12.3692C21.3897 12.7748 21.8364 13.0207 22.3149 13.0054H33.6883C34.1667 13.0207 34.6135 12.7748 34.8442 12.3692C35.0749 11.9636 35.0504 11.4671 34.7809 11.0846L29.0992 1.61496C28.8712 1.23788 28.4533 1.0061 28.0016 1.0061C27.5498 1.0061 27.132 1.23788 26.904 1.61496Z\"/><path d=\"M9 19H15V24H9V19Z\"/><path d=\"M9 17H15V14H9V17Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M22.5406 5H4C2.895 5 2 5.895 2 7V29C2 30.105 2.895 31 4 31H32C33.105 31 34 30.105 34 29V14.995C33.8886 15.0042 33.7764 15.0077 33.6637 15.0054H29V24H19V12.0312L19.0115 12.0289C18.956 11.3271 19.1334 10.6145 19.5361 10.0075L22.5406 5ZM17 26H7V12H17V26Z\"/><path d=\"M21 14.7515V17H27V15.0054H22.3395C21.8759 15.0147 21.4197 14.9263 21 14.7515Z\"/><path d=\"M27 22H21V19H27V22Z\"/>',\n    solidBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M9 19H15V24H9V19Z\"/><path d=\"M9 17H15V14H9V17Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M34 11.7453C32.8662 12.5362 31.4872 13 30 13C29.6605 13 29.3266 12.9758 29 12.9291V24H19V10H24.2547C23.4638 8.86617 23 7.48725 23 6C23 5.66048 23.0242 5.3266 23.0709 5H4C2.895 5 2 5.895 2 7V29C2 30.105 2.895 31 4 31H32C33.105 31 34 30.105 34 29V11.7453ZM17 26H7V12H17V26Z\"/><path d=\"M26.3924 12H21V17H27V12.3264C26.7916 12.2274 26.5889 12.1184 26.3924 12Z\"/><path d=\"M27 22H21V19H27V22Z\"/>'\n  })];\nexport { d as boxPlotIcon, V as boxPlotIconName };", "map": {"version": 3, "names": ["renderIcon", "H", "V", "d", "outline", "outlineAlerted", "outlineBadged", "solid", "solidAlerted", "solidBadged", "boxPlotIcon", "boxPlotIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/box-plot.js"], "sourcesContent": ["import{renderIcon as H}from\"../icon.renderer.js\";const V=\"box-plot\",d=[\"box-plot\",H({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4 5H32C33.1046 5 34 5.89543 34 7V29C34 30.1046 33.1046 31 32 31H4C2.89543 31 2 30.1046 2 29V7C2 5.89543 2.89543 5 4 5ZM4 7V29H32V7H4ZM7 12H17V26H7V12ZM15.4 24.4H8.6V18.8H15.4V24.4ZM15.4 13.6H8.6V17.2H15.4V13.6ZM29 24H19V10H29V24ZM20.6 11.6H27.4V17.2H20.6V11.6ZM20.6 22.4H27.4V18.8H20.6V22.4Z\"/>',outlineAlerted:'<path d=\"M26.9352 1.59934L21.2535 11.069C20.984 11.4515 20.9595 11.948 21.1902 12.3536C21.4209 12.7592 21.8677 13.0051 22.3462 12.9898H33.7195C34.198 13.0051 34.6447 12.7592 34.8755 12.3536C35.1062 11.948 35.0817 11.4515 34.8121 11.069L29.1304 1.59934C28.9024 1.22226 28.4846 0.990479 28.0328 0.990479C27.5811 0.990479 27.1632 1.22226 26.9352 1.59934Z\"/><path d=\"M4 5H22.5625L21.3625 7H4V29H32V14.9898H33.6949C33.7971 14.9919 33.8989 14.9892 34 14.9818V29C34 30.1046 33.1046 31 32 31H4C2.89543 31 2 30.1046 2 29V7C2 5.89543 2.89543 5 4 5Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M19 12.0078V24H29V14.9898H27.4V17.2H20.6V14.5178C20.1337 14.2374 19.7343 13.8392 19.4518 13.3425C19.2137 12.9239 19.0779 12.4681 19.0423 12.0078H19ZM20.6 22.4H27.4V18.8H20.6V22.4Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7 12H17V26H7V12ZM15.4 24.4H8.6V18.8H15.4V24.4ZM15.4 13.6H8.6V17.2H15.4V13.6Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.2547 10H19V24H29V12.9291C28.4428 12.8494 27.9068 12.7041 27.4 12.5012V17.2H20.6V11.6H25.7993C25.2035 11.1524 24.6815 10.6119 24.2547 10ZM27.4 22.4H20.6V18.8H27.4V22.4Z\"/><path d=\"M32 12.7101V29H4V7H23.0709C23.0242 6.6734 23 6.33952 23 6C23 5.66048 23.0242 5.3266 23.0709 5H4C2.89543 5 2 5.89543 2 7V29C2 30.1046 2.89543 31 4 31H32C33.1046 31 34 30.1046 34 29V11.7453C33.396 12.1666 32.7224 12.4951 32 12.7101Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M17 12H7V26H17V12ZM8.6 24.4H15.4V18.8H8.6V24.4ZM8.6 13.6H15.4V17.2H8.6V13.6Z\"/>',solid:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M34 7V29C34 30.105 33.105 31 32 31H4C2.895 31 2 30.105 2 29V7C2 5.895 2.895 5 4 5H32C33.105 5 34 5.895 34 7ZM7 26H17V12H7V26ZM15 19H9V24H15V19ZM15 17H9V14H15V17ZM29 24H19V10H29V24ZM21 12H27V17H21V12ZM21 22H27V19H21V22Z\"/>',solidAlerted:'<path d=\"M26.904 1.61496L21.2223 11.0846C20.9527 11.4671 20.9283 11.9636 21.159 12.3692C21.3897 12.7748 21.8364 13.0207 22.3149 13.0054H33.6883C34.1667 13.0207 34.6135 12.7748 34.8442 12.3692C35.0749 11.9636 35.0504 11.4671 34.7809 11.0846L29.0992 1.61496C28.8712 1.23788 28.4533 1.0061 28.0016 1.0061C27.5498 1.0061 27.132 1.23788 26.904 1.61496Z\"/><path d=\"M9 19H15V24H9V19Z\"/><path d=\"M9 17H15V14H9V17Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M22.5406 5H4C2.895 5 2 5.895 2 7V29C2 30.105 2.895 31 4 31H32C33.105 31 34 30.105 34 29V14.995C33.8886 15.0042 33.7764 15.0077 33.6637 15.0054H29V24H19V12.0312L19.0115 12.0289C18.956 11.3271 19.1334 10.6145 19.5361 10.0075L22.5406 5ZM17 26H7V12H17V26Z\"/><path d=\"M21 14.7515V17H27V15.0054H22.3395C21.8759 15.0147 21.4197 14.9263 21 14.7515Z\"/><path d=\"M27 22H21V19H27V22Z\"/>',solidBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M9 19H15V24H9V19Z\"/><path d=\"M9 17H15V14H9V17Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M34 11.7453C32.8662 12.5362 31.4872 13 30 13C29.6605 13 29.3266 12.9758 29 12.9291V24H19V10H24.2547C23.4638 8.86617 23 7.48725 23 6C23 5.66048 23.0242 5.3266 23.0709 5H4C2.895 5 2 5.895 2 7V29C2 30.105 2.895 31 4 31H32C33.105 31 34 30.105 34 29V11.7453ZM17 26H7V12H17V26Z\"/><path d=\"M26.3924 12H21V17H27V12.3264C26.7916 12.2274 26.5889 12.1184 26.3924 12Z\"/><path d=\"M27 22H21V19H27V22Z\"/>'})];export{d as boxPlotIcon,V as boxPlotIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,UAAU;EAACC,CAAC,GAAC,CAAC,UAAU,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,0VAA0V;IAACC,cAAc,EAAC,u4BAAu4B;IAACC,aAAa,EAAC,itBAAitB;IAACC,KAAK,EAAC,gRAAgR;IAACC,YAAY,EAAC,y2BAAy2B;IAACC,WAAW,EAAC;EAAgnB,CAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,WAAW,EAACR,CAAC,IAAIS,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}