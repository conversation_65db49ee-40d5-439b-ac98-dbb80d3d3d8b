{"ast": null, "code": "import _clone from \"./internal/_clone.js\";\nimport _curry1 from \"./internal/_curry1.js\";\n/**\n * Creates a deep copy of the source that can be used in place of the source\n * object without retaining any references to it.\n * The source object may contain (nested) `Array`s and `Object`s,\n * `Number`s, `String`s, `Boolean`s and `Date`s.\n * `Function`s are assigned by reference rather than copied.\n *\n * Dispatches to a `clone` method if present.\n *\n * Note that if the source object has multiple nodes that share a reference,\n * the returned object will have the same structure, but the references will\n * be pointed to the location within the cloned value.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Object\n * @sig {*} -> {*}\n * @param {*} value The object or array to clone\n * @return {*} A deeply cloned copy of `val`\n * @example\n *\n *      const objects = [{}, {}, {}];\n *      const objectsClone = R.clone(objects);\n *      objects === objectsClone; //=> false\n *      objects[0] === objectsClone[0]; //=> false\n */\n\nvar clone = /*#__PURE__*/\n_curry1(function clone(value) {\n  return value != null && typeof value.clone === 'function' ? value.clone() : _clone(value, true);\n});\nexport default clone;", "map": {"version": 3, "names": ["_clone", "_curry1", "clone", "value"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/clone.js"], "sourcesContent": ["import _clone from \"./internal/_clone.js\";\nimport _curry1 from \"./internal/_curry1.js\";\n/**\n * Creates a deep copy of the source that can be used in place of the source\n * object without retaining any references to it.\n * The source object may contain (nested) `Array`s and `Object`s,\n * `Number`s, `String`s, `Boolean`s and `Date`s.\n * `Function`s are assigned by reference rather than copied.\n *\n * Dispatches to a `clone` method if present.\n *\n * Note that if the source object has multiple nodes that share a reference,\n * the returned object will have the same structure, but the references will\n * be pointed to the location within the cloned value.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Object\n * @sig {*} -> {*}\n * @param {*} value The object or array to clone\n * @return {*} A deeply cloned copy of `val`\n * @example\n *\n *      const objects = [{}, {}, {}];\n *      const objectsClone = R.clone(objects);\n *      objects === objectsClone; //=> false\n *      objects[0] === objectsClone[0]; //=> false\n */\n\nvar clone =\n/*#__PURE__*/\n_curry1(function clone(value) {\n  return value != null && typeof value.clone === 'function' ? value.clone() : _clone(value, true);\n});\n\nexport default clone;"], "mappings": "AAAA,OAAOA,MAAM,MAAM,sBAAsB;AACzC,OAAOC,OAAO,MAAM,uBAAuB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,KAAK,GACT;AACAD,OAAO,CAAC,SAASC,KAAKA,CAACC,KAAK,EAAE;EAC5B,OAAOA,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,CAACD,KAAK,KAAK,UAAU,GAAGC,KAAK,CAACD,KAAK,CAAC,CAAC,GAAGF,MAAM,CAACG,KAAK,EAAE,IAAI,CAAC;AACjG,CAAC,CAAC;AAEF,eAAeD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}