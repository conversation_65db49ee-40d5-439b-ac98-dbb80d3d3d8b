{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"caravan\",\n  V = [\"caravan\", C({\n    outline: '<path d=\"M14.4375 21C11.9375 21 9.9375 23 9.9375 25.5C9.9375 28 11.9375 30 14.4375 30C16.9375 30 18.9375 28 18.9375 25.5C18.9375 23 16.9375 21 14.4375 21ZM14.4375 28C13.0375 28 11.9375 26.9 11.9375 25.5C11.9375 24.1 13.0375 23 14.4375 23C15.8375 23 16.9375 24.1 16.9375 25.5C16.9375 26.9 15.8375 28 14.4375 28ZM14.9375 12H6.9375V18H14.9375V12ZM12.9375 16H8.9375V14H12.9375V16ZM33.9375 24H30.9375V16.5C30.9375 16 30.8375 15.5 30.5375 15L26.5375 7.5C26.0375 6.5 25.8375 6 24.7375 6H5.9375C4.2375 6 2.9375 7.3 2.9375 9V23C2.9375 24.7 4.2375 26 5.9375 26H7.9375V24H5.9375C5.3375 24 4.9375 23.6 4.9375 23V9C4.9375 8.4 5.3375 8 5.9375 8H23.9375C24.3375 8 24.6375 8.2 24.8375 8.5L28.8375 16C28.9375 16.1 28.9375 16.3 28.9375 16.5V24H24.9375V12H16.9375V20H18.9375V14H22.9375V24H20.9375V26H33.9375C34.5375 26 34.9375 25.6 34.9375 25C34.9375 24.4 34.5375 24 33.9375 24Z\"/>',\n    solid: '<path d=\"M14.9375 12H6.9375V18H14.9375V12ZM14.4375 21C11.9375 21 9.9375 23 9.9375 25.5C9.9375 28 11.9375 30 14.4375 30C16.9375 30 18.9375 28 18.9375 25.5C18.9375 23 16.9375 21 14.4375 21ZM33.9375 24H30.9375V16.5C30.9375 16 30.8375 15.5 30.5375 15L26.5375 7.5C26.0375 6.5 25.8375 6 24.7375 6H5.9375C4.2375 6 2.9375 7.3 2.9375 9V23C2.9375 24.7 4.2375 26 5.9375 26H7.9375V24H5.9375C5.3375 24 4.9375 23.6 4.9375 23V9C4.9375 8.4 5.3375 8 5.9375 8H23.9375C24.3375 8 24.6375 8.2 24.8375 8.5L28.8375 16C28.9375 16.1 28.9375 16.3 28.9375 16.5V24H24.9375V12H16.9375V20H18.9375V14H22.9375V24H20.9375V26H33.9375C34.5375 26 34.9375 25.6 34.9375 25C34.9375 24.4 34.5375 24 33.9375 24Z\"/>'\n  })];\nexport { V as caravanIcon, H as caravanIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "V", "outline", "solid", "caravanIcon", "caravanIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/caravan.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"caravan\",V=[\"caravan\",C({outline:'<path d=\"M14.4375 21C11.9375 21 9.9375 23 9.9375 25.5C9.9375 28 11.9375 30 14.4375 30C16.9375 30 18.9375 28 18.9375 25.5C18.9375 23 16.9375 21 14.4375 21ZM14.4375 28C13.0375 28 11.9375 26.9 11.9375 25.5C11.9375 24.1 13.0375 23 14.4375 23C15.8375 23 16.9375 24.1 16.9375 25.5C16.9375 26.9 15.8375 28 14.4375 28ZM14.9375 12H6.9375V18H14.9375V12ZM12.9375 16H8.9375V14H12.9375V16ZM33.9375 24H30.9375V16.5C30.9375 16 30.8375 15.5 30.5375 15L26.5375 7.5C26.0375 6.5 25.8375 6 24.7375 6H5.9375C4.2375 6 2.9375 7.3 2.9375 9V23C2.9375 24.7 4.2375 26 5.9375 26H7.9375V24H5.9375C5.3375 24 4.9375 23.6 4.9375 23V9C4.9375 8.4 5.3375 8 5.9375 8H23.9375C24.3375 8 24.6375 8.2 24.8375 8.5L28.8375 16C28.9375 16.1 28.9375 16.3 28.9375 16.5V24H24.9375V12H16.9375V20H18.9375V14H22.9375V24H20.9375V26H33.9375C34.5375 26 34.9375 25.6 34.9375 25C34.9375 24.4 34.5375 24 33.9375 24Z\"/>',solid:'<path d=\"M14.9375 12H6.9375V18H14.9375V12ZM14.4375 21C11.9375 21 9.9375 23 9.9375 25.5C9.9375 28 11.9375 30 14.4375 30C16.9375 30 18.9375 28 18.9375 25.5C18.9375 23 16.9375 21 14.4375 21ZM33.9375 24H30.9375V16.5C30.9375 16 30.8375 15.5 30.5375 15L26.5375 7.5C26.0375 6.5 25.8375 6 24.7375 6H5.9375C4.2375 6 2.9375 7.3 2.9375 9V23C2.9375 24.7 4.2375 26 5.9375 26H7.9375V24H5.9375C5.3375 24 4.9375 23.6 4.9375 23V9C4.9375 8.4 5.3375 8 5.9375 8H23.9375C24.3375 8 24.6375 8.2 24.8375 8.5L28.8375 16C28.9375 16.1 28.9375 16.3 28.9375 16.5V24H24.9375V12H16.9375V20H18.9375V14H22.9375V24H20.9375V26H33.9375C34.5375 26 34.9375 25.6 34.9375 25C34.9375 24.4 34.5375 24 33.9375 24Z\"/>'})];export{V as caravanIcon,H as caravanIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,SAAS;EAACC,CAAC,GAAC,CAAC,SAAS,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,g2BAAg2B;IAACC,KAAK,EAAC;EAAmqB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,WAAW,EAACJ,CAAC,IAAIK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}