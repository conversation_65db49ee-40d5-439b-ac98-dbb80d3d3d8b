{"ast": null, "code": "import { renderIcon as t } from \"../icon.renderer.js\";\nconst a = \"align-bottom\",\n  o = [\"align-bottom\", t({\n    outline: '<path d=\"M34,30H2a1,1,0,0,0,0,2H34a1,1,0,0,0,0-2Z\"/><path d=\"M16,5a1,1,0,0,0-1-1H7A1,1,0,0,0,6,5V28H16ZM14,26H8V6h6Z\"/><path d=\"M30,13a1,1,0,0,0-1-1H21a1,1,0,0,0-1,1V28H30ZM28,26H22V14h6Z\"/>'\n  })];\nexport { o as alignBottomIcon, a as alignBottomIconName };", "map": {"version": 3, "names": ["renderIcon", "t", "a", "o", "outline", "alignBottomIcon", "alignBottomIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/align-bottom.js"], "sourcesContent": ["import{renderIcon as t}from\"../icon.renderer.js\";const a=\"align-bottom\",o=[\"align-bottom\",t({outline:'<path d=\"M34,30H2a1,1,0,0,0,0,2H34a1,1,0,0,0,0-2Z\"/><path d=\"M16,5a1,1,0,0,0-1-1H7A1,1,0,0,0,6,5V28H16ZM14,26H8V6h6Z\"/><path d=\"M30,13a1,1,0,0,0-1-1H21a1,1,0,0,0-1,1V28H30ZM28,26H22V14h6Z\"/>'})];export{o as alignBottomIcon,a as alignBottomIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,cAAc;EAACC,CAAC,GAAC,CAAC,cAAc,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAgM,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,eAAe,EAACH,CAAC,IAAII,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}