{"ast": null, "code": "import _arity from \"./_arity.js\";\nimport _isPlaceholder from \"./_isPlaceholder.js\";\n/**\n * Internal curryN function.\n *\n * @private\n * @category Function\n * @param {Number} length The arity of the curried function.\n * @param {Array} received An array of arguments received thus far.\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\n\nexport default function _curryN(length, received, fn) {\n  return function () {\n    var combined = [];\n    var argsIdx = 0;\n    var left = length;\n    var combinedIdx = 0;\n    var hasPlaceholder = false;\n    while (combinedIdx < received.length || argsIdx < arguments.length) {\n      var result;\n      if (combinedIdx < received.length && (!_isPlaceholder(received[combinedIdx]) || argsIdx >= arguments.length)) {\n        result = received[combinedIdx];\n      } else {\n        result = arguments[argsIdx];\n        argsIdx += 1;\n      }\n      combined[combinedIdx] = result;\n      if (!_isPlaceholder(result)) {\n        left -= 1;\n      } else {\n        hasPlaceholder = true;\n      }\n      combinedIdx += 1;\n    }\n    return !hasPlaceholder && left <= 0 ? fn.apply(this, combined) : _arity(Math.max(0, left), _curryN(length, combined, fn));\n  };\n}", "map": {"version": 3, "names": ["_arity", "_isPlaceholder", "_curryN", "length", "received", "fn", "combined", "argsIdx", "left", "combinedIdx", "hasPlaceholder", "arguments", "result", "apply", "Math", "max"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_curryN.js"], "sourcesContent": ["import _arity from \"./_arity.js\";\nimport _isPlaceholder from \"./_isPlaceholder.js\";\n/**\n * Internal curryN function.\n *\n * @private\n * @category Function\n * @param {Number} length The arity of the curried function.\n * @param {Array} received An array of arguments received thus far.\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\n\nexport default function _curryN(length, received, fn) {\n  return function () {\n    var combined = [];\n    var argsIdx = 0;\n    var left = length;\n    var combinedIdx = 0;\n    var hasPlaceholder = false;\n\n    while (combinedIdx < received.length || argsIdx < arguments.length) {\n      var result;\n\n      if (combinedIdx < received.length && (!_isPlaceholder(received[combinedIdx]) || argsIdx >= arguments.length)) {\n        result = received[combinedIdx];\n      } else {\n        result = arguments[argsIdx];\n        argsIdx += 1;\n      }\n\n      combined[combinedIdx] = result;\n\n      if (!_isPlaceholder(result)) {\n        left -= 1;\n      } else {\n        hasPlaceholder = true;\n      }\n\n      combinedIdx += 1;\n    }\n\n    return !hasPlaceholder && left <= 0 ? fn.apply(this, combined) : _arity(Math.max(0, left), _curryN(length, combined, fn));\n  };\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,aAAa;AAChC,OAAOC,cAAc,MAAM,qBAAqB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,OAAOA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,EAAE,EAAE;EACpD,OAAO,YAAY;IACjB,IAAIC,QAAQ,GAAG,EAAE;IACjB,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIC,IAAI,GAAGL,MAAM;IACjB,IAAIM,WAAW,GAAG,CAAC;IACnB,IAAIC,cAAc,GAAG,KAAK;IAE1B,OAAOD,WAAW,GAAGL,QAAQ,CAACD,MAAM,IAAII,OAAO,GAAGI,SAAS,CAACR,MAAM,EAAE;MAClE,IAAIS,MAAM;MAEV,IAAIH,WAAW,GAAGL,QAAQ,CAACD,MAAM,KAAK,CAACF,cAAc,CAACG,QAAQ,CAACK,WAAW,CAAC,CAAC,IAAIF,OAAO,IAAII,SAAS,CAACR,MAAM,CAAC,EAAE;QAC5GS,MAAM,GAAGR,QAAQ,CAACK,WAAW,CAAC;MAChC,CAAC,MAAM;QACLG,MAAM,GAAGD,SAAS,CAACJ,OAAO,CAAC;QAC3BA,OAAO,IAAI,CAAC;MACd;MAEAD,QAAQ,CAACG,WAAW,CAAC,GAAGG,MAAM;MAE9B,IAAI,CAACX,cAAc,CAACW,MAAM,CAAC,EAAE;QAC3BJ,IAAI,IAAI,CAAC;MACX,CAAC,MAAM;QACLE,cAAc,GAAG,IAAI;MACvB;MAEAD,WAAW,IAAI,CAAC;IAClB;IAEA,OAAO,CAACC,cAAc,IAAIF,IAAI,IAAI,CAAC,GAAGH,EAAE,CAACQ,KAAK,CAAC,IAAI,EAAEP,QAAQ,CAAC,GAAGN,MAAM,CAACc,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEP,IAAI,CAAC,EAAEN,OAAO,CAACC,MAAM,EAAEG,QAAQ,EAAED,EAAE,CAAC,CAAC;EAC3H,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}