{"ast": null, "code": "import { renderIcon as V } from \"../icon.renderer.js\";\nconst C = \"terminal\",\n  H = [\"terminal\", V({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 6H4C2.9 6 2 6.9 2 8V28C2 29.1 2.9 30 4 30H32C33.1 30 34 29.1 34 28V8C34 6.9 33.1 6 32 6ZM4 8H32V10.2H4V8ZM4 11.8V28H32V11.8H4ZM7 15.68L13.79 18.8L7 21.91V24.11L16.6 19.7V17.89L7 13.48V15.68ZM23 24H17V26H23V24Z\"/>',\n    outlineAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M21.9594 6H4C2.9 6 2 6.9 2 8V28C2 29.1 2.9 30 4 30H32C33.1 30 34 29.1 34 28V15.0263C33.8886 15.0354 33.7763 15.0389 33.6637 15.0367H32V28H4V11.8H19.0016C19.0031 11.2453 19.1491 10.693 19.4359 10.2H4V8H20.7594L21.9594 6Z\"/><path d=\"M13.79 18.8L7 15.68V13.48L16.6 17.89V19.7L7 24.11V21.91L13.79 18.8Z\"/><path d=\"M17 24H23V26H17V24Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M32 12.7101V28H4V11.8H26.0796C25.4347 11.3633 24.8665 10.8218 24.3995 10.2H4V8H23.2899C23.1013 7.36629 23 6.69497 23 6H4C2.9 6 2 6.9 2 8V28C2 29.1 2.9 30 4 30H32C33.1 30 34 29.1 34 28V11.7453C33.396 12.1666 32.7224 12.4951 32 12.7101Z\"/><path d=\"M13.79 18.8L7 15.68V13.48L16.6 17.89V19.7L7 24.11V21.91L13.79 18.8Z\"/><path d=\"M17 24H23V26H17V24Z\"/>',\n    solid: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 6H4C2.9 6 2 6.9 2 8V28C2 29.1 2.9 30 4 30H32C33.1 30 34 29.1 34 28V8C34 6.9 33.1 6 32 6ZM16.6 19.7L7 24.11V21.91L13.79 18.8L7 15.68V13.48L16.6 17.89V19.7ZM23 26H17V24H23V26ZM32 10H4V8H32V10Z\"/>',\n    solidAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M21.9594 6H4C2.9 6 2 6.9 2 8V28C2 29.1 2.9 30 4 30H32C33.1 30 34 29.1 34 28V15.0263C33.8886 15.0354 33.7763 15.0389 33.6637 15.0367H22.3395C21.1577 15.0604 20.0233 14.4489 19.4206 13.3893C18.8204 12.3342 18.8703 11.0423 19.5362 10.0387L19.5594 10H4V8H20.7594L21.9594 6ZM7 24.11L16.6 19.7V17.89L7 13.48V15.68L13.79 18.8L7 21.91V24.11ZM17 26H23V24H17V26Z\"/>',\n    solidBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M34 11.7453C32.8662 12.5362 31.4872 13 30 13C27.6213 13 25.5196 11.8135 24.2547 10H4V8H23.2899C23.1013 7.36629 23 6.69497 23 6H4C2.9 6 2 6.9 2 8V28C2 29.1 2.9 30 4 30H32C33.1 30 34 29.1 34 28V11.7453ZM7 24.11L16.6 19.7V17.89L7 13.48V15.68L13.79 18.8L7 21.91V24.11ZM17 26H23V24H17V26Z\"/>'\n  })];\nexport { H as terminalIcon, C as terminalIconName };", "map": {"version": 3, "names": ["renderIcon", "V", "C", "H", "outline", "outlineAlerted", "outlineBadged", "solid", "solidAlerted", "solidBadged", "terminalIcon", "terminalIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/terminal.js"], "sourcesContent": ["import{renderIcon as V}from\"../icon.renderer.js\";const C=\"terminal\",H=[\"terminal\",V({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 6H4C2.9 6 2 6.9 2 8V28C2 29.1 2.9 30 4 30H32C33.1 30 34 29.1 34 28V8C34 6.9 33.1 6 32 6ZM4 8H32V10.2H4V8ZM4 11.8V28H32V11.8H4ZM7 15.68L13.79 18.8L7 21.91V24.11L16.6 19.7V17.89L7 13.48V15.68ZM23 24H17V26H23V24Z\"/>',outlineAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M21.9594 6H4C2.9 6 2 6.9 2 8V28C2 29.1 2.9 30 4 30H32C33.1 30 34 29.1 34 28V15.0263C33.8886 15.0354 33.7763 15.0389 33.6637 15.0367H32V28H4V11.8H19.0016C19.0031 11.2453 19.1491 10.693 19.4359 10.2H4V8H20.7594L21.9594 6Z\"/><path d=\"M13.79 18.8L7 15.68V13.48L16.6 17.89V19.7L7 24.11V21.91L13.79 18.8Z\"/><path d=\"M17 24H23V26H17V24Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M32 12.7101V28H4V11.8H26.0796C25.4347 11.3633 24.8665 10.8218 24.3995 10.2H4V8H23.2899C23.1013 7.36629 23 6.69497 23 6H4C2.9 6 2 6.9 2 8V28C2 29.1 2.9 30 4 30H32C33.1 30 34 29.1 34 28V11.7453C33.396 12.1666 32.7224 12.4951 32 12.7101Z\"/><path d=\"M13.79 18.8L7 15.68V13.48L16.6 17.89V19.7L7 24.11V21.91L13.79 18.8Z\"/><path d=\"M17 24H23V26H17V24Z\"/>',solid:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 6H4C2.9 6 2 6.9 2 8V28C2 29.1 2.9 30 4 30H32C33.1 30 34 29.1 34 28V8C34 6.9 33.1 6 32 6ZM16.6 19.7L7 24.11V21.91L13.79 18.8L7 15.68V13.48L16.6 17.89V19.7ZM23 26H17V24H23V26ZM32 10H4V8H32V10Z\"/>',solidAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M21.9594 6H4C2.9 6 2 6.9 2 8V28C2 29.1 2.9 30 4 30H32C33.1 30 34 29.1 34 28V15.0263C33.8886 15.0354 33.7763 15.0389 33.6637 15.0367H22.3395C21.1577 15.0604 20.0233 14.4489 19.4206 13.3893C18.8204 12.3342 18.8703 11.0423 19.5362 10.0387L19.5594 10H4V8H20.7594L21.9594 6ZM7 24.11L16.6 19.7V17.89L7 13.48V15.68L13.79 18.8L7 21.91V24.11ZM17 26H23V24H17V26Z\"/>',solidBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M34 11.7453C32.8662 12.5362 31.4872 13 30 13C27.6213 13 25.5196 11.8135 24.2547 10H4V8H23.2899C23.1013 7.36629 23 6.69497 23 6H4C2.9 6 2 6.9 2 8V28C2 29.1 2.9 30 4 30H32C33.1 30 34 29.1 34 28V11.7453ZM7 24.11L16.6 19.7V17.89L7 13.48V15.68L13.79 18.8L7 21.91V24.11ZM17 26H23V24H17V26Z\"/>'})];export{H as terminalIcon,C as terminalIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,UAAU;EAACC,CAAC,GAAC,CAAC,UAAU,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,2QAA2Q;IAACC,cAAc,EAAC,0rBAA0rB;IAACC,aAAa,EAAC,oeAAoe;IAACC,KAAK,EAAC,wPAAwP;IAACC,YAAY,EAAC,yvBAAyvB;IAACC,WAAW,EAAC;EAA+c,CAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,YAAY,EAACR,CAAC,IAAIS,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}