{"ast": null, "code": "function e(e = 10) {\n  return new Promise(t => setTimeout(t, e));\n}\nexport { e as sleep };", "map": {"version": 3, "names": ["e", "Promise", "t", "setTimeout", "sleep"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/async.js"], "sourcesContent": ["function e(e=10){return new Promise((t=>setTimeout(t,e)))}export{e as sleep};\n"], "mappings": "AAAA,SAASA,CAACA,CAACA,CAAC,GAAC,EAAE,EAAC;EAAC,OAAO,IAAIC,OAAO,CAAEC,CAAC,IAAEC,UAAU,CAACD,CAAC,EAACF,CAAC,CAAE,CAAC;AAAA;AAAC,SAAOA,CAAC,IAAII,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}