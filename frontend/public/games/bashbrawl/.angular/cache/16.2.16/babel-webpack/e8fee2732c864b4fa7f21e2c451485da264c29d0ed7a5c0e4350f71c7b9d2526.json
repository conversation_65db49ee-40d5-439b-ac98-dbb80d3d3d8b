{"ast": null, "code": "import { fadeInKeyframes as a } from \"./keyframes/fade-in.js\";\nconst i = \"cds-responsive-popup-enter\",\n  o = [{\n    target: \".overlay-backdrop\",\n    onlyIf: \"isLayered:false responsive:true\",\n    animation: a,\n    options: {\n      duration: \"--animation-duration\",\n      easing: \"--animation-easing\",\n      fill: \"forwards\"\n    }\n  }, {\n    target: \".private-host\",\n    onlyIf: \"responsive:true\",\n    animation: [{\n      opacity: 0\n    }, {\n      opacity: 0,\n      marginBottom: \"-100%\",\n      offset: .001\n    }, {\n      opacity: 1,\n      marginBottom: \"0\"\n    }],\n    options: {\n      duration: \"--animation-duration\",\n      easing: \"--animation-easing\",\n      fill: \"forwards\"\n    }\n  }, {\n    target: \".private-host\",\n    onlyIf: \"responsive:false\",\n    animation: [{\n      opacity: 0\n    }, {\n      opacity: 1\n    }],\n    options: {\n      duration: 0,\n      easing: \"--animation-easing\",\n      fill: \"forwards\"\n    }\n  }];\nexport { o as AnimationResponsivePopupEnterConfig, i as AnimationResponsivePopupEnterName };", "map": {"version": 3, "names": ["fadeInKeyframes", "a", "i", "o", "target", "onlyIf", "animation", "options", "duration", "easing", "fill", "opacity", "marginBottom", "offset", "AnimationResponsivePopupEnterConfig", "AnimationResponsivePopupEnterName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/motion/animations/cds-dropdown-open.js"], "sourcesContent": ["import{fadeInKeyframes as a}from\"./keyframes/fade-in.js\";const i=\"cds-responsive-popup-enter\",o=[{target:\".overlay-backdrop\",onlyIf:\"isLayered:false responsive:true\",animation:a,options:{duration:\"--animation-duration\",easing:\"--animation-easing\",fill:\"forwards\"}},{target:\".private-host\",onlyIf:\"responsive:true\",animation:[{opacity:0},{opacity:0,marginBottom:\"-100%\",offset:.001},{opacity:1,marginBottom:\"0\"}],options:{duration:\"--animation-duration\",easing:\"--animation-easing\",fill:\"forwards\"}},{target:\".private-host\",onlyIf:\"responsive:false\",animation:[{opacity:0},{opacity:1}],options:{duration:0,easing:\"--animation-easing\",fill:\"forwards\"}}];export{o as AnimationResponsivePopupEnterConfig,i as AnimationResponsivePopupEnterName};\n"], "mappings": "AAAA,SAAOA,eAAe,IAAIC,CAAC,QAAK,wBAAwB;AAAC,MAAMC,CAAC,GAAC,4BAA4B;EAACC,CAAC,GAAC,CAAC;IAACC,MAAM,EAAC,mBAAmB;IAACC,MAAM,EAAC,iCAAiC;IAACC,SAAS,EAACL,CAAC;IAACM,OAAO,EAAC;MAACC,QAAQ,EAAC,sBAAsB;MAACC,MAAM,EAAC,oBAAoB;MAACC,IAAI,EAAC;IAAU;EAAC,CAAC,EAAC;IAACN,MAAM,EAAC,eAAe;IAACC,MAAM,EAAC,iBAAiB;IAACC,SAAS,EAAC,CAAC;MAACK,OAAO,EAAC;IAAC,CAAC,EAAC;MAACA,OAAO,EAAC,CAAC;MAACC,YAAY,EAAC,OAAO;MAACC,MAAM,EAAC;IAAI,CAAC,EAAC;MAACF,OAAO,EAAC,CAAC;MAACC,YAAY,EAAC;IAAG,CAAC,CAAC;IAACL,OAAO,EAAC;MAACC,QAAQ,EAAC,sBAAsB;MAACC,MAAM,EAAC,oBAAoB;MAACC,IAAI,EAAC;IAAU;EAAC,CAAC,EAAC;IAACN,MAAM,EAAC,eAAe;IAACC,MAAM,EAAC,kBAAkB;IAACC,SAAS,EAAC,CAAC;MAACK,OAAO,EAAC;IAAC,CAAC,EAAC;MAACA,OAAO,EAAC;IAAC,CAAC,CAAC;IAACJ,OAAO,EAAC;MAACC,QAAQ,EAAC,CAAC;MAACC,MAAM,EAAC,oBAAoB;MAACC,IAAI,EAAC;IAAU;EAAC,CAAC,CAAC;AAAC,SAAOP,CAAC,IAAIW,mCAAmC,EAACZ,CAAC,IAAIa,iCAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}