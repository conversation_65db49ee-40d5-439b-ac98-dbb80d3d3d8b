{"ast": null, "code": "import _curry1 from \"./_curry1.js\";\nimport _isPlaceholder from \"./_isPlaceholder.js\";\n/**\n * Optimized internal two-arity curry function.\n *\n * @private\n * @category Function\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\n\nexport default function _curry2(fn) {\n  return function f2(a, b) {\n    switch (arguments.length) {\n      case 0:\n        return f2;\n      case 1:\n        return _isPlaceholder(a) ? f2 : _curry1(function (_b) {\n          return fn(a, _b);\n        });\n      default:\n        return _isPlaceholder(a) && _isPlaceholder(b) ? f2 : _isPlaceholder(a) ? _curry1(function (_a) {\n          return fn(_a, b);\n        }) : _isPlaceholder(b) ? _curry1(function (_b) {\n          return fn(a, _b);\n        }) : fn(a, b);\n    }\n  };\n}", "map": {"version": 3, "names": ["_curry1", "_isPlaceholder", "_curry2", "fn", "f2", "a", "b", "arguments", "length", "_b", "_a"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_curry2.js"], "sourcesContent": ["import _curry1 from \"./_curry1.js\";\nimport _isPlaceholder from \"./_isPlaceholder.js\";\n/**\n * Optimized internal two-arity curry function.\n *\n * @private\n * @category Function\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\n\nexport default function _curry2(fn) {\n  return function f2(a, b) {\n    switch (arguments.length) {\n      case 0:\n        return f2;\n\n      case 1:\n        return _isPlaceholder(a) ? f2 : _curry1(function (_b) {\n          return fn(a, _b);\n        });\n\n      default:\n        return _isPlaceholder(a) && _isPlaceholder(b) ? f2 : _isPlaceholder(a) ? _curry1(function (_a) {\n          return fn(_a, b);\n        }) : _isPlaceholder(b) ? _curry1(function (_b) {\n          return fn(a, _b);\n        }) : fn(a, b);\n    }\n  };\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,cAAc;AAClC,OAAOC,cAAc,MAAM,qBAAqB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,OAAOA,CAACC,EAAE,EAAE;EAClC,OAAO,SAASC,EAAEA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACvB,QAAQC,SAAS,CAACC,MAAM;MACtB,KAAK,CAAC;QACJ,OAAOJ,EAAE;MAEX,KAAK,CAAC;QACJ,OAAOH,cAAc,CAACI,CAAC,CAAC,GAAGD,EAAE,GAAGJ,OAAO,CAAC,UAAUS,EAAE,EAAE;UACpD,OAAON,EAAE,CAACE,CAAC,EAAEI,EAAE,CAAC;QAClB,CAAC,CAAC;MAEJ;QACE,OAAOR,cAAc,CAACI,CAAC,CAAC,IAAIJ,cAAc,CAACK,CAAC,CAAC,GAAGF,EAAE,GAAGH,cAAc,CAACI,CAAC,CAAC,GAAGL,OAAO,CAAC,UAAUU,EAAE,EAAE;UAC7F,OAAOP,EAAE,CAACO,EAAE,EAAEJ,CAAC,CAAC;QAClB,CAAC,CAAC,GAAGL,cAAc,CAACK,CAAC,CAAC,GAAGN,OAAO,CAAC,UAAUS,EAAE,EAAE;UAC7C,OAAON,EAAE,CAACE,CAAC,EAAEI,EAAE,CAAC;QAClB,CAAC,CAAC,GAAGN,EAAE,CAACE,CAAC,EAAEC,CAAC,CAAC;IACjB;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}