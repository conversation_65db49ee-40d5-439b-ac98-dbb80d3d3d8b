{"ast": null, "code": "import _includes from \"./_includes.js\";\nvar _Set = /*#__PURE__*/\nfunction () {\n  function _Set() {\n    /* globals Set */\n    this._nativeSet = typeof Set === 'function' ? new Set() : null;\n    this._items = {};\n  }\n\n  // until we figure out why jsdo<PERSON> chokes on this\n  // @param item The item to add to the Set\n  // @returns {boolean} true if the item did not exist prior, otherwise false\n  //\n  _Set.prototype.add = function (item) {\n    return !hasOrAdd(item, true, this);\n  }; //\n  // @param item The item to check for existence in the Set\n  // @returns {boolean} true if the item exists in the Set, otherwise false\n  //\n\n  _Set.prototype.has = function (item) {\n    return hasOrAdd(item, false, this);\n  }; //\n  // Combines the logic for checking whether an item is a member of the set and\n  // for adding a new item to the set.\n  //\n  // @param item       The item to check or add to the Set instance.\n  // @param shouldAdd  If true, the item will be added to the set if it doesn't\n  //                   already exist.\n  // @param set        The set instance to check or add to.\n  // @return {boolean} true if the item already existed, otherwise false.\n  //\n\n  return _Set;\n}();\nfunction hasOrAdd(item, shouldAdd, set) {\n  var type = typeof item;\n  var prevSize, newSize;\n  switch (type) {\n    case 'string':\n    case 'number':\n      // distinguish between +0 and -0\n      if (item === 0 && 1 / item === -Infinity) {\n        if (set._items['-0']) {\n          return true;\n        } else {\n          if (shouldAdd) {\n            set._items['-0'] = true;\n          }\n          return false;\n        }\n      } // these types can all utilise the native Set\n\n      if (set._nativeSet !== null) {\n        if (shouldAdd) {\n          prevSize = set._nativeSet.size;\n          set._nativeSet.add(item);\n          newSize = set._nativeSet.size;\n          return newSize === prevSize;\n        } else {\n          return set._nativeSet.has(item);\n        }\n      } else {\n        if (!(type in set._items)) {\n          if (shouldAdd) {\n            set._items[type] = {};\n            set._items[type][item] = true;\n          }\n          return false;\n        } else if (item in set._items[type]) {\n          return true;\n        } else {\n          if (shouldAdd) {\n            set._items[type][item] = true;\n          }\n          return false;\n        }\n      }\n    case 'boolean':\n      // set._items['boolean'] holds a two element array\n      // representing [ falseExists, trueExists ]\n      if (type in set._items) {\n        var bIdx = item ? 1 : 0;\n        if (set._items[type][bIdx]) {\n          return true;\n        } else {\n          if (shouldAdd) {\n            set._items[type][bIdx] = true;\n          }\n          return false;\n        }\n      } else {\n        if (shouldAdd) {\n          set._items[type] = item ? [false, true] : [true, false];\n        }\n        return false;\n      }\n    case 'function':\n      // compare functions for reference equality\n      if (set._nativeSet !== null) {\n        if (shouldAdd) {\n          prevSize = set._nativeSet.size;\n          set._nativeSet.add(item);\n          newSize = set._nativeSet.size;\n          return newSize === prevSize;\n        } else {\n          return set._nativeSet.has(item);\n        }\n      } else {\n        if (!(type in set._items)) {\n          if (shouldAdd) {\n            set._items[type] = [item];\n          }\n          return false;\n        }\n        if (!_includes(item, set._items[type])) {\n          if (shouldAdd) {\n            set._items[type].push(item);\n          }\n          return false;\n        }\n        return true;\n      }\n    case 'undefined':\n      if (set._items[type]) {\n        return true;\n      } else {\n        if (shouldAdd) {\n          set._items[type] = true;\n        }\n        return false;\n      }\n    case 'object':\n      if (item === null) {\n        if (!set._items['null']) {\n          if (shouldAdd) {\n            set._items['null'] = true;\n          }\n          return false;\n        }\n        return true;\n      }\n\n    /* falls through */\n\n    default:\n      // reduce the search size of heterogeneous sets by creating buckets\n      // for each type.\n      type = Object.prototype.toString.call(item);\n      if (!(type in set._items)) {\n        if (shouldAdd) {\n          set._items[type] = [item];\n        }\n        return false;\n      } // scan through all previously applied items\n\n      if (!_includes(item, set._items[type])) {\n        if (shouldAdd) {\n          set._items[type].push(item);\n        }\n        return false;\n      }\n      return true;\n  }\n} // A simple Set type that honours R.equals semantics\n\nexport default _Set;", "map": {"version": 3, "names": ["_includes", "_Set", "_nativeSet", "Set", "_items", "prototype", "add", "item", "hasOrAdd", "has", "shouldAdd", "set", "type", "prevSize", "newSize", "Infinity", "size", "bIdx", "push", "Object", "toString", "call"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_Set.js"], "sourcesContent": ["import _includes from \"./_includes.js\";\n\nvar _Set =\n/*#__PURE__*/\nfunction () {\n  function _Set() {\n    /* globals Set */\n    this._nativeSet = typeof Set === 'function' ? new Set() : null;\n    this._items = {};\n  }\n\n  // until we figure out why jsdo<PERSON> chokes on this\n  // @param item The item to add to the Set\n  // @returns {boolean} true if the item did not exist prior, otherwise false\n  //\n  _Set.prototype.add = function (item) {\n    return !hasOrAdd(item, true, this);\n  }; //\n  // @param item The item to check for existence in the Set\n  // @returns {boolean} true if the item exists in the Set, otherwise false\n  //\n\n\n  _Set.prototype.has = function (item) {\n    return hasOrAdd(item, false, this);\n  }; //\n  // Combines the logic for checking whether an item is a member of the set and\n  // for adding a new item to the set.\n  //\n  // @param item       The item to check or add to the Set instance.\n  // @param shouldAdd  If true, the item will be added to the set if it doesn't\n  //                   already exist.\n  // @param set        The set instance to check or add to.\n  // @return {boolean} true if the item already existed, otherwise false.\n  //\n\n\n  return _Set;\n}();\n\nfunction hasOrAdd(item, shouldAdd, set) {\n  var type = typeof item;\n  var prevSize, newSize;\n\n  switch (type) {\n    case 'string':\n    case 'number':\n      // distinguish between +0 and -0\n      if (item === 0 && 1 / item === -Infinity) {\n        if (set._items['-0']) {\n          return true;\n        } else {\n          if (shouldAdd) {\n            set._items['-0'] = true;\n          }\n\n          return false;\n        }\n      } // these types can all utilise the native Set\n\n\n      if (set._nativeSet !== null) {\n        if (shouldAdd) {\n          prevSize = set._nativeSet.size;\n\n          set._nativeSet.add(item);\n\n          newSize = set._nativeSet.size;\n          return newSize === prevSize;\n        } else {\n          return set._nativeSet.has(item);\n        }\n      } else {\n        if (!(type in set._items)) {\n          if (shouldAdd) {\n            set._items[type] = {};\n            set._items[type][item] = true;\n          }\n\n          return false;\n        } else if (item in set._items[type]) {\n          return true;\n        } else {\n          if (shouldAdd) {\n            set._items[type][item] = true;\n          }\n\n          return false;\n        }\n      }\n\n    case 'boolean':\n      // set._items['boolean'] holds a two element array\n      // representing [ falseExists, trueExists ]\n      if (type in set._items) {\n        var bIdx = item ? 1 : 0;\n\n        if (set._items[type][bIdx]) {\n          return true;\n        } else {\n          if (shouldAdd) {\n            set._items[type][bIdx] = true;\n          }\n\n          return false;\n        }\n      } else {\n        if (shouldAdd) {\n          set._items[type] = item ? [false, true] : [true, false];\n        }\n\n        return false;\n      }\n\n    case 'function':\n      // compare functions for reference equality\n      if (set._nativeSet !== null) {\n        if (shouldAdd) {\n          prevSize = set._nativeSet.size;\n\n          set._nativeSet.add(item);\n\n          newSize = set._nativeSet.size;\n          return newSize === prevSize;\n        } else {\n          return set._nativeSet.has(item);\n        }\n      } else {\n        if (!(type in set._items)) {\n          if (shouldAdd) {\n            set._items[type] = [item];\n          }\n\n          return false;\n        }\n\n        if (!_includes(item, set._items[type])) {\n          if (shouldAdd) {\n            set._items[type].push(item);\n          }\n\n          return false;\n        }\n\n        return true;\n      }\n\n    case 'undefined':\n      if (set._items[type]) {\n        return true;\n      } else {\n        if (shouldAdd) {\n          set._items[type] = true;\n        }\n\n        return false;\n      }\n\n    case 'object':\n      if (item === null) {\n        if (!set._items['null']) {\n          if (shouldAdd) {\n            set._items['null'] = true;\n          }\n\n          return false;\n        }\n\n        return true;\n      }\n\n    /* falls through */\n\n    default:\n      // reduce the search size of heterogeneous sets by creating buckets\n      // for each type.\n      type = Object.prototype.toString.call(item);\n\n      if (!(type in set._items)) {\n        if (shouldAdd) {\n          set._items[type] = [item];\n        }\n\n        return false;\n      } // scan through all previously applied items\n\n\n      if (!_includes(item, set._items[type])) {\n        if (shouldAdd) {\n          set._items[type].push(item);\n        }\n\n        return false;\n      }\n\n      return true;\n  }\n} // A simple Set type that honours R.equals semantics\n\n\nexport default _Set;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AAEtC,IAAIC,IAAI,GACR;AACA,YAAY;EACV,SAASA,IAAIA,CAAA,EAAG;IACd;IACA,IAAI,CAACC,UAAU,GAAG,OAAOC,GAAG,KAAK,UAAU,GAAG,IAAIA,GAAG,CAAC,CAAC,GAAG,IAAI;IAC9D,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;EAClB;;EAEA;EACA;EACA;EACA;EACAH,IAAI,CAACI,SAAS,CAACC,GAAG,GAAG,UAAUC,IAAI,EAAE;IACnC,OAAO,CAACC,QAAQ,CAACD,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACpC,CAAC,CAAC,CAAC;EACH;EACA;EACA;;EAGAN,IAAI,CAACI,SAAS,CAACI,GAAG,GAAG,UAAUF,IAAI,EAAE;IACnC,OAAOC,QAAQ,CAACD,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;EACpC,CAAC,CAAC,CAAC;EACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAGA,OAAON,IAAI;AACb,CAAC,CAAC,CAAC;AAEH,SAASO,QAAQA,CAACD,IAAI,EAAEG,SAAS,EAAEC,GAAG,EAAE;EACtC,IAAIC,IAAI,GAAG,OAAOL,IAAI;EACtB,IAAIM,QAAQ,EAAEC,OAAO;EAErB,QAAQF,IAAI;IACV,KAAK,QAAQ;IACb,KAAK,QAAQ;MACX;MACA,IAAIL,IAAI,KAAK,CAAC,IAAI,CAAC,GAAGA,IAAI,KAAK,CAACQ,QAAQ,EAAE;QACxC,IAAIJ,GAAG,CAACP,MAAM,CAAC,IAAI,CAAC,EAAE;UACpB,OAAO,IAAI;QACb,CAAC,MAAM;UACL,IAAIM,SAAS,EAAE;YACbC,GAAG,CAACP,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI;UACzB;UAEA,OAAO,KAAK;QACd;MACF,CAAC,CAAC;;MAGF,IAAIO,GAAG,CAACT,UAAU,KAAK,IAAI,EAAE;QAC3B,IAAIQ,SAAS,EAAE;UACbG,QAAQ,GAAGF,GAAG,CAACT,UAAU,CAACc,IAAI;UAE9BL,GAAG,CAACT,UAAU,CAACI,GAAG,CAACC,IAAI,CAAC;UAExBO,OAAO,GAAGH,GAAG,CAACT,UAAU,CAACc,IAAI;UAC7B,OAAOF,OAAO,KAAKD,QAAQ;QAC7B,CAAC,MAAM;UACL,OAAOF,GAAG,CAACT,UAAU,CAACO,GAAG,CAACF,IAAI,CAAC;QACjC;MACF,CAAC,MAAM;QACL,IAAI,EAAEK,IAAI,IAAID,GAAG,CAACP,MAAM,CAAC,EAAE;UACzB,IAAIM,SAAS,EAAE;YACbC,GAAG,CAACP,MAAM,CAACQ,IAAI,CAAC,GAAG,CAAC,CAAC;YACrBD,GAAG,CAACP,MAAM,CAACQ,IAAI,CAAC,CAACL,IAAI,CAAC,GAAG,IAAI;UAC/B;UAEA,OAAO,KAAK;QACd,CAAC,MAAM,IAAIA,IAAI,IAAII,GAAG,CAACP,MAAM,CAACQ,IAAI,CAAC,EAAE;UACnC,OAAO,IAAI;QACb,CAAC,MAAM;UACL,IAAIF,SAAS,EAAE;YACbC,GAAG,CAACP,MAAM,CAACQ,IAAI,CAAC,CAACL,IAAI,CAAC,GAAG,IAAI;UAC/B;UAEA,OAAO,KAAK;QACd;MACF;IAEF,KAAK,SAAS;MACZ;MACA;MACA,IAAIK,IAAI,IAAID,GAAG,CAACP,MAAM,EAAE;QACtB,IAAIa,IAAI,GAAGV,IAAI,GAAG,CAAC,GAAG,CAAC;QAEvB,IAAII,GAAG,CAACP,MAAM,CAACQ,IAAI,CAAC,CAACK,IAAI,CAAC,EAAE;UAC1B,OAAO,IAAI;QACb,CAAC,MAAM;UACL,IAAIP,SAAS,EAAE;YACbC,GAAG,CAACP,MAAM,CAACQ,IAAI,CAAC,CAACK,IAAI,CAAC,GAAG,IAAI;UAC/B;UAEA,OAAO,KAAK;QACd;MACF,CAAC,MAAM;QACL,IAAIP,SAAS,EAAE;UACbC,GAAG,CAACP,MAAM,CAACQ,IAAI,CAAC,GAAGL,IAAI,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;QACzD;QAEA,OAAO,KAAK;MACd;IAEF,KAAK,UAAU;MACb;MACA,IAAII,GAAG,CAACT,UAAU,KAAK,IAAI,EAAE;QAC3B,IAAIQ,SAAS,EAAE;UACbG,QAAQ,GAAGF,GAAG,CAACT,UAAU,CAACc,IAAI;UAE9BL,GAAG,CAACT,UAAU,CAACI,GAAG,CAACC,IAAI,CAAC;UAExBO,OAAO,GAAGH,GAAG,CAACT,UAAU,CAACc,IAAI;UAC7B,OAAOF,OAAO,KAAKD,QAAQ;QAC7B,CAAC,MAAM;UACL,OAAOF,GAAG,CAACT,UAAU,CAACO,GAAG,CAACF,IAAI,CAAC;QACjC;MACF,CAAC,MAAM;QACL,IAAI,EAAEK,IAAI,IAAID,GAAG,CAACP,MAAM,CAAC,EAAE;UACzB,IAAIM,SAAS,EAAE;YACbC,GAAG,CAACP,MAAM,CAACQ,IAAI,CAAC,GAAG,CAACL,IAAI,CAAC;UAC3B;UAEA,OAAO,KAAK;QACd;QAEA,IAAI,CAACP,SAAS,CAACO,IAAI,EAAEI,GAAG,CAACP,MAAM,CAACQ,IAAI,CAAC,CAAC,EAAE;UACtC,IAAIF,SAAS,EAAE;YACbC,GAAG,CAACP,MAAM,CAACQ,IAAI,CAAC,CAACM,IAAI,CAACX,IAAI,CAAC;UAC7B;UAEA,OAAO,KAAK;QACd;QAEA,OAAO,IAAI;MACb;IAEF,KAAK,WAAW;MACd,IAAII,GAAG,CAACP,MAAM,CAACQ,IAAI,CAAC,EAAE;QACpB,OAAO,IAAI;MACb,CAAC,MAAM;QACL,IAAIF,SAAS,EAAE;UACbC,GAAG,CAACP,MAAM,CAACQ,IAAI,CAAC,GAAG,IAAI;QACzB;QAEA,OAAO,KAAK;MACd;IAEF,KAAK,QAAQ;MACX,IAAIL,IAAI,KAAK,IAAI,EAAE;QACjB,IAAI,CAACI,GAAG,CAACP,MAAM,CAAC,MAAM,CAAC,EAAE;UACvB,IAAIM,SAAS,EAAE;YACbC,GAAG,CAACP,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI;UAC3B;UAEA,OAAO,KAAK;QACd;QAEA,OAAO,IAAI;MACb;;IAEF;;IAEA;MACE;MACA;MACAQ,IAAI,GAAGO,MAAM,CAACd,SAAS,CAACe,QAAQ,CAACC,IAAI,CAACd,IAAI,CAAC;MAE3C,IAAI,EAAEK,IAAI,IAAID,GAAG,CAACP,MAAM,CAAC,EAAE;QACzB,IAAIM,SAAS,EAAE;UACbC,GAAG,CAACP,MAAM,CAACQ,IAAI,CAAC,GAAG,CAACL,IAAI,CAAC;QAC3B;QAEA,OAAO,KAAK;MACd,CAAC,CAAC;;MAGF,IAAI,CAACP,SAAS,CAACO,IAAI,EAAEI,GAAG,CAACP,MAAM,CAACQ,IAAI,CAAC,CAAC,EAAE;QACtC,IAAIF,SAAS,EAAE;UACbC,GAAG,CAACP,MAAM,CAACQ,IAAI,CAAC,CAACM,IAAI,CAACX,IAAI,CAAC;QAC7B;QAEA,OAAO,KAAK;MACd;MAEA,OAAO,IAAI;EACf;AACF,CAAC,CAAC;;AAGF,eAAeN,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}