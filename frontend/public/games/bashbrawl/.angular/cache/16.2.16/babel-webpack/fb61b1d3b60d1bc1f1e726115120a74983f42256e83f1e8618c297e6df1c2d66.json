{"ast": null, "code": "export default function _isPlaceholder(a) {\n  return a != null && typeof a === 'object' && a['@@functional/placeholder'] === true;\n}", "map": {"version": 3, "names": ["_isPlaceholder", "a"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_isPlaceholder.js"], "sourcesContent": ["export default function _isPlaceholder(a) {\n  return a != null && typeof a === 'object' && a['@@functional/placeholder'] === true;\n}"], "mappings": "AAAA,eAAe,SAASA,cAAcA,CAACC,CAAC,EAAE;EACxC,OAAOA,CAAC,IAAI,IAAI,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAAC,0BAA0B,CAAC,KAAK,IAAI;AACrF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}