{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst e = \"volume-up\",\n  d = [\"volume-up\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M16.514 31.3989C16.8881 31.776 17.397 31.9878 17.9276 31.9874C18.1912 31.9685 18.4484 31.8973 18.6842 31.7779C19.431 31.4705 19.9186 30.7416 19.9186 29.9327V5.99477C19.9237 5.17635 19.4294 4.43783 18.6717 4.13197C17.914 3.82611 17.0468 4.01498 16.4842 4.60837L9.18734 11.9793H3.99095C2.89138 11.9793 2 12.8724 2 13.9741V21.9534C2 23.0551 2.89138 23.9482 3.99095 23.9482H9.1077L16.514 31.3989ZM3.99097 21.9534V13.9741H9.60545C9.87149 13.973 10.126 13.8652 10.3122 13.6749L17.9276 5.99477V29.9925L10.2326 22.2526C10.0464 22.0622 9.79185 21.9545 9.52581 21.9534H3.99097Z\"/><path d=\"M25.5131 31.1096C25.0544 31.1069 24.6569 30.7904 24.5506 30.3433C24.4444 29.8961 24.6569 29.4341 25.0652 29.2245C29.3265 27.1208 32.0111 22.76 31.975 18.0003C31.939 13.2407 29.1886 8.92121 24.8959 6.88248C24.5655 6.73368 24.3419 6.41679 24.3119 6.05507C24.282 5.69334 24.4506 5.34389 24.7521 5.14265C25.0536 4.94141 25.4404 4.92014 25.762 5.08713C30.7482 7.44565 33.9485 12.454 33.9994 17.9784C34.0503 23.5028 30.9428 28.5695 26.0009 31.0199C25.8485 31.0905 25.6807 31.1214 25.5131 31.1096Z\"/><path d=\"M22.3443 24.4771C22.4696 24.9073 22.8659 25.201 23.3132 25.195C23.4947 25.1859 23.6703 25.1273 23.8209 25.0254C26.2485 23.5017 27.7028 20.8158 27.6539 17.946C27.605 15.0762 26.0601 12.4416 23.582 11.0018C23.2734 10.8052 22.8821 10.794 22.5628 10.9726C22.2435 11.1511 22.0476 11.4907 22.0525 11.857C22.0575 12.2234 22.2625 12.5575 22.5865 12.7273C24.4689 13.813 25.6445 15.809 25.6833 17.9849C25.722 20.1608 24.6182 22.1974 22.7756 23.3498C22.3954 23.5858 22.2189 24.0469 22.3443 24.4771Z\"/>',\n    solid: '<path d=\"M18.2918 3.99401L9.05361 11.8079H3.06772C2.51673 11.8079 2.07007 12.2452 2.07007 12.7847V22.4739C2.07007 23.0133 2.51673 23.4506 3.06772 23.4506H8.86405L18.3018 31.499C18.5933 31.7498 19.0069 31.8124 19.3621 31.6595C19.7173 31.5065 19.9497 31.1658 19.9579 30.786V4.69726C19.9493 4.31524 19.7139 3.97324 19.3555 3.82195C18.9971 3.67067 18.5815 3.7379 18.2918 3.99401Z\"/><path d=\"M25.5647 30.5418C25.1049 30.5391 24.7065 30.2292 24.6 29.7913C24.4936 29.3534 24.7065 28.901 25.1157 28.6957C29.3863 26.6356 32.0768 22.3651 32.0406 17.7042C32.0045 13.0432 29.2482 8.81326 24.9461 6.81678C24.615 6.67108 24.3908 6.36075 24.3608 6.00652C24.3309 5.6523 24.4998 5.31009 24.8019 5.11302C25.1041 4.91595 25.4917 4.89513 25.8141 5.05865C30.8111 7.36828 34.0184 12.2729 34.0694 17.6827C34.1204 23.0926 31.0062 28.0543 26.0535 30.4539C25.9007 30.5231 25.7326 30.5533 25.5647 30.5418Z\"/><path d=\"M22.3889 24.0467C22.5145 24.468 22.9117 24.7556 23.3599 24.7497C23.5418 24.7409 23.7178 24.6834 23.8687 24.5837C26.3017 23.0916 27.7591 20.4613 27.7101 17.651C27.6611 14.8407 26.1129 12.2606 23.6293 10.8507C23.32 10.6582 22.9279 10.6473 22.6079 10.8221C22.2879 10.9969 22.0916 11.3295 22.0965 11.6882C22.1015 12.047 22.3069 12.3742 22.6316 12.5405C24.5182 13.6037 25.6964 15.5582 25.7352 17.6891C25.774 19.8199 24.6678 21.8143 22.8212 22.9427C22.4401 23.1739 22.2633 23.6254 22.3889 24.0467Z\"/>'\n  })];\nexport { d as volumeUpIcon, e as volumeUpIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "e", "d", "outline", "solid", "volumeUpIcon", "volumeUpIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/volume-up.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const e=\"volume-up\",d=[\"volume-up\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M16.514 31.3989C16.8881 31.776 17.397 31.9878 17.9276 31.9874C18.1912 31.9685 18.4484 31.8973 18.6842 31.7779C19.431 31.4705 19.9186 30.7416 19.9186 29.9327V5.99477C19.9237 5.17635 19.4294 4.43783 18.6717 4.13197C17.914 3.82611 17.0468 4.01498 16.4842 4.60837L9.18734 11.9793H3.99095C2.89138 11.9793 2 12.8724 2 13.9741V21.9534C2 23.0551 2.89138 23.9482 3.99095 23.9482H9.1077L16.514 31.3989ZM3.99097 21.9534V13.9741H9.60545C9.87149 13.973 10.126 13.8652 10.3122 13.6749L17.9276 5.99477V29.9925L10.2326 22.2526C10.0464 22.0622 9.79185 21.9545 9.52581 21.9534H3.99097Z\"/><path d=\"M25.5131 31.1096C25.0544 31.1069 24.6569 30.7904 24.5506 30.3433C24.4444 29.8961 24.6569 29.4341 25.0652 29.2245C29.3265 27.1208 32.0111 22.76 31.975 18.0003C31.939 13.2407 29.1886 8.92121 24.8959 6.88248C24.5655 6.73368 24.3419 6.41679 24.3119 6.05507C24.282 5.69334 24.4506 5.34389 24.7521 5.14265C25.0536 4.94141 25.4404 4.92014 25.762 5.08713C30.7482 7.44565 33.9485 12.454 33.9994 17.9784C34.0503 23.5028 30.9428 28.5695 26.0009 31.0199C25.8485 31.0905 25.6807 31.1214 25.5131 31.1096Z\"/><path d=\"M22.3443 24.4771C22.4696 24.9073 22.8659 25.201 23.3132 25.195C23.4947 25.1859 23.6703 25.1273 23.8209 25.0254C26.2485 23.5017 27.7028 20.8158 27.6539 17.946C27.605 15.0762 26.0601 12.4416 23.582 11.0018C23.2734 10.8052 22.8821 10.794 22.5628 10.9726C22.2435 11.1511 22.0476 11.4907 22.0525 11.857C22.0575 12.2234 22.2625 12.5575 22.5865 12.7273C24.4689 13.813 25.6445 15.809 25.6833 17.9849C25.722 20.1608 24.6182 22.1974 22.7756 23.3498C22.3954 23.5858 22.2189 24.0469 22.3443 24.4771Z\"/>',solid:'<path d=\"M18.2918 3.99401L9.05361 11.8079H3.06772C2.51673 11.8079 2.07007 12.2452 2.07007 12.7847V22.4739C2.07007 23.0133 2.51673 23.4506 3.06772 23.4506H8.86405L18.3018 31.499C18.5933 31.7498 19.0069 31.8124 19.3621 31.6595C19.7173 31.5065 19.9497 31.1658 19.9579 30.786V4.69726C19.9493 4.31524 19.7139 3.97324 19.3555 3.82195C18.9971 3.67067 18.5815 3.7379 18.2918 3.99401Z\"/><path d=\"M25.5647 30.5418C25.1049 30.5391 24.7065 30.2292 24.6 29.7913C24.4936 29.3534 24.7065 28.901 25.1157 28.6957C29.3863 26.6356 32.0768 22.3651 32.0406 17.7042C32.0045 13.0432 29.2482 8.81326 24.9461 6.81678C24.615 6.67108 24.3908 6.36075 24.3608 6.00652C24.3309 5.6523 24.4998 5.31009 24.8019 5.11302C25.1041 4.91595 25.4917 4.89513 25.8141 5.05865C30.8111 7.36828 34.0184 12.2729 34.0694 17.6827C34.1204 23.0926 31.0062 28.0543 26.0535 30.4539C25.9007 30.5231 25.7326 30.5533 25.5647 30.5418Z\"/><path d=\"M22.3889 24.0467C22.5145 24.468 22.9117 24.7556 23.3599 24.7497C23.5418 24.7409 23.7178 24.6834 23.8687 24.5837C26.3017 23.0916 27.7591 20.4613 27.7101 17.651C27.6611 14.8407 26.1129 12.2606 23.6293 10.8507C23.32 10.6582 22.9279 10.6473 22.6079 10.8221C22.2879 10.9969 22.0916 11.3295 22.0965 11.6882C22.1015 12.047 22.3069 12.3742 22.6316 12.5405C24.5182 13.6037 25.6964 15.5582 25.7352 17.6891C25.774 19.8199 24.6678 21.8143 22.8212 22.9427C22.4401 23.1739 22.2633 23.6254 22.3889 24.0467Z\"/>'})];export{d as volumeUpIcon,e as volumeUpIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,WAAW;EAACC,CAAC,GAAC,CAAC,WAAW,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,slDAAslD;IAACC,KAAK,EAAC;EAA02C,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,YAAY,EAACJ,CAAC,IAAIK,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}