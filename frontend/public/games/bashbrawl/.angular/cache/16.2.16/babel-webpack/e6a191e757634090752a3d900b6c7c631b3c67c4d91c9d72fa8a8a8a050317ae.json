{"ast": null, "code": "\"use strict\";\n\n/**\n * Zone.js flags to disable problematic patches\n */\n// 禁用导致 \"Cannot set property once\" 错误的补丁\nwindow.__Zone_disable_addEventListener = true; // 禁用addEventListener补丁\nwindow.__Zone_disable_on_property = true; // 禁用onProperty补丁\nwindow.__zone_symbol__UNPATCHED_EVENTS = ['scroll', 'mousemove', 'click', 'load']; // 禁用特定事件补丁\nwindow.__Zone_enable_cross_context_check = true; // iframe环境兼容性", "map": {"version": 3, "names": ["window", "__Zone_disable_addEventListener", "__Zone_disable_on_property", "__zone_symbol__UNPATCHED_EVENTS", "__Zone_enable_cross_context_check"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/src/zone-flags.ts"], "sourcesContent": ["/**\n * Zone.js flags to disable problematic patches\n */\n\n// 禁用导致 \"Cannot set property once\" 错误的补丁\n(window as any).__Zone_disable_addEventListener = true; // 禁用addEventListener补丁\n(window as any).__Zone_disable_on_property = true; // 禁用onProperty补丁\n(window as any).__zone_symbol__UNPATCHED_EVENTS = ['scroll', 'mousemove', 'click', 'load']; // 禁用特定事件补丁\n(window as any).__Zone_enable_cross_context_check = true; // iframe环境兼容性\n"], "mappings": ";;AAAA;;;AAIA;AACCA,MAAc,CAACC,+BAA+B,GAAG,IAAI,CAAC,CAAC;AACvDD,MAAc,CAACE,0BAA0B,GAAG,IAAI,CAAC,CAAC;AAClDF,MAAc,CAACG,+BAA+B,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;AAC3FH,MAAc,CAACI,iCAAiC,GAAG,IAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}