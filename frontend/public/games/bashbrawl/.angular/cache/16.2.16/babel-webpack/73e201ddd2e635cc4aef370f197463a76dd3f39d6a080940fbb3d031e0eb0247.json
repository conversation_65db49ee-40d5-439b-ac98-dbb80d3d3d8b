{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"unpin\",\n  n = [\"unpin\", C({\n    outline: '<path d=\"M24.1697 19.8993L28.5176 15.5616L27.1083 14.1556L22.7603 18.5033L24.1697 19.9093V19.8993ZM32.2859 16.2995C32.4758 16.489 32.7257 16.5887 32.9955 16.5887C33.3953 16.5887 33.7552 16.3494 33.9151 15.9804C34.075 15.6115 33.9851 15.1827 33.7052 14.8935L21.1311 2.32912C20.7313 1.99008 20.1416 2.01002 19.7717 2.37898C19.4019 2.74793 19.3819 3.33626 19.7218 3.73513L32.2859 16.2895V16.2995ZM21.8907 8.91046L20.4614 7.50444L16.1034 11.8621L17.5228 13.2781L21.8907 8.92043V8.91046ZM33.6952 32.2144L3.70926 2.28923C3.31944 1.90033 2.67974 1.90033 2.28992 2.28923C1.9001 2.67813 1.9001 3.31632 2.28992 3.70522L12.715 14.1057C9.34661 13.6071 5.91822 14.6941 3.45937 17.1072C3.26946 17.2967 3.15951 17.546 3.15951 17.8152C3.15951 18.0844 3.26946 18.3337 3.45937 18.5232L9.77641 24.8353L2.34989 32.2443C2.07002 32.4836 1.94009 32.8725 2.03004 33.2315C2.12 33.6004 2.39987 33.8797 2.7697 33.9694C3.13952 34.0591 3.51935 33.9295 3.75923 33.6503L11.1858 26.2313L17.5028 32.5434C17.6927 32.7329 17.9426 32.8326 18.2125 32.8326C18.4823 32.8326 18.7322 32.7229 18.9221 32.5335C21.331 30.0705 22.4105 26.6402 21.9207 23.2697L32.2859 33.6104C32.4858 33.8098 32.7357 33.8996 32.9955 33.8996C33.2554 33.8996 33.5053 33.7999 33.7052 33.6104C34.095 33.2215 34.095 32.5933 33.7052 32.2044L33.6952 32.2144ZM18.1525 30.4095L5.62836 17.8651C7.91728 16.0801 10.9259 15.5117 13.7046 16.3394C14.0544 16.4491 14.4442 16.3494 14.7041 16.0901L19.9516 21.3153L19.9317 21.3352C19.6718 21.5945 19.5718 21.9834 19.6818 22.3324C20.5014 25.1145 19.9416 28.116 18.1525 30.3995V30.4095Z\"/>',\n    solid: '<path d=\"M28.5176 15.5616L20.4514 7.50444L16.0935 11.8621L24.1597 19.9093L28.5076 15.5716L28.5176 15.5616ZM32.2759 16.2995C32.4658 16.489 32.7157 16.5887 32.9855 16.5887C33.3853 16.5887 33.7452 16.3494 33.9051 15.9804C34.065 15.6115 33.9751 15.1827 33.6952 14.8935L21.1311 2.32912C20.7313 1.99008 20.1416 2.01002 19.7717 2.37898C19.4019 2.74793 19.3819 3.33626 19.7218 3.73513L32.2859 16.2895L32.2759 16.2995ZM33.6852 32.2144L3.70926 2.28923C3.31944 1.90033 2.67974 1.90033 2.28992 2.28923C1.9001 2.67813 1.9001 3.31632 2.28992 3.70522L12.715 14.1057C9.34661 13.6071 5.91822 14.6941 3.45937 17.1072C3.26946 17.2967 3.15951 17.546 3.15951 17.8152C3.15951 18.0844 3.26946 18.3337 3.45937 18.5232L9.77641 24.8353L2.34989 32.2443C2.07002 32.4836 1.94009 32.8725 2.03004 33.2315C2.12 33.6004 2.39987 33.8797 2.7697 33.9694C3.13952 34.0591 3.51935 33.9295 3.75923 33.6503L11.1858 26.2313L17.5028 32.5434C17.6927 32.7329 17.9426 32.8326 18.2125 32.8326C18.4823 32.8326 18.7322 32.7229 18.9221 32.5335C21.331 30.0705 22.4105 26.6402 21.9207 23.2697L32.2859 33.6104C32.4858 33.8098 32.7357 33.8996 32.9955 33.8996C33.2554 33.8996 33.5053 33.7999 33.7052 33.6104C34.095 33.2215 34.095 32.5933 33.7052 32.2044L33.6852 32.2144Z\"/>'\n  })];\nexport { n as unpinIcon, L as unpinIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "n", "outline", "solid", "unpinIcon", "unpinIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/unpin.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"unpin\",n=[\"unpin\",C({outline:'<path d=\"M24.1697 19.8993L28.5176 15.5616L27.1083 14.1556L22.7603 18.5033L24.1697 19.9093V19.8993ZM32.2859 16.2995C32.4758 16.489 32.7257 16.5887 32.9955 16.5887C33.3953 16.5887 33.7552 16.3494 33.9151 15.9804C34.075 15.6115 33.9851 15.1827 33.7052 14.8935L21.1311 2.32912C20.7313 1.99008 20.1416 2.01002 19.7717 2.37898C19.4019 2.74793 19.3819 3.33626 19.7218 3.73513L32.2859 16.2895V16.2995ZM21.8907 8.91046L20.4614 7.50444L16.1034 11.8621L17.5228 13.2781L21.8907 8.92043V8.91046ZM33.6952 32.2144L3.70926 2.28923C3.31944 1.90033 2.67974 1.90033 2.28992 2.28923C1.9001 2.67813 1.9001 3.31632 2.28992 3.70522L12.715 14.1057C9.34661 13.6071 5.91822 14.6941 3.45937 17.1072C3.26946 17.2967 3.15951 17.546 3.15951 17.8152C3.15951 18.0844 3.26946 18.3337 3.45937 18.5232L9.77641 24.8353L2.34989 32.2443C2.07002 32.4836 1.94009 32.8725 2.03004 33.2315C2.12 33.6004 2.39987 33.8797 2.7697 33.9694C3.13952 34.0591 3.51935 33.9295 3.75923 33.6503L11.1858 26.2313L17.5028 32.5434C17.6927 32.7329 17.9426 32.8326 18.2125 32.8326C18.4823 32.8326 18.7322 32.7229 18.9221 32.5335C21.331 30.0705 22.4105 26.6402 21.9207 23.2697L32.2859 33.6104C32.4858 33.8098 32.7357 33.8996 32.9955 33.8996C33.2554 33.8996 33.5053 33.7999 33.7052 33.6104C34.095 33.2215 34.095 32.5933 33.7052 32.2044L33.6952 32.2144ZM18.1525 30.4095L5.62836 17.8651C7.91728 16.0801 10.9259 15.5117 13.7046 16.3394C14.0544 16.4491 14.4442 16.3494 14.7041 16.0901L19.9516 21.3153L19.9317 21.3352C19.6718 21.5945 19.5718 21.9834 19.6818 22.3324C20.5014 25.1145 19.9416 28.116 18.1525 30.3995V30.4095Z\"/>',solid:'<path d=\"M28.5176 15.5616L20.4514 7.50444L16.0935 11.8621L24.1597 19.9093L28.5076 15.5716L28.5176 15.5616ZM32.2759 16.2995C32.4658 16.489 32.7157 16.5887 32.9855 16.5887C33.3853 16.5887 33.7452 16.3494 33.9051 15.9804C34.065 15.6115 33.9751 15.1827 33.6952 14.8935L21.1311 2.32912C20.7313 1.99008 20.1416 2.01002 19.7717 2.37898C19.4019 2.74793 19.3819 3.33626 19.7218 3.73513L32.2859 16.2895L32.2759 16.2995ZM33.6852 32.2144L3.70926 2.28923C3.31944 1.90033 2.67974 1.90033 2.28992 2.28923C1.9001 2.67813 1.9001 3.31632 2.28992 3.70522L12.715 14.1057C9.34661 13.6071 5.91822 14.6941 3.45937 17.1072C3.26946 17.2967 3.15951 17.546 3.15951 17.8152C3.15951 18.0844 3.26946 18.3337 3.45937 18.5232L9.77641 24.8353L2.34989 32.2443C2.07002 32.4836 1.94009 32.8725 2.03004 33.2315C2.12 33.6004 2.39987 33.8797 2.7697 33.9694C3.13952 34.0591 3.51935 33.9295 3.75923 33.6503L11.1858 26.2313L17.5028 32.5434C17.6927 32.7329 17.9426 32.8326 18.2125 32.8326C18.4823 32.8326 18.7322 32.7229 18.9221 32.5335C21.331 30.0705 22.4105 26.6402 21.9207 23.2697L32.2859 33.6104C32.4858 33.8098 32.7357 33.8996 32.9955 33.8996C33.2554 33.8996 33.5053 33.7999 33.7052 33.6104C34.095 33.2215 34.095 32.5933 33.7052 32.2044L33.6852 32.2144Z\"/>'})];export{n as unpinIcon,L as unpinIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,OAAO;EAACC,CAAC,GAAC,CAAC,OAAO,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,qhDAAqhD;IAACC,KAAK,EAAC;EAAosC,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,SAAS,EAACJ,CAAC,IAAIK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}