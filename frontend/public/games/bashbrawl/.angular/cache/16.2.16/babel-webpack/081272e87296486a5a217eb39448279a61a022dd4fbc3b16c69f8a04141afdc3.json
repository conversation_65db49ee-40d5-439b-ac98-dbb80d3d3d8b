{"ast": null, "code": "import { renderIcon as e } from \"../icon.renderer.js\";\nconst o = \"window-restore\",\n  r = [\"window-restore\", e({\n    outline: '<path d=\"M14 8H28C29.1046 8 30 8.89543 30 10V20C30 21.1046 29.1046 22 28 22H26V20H28V10H14V12H12V10C12 8.89543 12.8954 8 14 8Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M22 14H8C6.89543 14 6 14.8954 6 16V26C6 27.1046 6.89543 28 8 28H22C23.1046 28 24 27.1046 24 26V16C24 14.8954 23.1046 14 22 14ZM8 26V16H22V26H8Z\"/>'\n  })];\nexport { r as windowRestoreIcon, o as windowRestoreIconName };", "map": {"version": 3, "names": ["renderIcon", "e", "o", "r", "outline", "windowRestoreIcon", "windowRestoreIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/window-restore.js"], "sourcesContent": ["import{renderIcon as e}from\"../icon.renderer.js\";const o=\"window-restore\",r=[\"window-restore\",e({outline:'<path d=\"M14 8H28C29.1046 8 30 8.89543 30 10V20C30 21.1046 29.1046 22 28 22H26V20H28V10H14V12H12V10C12 8.89543 12.8954 8 14 8Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M22 14H8C6.89543 14 6 14.8954 6 16V26C6 27.1046 6.89543 28 8 28H22C23.1046 28 24 27.1046 24 26V16C24 14.8954 23.1046 14 22 14ZM8 26V16H22V26H8Z\"/>'})];export{r as windowRestoreIcon,o as windowRestoreIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,gBAAgB;EAACC,CAAC,GAAC,CAAC,gBAAgB,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAsU,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,iBAAiB,EAACH,CAAC,IAAII,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}