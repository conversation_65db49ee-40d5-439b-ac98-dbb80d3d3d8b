{"ast": null, "code": "export const phpConfig = {\n  name: 'PHP 8',\n  cmds: [\n  // keywords\n  ['and'], ['array'], ['as'], ['break'], ['callable'], ['case'], ['catch'], ['class'], ['clone'], ['const'], ['continue'], ['declare'], ['default'], ['die'], ['do'], ['echo'], ['else'], ['elseif'], ['empty'], ['enddeclare'], ['endfor'], ['endforeach'], ['endif'], ['endswitch'], ['endwhile'], ['eval'], ['exit'], ['extends'], ['final'], ['finally'], ['for'], ['foreach'], ['function'], ['global'], ['goto'], ['if'], ['implements'], ['include'], ['include_once'], ['instanceof'], ['insteadof'], ['interface'], ['isset'], ['list'], ['namespace'], ['new'], ['or'], ['print'], ['private'], ['protected'], ['public'], ['require'], ['require_once'], ['return'], ['static'], ['switch'], ['throw'], ['trait'], ['try'], ['unset'], ['use'], ['var'], ['while'], ['xor'], ['yield'],\n  // standard library functions\n  ['abs'], ['array_change_key_case'], ['array_chunk'], ['array_column'], ['array_combine'], ['array_count_values'], ['array_diff'], ['array_filter'], ['array_flip'], ['array_intersect'], ['array_key_exists'], ['array_keys'], ['array_map'], ['array_merge'], ['array_merge_recursive'], ['array_multisort'], ['array_pad'], ['array_pop'], ['array_push'], ['array_rand'], ['array_reduce'], ['array_reverse'], ['array_search'], ['array_shift'], ['array_splice'], ['array_sum'], ['array_udiff'], ['array_uintersect'], ['array_unique'], ['array_unshift'], ['array_values'], ['array_walk'], ['assert'], ['basename'], ['bcadd'], ['bcdiv'], ['bcmul'], ['bcpow'], ['bcsub'], ['bin2hex'], ['chmod'], ['chown'], ['chgrp'], ['clearstatcache'], ['closedir'], ['copy'], ['date'], ['dirname'], ['disk_free_space'], ['disk_total_space'], ['diskfreespace'], ['exec'], ['file'], ['file_get_contents'], ['file_put_contents'], ['fopen'], ['fread'], ['fwrite'], ['fclose'], ['fgets'], ['fgetcsv'], ['flock'], ['filesize'], ['filetype'], ['file_exists'], ['fileatime'], ['filectime'], ['filemtime'], ['fileinode'], ['finfo_file'], ['finfo_open'], ['flock'], ['getcwd'], ['gethostbyaddr'], ['gethostbyname'], ['gethostname'], ['getprotobyname'], ['getservbyname'], ['getservbyport'], ['getmypid'], ['getlastmod'], ['gettype'], ['http_build_query'], ['htmlspecialchars'], ['htmlspecialchars_decode'], ['implode'], ['in_array'], ['ini_get'], ['ini_set'], ['is_array'], ['is_bool'], ['is_callable'], ['is_dir'], ['is_file'], ['is_int'], ['is_numeric'], ['is_object'], ['is_readable'], ['is_writable'], ['is_writeable'], ['json_decode'], ['json_encode'], ['ksort'], ['mkdir'], ['move_uploaded_file'], ['parse_url'], ['pathinfo'], ['print_r'], ['readfile'], ['realpath'], ['rename'], ['rmdir'], ['setcookie'], ['setlocale'], ['sleep'], ['str_replace'], ['strlen'], ['strpos'], ['strtolower'], ['strtoupper'], ['strtr'], ['trim'], ['ucfirst'], ['var_dump'], ['version_compare'], ['vprintf'], ['xml_parser_create'], ['xml_parse'], ['xml_parse_into_struct'], ['xml_set_character_data_handler'], ['xml_set_element_handler'], ['xml_set_end_namespace_decl_handler'], ['xml_set_start_namespace_decl_handler'], ['xmlwriter_end_document'], ['xmlwriter_end_element'], ['xmlwriter_flush'], ['xmlwriter_open_memory'], ['xmlwriter_open_uri'], ['xmlwriter_start_attribute'], ['xmlwriter_start_document'], ['xmlwriter_start_element'], ['xmlwriter_start_element_ns'], ['xmlwriter_write_attribute'], ['xmlwriter_write_cdata'], ['xmlwriter_write_comment'], ['xmlwriter_write_element'], ['xmlwriter_write_element_ns'], ['xmlwriter_write_pi'], ['xmlwriter_write_raw'], ['xmlwriter_write_string']]\n};", "map": {"version": 3, "names": ["phpConfig", "name", "cmds"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/src/app/terminals/bashbrawl/languages/php.ts"], "sourcesContent": ["/**\n * This is a list of PHP 8.x keywords, standard library functions, and common objects.\n */\nimport { LanguageConfig } from './language-config.interface';\nexport const phpConfig: LanguageConfig = {\n  name: 'PHP 8',\n  cmds: [\n    // keywords\n    ['and'],\n    ['array'],\n    ['as'],\n    ['break'],\n    ['callable'],\n    ['case'],\n    ['catch'],\n    ['class'],\n    ['clone'],\n    ['const'],\n    ['continue'],\n    ['declare'],\n    ['default'],\n    ['die'],\n    ['do'],\n    ['echo'],\n    ['else'],\n    ['elseif'],\n    ['empty'],\n    ['enddeclare'],\n    ['endfor'],\n    ['endforeach'],\n    ['endif'],\n    ['endswitch'],\n    ['endwhile'],\n    ['eval'],\n    ['exit'],\n    ['extends'],\n    ['final'],\n    ['finally'],\n    ['for'],\n    ['foreach'],\n    ['function'],\n    ['global'],\n    ['goto'],\n    ['if'],\n    ['implements'],\n    ['include'],\n    ['include_once'],\n    ['instanceof'],\n    ['insteadof'],\n    ['interface'],\n    ['isset'],\n    ['list'],\n    ['namespace'],\n    ['new'],\n    ['or'],\n    ['print'],\n    ['private'],\n    ['protected'],\n    ['public'],\n    ['require'],\n    ['require_once'],\n    ['return'],\n    ['static'],\n    ['switch'],\n    ['throw'],\n    ['trait'],\n    ['try'],\n    ['unset'],\n    ['use'],\n    ['var'],\n    ['while'],\n    ['xor'],\n    ['yield'],\n    // standard library functions\n    ['abs'],\n    ['array_change_key_case'],\n    ['array_chunk'],\n    ['array_column'],\n    ['array_combine'],\n    ['array_count_values'],\n    ['array_diff'],\n    ['array_filter'],\n    ['array_flip'],\n    ['array_intersect'],\n    ['array_key_exists'],\n    ['array_keys'],\n    ['array_map'],\n    ['array_merge'],\n    ['array_merge_recursive'],\n    ['array_multisort'],\n    ['array_pad'],\n    ['array_pop'],\n    ['array_push'],\n    ['array_rand'],\n    ['array_reduce'],\n    ['array_reverse'],\n    ['array_search'],\n    ['array_shift'],\n    ['array_splice'],\n    ['array_sum'],\n    ['array_udiff'],\n    ['array_uintersect'],\n    ['array_unique'],\n    ['array_unshift'],\n    ['array_values'],\n    ['array_walk'],\n    ['assert'],\n    ['basename'],\n    ['bcadd'],\n    ['bcdiv'],\n    ['bcmul'],\n    ['bcpow'],\n    ['bcsub'],\n    ['bin2hex'],\n    ['chmod'],\n    ['chown'],\n    ['chgrp'],\n    ['clearstatcache'],\n    ['closedir'],\n    ['copy'],\n    ['date'],\n    ['dirname'],\n    ['disk_free_space'],\n    ['disk_total_space'],\n    ['diskfreespace'],\n    ['exec'],\n    ['file'],\n    ['file_get_contents'],\n    ['file_put_contents'],\n    ['fopen'],\n    ['fread'],\n    ['fwrite'],\n    ['fclose'],\n    ['fgets'],\n    ['fgetcsv'],\n    ['flock'],\n    ['filesize'],\n    ['filetype'],\n    ['file_exists'],\n    ['fileatime'],\n    ['filectime'],\n    ['filemtime'],\n    ['fileinode'],\n    ['finfo_file'],\n    ['finfo_open'],\n    ['flock'],\n    ['getcwd'],\n    ['gethostbyaddr'],\n    ['gethostbyname'],\n    ['gethostname'],\n    ['getprotobyname'],\n    ['getservbyname'],\n    ['getservbyport'],\n    ['getmypid'],\n    ['getlastmod'],\n    ['gettype'],\n    ['http_build_query'],\n    ['htmlspecialchars'],\n    ['htmlspecialchars_decode'],\n    ['implode'],\n    ['in_array'],\n    ['ini_get'],\n    ['ini_set'],\n    ['is_array'],\n    ['is_bool'],\n    ['is_callable'],\n    ['is_dir'],\n    ['is_file'],\n    ['is_int'],\n    ['is_numeric'],\n    ['is_object'],\n    ['is_readable'],\n    ['is_writable'],\n    ['is_writeable'],\n    ['json_decode'],\n    ['json_encode'],\n    ['ksort'],\n    ['mkdir'],\n    ['move_uploaded_file'],\n    ['parse_url'],\n    ['pathinfo'],\n    ['print_r'],\n    ['readfile'],\n    ['realpath'],\n    ['rename'],\n    ['rmdir'],\n    ['setcookie'],\n    ['setlocale'],\n    ['sleep'],\n    ['str_replace'],\n    ['strlen'],\n    ['strpos'],\n    ['strtolower'],\n    ['strtoupper'],\n    ['strtr'],\n    ['trim'],\n    ['ucfirst'],\n    ['var_dump'],\n    ['version_compare'],\n    ['vprintf'],\n    ['xml_parser_create'],\n    ['xml_parse'],\n    ['xml_parse_into_struct'],\n    ['xml_set_character_data_handler'],\n    ['xml_set_element_handler'],\n    ['xml_set_end_namespace_decl_handler'],\n    ['xml_set_start_namespace_decl_handler'],\n    ['xmlwriter_end_document'],\n    ['xmlwriter_end_element'],\n    ['xmlwriter_flush'],\n    ['xmlwriter_open_memory'],\n    ['xmlwriter_open_uri'],\n    ['xmlwriter_start_attribute'],\n    ['xmlwriter_start_document'],\n    ['xmlwriter_start_element'],\n    ['xmlwriter_start_element_ns'],\n    ['xmlwriter_write_attribute'],\n    ['xmlwriter_write_cdata'],\n    ['xmlwriter_write_comment'],\n    ['xmlwriter_write_element'],\n    ['xmlwriter_write_element_ns'],\n    ['xmlwriter_write_pi'],\n    ['xmlwriter_write_raw'],\n    ['xmlwriter_write_string'],\n  ],\n};\n"], "mappings": "AAIA,OAAO,MAAMA,SAAS,GAAmB;EACvCC,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE;EACJ;EACA,CAAC,KAAK,CAAC,EACP,CAAC,OAAO,CAAC,EACT,CAAC,IAAI,CAAC,EACN,CAAC,OAAO,CAAC,EACT,CAAC,UAAU,CAAC,EACZ,CAAC,MAAM,CAAC,EACR,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,UAAU,CAAC,EACZ,CAAC,SAAS,CAAC,EACX,CAAC,SAAS,CAAC,EACX,CAAC,KAAK,CAAC,EACP,CAAC,IAAI,CAAC,EACN,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,YAAY,CAAC,EACd,CAAC,QAAQ,CAAC,EACV,CAAC,YAAY,CAAC,EACd,CAAC,OAAO,CAAC,EACT,CAAC,WAAW,CAAC,EACb,CAAC,UAAU,CAAC,EACZ,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,SAAS,CAAC,EACX,CAAC,OAAO,CAAC,EACT,CAAC,SAAS,CAAC,EACX,CAAC,KAAK,CAAC,EACP,CAAC,SAAS,CAAC,EACX,CAAC,UAAU,CAAC,EACZ,CAAC,QAAQ,CAAC,EACV,CAAC,MAAM,CAAC,EACR,CAAC,IAAI,CAAC,EACN,CAAC,YAAY,CAAC,EACd,CAAC,SAAS,CAAC,EACX,CAAC,cAAc,CAAC,EAChB,CAAC,YAAY,CAAC,EACd,CAAC,WAAW,CAAC,EACb,CAAC,WAAW,CAAC,EACb,CAAC,OAAO,CAAC,EACT,CAAC,MAAM,CAAC,EACR,CAAC,WAAW,CAAC,EACb,CAAC,KAAK,CAAC,EACP,CAAC,IAAI,CAAC,EACN,CAAC,OAAO,CAAC,EACT,CAAC,SAAS,CAAC,EACX,CAAC,WAAW,CAAC,EACb,CAAC,QAAQ,CAAC,EACV,CAAC,SAAS,CAAC,EACX,CAAC,cAAc,CAAC,EAChB,CAAC,QAAQ,CAAC,EACV,CAAC,QAAQ,CAAC,EACV,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,KAAK,CAAC,EACP,CAAC,OAAO,CAAC,EACT,CAAC,KAAK,CAAC,EACP,CAAC,KAAK,CAAC,EACP,CAAC,OAAO,CAAC,EACT,CAAC,KAAK,CAAC,EACP,CAAC,OAAO,CAAC;EACT;EACA,CAAC,KAAK,CAAC,EACP,CAAC,uBAAuB,CAAC,EACzB,CAAC,aAAa,CAAC,EACf,CAAC,cAAc,CAAC,EAChB,CAAC,eAAe,CAAC,EACjB,CAAC,oBAAoB,CAAC,EACtB,CAAC,YAAY,CAAC,EACd,CAAC,cAAc,CAAC,EAChB,CAAC,YAAY,CAAC,EACd,CAAC,iBAAiB,CAAC,EACnB,CAAC,kBAAkB,CAAC,EACpB,CAAC,YAAY,CAAC,EACd,CAAC,WAAW,CAAC,EACb,CAAC,aAAa,CAAC,EACf,CAAC,uBAAuB,CAAC,EACzB,CAAC,iBAAiB,CAAC,EACnB,CAAC,WAAW,CAAC,EACb,CAAC,WAAW,CAAC,EACb,CAAC,YAAY,CAAC,EACd,CAAC,YAAY,CAAC,EACd,CAAC,cAAc,CAAC,EAChB,CAAC,eAAe,CAAC,EACjB,CAAC,cAAc,CAAC,EAChB,CAAC,aAAa,CAAC,EACf,CAAC,cAAc,CAAC,EAChB,CAAC,WAAW,CAAC,EACb,CAAC,aAAa,CAAC,EACf,CAAC,kBAAkB,CAAC,EACpB,CAAC,cAAc,CAAC,EAChB,CAAC,eAAe,CAAC,EACjB,CAAC,cAAc,CAAC,EAChB,CAAC,YAAY,CAAC,EACd,CAAC,QAAQ,CAAC,EACV,CAAC,UAAU,CAAC,EACZ,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,SAAS,CAAC,EACX,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,gBAAgB,CAAC,EAClB,CAAC,UAAU,CAAC,EACZ,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,SAAS,CAAC,EACX,CAAC,iBAAiB,CAAC,EACnB,CAAC,kBAAkB,CAAC,EACpB,CAAC,eAAe,CAAC,EACjB,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,mBAAmB,CAAC,EACrB,CAAC,mBAAmB,CAAC,EACrB,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,QAAQ,CAAC,EACV,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,SAAS,CAAC,EACX,CAAC,OAAO,CAAC,EACT,CAAC,UAAU,CAAC,EACZ,CAAC,UAAU,CAAC,EACZ,CAAC,aAAa,CAAC,EACf,CAAC,WAAW,CAAC,EACb,CAAC,WAAW,CAAC,EACb,CAAC,WAAW,CAAC,EACb,CAAC,WAAW,CAAC,EACb,CAAC,YAAY,CAAC,EACd,CAAC,YAAY,CAAC,EACd,CAAC,OAAO,CAAC,EACT,CAAC,QAAQ,CAAC,EACV,CAAC,eAAe,CAAC,EACjB,CAAC,eAAe,CAAC,EACjB,CAAC,aAAa,CAAC,EACf,CAAC,gBAAgB,CAAC,EAClB,CAAC,eAAe,CAAC,EACjB,CAAC,eAAe,CAAC,EACjB,CAAC,UAAU,CAAC,EACZ,CAAC,YAAY,CAAC,EACd,CAAC,SAAS,CAAC,EACX,CAAC,kBAAkB,CAAC,EACpB,CAAC,kBAAkB,CAAC,EACpB,CAAC,yBAAyB,CAAC,EAC3B,CAAC,SAAS,CAAC,EACX,CAAC,UAAU,CAAC,EACZ,CAAC,SAAS,CAAC,EACX,CAAC,SAAS,CAAC,EACX,CAAC,UAAU,CAAC,EACZ,CAAC,SAAS,CAAC,EACX,CAAC,aAAa,CAAC,EACf,CAAC,QAAQ,CAAC,EACV,CAAC,SAAS,CAAC,EACX,CAAC,QAAQ,CAAC,EACV,CAAC,YAAY,CAAC,EACd,CAAC,WAAW,CAAC,EACb,CAAC,aAAa,CAAC,EACf,CAAC,aAAa,CAAC,EACf,CAAC,cAAc,CAAC,EAChB,CAAC,aAAa,CAAC,EACf,CAAC,aAAa,CAAC,EACf,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,oBAAoB,CAAC,EACtB,CAAC,WAAW,CAAC,EACb,CAAC,UAAU,CAAC,EACZ,CAAC,SAAS,CAAC,EACX,CAAC,UAAU,CAAC,EACZ,CAAC,UAAU,CAAC,EACZ,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,WAAW,CAAC,EACb,CAAC,WAAW,CAAC,EACb,CAAC,OAAO,CAAC,EACT,CAAC,aAAa,CAAC,EACf,CAAC,QAAQ,CAAC,EACV,CAAC,QAAQ,CAAC,EACV,CAAC,YAAY,CAAC,EACd,CAAC,YAAY,CAAC,EACd,CAAC,OAAO,CAAC,EACT,CAAC,MAAM,CAAC,EACR,CAAC,SAAS,CAAC,EACX,CAAC,UAAU,CAAC,EACZ,CAAC,iBAAiB,CAAC,EACnB,CAAC,SAAS,CAAC,EACX,CAAC,mBAAmB,CAAC,EACrB,CAAC,WAAW,CAAC,EACb,CAAC,uBAAuB,CAAC,EACzB,CAAC,gCAAgC,CAAC,EAClC,CAAC,yBAAyB,CAAC,EAC3B,CAAC,oCAAoC,CAAC,EACtC,CAAC,sCAAsC,CAAC,EACxC,CAAC,wBAAwB,CAAC,EAC1B,CAAC,uBAAuB,CAAC,EACzB,CAAC,iBAAiB,CAAC,EACnB,CAAC,uBAAuB,CAAC,EACzB,CAAC,oBAAoB,CAAC,EACtB,CAAC,2BAA2B,CAAC,EAC7B,CAAC,0BAA0B,CAAC,EAC5B,CAAC,yBAAyB,CAAC,EAC3B,CAAC,4BAA4B,CAAC,EAC9B,CAAC,2BAA2B,CAAC,EAC7B,CAAC,uBAAuB,CAAC,EACzB,CAAC,yBAAyB,CAAC,EAC3B,CAAC,yBAAyB,CAAC,EAC3B,CAAC,4BAA4B,CAAC,EAC9B,CAAC,oBAAoB,CAAC,EACtB,CAAC,qBAAqB,CAAC,EACvB,CAAC,wBAAwB,CAAC;CAE7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}