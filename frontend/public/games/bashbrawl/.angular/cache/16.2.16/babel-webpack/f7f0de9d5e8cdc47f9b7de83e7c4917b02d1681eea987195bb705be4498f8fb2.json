{"ast": null, "code": "import { renderIcon as L } from \"../icon.renderer.js\";\nconst H = \"hotel\",\n  V = [\"hotel\", L({\n    outline: '<path d=\"M10.9375 7.11001L12.9375 5.06001L10.1775 4.64001L8.9375 2.01001L7.7075 4.64001L4.9475 5.06001L6.9475 7.11001L6.4775 10L8.9375 8.63001L11.4075 10L10.9375 7.11001ZM12.9375 26H16.9375V24H12.9375V26ZM12.9375 22H16.9375V20H12.9375V22ZM26.4675 10L28.9275 8.63001L31.3975 10L30.9275 7.11001L32.9275 5.06001L30.1675 4.64001L28.9275 2.01001L27.6975 4.64001L24.9375 5.06001L26.9375 7.11001L26.4675 10ZM2.9375 19V34H4.9375V20H6.9375V18H3.9375C3.3875 18 2.9375 18.45 2.9375 19ZM16.4675 10L18.9275 8.63001L21.3975 10L20.9275 7.11001L22.9275 5.06001L20.1675 4.64001L18.9275 2.01001L17.6975 4.64001L14.9375 5.06001L16.9375 7.11001L16.4675 10ZM27.9375 12H9.9375C9.3875 12 8.9375 12.45 8.9375 13V34H10.9375V14H26.9375V34H28.9375V13C28.9375 12.45 28.4875 12 27.9375 12ZM33.9375 18H30.9375V20H32.9375V34H34.9375V19C34.9375 18.45 34.4875 18 33.9375 18ZM12.9375 18H16.9375V16H12.9375V18ZM20.9375 18H24.9375V16H20.9375V18ZM20.9375 22H24.9375V20H20.9375V22ZM20.9375 26H24.9375V24H20.9375V26Z\"/>',\n    solid: '<path d=\"M26.4675 10L28.9275 8.63001L31.3975 10L30.9275 7.11001L32.9275 5.06001L30.1675 4.64001L28.9275 2.01001L27.6975 4.64001L24.9375 5.06001L26.9375 7.11001L26.4675 10ZM2.9375 19V34H4.9375V20H6.9375V18H3.9375C3.3875 18 2.9375 18.45 2.9375 19ZM16.4675 10L18.9275 8.63001L21.3975 10L20.9275 7.11001L22.9275 5.06001L20.1675 4.64001L18.9275 2.01001L17.6975 4.64001L14.9375 5.06001L16.9375 7.11001L16.4675 10ZM27.9375 12H9.9375C9.3875 12 8.9375 12.45 8.9375 13V34H28.9375V13C28.9375 12.45 28.4875 12 27.9375 12ZM17.0375 26.1H12.8375V23.9H17.0375V26.1ZM17.0375 22.1H12.8375V19.9H17.0375V22.1ZM17.0375 18.1H12.8375V15.9H17.0375V18.1ZM25.0375 26.1H20.8375V23.9H25.0375V26.1ZM25.0375 22.1H20.8375V19.9H25.0375V22.1ZM25.0375 18.1H20.8375V15.9H25.0375V18.1ZM33.9375 18H30.9375V20H32.9375V34H34.9375V19C34.9375 18.45 34.4875 18 33.9375 18ZM10.9375 7.11001L12.9375 5.06001L10.1775 4.64001L8.9375 2.01001L7.7075 4.64001L4.9475 5.06001L6.9475 7.11001L6.4775 10L8.9375 8.63001L11.4075 10L10.9375 7.11001Z\"/>'\n  })];\nexport { V as hotelIcon, H as hotelIconName };", "map": {"version": 3, "names": ["renderIcon", "L", "H", "V", "outline", "solid", "hotelIcon", "hotelIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/hotel.js"], "sourcesContent": ["import{renderIcon as L}from\"../icon.renderer.js\";const H=\"hotel\",V=[\"hotel\",L({outline:'<path d=\"M10.9375 7.11001L12.9375 5.06001L10.1775 4.64001L8.9375 2.01001L7.7075 4.64001L4.9475 5.06001L6.9475 7.11001L6.4775 10L8.9375 8.63001L11.4075 10L10.9375 7.11001ZM12.9375 26H16.9375V24H12.9375V26ZM12.9375 22H16.9375V20H12.9375V22ZM26.4675 10L28.9275 8.63001L31.3975 10L30.9275 7.11001L32.9275 5.06001L30.1675 4.64001L28.9275 2.01001L27.6975 4.64001L24.9375 5.06001L26.9375 7.11001L26.4675 10ZM2.9375 19V34H4.9375V20H6.9375V18H3.9375C3.3875 18 2.9375 18.45 2.9375 19ZM16.4675 10L18.9275 8.63001L21.3975 10L20.9275 7.11001L22.9275 5.06001L20.1675 4.64001L18.9275 2.01001L17.6975 4.64001L14.9375 5.06001L16.9375 7.11001L16.4675 10ZM27.9375 12H9.9375C9.3875 12 8.9375 12.45 8.9375 13V34H10.9375V14H26.9375V34H28.9375V13C28.9375 12.45 28.4875 12 27.9375 12ZM33.9375 18H30.9375V20H32.9375V34H34.9375V19C34.9375 18.45 34.4875 18 33.9375 18ZM12.9375 18H16.9375V16H12.9375V18ZM20.9375 18H24.9375V16H20.9375V18ZM20.9375 22H24.9375V20H20.9375V22ZM20.9375 26H24.9375V24H20.9375V26Z\"/>',solid:'<path d=\"M26.4675 10L28.9275 8.63001L31.3975 10L30.9275 7.11001L32.9275 5.06001L30.1675 4.64001L28.9275 2.01001L27.6975 4.64001L24.9375 5.06001L26.9375 7.11001L26.4675 10ZM2.9375 19V34H4.9375V20H6.9375V18H3.9375C3.3875 18 2.9375 18.45 2.9375 19ZM16.4675 10L18.9275 8.63001L21.3975 10L20.9275 7.11001L22.9275 5.06001L20.1675 4.64001L18.9275 2.01001L17.6975 4.64001L14.9375 5.06001L16.9375 7.11001L16.4675 10ZM27.9375 12H9.9375C9.3875 12 8.9375 12.45 8.9375 13V34H28.9375V13C28.9375 12.45 28.4875 12 27.9375 12ZM17.0375 26.1H12.8375V23.9H17.0375V26.1ZM17.0375 22.1H12.8375V19.9H17.0375V22.1ZM17.0375 18.1H12.8375V15.9H17.0375V18.1ZM25.0375 26.1H20.8375V23.9H25.0375V26.1ZM25.0375 22.1H20.8375V19.9H25.0375V22.1ZM25.0375 18.1H20.8375V15.9H25.0375V18.1ZM33.9375 18H30.9375V20H32.9375V34H34.9375V19C34.9375 18.45 34.4875 18 33.9375 18ZM10.9375 7.11001L12.9375 5.06001L10.1775 4.64001L8.9375 2.01001L7.7075 4.64001L4.9475 5.06001L6.9475 7.11001L6.4775 10L8.9375 8.63001L11.4075 10L10.9375 7.11001Z\"/>'})];export{V as hotelIcon,H as hotelIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,OAAO;EAACC,CAAC,GAAC,CAAC,OAAO,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,s9BAAs9B;IAACC,KAAK,EAAC;EAAo+B,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,SAAS,EAACJ,CAAC,IAAIK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}