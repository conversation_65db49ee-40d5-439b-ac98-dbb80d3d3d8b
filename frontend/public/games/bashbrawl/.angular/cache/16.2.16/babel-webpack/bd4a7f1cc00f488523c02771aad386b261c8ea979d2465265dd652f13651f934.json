{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst V = \"castle\",\n  H = [\"castle\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M33.9375 14H3.9375C3.3875 14 2.9375 13.55 2.9375 13V3C2.9375 2.45 3.3875 2 3.9375 2H9.9375C10.4875 2 10.9375 2.45 10.9375 3V6H14.9375V3C14.9375 2.45 15.3875 2 15.9375 2H21.9375C22.4875 2 22.9375 2.45 22.9375 3V6H26.9375V3C26.9375 2.45 27.3875 2 27.9375 2H33.9375C34.4875 2 34.9375 2.45 34.9375 3V13C34.9375 13.55 34.4875 14 33.9375 14ZM4.9375 12H32.9375V4H28.9375V7C28.9375 7.55 28.4875 8 27.9375 8H21.9375C21.3875 8 20.9375 7.55 20.9375 7V4H16.9375V7C16.9375 7.55 16.4875 8 15.9375 8H9.9375C9.3875 8 8.9375 7.55 8.9375 7V4H4.9375V12Z\"/><path d=\"M30.9375 33C30.9375 33.55 30.4875 34 29.9375 34H7.9375C7.3875 34 6.9375 33.55 6.9375 33V16H8.9375V32H28.9375V16H30.9375V33Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.9375 27C14.9375 27.55 15.3875 28 15.9375 28H21.9375C22.4875 28 22.9375 27.55 22.9375 27V22C22.9375 19.79 21.1475 18 18.9375 18C16.7275 18 14.9375 19.79 14.9375 22V27ZM20.9375 22V26H16.9375V22C16.9375 20.9 17.8375 20 18.9375 20C20.0375 20 20.9375 20.9 20.9375 22Z\"/>',\n    solid: '<path d=\"M33.9375 14H3.9375C3.3875 14 2.9375 13.55 2.9375 13V3C2.9375 2.45 3.3875 2 3.9375 2H9.9375C10.4875 2 10.9375 2.45 10.9375 3V6H14.9375V3C14.9375 2.45 15.3875 2 15.9375 2H21.9375C22.4875 2 22.9375 2.45 22.9375 3V6H26.9375V3C26.9375 2.45 27.3875 2 27.9375 2H33.9375C34.4875 2 34.9375 2.45 34.9375 3V13C34.9375 13.55 34.4875 14 33.9375 14Z\"/><path d=\"M16.9375 22C16.9375 20.9 17.8375 20 18.9375 20C20.0375 20 20.9375 20.9 20.9375 22V26H16.9375V22Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M6.9375 16V33C6.9375 33.55 7.3875 34 7.9375 34H29.9375C30.4875 34 30.9375 33.55 30.9375 33V16H6.9375ZM22.9375 27C22.9375 27.55 22.4875 28 21.9375 28H15.9375C15.3875 28 14.9375 27.55 14.9375 27V22C14.9375 19.79 16.7275 18 18.9375 18C21.1475 18 22.9375 19.79 22.9375 22V27Z\"/>'\n  })];\nexport { H as castleIcon, V as castleIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "V", "H", "outline", "solid", "castleIcon", "castleIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/castle.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const V=\"castle\",H=[\"castle\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M33.9375 14H3.9375C3.3875 14 2.9375 13.55 2.9375 13V3C2.9375 2.45 3.3875 2 3.9375 2H9.9375C10.4875 2 10.9375 2.45 10.9375 3V6H14.9375V3C14.9375 2.45 15.3875 2 15.9375 2H21.9375C22.4875 2 22.9375 2.45 22.9375 3V6H26.9375V3C26.9375 2.45 27.3875 2 27.9375 2H33.9375C34.4875 2 34.9375 2.45 34.9375 3V13C34.9375 13.55 34.4875 14 33.9375 14ZM4.9375 12H32.9375V4H28.9375V7C28.9375 7.55 28.4875 8 27.9375 8H21.9375C21.3875 8 20.9375 7.55 20.9375 7V4H16.9375V7C16.9375 7.55 16.4875 8 15.9375 8H9.9375C9.3875 8 8.9375 7.55 8.9375 7V4H4.9375V12Z\"/><path d=\"M30.9375 33C30.9375 33.55 30.4875 34 29.9375 34H7.9375C7.3875 34 6.9375 33.55 6.9375 33V16H8.9375V32H28.9375V16H30.9375V33Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.9375 27C14.9375 27.55 15.3875 28 15.9375 28H21.9375C22.4875 28 22.9375 27.55 22.9375 27V22C22.9375 19.79 21.1475 18 18.9375 18C16.7275 18 14.9375 19.79 14.9375 22V27ZM20.9375 22V26H16.9375V22C16.9375 20.9 17.8375 20 18.9375 20C20.0375 20 20.9375 20.9 20.9375 22Z\"/>',solid:'<path d=\"M33.9375 14H3.9375C3.3875 14 2.9375 13.55 2.9375 13V3C2.9375 2.45 3.3875 2 3.9375 2H9.9375C10.4875 2 10.9375 2.45 10.9375 3V6H14.9375V3C14.9375 2.45 15.3875 2 15.9375 2H21.9375C22.4875 2 22.9375 2.45 22.9375 3V6H26.9375V3C26.9375 2.45 27.3875 2 27.9375 2H33.9375C34.4875 2 34.9375 2.45 34.9375 3V13C34.9375 13.55 34.4875 14 33.9375 14Z\"/><path d=\"M16.9375 22C16.9375 20.9 17.8375 20 18.9375 20C20.0375 20 20.9375 20.9 20.9375 22V26H16.9375V22Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M6.9375 16V33C6.9375 33.55 7.3875 34 7.9375 34H29.9375C30.4875 34 30.9375 33.55 30.9375 33V16H6.9375ZM22.9375 27C22.9375 27.55 22.4875 28 21.9375 28H15.9375C15.3875 28 14.9375 27.55 14.9375 27V22C14.9375 19.79 16.7275 18 18.9375 18C21.1475 18 22.9375 19.79 22.9375 22V27Z\"/>'})];export{H as castleIcon,V as castleIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,ihCAAihC;IAACC,KAAK,EAAC;EAA4wB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,UAAU,EAACJ,CAAC,IAAIK,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}