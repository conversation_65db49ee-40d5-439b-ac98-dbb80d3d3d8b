{"ast": null, "code": "import { renderIcon as e } from \"../icon.renderer.js\";\nconst C = \"search\",\n  r = [\"search\", e({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.016 2.0022C7.82748 2.0022 2 7.8225 2 15.0022C2 22.1819 7.82748 28.0022 15.016 28.0022C22.2046 28.0022 28.0321 22.1819 28.0321 15.0022C28.0321 7.8225 22.2046 2.0022 15.016 2.0022ZM15.016 4.05219C19.4513 4.04814 23.452 6.71346 25.1521 10.8049C26.8522 14.8963 25.9167 19.6077 22.782 22.7415C19.6472 25.8752 14.9308 26.8139 10.8328 25.1196C6.7348 23.4254 4.06254 19.432 4.06254 15.0022C4.0899 8.96993 8.97636 4.08503 15.016 4.05219ZM26.33 24.8722L33.7091 32.2922C33.9612 32.5459 34.0588 32.9146 33.9649 33.2596C33.8711 33.6045 33.6001 33.8733 33.254 33.9646C32.908 34.0559 32.5395 33.9559 32.2873 33.7022L24.9082 26.2822L26.33 24.8722Z\"/>'\n  })];\nexport { r as searchIcon, C as searchIconName };", "map": {"version": 3, "names": ["renderIcon", "e", "C", "r", "outline", "searchIcon", "searchIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/search.js"], "sourcesContent": ["import{renderIcon as e}from\"../icon.renderer.js\";const C=\"search\",r=[\"search\",e({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.016 2.0022C7.82748 2.0022 2 7.8225 2 15.0022C2 22.1819 7.82748 28.0022 15.016 28.0022C22.2046 28.0022 28.0321 22.1819 28.0321 15.0022C28.0321 7.8225 22.2046 2.0022 15.016 2.0022ZM15.016 4.05219C19.4513 4.04814 23.452 6.71346 25.1521 10.8049C26.8522 14.8963 25.9167 19.6077 22.782 22.7415C19.6472 25.8752 14.9308 26.8139 10.8328 25.1196C6.7348 23.4254 4.06254 19.432 4.06254 15.0022C4.0899 8.96993 8.97636 4.08503 15.016 4.05219ZM26.33 24.8722L33.7091 32.2922C33.9612 32.5459 34.0588 32.9146 33.9649 33.2596C33.8711 33.6045 33.6001 33.8733 33.254 33.9646C32.908 34.0559 32.5395 33.9559 32.2873 33.7022L24.9082 26.2822L26.33 24.8722Z\"/>'})];export{r as searchIcon,C as searchIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAirB,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,UAAU,EAACH,CAAC,IAAII,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}