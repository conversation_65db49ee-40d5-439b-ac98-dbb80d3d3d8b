{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"crown\",\n  M = [\"crown\", C({\n    outline: '<path d=\"M6.00125 12C6.00125 10.9 5.10097 10 4.00063 10C2.90028 10 2 10.9 2 12C2 13.1 2.90028 14 4.00063 14C5.10097 14 6.00125 13.1 6.00125 12ZM30.5589 16.11L23.4467 19.66L18.8953 10.55C18.5552 9.87 17.4448 9.87 17.1047 10.55L12.5533 19.66L5.44108 16.11C5.09097 15.93 4.66083 15.98 4.35073 16.23C4.05064 16.48 3.9206 16.89 4.03063 17.27L8.03189 31.27C8.15192 31.7 8.55205 32 8.99218 32H26.9978C27.448 32 27.8381 31.7 27.9581 31.27L31.9594 17.27C32.0694 16.89 31.9394 16.48 31.6393 16.23C31.3292 15.98 30.909 15.93 30.5489 16.11H30.5589ZM26.2476 30H9.75242L6.58143 18.91L12.5533 21.89C13.0535 22.14 13.6436 21.94 13.8937 21.44L18.005 13.23L22.1163 21.44C22.3664 21.93 22.9666 22.13 23.4567 21.89L29.4286 18.91L26.2576 30H26.2476ZM12.9934 26C12.4433 26 11.9931 26.45 11.9931 27C11.9931 27.55 12.4433 28 12.9934 28C13.5436 28 13.9937 27.55 13.9937 27C13.9937 26.45 13.5436 26 12.9934 26ZM17.995 8C19.0953 8 19.9956 7.1 19.9956 6C19.9956 4.9 19.0953 4 17.995 4C16.8947 4 15.9944 4.9 15.9944 6C15.9944 7.1 16.8947 8 17.995 8ZM31.9994 10C30.899 10 29.9987 10.9 29.9987 12C29.9987 13.1 30.899 14 31.9994 14C33.0997 14 34 13.1 34 12C34 10.9 33.0997 10 31.9994 10ZM21.9962 27C21.9962 27.55 22.4464 28 22.9966 28C23.5467 28 23.9969 27.55 23.9969 27C23.9969 26.45 23.5467 26 22.9966 26C22.4464 26 21.9962 26.45 21.9962 27ZM17.995 26C17.4448 26 16.9947 26.45 16.9947 27C16.9947 27.55 17.4448 28 17.995 28C18.5452 28 18.9953 27.55 18.9953 27C18.9953 26.45 18.5452 26 17.995 26Z\"/>',\n    solid: '<path d=\"M6 12C6 10.9 5.1 10 4 10C2.9 10 2 10.9 2 12C2 13.1 2.9 14 4 14C5.1 14 6 13.1 6 12ZM18 8C19.1 8 20 7.1 20 6C20 4.9 19.1 4 18 4C16.9 4 16 4.9 16 6C16 7.1 16.9 8 18 8ZM30.55 16.11L23.44 19.66L18.89 10.55C18.55 9.87 17.44 9.87 17.1 10.55L12.55 19.66L5.44 16.11C5.09 15.93 4.66 15.98 4.35 16.23C4.05 16.48 3.92 16.89 4.03 17.27L8.03 31.27C8.15 31.7 8.55 32 8.99 32H26.99C27.44 32 27.83 31.7 27.95 31.27L31.95 17.27C32.06 16.89 31.93 16.48 31.63 16.23C31.32 15.98 30.9 15.93 30.54 16.11H30.55ZM13 28.1C12.39 28.1 11.9 27.61 11.9 27C11.9 26.39 12.39 25.9 13 25.9C13.61 25.9 14.1 26.39 14.1 27C14.1 27.61 13.61 28.1 13 28.1ZM18 28.1C17.39 28.1 16.9 27.61 16.9 27C16.9 26.39 17.39 25.9 18 25.9C18.61 25.9 19.1 26.39 19.1 27C19.1 27.61 18.61 28.1 18 28.1ZM23 28.1C22.39 28.1 21.9 27.61 21.9 27C21.9 26.39 22.39 25.9 23 25.9C23.61 25.9 24.1 26.39 24.1 27C24.1 27.61 23.61 28.1 23 28.1ZM32 10C30.9 10 30 10.9 30 12C30 13.1 30.9 14 32 14C33.1 14 34 13.1 34 12C34 10.9 33.1 10 32 10Z\"/>'\n  })];\nexport { M as crownIcon, L as crownIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "M", "outline", "solid", "crownIcon", "crownIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/crown.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"crown\",M=[\"crown\",C({outline:'<path d=\"M6.00125 12C6.00125 10.9 5.10097 10 4.00063 10C2.90028 10 2 10.9 2 12C2 13.1 2.90028 14 4.00063 14C5.10097 14 6.00125 13.1 6.00125 12ZM30.5589 16.11L23.4467 19.66L18.8953 10.55C18.5552 9.87 17.4448 9.87 17.1047 10.55L12.5533 19.66L5.44108 16.11C5.09097 15.93 4.66083 15.98 4.35073 16.23C4.05064 16.48 3.9206 16.89 4.03063 17.27L8.03189 31.27C8.15192 31.7 8.55205 32 8.99218 32H26.9978C27.448 32 27.8381 31.7 27.9581 31.27L31.9594 17.27C32.0694 16.89 31.9394 16.48 31.6393 16.23C31.3292 15.98 30.909 15.93 30.5489 16.11H30.5589ZM26.2476 30H9.75242L6.58143 18.91L12.5533 21.89C13.0535 22.14 13.6436 21.94 13.8937 21.44L18.005 13.23L22.1163 21.44C22.3664 21.93 22.9666 22.13 23.4567 21.89L29.4286 18.91L26.2576 30H26.2476ZM12.9934 26C12.4433 26 11.9931 26.45 11.9931 27C11.9931 27.55 12.4433 28 12.9934 28C13.5436 28 13.9937 27.55 13.9937 27C13.9937 26.45 13.5436 26 12.9934 26ZM17.995 8C19.0953 8 19.9956 7.1 19.9956 6C19.9956 4.9 19.0953 4 17.995 4C16.8947 4 15.9944 4.9 15.9944 6C15.9944 7.1 16.8947 8 17.995 8ZM31.9994 10C30.899 10 29.9987 10.9 29.9987 12C29.9987 13.1 30.899 14 31.9994 14C33.0997 14 34 13.1 34 12C34 10.9 33.0997 10 31.9994 10ZM21.9962 27C21.9962 27.55 22.4464 28 22.9966 28C23.5467 28 23.9969 27.55 23.9969 27C23.9969 26.45 23.5467 26 22.9966 26C22.4464 26 21.9962 26.45 21.9962 27ZM17.995 26C17.4448 26 16.9947 26.45 16.9947 27C16.9947 27.55 17.4448 28 17.995 28C18.5452 28 18.9953 27.55 18.9953 27C18.9953 26.45 18.5452 26 17.995 26Z\"/>',solid:'<path d=\"M6 12C6 10.9 5.1 10 4 10C2.9 10 2 10.9 2 12C2 13.1 2.9 14 4 14C5.1 14 6 13.1 6 12ZM18 8C19.1 8 20 7.1 20 6C20 4.9 19.1 4 18 4C16.9 4 16 4.9 16 6C16 7.1 16.9 8 18 8ZM30.55 16.11L23.44 19.66L18.89 10.55C18.55 9.87 17.44 9.87 17.1 10.55L12.55 19.66L5.44 16.11C5.09 15.93 4.66 15.98 4.35 16.23C4.05 16.48 3.92 16.89 4.03 17.27L8.03 31.27C8.15 31.7 8.55 32 8.99 32H26.99C27.44 32 27.83 31.7 27.95 31.27L31.95 17.27C32.06 16.89 31.93 16.48 31.63 16.23C31.32 15.98 30.9 15.93 30.54 16.11H30.55ZM13 28.1C12.39 28.1 11.9 27.61 11.9 27C11.9 26.39 12.39 25.9 13 25.9C13.61 25.9 14.1 26.39 14.1 27C14.1 27.61 13.61 28.1 13 28.1ZM18 28.1C17.39 28.1 16.9 27.61 16.9 27C16.9 26.39 17.39 25.9 18 25.9C18.61 25.9 19.1 26.39 19.1 27C19.1 27.61 18.61 28.1 18 28.1ZM23 28.1C22.39 28.1 21.9 27.61 21.9 27C21.9 26.39 22.39 25.9 23 25.9C23.61 25.9 24.1 26.39 24.1 27C24.1 27.61 23.61 28.1 23 28.1ZM32 10C30.9 10 30 10.9 30 12C30 13.1 30.9 14 32 14C33.1 14 34 13.1 34 12C34 10.9 33.1 10 32 10Z\"/>'})];export{M as crownIcon,L as crownIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,OAAO;EAACC,CAAC,GAAC,CAAC,OAAO,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,67CAA67C;IAACC,KAAK,EAAC;EAAu9B,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,SAAS,EAACJ,CAAC,IAAIK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}