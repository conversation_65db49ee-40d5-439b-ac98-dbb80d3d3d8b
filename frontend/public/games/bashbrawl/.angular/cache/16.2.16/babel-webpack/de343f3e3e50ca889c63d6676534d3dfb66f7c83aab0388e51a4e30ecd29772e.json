{"ast": null, "code": "export default function _functionName(f) {\n  // String(x => x) evaluates to \"x => x\", so the pattern may not match.\n  var match = String(f).match(/^function (\\w*)/);\n  return match == null ? '' : match[1];\n}", "map": {"version": 3, "names": ["_functionName", "f", "match", "String"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_functionName.js"], "sourcesContent": ["export default function _functionName(f) {\n  // String(x => x) evaluates to \"x => x\", so the pattern may not match.\n  var match = String(f).match(/^function (\\w*)/);\n  return match == null ? '' : match[1];\n}"], "mappings": "AAAA,eAAe,SAASA,aAAaA,CAACC,CAAC,EAAE;EACvC;EACA,IAAIC,KAAK,GAAGC,MAAM,CAACF,CAAC,CAAC,CAACC,KAAK,CAAC,iBAAiB,CAAC;EAC9C,OAAOA,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}