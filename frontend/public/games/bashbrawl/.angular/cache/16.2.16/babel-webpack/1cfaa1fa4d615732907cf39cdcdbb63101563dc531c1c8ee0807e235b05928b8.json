{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"e-check\",\n  V = [\"e-check\", C({\n    outline: '<path d=\"M30 23V21H22V23H30ZM8.18005 18.57C9.77005 18.62 11.32 18.05 12.5 16.98C12.73 16.76 12.86 16.46 12.86 16.14C12.86 15.85 12.76 15.56 12.55 15.35C12.34 15.14 12.0601 15.02 11.7701 15.03C11.51 15.02 11.26 15.11 11.0601 15.28C10.26 15.94 9.26005 16.3 8.22005 16.28C6.46005 16.35 4.94005 15.04 4.76005 13.28H12.29C12.64 13.28 12.99 13.15 13.24 12.9C13.49 12.65 13.62 12.31 13.61 11.95C13.68 10.39 13.1 8.88003 12.0201 7.76003C10.93 6.64003 9.43005 6.02003 7.88005 6.03003C6.27005 6.06003 4.73005 6.75003 3.63005 7.92003C2.52005 9.10003 1.94005 10.68 2.00005 12.29C1.92005 13.96 2.54005 15.59 3.71005 16.78C4.88005 17.97 6.50005 18.62 8.17005 18.57H8.18005ZM7.86005 8.19003C9.68005 8.19003 10.73 9.58003 10.86 11.35H4.74005C4.99005 9.49003 6.17005 8.19003 7.86005 8.19003ZM30 16H18V18H30V16ZM33 8.00003H23.965H14.93C15.2801 8.63003 15.54 9.30003 15.72 10H32V26H5.00005V19.9C4.27005 19.56 3.59005 19.11 3.00005 18.56V27C3.00005 27.55 3.45005 28 4.00005 28H33C33.55 28 34 27.55 34 27V9.00003C34 8.45003 33.55 8.00003 33 8.00003Z\"/>',\n    solid: '<path d=\"M8.18002 18.54C9.77002 18.58 11.32 18.01 12.5 16.94C12.73 16.72 12.86 16.42 12.86 16.1C12.86 15.81 12.76 15.52 12.55 15.31C12.34 15.1 12.06 14.98 11.77 14.99C11.51 14.98 11.26 15.07 11.06 15.24C10.26 15.9 9.26002 16.26 8.22002 16.24C6.46002 16.31 4.94002 15 4.76002 13.24H12.31C12.66 13.24 12.99 13.1 13.24 12.86C13.48 12.61 13.62 12.27 13.61 11.92C13.68 10.36 13.1 8.85 12.02 7.73C10.94 6.61 9.44002 5.99 7.88002 6C6.26002 6.03 4.72002 6.72 3.61002 7.91C2.50002 9.1 1.93002 10.68 2.00002 12.3C1.93002 13.96 2.56002 15.58 3.73002 16.76C4.90002 17.94 6.51002 18.59 8.17002 18.54H8.18002ZM7.91002 8.25C9.68002 8.25 10.73 9.64 10.91 11.41H4.79002C5.04002 9.55 6.14002 8.25 7.91002 8.25ZM33 8H14.91C15.7 9.33 16.11 10.85 16.11 12.39C16.18 14.52 15.4 16.59 13.94 18.14C12.48 19.69 10.46 20.6 8.33002 20.66C6.32002 20.66 4.40002 19.84 3.00002 18.41V27C3.00002 27.55 3.45002 28 4.00002 28H33C33.55 28 34 27.55 34 27V9C34 8.45 33.55 8 33 8ZM30 23H22V21H30V23ZM30 17.99H18V15.99H30V17.99Z\"/>'\n  })];\nexport { V as eCheckIcon, H as eCheckIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "V", "outline", "solid", "eCheckIcon", "eCheckIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/e-check.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"e-check\",V=[\"e-check\",C({outline:'<path d=\"M30 23V21H22V23H30ZM8.18005 18.57C9.77005 18.62 11.32 18.05 12.5 16.98C12.73 16.76 12.86 16.46 12.86 16.14C12.86 15.85 12.76 15.56 12.55 15.35C12.34 15.14 12.0601 15.02 11.7701 15.03C11.51 15.02 11.26 15.11 11.0601 15.28C10.26 15.94 9.26005 16.3 8.22005 16.28C6.46005 16.35 4.94005 15.04 4.76005 13.28H12.29C12.64 13.28 12.99 13.15 13.24 12.9C13.49 12.65 13.62 12.31 13.61 11.95C13.68 10.39 13.1 8.88003 12.0201 7.76003C10.93 6.64003 9.43005 6.02003 7.88005 6.03003C6.27005 6.06003 4.73005 6.75003 3.63005 7.92003C2.52005 9.10003 1.94005 10.68 2.00005 12.29C1.92005 13.96 2.54005 15.59 3.71005 16.78C4.88005 17.97 6.50005 18.62 8.17005 18.57H8.18005ZM7.86005 8.19003C9.68005 8.19003 10.73 9.58003 10.86 11.35H4.74005C4.99005 9.49003 6.17005 8.19003 7.86005 8.19003ZM30 16H18V18H30V16ZM33 8.00003H23.965H14.93C15.2801 8.63003 15.54 9.30003 15.72 10H32V26H5.00005V19.9C4.27005 19.56 3.59005 19.11 3.00005 18.56V27C3.00005 27.55 3.45005 28 4.00005 28H33C33.55 28 34 27.55 34 27V9.00003C34 8.45003 33.55 8.00003 33 8.00003Z\"/>',solid:'<path d=\"M8.18002 18.54C9.77002 18.58 11.32 18.01 12.5 16.94C12.73 16.72 12.86 16.42 12.86 16.1C12.86 15.81 12.76 15.52 12.55 15.31C12.34 15.1 12.06 14.98 11.77 14.99C11.51 14.98 11.26 15.07 11.06 15.24C10.26 15.9 9.26002 16.26 8.22002 16.24C6.46002 16.31 4.94002 15 4.76002 13.24H12.31C12.66 13.24 12.99 13.1 13.24 12.86C13.48 12.61 13.62 12.27 13.61 11.92C13.68 10.36 13.1 8.85 12.02 7.73C10.94 6.61 9.44002 5.99 7.88002 6C6.26002 6.03 4.72002 6.72 3.61002 7.91C2.50002 9.1 1.93002 10.68 2.00002 12.3C1.93002 13.96 2.56002 15.58 3.73002 16.76C4.90002 17.94 6.51002 18.59 8.17002 18.54H8.18002ZM7.91002 8.25C9.68002 8.25 10.73 9.64 10.91 11.41H4.79002C5.04002 9.55 6.14002 8.25 7.91002 8.25ZM33 8H14.91C15.7 9.33 16.11 10.85 16.11 12.39C16.18 14.52 15.4 16.59 13.94 18.14C12.48 19.69 10.46 20.6 8.33002 20.66C6.32002 20.66 4.40002 19.84 3.00002 18.41V27C3.00002 27.55 3.45002 28 4.00002 28H33C33.55 28 34 27.55 34 27V9C34 8.45 33.55 8 33 8ZM30 23H22V21H30V23ZM30 17.99H18V15.99H30V17.99Z\"/>'})];export{V as eCheckIcon,H as eCheckIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,SAAS;EAACC,CAAC,GAAC,CAAC,SAAS,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,ygCAAygC;IAACC,KAAK,EAAC;EAAi+B,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,UAAU,EAACJ,CAAC,IAAIK,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}