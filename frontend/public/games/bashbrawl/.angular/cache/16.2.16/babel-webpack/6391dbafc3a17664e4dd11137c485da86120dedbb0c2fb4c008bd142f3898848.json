{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst t = \"outdent\",\n  o = [\"outdent\", C({\n    outline: '<path d=\"M1 2.125H27C27.27 2.125 27.52 2.015 27.71 1.835C27.9 1.655 28 1.395 28 1.125C28 0.855 27.89 0.605 27.71 0.415C27.53 0.225 27.27 0.125 27 0.125H1C0.73 0.125 0.48 0.235 0.29 0.415C0.1 0.595 0 0.855 0 1.125C0 1.395 0.11 1.645 0.29 1.835C0.47 2.025 0.73 2.125 1 2.125ZM27 18.125H1C0.73 18.125 0.48 18.235 0.29 18.415C0.1 18.595 0 18.855 0 19.125C0 19.395 0.11 19.645 0.29 19.835C0.47 20.025 0.73 20.125 1 20.125H27C27.27 20.125 27.52 20.015 27.71 19.835C27.9 19.655 28 19.395 28 19.125C28 18.855 27.89 18.605 27.71 18.415C27.53 18.225 27.27 18.125 27 18.125ZM6.43 6.795C6.47 6.605 6.45 6.395 6.38 6.215C6.31 6.035 6.18 5.875 6.01 5.765C5.85 5.655 5.65 5.595 5.46 5.595C5.33 5.595 5.2 5.625 5.07 5.675C4.94 5.725 4.84 5.805 4.75 5.895L0.5 10.135L4.74 14.375C4.93 14.565 5.18 14.665 5.45 14.665C5.72 14.665 5.97 14.565 6.15 14.375C6.34 14.185 6.44 13.935 6.44 13.675C6.44 13.415 6.34 13.155 6.15 12.965L3.33 10.135L6.15 7.305C6.29 7.165 6.39 6.985 6.43 6.795ZM27 12.125H10C9.73 12.125 9.48 12.235 9.29 12.415C9.1 12.595 9 12.855 9 13.125C9 13.395 9.11 13.645 9.29 13.835C9.47 14.025 9.73 14.125 10 14.125H27C27.27 14.125 27.52 14.015 27.71 13.835C27.9 13.655 28 13.395 28 13.125C28 12.855 27.89 12.605 27.71 12.415C27.53 12.225 27.27 12.125 27 12.125ZM27 6.125H10C9.73 6.125 9.48 6.235 9.29 6.415C9.1 6.595 9 6.855 9 7.125C9 7.395 9.11 7.645 9.29 7.835C9.47 8.025 9.73 8.125 10 8.125H27C27.27 8.125 27.52 8.015 27.71 7.835C27.9 7.655 28 7.395 28 7.125C28 6.855 27.89 6.605 27.71 6.415C27.53 6.225 27.27 6.125 27 6.125Z\"/>'\n  })];\nexport { o as outdentIcon, t as outdentIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "t", "o", "outline", "outdentIcon", "outdentIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/outdent.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const t=\"outdent\",o=[\"outdent\",C({outline:'<path d=\"M1 2.125H27C27.27 2.125 27.52 2.015 27.71 1.835C27.9 1.655 28 1.395 28 1.125C28 0.855 27.89 0.605 27.71 0.415C27.53 0.225 27.27 0.125 27 0.125H1C0.73 0.125 0.48 0.235 0.29 0.415C0.1 0.595 0 0.855 0 1.125C0 1.395 0.11 1.645 0.29 1.835C0.47 2.025 0.73 2.125 1 2.125ZM27 18.125H1C0.73 18.125 0.48 18.235 0.29 18.415C0.1 18.595 0 18.855 0 19.125C0 19.395 0.11 19.645 0.29 19.835C0.47 20.025 0.73 20.125 1 20.125H27C27.27 20.125 27.52 20.015 27.71 19.835C27.9 19.655 28 19.395 28 19.125C28 18.855 27.89 18.605 27.71 18.415C27.53 18.225 27.27 18.125 27 18.125ZM6.43 6.795C6.47 6.605 6.45 6.395 6.38 6.215C6.31 6.035 6.18 5.875 6.01 5.765C5.85 5.655 5.65 5.595 5.46 5.595C5.33 5.595 5.2 5.625 5.07 5.675C4.94 5.725 4.84 5.805 4.75 5.895L0.5 10.135L4.74 14.375C4.93 14.565 5.18 14.665 5.45 14.665C5.72 14.665 5.97 14.565 6.15 14.375C6.34 14.185 6.44 13.935 6.44 13.675C6.44 13.415 6.34 13.155 6.15 12.965L3.33 10.135L6.15 7.305C6.29 7.165 6.39 6.985 6.43 6.795ZM27 12.125H10C9.73 12.125 9.48 12.235 9.29 12.415C9.1 12.595 9 12.855 9 13.125C9 13.395 9.11 13.645 9.29 13.835C9.47 14.025 9.73 14.125 10 14.125H27C27.27 14.125 27.52 14.015 27.71 13.835C27.9 13.655 28 13.395 28 13.125C28 12.855 27.89 12.605 27.71 12.415C27.53 12.225 27.27 12.125 27 12.125ZM27 6.125H10C9.73 6.125 9.48 6.235 9.29 6.415C9.1 6.595 9 6.855 9 7.125C9 7.395 9.11 7.645 9.29 7.835C9.47 8.025 9.73 8.125 10 8.125H27C27.27 8.125 27.52 8.015 27.71 7.835C27.9 7.655 28 7.395 28 7.125C28 6.855 27.89 6.605 27.71 6.415C27.53 6.225 27.27 6.125 27 6.125Z\"/>'})];export{o as outdentIcon,t as outdentIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,SAAS;EAACC,CAAC,GAAC,CAAC,SAAS,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAs/C,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,WAAW,EAACH,CAAC,IAAII,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}