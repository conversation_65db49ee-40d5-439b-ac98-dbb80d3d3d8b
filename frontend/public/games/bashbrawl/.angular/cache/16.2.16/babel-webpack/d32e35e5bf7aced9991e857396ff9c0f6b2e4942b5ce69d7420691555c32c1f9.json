{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst V = \"storage\",\n  d = [\"storage\", C({\n    outline: '<path d=\"M18 2C16.57 2 4 2.11 4 6V30C4 33.89 16.57 34 18 34C19.43 34 32 33.89 32 30V6C32 2.11 19.43 2 18 2ZM30 13.92C29.52 14.68 25.17 16 18 16C13.42 16 10.01 15.46 8 14.88V16.96C11.81 17.96 17.1 18.01 18 18.01C19.07 18.01 26.39 17.95 30 16.28V21.93C29.52 22.69 25.17 24.01 18 24.01C13.42 24.01 10.01 23.47 8 22.89V24.97C11.81 25.97 17.1 26.02 18 26.02C19.07 26.02 26.39 25.96 30 24.29V29.95C29.49 30.71 25.14 32.02 18 32.02C10.86 32.02 6.51 30.71 6 29.95V6H6.05C6.77 5.23 11.07 4 18 4C24.93 4 29.23 5.23 29.95 6C29.23 6.77 24.93 8 18 8C13.43 8 10.01 7.46 8 6.88V8.96C11.81 9.96 17.1 10.01 18 10.01C19.07 10.01 26.39 9.95 30 8.28V13.93V13.92Z\"/>',\n    outlineAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M24.1471 2.35376C21.2947 2.02038 18.6155 2 18 2C16.57 2 4 2.11 4 6V30C4 33.89 16.57 34 18 34C19.43 34 32 33.89 32 30V15.0367H27.417C25.3603 15.5571 22.1532 16 18 16C13.42 16 10.01 15.46 8 14.88V16.96C11.81 17.96 17.1 18.01 18 18.01C19.07 18.01 26.39 17.95 30 16.28V21.93C29.52 22.69 25.17 24.01 18 24.01C13.42 24.01 10.01 23.47 8 22.89V24.97C11.81 25.97 17.1 26.02 18 26.02C19.07 26.02 26.39 25.96 30 24.29V29.95C29.49 30.71 25.14 32.02 18 32.02C10.86 32.02 6.51 30.71 6 29.95V6H6.05C6.77 5.23 11.07 4 18 4C19.8705 4 21.5494 4.08961 23.018 4.2356L24.1471 2.35376Z\"/><path d=\"M20.8015 7.92986C19.923 7.97499 18.9882 8 18 8C13.43 8 10.01 7.46 8 6.88V8.96C11.81 9.96 17.1 10.01 18 10.01C18.242 10.01 18.8036 10.0069 19.5697 9.98287L20.8015 7.92986Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M32 12.7101C31.3663 12.8987 30.695 13 30 13V13.92C29.52 14.68 25.17 16 18 16C13.42 16 10.01 15.46 8 14.88V16.96C11.81 17.96 17.1 18.01 18 18.01C19.07 18.01 26.39 17.95 30 16.28V21.93C29.52 22.69 25.17 24.01 18 24.01C13.42 24.01 10.01 23.47 8 22.89V24.97C11.81 25.97 17.1 26.02 18 26.02C19.07 26.02 26.39 25.96 30 24.29V29.95C29.49 30.71 25.14 32.02 18 32.02C10.86 32.02 6.51 30.71 6 29.95V6H6.05C6.77 5.23 11.07 4 18 4C19.9564 4 21.7032 4.09803 23.219 4.25606C23.3945 3.57146 23.671 2.92731 24.0316 2.34042C21.2227 2.01983 18.6072 2 18 2C16.57 2 4 2.11 4 6V30C4 33.89 16.57 34 18 34C19.43 34 32 33.89 32 30V12.7101Z\"/><path d=\"M23.219 7.74394C21.7032 7.90197 19.9564 8 18 8C13.43 8 10.01 7.46 8 6.88V8.96C11.81 9.96 17.1 10.01 18 10.01C18.607 10.01 21.2258 9.99069 24.0374 9.66902C23.674 9.0796 23.3955 8.43221 23.219 7.74394Z\"/>',\n    solid: '<path d=\"M19 9C26.72 9 31.44 7.67 32 6.9V6C32 2.11 19.43 2 18 2C16.57 2 4 2.11 4 6V30C4 33.89 16.57 34 18 34C19.43 34 32 33.89 32 30V25.2C28.19 26.93 20.16 27 19 27C18.01 27 12.1 26.95 8 25.89V23.82C10.15 24.43 13.92 25.01 19 25.01C26.72 25.01 31.44 23.68 32 22.91V17.21C28.19 18.94 20.16 19.01 19 19.01C18.01 19.01 12.1 18.96 8 17.9V15.83C10.15 16.44 13.92 17.02 19 17.02C26.72 17.02 31.44 15.69 32 14.92V9.22C28.19 10.95 20.16 11.02 19 11.02C18.01 11.02 12.1 10.97 8 9.91V7.84C10.15 8.45 13.92 9.03 19 9.03V9Z\"/>',\n    solidAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M19.1005 11.0198C18.9001 11.8064 19.0029 12.6552 19.4206 13.3893C20.0233 14.4489 21.1577 15.0604 22.3395 15.0367H31.8882C30.9715 15.8168 26.3427 17.02 19 17.02C13.92 17.02 10.15 16.44 8 15.83V17.9C12.1 18.96 18.01 19.01 19 19.01C20.16 19.01 28.19 18.94 32 17.21V22.91C31.44 23.68 26.72 25.01 19 25.01C13.92 25.01 10.15 24.43 8 23.82V25.89C12.1 26.95 18.01 27 19 27C20.16 27 28.19 26.93 32 25.2V30C32 33.89 19.43 34 18 34C16.57 34 4 33.89 4 30V6C4 2.11 16.57 2 18 2C18.6155 2 21.2947 2.02038 24.1471 2.35376L20.1656 8.98973C19.7851 8.99649 19.3965 9 19 9V9.03C13.92 9.03 10.15 8.45 8 7.84V9.91C12.1 10.97 18.01 11.02 19 11.02C19.0292 11.02 19.0627 11.02 19.1005 11.0198Z\"/>',\n    solidBadged: '<path d=\"M30 10.86C32.7614 10.86 35 8.62141 35 5.85999C35 3.09856 32.7614 0.859985 30 0.859985C27.2386 0.859985 25 3.09856 25 5.85999C25 8.62141 27.2386 10.86 30 10.86Z\"/><path d=\"M32 12.5701C31.3663 12.7587 30.695 12.86 30 12.86C28.0246 12.86 26.2404 12.0418 24.9677 10.7258C22.1504 11.0004 19.6137 11.02 19 11.02C18.01 11.02 12.1 10.97 8 9.91V7.84C10.15 8.45 13.92 9.03 19 9.03V9C20.7026 9 22.2593 8.93531 23.6577 8.82619C23.2357 7.92556 23 6.92031 23 5.86C23 4.57322 23.3472 3.36754 23.953 2.33153C21.1741 2.01946 18.6015 2 18 2C16.57 2 4 2.11 4 6V30C4 33.89 16.57 34 18 34C19.43 34 32 33.89 32 30V25.2C28.19 26.93 20.16 27 19 27C18.01 27 12.1 26.95 8 25.89V23.82C10.15 24.43 13.92 25.01 19 25.01C26.72 25.01 31.44 23.68 32 22.91V17.21C28.19 18.94 20.16 19.01 19 19.01C18.01 19.01 12.1 18.96 8 17.9V15.83C10.15 16.44 13.92 17.02 19 17.02C26.72 17.02 31.44 15.69 32 14.92V12.5701Z\"/>'\n  })];\nexport { d as storageIcon, V as storageIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "V", "d", "outline", "outlineAlerted", "outlineBadged", "solid", "solidAlerted", "solidBadged", "storageIcon", "storageIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/storage.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const V=\"storage\",d=[\"storage\",C({outline:'<path d=\"M18 2C16.57 2 4 2.11 4 6V30C4 33.89 16.57 34 18 34C19.43 34 32 33.89 32 30V6C32 2.11 19.43 2 18 2ZM30 13.92C29.52 14.68 25.17 16 18 16C13.42 16 10.01 15.46 8 14.88V16.96C11.81 17.96 17.1 18.01 18 18.01C19.07 18.01 26.39 17.95 30 16.28V21.93C29.52 22.69 25.17 24.01 18 24.01C13.42 24.01 10.01 23.47 8 22.89V24.97C11.81 25.97 17.1 26.02 18 26.02C19.07 26.02 26.39 25.96 30 24.29V29.95C29.49 30.71 25.14 32.02 18 32.02C10.86 32.02 6.51 30.71 6 29.95V6H6.05C6.77 5.23 11.07 4 18 4C24.93 4 29.23 5.23 29.95 6C29.23 6.77 24.93 8 18 8C13.43 8 10.01 7.46 8 6.88V8.96C11.81 9.96 17.1 10.01 18 10.01C19.07 10.01 26.39 9.95 30 8.28V13.93V13.92Z\"/>',outlineAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M24.1471 2.35376C21.2947 2.02038 18.6155 2 18 2C16.57 2 4 2.11 4 6V30C4 33.89 16.57 34 18 34C19.43 34 32 33.89 32 30V15.0367H27.417C25.3603 15.5571 22.1532 16 18 16C13.42 16 10.01 15.46 8 14.88V16.96C11.81 17.96 17.1 18.01 18 18.01C19.07 18.01 26.39 17.95 30 16.28V21.93C29.52 22.69 25.17 24.01 18 24.01C13.42 24.01 10.01 23.47 8 22.89V24.97C11.81 25.97 17.1 26.02 18 26.02C19.07 26.02 26.39 25.96 30 24.29V29.95C29.49 30.71 25.14 32.02 18 32.02C10.86 32.02 6.51 30.71 6 29.95V6H6.05C6.77 5.23 11.07 4 18 4C19.8705 4 21.5494 4.08961 23.018 4.2356L24.1471 2.35376Z\"/><path d=\"M20.8015 7.92986C19.923 7.97499 18.9882 8 18 8C13.43 8 10.01 7.46 8 6.88V8.96C11.81 9.96 17.1 10.01 18 10.01C18.242 10.01 18.8036 10.0069 19.5697 9.98287L20.8015 7.92986Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M32 12.7101C31.3663 12.8987 30.695 13 30 13V13.92C29.52 14.68 25.17 16 18 16C13.42 16 10.01 15.46 8 14.88V16.96C11.81 17.96 17.1 18.01 18 18.01C19.07 18.01 26.39 17.95 30 16.28V21.93C29.52 22.69 25.17 24.01 18 24.01C13.42 24.01 10.01 23.47 8 22.89V24.97C11.81 25.97 17.1 26.02 18 26.02C19.07 26.02 26.39 25.96 30 24.29V29.95C29.49 30.71 25.14 32.02 18 32.02C10.86 32.02 6.51 30.71 6 29.95V6H6.05C6.77 5.23 11.07 4 18 4C19.9564 4 21.7032 4.09803 23.219 4.25606C23.3945 3.57146 23.671 2.92731 24.0316 2.34042C21.2227 2.01983 18.6072 2 18 2C16.57 2 4 2.11 4 6V30C4 33.89 16.57 34 18 34C19.43 34 32 33.89 32 30V12.7101Z\"/><path d=\"M23.219 7.74394C21.7032 7.90197 19.9564 8 18 8C13.43 8 10.01 7.46 8 6.88V8.96C11.81 9.96 17.1 10.01 18 10.01C18.607 10.01 21.2258 9.99069 24.0374 9.66902C23.674 9.0796 23.3955 8.43221 23.219 7.74394Z\"/>',solid:'<path d=\"M19 9C26.72 9 31.44 7.67 32 6.9V6C32 2.11 19.43 2 18 2C16.57 2 4 2.11 4 6V30C4 33.89 16.57 34 18 34C19.43 34 32 33.89 32 30V25.2C28.19 26.93 20.16 27 19 27C18.01 27 12.1 26.95 8 25.89V23.82C10.15 24.43 13.92 25.01 19 25.01C26.72 25.01 31.44 23.68 32 22.91V17.21C28.19 18.94 20.16 19.01 19 19.01C18.01 19.01 12.1 18.96 8 17.9V15.83C10.15 16.44 13.92 17.02 19 17.02C26.72 17.02 31.44 15.69 32 14.92V9.22C28.19 10.95 20.16 11.02 19 11.02C18.01 11.02 12.1 10.97 8 9.91V7.84C10.15 8.45 13.92 9.03 19 9.03V9Z\"/>',solidAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M19.1005 11.0198C18.9001 11.8064 19.0029 12.6552 19.4206 13.3893C20.0233 14.4489 21.1577 15.0604 22.3395 15.0367H31.8882C30.9715 15.8168 26.3427 17.02 19 17.02C13.92 17.02 10.15 16.44 8 15.83V17.9C12.1 18.96 18.01 19.01 19 19.01C20.16 19.01 28.19 18.94 32 17.21V22.91C31.44 23.68 26.72 25.01 19 25.01C13.92 25.01 10.15 24.43 8 23.82V25.89C12.1 26.95 18.01 27 19 27C20.16 27 28.19 26.93 32 25.2V30C32 33.89 19.43 34 18 34C16.57 34 4 33.89 4 30V6C4 2.11 16.57 2 18 2C18.6155 2 21.2947 2.02038 24.1471 2.35376L20.1656 8.98973C19.7851 8.99649 19.3965 9 19 9V9.03C13.92 9.03 10.15 8.45 8 7.84V9.91C12.1 10.97 18.01 11.02 19 11.02C19.0292 11.02 19.0627 11.02 19.1005 11.0198Z\"/>',solidBadged:'<path d=\"M30 10.86C32.7614 10.86 35 8.62141 35 5.85999C35 3.09856 32.7614 0.859985 30 0.859985C27.2386 0.859985 25 3.09856 25 5.85999C25 8.62141 27.2386 10.86 30 10.86Z\"/><path d=\"M32 12.5701C31.3663 12.7587 30.695 12.86 30 12.86C28.0246 12.86 26.2404 12.0418 24.9677 10.7258C22.1504 11.0004 19.6137 11.02 19 11.02C18.01 11.02 12.1 10.97 8 9.91V7.84C10.15 8.45 13.92 9.03 19 9.03V9C20.7026 9 22.2593 8.93531 23.6577 8.82619C23.2357 7.92556 23 6.92031 23 5.86C23 4.57322 23.3472 3.36754 23.953 2.33153C21.1741 2.01946 18.6015 2 18 2C16.57 2 4 2.11 4 6V30C4 33.89 16.57 34 18 34C19.43 34 32 33.89 32 30V25.2C28.19 26.93 20.16 27 19 27C18.01 27 12.1 26.95 8 25.89V23.82C10.15 24.43 13.92 25.01 19 25.01C26.72 25.01 31.44 23.68 32 22.91V17.21C28.19 18.94 20.16 19.01 19 19.01C18.01 19.01 12.1 18.96 8 17.9V15.83C10.15 16.44 13.92 17.02 19 17.02C26.72 17.02 31.44 15.69 32 14.92V12.5701Z\"/>'})];export{d as storageIcon,V as storageIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,SAAS;EAACC,CAAC,GAAC,CAAC,SAAS,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,uoBAAuoB;IAACC,cAAc,EAAC,0lCAA0lC;IAACC,aAAa,EAAC,s8BAAs8B;IAACC,KAAK,EAAC,ogBAAogB;IAACC,YAAY,EAAC,8gCAA8gC;IAACC,WAAW,EAAC;EAAu3B,CAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,WAAW,EAACR,CAAC,IAAIS,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}