{"ast": null, "code": "export default function _has(prop, obj) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}", "map": {"version": 3, "names": ["_has", "prop", "obj", "Object", "prototype", "hasOwnProperty", "call"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_has.js"], "sourcesContent": ["export default function _has(prop, obj) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}"], "mappings": "AAAA,eAAe,SAASA,IAAIA,CAACC,IAAI,EAAEC,GAAG,EAAE;EACtC,OAAOC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,GAAG,EAAED,IAAI,CAAC;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}