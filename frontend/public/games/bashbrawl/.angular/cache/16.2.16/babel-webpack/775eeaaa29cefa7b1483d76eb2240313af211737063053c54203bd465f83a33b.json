{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"volume\",\n  o = [\"volume\", C({\n    outline: '<path d=\"M17.985 18.9753H23.9831C24.253 18.9753 24.503 18.8653 24.6929 18.6852C24.8829 18.4952 24.9828 18.2451 24.9828 17.975C24.9828 17.7049 24.8729 17.4548 24.6929 17.2647C24.503 17.0747 24.253 16.9746 23.9831 16.9746H17.985C17.7151 16.9746 17.4652 17.0847 17.2752 17.2647C17.0853 17.4548 16.9853 17.7049 16.9853 17.975C16.9853 18.2451 17.0953 18.4952 17.2752 18.6852C17.4652 18.8753 17.7151 18.9753 17.985 18.9753ZM23.9831 12.9632H20.9841C20.7142 12.9632 20.4642 13.0732 20.2743 13.2533C20.0844 13.4434 19.9844 13.6935 19.9844 13.9636C19.9844 14.2337 20.0943 14.4837 20.2743 14.6738C20.4642 14.8639 20.7142 14.9639 20.9841 14.9639H23.9831C24.253 14.9639 24.503 14.8539 24.6929 14.6738C24.8829 14.4837 24.9828 14.2337 24.9828 13.9636C24.9828 13.6935 24.8729 13.4434 24.6929 13.2533C24.503 13.0632 24.253 12.9632 23.9831 12.9632ZM33.7001 4.33012C33.5102 4.14005 33.2602 4.04001 32.9903 4.04001H29.0515C28.9816 4.03001 28.9216 4 28.8516 4H2.98969C2.78975 4 2.59981 4.06002 2.43986 4.17006C2.27991 4.2801 2.14995 4.44016 2.07998 4.62022C2 4.80029 1.98001 5.01036 2.01999 5.20043C2.05998 5.3905 2.15995 5.57056 2.29991 5.71061L7.98813 11.4126V27.9986C7.98813 29.0589 8.408 30.0793 9.15776 30.8296C9.90753 31.5798 10.9272 32 11.9869 32H25.8725C26.9322 32 27.9419 31.5698 28.6917 30.8196C29.4414 30.0693 29.8613 29.0489 29.8613 27.9886V5.94069H32.0006V14.9639C32.0006 15.234 32.1106 15.4841 32.2905 15.6742C32.4705 15.8642 32.7304 15.9643 33.0003 15.9643C33.2702 15.9643 33.5202 15.8542 33.7101 15.6742C33.9 15.4841 34 15.234 34 14.9639V5.04037C34 4.77028 33.89 4.52019 33.7101 4.33012H33.7001ZM27.9919 27.9986C27.9619 28.5388 27.732 29.059 27.3321 29.4291C26.9322 29.7992 26.4124 30.0093 25.8725 29.9993H11.9869C11.447 29.9893 10.9372 29.7692 10.5473 29.3891C10.1575 29.0089 9.93752 28.4988 9.90753 27.9586V10.9225C9.92752 10.8124 9.90753 10.6924 9.87754 10.5824C9.83755 10.4723 9.77757 10.3723 9.6976 10.2922L5.41893 5.94069H27.9919V27.9986ZM17.985 10.9525H23.9831C24.253 10.9525 24.503 10.8424 24.6929 10.6624C24.8829 10.4723 24.9828 10.2222 24.9828 9.95213C24.9828 9.68203 24.8729 9.43194 24.6929 9.24187C24.503 9.0518 24.253 8.95177 23.9831 8.95177H17.985C17.7151 8.95177 17.4652 9.06181 17.2752 9.24187C17.0853 9.43194 16.9853 9.68203 16.9853 9.95213C16.9853 10.2222 17.0953 10.4723 17.2752 10.6624C17.4652 10.8524 17.7151 10.9525 17.985 10.9525ZM17.985 26.9882H23.9831C24.253 26.9882 24.503 26.8782 24.6929 26.6981C24.8829 26.508 24.9828 26.2579 24.9828 25.9879C24.9828 25.7178 24.8729 25.4677 24.6929 25.2776C24.503 25.0875 24.253 24.9875 23.9831 24.9875H17.985C17.7151 24.9875 17.4652 25.0975 17.2752 25.2776C17.0853 25.4677 16.9853 25.7178 16.9853 25.9879C16.9853 26.2579 17.0953 26.508 17.2752 26.6981C17.4652 26.8882 17.7151 26.9882 17.985 26.9882ZM23.9831 20.9761H20.9841C20.7142 20.9761 20.4642 21.0861 20.2743 21.2662C20.0844 21.4562 19.9844 21.7063 19.9844 21.9764C19.9844 22.2465 20.0943 22.4966 20.2743 22.6867C20.4642 22.8767 20.7142 22.9768 20.9841 22.9768H23.9831C24.253 22.9768 24.503 22.8667 24.6929 22.6867C24.8829 22.4966 24.9828 22.2465 24.9828 21.9764C24.9828 21.7063 24.8729 21.4562 24.6929 21.2662C24.503 21.0761 24.253 20.9761 23.9831 20.9761Z\"/>'\n  })];\nexport { o as volumeIcon, H as volumeIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "o", "outline", "volumeIcon", "volumeIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/volume.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"volume\",o=[\"volume\",C({outline:'<path d=\"M17.985 18.9753H23.9831C24.253 18.9753 24.503 18.8653 24.6929 18.6852C24.8829 18.4952 24.9828 18.2451 24.9828 17.975C24.9828 17.7049 24.8729 17.4548 24.6929 17.2647C24.503 17.0747 24.253 16.9746 23.9831 16.9746H17.985C17.7151 16.9746 17.4652 17.0847 17.2752 17.2647C17.0853 17.4548 16.9853 17.7049 16.9853 17.975C16.9853 18.2451 17.0953 18.4952 17.2752 18.6852C17.4652 18.8753 17.7151 18.9753 17.985 18.9753ZM23.9831 12.9632H20.9841C20.7142 12.9632 20.4642 13.0732 20.2743 13.2533C20.0844 13.4434 19.9844 13.6935 19.9844 13.9636C19.9844 14.2337 20.0943 14.4837 20.2743 14.6738C20.4642 14.8639 20.7142 14.9639 20.9841 14.9639H23.9831C24.253 14.9639 24.503 14.8539 24.6929 14.6738C24.8829 14.4837 24.9828 14.2337 24.9828 13.9636C24.9828 13.6935 24.8729 13.4434 24.6929 13.2533C24.503 13.0632 24.253 12.9632 23.9831 12.9632ZM33.7001 4.33012C33.5102 4.14005 33.2602 4.04001 32.9903 4.04001H29.0515C28.9816 4.03001 28.9216 4 28.8516 4H2.98969C2.78975 4 2.59981 4.06002 2.43986 4.17006C2.27991 4.2801 2.14995 4.44016 2.07998 4.62022C2 4.80029 1.98001 5.01036 2.01999 5.20043C2.05998 5.3905 2.15995 5.57056 2.29991 5.71061L7.98813 11.4126V27.9986C7.98813 29.0589 8.408 30.0793 9.15776 30.8296C9.90753 31.5798 10.9272 32 11.9869 32H25.8725C26.9322 32 27.9419 31.5698 28.6917 30.8196C29.4414 30.0693 29.8613 29.0489 29.8613 27.9886V5.94069H32.0006V14.9639C32.0006 15.234 32.1106 15.4841 32.2905 15.6742C32.4705 15.8642 32.7304 15.9643 33.0003 15.9643C33.2702 15.9643 33.5202 15.8542 33.7101 15.6742C33.9 15.4841 34 15.234 34 14.9639V5.04037C34 4.77028 33.89 4.52019 33.7101 4.33012H33.7001ZM27.9919 27.9986C27.9619 28.5388 27.732 29.059 27.3321 29.4291C26.9322 29.7992 26.4124 30.0093 25.8725 29.9993H11.9869C11.447 29.9893 10.9372 29.7692 10.5473 29.3891C10.1575 29.0089 9.93752 28.4988 9.90753 27.9586V10.9225C9.92752 10.8124 9.90753 10.6924 9.87754 10.5824C9.83755 10.4723 9.77757 10.3723 9.6976 10.2922L5.41893 5.94069H27.9919V27.9986ZM17.985 10.9525H23.9831C24.253 10.9525 24.503 10.8424 24.6929 10.6624C24.8829 10.4723 24.9828 10.2222 24.9828 9.95213C24.9828 9.68203 24.8729 9.43194 24.6929 9.24187C24.503 9.0518 24.253 8.95177 23.9831 8.95177H17.985C17.7151 8.95177 17.4652 9.06181 17.2752 9.24187C17.0853 9.43194 16.9853 9.68203 16.9853 9.95213C16.9853 10.2222 17.0953 10.4723 17.2752 10.6624C17.4652 10.8524 17.7151 10.9525 17.985 10.9525ZM17.985 26.9882H23.9831C24.253 26.9882 24.503 26.8782 24.6929 26.6981C24.8829 26.508 24.9828 26.2579 24.9828 25.9879C24.9828 25.7178 24.8729 25.4677 24.6929 25.2776C24.503 25.0875 24.253 24.9875 23.9831 24.9875H17.985C17.7151 24.9875 17.4652 25.0975 17.2752 25.2776C17.0853 25.4677 16.9853 25.7178 16.9853 25.9879C16.9853 26.2579 17.0953 26.508 17.2752 26.6981C17.4652 26.8882 17.7151 26.9882 17.985 26.9882ZM23.9831 20.9761H20.9841C20.7142 20.9761 20.4642 21.0861 20.2743 21.2662C20.0844 21.4562 19.9844 21.7063 19.9844 21.9764C19.9844 22.2465 20.0943 22.4966 20.2743 22.6867C20.4642 22.8767 20.7142 22.9768 20.9841 22.9768H23.9831C24.253 22.9768 24.503 22.8667 24.6929 22.6867C24.8829 22.4966 24.9828 22.2465 24.9828 21.9764C24.9828 21.7063 24.8729 21.4562 24.6929 21.2662C24.503 21.0761 24.253 20.9761 23.9831 20.9761Z\"/>'})];export{o as volumeIcon,H as volumeIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAsmG,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,UAAU,EAACH,CAAC,IAAII,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}