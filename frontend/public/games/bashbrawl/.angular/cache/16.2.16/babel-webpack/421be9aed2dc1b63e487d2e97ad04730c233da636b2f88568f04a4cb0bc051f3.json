{"ast": null, "code": "import _complement from \"./internal/_complement.js\";\nimport _curry2 from \"./internal/_curry2.js\";\nimport filter from \"./filter.js\";\n/**\n * The complement of [`filter`](#filter).\n *\n * Acts as a transducer if a transformer is given in list position. Filterable\n * objects include plain objects or any object that has a filter method such\n * as `Array`.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig Filterable f => (a -> <PERSON><PERSON><PERSON>) -> f a -> f a\n * @param {Function} pred\n * @param {Array} filterable\n * @return {Array}\n * @see R.filter, R.transduce, R.addIndex\n * @example\n *\n *      const isOdd = (n) => n % 2 !== 0;\n *\n *      R.reject(isOdd, [1, 2, 3, 4]); //=> [2, 4]\n *\n *      R.reject(isOdd, {a: 1, b: 2, c: 3, d: 4}); //=> {b: 2, d: 4}\n */\n\nvar reject = /*#__PURE__*/\n_curry2(function reject(pred, filterable) {\n  return filter(_complement(pred), filterable);\n});\nexport default reject;", "map": {"version": 3, "names": ["_complement", "_curry2", "filter", "reject", "pred", "filterable"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/reject.js"], "sourcesContent": ["import _complement from \"./internal/_complement.js\";\nimport _curry2 from \"./internal/_curry2.js\";\nimport filter from \"./filter.js\";\n/**\n * The complement of [`filter`](#filter).\n *\n * Acts as a transducer if a transformer is given in list position. Filterable\n * objects include plain objects or any object that has a filter method such\n * as `Array`.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig Filterable f => (a -> <PERSON><PERSON><PERSON>) -> f a -> f a\n * @param {Function} pred\n * @param {Array} filterable\n * @return {Array}\n * @see R.filter, R.transduce, R.addIndex\n * @example\n *\n *      const isOdd = (n) => n % 2 !== 0;\n *\n *      R.reject(isOdd, [1, 2, 3, 4]); //=> [2, 4]\n *\n *      R.reject(isOdd, {a: 1, b: 2, c: 3, d: 4}); //=> {b: 2, d: 4}\n */\n\nvar reject =\n/*#__PURE__*/\n_curry2(function reject(pred, filterable) {\n  return filter(_complement(pred), filterable);\n});\n\nexport default reject;"], "mappings": "AAAA,OAAOA,WAAW,MAAM,2BAA2B;AACnD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,MAAM,MAAM,aAAa;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,MAAM,GACV;AACAF,OAAO,CAAC,SAASE,MAAMA,CAACC,IAAI,EAAEC,UAAU,EAAE;EACxC,OAAOH,MAAM,CAACF,WAAW,CAACI,IAAI,CAAC,EAAEC,UAAU,CAAC;AAC9C,CAAC,CAAC;AAEF,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}