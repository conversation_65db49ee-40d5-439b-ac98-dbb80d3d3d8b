{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"host\",\n  d = [\"host\", C({\n    outline: '<path d=\"M18 24C16.35 24 15 25.35 15 27C15 28.65 16.35 30 18 30C19.65 30 21 28.65 21 27C21 25.35 19.65 24 18 24ZM18 28C17.45 28 17 27.55 17 27C17 26.45 17.45 26 18 26C18.55 26 19 26.45 19 27C19 27.55 18.55 28 18 28ZM24 6H12V8H24V6ZM26 2H10C8.9 2 8 2.9 8 4V34H28V4C28 2.9 27.1 2 26 2ZM26 32H10V4H26V32ZM24 10H12V12H24V10Z\"/>',\n    outlineAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M24.3594 2H10C8.9 2 8 2.9 8 4V34H28V15.0367H26V32H10V4H23.1594L24.3594 2Z\"/><path d=\"M21.9594 6H12V8H20.7594L21.9594 6Z\"/><path d=\"M19.5594 10H12V12H19.0073C18.9663 11.3177 19.1449 10.6284 19.5362 10.0387L19.5594 10Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15 27C15 25.35 16.35 24 18 24C19.65 24 21 25.35 21 27C21 28.65 19.65 30 18 30C16.35 30 15 28.65 15 27ZM17 27C17 27.55 17.45 28 18 28C18.55 28 19 27.55 19 27C19 26.45 18.55 26 18 26C17.45 26 17 26.45 17 27Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M24.2547 2H10C8.9 2 8 2.9 8 4V34H28V12.7101C27.2776 12.4951 26.604 12.1666 26 11.7453V32H10V4H23.2899C23.5049 3.27757 23.8334 2.60401 24.2547 2Z\"/><path d=\"M23 6H12V8H23.2899C23.1013 7.36629 23 6.69497 23 6Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15 27C15 25.35 16.35 24 18 24C19.65 24 21 25.35 21 27C21 28.65 19.65 30 18 30C16.35 30 15 28.65 15 27ZM17 27C17 27.55 17.45 28 18 28C18.55 28 19 27.55 19 27C19 26.45 18.55 26 18 26C17.45 26 17 26.45 17 27Z\"/><path d=\"M12 10H24V12H12V10Z\"/>',\n    solid: '<path d=\"M18 26C17.45 26 17 26.45 17 27C17 27.55 17.45 28 18 28C18.55 28 19 27.55 19 27C19 26.45 18.55 26 18 26ZM26 2H10C8.9 2 8 2.9 8 4V34H28V4C28 2.9 27.1 2 26 2ZM18 30C16.35 30 15 28.65 15 27C15 25.35 16.35 24 18 24C19.65 24 21 25.35 21 27C21 28.65 19.65 30 18 30ZM24 12H12V10H24V12ZM24 8H12V6H24V8Z\"/>',\n    solidAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M17 27C17 26.45 17.45 26 18 26C18.55 26 19 26.45 19 27C19 27.55 18.55 28 18 28C17.45 28 17 27.55 17 27Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.3594 2H10C8.9 2 8 2.9 8 4V34H28V15.0367H22.3395C21.1577 15.0604 20.0233 14.4489 19.4206 13.3893C19.173 12.9542 19.0361 12.4788 19.0073 12H12V10H19.5594L20.7594 8H12V6H21.9594L24.3594 2ZM15 27C15 28.65 16.35 30 18 30C19.65 30 21 28.65 21 27C21 25.35 19.65 24 18 24C16.35 24 15 25.35 15 27Z\"/>',\n    solidBadged: '<path d=\"M30 10.9C32.7614 10.9 35 8.66145 35 5.90002C35 3.1386 32.7614 0.900024 30 0.900024C27.2386 0.900024 25 3.1386 25 5.90002C25 8.66145 27.2386 10.9 30 10.9Z\"/><path d=\"M17 27C17 26.45 17.45 26 18 26C18.55 26 19 26.45 19 27C19 27.55 18.55 28 18 28C17.45 28 17 27.55 17 27Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.1862 2H10C8.9 2 8 2.9 8 4V34H28V12.6101C25.7757 11.9481 24.0147 10.2103 23.3205 8H12V6H23.0007C23.0002 5.96672 23 5.93338 23 5.89999C23 4.4562 23.4371 3.1145 24.1862 2ZM15 27C15 28.65 16.35 30 18 30C19.65 30 21 28.65 21 27C21 25.35 19.65 24 18 24C16.35 24 15 25.35 15 27ZM12 12H24V10H12V12Z\"/>'\n  })];\nexport { d as hostIcon, H as hostIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "d", "outline", "outlineAlerted", "outlineBadged", "solid", "solidAlerted", "solidBadged", "hostIcon", "hostIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/host.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"host\",d=[\"host\",C({outline:'<path d=\"M18 24C16.35 24 15 25.35 15 27C15 28.65 16.35 30 18 30C19.65 30 21 28.65 21 27C21 25.35 19.65 24 18 24ZM18 28C17.45 28 17 27.55 17 27C17 26.45 17.45 26 18 26C18.55 26 19 26.45 19 27C19 27.55 18.55 28 18 28ZM24 6H12V8H24V6ZM26 2H10C8.9 2 8 2.9 8 4V34H28V4C28 2.9 27.1 2 26 2ZM26 32H10V4H26V32ZM24 10H12V12H24V10Z\"/>',outlineAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M24.3594 2H10C8.9 2 8 2.9 8 4V34H28V15.0367H26V32H10V4H23.1594L24.3594 2Z\"/><path d=\"M21.9594 6H12V8H20.7594L21.9594 6Z\"/><path d=\"M19.5594 10H12V12H19.0073C18.9663 11.3177 19.1449 10.6284 19.5362 10.0387L19.5594 10Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15 27C15 25.35 16.35 24 18 24C19.65 24 21 25.35 21 27C21 28.65 19.65 30 18 30C16.35 30 15 28.65 15 27ZM17 27C17 27.55 17.45 28 18 28C18.55 28 19 27.55 19 27C19 26.45 18.55 26 18 26C17.45 26 17 26.45 17 27Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M24.2547 2H10C8.9 2 8 2.9 8 4V34H28V12.7101C27.2776 12.4951 26.604 12.1666 26 11.7453V32H10V4H23.2899C23.5049 3.27757 23.8334 2.60401 24.2547 2Z\"/><path d=\"M23 6H12V8H23.2899C23.1013 7.36629 23 6.69497 23 6Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15 27C15 25.35 16.35 24 18 24C19.65 24 21 25.35 21 27C21 28.65 19.65 30 18 30C16.35 30 15 28.65 15 27ZM17 27C17 27.55 17.45 28 18 28C18.55 28 19 27.55 19 27C19 26.45 18.55 26 18 26C17.45 26 17 26.45 17 27Z\"/><path d=\"M12 10H24V12H12V10Z\"/>',solid:'<path d=\"M18 26C17.45 26 17 26.45 17 27C17 27.55 17.45 28 18 28C18.55 28 19 27.55 19 27C19 26.45 18.55 26 18 26ZM26 2H10C8.9 2 8 2.9 8 4V34H28V4C28 2.9 27.1 2 26 2ZM18 30C16.35 30 15 28.65 15 27C15 25.35 16.35 24 18 24C19.65 24 21 25.35 21 27C21 28.65 19.65 30 18 30ZM24 12H12V10H24V12ZM24 8H12V6H24V8Z\"/>',solidAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M17 27C17 26.45 17.45 26 18 26C18.55 26 19 26.45 19 27C19 27.55 18.55 28 18 28C17.45 28 17 27.55 17 27Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.3594 2H10C8.9 2 8 2.9 8 4V34H28V15.0367H22.3395C21.1577 15.0604 20.0233 14.4489 19.4206 13.3893C19.173 12.9542 19.0361 12.4788 19.0073 12H12V10H19.5594L20.7594 8H12V6H21.9594L24.3594 2ZM15 27C15 28.65 16.35 30 18 30C19.65 30 21 28.65 21 27C21 25.35 19.65 24 18 24C16.35 24 15 25.35 15 27Z\"/>',solidBadged:'<path d=\"M30 10.9C32.7614 10.9 35 8.66145 35 5.90002C35 3.1386 32.7614 0.900024 30 0.900024C27.2386 0.900024 25 3.1386 25 5.90002C25 8.66145 27.2386 10.9 30 10.9Z\"/><path d=\"M17 27C17 26.45 17.45 26 18 26C18.55 26 19 26.45 19 27C19 27.55 18.55 28 18 28C17.45 28 17 27.55 17 27Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.1862 2H10C8.9 2 8 2.9 8 4V34H28V12.6101C25.7757 11.9481 24.0147 10.2103 23.3205 8H12V6H23.0007C23.0002 5.96672 23 5.93338 23 5.89999C23 4.4562 23.4371 3.1145 24.1862 2ZM15 27C15 28.65 16.35 30 18 30C19.65 30 21 28.65 21 27C21 25.35 19.65 24 18 24C16.35 24 15 25.35 15 27ZM12 12H24V10H12V12Z\"/>'})];export{d as hostIcon,H as hostIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,MAAM;EAACC,CAAC,GAAC,CAAC,MAAM,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,qUAAqU;IAACC,cAAc,EAAC,20BAA20B;IAACC,aAAa,EAAC,4nBAA4nB;IAACC,KAAK,EAAC,mTAAmT;IAACC,YAAY,EAAC,gzBAAgzB;IAACC,WAAW,EAAC;EAAonB,CAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,QAAQ,EAACR,CAAC,IAAIS,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}