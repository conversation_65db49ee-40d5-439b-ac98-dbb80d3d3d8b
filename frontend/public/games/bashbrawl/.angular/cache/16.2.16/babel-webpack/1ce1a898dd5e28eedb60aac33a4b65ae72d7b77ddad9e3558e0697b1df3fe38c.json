{"ast": null, "code": "import _curry2 from \"./internal/_curry2.js\";\nimport _isString from \"./internal/_isString.js\";\n/**\n * Returns the nth element of the given list or string. If n is negative the\n * element at index length + n is returned.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig Number -> [a] -> a | Undefined\n * @sig Number -> String -> String\n * @param {Number} offset\n * @param {*} list\n * @return {*}\n * @example\n *\n *      const list = ['foo', 'bar', 'baz', 'quux'];\n *      R.nth(1, list); //=> 'bar'\n *      R.nth(-1, list); //=> 'quux'\n *      R.nth(-99, list); //=> undefined\n *\n *      R.nth(2, 'abc'); //=> 'c'\n *      R.nth(3, 'abc'); //=> ''\n * @symb R.nth(-1, [a, b, c]) = c\n * @symb R.nth(0, [a, b, c]) = a\n * @symb R.nth(1, [a, b, c]) = b\n */\n\nvar nth = /*#__PURE__*/\n_curry2(function nth(offset, list) {\n  var idx = offset < 0 ? list.length + offset : offset;\n  return _isString(list) ? list.charAt(idx) : list[idx];\n});\nexport default nth;", "map": {"version": 3, "names": ["_curry2", "_isString", "nth", "offset", "list", "idx", "length", "char<PERSON>t"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/nth.js"], "sourcesContent": ["import _curry2 from \"./internal/_curry2.js\";\nimport _isString from \"./internal/_isString.js\";\n/**\n * Returns the nth element of the given list or string. If n is negative the\n * element at index length + n is returned.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig Number -> [a] -> a | Undefined\n * @sig Number -> String -> String\n * @param {Number} offset\n * @param {*} list\n * @return {*}\n * @example\n *\n *      const list = ['foo', 'bar', 'baz', 'quux'];\n *      R.nth(1, list); //=> 'bar'\n *      R.nth(-1, list); //=> 'quux'\n *      R.nth(-99, list); //=> undefined\n *\n *      R.nth(2, 'abc'); //=> 'c'\n *      R.nth(3, 'abc'); //=> ''\n * @symb R.nth(-1, [a, b, c]) = c\n * @symb R.nth(0, [a, b, c]) = a\n * @symb R.nth(1, [a, b, c]) = b\n */\n\nvar nth =\n/*#__PURE__*/\n_curry2(function nth(offset, list) {\n  var idx = offset < 0 ? list.length + offset : offset;\n  return _isString(list) ? list.charAt(idx) : list[idx];\n});\n\nexport default nth;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,SAAS,MAAM,yBAAyB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,GAAG,GACP;AACAF,OAAO,CAAC,SAASE,GAAGA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACjC,IAAIC,GAAG,GAAGF,MAAM,GAAG,CAAC,GAAGC,IAAI,CAACE,MAAM,GAAGH,MAAM,GAAGA,MAAM;EACpD,OAAOF,SAAS,CAACG,IAAI,CAAC,GAAGA,IAAI,CAACG,MAAM,CAACF,GAAG,CAAC,GAAGD,IAAI,CAACC,GAAG,CAAC;AACvD,CAAC,CAAC;AAEF,eAAeH,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}