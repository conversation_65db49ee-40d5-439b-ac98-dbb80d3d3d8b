{"ast": null, "code": "function t() {\n  return t => t.addInitializer(t => new s(t));\n}\nclass s {\n  constructor(t) {\n    this.host = t, this.host.addController(this);\n  }\n  hostUpdated() {\n    null !== this.host.expanded && void 0 !== this.host.expanded && (this.host.ariaExpanded = this.host.expanded ? \"true\" : \"false\"), this.host.readonly && (this.host.ariaExpanded = null);\n  }\n}\nexport { s as AriaExpandedController, t as ariaExpanded };", "map": {"version": 3, "names": ["t", "addInitializer", "s", "constructor", "host", "addController", "hostUpdated", "expanded", "ariaExpanded", "readonly", "AriaExpandedController"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/aria-expanded.controller.js"], "sourcesContent": ["function t(){return t=>t.addInitializer((t=>new s(t)))}class s{constructor(t){this.host=t,this.host.addController(this)}hostUpdated(){null!==this.host.expanded&&void 0!==this.host.expanded&&(this.host.ariaExpanded=this.host.expanded?\"true\":\"false\"),this.host.readonly&&(this.host.ariaExpanded=null)}}export{s as AriaExpandedController,t as ariaExpanded};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAOA,CAAC,IAAEA,CAAC,CAACC,cAAc,CAAED,CAAC,IAAE,IAAIE,CAAC,CAACF,CAAC,CAAE,CAAC;AAAA;AAAC,MAAME,CAAC;EAACC,WAAWA,CAACH,CAAC,EAAC;IAAC,IAAI,CAACI,IAAI,GAACJ,CAAC,EAAC,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;EAAA;EAACC,WAAWA,CAAA,EAAE;IAAC,IAAI,KAAG,IAAI,CAACF,IAAI,CAACG,QAAQ,IAAE,KAAK,CAAC,KAAG,IAAI,CAACH,IAAI,CAACG,QAAQ,KAAG,IAAI,CAACH,IAAI,CAACI,YAAY,GAAC,IAAI,CAACJ,IAAI,CAACG,QAAQ,GAAC,MAAM,GAAC,OAAO,CAAC,EAAC,IAAI,CAACH,IAAI,CAACK,QAAQ,KAAG,IAAI,CAACL,IAAI,CAACI,YAAY,GAAC,IAAI,CAAC;EAAA;AAAC;AAAC,SAAON,CAAC,IAAIQ,sBAAsB,EAACV,CAAC,IAAIQ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}