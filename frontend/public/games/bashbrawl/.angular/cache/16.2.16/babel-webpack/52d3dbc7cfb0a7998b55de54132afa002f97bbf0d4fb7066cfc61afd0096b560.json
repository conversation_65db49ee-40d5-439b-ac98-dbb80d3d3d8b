{"ast": null, "code": "import { css as o } from \"lit\";\nvar t = o`:host{--icon-width:var(--cds-global-space-7, calc(16 * 1rem / var(--cds-global-base, 20)));--icon-height:var(--cds-global-space-7, calc(16 * 1rem / var(--cds-global-base, 20)));--width:var(--icon-width);--height:var(--icon-width);--cursor:pointer;--color:var(--cds-global-typography-color-300, var(--cds-global-color-construction-800, #2d4048));--font-size:var(--cds-global-typography-font-size-3, calc(13 * 1rem / var(--cds-global-base, 20)));--background:transparent;--padding:0;--outline:var(--cds-alias-object-interaction-outline, Highlight solid 2px);--outline-offset:calc(var(--cds-alias-object-interaction-outline-offset, 1px) * -1);pointer-events:none;display:inline-block;outline:0!important}:host([role=button]){pointer-events:initial;cursor:var(--cursor)!important}:host([status=active]){--color:var(--cds-alias-status-info, var(--cds-global-color-blue-700, #0079ad))}::slotted([shape=close]),::slotted([shape=info-circle]),::slotted([shape=times]),:host([shape=close]),:host([shape=info-circle]){--icon-width:var(--cds-global-space-8, calc(18 * 1rem / var(--cds-global-base, 20)));--icon-height:var(--cds-global-space-8, calc(18 * 1rem / var(--cds-global-base, 20)))}:host(:hover){--color:var(--cds-alias-object-interaction-color-hover, var(--cds-global-color-construction-1000, #1b2b32))}:host(:active){--color:var(--cds-alias-object-interaction-color-active, var(--cds-global-color-construction-1000, #1b2b32))}:host(:active) .private-host{transform:translateY(calc(var(--cds-global-space-1,calc(1 * 1rem / var(--cds-global-base,20)))/ 2))}:host([disabled]){--color:var(--cds-alias-object-interaction-color-disabled, var(--cds-global-color-construction-300, #aeb8bc))}.private-host{--icon-color:var(--color);background:var(--background);padding:var(--padding);color:var(--color);font-size:var(--font-size);display:flex;justify-content:center;align-items:center;min-width:var(--width);min-height:var(--height)}::slotted(cds-icon),cds-icon{width:var(--icon-width);height:var(--icon-height);pointer-events:none}::slotted(cds-icon:not([status])),cds-icon{--color:var(--icon-color)}:host([disabled]:active){pointer-events:none!important}.private-host::after{content:\"\";position:absolute;left:calc(-1*var(--width) - 1);top:calc(-1*var(--height) - 1);width:var(--cds-alias-object-interaction-touch-target,calc(36 * 1rem / var(--cds-global-base,20)));height:var(--cds-alias-object-interaction-touch-target,calc(36 * 1rem / var(--cds-global-base,20)))}:host([disabled]) .private-host::after{outline:0!important}:host(:focus) .private-host::after{outline:var(--outline);outline-offset:var(--outline-offset)}@media (-webkit-min-device-pixel-ratio:0){:host(:focus) .private-host::after{outline-color:-webkit-focus-ring-color;outline-style:auto}}`;\nexport { t as default };", "map": {"version": 3, "names": ["css", "o", "t", "default"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/button-action/button-action.element.scss.js"], "sourcesContent": ["import{css as o}from\"lit\";var t=o`:host{--icon-width:var(--cds-global-space-7, calc(16 * 1rem / var(--cds-global-base, 20)));--icon-height:var(--cds-global-space-7, calc(16 * 1rem / var(--cds-global-base, 20)));--width:var(--icon-width);--height:var(--icon-width);--cursor:pointer;--color:var(--cds-global-typography-color-300, var(--cds-global-color-construction-800, #2d4048));--font-size:var(--cds-global-typography-font-size-3, calc(13 * 1rem / var(--cds-global-base, 20)));--background:transparent;--padding:0;--outline:var(--cds-alias-object-interaction-outline, Highlight solid 2px);--outline-offset:calc(var(--cds-alias-object-interaction-outline-offset, 1px) * -1);pointer-events:none;display:inline-block;outline:0!important}:host([role=button]){pointer-events:initial;cursor:var(--cursor)!important}:host([status=active]){--color:var(--cds-alias-status-info, var(--cds-global-color-blue-700, #0079ad))}::slotted([shape=close]),::slotted([shape=info-circle]),::slotted([shape=times]),:host([shape=close]),:host([shape=info-circle]){--icon-width:var(--cds-global-space-8, calc(18 * 1rem / var(--cds-global-base, 20)));--icon-height:var(--cds-global-space-8, calc(18 * 1rem / var(--cds-global-base, 20)))}:host(:hover){--color:var(--cds-alias-object-interaction-color-hover, var(--cds-global-color-construction-1000, #1b2b32))}:host(:active){--color:var(--cds-alias-object-interaction-color-active, var(--cds-global-color-construction-1000, #1b2b32))}:host(:active) .private-host{transform:translateY(calc(var(--cds-global-space-1,calc(1 * 1rem / var(--cds-global-base,20)))/ 2))}:host([disabled]){--color:var(--cds-alias-object-interaction-color-disabled, var(--cds-global-color-construction-300, #aeb8bc))}.private-host{--icon-color:var(--color);background:var(--background);padding:var(--padding);color:var(--color);font-size:var(--font-size);display:flex;justify-content:center;align-items:center;min-width:var(--width);min-height:var(--height)}::slotted(cds-icon),cds-icon{width:var(--icon-width);height:var(--icon-height);pointer-events:none}::slotted(cds-icon:not([status])),cds-icon{--color:var(--icon-color)}:host([disabled]:active){pointer-events:none!important}.private-host::after{content:\"\";position:absolute;left:calc(-1*var(--width) - 1);top:calc(-1*var(--height) - 1);width:var(--cds-alias-object-interaction-touch-target,calc(36 * 1rem / var(--cds-global-base,20)));height:var(--cds-alias-object-interaction-touch-target,calc(36 * 1rem / var(--cds-global-base,20)))}:host([disabled]) .private-host::after{outline:0!important}:host(:focus) .private-host::after{outline:var(--outline);outline-offset:var(--outline-offset)}@media (-webkit-min-device-pixel-ratio:0){:host(:focus) .private-host::after{outline-color:-webkit-focus-ring-color;outline-style:auto}}`;export{t as default};\n"], "mappings": "AAAA,SAAOA,GAAG,IAAIC,CAAC,QAAK,KAAK;AAAC,IAAIC,CAAC,GAACD,CAAE,6rFAA4rF;AAAC,SAAOC,CAAC,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}