{"ast": null, "code": "import { isJsdomTest as e } from \"./environment.js\";\nfunction t(e) {\n  e.preventDefault(), e.stopPropagation();\n}\nconst n = (t, n, r) => {\n  t.hasAttribute(n) ? r(t.getAttribute(n)) : void 0 !== t[n] && r(t[n]), !t._valueTracker || \"checked\" !== n && \"value\" !== n || (t._valueTracker = null);\n  const o = Object.getOwnPropertyDescriptor(Object.getPrototypeOf(t), n);\n  return o && !e() && Object.defineProperty(t, n, {\n    get: o.get,\n    set: e => {\n      r(e), o.set.call(t, e);\n    }\n  }), s(t, n, e => r(e));\n};\nfunction r(e) {\n  return new Promise(t => {\n    const n = () => {\n      t(null), e.__cdsTouched = !0;\n    };\n    e.__cdsTouched && t(null), e.addEventListener(\"mouseover\", n, {\n      once: !0,\n      passive: !0\n    }), e.addEventListener(\"touchstart\", n, {\n      once: !0,\n      passive: !0\n    }), e.addEventListener(\"keydown\", n, {\n      once: !0,\n      passive: !0\n    }), e.addEventListener(\"focus\", n, {\n      once: !0,\n      passive: !0\n    });\n  });\n}\nfunction o(e, t) {\n  const n = new MutationObserver(e => {\n    for (const n of e) \"childList\" === n.type && t(n);\n  });\n  return n.observe(e, {\n    childList: !0\n  }), n;\n}\nfunction s(e, t, n) {\n  const r = new MutationObserver(r => {\n    r.find(e => e.attributeName === t) && n(e.getAttribute(t));\n  });\n  return r.observe(e, {\n    attributes: !0\n  }), r;\n}\nfunction i(e, t, n) {\n  const r = new MutationObserver(r => {\n    const o = r.find(e => t.find(t => e.attributeName === t));\n    o && n(e.getAttribute(o.attributeName));\n  });\n  return r.observe(e, {\n    attributes: !0,\n    attributeFilter: t,\n    subtree: !0\n  }), r;\n}\nexport { n as getElementUpdates, s as listenForAttributeChange, i as listenForAttributeListChange, o as onChildListMutation, r as onFirstInteraction, t as stopEvent };", "map": {"version": 3, "names": ["isJsdomTest", "e", "t", "preventDefault", "stopPropagation", "n", "r", "hasAttribute", "getAttribute", "_valueTracker", "o", "Object", "getOwnPropertyDescriptor", "getPrototypeOf", "defineProperty", "get", "set", "call", "s", "Promise", "__cdsTouched", "addEventListener", "once", "passive", "MutationObserver", "type", "observe", "childList", "find", "attributeName", "attributes", "i", "attributeFilter", "subtree", "getElementUpdates", "listenForAttributeChange", "listenForAttributeListChange", "onChildListMutation", "onFirstInteraction", "stopEvent"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/events.js"], "sourcesContent": ["import{isJsdomTest as e}from\"./environment.js\";function t(e){e.preventDefault(),e.stopPropagation()}const n=(t,n,r)=>{t.hasAttribute(n)?r(t.getAttribute(n)):void 0!==t[n]&&r(t[n]),!t._valueTracker||\"checked\"!==n&&\"value\"!==n||(t._valueTracker=null);const o=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(t),n);return o&&!e()&&Object.defineProperty(t,n,{get:o.get,set:e=>{r(e),o.set.call(t,e)}}),s(t,n,(e=>r(e)))};function r(e){return new Promise((t=>{const n=()=>{t(null),e.__cdsTouched=!0};e.__cdsTouched&&t(null),e.addEventListener(\"mouseover\",n,{once:!0,passive:!0}),e.addEventListener(\"touchstart\",n,{once:!0,passive:!0}),e.addEventListener(\"keydown\",n,{once:!0,passive:!0}),e.addEventListener(\"focus\",n,{once:!0,passive:!0})}))}function o(e,t){const n=new MutationObserver((e=>{for(const n of e)\"childList\"===n.type&&t(n)}));return n.observe(e,{childList:!0}),n}function s(e,t,n){const r=new MutationObserver((r=>{r.find((e=>e.attributeName===t))&&n(e.getAttribute(t))}));return r.observe(e,{attributes:!0}),r}function i(e,t,n){const r=new MutationObserver((r=>{const o=r.find((e=>t.find((t=>e.attributeName===t))));o&&n(e.getAttribute(o.attributeName))}));return r.observe(e,{attributes:!0,attributeFilter:t,subtree:!0}),r}export{n as getElementUpdates,s as listenForAttributeChange,i as listenForAttributeListChange,o as onChildListMutation,r as onFirstInteraction,t as stopEvent};\n"], "mappings": "AAAA,SAAOA,WAAW,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAASC,CAACA,CAACD,CAAC,EAAC;EAACA,CAAC,CAACE,cAAc,CAAC,CAAC,EAACF,CAAC,CAACG,eAAe,CAAC,CAAC;AAAA;AAAC,MAAMC,CAAC,GAACA,CAACH,CAAC,EAACG,CAAC,EAACC,CAAC,KAAG;EAACJ,CAAC,CAACK,YAAY,CAAC<PERSON>,CAAC,<PERSON>AC,GAACC,CAAC,CAACJ,CAAC,CAACM,YAAY,CAACH,CAAC,CAAC,CAAC,GAAC,KAAK,CAAC,KAAGH,CAAC,CAACG,CAAC,CAAC,IAAEC,CAAC,CAACJ,CAAC,CAACG,CAAC,CAAC,CAAC,EAAC,CAACH,CAAC,CAACO,aAAa,IAAE,SAAS,KAAGJ,CAAC,IAAE,OAAO,KAAGA,CAAC,KAAGH,CAAC,CAACO,aAAa,GAAC,IAAI,CAAC;EAAC,MAAMC,CAAC,GAACC,MAAM,CAACC,wBAAwB,CAACD,MAAM,CAACE,cAAc,CAACX,CAAC,CAAC,EAACG,CAAC,CAAC;EAAC,OAAOK,CAAC,IAAE,CAACT,CAAC,CAAC,CAAC,IAAEU,MAAM,CAACG,cAAc,CAACZ,CAAC,EAACG,CAAC,EAAC;IAACU,GAAG,EAACL,CAAC,CAACK,GAAG;IAACC,GAAG,EAACf,CAAC,IAAE;MAACK,CAAC,CAACL,CAAC,CAAC,EAACS,CAAC,CAACM,GAAG,CAACC,IAAI,CAACf,CAAC,EAACD,CAAC,CAAC;IAAA;EAAC,CAAC,CAAC,EAACiB,CAAC,CAAChB,CAAC,EAACG,CAAC,EAAEJ,CAAC,IAAEK,CAAC,CAACL,CAAC,CAAE,CAAC;AAAA,CAAC;AAAC,SAASK,CAACA,CAACL,CAAC,EAAC;EAAC,OAAO,IAAIkB,OAAO,CAAEjB,CAAC,IAAE;IAAC,MAAMG,CAAC,GAACA,CAAA,KAAI;MAACH,CAAC,CAAC,IAAI,CAAC,EAACD,CAAC,CAACmB,YAAY,GAAC,CAAC,CAAC;IAAA,CAAC;IAACnB,CAAC,CAACmB,YAAY,IAAElB,CAAC,CAAC,IAAI,CAAC,EAACD,CAAC,CAACoB,gBAAgB,CAAC,WAAW,EAAChB,CAAC,EAAC;MAACiB,IAAI,EAAC,CAAC,CAAC;MAACC,OAAO,EAAC,CAAC;IAAC,CAAC,CAAC,EAACtB,CAAC,CAACoB,gBAAgB,CAAC,YAAY,EAAChB,CAAC,EAAC;MAACiB,IAAI,EAAC,CAAC,CAAC;MAACC,OAAO,EAAC,CAAC;IAAC,CAAC,CAAC,EAACtB,CAAC,CAACoB,gBAAgB,CAAC,SAAS,EAAChB,CAAC,EAAC;MAACiB,IAAI,EAAC,CAAC,CAAC;MAACC,OAAO,EAAC,CAAC;IAAC,CAAC,CAAC,EAACtB,CAAC,CAACoB,gBAAgB,CAAC,OAAO,EAAChB,CAAC,EAAC;MAACiB,IAAI,EAAC,CAAC,CAAC;MAACC,OAAO,EAAC,CAAC;IAAC,CAAC,CAAC;EAAA,CAAE,CAAC;AAAA;AAAC,SAASb,CAACA,CAACT,CAAC,EAACC,CAAC,EAAC;EAAC,MAAMG,CAAC,GAAC,IAAImB,gBAAgB,CAAEvB,CAAC,IAAE;IAAC,KAAI,MAAMI,CAAC,IAAIJ,CAAC,EAAC,WAAW,KAAGI,CAAC,CAACoB,IAAI,IAAEvB,CAAC,CAACG,CAAC,CAAC;EAAA,CAAE,CAAC;EAAC,OAAOA,CAAC,CAACqB,OAAO,CAACzB,CAAC,EAAC;IAAC0B,SAAS,EAAC,CAAC;EAAC,CAAC,CAAC,EAACtB,CAAC;AAAA;AAAC,SAASa,CAACA,CAACjB,CAAC,EAACC,CAAC,EAACG,CAAC,EAAC;EAAC,MAAMC,CAAC,GAAC,IAAIkB,gBAAgB,CAAElB,CAAC,IAAE;IAACA,CAAC,CAACsB,IAAI,CAAE3B,CAAC,IAAEA,CAAC,CAAC4B,aAAa,KAAG3B,CAAE,CAAC,IAAEG,CAAC,CAACJ,CAAC,CAACO,YAAY,CAACN,CAAC,CAAC,CAAC;EAAA,CAAE,CAAC;EAAC,OAAOI,CAAC,CAACoB,OAAO,CAACzB,CAAC,EAAC;IAAC6B,UAAU,EAAC,CAAC;EAAC,CAAC,CAAC,EAACxB,CAAC;AAAA;AAAC,SAASyB,CAACA,CAAC9B,CAAC,EAACC,CAAC,EAACG,CAAC,EAAC;EAAC,MAAMC,CAAC,GAAC,IAAIkB,gBAAgB,CAAElB,CAAC,IAAE;IAAC,MAAMI,CAAC,GAACJ,CAAC,CAACsB,IAAI,CAAE3B,CAAC,IAAEC,CAAC,CAAC0B,IAAI,CAAE1B,CAAC,IAAED,CAAC,CAAC4B,aAAa,KAAG3B,CAAE,CAAE,CAAC;IAACQ,CAAC,IAAEL,CAAC,CAACJ,CAAC,CAACO,YAAY,CAACE,CAAC,CAACmB,aAAa,CAAC,CAAC;EAAA,CAAE,CAAC;EAAC,OAAOvB,CAAC,CAACoB,OAAO,CAACzB,CAAC,EAAC;IAAC6B,UAAU,EAAC,CAAC,CAAC;IAACE,eAAe,EAAC9B,CAAC;IAAC+B,OAAO,EAAC,CAAC;EAAC,CAAC,CAAC,EAAC3B,CAAC;AAAA;AAAC,SAAOD,CAAC,IAAI6B,iBAAiB,EAAChB,CAAC,IAAIiB,wBAAwB,EAACJ,CAAC,IAAIK,4BAA4B,EAAC1B,CAAC,IAAI2B,mBAAmB,EAAC/B,CAAC,IAAIgC,kBAAkB,EAACpC,CAAC,IAAIqC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}