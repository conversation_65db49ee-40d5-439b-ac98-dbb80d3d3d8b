{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst V = \"ferry\",\n  H = [\"ferry\", C({\n    outline: '<path d=\"M6.93687 24.2V19.97H32.2129L29.8733 24.06C30.5632 24 31.2831 24.16 31.943 24.46L34.8025 19.46C34.9825 19.15 34.9825 18.77 34.8025 18.46C34.6225 18.15 34.2926 17.96 33.9327 17.96H31.433L25.434 9.98H17.9352C16.2854 9.98 14.9356 11.32 14.9356 12.97V13.97H12.9359V6H6.93687V14.16C5.77706 14.57 4.93719 15.67 4.93719 16.97V25.21C4.99718 25.16 5.06717 25.11 5.12716 25.05C5.67707 24.66 6.29697 24.38 6.93687 24.19V24.2ZM8.93656 8H10.9362V13.99H8.93656V8ZM6.93687 16.98C6.93687 16.43 7.3868 15.98 7.93672 15.98H16.9353V12.99C16.9353 12.44 17.3852 11.99 17.9352 11.99H24.4341L25.9339 13.99H18.935V15.99H27.4337L28.9334 17.99H6.93687V16.99V16.98ZM29.8533 26.09C28.6535 26.09 27.5637 26.49 26.5638 27.19C25.464 28.29 23.5743 28.29 22.4744 27.19C21.5746 26.39 20.3848 25.99 19.185 25.99C17.9851 25.99 16.8953 26.39 15.9955 27.19C14.7956 28.29 13.0059 28.29 11.8061 27.19C10.9063 26.39 9.71644 25.99 8.51663 25.99C7.31682 25.99 6.127 26.39 5.12716 27.09C4.52725 27.69 3.72738 27.99 2.9375 27.99V29.99C4.2373 30.09 5.42711 29.59 6.32697 28.79C7.02686 28.29 7.82674 27.99 8.61661 27.99C9.40649 27.99 10.1164 28.29 10.7063 28.79C12.596 30.39 15.3956 30.39 17.1853 28.79C17.7852 28.29 18.4851 27.99 19.2749 27.99C20.0648 27.99 20.7747 28.29 21.3646 28.79C23.2543 30.39 25.9539 30.39 27.8436 28.79C28.3435 28.29 29.1434 27.99 29.8333 27.99C30.5232 27.99 31.3331 28.29 31.823 28.79C32.7228 29.49 33.8127 29.89 34.9125 29.99V28.09C34.2126 28.09 33.5127 27.79 33.0228 27.19C32.1229 26.39 30.9331 25.99 29.8333 26.09H29.8533Z\"/>',\n    solid: '<path d=\"M29.8533 26.0861C28.6535 26.0861 27.5637 26.4862 26.5638 27.1865C25.464 28.2868 23.5743 28.2868 22.4744 27.1865C21.5746 26.3862 20.3848 25.9861 19.185 25.9861C17.9851 25.9861 16.8953 26.3862 15.9955 27.1865C14.7956 28.2868 13.0059 28.2868 11.8061 27.1865C10.9063 26.3862 9.71644 25.9861 8.51663 25.9861C7.31682 25.9861 6.127 26.3862 5.12716 27.0864C4.52725 27.6866 3.72738 27.9867 2.9375 27.9867V29.9873C4.2373 30.0873 5.42711 29.5872 6.32697 28.7869C7.02686 28.2868 7.82674 27.9867 8.61661 27.9867C9.40649 27.9867 10.1164 28.2868 10.7063 28.7869C12.596 30.3874 15.3956 30.3874 17.1853 28.7869C17.7852 28.2868 18.4851 27.9867 19.2749 27.9867C20.0648 27.9867 20.7747 28.2868 21.3646 28.7869C23.2543 30.3874 25.9539 30.3874 27.8436 28.7869C28.3435 28.2868 29.1434 27.9867 29.8333 27.9867C30.5232 27.9867 31.3331 28.2868 31.823 28.7869C32.7228 29.4872 33.8127 29.8873 34.9125 29.9873V28.0867C34.2126 28.0867 33.5127 27.7866 33.0228 27.1865C32.1229 26.3862 30.9331 25.9861 29.8333 26.0861H29.8533ZM6.93687 24.2056V19.9743H32.2129L29.8733 24.0655C30.5632 24.0055 31.2831 24.1655 31.943 24.4656L34.8025 19.4641C34.9825 19.154 34.9825 18.7739 34.8025 18.4638C34.6225 18.1537 34.2926 17.9636 33.9327 17.9636H31.433L25.434 9.98121H17.9352C16.2854 9.98121 14.9356 11.3216 14.9356 12.9721V13.9724H12.9359V6H6.93687V14.1625C5.77706 14.5726 4.93719 15.6729 4.93719 16.9733V25.2159C4.99718 25.1658 5.06717 25.1158 5.12716 25.0558C5.67707 24.6657 6.29697 24.3856 6.93687 24.1955V24.2056ZM19.8149 12.8721H24.9841L25.014 12.9121L26.6438 15.0728H19.8149V12.8721ZM8.93656 8.00061H10.9362V13.9924H8.93656V8.00061Z\"/>'\n  })];\nexport { H as ferryIcon, V as ferryIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "V", "H", "outline", "solid", "ferryIcon", "ferryIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/ferry.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const V=\"ferry\",H=[\"ferry\",C({outline:'<path d=\"M6.93687 24.2V19.97H32.2129L29.8733 24.06C30.5632 24 31.2831 24.16 31.943 24.46L34.8025 19.46C34.9825 19.15 34.9825 18.77 34.8025 18.46C34.6225 18.15 34.2926 17.96 33.9327 17.96H31.433L25.434 9.98H17.9352C16.2854 9.98 14.9356 11.32 14.9356 12.97V13.97H12.9359V6H6.93687V14.16C5.77706 14.57 4.93719 15.67 4.93719 16.97V25.21C4.99718 25.16 5.06717 25.11 5.12716 25.05C5.67707 24.66 6.29697 24.38 6.93687 24.19V24.2ZM8.93656 8H10.9362V13.99H8.93656V8ZM6.93687 16.98C6.93687 16.43 7.3868 15.98 7.93672 15.98H16.9353V12.99C16.9353 12.44 17.3852 11.99 17.9352 11.99H24.4341L25.9339 13.99H18.935V15.99H27.4337L28.9334 17.99H6.93687V16.99V16.98ZM29.8533 26.09C28.6535 26.09 27.5637 26.49 26.5638 27.19C25.464 28.29 23.5743 28.29 22.4744 27.19C21.5746 26.39 20.3848 25.99 19.185 25.99C17.9851 25.99 16.8953 26.39 15.9955 27.19C14.7956 28.29 13.0059 28.29 11.8061 27.19C10.9063 26.39 9.71644 25.99 8.51663 25.99C7.31682 25.99 6.127 26.39 5.12716 27.09C4.52725 27.69 3.72738 27.99 2.9375 27.99V29.99C4.2373 30.09 5.42711 29.59 6.32697 28.79C7.02686 28.29 7.82674 27.99 8.61661 27.99C9.40649 27.99 10.1164 28.29 10.7063 28.79C12.596 30.39 15.3956 30.39 17.1853 28.79C17.7852 28.29 18.4851 27.99 19.2749 27.99C20.0648 27.99 20.7747 28.29 21.3646 28.79C23.2543 30.39 25.9539 30.39 27.8436 28.79C28.3435 28.29 29.1434 27.99 29.8333 27.99C30.5232 27.99 31.3331 28.29 31.823 28.79C32.7228 29.49 33.8127 29.89 34.9125 29.99V28.09C34.2126 28.09 33.5127 27.79 33.0228 27.19C32.1229 26.39 30.9331 25.99 29.8333 26.09H29.8533Z\"/>',solid:'<path d=\"M29.8533 26.0861C28.6535 26.0861 27.5637 26.4862 26.5638 27.1865C25.464 28.2868 23.5743 28.2868 22.4744 27.1865C21.5746 26.3862 20.3848 25.9861 19.185 25.9861C17.9851 25.9861 16.8953 26.3862 15.9955 27.1865C14.7956 28.2868 13.0059 28.2868 11.8061 27.1865C10.9063 26.3862 9.71644 25.9861 8.51663 25.9861C7.31682 25.9861 6.127 26.3862 5.12716 27.0864C4.52725 27.6866 3.72738 27.9867 2.9375 27.9867V29.9873C4.2373 30.0873 5.42711 29.5872 6.32697 28.7869C7.02686 28.2868 7.82674 27.9867 8.61661 27.9867C9.40649 27.9867 10.1164 28.2868 10.7063 28.7869C12.596 30.3874 15.3956 30.3874 17.1853 28.7869C17.7852 28.2868 18.4851 27.9867 19.2749 27.9867C20.0648 27.9867 20.7747 28.2868 21.3646 28.7869C23.2543 30.3874 25.9539 30.3874 27.8436 28.7869C28.3435 28.2868 29.1434 27.9867 29.8333 27.9867C30.5232 27.9867 31.3331 28.2868 31.823 28.7869C32.7228 29.4872 33.8127 29.8873 34.9125 29.9873V28.0867C34.2126 28.0867 33.5127 27.7866 33.0228 27.1865C32.1229 26.3862 30.9331 25.9861 29.8333 26.0861H29.8533ZM6.93687 24.2056V19.9743H32.2129L29.8733 24.0655C30.5632 24.0055 31.2831 24.1655 31.943 24.4656L34.8025 19.4641C34.9825 19.154 34.9825 18.7739 34.8025 18.4638C34.6225 18.1537 34.2926 17.9636 33.9327 17.9636H31.433L25.434 9.98121H17.9352C16.2854 9.98121 14.9356 11.3216 14.9356 12.9721V13.9724H12.9359V6H6.93687V14.1625C5.77706 14.5726 4.93719 15.6729 4.93719 16.9733V25.2159C4.99718 25.1658 5.06717 25.1158 5.12716 25.0558C5.67707 24.6657 6.29697 24.3856 6.93687 24.1955V24.2056ZM19.8149 12.8721H24.9841L25.014 12.9121L26.6438 15.0728H19.8149V12.8721ZM8.93656 8.00061H10.9362V13.9924H8.93656V8.00061Z\"/>'})];export{H as ferryIcon,V as ferryIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,OAAO;EAACC,CAAC,GAAC,CAAC,OAAO,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,8+CAA8+C;IAACC,KAAK,EAAC;EAAukD,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,SAAS,EAACJ,CAAC,IAAIK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}