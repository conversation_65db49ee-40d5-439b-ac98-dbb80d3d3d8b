{"ast": null, "code": "import { hingeKeyframes as a } from \"./keyframes/hinge.js\";\nconst o = \"cds-modal-hinge-exit\",\n  e = [{\n    target: \".overlay-backdrop\",\n    onlyIf: \"isLayered:false\",\n    animation: [{\n      opacity: 1,\n      offset: .8\n    }, {\n      opacity: 0\n    }],\n    options: {\n      duration: 1200,\n      easing: \"ease-in-out\",\n      fill: \"forwards\"\n    }\n  }, {\n    target: \".private-host\",\n    animation: a,\n    options: {\n      duration: 1200,\n      easing: \"ease-in-out\",\n      fill: \"forwards\",\n      endDelay: 50\n    }\n  }];\nexport { e as AnimationHingeConfig, o as AnimationHingeName };", "map": {"version": 3, "names": ["hingeKeyframes", "a", "o", "e", "target", "onlyIf", "animation", "opacity", "offset", "options", "duration", "easing", "fill", "endDelay", "AnimationHingeConfig", "AnimationHingeName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/motion/animations/cds-overlay-hinge-example.js"], "sourcesContent": ["import{hingeKeyframes as a}from\"./keyframes/hinge.js\";const o=\"cds-modal-hinge-exit\",e=[{target:\".overlay-backdrop\",onlyIf:\"isLayered:false\",animation:[{opacity:1,offset:.8},{opacity:0}],options:{duration:1200,easing:\"ease-in-out\",fill:\"forwards\"}},{target:\".private-host\",animation:a,options:{duration:1200,easing:\"ease-in-out\",fill:\"forwards\",endDelay:50}}];export{e as AnimationHingeConfig,o as AnimationHingeName};\n"], "mappings": "AAAA,SAAOA,cAAc,IAAIC,CAAC,QAAK,sBAAsB;AAAC,MAAMC,CAAC,GAAC,sBAAsB;EAACC,CAAC,GAAC,CAAC;IAACC,MAAM,EAAC,mBAAmB;IAACC,MAAM,EAAC,iBAAiB;IAACC,SAAS,EAAC,CAAC;MAACC,OAAO,EAAC,CAAC;MAACC,MAAM,EAAC;IAAE,CAAC,EAAC;MAACD,OAAO,EAAC;IAAC,CAAC,CAAC;IAACE,OAAO,EAAC;MAACC,QAAQ,EAAC,IAAI;MAACC,MAAM,EAAC,aAAa;MAACC,IAAI,EAAC;IAAU;EAAC,CAAC,EAAC;IAACR,MAAM,EAAC,eAAe;IAACE,SAAS,EAACL,CAAC;IAACQ,OAAO,EAAC;MAACC,QAAQ,EAAC,IAAI;MAACC,MAAM,EAAC,aAAa;MAACC,IAAI,EAAC,UAAU;MAACC,QAAQ,EAAC;IAAE;EAAC,CAAC,CAAC;AAAC,SAAOV,CAAC,IAAIW,oBAAoB,EAACZ,CAAC,IAAIa,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}