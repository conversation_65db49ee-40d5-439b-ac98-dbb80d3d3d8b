{"ast": null, "code": "export default {\n  foreground: '#3e3e3e',\n  background: '#f4f4f4',\n  cursor: '#3f3f3f',\n  cursorAccent: '#c7c7c7',\n  black: '#3e3e3e',\n  brightBlack: '#666666',\n  red: '#970b16',\n  brightRed: '#de0000',\n  green: '#07962a',\n  brightGreen: '#87d5a2',\n  yellow: '#f8eec7',\n  brightYellow: '#f1d007',\n  blue: '#003e8a',\n  brightBlue: '#2e6cba',\n  magenta: '#e94691',\n  brightMagenta: '#ffa29f',\n  cyan: '#89d1ec',\n  brightCyan: '#1cfafe',\n  white: '#ffffff',\n  brightWhite: '#ffffff'\n};", "map": {"version": 3, "names": ["foreground", "background", "cursor", "cursorAccent", "black", "brightBlack", "red", "brightRed", "green", "bright<PERSON><PERSON>", "yellow", "<PERSON><PERSON><PERSON><PERSON>", "blue", "brightBlue", "magenta", "brightMagenta", "cyan", "bright<PERSON>yan", "white", "bright<PERSON><PERSON>e"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/src/app/terminals/terminal-themes/GitHub.ts"], "sourcesContent": ["export default {\n  foreground: '#3e3e3e',\n  background: '#f4f4f4',\n  cursor: '#3f3f3f',\n  cursorAccent: '#c7c7c7',\n\n  black: '#3e3e3e',\n  brightBlack: '#666666',\n\n  red: '#970b16',\n  brightRed: '#de0000',\n\n  green: '#07962a',\n  brightGreen: '#87d5a2',\n\n  yellow: '#f8eec7',\n  brightYellow: '#f1d007',\n\n  blue: '#003e8a',\n  brightBlue: '#2e6cba',\n\n  magenta: '#e94691',\n  brightMagenta: '#ffa29f',\n\n  cyan: '#89d1ec',\n  brightCyan: '#1cfafe',\n\n  white: '#ffffff',\n  brightWhite: '#ffffff',\n};\n"], "mappings": "AAAA,eAAe;EACbA,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,SAAS;EACrBC,MAAM,EAAE,SAAS;EACjBC,YAAY,EAAE,SAAS;EAEvBC,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE,SAAS;EAEtBC,GAAG,EAAE,SAAS;EACdC,SAAS,EAAE,SAAS;EAEpBC,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE,SAAS;EAEtBC,MAAM,EAAE,SAAS;EACjBC,YAAY,EAAE,SAAS;EAEvBC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,SAAS;EAErBC,OAAO,EAAE,SAAS;EAClBC,aAAa,EAAE,SAAS;EAExBC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,SAAS;EAErBC,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE;CACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}