{"ast": null, "code": "const e = (e, n, t) => {\n    const {\n      kind: s,\n      elements: i\n    } = n;\n    return {\n      kind: s,\n      elements: i,\n      finisher(n) {\n        t(e, n);\n      }\n    };\n  },\n  n = (e, n, t) => (t(e, n), n);\nexport { n as classLegacyDecorator, e as classStandardDecorator };", "map": {"version": 3, "names": ["e", "n", "t", "kind", "s", "elements", "i", "finisher", "classLegacyDecorator", "classStandardDecorator"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/decorators/utils.js"], "sourcesContent": ["const e=(e,n,t)=>{const{kind:s,elements:i}=n;return{kind:s,elements:i,finisher(n){t(e,n)}}},n=(e,n,t)=>(t(e,n),n);export{n as classLegacyDecorator,e as classStandardDecorator};\n"], "mappings": "AAAA,MAAMA,CAAC,GAACA,CAACA,CAAC,EAACC,CAAC,EAACC,CAAC,KAAG;IAAC,MAAK;MAACC,IAAI,EAACC,CAAC;MAACC,QAAQ,EAACC;IAAC,CAAC,GAACL,CAAC;IAAC,OAAM;MAACE,IAAI,EAACC,CAAC;MAACC,QAAQ,EAACC,CAAC;MAACC,QAAQA,CAACN,CAAC,EAAC;QAACC,CAAC,CAACF,CAAC,EAACC,CAAC,CAAC;MAAA;IAAC,CAAC;EAAA,CAAC;EAACA,CAAC,GAACA,CAACD,CAAC,EAACC,CAAC,EAACC,CAAC,MAAIA,CAAC,CAACF,CAAC,EAACC,CAAC,CAAC,EAACA,CAAC,CAAC;AAAC,SAAOA,CAAC,IAAIO,oBAAoB,EAACR,CAAC,IAAIS,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}