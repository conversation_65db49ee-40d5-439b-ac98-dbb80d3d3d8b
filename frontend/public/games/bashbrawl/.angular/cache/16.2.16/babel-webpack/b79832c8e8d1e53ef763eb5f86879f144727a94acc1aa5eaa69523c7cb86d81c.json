{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"filter-off\",\n  V = [\"filter-off\", C({\n    outline: '<path d=\"M3.71006 2.29055C3.32006 1.90055 2.68006 1.90055 2.29006 2.29055C1.90006 2.68055 1.90006 3.32055 2.29006 3.71055L4.58006 6.00055H3.00006C2.45006 5.99055 2.00006 6.44055 2.00006 6.99055V8.66055C2.00006 9.14055 2.19006 9.59055 2.53006 9.93055L14.0001 21.5705V30.0505L16.0001 31.0005V20.9805C16.0001 20.7105 15.9001 20.4605 15.7101 20.2705L4.00006 8.58055V7.99055H6.58006L20.0001 21.4105V32.9905L22.0001 33.9905V23.4105L32.2901 33.7005C32.4901 33.9005 32.7401 33.9905 33.0001 33.9905C33.2601 33.9905 33.5101 33.8905 33.7101 33.7005C34.1001 33.3105 34.1001 32.6805 33.7101 32.2905L3.71006 2.29055ZM33.0001 5.99055H10.2301L12.2301 7.99055H32.0001V8.60055L22.4201 18.1805L23.8601 19.6205L33.4701 9.99055C33.8101 9.65055 34.0101 9.18055 34.0001 8.69055V6.99055C34.0001 6.44055 33.5501 5.99055 33.0001 5.99055Z\"/>',\n    solid: '<path d=\"M33.4701 9.99055C33.8101 9.65055 34.0101 9.18055 34.0001 8.69055V6.99055C34.0001 6.44055 33.5501 5.99055 33.0001 5.99055H10.2301L23.8601 19.6205L33.4701 9.99055ZM33.7101 32.2905L3.71006 2.29055C3.32006 1.90055 2.68006 1.90055 2.29006 2.29055C1.90006 2.68055 1.90006 3.32055 2.29006 3.71055L4.58006 6.00055H3.00006C2.45006 6.00055 2.00006 6.45055 2.00006 7.00055V8.67055C2.00006 9.15055 2.19006 9.60055 2.53006 9.94055L14.0001 21.5805V30.0605L22.0001 34.0005V23.4205L32.2901 33.7105C32.4901 33.9105 32.7401 34.0005 33.0001 34.0005C33.2601 34.0005 33.5101 33.9005 33.7101 33.7105C34.1001 33.3205 34.1001 32.6905 33.7101 32.3005V32.2905Z\"/>'\n  })];\nexport { V as filterOffIcon, L as filterOffIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "V", "outline", "solid", "filterOffIcon", "filterOffIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/filter-off.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"filter-off\",V=[\"filter-off\",C({outline:'<path d=\"M3.71006 2.29055C3.32006 1.90055 2.68006 1.90055 2.29006 2.29055C1.90006 2.68055 1.90006 3.32055 2.29006 3.71055L4.58006 6.00055H3.00006C2.45006 5.99055 2.00006 6.44055 2.00006 6.99055V8.66055C2.00006 9.14055 2.19006 9.59055 2.53006 9.93055L14.0001 21.5705V30.0505L16.0001 31.0005V20.9805C16.0001 20.7105 15.9001 20.4605 15.7101 20.2705L4.00006 8.58055V7.99055H6.58006L20.0001 21.4105V32.9905L22.0001 33.9905V23.4105L32.2901 33.7005C32.4901 33.9005 32.7401 33.9905 33.0001 33.9905C33.2601 33.9905 33.5101 33.8905 33.7101 33.7005C34.1001 33.3105 34.1001 32.6805 33.7101 32.2905L3.71006 2.29055ZM33.0001 5.99055H10.2301L12.2301 7.99055H32.0001V8.60055L22.4201 18.1805L23.8601 19.6205L33.4701 9.99055C33.8101 9.65055 34.0101 9.18055 34.0001 8.69055V6.99055C34.0001 6.44055 33.5501 5.99055 33.0001 5.99055Z\"/>',solid:'<path d=\"M33.4701 9.99055C33.8101 9.65055 34.0101 9.18055 34.0001 8.69055V6.99055C34.0001 6.44055 33.5501 5.99055 33.0001 5.99055H10.2301L23.8601 19.6205L33.4701 9.99055ZM33.7101 32.2905L3.71006 2.29055C3.32006 1.90055 2.68006 1.90055 2.29006 2.29055C1.90006 2.68055 1.90006 3.32055 2.29006 3.71055L4.58006 6.00055H3.00006C2.45006 6.00055 2.00006 6.45055 2.00006 7.00055V8.67055C2.00006 9.15055 2.19006 9.60055 2.53006 9.94055L14.0001 21.5805V30.0605L22.0001 34.0005V23.4205L32.2901 33.7105C32.4901 33.9105 32.7401 34.0005 33.0001 34.0005C33.2601 34.0005 33.5101 33.9005 33.7101 33.7105C34.1001 33.3205 34.1001 32.6905 33.7101 32.3005V32.2905Z\"/>'})];export{V as filterOffIcon,L as filterOffIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,YAAY;EAACC,CAAC,GAAC,CAAC,YAAY,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,gzBAAgzB;IAACC,KAAK,EAAC;EAAwoB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,aAAa,EAACJ,CAAC,IAAIK,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}