{"ast": null, "code": "\"use strict\";\n\n/**\n * Zone.js flags to disable problematic patches\n */\n// Disable some patches that might cause issues in iframe environments\nwindow.__Zone_disable_on_property = true; // disable patch onProperty such as onclick\nwindow.__zone_symbol__UNPATCHED_EVENTS = ['scroll', 'mousemove']; // disable patch specified eventNames\nwindow.__Zone_enable_cross_context_check = true; // bypass zone.js patch for cross-context", "map": {"version": 3, "names": ["window", "__Zone_disable_on_property", "__zone_symbol__UNPATCHED_EVENTS", "__Zone_enable_cross_context_check"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/src/zone-flags.ts"], "sourcesContent": ["/**\n * Zone.js flags to disable problematic patches\n */\n\n// Disable some patches that might cause issues in iframe environments\n(window as any).__Zone_disable_on_property = true; // disable patch onProperty such as onclick\n(window as any).__zone_symbol__UNPATCHED_EVENTS = ['scroll', 'mousemove']; // disable patch specified eventNames\n(window as any).__Zone_enable_cross_context_check = true; // bypass zone.js patch for cross-context\n"], "mappings": ";;AAAA;;;AAIA;AACCA,MAAc,CAACC,0BAA0B,GAAG,IAAI,CAAC,CAAC;AAClDD,MAAc,CAACE,+BAA+B,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC;AAC1EF,MAAc,CAACG,iCAAiC,GAAG,IAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}