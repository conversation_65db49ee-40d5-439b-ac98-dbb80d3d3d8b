{"ast": null, "code": "function t() {\n  return t => t.addInitializer(t => new s(t));\n}\nclass s {\n  constructor(t) {\n    this.host = t, this.host.addController(this);\n  }\n  hostConnected() {\n    this.host.tabIndex = 0;\n  }\n  hostUpdated() {\n    this.host.role = this.host.readonly ? null : \"button\", this.host.tabIndex = this.host.disabled ? -1 : 0, this.host.readonly && this.host.removeAttribute(\"tabindex\");\n  }\n}\nexport { s as AriaButtonController, t as ariaButton };", "map": {"version": 3, "names": ["t", "addInitializer", "s", "constructor", "host", "addController", "hostConnected", "tabIndex", "hostUpdated", "role", "readonly", "disabled", "removeAttribute", "AriaButtonController", "aria<PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/aria-button.controller.js"], "sourcesContent": ["function t(){return t=>t.addInitializer((t=>new s(t)))}class s{constructor(t){this.host=t,this.host.addController(this)}hostConnected(){this.host.tabIndex=0}hostUpdated(){this.host.role=this.host.readonly?null:\"button\",this.host.tabIndex=this.host.disabled?-1:0,this.host.readonly&&this.host.removeAttribute(\"tabindex\")}}export{s as AriaButtonController,t as ariaButton};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAOA,CAAC,IAAEA,CAAC,CAACC,cAAc,CAAED,CAAC,IAAE,IAAIE,CAAC,CAACF,CAAC,CAAE,CAAC;AAAA;AAAC,MAAME,CAAC;EAACC,WAAWA,CAACH,CAAC,EAAC;IAAC,IAAI,CAACI,IAAI,GAACJ,CAAC,EAAC,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;EAAA;EAACC,aAAaA,CAAA,EAAE;IAAC,IAAI,CAACF,IAAI,CAACG,QAAQ,GAAC,CAAC;EAAA;EAACC,WAAWA,CAAA,EAAE;IAAC,IAAI,CAACJ,IAAI,CAACK,IAAI,GAAC,IAAI,CAACL,IAAI,CAACM,QAAQ,GAAC,IAAI,GAAC,QAAQ,EAAC,IAAI,CAACN,IAAI,CAACG,QAAQ,GAAC,IAAI,CAACH,IAAI,CAACO,QAAQ,GAAC,CAAC,CAAC,GAAC,CAAC,EAAC,IAAI,CAACP,IAAI,CAACM,QAAQ,IAAE,IAAI,CAACN,IAAI,CAACQ,eAAe,CAAC,UAAU,CAAC;EAAA;AAAC;AAAC,SAAOV,CAAC,IAAIW,oBAAoB,EAACb,CAAC,IAAIc,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}