{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst r = \"bars\",\n  t = [\"bars\", C({\n    outline: '<path d=\"M32 9H4C3.44772 9 3 8.55228 3 8C3 7.44772 3.44772 7 4 7H32C32.5523 7 33 7.44772 33 8C33 8.55228 32.5523 9 32 9Z\"/><path d=\"M32 19H4C3.44772 19 3 18.5523 3 18C3 17.4477 3.44772 17 4 17H32C32.5523 17 33 17.4477 33 18C33 18.5523 32.5523 19 32 19Z\"/><path d=\"M4 29H32C32.5523 29 33 28.5523 33 28C33 27.4477 32.5523 27 32 27H4C3.44772 27 3 27.4477 3 28C3 28.5523 3.44772 29 4 29Z\"/>'\n  })];\nexport { t as barsIcon, r as barsIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "r", "t", "outline", "barsIcon", "barsIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/bars.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const r=\"bars\",t=[\"bars\",C({outline:'<path d=\"M32 9H4C3.44772 9 3 8.55228 3 8C3 7.44772 3.44772 7 4 7H32C32.5523 7 33 7.44772 33 8C33 8.55228 32.5523 9 32 9Z\"/><path d=\"M32 19H4C3.44772 19 3 18.5523 3 18C3 17.4477 3.44772 17 4 17H32C32.5523 17 33 17.4477 33 18C33 18.5523 32.5523 19 32 19Z\"/><path d=\"M4 29H32C32.5523 29 33 28.5523 33 28C33 27.4477 32.5523 27 32 27H4C3.44772 27 3 27.4477 3 28C3 28.5523 3.44772 29 4 29Z\"/>'})];export{t as barsIcon,r as barsIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,MAAM;EAACC,CAAC,GAAC,CAAC,MAAM,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAoY,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,QAAQ,EAACH,CAAC,IAAII,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}