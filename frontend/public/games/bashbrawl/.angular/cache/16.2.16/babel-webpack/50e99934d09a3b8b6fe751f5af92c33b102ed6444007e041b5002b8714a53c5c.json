{"ast": null, "code": "import { renderIcon as L } from \"../icon.renderer.js\";\nconst C = \"bundle\",\n  V = [\"bundle\", L({\n    outline: '<path d=\"M31.49 8.92206L19.49 2.12991C19.16 1.93985 18.75 1.95986 18.43 2.17993L4.43 11.803C4.16 11.993 4 12.3031 4 12.6232V26.2075C4 26.5676 4.19 26.8978 4.51 27.0778L16.51 33.87C16.66 33.96 16.83 34 17 34C17.2 34 17.4 33.94 17.57 33.8199L31.57 24.1869C31.84 23.9968 32 23.6867 32 23.3666V9.79234C32 9.43222 31.81 9.10212 31.49 8.92206ZM19.05 4.18056L28.78 9.6923L24.1 12.9133L14.37 7.40158L19.05 4.18056ZM16 31.2991L6 25.6374V13.9336L16 19.5954V31.2991ZM16.95 17.8349L7.22 12.3231L12.54 8.66198L22.27 14.1737L16.95 17.8349ZM30 22.8565L18 31.1091V19.5354L30 11.2828V22.8565Z\"/>',\n    solid: '<path d=\"M31.4898 8.92218L19.4898 2.13003C19.1598 1.93997 18.7498 1.95998 18.4298 2.18005L4.93977 11.463L4.43977 11.8031C4.16977 11.9932 4.00977 12.3033 4.00977 12.6234V26.2077C4.00977 26.5678 4.19977 26.8979 4.51977 27.0779L16.0198 33.59L16.5198 33.8701C16.6698 33.9601 16.8398 34.0001 17.0098 34.0001C17.2098 34.0001 17.4098 33.9401 17.5798 33.8201L31.5798 24.187C31.8498 23.997 32.0098 23.6869 32.0098 23.3668V9.79246C32.0098 9.43234 31.8198 9.10224 31.4998 8.92218H31.4898ZM19.0498 4.18068L28.7798 9.69243L24.0998 12.9134L14.3698 7.4017L19.0498 4.18068ZM17.1098 17.715L7.38977 12.2032L12.5398 8.6621L22.2698 14.1738L17.1198 17.715H17.1098ZM29.9898 22.8566L17.9898 31.1092V19.5355L29.9898 11.2829V22.8566Z\"/>'\n  })];\nexport { V as bundleIcon, C as bundleIconName };", "map": {"version": 3, "names": ["renderIcon", "L", "C", "V", "outline", "solid", "bundleIcon", "bundleIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/bundle.js"], "sourcesContent": ["import{renderIcon as L}from\"../icon.renderer.js\";const C=\"bundle\",V=[\"bundle\",L({outline:'<path d=\"M31.49 8.92206L19.49 2.12991C19.16 1.93985 18.75 1.95986 18.43 2.17993L4.43 11.803C4.16 11.993 4 12.3031 4 12.6232V26.2075C4 26.5676 4.19 26.8978 4.51 27.0778L16.51 33.87C16.66 33.96 16.83 34 17 34C17.2 34 17.4 33.94 17.57 33.8199L31.57 24.1869C31.84 23.9968 32 23.6867 32 23.3666V9.79234C32 9.43222 31.81 9.10212 31.49 8.92206ZM19.05 4.18056L28.78 9.6923L24.1 12.9133L14.37 7.40158L19.05 4.18056ZM16 31.2991L6 25.6374V13.9336L16 19.5954V31.2991ZM16.95 17.8349L7.22 12.3231L12.54 8.66198L22.27 14.1737L16.95 17.8349ZM30 22.8565L18 31.1091V19.5354L30 11.2828V22.8565Z\"/>',solid:'<path d=\"M31.4898 8.92218L19.4898 2.13003C19.1598 1.93997 18.7498 1.95998 18.4298 2.18005L4.93977 11.463L4.43977 11.8031C4.16977 11.9932 4.00977 12.3033 4.00977 12.6234V26.2077C4.00977 26.5678 4.19977 26.8979 4.51977 27.0779L16.0198 33.59L16.5198 33.8701C16.6698 33.9601 16.8398 34.0001 17.0098 34.0001C17.2098 34.0001 17.4098 33.9401 17.5798 33.8201L31.5798 24.187C31.8498 23.997 32.0098 23.6869 32.0098 23.3668V9.79246C32.0098 9.43234 31.8198 9.10224 31.4998 8.92218H31.4898ZM19.0498 4.18068L28.7798 9.69243L24.0998 12.9134L14.3698 7.4017L19.0498 4.18068ZM17.1098 17.715L7.38977 12.2032L12.5398 8.6621L22.2698 14.1738L17.1198 17.715H17.1098ZM29.9898 22.8566L17.9898 31.1092V19.5355L29.9898 11.2829V22.8566Z\"/>'})];export{V as bundleIcon,C as bundleIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,okBAAokB;IAACC,KAAK,EAAC;EAAysB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,UAAU,EAACJ,CAAC,IAAIK,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}