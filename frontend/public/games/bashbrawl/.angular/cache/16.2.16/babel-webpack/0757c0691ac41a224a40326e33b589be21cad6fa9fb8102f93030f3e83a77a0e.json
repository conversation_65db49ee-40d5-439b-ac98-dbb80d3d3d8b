{"ast": null, "code": "import { renderIcon as a } from \"../icon.renderer.js\";\nconst e = \"detail-expand\",\n  t = [\"detail-expand\", a({\n    outline: '<g><path d=\"M14.71 19.71l.68-.71-.68-.71-4.51-4.67A1 1 0 108.76 15l2.89 3H2a1 1 0 000 2h9.65l-2.89 3a1 1 0 000 1.42 1.05 1.05 0 00.7.28 1 1 0 00.72-.31l4.51-4.67z\"/><path d=\"M33 32H9v-4a1 1 0 012 0v2h20V6H11v4a1 1 0 01-2 0V4h24z\"/></g>'\n  })];\nexport { t as detailExpandIcon, e as detailExpandIconName };", "map": {"version": 3, "names": ["renderIcon", "a", "e", "t", "outline", "detailExpandIcon", "detailExpandIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/detail-expand.js"], "sourcesContent": ["import{renderIcon as a}from\"../icon.renderer.js\";const e=\"detail-expand\",t=[\"detail-expand\",a({outline:'<g><path d=\"M14.71 19.71l.68-.71-.68-.71-4.51-4.67A1 1 0 108.76 15l2.89 3H2a1 1 0 000 2h9.65l-2.89 3a1 1 0 000 1.42 1.05 1.05 0 00.7.28 1 1 0 00.72-.31l4.51-4.67z\"/><path d=\"M33 32H9v-4a1 1 0 012 0v2h20V6H11v4a1 1 0 01-2 0V4h24z\"/></g>'})];export{t as detailExpandIcon,e as detailExpandIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,eAAe;EAACC,CAAC,GAAC,CAAC,eAAe,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAA6O,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,gBAAgB,EAACH,CAAC,IAAII,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}