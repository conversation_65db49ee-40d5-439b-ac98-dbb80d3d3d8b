{"ast": null, "code": "import { renderIcon as t } from \"../icon.renderer.js\";\nconst h = \"application\",\n  i = [\"application\", t({\n    outline: '<rect x=\"5\" y=\"7\" width=\"2\" height=\"2\"/><rect x=\"9\" y=\"7\" width=\"2\" height=\"2\"/><rect x=\"13\" y=\"7\" width=\"2\" height=\"2\"/><path d=\"M32,4H4A2,2,0,0,0,2,6V30a2,2,0,0,0,2,2H32a2,2,0,0,0,2-2V6A2,2,0,0,0,32,4ZM4,6H32v4.2H4ZM4,30V11.8H32V30Z\"/>',\n    solid: '<path d=\"M32,4H4A2,2,0,0,0,2,6V30a2,2,0,0,0,2,2H32a2,2,0,0,0,2-2V6A2,2,0,0,0,32,4Zm0,6.2H4V6H32Z\"/><rect x=\"5\" y=\"7\" width=\"2\" height=\"2\"/><rect x=\"9\" y=\"7\" width=\"2\" height=\"2\"/><rect x=\"13\" y=\"7\" width=\"2\" height=\"2\"/>'\n  })];\nexport { i as applicationIcon, h as applicationIconName };", "map": {"version": 3, "names": ["renderIcon", "t", "h", "i", "outline", "solid", "applicationIcon", "applicationIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/application.js"], "sourcesContent": ["import{renderIcon as t}from\"../icon.renderer.js\";const h=\"application\",i=[\"application\",t({outline:'<rect x=\"5\" y=\"7\" width=\"2\" height=\"2\"/><rect x=\"9\" y=\"7\" width=\"2\" height=\"2\"/><rect x=\"13\" y=\"7\" width=\"2\" height=\"2\"/><path d=\"M32,4H4A2,2,0,0,0,2,6V30a2,2,0,0,0,2,2H32a2,2,0,0,0,2-2V6A2,2,0,0,0,32,4ZM4,6H32v4.2H4ZM4,30V11.8H32V30Z\"/>',solid:'<path d=\"M32,4H4A2,2,0,0,0,2,6V30a2,2,0,0,0,2,2H32a2,2,0,0,0,2-2V6A2,2,0,0,0,32,4Zm0,6.2H4V6H32Z\"/><rect x=\"5\" y=\"7\" width=\"2\" height=\"2\"/><rect x=\"9\" y=\"7\" width=\"2\" height=\"2\"/><rect x=\"13\" y=\"7\" width=\"2\" height=\"2\"/>'})];export{i as applicationIcon,h as applicationIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,aAAa;EAACC,CAAC,GAAC,CAAC,aAAa,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,+OAA+O;IAACC,KAAK,EAAC;EAA8N,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,eAAe,EAACJ,CAAC,IAAIK,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}