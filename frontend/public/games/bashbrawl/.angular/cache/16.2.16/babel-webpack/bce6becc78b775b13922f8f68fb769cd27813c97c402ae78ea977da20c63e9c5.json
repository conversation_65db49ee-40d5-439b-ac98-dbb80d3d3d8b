{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"plugin\",\n  V = [\"plugin\", C({\n    outline: '<path d=\"M25.29 34H4.71C3.21 34 2 32.78 2 31.29V25C2 24.45 2.45 24 3 24H5C6.65 24 8 22.65 8 21C8 19.35 6.65 18 5 18H3C2.45 18 2 17.55 2 17V10.71C2 9.21 3.22 8 4.71 8H10V7C10 4.24 12.24 2 15 2C17.76 2 20 4.24 20 7V8H25.29C26.79 8 28 9.22 28 10.71V16H29C31.76 16 34 18.24 34 21C34 23.76 31.76 26 29 26H28V31.29C28 32.79 26.78 34 25.29 34ZM4 26V31.29C4 31.68 4.32 32 4.71 32H25.28C25.67 32 25.99 31.68 25.99 31.29V25C25.99 24.45 26.44 24 26.99 24H28.99C30.64 24 31.99 22.65 31.99 21C31.99 19.35 30.64 18 28.99 18H26.99C26.44 18 25.99 17.55 25.99 17V10.71C25.99 10.32 25.67 10 25.28 10H18.99C18.44 10 17.99 9.55 17.99 9V7C17.99 5.35 16.64 4 14.99 4C13.34 4 11.99 5.35 11.99 7V9C11.99 9.55 11.54 10 10.99 10H4.71C4.32 10 4 10.32 4 10.71V16H5C7.76 16 10 18.24 10 21C10 23.76 7.76 26 5 26H4Z\"/>',\n    outlineAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M20.7594 8H20V7C20 4.24 17.76 2 15 2C12.24 2 10 4.24 10 7V8H4.71C3.22 8 2 9.21 2 10.71V17C2 17.55 2.45 18 3 18H5C6.65 18 8 19.35 8 21C8 22.65 6.65 24 5 24H3C2.45 24 2 24.45 2 25V31.29C2 32.78 3.21 34 4.71 34H25.29C26.78 34 28 32.79 28 31.29V26H29C31.76 26 34 23.76 34 21C34 18.24 31.76 16 29 16H28V15.0367H25.99V17C25.99 17.55 26.44 18 26.99 18H28.99C30.64 18 31.99 19.35 31.99 21C31.99 22.65 30.64 24 28.99 24H26.99C26.44 24 25.99 24.45 25.99 25V31.29C25.99 31.68 25.67 32 25.28 32H4.71C4.32 32 4 31.68 4 31.29V26H5C7.76 26 10 23.76 10 21C10 18.24 7.76 16 5 16H4V10.71C4 10.32 4.32 10 4.71 10H10.99C11.54 10 11.99 9.55 11.99 9V7C11.99 5.35 13.34 4 14.99 4C16.64 4 17.99 5.35 17.99 7V9C17.99 9.55 18.44 10 18.99 10H19.5594L20.7594 8Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M23.2899 8H20V7C20 4.24 17.76 2 15 2C12.24 2 10 4.24 10 7V8H4.71C3.22 8 2 9.21 2 10.71V17C2 17.55 2.45 18 3 18H5C6.65 18 8 19.35 8 21C8 22.65 6.65 24 5 24H3C2.45 24 2 24.45 2 25V31.29C2 32.78 3.21 34 4.71 34H25.29C26.78 34 28 32.79 28 31.29V26H29C31.76 26 34 23.76 34 21C34 18.24 31.76 16 29 16H28V12.7101C27.2736 12.4939 26.5966 12.163 25.99 11.7383V17C25.99 17.55 26.44 18 26.99 18H28.99C30.64 18 31.99 19.35 31.99 21C31.99 22.65 30.64 24 28.99 24H26.99C26.44 24 25.99 24.45 25.99 25V31.29C25.99 31.68 25.67 32 25.28 32H4.71C4.32 32 4 31.68 4 31.29V26H5C7.76 26 10 23.76 10 21C10 18.24 7.76 16 5 16H4V10.71C4 10.32 4.32 10 4.71 10H10.99C11.54 10 11.99 9.55 11.99 9V7C11.99 5.35 13.34 4 14.99 4C16.64 4 17.99 5.35 17.99 7V9C17.99 9.55 18.44 10 18.99 10H24.2547C23.8334 9.39599 23.5049 8.72243 23.2899 8Z\"/>',\n    solid: '<path d=\"M29.0084 16H28.0081V10.71C28.0081 9.21 26.7877 8 25.2973 8H20.0056V7C20.0056 4.24 17.7649 2 15.0041 2C12.2432 2 10.0025 4.24 10.0025 7V8H4.71085C3.21038 8 2 9.22 2 10.71V16C2 16.55 2.45014 17 3.00031 17H5.00094C7.21163 17 9.00219 18.79 9.00219 21C9.00219 23.21 7.21163 25 5.00094 25H3.00031C2.45014 25 2 25.45 2 26V31.29C2 32.79 3.22038 34 4.71085 34H25.2873C26.7877 34 27.9981 32.78 27.9981 31.29V26H28.9984C31.7593 26 34 23.76 34 21C34 18.24 31.7593 16 28.9984 16H29.0084Z\"/>',\n    solidAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M20.7594 8L19.5362 10.0387C18.8703 11.0423 18.8204 12.3342 19.4206 13.3893C20.0233 14.4489 21.1577 15.0604 22.3395 15.0367H28.0081V16H28.9984C31.7593 16 34 18.24 34 21C34 23.76 31.7593 26 28.9984 26H27.9981V31.29C27.9981 32.78 26.7877 34 25.2873 34H4.71085C3.22038 34 2 32.79 2 31.29V26C2 25.45 2.45014 25 3.00031 25H5.00094C7.21163 25 9.00219 23.21 9.00219 21C9.00219 18.79 7.21163 17 5.00094 17H3.00031C2.45014 17 2 16.55 2 16V10.71C2 9.22 3.21038 8 4.71085 8H10.0025V7C10.0025 4.24 12.2432 2 15.0041 2C17.7649 2 20.0056 4.24 20.0056 7V8H20.7594Z\"/>',\n    solidBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M23.2899 8C23.9626 10.2605 25.7465 12.0424 28.0081 12.7125V16H28.9984C31.7593 16 34 18.24 34 21C34 23.76 31.7593 26 28.9984 26H27.9981V31.29C27.9981 32.78 26.7877 34 25.2873 34H4.71085C3.22038 34 2 32.79 2 31.29V26C2 25.45 2.45014 25 3.00031 25H5.00094C7.21163 25 9.00219 23.21 9.00219 21C9.00219 18.79 7.21163 17 5.00094 17H3.00031C2.45014 17 2 16.55 2 16V10.71C2 9.22 3.21038 8 4.71085 8H10.0025V7C10.0025 4.24 12.2432 2 15.0041 2C17.7649 2 20.0056 4.24 20.0056 7V8H23.2899Z\"/>'\n  })];\nexport { V as pluginIcon, H as pluginIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "V", "outline", "outlineAlerted", "outlineBadged", "solid", "solidAlerted", "solidBadged", "pluginIcon", "pluginIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/plugin.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"plugin\",V=[\"plugin\",C({outline:'<path d=\"M25.29 34H4.71C3.21 34 2 32.78 2 31.29V25C2 24.45 2.45 24 3 24H5C6.65 24 8 22.65 8 21C8 19.35 6.65 18 5 18H3C2.45 18 2 17.55 2 17V10.71C2 9.21 3.22 8 4.71 8H10V7C10 4.24 12.24 2 15 2C17.76 2 20 4.24 20 7V8H25.29C26.79 8 28 9.22 28 10.71V16H29C31.76 16 34 18.24 34 21C34 23.76 31.76 26 29 26H28V31.29C28 32.79 26.78 34 25.29 34ZM4 26V31.29C4 31.68 4.32 32 4.71 32H25.28C25.67 32 25.99 31.68 25.99 31.29V25C25.99 24.45 26.44 24 26.99 24H28.99C30.64 24 31.99 22.65 31.99 21C31.99 19.35 30.64 18 28.99 18H26.99C26.44 18 25.99 17.55 25.99 17V10.71C25.99 10.32 25.67 10 25.28 10H18.99C18.44 10 17.99 9.55 17.99 9V7C17.99 5.35 16.64 4 14.99 4C13.34 4 11.99 5.35 11.99 7V9C11.99 9.55 11.54 10 10.99 10H4.71C4.32 10 4 10.32 4 10.71V16H5C7.76 16 10 18.24 10 21C10 23.76 7.76 26 5 26H4Z\"/>',outlineAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M20.7594 8H20V7C20 4.24 17.76 2 15 2C12.24 2 10 4.24 10 7V8H4.71C3.22 8 2 9.21 2 10.71V17C2 17.55 2.45 18 3 18H5C6.65 18 8 19.35 8 21C8 22.65 6.65 24 5 24H3C2.45 24 2 24.45 2 25V31.29C2 32.78 3.21 34 4.71 34H25.29C26.78 34 28 32.79 28 31.29V26H29C31.76 26 34 23.76 34 21C34 18.24 31.76 16 29 16H28V15.0367H25.99V17C25.99 17.55 26.44 18 26.99 18H28.99C30.64 18 31.99 19.35 31.99 21C31.99 22.65 30.64 24 28.99 24H26.99C26.44 24 25.99 24.45 25.99 25V31.29C25.99 31.68 25.67 32 25.28 32H4.71C4.32 32 4 31.68 4 31.29V26H5C7.76 26 10 23.76 10 21C10 18.24 7.76 16 5 16H4V10.71C4 10.32 4.32 10 4.71 10H10.99C11.54 10 11.99 9.55 11.99 9V7C11.99 5.35 13.34 4 14.99 4C16.64 4 17.99 5.35 17.99 7V9C17.99 9.55 18.44 10 18.99 10H19.5594L20.7594 8Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M23.2899 8H20V7C20 4.24 17.76 2 15 2C12.24 2 10 4.24 10 7V8H4.71C3.22 8 2 9.21 2 10.71V17C2 17.55 2.45 18 3 18H5C6.65 18 8 19.35 8 21C8 22.65 6.65 24 5 24H3C2.45 24 2 24.45 2 25V31.29C2 32.78 3.21 34 4.71 34H25.29C26.78 34 28 32.79 28 31.29V26H29C31.76 26 34 23.76 34 21C34 18.24 31.76 16 29 16H28V12.7101C27.2736 12.4939 26.5966 12.163 25.99 11.7383V17C25.99 17.55 26.44 18 26.99 18H28.99C30.64 18 31.99 19.35 31.99 21C31.99 22.65 30.64 24 28.99 24H26.99C26.44 24 25.99 24.45 25.99 25V31.29C25.99 31.68 25.67 32 25.28 32H4.71C4.32 32 4 31.68 4 31.29V26H5C7.76 26 10 23.76 10 21C10 18.24 7.76 16 5 16H4V10.71C4 10.32 4.32 10 4.71 10H10.99C11.54 10 11.99 9.55 11.99 9V7C11.99 5.35 13.34 4 14.99 4C16.64 4 17.99 5.35 17.99 7V9C17.99 9.55 18.44 10 18.99 10H24.2547C23.8334 9.39599 23.5049 8.72243 23.2899 8Z\"/>',solid:'<path d=\"M29.0084 16H28.0081V10.71C28.0081 9.21 26.7877 8 25.2973 8H20.0056V7C20.0056 4.24 17.7649 2 15.0041 2C12.2432 2 10.0025 4.24 10.0025 7V8H4.71085C3.21038 8 2 9.22 2 10.71V16C2 16.55 2.45014 17 3.00031 17H5.00094C7.21163 17 9.00219 18.79 9.00219 21C9.00219 23.21 7.21163 25 5.00094 25H3.00031C2.45014 25 2 25.45 2 26V31.29C2 32.79 3.22038 34 4.71085 34H25.2873C26.7877 34 27.9981 32.78 27.9981 31.29V26H28.9984C31.7593 26 34 23.76 34 21C34 18.24 31.7593 16 28.9984 16H29.0084Z\"/>',solidAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M20.7594 8L19.5362 10.0387C18.8703 11.0423 18.8204 12.3342 19.4206 13.3893C20.0233 14.4489 21.1577 15.0604 22.3395 15.0367H28.0081V16H28.9984C31.7593 16 34 18.24 34 21C34 23.76 31.7593 26 28.9984 26H27.9981V31.29C27.9981 32.78 26.7877 34 25.2873 34H4.71085C3.22038 34 2 32.79 2 31.29V26C2 25.45 2.45014 25 3.00031 25H5.00094C7.21163 25 9.00219 23.21 9.00219 21C9.00219 18.79 7.21163 17 5.00094 17H3.00031C2.45014 17 2 16.55 2 16V10.71C2 9.22 3.21038 8 4.71085 8H10.0025V7C10.0025 4.24 12.2432 2 15.0041 2C17.7649 2 20.0056 4.24 20.0056 7V8H20.7594Z\"/>',solidBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M23.2899 8C23.9626 10.2605 25.7465 12.0424 28.0081 12.7125V16H28.9984C31.7593 16 34 18.24 34 21C34 23.76 31.7593 26 28.9984 26H27.9981V31.29C27.9981 32.78 26.7877 34 25.2873 34H4.71085C3.22038 34 2 32.79 2 31.29V26C2 25.45 2.45014 25 3.00031 25H5.00094C7.21163 25 9.00219 23.21 9.00219 21C9.00219 18.79 7.21163 17 5.00094 17H3.00031C2.45014 17 2 16.55 2 16V10.71C2 9.22 3.21038 8 4.71085 8H10.0025V7C10.0025 4.24 12.2432 2 15.0041 2C17.7649 2 20.0056 4.24 20.0056 7V8H23.2899Z\"/>'})];export{V as pluginIcon,H as pluginIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,qxBAAqxB;IAACC,cAAc,EAAC,8kCAA8kC;IAACC,aAAa,EAAC,g7BAAg7B;IAACC,KAAK,EAAC,weAAwe;IAACC,YAAY,EAAC,q5BAAq5B;IAACC,WAAW,EAAC;EAAwmB,CAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,UAAU,EAACR,CAAC,IAAIS,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}