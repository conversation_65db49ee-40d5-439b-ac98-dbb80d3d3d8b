{"ast": null, "code": "import { renderIcon as r } from \"../icon.renderer.js\";\nconst C = \"align-right\",\n  H = [\"align-right\", r({\n    outline: '<path d=\"M12 27.25C12 27.8 12.45 28.25 13 28.25H30V26.25H13C12.45 26.25 12 26.7 12 27.25ZM6 21.25C6 21.8 6.45 22.25 7 22.25H30V20.25H7C6.45 20.25 6 20.7 6 21.25ZM7 8.25C6.45 8.25 6 8.7 6 9.25C6 9.8 6.45 10.25 7 10.25H30V8.25H7ZM12 15.25C12 15.8 12.45 16.25 13 16.25H30V14.25H13C12.45 14.25 12 14.7 12 15.25Z\"/>'\n  })];\nexport { H as alignRightIcon, C as alignRightIconName };", "map": {"version": 3, "names": ["renderIcon", "r", "C", "H", "outline", "alignRightIcon", "alignRightIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/align-right.js"], "sourcesContent": ["import{renderIcon as r}from\"../icon.renderer.js\";const C=\"align-right\",H=[\"align-right\",r({outline:'<path d=\"M12 27.25C12 27.8 12.45 28.25 13 28.25H30V26.25H13C12.45 26.25 12 26.7 12 27.25ZM6 21.25C6 21.8 6.45 22.25 7 22.25H30V20.25H7C6.45 20.25 6 20.7 6 21.25ZM7 8.25C6.45 8.25 6 8.7 6 9.25C6 9.8 6.45 10.25 7 10.25H30V8.25H7ZM12 15.25C12 15.8 12.45 16.25 13 16.25H30V14.25H13C12.45 14.25 12 14.7 12 15.25Z\"/>'})];export{H as alignRightIcon,C as alignRightIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,aAAa;EAACC,CAAC,GAAC,CAAC,aAAa,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAwT,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,cAAc,EAACH,CAAC,IAAII,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}