{"ast": null, "code": "import { renderIcon as e } from \"../icon.renderer.js\";\nconst i = \"check-mini\",\n  c = [\"check-mini\", e({\n    outline: '<path d=\"M13.13,27.94,4.61,17.43a2,2,0,1,1,3.11-2.52l5.71,7L28.49,6.68a2,2,0,0,1,2.85,2.81Z\"/>'\n  })];\nexport { c as checkMiniIcon, i as checkMiniIconName };", "map": {"version": 3, "names": ["renderIcon", "e", "i", "c", "outline", "checkMiniIcon", "checkMiniIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/check-mini.js"], "sourcesContent": ["import{renderIcon as e}from\"../icon.renderer.js\";const i=\"check-mini\",c=[\"check-mini\",e({outline:'<path d=\"M13.13,27.94,4.61,17.43a2,2,0,1,1,3.11-2.52l5.71,7L28.49,6.68a2,2,0,0,1,2.85,2.81Z\"/>'})];export{c as checkMiniIcon,i as checkMiniIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,YAAY;EAACC,CAAC,GAAC,CAAC,YAAY,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAgG,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,aAAa,EAACH,CAAC,IAAII,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}