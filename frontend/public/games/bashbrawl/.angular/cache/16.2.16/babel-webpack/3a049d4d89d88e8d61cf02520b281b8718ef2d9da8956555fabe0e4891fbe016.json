{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nexport const legacyPrototypeMethod = (descriptor, proto, name) => {\n  Object.defineProperty(proto, name, descriptor);\n};\nexport const standardPrototypeMethod = (descriptor, element) => ({\n  kind: 'method',\n  placement: 'prototype',\n  key: element.key,\n  descriptor\n});\n/**\n * Helper for decorating a property that is compatible with both TypeScript\n * and Babel decorators. The optional `finisher` can be used to perform work on\n * the class. The optional `descriptor` should return a PropertyDescriptor\n * to install for the given property.\n *\n * @param finisher {function} Optional finisher method; receives the element\n * constructor and property key as arguments and has no return value.\n * @param descriptor {function} Optional descriptor method; receives the\n * property key as an argument and returns a property descriptor to define for\n * the given property.\n * @returns {ClassElement|void}\n */\nexport const decorateProperty = ({\n  finisher,\n  descriptor\n}) => (protoOrDescriptor, name\n// Note TypeScript requires the return type to be `void|any`\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n) => {\n  var _a;\n  // TypeScript / Babel legacy mode\n  if (name !== undefined) {\n    const ctor = protoOrDescriptor.constructor;\n    if (descriptor !== undefined) {\n      Object.defineProperty(protoOrDescriptor, name, descriptor(name));\n    }\n    finisher === null || finisher === void 0 ? void 0 : finisher(ctor, name);\n    // Babel standard mode\n  } else {\n    // Note, the @property decorator saves `key` as `originalKey`\n    // so try to use it here.\n    const key =\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (_a = protoOrDescriptor.originalKey) !== null && _a !== void 0 ? _a : protoOrDescriptor.key;\n    const info = descriptor != undefined ? {\n      kind: 'method',\n      placement: 'prototype',\n      key,\n      descriptor: descriptor(protoOrDescriptor.key)\n    } : {\n      ...protoOrDescriptor,\n      key\n    };\n    if (finisher != undefined) {\n      info.finisher = function (ctor) {\n        finisher(ctor, key);\n      };\n    }\n    return info;\n  }\n};", "map": {"version": 3, "names": ["legacyPrototypeMethod", "descriptor", "proto", "name", "Object", "defineProperty", "standardPrototypeMethod", "element", "kind", "placement", "key", "decorateProperty", "finisher", "protoOrDescriptor", "_a", "undefined", "ctor", "constructor", "original<PERSON>ey", "info"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@lit/reactive-element/development/decorators/base.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nexport const legacyPrototypeMethod = (descriptor, proto, name) => {\n    Object.defineProperty(proto, name, descriptor);\n};\nexport const standardPrototypeMethod = (descriptor, element) => ({\n    kind: 'method',\n    placement: 'prototype',\n    key: element.key,\n    descriptor,\n});\n/**\n * Helper for decorating a property that is compatible with both TypeScript\n * and Babel decorators. The optional `finisher` can be used to perform work on\n * the class. The optional `descriptor` should return a PropertyDescriptor\n * to install for the given property.\n *\n * @param finisher {function} Optional finisher method; receives the element\n * constructor and property key as arguments and has no return value.\n * @param descriptor {function} Optional descriptor method; receives the\n * property key as an argument and returns a property descriptor to define for\n * the given property.\n * @returns {ClassElement|void}\n */\nexport const decorateProperty = ({ finisher, descriptor, }) => (protoOrDescriptor, name\n// Note TypeScript requires the return type to be `void|any`\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n) => {\n    var _a;\n    // TypeScript / Babel legacy mode\n    if (name !== undefined) {\n        const ctor = protoOrDescriptor\n            .constructor;\n        if (descriptor !== undefined) {\n            Object.defineProperty(protoOrDescriptor, name, descriptor(name));\n        }\n        finisher === null || finisher === void 0 ? void 0 : finisher(ctor, name);\n        // Babel standard mode\n    }\n    else {\n        // Note, the @property decorator saves `key` as `originalKey`\n        // so try to use it here.\n        const key = \n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (_a = protoOrDescriptor.originalKey) !== null && _a !== void 0 ? _a : protoOrDescriptor.key;\n        const info = descriptor != undefined\n            ? {\n                kind: 'method',\n                placement: 'prototype',\n                key,\n                descriptor: descriptor(protoOrDescriptor.key),\n            }\n            : { ...protoOrDescriptor, key };\n        if (finisher != undefined) {\n            info.finisher = function (ctor) {\n                finisher(ctor, key);\n            };\n        }\n        return info;\n    }\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,qBAAqB,GAAGA,CAACC,UAAU,EAAEC,KAAK,EAAEC,IAAI,KAAK;EAC9DC,MAAM,CAACC,cAAc,CAACH,KAAK,EAAEC,IAAI,EAAEF,UAAU,CAAC;AAClD,CAAC;AACD,OAAO,MAAMK,uBAAuB,GAAGA,CAACL,UAAU,EAAEM,OAAO,MAAM;EAC7DC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,WAAW;EACtBC,GAAG,EAAEH,OAAO,CAACG,GAAG;EAChBT;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMU,gBAAgB,GAAGA,CAAC;EAAEC,QAAQ;EAAEX;AAAY,CAAC,KAAK,CAACY,iBAAiB,EAAEV;AACnF;AACA;AAAA,KACK;EACD,IAAIW,EAAE;EACN;EACA,IAAIX,IAAI,KAAKY,SAAS,EAAE;IACpB,MAAMC,IAAI,GAAGH,iBAAiB,CACzBI,WAAW;IAChB,IAAIhB,UAAU,KAAKc,SAAS,EAAE;MAC1BX,MAAM,CAACC,cAAc,CAACQ,iBAAiB,EAAEV,IAAI,EAAEF,UAAU,CAACE,IAAI,CAAC,CAAC;IACpE;IACAS,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACI,IAAI,EAAEb,IAAI,CAAC;IACxE;EACJ,CAAC,MACI;IACD;IACA;IACA,MAAMO,GAAG;IACT;IACA,CAACI,EAAE,GAAGD,iBAAiB,CAACK,WAAW,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGD,iBAAiB,CAACH,GAAG;IAC3F,MAAMS,IAAI,GAAGlB,UAAU,IAAIc,SAAS,GAC9B;MACEP,IAAI,EAAE,QAAQ;MACdC,SAAS,EAAE,WAAW;MACtBC,GAAG;MACHT,UAAU,EAAEA,UAAU,CAACY,iBAAiB,CAACH,GAAG;IAChD,CAAC,GACC;MAAE,GAAGG,iBAAiB;MAAEH;IAAI,CAAC;IACnC,IAAIE,QAAQ,IAAIG,SAAS,EAAE;MACvBI,IAAI,CAACP,QAAQ,GAAG,UAAUI,IAAI,EAAE;QAC5BJ,QAAQ,CAACI,IAAI,EAAEN,GAAG,CAAC;MACvB,CAAC;IACL;IACA,OAAOS,IAAI;EACf;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}