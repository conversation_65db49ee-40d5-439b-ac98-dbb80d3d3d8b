{"ast": null, "code": "function t() {\n  return t => t.addInitializer(t => new r(t));\n}\nclass r {\n  constructor(t) {\n    this.host = t, this.host.addController(this);\n  }\n  get currentAnchor() {\n    return \"A\" === this.host.parentElement?.tagName ? this.host.parentElement : null;\n  }\n  hostConnected() {\n    this.setAnchor();\n  }\n  hostUpdated() {\n    this.setAnchor();\n  }\n  setAnchor() {\n    this.currentAnchor && this.currentAnchor !== this.previousAnchor && (this.previousAnchor = this.currentAnchor, this.host.readonly = !0, this.currentAnchor.style.lineHeight = \"0\", this.currentAnchor.style.textDecoration = \"none\");\n  }\n}\nexport { r as ButtonAnchorController, t as buttonAnchor };", "map": {"version": 3, "names": ["t", "addInitializer", "r", "constructor", "host", "addController", "currentAnchor", "parentElement", "tagName", "hostConnected", "setAnchor", "hostUpdated", "previousAnchor", "readonly", "style", "lineHeight", "textDecoration", "ButtonAnchorController", "buttonAnchor"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/button-anchor.controller.js"], "sourcesContent": ["function t(){return t=>t.addInitializer((t=>new r(t)))}class r{constructor(t){this.host=t,this.host.addController(this)}get currentAnchor(){return\"A\"===this.host.parentElement?.tagName?this.host.parentElement:null}hostConnected(){this.setAnchor()}hostUpdated(){this.setAnchor()}setAnchor(){this.currentAnchor&&this.currentAnchor!==this.previousAnchor&&(this.previousAnchor=this.currentAnchor,this.host.readonly=!0,this.currentAnchor.style.lineHeight=\"0\",this.currentAnchor.style.textDecoration=\"none\")}}export{r as ButtonAnchorController,t as buttonAnchor};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAOA,CAAC,IAAEA,CAAC,CAACC,cAAc,CAAED,CAAC,IAAE,IAAIE,CAAC,CAACF,CAAC,CAAE,CAAC;AAAA;AAAC,MAAME,CAAC;EAACC,WAAWA,CAACH,CAAC,EAAC;IAAC,IAAI,CAACI,IAAI,GAACJ,CAAC,EAAC,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;EAAA;EAAC,IAAIC,aAAaA,CAAA,EAAE;IAAC,OAAM,GAAG,KAAG,IAAI,CAACF,IAAI,CAACG,aAAa,EAAEC,OAAO,GAAC,IAAI,CAACJ,IAAI,CAACG,aAAa,GAAC,IAAI;EAAA;EAACE,aAAaA,CAAA,EAAE;IAAC,IAAI,CAACC,SAAS,CAAC,CAAC;EAAA;EAACC,WAAWA,CAAA,EAAE;IAAC,IAAI,CAACD,SAAS,CAAC,CAAC;EAAA;EAACA,SAASA,CAAA,EAAE;IAAC,IAAI,CAACJ,aAAa,IAAE,IAAI,CAACA,aAAa,KAAG,IAAI,CAACM,cAAc,KAAG,IAAI,CAACA,cAAc,GAAC,IAAI,CAACN,aAAa,EAAC,IAAI,CAACF,IAAI,CAACS,QAAQ,GAAC,CAAC,CAAC,EAAC,IAAI,CAACP,aAAa,CAACQ,KAAK,CAACC,UAAU,GAAC,GAAG,EAAC,IAAI,CAACT,aAAa,CAACQ,KAAK,CAACE,cAAc,GAAC,MAAM,CAAC;EAAA;AAAC;AAAC,SAAOd,CAAC,IAAIe,sBAAsB,EAACjB,CAAC,IAAIkB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}