{"ast": null, "code": "import { renderIcon as i } from \"../icon.renderer.js\";\nconst t = \"info-circle-mini\",\n  e = [\"info-circle-mini\", i({\n    outline: '<path d=\"M18,4A14,14,0,1,0,32,18,14,14,0,0,0,18,4Zm0,24A10,10,0,1,1,28,18,10,10,0,0,1,18,28Z\"/><rect x=\"16\" y=\"18\" width=\"4\" height=\"6\"/><rect x=\"16\" y=\"12\" width=\"4\" height=\"3.2\"/>',\n    solid: '<path d=\"M18,4A14,14,0,1,0,32,18,14,14,0,0,0,18,4Zm2,22H16V16h4Zm0-14H16V8h4Z\"/>'\n  })];\nexport { e as infoCircleMiniIcon, t as infoCircleMiniIconName };", "map": {"version": 3, "names": ["renderIcon", "i", "t", "e", "outline", "solid", "infoCircleMiniIcon", "infoCircleMiniIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/info-circle-mini.js"], "sourcesContent": ["import{renderIcon as i}from\"../icon.renderer.js\";const t=\"info-circle-mini\",e=[\"info-circle-mini\",i({outline:'<path d=\"M18,4A14,14,0,1,0,32,18,14,14,0,0,0,18,4Zm0,24A10,10,0,1,1,28,18,10,10,0,0,1,18,28Z\"/><rect x=\"16\" y=\"18\" width=\"4\" height=\"6\"/><rect x=\"16\" y=\"12\" width=\"4\" height=\"3.2\"/>',solid:'<path d=\"M18,4A14,14,0,1,0,32,18,14,14,0,0,0,18,4Zm2,22H16V16h4Zm0-14H16V8h4Z\"/>'})];export{e as infoCircleMiniIcon,t as infoCircleMiniIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,kBAAkB;EAACC,CAAC,GAAC,CAAC,kBAAkB,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,uLAAuL;IAACC,KAAK,EAAC;EAAkF,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,kBAAkB,EAACJ,CAAC,IAAIK,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}