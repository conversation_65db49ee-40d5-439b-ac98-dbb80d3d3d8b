{"ast": null, "code": "function n(n, t) {\n  const e = n.find(n => !0 === n[0]());\n  return e ? e[1]() : t();\n}\nfunction t(n, t, e) {\n  return n.map(n => n()).indexOf(!0) > -1 || !(t.map(n => n()).indexOf(!0) > -1) && e;\n}\nexport { t as anyPassOrAnyFail, n as returnOrFallthrough };", "map": {"version": 3, "names": ["n", "t", "e", "find", "map", "indexOf", "anyPassOrAnyFail", "returnOrFallthrough"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/conditional.js"], "sourcesContent": ["function n(n,t){const e=n.find((n=>!0===n[0]()));return e?e[1]():t()}function t(n,t,e){return n.map((n=>n())).indexOf(!0)>-1||!(t.map((n=>n())).indexOf(!0)>-1)&&e}export{t as anyPassOrAnyFail,n as returnOrFallthrough};\n"], "mappings": "AAAA,SAASA,CAACA,CAACA,CAAC,EAACC,CAAC,EAAC;EAAC,MAAMC,CAAC,GAACF,CAAC,CAACG,IAAI,CAAEH,CAAC,IAAE,CAAC,CAAC,KAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC;EAAC,OAAOE,CAAC,GAACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAACD,CAAC,CAAC,CAAC;AAAA;AAAC,SAASA,CAACA,CAACD,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOF,CAAC,CAACI,GAAG,CAAEJ,CAAC,IAAEA,CAAC,CAAC,CAAE,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,IAAE,EAAEJ,CAAC,CAACG,GAAG,CAAEJ,CAAC,IAAEA,CAAC,CAAC,CAAE,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,<PERSON>AC,CAAC,IAAEH,CAAC;AAAA;AAAC,SAAOD,CAAC,IAAIK,gBAAgB,EAACN,CAAC,IAAIO,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}