{"ast": null, "code": "import \"@lit/reactive-element\";\nimport \"lit-html\";\nexport * from \"lit-element/lit-element.js\";\nexport * from \"lit-html/is-server.js\";", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/lit/index.js"], "sourcesContent": ["import\"@lit/reactive-element\";import\"lit-html\";export*from\"lit-element/lit-element.js\";export*from\"lit-html/is-server.js\";\n"], "mappings": "AAAA,OAAM,uBAAuB;AAAC,OAAM,UAAU;AAAC,cAAW,4BAA4B;AAAC,cAAW,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}