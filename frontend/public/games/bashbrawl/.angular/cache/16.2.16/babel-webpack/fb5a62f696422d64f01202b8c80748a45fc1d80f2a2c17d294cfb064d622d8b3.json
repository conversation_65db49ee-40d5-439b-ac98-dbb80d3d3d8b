{"ast": null, "code": "import _curry1 from \"./internal/_curry1.js\";\nimport _isArguments from \"./internal/_isArguments.js\";\nimport _isArray from \"./internal/_isArray.js\";\nimport _isObject from \"./internal/_isObject.js\";\nimport _isString from \"./internal/_isString.js\";\nimport _isTypedArray from \"./internal/_isTypedArray.js\";\n/**\n * Returns the empty value of its argument's type. <PERSON><PERSON> defines the empty\n * value of Array (`[]`), Object (`{}`), String (`''`),\n * TypedArray (`Uint8Array []`, `Float32Array []`, etc), and Arguments. Other\n * types are supported if they define `<Type>.empty`,\n * `<Type>.prototype.empty` or implement the\n * [FantasyLand Monoid spec](https://github.com/fantasyland/fantasy-land#monoid).\n *\n * Dispatches to the `empty` method of the first argument, if present.\n *\n * @func\n * @memberOf R\n * @since v0.3.0\n * @category Function\n * @sig a -> a\n * @param {*} x\n * @return {*}\n * @example\n *\n *      R.empty(Just(42));               //=> Nothing()\n *      R.empty([1, 2, 3]);              //=> []\n *      R.empty('unicorns');             //=> ''\n *      R.empty({x: 1, y: 2});           //=> {}\n *      R.empty(Uint8Array.from('123')); //=> Uint8Array []\n */\n\nvar empty = /*#__PURE__*/\n_curry1(function empty(x) {\n  return x != null && typeof x['fantasy-land/empty'] === 'function' ? x['fantasy-land/empty']() : x != null && x.constructor != null && typeof x.constructor['fantasy-land/empty'] === 'function' ? x.constructor['fantasy-land/empty']() : x != null && typeof x.empty === 'function' ? x.empty() : x != null && x.constructor != null && typeof x.constructor.empty === 'function' ? x.constructor.empty() : _isArray(x) ? [] : _isString(x) ? '' : _isObject(x) ? {} : _isArguments(x) ? function () {\n    return arguments;\n  }() : _isTypedArray(x) ? x.constructor.from('') : void 0 // else\n  ;\n});\n\nexport default empty;", "map": {"version": 3, "names": ["_curry1", "_isArguments", "_isArray", "_isObject", "_isString", "_isTypedArray", "empty", "x", "constructor", "arguments", "from"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/empty.js"], "sourcesContent": ["import _curry1 from \"./internal/_curry1.js\";\nimport _isArguments from \"./internal/_isArguments.js\";\nimport _isArray from \"./internal/_isArray.js\";\nimport _isObject from \"./internal/_isObject.js\";\nimport _isString from \"./internal/_isString.js\";\nimport _isTypedArray from \"./internal/_isTypedArray.js\";\n/**\n * Returns the empty value of its argument's type. <PERSON><PERSON> defines the empty\n * value of Array (`[]`), Object (`{}`), String (`''`),\n * TypedArray (`Uint8Array []`, `Float32Array []`, etc), and Arguments. Other\n * types are supported if they define `<Type>.empty`,\n * `<Type>.prototype.empty` or implement the\n * [FantasyLand Monoid spec](https://github.com/fantasyland/fantasy-land#monoid).\n *\n * Dispatches to the `empty` method of the first argument, if present.\n *\n * @func\n * @memberOf R\n * @since v0.3.0\n * @category Function\n * @sig a -> a\n * @param {*} x\n * @return {*}\n * @example\n *\n *      R.empty(Just(42));               //=> Nothing()\n *      R.empty([1, 2, 3]);              //=> []\n *      R.empty('unicorns');             //=> ''\n *      R.empty({x: 1, y: 2});           //=> {}\n *      R.empty(Uint8Array.from('123')); //=> Uint8Array []\n */\n\nvar empty =\n/*#__PURE__*/\n_curry1(function empty(x) {\n  return x != null && typeof x['fantasy-land/empty'] === 'function' ? x['fantasy-land/empty']() : x != null && x.constructor != null && typeof x.constructor['fantasy-land/empty'] === 'function' ? x.constructor['fantasy-land/empty']() : x != null && typeof x.empty === 'function' ? x.empty() : x != null && x.constructor != null && typeof x.constructor.empty === 'function' ? x.constructor.empty() : _isArray(x) ? [] : _isString(x) ? '' : _isObject(x) ? {} : _isArguments(x) ? function () {\n    return arguments;\n  }() : _isTypedArray(x) ? x.constructor.from('') : void 0 // else\n  ;\n});\n\nexport default empty;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,aAAa,MAAM,6BAA6B;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,KAAK,GACT;AACAN,OAAO,CAAC,SAASM,KAAKA,CAACC,CAAC,EAAE;EACxB,OAAOA,CAAC,IAAI,IAAI,IAAI,OAAOA,CAAC,CAAC,oBAAoB,CAAC,KAAK,UAAU,GAAGA,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAGA,CAAC,IAAI,IAAI,IAAIA,CAAC,CAACC,WAAW,IAAI,IAAI,IAAI,OAAOD,CAAC,CAACC,WAAW,CAAC,oBAAoB,CAAC,KAAK,UAAU,GAAGD,CAAC,CAACC,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAGD,CAAC,IAAI,IAAI,IAAI,OAAOA,CAAC,CAACD,KAAK,KAAK,UAAU,GAAGC,CAAC,CAACD,KAAK,CAAC,CAAC,GAAGC,CAAC,IAAI,IAAI,IAAIA,CAAC,CAACC,WAAW,IAAI,IAAI,IAAI,OAAOD,CAAC,CAACC,WAAW,CAACF,KAAK,KAAK,UAAU,GAAGC,CAAC,CAACC,WAAW,CAACF,KAAK,CAAC,CAAC,GAAGJ,QAAQ,CAACK,CAAC,CAAC,GAAG,EAAE,GAAGH,SAAS,CAACG,CAAC,CAAC,GAAG,EAAE,GAAGJ,SAAS,CAACI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGN,YAAY,CAACM,CAAC,CAAC,GAAG,YAAY;IACpe,OAAOE,SAAS;EAClB,CAAC,CAAC,CAAC,GAAGJ,aAAa,CAACE,CAAC,CAAC,GAAGA,CAAC,CAACC,WAAW,CAACE,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;EAAA;AAE3D,CAAC,CAAC;;AAEF,eAAeJ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}