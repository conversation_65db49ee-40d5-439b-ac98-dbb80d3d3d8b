{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { listenForAttributeChange as t } from \"../utils/events.js\";\nimport { TriggerController as r } from \"./trigger.controller.js\";\nfunction e() {\n  return t => t.addInitializer(t => new i(t));\n}\nclass i {\n  constructor(t) {\n    this.host = t, this.host.addController(this), this.trigger = new r(this.host, {\n      focus: !1\n    });\n  }\n  hostConnected() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.host.updateComplete, _this.observer = t(_this.host, \"hidden\", () => _this.updateTrigger(!_this.host.hidden));\n    })();\n  }\n  hostUpdate() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      yield _this2.host.updateComplete, _this2.updateTrigger(!_this2.host.hidden);\n    })();\n  }\n  hostDisconnected() {\n    this.updateTrigger(!1), this.observer?.disconnect();\n  }\n  updateTrigger(t) {\n    this.trigger.current?.hasAttribute(\"aria-controls\") && (this.trigger.current.ariaExpanded = \"\" + t), this.trigger.prev?.hasAttribute(\"aria-controls\") && this.trigger.prev !== this.trigger.current && (this.trigger.prev.ariaExpanded = \"false\");\n  }\n}\nexport { i as AriaPopupController, e as ariaPopup };", "map": {"version": 3, "names": ["listenForAttributeChange", "t", "TriggerController", "r", "e", "addInitializer", "i", "constructor", "host", "addController", "trigger", "focus", "hostConnected", "_this", "_asyncToGenerator", "updateComplete", "observer", "updateTrigger", "hidden", "hostUpdate", "_this2", "hostDisconnected", "disconnect", "current", "hasAttribute", "ariaExpanded", "prev", "AriaPopupController", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/aria-popup.controller.js"], "sourcesContent": ["import{listenForAttribute<PERSON>hange as t}from\"../utils/events.js\";import{<PERSON>gger<PERSON>ontroller as r}from\"./trigger.controller.js\";function e(){return t=>t.addInitializer((t=>new i(t)))}class i{constructor(t){this.host=t,this.host.addController(this),this.trigger=new r(this.host,{focus:!1})}async hostConnected(){await this.host.updateComplete,this.observer=t(this.host,\"hidden\",(()=>this.updateTrigger(!this.host.hidden)))}async hostUpdate(){await this.host.updateComplete,this.updateTrigger(!this.host.hidden)}hostDisconnected(){this.updateTrigger(!1),this.observer?.disconnect()}updateTrigger(t){this.trigger.current?.hasAttribute(\"aria-controls\")&&(this.trigger.current.ariaExpanded=\"\"+t),this.trigger.prev?.hasAttribute(\"aria-controls\")&&this.trigger.prev!==this.trigger.current&&(this.trigger.prev.ariaExpanded=\"false\")}}export{i as AriaPopupController,e as ariaPopup};\n"], "mappings": ";AAAA,SAAOA,wBAAwB,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,iBAAiB,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,OAAOH,CAAC,IAAEA,CAAC,CAACI,cAAc,CAAEJ,CAAC,IAAE,IAAIK,CAAC,CAACL,CAAC,CAAE,CAAC;AAAA;AAAC,MAAMK,CAAC;EAACC,WAAWA,CAACN,CAAC,EAAC;IAAC,IAAI,CAACO,IAAI,GAACP,CAAC,EAAC,IAAI,CAACO,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC,EAAC,IAAI,CAACC,OAAO,GAAC,IAAIP,CAAC,CAAC,IAAI,CAACK,IAAI,EAAC;MAACG,KAAK,EAAC,CAAC;IAAC,CAAC,CAAC;EAAA;EAAOC,aAAaA,CAAA,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAAC,MAAMD,KAAI,CAACL,IAAI,CAACO,cAAc,EAACF,KAAI,CAACG,QAAQ,GAACf,CAAC,CAACY,KAAI,CAACL,IAAI,EAAC,QAAQ,EAAE,MAAIK,KAAI,CAACI,aAAa,CAAC,CAACJ,KAAI,CAACL,IAAI,CAACU,MAAM,CAAE,CAAC;IAAA;EAAA;EAAOC,UAAUA,CAAA,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAN,iBAAA;MAAC,MAAMM,MAAI,CAACZ,IAAI,CAACO,cAAc,EAACK,MAAI,CAACH,aAAa,CAAC,CAACG,MAAI,CAACZ,IAAI,CAACU,MAAM,CAAC;IAAA;EAAA;EAACG,gBAAgBA,CAAA,EAAE;IAAC,IAAI,CAACJ,aAAa,CAAC,CAAC,CAAC,CAAC,EAAC,IAAI,CAACD,QAAQ,EAAEM,UAAU,CAAC,CAAC;EAAA;EAACL,aAAaA,CAAChB,CAAC,EAAC;IAAC,IAAI,CAACS,OAAO,CAACa,OAAO,EAAEC,YAAY,CAAC,eAAe,CAAC,KAAG,IAAI,CAACd,OAAO,CAACa,OAAO,CAACE,YAAY,GAAC,EAAE,GAACxB,CAAC,CAAC,EAAC,IAAI,CAACS,OAAO,CAACgB,IAAI,EAAEF,YAAY,CAAC,eAAe,CAAC,IAAE,IAAI,CAACd,OAAO,CAACgB,IAAI,KAAG,IAAI,CAAChB,OAAO,CAACa,OAAO,KAAG,IAAI,CAACb,OAAO,CAACgB,IAAI,CAACD,YAAY,GAAC,OAAO,CAAC;EAAA;AAAC;AAAC,SAAOnB,CAAC,IAAIqB,mBAAmB,EAACvB,CAAC,IAAIwB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}