{"ast": null, "code": "var XWrap = /*#__PURE__*/\nfunction () {\n  function XWrap(fn) {\n    this.f = fn;\n  }\n  XWrap.prototype['@@transducer/init'] = function () {\n    throw new Error('init not implemented on XWrap');\n  };\n  XWrap.prototype['@@transducer/result'] = function (acc) {\n    return acc;\n  };\n  XWrap.prototype['@@transducer/step'] = function (acc, x) {\n    return this.f(acc, x);\n  };\n  return XWrap;\n}();\nexport default function _xwrap(fn) {\n  return new XWrap(fn);\n}", "map": {"version": 3, "names": ["XWrap", "fn", "f", "prototype", "Error", "acc", "x", "_xwrap"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_xwrap.js"], "sourcesContent": ["var XWrap =\n/*#__PURE__*/\nfunction () {\n  function XWrap(fn) {\n    this.f = fn;\n  }\n\n  XWrap.prototype['@@transducer/init'] = function () {\n    throw new Error('init not implemented on XWrap');\n  };\n\n  XWrap.prototype['@@transducer/result'] = function (acc) {\n    return acc;\n  };\n\n  XWrap.prototype['@@transducer/step'] = function (acc, x) {\n    return this.f(acc, x);\n  };\n\n  return XWrap;\n}();\n\nexport default function _xwrap(fn) {\n  return new XWrap(fn);\n}"], "mappings": "AAAA,IAAIA,KAAK,GACT;AACA,YAAY;EACV,SAASA,KAAKA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACC,CAAC,GAAGD,EAAE;EACb;EAEAD,KAAK,CAACG,SAAS,CAAC,mBAAmB,CAAC,GAAG,YAAY;IACjD,MAAM,IAAIC,KAAK,CAAC,+BAA+B,CAAC;EAClD,CAAC;EAEDJ,KAAK,CAACG,SAAS,CAAC,qBAAqB,CAAC,GAAG,UAAUE,GAAG,EAAE;IACtD,OAAOA,GAAG;EACZ,CAAC;EAEDL,KAAK,CAACG,SAAS,CAAC,mBAAmB,CAAC,GAAG,UAAUE,GAAG,EAAEC,CAAC,EAAE;IACvD,OAAO,IAAI,CAACJ,CAAC,CAACG,GAAG,EAAEC,CAAC,CAAC;EACvB,CAAC;EAED,OAAON,KAAK;AACd,CAAC,CAAC,CAAC;AAEH,eAAe,SAASO,MAAMA,CAACN,EAAE,EAAE;EACjC,OAAO,IAAID,KAAK,CAACC,EAAE,CAAC;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}