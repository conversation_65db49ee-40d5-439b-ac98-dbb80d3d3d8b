{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst V = \"unlock\",\n  H = [\"unlock\", C({\n    outline: '<path d=\"M13 23.72V28H15V23.72C15.6 23.37 16 22.74 16 22C16 20.9 15.1 20 14 20C12.9 20 12 20.9 12 22C12 22.74 12.4 23.38 13 23.72ZM26 2C21.59 2 18 5.59 18 10V14H2V30.9C2 32.61 3.39 34 5.1 34H22.9C24.61 34 26 32.61 26 30.9V14H20V10C20 6.69 22.69 4 26 4C29.31 4 32 6.69 32 10V16H34V10C34 5.59 30.41 2 26 2ZM24 16V30.9C24 31.51 23.51 32 22.9 32H5.1C4.49 32 4 31.51 4 30.9V16H24Z\"/>',\n    solid: '<path d=\"M26 2C21.59 2 18 5.59 18 10V14H2V30.9C2 32.61 3.39 34 5.1 34H22.9C24.61 34 26 32.61 26 30.9V14H20V10C20 6.69 22.69 4 26 4C29.31 4 32 6.69 32 10V16H34V10C34 5.59 30.41 2 26 2ZM15.2 23.84V28.2H12.8V23.84C12.2 23.45 11.8 22.77 11.8 22C11.8 20.79 12.79 19.8 14 19.8C15.21 19.8 16.2 20.79 16.2 22C16.2 22.77 15.8 23.45 15.2 23.84Z\"/>'\n  })];\nexport { H as unlockIcon, V as unlockIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "V", "H", "outline", "solid", "unlockIcon", "unlockIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/unlock.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const V=\"unlock\",H=[\"unlock\",C({outline:'<path d=\"M13 23.72V28H15V23.72C15.6 23.37 16 22.74 16 22C16 20.9 15.1 20 14 20C12.9 20 12 20.9 12 22C12 22.74 12.4 23.38 13 23.72ZM26 2C21.59 2 18 5.59 18 10V14H2V30.9C2 32.61 3.39 34 5.1 34H22.9C24.61 34 26 32.61 26 30.9V14H20V10C20 6.69 22.69 4 26 4C29.31 4 32 6.69 32 10V16H34V10C34 5.59 30.41 2 26 2ZM24 16V30.9C24 31.51 23.51 32 22.9 32H5.1C4.49 32 4 31.51 4 30.9V16H24Z\"/>',solid:'<path d=\"M26 2C21.59 2 18 5.59 18 10V14H2V30.9C2 32.61 3.39 34 5.1 34H22.9C24.61 34 26 32.61 26 30.9V14H20V10C20 6.69 22.69 4 26 4C29.31 4 32 6.69 32 10V16H34V10C34 5.59 30.41 2 26 2ZM15.2 23.84V28.2H12.8V23.84C12.2 23.45 11.8 22.77 11.8 22C11.8 20.79 12.79 19.8 14 19.8C15.21 19.8 16.2 20.79 16.2 22C16.2 22.77 15.8 23.45 15.2 23.84Z\"/>'})];export{H as unlockIcon,V as unlockIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,4XAA4X;IAACC,KAAK,EAAC;EAAmV,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,UAAU,EAACJ,CAAC,IAAIK,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}