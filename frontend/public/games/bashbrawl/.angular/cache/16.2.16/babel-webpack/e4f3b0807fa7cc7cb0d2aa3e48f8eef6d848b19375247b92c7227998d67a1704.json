{"ast": null, "code": "export const environment = {\n  production: false,\n  server: '',\n  imprint: '',\n  privacypolicy: ''\n};", "map": {"version": 3, "names": ["environment", "production", "server", "imprint", "privacypolicy"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/src/environments/environment.local.ts"], "sourcesContent": ["export const environment = {\n  production: false,\n  server: '', // 禁用服务器连接，使用本地存储\n  imprint: '',\n  privacypolicy: '',\n};\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,EAAE;EACVC,OAAO,EAAE,EAAE;EACXC,aAAa,EAAE;CAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}