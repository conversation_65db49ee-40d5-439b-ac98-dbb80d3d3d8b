{"ast": null, "code": "import { __decorate as t } from \"tslib\";\nimport { baseStyles as s, hasStringPropertyChangedAndNotNil as e, hasStringPropertyChanged as i, pxToRem as r, GlobalStateService as o, isString as p, property as n, state as h } from \"@cds/core/internal\";\nimport { LitElement as a, html as d, svg as c } from \"lit\";\nimport { query as l } from \"lit/decorators/query.js\";\nimport y from \"./icon.element.scss.js\";\nimport { ClarityIcons as u } from \"./icon.service.js\";\nimport { updateIconSizeStyle as g } from \"./utils/icon.classnames.js\";\nimport { getIconSVG as f, getIconBadgeSVG as m } from \"./utils/icon.svg-helpers.js\";\nclass v extends a {\n  constructor() {\n    super(...arguments);\n    this._shape = \"unknown\", this.solid = !1, this.inverse = !1;\n  }\n  static get styles() {\n    return [s, y];\n  }\n  get shape() {\n    return this._shape;\n  }\n  set shape(t) {\n    if (e(t, this._shape)) {\n      const s = this._shape;\n      this._shape = t, this.requestUpdate(\"shape\", s);\n    }\n  }\n  get size() {\n    return this._size;\n  }\n  set size(t) {\n    if (i(t, this._size)) {\n      const s = this._size;\n      this._size = t, g(this, t), this.requestUpdate(\"size\", s);\n    }\n  }\n  updated(t) {\n    if (t.has(\"innerOffset\") && this.innerOffset > 0) {\n      const t = r(-1 * this.innerOffset),\n        s = `calc(100% + ${r(2 * this.innerOffset)})`;\n      this.svg.style.width = s, this.svg.style.height = s, this.svg.style.margin = `${t} 0 0 ${t}`;\n    }\n  }\n  firstUpdated(t) {\n    if (super.firstUpdated(t), this.isConnected) {\n      let t = \"unknown\";\n      this.subscription = o.stateUpdates.subscribe(s => {\n        \"iconRegistry\" === s.key && u.registry[this.shape] && t !== this.shape && (t = this.shape, this.requestUpdate(\"shape\"));\n      });\n    }\n  }\n  disconnectedCallback() {\n    super.disconnectedCallback(), this.subscription?.unsubscribe();\n  }\n  render() {\n    return p(u.registry[this.shape]) ? d`<span .innerHTML=\"${u.registry[this.shape]}\"></span>` : c`<svg .innerHTML=\"${f(this) + m(this)}\" viewBox=\"0 0 36 36\" xmlns=\"http://www.w3.org/2000/svg\" aria-hidden=\"true\"></svg>`;\n  }\n}\nt([n({\n  type: String\n})], v.prototype, \"shape\", null), t([n({\n  type: String\n})], v.prototype, \"size\", null), t([n({\n  type: String\n})], v.prototype, \"direction\", void 0), t([n({\n  type: String\n})], v.prototype, \"flip\", void 0), t([n({\n  type: Boolean\n})], v.prototype, \"solid\", void 0), t([n({\n  type: String\n})], v.prototype, \"status\", void 0), t([n({\n  type: Boolean\n})], v.prototype, \"inverse\", void 0), t([n({\n  type: String\n})], v.prototype, \"badge\", void 0), t([h({\n  type: Number\n})], v.prototype, \"innerOffset\", void 0), t([l(\"svg\")], v.prototype, \"svg\", void 0);\nexport { v as CdsIcon };", "map": {"version": 3, "names": ["__decorate", "t", "baseStyles", "s", "hasStringPropertyChangedAndNotNil", "e", "hasStringPropertyChanged", "i", "pxToRem", "r", "GlobalStateService", "o", "isString", "p", "property", "n", "state", "h", "LitElement", "a", "html", "d", "svg", "c", "query", "l", "y", "ClarityIcons", "u", "updateIconSizeStyle", "g", "getIconSVG", "f", "getIconBadgeSVG", "m", "v", "constructor", "arguments", "_shape", "solid", "inverse", "styles", "shape", "requestUpdate", "size", "_size", "updated", "has", "innerOffset", "style", "width", "height", "margin", "firstUpdated", "isConnected", "subscription", "stateUpdates", "subscribe", "key", "registry", "disconnectedCallback", "unsubscribe", "render", "type", "String", "prototype", "Boolean", "Number", "CdsIcon"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/icon.element.js"], "sourcesContent": ["import{__decorate as t}from\"tslib\";import{baseStyles as s,hasStringPropertyChangedAndNotNil as e,hasStringPropertyChanged as i,pxToRem as r,GlobalStateService as o,isString as p,property as n,state as h}from\"@cds/core/internal\";import{LitElement as a,html as d,svg as c}from\"lit\";import{query as l}from\"lit/decorators/query.js\";import y from\"./icon.element.scss.js\";import{ClarityIcons as u}from\"./icon.service.js\";import{updateIconSizeStyle as g}from\"./utils/icon.classnames.js\";import{getIconSVG as f,getIconBadgeSVG as m}from\"./utils/icon.svg-helpers.js\";class v extends a{constructor(){super(...arguments);this._shape=\"unknown\",this.solid=!1,this.inverse=!1}static get styles(){return[s,y]}get shape(){return this._shape}set shape(t){if(e(t,this._shape)){const s=this._shape;this._shape=t,this.requestUpdate(\"shape\",s)}}get size(){return this._size}set size(t){if(i(t,this._size)){const s=this._size;this._size=t,g(this,t),this.requestUpdate(\"size\",s)}}updated(t){if(t.has(\"innerOffset\")&&this.innerOffset>0){const t=r(-1*this.innerOffset),s=`calc(100% + ${r(2*this.innerOffset)})`;this.svg.style.width=s,this.svg.style.height=s,this.svg.style.margin=`${t} 0 0 ${t}`}}firstUpdated(t){if(super.firstUpdated(t),this.isConnected){let t=\"unknown\";this.subscription=o.stateUpdates.subscribe((s=>{\"iconRegistry\"===s.key&&u.registry[this.shape]&&t!==this.shape&&(t=this.shape,this.requestUpdate(\"shape\"))}))}}disconnectedCallback(){super.disconnectedCallback(),this.subscription?.unsubscribe()}render(){return p(u.registry[this.shape])?d`<span .innerHTML=\"${u.registry[this.shape]}\"></span>`:c`<svg .innerHTML=\"${f(this)+m(this)}\" viewBox=\"0 0 36 36\" xmlns=\"http://www.w3.org/2000/svg\" aria-hidden=\"true\"></svg>`}}t([n({type:String})],v.prototype,\"shape\",null),t([n({type:String})],v.prototype,\"size\",null),t([n({type:String})],v.prototype,\"direction\",void 0),t([n({type:String})],v.prototype,\"flip\",void 0),t([n({type:Boolean})],v.prototype,\"solid\",void 0),t([n({type:String})],v.prototype,\"status\",void 0),t([n({type:Boolean})],v.prototype,\"inverse\",void 0),t([n({type:String})],v.prototype,\"badge\",void 0),t([h({type:Number})],v.prototype,\"innerOffset\",void 0),t([l(\"svg\")],v.prototype,\"svg\",void 0);export{v as CdsIcon};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,UAAU,IAAIC,CAAC,EAACC,iCAAiC,IAAIC,CAAC,EAACC,wBAAwB,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,EAACC,kBAAkB,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,EAACC,KAAK,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,UAAU,IAAIC,CAAC,EAACC,IAAI,IAAIC,CAAC,EAACC,GAAG,IAAIC,CAAC,QAAK,KAAK;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,yBAAyB;AAAC,OAAOC,CAAC,MAAK,wBAAwB;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,UAAU,IAAIC,CAAC,EAACC,eAAe,IAAIC,CAAC,QAAK,6BAA6B;AAAC,MAAMC,CAAC,SAAShB,CAAC;EAACiB,WAAWA,CAAA,EAAE;IAAC,KAAK,CAAC,GAAGC,SAAS,CAAC;IAAC,IAAI,CAACC,MAAM,GAAC,SAAS,EAAC,IAAI,CAACC,KAAK,GAAC,CAAC,CAAC,EAAC,IAAI,CAACC,OAAO,GAAC,CAAC,CAAC;EAAA;EAAC,WAAWC,MAAMA,CAAA,EAAE;IAAC,OAAM,CAACtC,CAAC,EAACuB,CAAC,CAAC;EAAA;EAAC,IAAIgB,KAAKA,CAAA,EAAE;IAAC,OAAO,IAAI,CAACJ,MAAM;EAAA;EAAC,IAAII,KAAKA,CAACzC,CAAC,EAAC;IAAC,IAAGI,CAAC,CAACJ,CAAC,EAAC,IAAI,CAACqC,MAAM,CAAC,EAAC;MAAC,MAAMnC,CAAC,GAAC,IAAI,CAACmC,MAAM;MAAC,IAAI,CAACA,MAAM,GAACrC,CAAC,EAAC,IAAI,CAAC0C,aAAa,CAAC,OAAO,EAACxC,CAAC,CAAC;IAAA;EAAC;EAAC,IAAIyC,IAAIA,CAAA,EAAE;IAAC,OAAO,IAAI,CAACC,KAAK;EAAA;EAAC,IAAID,IAAIA,CAAC3C,CAAC,EAAC;IAAC,IAAGM,CAAC,CAACN,CAAC,EAAC,IAAI,CAAC4C,KAAK,CAAC,EAAC;MAAC,MAAM1C,CAAC,GAAC,IAAI,CAAC0C,KAAK;MAAC,IAAI,CAACA,KAAK,GAAC5C,CAAC,EAAC6B,CAAC,CAAC,IAAI,EAAC7B,CAAC,CAAC,EAAC,IAAI,CAAC0C,aAAa,CAAC,MAAM,EAACxC,CAAC,CAAC;IAAA;EAAC;EAAC2C,OAAOA,CAAC7C,CAAC,EAAC;IAAC,IAAGA,CAAC,CAAC8C,GAAG,CAAC,aAAa,CAAC,IAAE,IAAI,CAACC,WAAW,GAAC,CAAC,EAAC;MAAC,MAAM/C,CAAC,GAACQ,CAAC,CAAC,CAAC,CAAC,GAAC,IAAI,CAACuC,WAAW,CAAC;QAAC7C,CAAC,GAAE,eAAcM,CAAC,CAAC,CAAC,GAAC,IAAI,CAACuC,WAAW,CAAE,GAAE;MAAC,IAAI,CAAC1B,GAAG,CAAC2B,KAAK,CAACC,KAAK,GAAC/C,CAAC,EAAC,IAAI,CAACmB,GAAG,CAAC2B,KAAK,CAACE,MAAM,GAAChD,CAAC,EAAC,IAAI,CAACmB,GAAG,CAAC2B,KAAK,CAACG,MAAM,GAAE,GAAEnD,CAAE,QAAOA,CAAE,EAAC;IAAA;EAAC;EAACoD,YAAYA,CAACpD,CAAC,EAAC;IAAC,IAAG,KAAK,CAACoD,YAAY,CAACpD,CAAC,CAAC,EAAC,IAAI,CAACqD,WAAW,EAAC;MAAC,IAAIrD,CAAC,GAAC,SAAS;MAAC,IAAI,CAACsD,YAAY,GAAC5C,CAAC,CAAC6C,YAAY,CAACC,SAAS,CAAEtD,CAAC,IAAE;QAAC,cAAc,KAAGA,CAAC,CAACuD,GAAG,IAAE9B,CAAC,CAAC+B,QAAQ,CAAC,IAAI,CAACjB,KAAK,CAAC,IAAEzC,CAAC,KAAG,IAAI,CAACyC,KAAK,KAAGzC,CAAC,GAAC,IAAI,CAACyC,KAAK,EAAC,IAAI,CAACC,aAAa,CAAC,OAAO,CAAC,CAAC;MAAA,CAAE,CAAC;IAAA;EAAC;EAACiB,oBAAoBA,CAAA,EAAE;IAAC,KAAK,CAACA,oBAAoB,CAAC,CAAC,EAAC,IAAI,CAACL,YAAY,EAAEM,WAAW,CAAC,CAAC;EAAA;EAACC,MAAMA,CAAA,EAAE;IAAC,OAAOjD,CAAC,CAACe,CAAC,CAAC+B,QAAQ,CAAC,IAAI,CAACjB,KAAK,CAAC,CAAC,GAACrB,CAAE,qBAAoBO,CAAC,CAAC+B,QAAQ,CAAC,IAAI,CAACjB,KAAK,CAAE,WAAU,GAACnB,CAAE,oBAAmBS,CAAC,CAAC,IAAI,CAAC,GAACE,CAAC,CAAC,IAAI,CAAE,oFAAmF;EAAA;AAAC;AAACjC,CAAC,CAAC,CAACc,CAAC,CAAC;EAACgD,IAAI,EAACC;AAAM,CAAC,CAAC,CAAC,EAAC7B,CAAC,CAAC8B,SAAS,EAAC,OAAO,EAAC,IAAI,CAAC,EAAChE,CAAC,CAAC,CAACc,CAAC,CAAC;EAACgD,IAAI,EAACC;AAAM,CAAC,CAAC,CAAC,EAAC7B,CAAC,CAAC8B,SAAS,EAAC,MAAM,EAAC,IAAI,CAAC,EAAChE,CAAC,CAAC,CAACc,CAAC,CAAC;EAACgD,IAAI,EAACC;AAAM,CAAC,CAAC,CAAC,EAAC7B,CAAC,CAAC8B,SAAS,EAAC,WAAW,EAAC,KAAK,CAAC,CAAC,EAAChE,CAAC,CAAC,CAACc,CAAC,CAAC;EAACgD,IAAI,EAACC;AAAM,CAAC,CAAC,CAAC,EAAC7B,CAAC,CAAC8B,SAAS,EAAC,MAAM,EAAC,KAAK,CAAC,CAAC,EAAChE,CAAC,CAAC,CAACc,CAAC,CAAC;EAACgD,IAAI,EAACG;AAAO,CAAC,CAAC,CAAC,EAAC/B,CAAC,CAAC8B,SAAS,EAAC,OAAO,EAAC,KAAK,CAAC,CAAC,EAAChE,CAAC,CAAC,CAACc,CAAC,CAAC;EAACgD,IAAI,EAACC;AAAM,CAAC,CAAC,CAAC,EAAC7B,CAAC,CAAC8B,SAAS,EAAC,QAAQ,EAAC,KAAK,CAAC,CAAC,EAAChE,CAAC,CAAC,CAACc,CAAC,CAAC;EAACgD,IAAI,EAACG;AAAO,CAAC,CAAC,CAAC,EAAC/B,CAAC,CAAC8B,SAAS,EAAC,SAAS,EAAC,KAAK,CAAC,CAAC,EAAChE,CAAC,CAAC,CAACc,CAAC,CAAC;EAACgD,IAAI,EAACC;AAAM,CAAC,CAAC,CAAC,EAAC7B,CAAC,CAAC8B,SAAS,EAAC,OAAO,EAAC,KAAK,CAAC,CAAC,EAAChE,CAAC,CAAC,CAACgB,CAAC,CAAC;EAAC8C,IAAI,EAACI;AAAM,CAAC,CAAC,CAAC,EAAChC,CAAC,CAAC8B,SAAS,EAAC,aAAa,EAAC,KAAK,CAAC,CAAC,EAAChE,CAAC,CAAC,CAACwB,CAAC,CAAC,KAAK,CAAC,CAAC,EAACU,CAAC,CAAC8B,SAAS,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC;AAAC,SAAO9B,CAAC,IAAIiC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}