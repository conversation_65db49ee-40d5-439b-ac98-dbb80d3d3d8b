{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst t = \"filter-2\",\n  r = [\"filter-2\", C({\n    outline: '<path d=\"M33 11H3C2.44772 11 2 11.4477 2 12C2 12.5523 2.44772 13 3 13H33C33.5523 13 34 12.5523 34 12C34 11.4477 33.5523 11 33 11Z\"/><path d=\"M28 17H8C7.44772 17 7 17.4477 7 18C7 18.5523 7.44772 19 8 19H28C28.5523 19 29 18.5523 29 18C29 17.4477 28.5523 17 28 17Z\"/><path d=\"M13 23H23C23.5523 23 24 23.4477 24 24C24 24.5523 23.5523 25 23 25H13C12.4477 25 12 24.5523 12 24C12 23.4477 12.4477 23 13 23Z\"/>'\n  })];\nexport { r as filter2Icon, t as filter2IconName };", "map": {"version": 3, "names": ["renderIcon", "C", "t", "r", "outline", "filter2Icon", "filter2IconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/filter-2.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const t=\"filter-2\",r=[\"filter-2\",C({outline:'<path d=\"M33 11H3C2.44772 11 2 11.4477 2 12C2 12.5523 2.44772 13 3 13H33C33.5523 13 34 12.5523 34 12C34 11.4477 33.5523 11 33 11Z\"/><path d=\"M28 17H8C7.44772 17 7 17.4477 7 18C7 18.5523 7.44772 19 8 19H28C28.5523 19 29 18.5523 29 18C29 17.4477 28.5523 17 28 17Z\"/><path d=\"M13 23H23C23.5523 23 24 23.4477 24 24C24 24.5523 23.5523 25 23 25H13C12.4477 25 12 24.5523 12 24C12 23.4477 12.4477 23 13 23Z\"/>'})];export{r as filter2Icon,t as filter2IconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,UAAU;EAACC,CAAC,GAAC,CAAC,UAAU,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAmZ,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,WAAW,EAACH,CAAC,IAAII,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}