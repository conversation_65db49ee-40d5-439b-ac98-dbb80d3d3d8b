{"ast": null, "code": "import \"@cds/core/icon/register.js\";\nimport { registerElementSafely as o } from \"@cds/core/internal\";\nimport { ClarityIcons as s } from \"@cds/core/icon/icon.service.js\";\nimport { timesIcon as e } from \"@cds/core/icon/shapes/times.js\";\nimport { CdsCloseButtonTagName as r, CdsInternalCloseButton as c } from \"./close-button.element.js\";\no(r, c), s.addIcons(e), s.addAliases([\"times\", [\"close\"]]);", "map": {"version": 3, "names": ["registerElementSafely", "o", "ClarityIcons", "s", "timesIcon", "e", "CdsCloseButtonTagName", "r", "CdsInternalCloseButton", "c", "addIcons", "addAliases"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal-components/close-button/register.js"], "sourcesContent": ["import\"@cds/core/icon/register.js\";import{registerElementSafely as o}from\"@cds/core/internal\";import{ClarityIcons as s}from\"@cds/core/icon/icon.service.js\";import{timesIcon as e}from\"@cds/core/icon/shapes/times.js\";import{CdsCloseButtonTagName as r,CdsInternalCloseButton as c}from\"./close-button.element.js\";o(r,c),s.addIcons(e),s.addAliases([\"times\",[\"close\"]]);\n"], "mappings": "AAAA,OAAM,4BAA4B;AAAC,SAAOA,qBAAqB,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,gCAAgC;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,gCAAgC;AAAC,SAAOC,qBAAqB,IAAIC,CAAC,EAACC,sBAAsB,IAAIC,CAAC,QAAK,2BAA2B;AAACR,CAAC,CAACM,CAAC,EAACE,CAAC,CAAC,EAACN,CAAC,CAACO,QAAQ,CAACL,CAAC,CAAC,EAACF,CAAC,CAACQ,UAAU,CAAC,CAAC,OAAO,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}