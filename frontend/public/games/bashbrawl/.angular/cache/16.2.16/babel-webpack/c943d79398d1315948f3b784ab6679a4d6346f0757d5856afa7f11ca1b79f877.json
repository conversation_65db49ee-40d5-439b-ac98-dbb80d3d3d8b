{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"bicycle\",\n  L = [\"bicycle\", C({\n    outline: '<path d=\"M27.9297 16C27.5398 16 27.1599 16.04 26.7801 16.1L23.891 6.71C23.761 6.29 23.3711 6 22.9313 6H19.9322C19.3824 6 18.9325 6.45 18.9325 7C18.9325 7.55 19.3824 8 19.9322 8H22.1915L23.4211 12H13.5442L12.8244 10.55C12.6545 10.21 12.3046 10 11.9347 10H7.93594C7.38611 10 6.93625 10.45 6.93625 11C6.93625 11.55 7.38611 12 7.93594 12H11.3149L11.8647 13.09L10.965 16.08C10.6251 16.03 10.2852 16 9.93531 16C6.07652 16 2.9375 19.14 2.9375 23C2.9375 26.86 6.07652 30 9.93531 30C13.4542 30 16.3633 27.39 16.8532 24H18.9325V22H16.8532C16.5133 19.63 14.9837 17.64 12.8844 16.66L13.6841 14H24.0509L24.8906 16.71C22.5614 17.85 20.9419 20.24 20.9419 23C20.9419 26.86 24.0809 30 27.9397 30C31.7985 30 34.9375 26.86 34.9375 23C34.9375 19.14 31.7985 16 27.9397 16H27.9297ZM9.93531 28C7.17618 28 4.93688 25.76 4.93688 23C4.93688 20.24 7.17618 18 9.93531 18C10.0853 18 10.2252 18.03 10.3752 18.04L8.97561 22.71C8.88564 23.01 8.94562 23.34 9.13556 23.59C9.3255 23.84 9.62541 23.99 9.93531 23.99H14.8338C14.3739 26.27 12.3546 27.99 9.93531 27.99V28ZM14.8338 22H11.2749L12.2846 18.62C13.5642 19.31 14.5239 20.53 14.8238 22H14.8338ZM27.9297 28C25.1706 28 22.9313 25.76 22.9313 23C22.9313 21.14 23.9709 19.53 25.4805 18.67L26.98 23.52C27.1099 23.95 27.5098 24.23 27.9397 24.23C28.0397 24.23 28.1396 24.22 28.2296 24.19C28.7594 24.03 29.0493 23.47 28.8894 22.94L27.3899 18.06C27.5698 18.04 27.7497 18 27.9397 18C30.6988 18 32.9381 20.24 32.9381 23C32.9381 25.76 30.6988 28 27.9397 28H27.9297Z\"/>',\n    solid: '<path d=\"M16.8575 23.9925H18.9375V21.9933H16.8575C16.9075 22.3232 16.9375 22.6531 16.9375 22.9929C16.9375 23.3328 16.9075 23.6626 16.8575 23.9925ZM9.1375 23.5927C8.9475 23.3428 8.8875 23.0129 8.9775 22.713L10.9675 16.0858C10.6275 16.0358 10.2875 16.0058 9.9375 16.0058C6.0775 16.0058 2.9375 19.1445 2.9375 23.0029C2.9375 26.8613 6.0775 30 9.9375 30C13.4575 30 16.3675 27.3911 16.8575 24.0025H9.9375C9.6175 24.0025 9.3275 23.8526 9.1375 23.6027V23.5927ZM23.8975 6.7097C23.7675 6.28988 23.3775 6 22.9375 6H19.9375C19.3875 6 18.9375 6.44981 18.9375 6.99958C18.9375 7.54935 19.3875 7.99917 19.9375 7.99917H22.1975L23.4275 11.9975H13.5475L12.8275 10.5481C12.6575 10.2082 12.3075 9.99833 11.9375 9.99833H7.9375C7.3875 9.99833 6.9375 10.4481 6.9375 10.9979C6.9375 11.5477 7.3875 11.9975 7.9375 11.9975H11.3175L11.8675 13.087L10.9675 16.0758C11.6475 16.1758 12.2875 16.3757 12.8875 16.6456L13.6875 13.9867H24.0575L24.8975 16.6955C25.4875 16.4057 26.1275 16.1958 26.8075 16.0858L23.8975 6.7097ZM16.8575 21.9933C16.5175 19.6243 14.9875 17.6352 12.8875 16.6556L11.2875 21.9933H16.8675H16.8575ZM27.9375 15.9958C27.5475 15.9958 27.1675 16.0358 26.7875 16.0958L28.8975 22.9229C29.0575 23.4527 28.7675 24.0125 28.2375 24.1724C28.1375 24.2024 28.0375 24.2124 27.9475 24.2124C27.5175 24.2124 27.1275 23.9325 26.9875 23.5027L24.8875 16.6955C22.5575 17.8351 20.9375 20.2241 20.9375 22.9829C20.9375 26.8413 24.0775 29.98 27.9375 29.98C31.7975 29.98 34.9375 26.8413 34.9375 22.9829C34.9375 19.1245 31.7975 15.9858 27.9375 15.9858V15.9958Z\"/>'\n  })];\nexport { L as bicycleIcon, H as bicycleIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "L", "outline", "solid", "bicycleIcon", "bicycleIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/bicycle.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"bicycle\",L=[\"bicycle\",C({outline:'<path d=\"M27.9297 16C27.5398 16 27.1599 16.04 26.7801 16.1L23.891 6.71C23.761 6.29 23.3711 6 22.9313 6H19.9322C19.3824 6 18.9325 6.45 18.9325 7C18.9325 7.55 19.3824 8 19.9322 8H22.1915L23.4211 12H13.5442L12.8244 10.55C12.6545 10.21 12.3046 10 11.9347 10H7.93594C7.38611 10 6.93625 10.45 6.93625 11C6.93625 11.55 7.38611 12 7.93594 12H11.3149L11.8647 13.09L10.965 16.08C10.6251 16.03 10.2852 16 9.93531 16C6.07652 16 2.9375 19.14 2.9375 23C2.9375 26.86 6.07652 30 9.93531 30C13.4542 30 16.3633 27.39 16.8532 24H18.9325V22H16.8532C16.5133 19.63 14.9837 17.64 12.8844 16.66L13.6841 14H24.0509L24.8906 16.71C22.5614 17.85 20.9419 20.24 20.9419 23C20.9419 26.86 24.0809 30 27.9397 30C31.7985 30 34.9375 26.86 34.9375 23C34.9375 19.14 31.7985 16 27.9397 16H27.9297ZM9.93531 28C7.17618 28 4.93688 25.76 4.93688 23C4.93688 20.24 7.17618 18 9.93531 18C10.0853 18 10.2252 18.03 10.3752 18.04L8.97561 22.71C8.88564 23.01 8.94562 23.34 9.13556 23.59C9.3255 23.84 9.62541 23.99 9.93531 23.99H14.8338C14.3739 26.27 12.3546 27.99 9.93531 27.99V28ZM14.8338 22H11.2749L12.2846 18.62C13.5642 19.31 14.5239 20.53 14.8238 22H14.8338ZM27.9297 28C25.1706 28 22.9313 25.76 22.9313 23C22.9313 21.14 23.9709 19.53 25.4805 18.67L26.98 23.52C27.1099 23.95 27.5098 24.23 27.9397 24.23C28.0397 24.23 28.1396 24.22 28.2296 24.19C28.7594 24.03 29.0493 23.47 28.8894 22.94L27.3899 18.06C27.5698 18.04 27.7497 18 27.9397 18C30.6988 18 32.9381 20.24 32.9381 23C32.9381 25.76 30.6988 28 27.9397 28H27.9297Z\"/>',solid:'<path d=\"M16.8575 23.9925H18.9375V21.9933H16.8575C16.9075 22.3232 16.9375 22.6531 16.9375 22.9929C16.9375 23.3328 16.9075 23.6626 16.8575 23.9925ZM9.1375 23.5927C8.9475 23.3428 8.8875 23.0129 8.9775 22.713L10.9675 16.0858C10.6275 16.0358 10.2875 16.0058 9.9375 16.0058C6.0775 16.0058 2.9375 19.1445 2.9375 23.0029C2.9375 26.8613 6.0775 30 9.9375 30C13.4575 30 16.3675 27.3911 16.8575 24.0025H9.9375C9.6175 24.0025 9.3275 23.8526 9.1375 23.6027V23.5927ZM23.8975 6.7097C23.7675 6.28988 23.3775 6 22.9375 6H19.9375C19.3875 6 18.9375 6.44981 18.9375 6.99958C18.9375 7.54935 19.3875 7.99917 19.9375 7.99917H22.1975L23.4275 11.9975H13.5475L12.8275 10.5481C12.6575 10.2082 12.3075 9.99833 11.9375 9.99833H7.9375C7.3875 9.99833 6.9375 10.4481 6.9375 10.9979C6.9375 11.5477 7.3875 11.9975 7.9375 11.9975H11.3175L11.8675 13.087L10.9675 16.0758C11.6475 16.1758 12.2875 16.3757 12.8875 16.6456L13.6875 13.9867H24.0575L24.8975 16.6955C25.4875 16.4057 26.1275 16.1958 26.8075 16.0858L23.8975 6.7097ZM16.8575 21.9933C16.5175 19.6243 14.9875 17.6352 12.8875 16.6556L11.2875 21.9933H16.8675H16.8575ZM27.9375 15.9958C27.5475 15.9958 27.1675 16.0358 26.7875 16.0958L28.8975 22.9229C29.0575 23.4527 28.7675 24.0125 28.2375 24.1724C28.1375 24.2024 28.0375 24.2124 27.9475 24.2124C27.5175 24.2124 27.1275 23.9325 26.9875 23.5027L24.8875 16.6955C22.5575 17.8351 20.9375 20.2241 20.9375 22.9829C20.9375 26.8413 24.0775 29.98 27.9375 29.98C31.7975 29.98 34.9375 26.8413 34.9375 22.9829C34.9375 19.1245 31.7975 15.9858 27.9375 15.9858V15.9958Z\"/>'})];export{L as bicycleIcon,H as bicycleIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,SAAS;EAACC,CAAC,GAAC,CAAC,SAAS,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,o8CAAo8C;IAACC,KAAK,EAAC;EAAk/C,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,WAAW,EAACJ,CAAC,IAAIK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}