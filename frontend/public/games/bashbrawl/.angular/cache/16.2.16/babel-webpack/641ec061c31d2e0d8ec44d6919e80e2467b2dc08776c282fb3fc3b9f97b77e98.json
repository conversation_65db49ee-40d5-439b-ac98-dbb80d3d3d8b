{"ast": null, "code": "import { registerElementSafely as t } from \"../utils/registration.js\";\nimport { classLegacyDecorator as o, classStandardDecorator as i } from \"./utils.js\";\nconst r = r => s => \"function\" == typeof s ? o(r, s, (o, i) => t(o, i)) : i(r, s, (o, i) => t(o, i));\nexport { r as customElement };", "map": {"version": 3, "names": ["registerElementSafely", "t", "classLegacyDecorator", "o", "classStandardDecorator", "i", "r", "s", "customElement"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/decorators/element.js"], "sourcesContent": ["import{registerElementSafely as t}from\"../utils/registration.js\";import{classLegacyDecorator as o,classStandardDecorator as i}from\"./utils.js\";const r=r=>s=>\"function\"==typeof s?o(r,s,((o,i)=>t(o,i))):i(r,s,((o,i)=>t(o,i)));export{r as customElement};\n"], "mappings": "AAAA,SAAOA,qBAAqB,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,oBAAoB,IAAIC,CAAC,EAACC,sBAAsB,IAAIC,CAAC,QAAK,YAAY;AAAC,MAAMC,CAAC,GAACA,CAAC,IAAEC,CAAC,IAAE,UAAU,IAAE,OAAOA,CAAC,GAACJ,CAAC,CAACG,CAAC,EAACC,CAAC,EAAE,CAACJ,CAAC,EAACE,CAAC,KAAGJ,CAAC,CAACE,CAAC,EAACE,CAAC,CAAE,CAAC,GAACA,CAAC,CAACC,CAAC,EAACC,CAAC,EAAE,CAACJ,CAAC,EAACE,CAAC,KAAGJ,CAAC,CAACE,CAAC,EAACE,CAAC,CAAE,CAAC;AAAC,SAAOC,CAAC,IAAIE,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}