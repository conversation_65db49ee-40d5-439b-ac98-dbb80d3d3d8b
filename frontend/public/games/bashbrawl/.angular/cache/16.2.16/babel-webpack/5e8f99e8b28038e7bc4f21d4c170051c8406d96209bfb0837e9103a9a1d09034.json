{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst e = \"flame\",\n  L = [\"flame\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M31.3001 16.6573C30.1101 14.5677 23.3601 2.50992 23.3601 2.50992C23.1839 2.19167 22.8488 1.99414 22.4851 1.99414C22.1213 1.99414 21.7862 2.19167 21.6101 2.50992L15.6101 13.148L12.6101 7.86894C12.4339 7.55068 12.0988 7.35316 11.7351 7.35316C11.3713 7.35316 11.0362 7.55068 10.8601 7.86894C10.8601 7.86894 5.40006 17.7771 4.42006 19.4968C3.46077 21.0259 2.96737 22.8011 3.00006 24.6059C3.00006 29.7149 5.86006 33.9941 11.6701 33.9941H22.4801C28.2801 33.9941 33.0001 28.9951 33.0001 22.7762C33.0399 20.6148 32.4492 18.4886 31.3001 16.6573ZM22.4801 31.9945H11.7701C8.13006 31.9945 5.00006 28.6551 5.00006 24.6058C4.97226 23.1453 5.37575 21.7091 6.16006 20.4766C6.89006 19.1868 10.2101 13.2679 11.8101 10.4085L14.8101 15.6875C14.9868 16.0019 15.3193 16.1968 15.6801 16.1974C16.0408 16.1968 16.3733 16.0019 16.5501 15.6875L22.4901 4.99945C24.3501 8.32884 28.6401 15.9974 29.5601 17.5971C30.5429 19.1426 31.0441 20.9453 31.0001 22.7762C31.0001 27.8652 27.1801 31.9945 22.4801 31.9945ZM21.3701 13.9178C21.3701 13.9178 25.1001 20.5666 25.7501 21.7264L25.7301 21.7564C26.3925 22.8027 26.7397 24.0175 26.7301 25.2558C26.7316 26.588 26.3346 27.8902 25.5901 28.9951H23.4601C24.52 28.0489 25.1273 26.6965 25.1301 25.2757C25.1484 24.3161 24.8803 23.3728 24.3601 22.5662L20.6701 15.9975L17.0001 22.5163C16.8516 22.775 16.718 23.0421 16.6001 23.3161C16.4843 23.5893 16.2269 23.776 15.9312 23.8012C15.6356 23.8263 15.3503 23.6858 15.1901 23.4361L13.5301 20.8366L10.7301 25.2158C10.3547 25.7133 10.1575 26.3224 10.1701 26.9454C10.1881 27.6921 10.4841 28.4051 11.0001 28.9451H9.05006C8.73053 28.3263 8.55935 27.6417 8.55006 26.9454C8.53774 26.0135 8.8252 25.1022 9.37006 24.3459L12.8301 18.9069C12.9769 18.6761 13.2315 18.5364 13.5051 18.5364C13.7786 18.5364 14.0332 18.6761 14.1801 18.9069L15.7701 21.3965L19.9701 13.9178C20.111 13.6632 20.379 13.5052 20.6701 13.5052C20.9611 13.5052 21.2292 13.6632 21.3701 13.9178Z\"/>',\n    solid: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M23.3601 2.16466C23.3601 2.16466 30.1101 14.0957 31.3001 16.1634C32.4492 17.9753 33.0399 20.0792 33.0001 22.2179C33.0001 28.3318 28.2801 33.6543 22.4801 33.6543H11.6701C6.88006 33.5653 3.00006 29.0738 3.00006 24.0184C2.96737 22.2327 3.46077 20.4761 4.42006 18.9631C5.40006 17.2615 10.8601 7.46734 10.8601 7.46734C11.0362 7.15243 11.3713 6.95698 11.7351 6.95698C12.0988 6.95698 12.4339 7.15243 12.6101 7.46734L15.6101 12.6909L21.6101 2.16466C21.7862 1.84975 22.1213 1.6543 22.4851 1.6543C22.8488 1.6543 23.1839 1.84975 23.3601 2.16466ZM14.5401 31.6759H21.4801C22.1001 31.6759 27.0001 31.6759 27.0001 25.5026C27.004 24.3749 26.6737 23.2708 26.0501 22.3269C25.4201 21.2387 21.8201 14.9269 21.8201 14.9269C21.7313 14.7719 21.5651 14.6761 21.3851 14.6761C21.205 14.6761 21.0388 14.7719 20.9501 14.9269C20.9501 14.9269 17.3501 21.2288 16.7201 22.3269V22.4061L14.9401 19.6657C14.848 19.5246 14.6898 19.4394 14.5201 19.4394C14.3503 19.4394 14.1922 19.5246 14.1001 19.6657L10.7501 24.82C10.2551 25.4919 9.99209 26.3036 10.0001 27.135C10.047 29.6043 12.0447 31.6024 14.5401 31.6759Z\"/>'\n  })];\nexport { L as flameIcon, e as flameIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "e", "L", "outline", "solid", "flameIcon", "flameIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/flame.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const e=\"flame\",L=[\"flame\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M31.3001 16.6573C30.1101 14.5677 23.3601 2.50992 23.3601 2.50992C23.1839 2.19167 22.8488 1.99414 22.4851 1.99414C22.1213 1.99414 21.7862 2.19167 21.6101 2.50992L15.6101 13.148L12.6101 7.86894C12.4339 7.55068 12.0988 7.35316 11.7351 7.35316C11.3713 7.35316 11.0362 7.55068 10.8601 7.86894C10.8601 7.86894 5.40006 17.7771 4.42006 19.4968C3.46077 21.0259 2.96737 22.8011 3.00006 24.6059C3.00006 29.7149 5.86006 33.9941 11.6701 33.9941H22.4801C28.2801 33.9941 33.0001 28.9951 33.0001 22.7762C33.0399 20.6148 32.4492 18.4886 31.3001 16.6573ZM22.4801 31.9945H11.7701C8.13006 31.9945 5.00006 28.6551 5.00006 24.6058C4.97226 23.1453 5.37575 21.7091 6.16006 20.4766C6.89006 19.1868 10.2101 13.2679 11.8101 10.4085L14.8101 15.6875C14.9868 16.0019 15.3193 16.1968 15.6801 16.1974C16.0408 16.1968 16.3733 16.0019 16.5501 15.6875L22.4901 4.99945C24.3501 8.32884 28.6401 15.9974 29.5601 17.5971C30.5429 19.1426 31.0441 20.9453 31.0001 22.7762C31.0001 27.8652 27.1801 31.9945 22.4801 31.9945ZM21.3701 13.9178C21.3701 13.9178 25.1001 20.5666 25.7501 21.7264L25.7301 21.7564C26.3925 22.8027 26.7397 24.0175 26.7301 25.2558C26.7316 26.588 26.3346 27.8902 25.5901 28.9951H23.4601C24.52 28.0489 25.1273 26.6965 25.1301 25.2757C25.1484 24.3161 24.8803 23.3728 24.3601 22.5662L20.6701 15.9975L17.0001 22.5163C16.8516 22.775 16.718 23.0421 16.6001 23.3161C16.4843 23.5893 16.2269 23.776 15.9312 23.8012C15.6356 23.8263 15.3503 23.6858 15.1901 23.4361L13.5301 20.8366L10.7301 25.2158C10.3547 25.7133 10.1575 26.3224 10.1701 26.9454C10.1881 27.6921 10.4841 28.4051 11.0001 28.9451H9.05006C8.73053 28.3263 8.55935 27.6417 8.55006 26.9454C8.53774 26.0135 8.8252 25.1022 9.37006 24.3459L12.8301 18.9069C12.9769 18.6761 13.2315 18.5364 13.5051 18.5364C13.7786 18.5364 14.0332 18.6761 14.1801 18.9069L15.7701 21.3965L19.9701 13.9178C20.111 13.6632 20.379 13.5052 20.6701 13.5052C20.9611 13.5052 21.2292 13.6632 21.3701 13.9178Z\"/>',solid:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M23.3601 2.16466C23.3601 2.16466 30.1101 14.0957 31.3001 16.1634C32.4492 17.9753 33.0399 20.0792 33.0001 22.2179C33.0001 28.3318 28.2801 33.6543 22.4801 33.6543H11.6701C6.88006 33.5653 3.00006 29.0738 3.00006 24.0184C2.96737 22.2327 3.46077 20.4761 4.42006 18.9631C5.40006 17.2615 10.8601 7.46734 10.8601 7.46734C11.0362 7.15243 11.3713 6.95698 11.7351 6.95698C12.0988 6.95698 12.4339 7.15243 12.6101 7.46734L15.6101 12.6909L21.6101 2.16466C21.7862 1.84975 22.1213 1.6543 22.4851 1.6543C22.8488 1.6543 23.1839 1.84975 23.3601 2.16466ZM14.5401 31.6759H21.4801C22.1001 31.6759 27.0001 31.6759 27.0001 25.5026C27.004 24.3749 26.6737 23.2708 26.0501 22.3269C25.4201 21.2387 21.8201 14.9269 21.8201 14.9269C21.7313 14.7719 21.5651 14.6761 21.3851 14.6761C21.205 14.6761 21.0388 14.7719 20.9501 14.9269C20.9501 14.9269 17.3501 21.2288 16.7201 22.3269V22.4061L14.9401 19.6657C14.848 19.5246 14.6898 19.4394 14.5201 19.4394C14.3503 19.4394 14.1922 19.5246 14.1001 19.6657L10.7501 24.82C10.2551 25.4919 9.99209 26.3036 10.0001 27.135C10.047 29.6043 12.0447 31.6024 14.5401 31.6759Z\"/>'})];export{L as flameIcon,e as flameIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,OAAO;EAACC,CAAC,GAAC,CAAC,OAAO,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,+5DAA+5D;IAACC,KAAK,EAAC;EAAsmC,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,SAAS,EAACJ,CAAC,IAAIK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}