{"ast": null, "code": "/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nimport { nothing } from '../lit-html.js';\n/**\n * For AttributeParts, sets the attribute if the value is defined and removes\n * the attribute if the value is undefined.\n *\n * For other part types, this directive is a no-op.\n */\nexport const ifDefined = value => value !== null && value !== void 0 ? value : nothing;", "map": {"version": 3, "names": ["nothing", "ifDefined", "value"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/lit-html/development/directives/if-defined.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nimport { nothing } from '../lit-html.js';\n/**\n * For AttributeParts, sets the attribute if the value is defined and removes\n * the attribute if the value is undefined.\n *\n * For other part types, this directive is a no-op.\n */\nexport const ifDefined = (value) => value !== null && value !== void 0 ? value : nothing;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,gBAAgB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,SAAS,GAAIC,KAAK,IAAKA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}