{"ast": null, "code": "const o = \"cds-accordion-panel-open\",\n  n = [{\n    target: \".accordion-content\",\n    animation: [{\n      opacity: 0,\n      height: \"0\"\n    }, {\n      opacity: 1,\n      height: \"from:cds-accordion-content\"\n    }],\n    options: {\n      duration: \"--animation-duration\",\n      easing: \"--animation-easing\",\n      fill: \"forwards\"\n    }\n  }];\nexport { n as AnimationAccordionPanelOpenConfig, o as AnimationAccordionPanelOpenName };", "map": {"version": 3, "names": ["o", "n", "target", "animation", "opacity", "height", "options", "duration", "easing", "fill", "AnimationAccordionPanelOpenConfig", "AnimationAccordionPanelOpenName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/motion/animations/cds-accordion-panel-open.js"], "sourcesContent": ["const o=\"cds-accordion-panel-open\",n=[{target:\".accordion-content\",animation:[{opacity:0,height:\"0\"},{opacity:1,height:\"from:cds-accordion-content\"}],options:{duration:\"--animation-duration\",easing:\"--animation-easing\",fill:\"forwards\"}}];export{n as AnimationAccordionPanelOpenConfig,o as AnimationAccordionPanelOpenName};\n"], "mappings": "AAAA,MAAMA,CAAC,GAAC,0BAA0B;EAACC,CAAC,GAAC,CAAC;IAACC,MAAM,EAAC,oBAAoB;IAACC,SAAS,EAAC,CAAC;MAACC,OAAO,EAAC,CAAC;MAACC,MAAM,EAAC;IAAG,CAAC,EAAC;MAACD,OAAO,EAAC,CAAC;MAACC,MAAM,EAAC;IAA4B,CAAC,CAAC;IAACC,OAAO,EAAC;MAACC,QAAQ,EAAC,sBAAsB;MAACC,MAAM,EAAC,oBAAoB;MAACC,IAAI,EAAC;IAAU;EAAC,CAAC,CAAC;AAAC,SAAOR,CAAC,IAAIS,iCAAiC,EAACV,CAAC,IAAIW,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}