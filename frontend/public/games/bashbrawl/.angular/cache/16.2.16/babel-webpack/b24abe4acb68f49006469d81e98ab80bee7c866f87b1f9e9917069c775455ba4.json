{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst V = \"employee-group\",\n  H = [\"employee-group\", C({\n    outline: '<path d=\"M24.7788 13.09C24.5396 13.85 24.1807 14.54 23.7122 15.16C24.1308 15.12 24.5495 15.08 25.0081 15.08C28.557 15.08 30.9595 16.5 31.8368 17.12C31.9364 17.19 31.9863 17.28 31.9863 17.37V20H33.0031C33.3421 20 33.6611 20.07 33.9601 20.17C33.9601 20.14 33.9801 20.11 33.9801 20.08V17.36C33.9801 16.62 33.6012 15.92 32.9832 15.48C31.7072 14.58 28.9757 13.08 25.0081 13.08C24.9283 13.08 24.8486 13.08 24.7688 13.08L24.7788 13.09ZM13.1352 11C13.1352 13.76 15.3682 16 18.1196 16C20.871 16 23.104 13.76 23.104 11C23.104 8.24 20.871 6 18.1196 6C15.3682 6 13.1352 8.24 13.1352 11ZM18.1196 8C19.7645 8 21.1103 9.35 21.1103 11C21.1103 12.65 19.7645 14 18.1196 14C16.4748 14 15.129 12.65 15.129 11C15.129 9.35 16.4748 8 18.1196 8ZM25.0978 4C26.7427 4 28.0885 5.35 28.0885 7C28.0885 8.65 26.7427 10 25.0978 10C25.0679 10 25.048 10 25.0181 10C25.0679 10.33 25.0978 10.66 25.0978 11.01C25.0978 11.36 25.0679 11.68 25.0181 12C25.048 12 25.0679 12 25.0978 12C27.8492 12 30.0822 9.76 30.0822 7C30.0822 4.24 27.8393 2 25.0978 2C23.2436 2 21.6486 3.03 20.7913 4.53C21.4093 4.79 21.9875 5.13 22.496 5.55C23.0044 4.63 23.9713 4 25.0978 4ZM22.0673 19.75C22.1271 19.04 22.4262 18.39 22.8947 17.91C21.5788 17.44 19.9539 17.09 18.0299 17.09C14.0623 17.09 11.3308 18.59 10.0548 19.49C9.42679 19.93 9.05794 20.63 9.05794 21.37V24.09C9.05794 24.64 9.50654 25.09 10.0548 25.09C10.6031 25.09 11.0517 24.64 11.0517 24.09V21.37C11.0517 21.28 11.1115 21.19 11.2012 21.12C12.0685 20.51 14.481 19.08 18.0299 19.08C19.615 19.08 20.9607 19.37 22.0573 19.74L22.0673 19.75ZM33.0031 22H28.0187V24H32.0062V32H18.0498V24H24.0312V25C24.0312 25.55 24.4798 26 25.028 26C25.5763 26 26.0249 25.55 26.0249 25V20C26.0249 19.45 25.5763 19 25.028 19C24.4798 19 24.0312 19.45 24.0312 20V22H17.053C16.5047 22 16.0561 22.45 16.0561 23V33C16.0561 33.55 16.5047 34 17.053 34H33.0031C33.5514 34 34 33.55 34 33V23C34 22.45 33.5514 22 33.0031 22ZM22.0374 29H28.0187V27.6H22.0374V29ZM12.5271 15.18C12.0586 14.56 11.6997 13.86 11.4604 13.1C11.291 13.1 11.1414 13.08 10.972 13.08C7.00436 13.08 4.2729 14.58 3.00685 15.48C2.37882 15.92 2 16.62 2 17.36V20.08C2 20.63 2.4486 21.08 2.99688 21.08C3.54517 21.08 3.99377 20.63 3.99377 20.08V17.36C3.99377 17.27 4.05358 17.18 4.1433 17.11C5.01059 16.5 7.42305 15.07 10.972 15.07C11.5202 15.07 12.0386 15.11 12.5371 15.17L12.5271 15.18ZM11.0517 12C11.1115 12 11.1614 11.99 11.2112 11.98C11.1614 11.66 11.1315 11.33 11.1315 11C11.1315 10.67 11.1614 10.32 11.2112 9.98C11.1514 9.98 11.1016 10 11.0417 10C9.39688 10 8.05109 8.65 8.05109 7C8.05109 5.35 9.39688 4 11.0417 4C12.1882 4 13.1651 4.65 13.6735 5.6C14.1819 5.18 14.7502 4.83 15.3682 4.56C14.5109 3.04 12.9059 2 11.0417 2C8.29034 2 6.05732 4.24 6.05732 7C6.05732 9.76 8.29034 12 11.0417 12H11.0517Z\"/>',\n    solid: '<path d=\"M25.0906 11C25.0906 11.34 25.0607 11.67 25.0109 11.99C25.0408 11.99 25.0607 11.99 25.0906 11.99C27.8411 11.99 30.0735 9.75 30.0735 6.99C30.0735 4.23 27.8312 2 25.0906 2C23.237 2 21.6424 3.03 20.7854 4.53C23.3167 5.58 25.0906 8.08 25.0906 11ZM13.1317 11C13.1317 13.76 15.364 16 18.1146 16C20.8651 16 23.0974 13.76 23.0974 11C23.0974 8.24 20.8651 6 18.1146 6C15.364 6 13.1317 8.24 13.1317 11ZM22.0311 29H28.0105V27.6H22.0311V29ZM24.7617 13.09C24.4328 14.15 23.8548 15.1 23.0974 15.88C24.3332 16.28 25.3397 16.77 26.137 17.23C26.426 17.35 26.6751 17.51 26.9143 17.71C26.9841 17.76 27.0738 17.81 27.1336 17.85C27.9009 18.39 28.4391 19.15 28.7281 20H32.9835C33.3223 20 33.6412 20.07 33.9402 20.17C33.9402 20.14 33.9601 20.11 33.9601 20.08V17.36C33.9601 16.62 33.5814 15.92 32.9635 15.48C31.6879 14.58 28.9573 13.08 24.9909 13.08C24.9112 13.08 24.8315 13.08 24.7517 13.08L24.7617 13.09ZM22.0311 20C22.0311 19.18 22.36 18.45 22.8882 17.91C21.5727 17.44 19.9483 17.09 18.0249 17.09C14.0585 17.09 11.3279 18.59 10.0523 19.49C9.42444 19.93 9.0557 20.63 9.0557 21.37V24.09C9.0557 24.64 9.50416 25.09 10.0523 25.09H14.0585V23.01C14.0585 21.36 15.4039 20.01 17.0482 20.01H22.0311V20ZM32.9934 22H28.0105V24H31.9968V32H18.0448V24H24.0243V25C24.0243 25.55 24.4727 26 25.0208 26C25.5689 26 26.0174 25.55 26.0174 25V20C26.0174 19.45 25.5689 19 25.0208 19C24.4727 19 24.0243 19.45 24.0243 20V22H17.0482C16.5001 22 16.0517 22.45 16.0517 23V33C16.0517 33.55 16.5001 34 17.0482 34H32.9934C33.5415 34 33.99 33.55 33.99 33V23C33.99 22.45 33.5415 22 32.9934 22ZM11.0489 12C11.1086 12 11.1585 11.99 11.2083 11.98C11.1585 11.66 11.1286 11.33 11.1286 11C11.1286 8.11 12.8825 5.63 15.364 4.56C14.507 3.04 12.9025 2 11.0389 2C8.28834 2 6.05601 4.24 6.05601 7C6.05601 9.76 8.28834 12 11.0389 12H11.0489ZM11.4574 13.1C11.288 13.1 11.1286 13.08 10.9592 13.08C6.99279 13.08 4.26218 14.58 2.99653 15.48C2.36869 15.92 1.98999 16.62 1.98999 17.36V20.08C1.98999 20.63 2.43845 21.08 2.98656 21.08H7.07252C7.16221 19.8 7.82992 18.6 8.90622 17.84C9.7533 17.24 11.1684 16.42 13.0719 15.83C12.3444 15.06 11.7764 14.13 11.4574 13.1Z\"/>'\n  })];\nexport { H as employeeGroupIcon, V as employeeGroupIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "V", "H", "outline", "solid", "employeeGroupIcon", "employeeGroupIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/employee-group.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const V=\"employee-group\",H=[\"employee-group\",C({outline:'<path d=\"M24.7788 13.09C24.5396 13.85 24.1807 14.54 23.7122 15.16C24.1308 15.12 24.5495 15.08 25.0081 15.08C28.557 15.08 30.9595 16.5 31.8368 17.12C31.9364 17.19 31.9863 17.28 31.9863 17.37V20H33.0031C33.3421 20 33.6611 20.07 33.9601 20.17C33.9601 20.14 33.9801 20.11 33.9801 20.08V17.36C33.9801 16.62 33.6012 15.92 32.9832 15.48C31.7072 14.58 28.9757 13.08 25.0081 13.08C24.9283 13.08 24.8486 13.08 24.7688 13.08L24.7788 13.09ZM13.1352 11C13.1352 13.76 15.3682 16 18.1196 16C20.871 16 23.104 13.76 23.104 11C23.104 8.24 20.871 6 18.1196 6C15.3682 6 13.1352 8.24 13.1352 11ZM18.1196 8C19.7645 8 21.1103 9.35 21.1103 11C21.1103 12.65 19.7645 14 18.1196 14C16.4748 14 15.129 12.65 15.129 11C15.129 9.35 16.4748 8 18.1196 8ZM25.0978 4C26.7427 4 28.0885 5.35 28.0885 7C28.0885 8.65 26.7427 10 25.0978 10C25.0679 10 25.048 10 25.0181 10C25.0679 10.33 25.0978 10.66 25.0978 11.01C25.0978 11.36 25.0679 11.68 25.0181 12C25.048 12 25.0679 12 25.0978 12C27.8492 12 30.0822 9.76 30.0822 7C30.0822 4.24 27.8393 2 25.0978 2C23.2436 2 21.6486 3.03 20.7913 4.53C21.4093 4.79 21.9875 5.13 22.496 5.55C23.0044 4.63 23.9713 4 25.0978 4ZM22.0673 19.75C22.1271 19.04 22.4262 18.39 22.8947 17.91C21.5788 17.44 19.9539 17.09 18.0299 17.09C14.0623 17.09 11.3308 18.59 10.0548 19.49C9.42679 19.93 9.05794 20.63 9.05794 21.37V24.09C9.05794 24.64 9.50654 25.09 10.0548 25.09C10.6031 25.09 11.0517 24.64 11.0517 24.09V21.37C11.0517 21.28 11.1115 21.19 11.2012 21.12C12.0685 20.51 14.481 19.08 18.0299 19.08C19.615 19.08 20.9607 19.37 22.0573 19.74L22.0673 19.75ZM33.0031 22H28.0187V24H32.0062V32H18.0498V24H24.0312V25C24.0312 25.55 24.4798 26 25.028 26C25.5763 26 26.0249 25.55 26.0249 25V20C26.0249 19.45 25.5763 19 25.028 19C24.4798 19 24.0312 19.45 24.0312 20V22H17.053C16.5047 22 16.0561 22.45 16.0561 23V33C16.0561 33.55 16.5047 34 17.053 34H33.0031C33.5514 34 34 33.55 34 33V23C34 22.45 33.5514 22 33.0031 22ZM22.0374 29H28.0187V27.6H22.0374V29ZM12.5271 15.18C12.0586 14.56 11.6997 13.86 11.4604 13.1C11.291 13.1 11.1414 13.08 10.972 13.08C7.00436 13.08 4.2729 14.58 3.00685 15.48C2.37882 15.92 2 16.62 2 17.36V20.08C2 20.63 2.4486 21.08 2.99688 21.08C3.54517 21.08 3.99377 20.63 3.99377 20.08V17.36C3.99377 17.27 4.05358 17.18 4.1433 17.11C5.01059 16.5 7.42305 15.07 10.972 15.07C11.5202 15.07 12.0386 15.11 12.5371 15.17L12.5271 15.18ZM11.0517 12C11.1115 12 11.1614 11.99 11.2112 11.98C11.1614 11.66 11.1315 11.33 11.1315 11C11.1315 10.67 11.1614 10.32 11.2112 9.98C11.1514 9.98 11.1016 10 11.0417 10C9.39688 10 8.05109 8.65 8.05109 7C8.05109 5.35 9.39688 4 11.0417 4C12.1882 4 13.1651 4.65 13.6735 5.6C14.1819 5.18 14.7502 4.83 15.3682 4.56C14.5109 3.04 12.9059 2 11.0417 2C8.29034 2 6.05732 4.24 6.05732 7C6.05732 9.76 8.29034 12 11.0417 12H11.0517Z\"/>',solid:'<path d=\"M25.0906 11C25.0906 11.34 25.0607 11.67 25.0109 11.99C25.0408 11.99 25.0607 11.99 25.0906 11.99C27.8411 11.99 30.0735 9.75 30.0735 6.99C30.0735 4.23 27.8312 2 25.0906 2C23.237 2 21.6424 3.03 20.7854 4.53C23.3167 5.58 25.0906 8.08 25.0906 11ZM13.1317 11C13.1317 13.76 15.364 16 18.1146 16C20.8651 16 23.0974 13.76 23.0974 11C23.0974 8.24 20.8651 6 18.1146 6C15.364 6 13.1317 8.24 13.1317 11ZM22.0311 29H28.0105V27.6H22.0311V29ZM24.7617 13.09C24.4328 14.15 23.8548 15.1 23.0974 15.88C24.3332 16.28 25.3397 16.77 26.137 17.23C26.426 17.35 26.6751 17.51 26.9143 17.71C26.9841 17.76 27.0738 17.81 27.1336 17.85C27.9009 18.39 28.4391 19.15 28.7281 20H32.9835C33.3223 20 33.6412 20.07 33.9402 20.17C33.9402 20.14 33.9601 20.11 33.9601 20.08V17.36C33.9601 16.62 33.5814 15.92 32.9635 15.48C31.6879 14.58 28.9573 13.08 24.9909 13.08C24.9112 13.08 24.8315 13.08 24.7517 13.08L24.7617 13.09ZM22.0311 20C22.0311 19.18 22.36 18.45 22.8882 17.91C21.5727 17.44 19.9483 17.09 18.0249 17.09C14.0585 17.09 11.3279 18.59 10.0523 19.49C9.42444 19.93 9.0557 20.63 9.0557 21.37V24.09C9.0557 24.64 9.50416 25.09 10.0523 25.09H14.0585V23.01C14.0585 21.36 15.4039 20.01 17.0482 20.01H22.0311V20ZM32.9934 22H28.0105V24H31.9968V32H18.0448V24H24.0243V25C24.0243 25.55 24.4727 26 25.0208 26C25.5689 26 26.0174 25.55 26.0174 25V20C26.0174 19.45 25.5689 19 25.0208 19C24.4727 19 24.0243 19.45 24.0243 20V22H17.0482C16.5001 22 16.0517 22.45 16.0517 23V33C16.0517 33.55 16.5001 34 17.0482 34H32.9934C33.5415 34 33.99 33.55 33.99 33V23C33.99 22.45 33.5415 22 32.9934 22ZM11.0489 12C11.1086 12 11.1585 11.99 11.2083 11.98C11.1585 11.66 11.1286 11.33 11.1286 11C11.1286 8.11 12.8825 5.63 15.364 4.56C14.507 3.04 12.9025 2 11.0389 2C8.28834 2 6.05601 4.24 6.05601 7C6.05601 9.76 8.28834 12 11.0389 12H11.0489ZM11.4574 13.1C11.288 13.1 11.1286 13.08 10.9592 13.08C6.99279 13.08 4.26218 14.58 2.99653 15.48C2.36869 15.92 1.98999 16.62 1.98999 17.36V20.08C1.98999 20.63 2.43845 21.08 2.98656 21.08H7.07252C7.16221 19.8 7.82992 18.6 8.90622 17.84C9.7533 17.24 11.1684 16.42 13.0719 15.83C12.3444 15.06 11.7764 14.13 11.4574 13.1Z\"/>'})];export{H as employeeGroupIcon,V as employeeGroupIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,gBAAgB;EAACC,CAAC,GAAC,CAAC,gBAAgB,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,krFAAkrF;IAACC,KAAK,EAAC;EAAsjE,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,iBAAiB,EAACJ,CAAC,IAAIK,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}