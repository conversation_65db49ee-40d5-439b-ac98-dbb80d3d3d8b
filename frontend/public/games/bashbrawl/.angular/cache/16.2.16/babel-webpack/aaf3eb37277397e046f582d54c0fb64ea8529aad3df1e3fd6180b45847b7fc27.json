{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst r = \"drag-handle-corner\",\n  e = [\"drag-handle-corner\", C({\n    outline: '<path d=\"M10 24C8.9 24 8 24.9 8 26C8 27.1 8.9 28 10 28C11.1 28 12 27.1 12 26C12 24.9 11.1 24 10 24ZM18 24C16.9 24 16 24.9 16 26C16 27.1 16.9 28 18 28C19.1 28 20 27.1 20 26C20 24.9 19.1 24 18 24ZM26 12C27.1 12 28 11.1 28 10C28 8.9 27.1 8 26 8C24.9 8 24 8.9 24 10C24 11.1 24.9 12 26 12ZM26 16C24.9 16 24 16.9 24 18C24 19.1 24.9 20 26 20C27.1 20 28 19.1 28 18C28 16.9 27.1 16 26 16ZM26 24C24.9 24 24 24.9 24 26C24 27.1 24.9 28 26 28C27.1 28 28 27.1 28 26C28 24.9 27.1 24 26 24ZM18 16C16.9 16 16 16.9 16 18C16 19.1 16.9 20 18 20C19.1 20 20 19.1 20 18C20 16.9 19.1 16 18 16Z\"/>'\n  })];\nexport { e as dragHandleCornerIcon, r as dragH<PERSON>le<PERSON>orner<PERSON>con<PERSON>ame };", "map": {"version": 3, "names": ["renderIcon", "C", "r", "e", "outline", "dragHandleCornerIcon", "dragHandleCornerIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/drag-handle-corner.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const r=\"drag-handle-corner\",e=[\"drag-handle-corner\",C({outline:'<path d=\"M10 24C8.9 24 8 24.9 8 26C8 27.1 8.9 28 10 28C11.1 28 12 27.1 12 26C12 24.9 11.1 24 10 24ZM18 24C16.9 24 16 24.9 16 26C16 27.1 16.9 28 18 28C19.1 28 20 27.1 20 26C20 24.9 19.1 24 18 24ZM26 12C27.1 12 28 11.1 28 10C28 8.9 27.1 8 26 8C24.9 8 24 8.9 24 10C24 11.1 24.9 12 26 12ZM26 16C24.9 16 24 16.9 24 18C24 19.1 24.9 20 26 20C27.1 20 28 19.1 28 18C28 16.9 27.1 16 26 16ZM26 24C24.9 24 24 24.9 24 26C24 27.1 24.9 28 26 28C27.1 28 28 27.1 28 26C28 24.9 27.1 24 26 24ZM18 16C16.9 16 16 16.9 16 18C16 19.1 16.9 20 18 20C19.1 20 20 19.1 20 18C20 16.9 19.1 16 18 16Z\"/>'})];export{e as dragHandleCornerIcon,r as dragH<PERSON>le<PERSON>orner<PERSON>con<PERSON>ame};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,oBAAoB;EAACC,CAAC,GAAC,CAAC,oBAAoB,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAA8jB,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,oBAAoB,EAACH,CAAC,IAAII,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}