{"ast": null, "code": "import { renderIcon as H } from \"../icon.renderer.js\";\nconst V = \"switch\",\n  C = [\"switch\", H({\n    outline: '<path d=\"M23.9929 10.0062H19.9943V12.0082H23.9929V10.0062ZM7.99857 26.0219H9.99786V24.02H7.99857V26.0219ZM27.9914 10.0062H25.9921V12.0082H27.9914V10.0062ZM25.7022 18.3044C25.3124 17.914 24.6826 17.914 24.2928 18.3044C23.9029 18.6947 23.9029 19.3254 24.2928 19.7157L28.2513 24.01H18.005V26.0119H28.2513L24.2928 30.3061C23.9029 30.6965 23.9029 31.3271 24.2928 31.7175C24.4927 31.9177 24.7426 32.0078 25.0025 32.0078C25.2624 32.0078 25.5123 31.9077 25.7122 31.7175L32 25.0009L25.7122 18.2843L25.7022 18.3044ZM11.7072 16.3024L7.74866 12.0082H17.995V10.0062H7.74866L11.7072 5.71199C12.0971 5.3216 12.0971 4.69098 11.7072 4.3006C11.3174 3.91022 10.6876 3.91022 10.2978 4.3006L4 11.0072L10.2878 17.7238C10.4877 17.924 10.7376 18.0141 10.9975 18.0141C11.2574 18.0141 11.5073 17.914 11.7072 17.7238C12.0971 17.3334 12.0971 16.7028 11.7072 16.3124V16.3024ZM11.9971 26.0219H15.9957V24.02H11.9971V26.0219Z\"/>'\n  })];\nexport { C as switchIcon, V as switchIconName };", "map": {"version": 3, "names": ["renderIcon", "H", "V", "C", "outline", "switchIcon", "switchIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/switch.js"], "sourcesContent": ["import{renderIcon as H}from\"../icon.renderer.js\";const V=\"switch\",C=[\"switch\",H({outline:'<path d=\"M23.9929 10.0062H19.9943V12.0082H23.9929V10.0062ZM7.99857 26.0219H9.99786V24.02H7.99857V26.0219ZM27.9914 10.0062H25.9921V12.0082H27.9914V10.0062ZM25.7022 18.3044C25.3124 17.914 24.6826 17.914 24.2928 18.3044C23.9029 18.6947 23.9029 19.3254 24.2928 19.7157L28.2513 24.01H18.005V26.0119H28.2513L24.2928 30.3061C23.9029 30.6965 23.9029 31.3271 24.2928 31.7175C24.4927 31.9177 24.7426 32.0078 25.0025 32.0078C25.2624 32.0078 25.5123 31.9077 25.7122 31.7175L32 25.0009L25.7122 18.2843L25.7022 18.3044ZM11.7072 16.3024L7.74866 12.0082H17.995V10.0062H7.74866L11.7072 5.71199C12.0971 5.3216 12.0971 4.69098 11.7072 4.3006C11.3174 3.91022 10.6876 3.91022 10.2978 4.3006L4 11.0072L10.2878 17.7238C10.4877 17.924 10.7376 18.0141 10.9975 18.0141C11.2574 18.0141 11.5073 17.914 11.7072 17.7238C12.0971 17.3334 12.0971 16.7028 11.7072 16.3124V16.3024ZM11.9971 26.0219H15.9957V24.02H11.9971V26.0219Z\"/>'})];export{C as switchIcon,V as switchIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAk4B,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,UAAU,EAACH,CAAC,IAAII,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}