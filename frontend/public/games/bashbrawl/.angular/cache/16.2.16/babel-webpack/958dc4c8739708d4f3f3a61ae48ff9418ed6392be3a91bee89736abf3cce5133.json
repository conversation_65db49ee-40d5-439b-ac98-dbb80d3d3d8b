{"ast": null, "code": "import { Observable } from '../Observable';\nimport { noop } from '../util/noop';\nexport const NEVER = new Observable(noop);\nexport function never() {\n  return NEVER;\n}", "map": {"version": 3, "names": ["Observable", "noop", "NEVER", "never"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/rxjs/dist/esm/internal/observable/never.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { noop } from '../util/noop';\nexport const NEVER = new Observable(noop);\nexport function never() {\n    return NEVER;\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,IAAI,QAAQ,cAAc;AACnC,OAAO,MAAMC,KAAK,GAAG,IAAIF,UAAU,CAACC,IAAI,CAAC;AACzC,OAAO,SAASE,KAAKA,CAAA,EAAG;EACpB,OAAOD,KAAK;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}