{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nfunction t() {\n  return t => t.addInitializer(t => new e(t));\n}\nclass e {\n  constructor(t) {\n    this.host = t, this.host.addController(this);\n  }\n  hostConnected() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.host.updateComplete, _this.host.addEventListener(\"keydown\", t => _this.emulateActive(t)), _this.host.addEventListener(\"mousedown\", t => _this.emulateActive(t)), _this.host.addEventListener(\"keyup\", () => _this.emulateInactive()), _this.host.addEventListener(\"blur\", () => _this.emulateInactive()), _this.host.addEventListener(\"mouseup\", () => _this.emulateInactive());\n    })();\n  }\n  emulateActive(t) {\n    this.host.disabled || this.host?.setAttribute(\"cds-active\", \"\"), \"Space\" === t.code && t.target === this.host && t.preventDefault();\n  }\n  emulateInactive() {\n    this.host.removeAttribute(\"cds-active\");\n  }\n}\nexport { e as ActiveController, t as active };", "map": {"version": 3, "names": ["t", "addInitializer", "e", "constructor", "host", "addController", "hostConnected", "_this", "_asyncToGenerator", "updateComplete", "addEventListener", "emulateActive", "emulateInactive", "disabled", "setAttribute", "code", "target", "preventDefault", "removeAttribute", "ActiveController", "active"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/active.controller.js"], "sourcesContent": ["function t(){return t=>t.addInitializer((t=>new e(t)))}class e{constructor(t){this.host=t,this.host.addController(this)}async hostConnected(){await this.host.updateComplete,this.host.addEventListener(\"keydown\",(t=>this.emulateActive(t))),this.host.addEventListener(\"mousedown\",(t=>this.emulateActive(t))),this.host.addEventListener(\"keyup\",(()=>this.emulateInactive())),this.host.addEventListener(\"blur\",(()=>this.emulateInactive())),this.host.addEventListener(\"mouseup\",(()=>this.emulateInactive()))}emulateActive(t){this.host.disabled||this.host?.setAttribute(\"cds-active\",\"\"),\"Space\"===t.code&&t.target===this.host&&t.preventDefault()}emulateInactive(){this.host.removeAttribute(\"cds-active\")}}export{e as ActiveController,t as active};\n"], "mappings": ";AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAOA,CAAC,IAAEA,CAAC,CAACC,cAAc,CAAED,CAAC,IAAE,IAAIE,CAAC,CAACF,CAAC,CAAE,CAAC;AAAA;AAAC,MAAME,CAAC;EAACC,WAAWA,CAACH,CAAC,EAAC;IAAC,IAAI,CAACI,IAAI,GAACJ,CAAC,EAAC,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;EAAA;EAAOC,aAAaA,CAAA,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAAC,MAAMD,KAAI,CAACH,IAAI,CAACK,cAAc,EAACF,KAAI,CAACH,IAAI,CAACM,gBAAgB,CAAC,SAAS,EAAEV,CAAC,IAAEO,KAAI,CAACI,aAAa,CAACX,CAAC,CAAE,CAAC,EAACO,KAAI,CAACH,IAAI,CAACM,gBAAgB,CAAC,WAAW,EAAEV,CAAC,IAAEO,KAAI,CAACI,aAAa,CAACX,CAAC,CAAE,CAAC,EAACO,KAAI,CAACH,IAAI,CAACM,gBAAgB,CAAC,OAAO,EAAE,MAAIH,KAAI,CAACK,eAAe,CAAC,CAAE,CAAC,EAACL,KAAI,CAACH,IAAI,CAACM,gBAAgB,CAAC,MAAM,EAAE,MAAIH,KAAI,CAACK,eAAe,CAAC,CAAE,CAAC,EAACL,KAAI,CAACH,IAAI,CAACM,gBAAgB,CAAC,SAAS,EAAE,MAAIH,KAAI,CAACK,eAAe,CAAC,CAAE,CAAC;IAAA;EAAA;EAACD,aAAaA,CAACX,CAAC,EAAC;IAAC,IAAI,CAACI,IAAI,CAACS,QAAQ,IAAE,IAAI,CAACT,IAAI,EAAEU,YAAY,CAAC,YAAY,EAAC,EAAE,CAAC,EAAC,OAAO,KAAGd,CAAC,CAACe,IAAI,IAAEf,CAAC,CAACgB,MAAM,KAAG,IAAI,CAACZ,IAAI,IAAEJ,CAAC,CAACiB,cAAc,CAAC,CAAC;EAAA;EAACL,eAAeA,CAAA,EAAE;IAAC,IAAI,CAACR,IAAI,CAACc,eAAe,CAAC,YAAY,CAAC;EAAA;AAAC;AAAC,SAAOhB,CAAC,IAAIiB,gBAAgB,EAACnB,CAAC,IAAIoB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}