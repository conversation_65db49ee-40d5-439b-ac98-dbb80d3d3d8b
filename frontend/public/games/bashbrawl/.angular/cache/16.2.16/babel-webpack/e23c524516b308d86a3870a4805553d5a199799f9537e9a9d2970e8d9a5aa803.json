{"ast": null, "code": "import { renderIcon as L } from \"../icon.renderer.js\";\nconst h = \"host-group\",\n  t = [\"host-group\", L({\n    outline: '<path d=\"M21.08,34h-14A1.08,1.08,0,0,1,6,33V12a1.08,1.08,0,0,1,1.08-1.08h14A1.08,1.08,0,0,1,22.16,12V33A1.08,1.08,0,0,1,21.08,34ZM8.16,31.88H20V13H8.16Z\"/><rect x=\"10.08\" y=\"14.96\" width=\"8\" height=\"2\"/><path d=\"M26.1,27.81h-2V9h-12V7h13a1,1,0,0,1,1,1Z\"/><path d=\"M30.08,23h-2V5h-11V3h12a1,1,0,0,1,1,1Z\"/><rect x=\"13.08\" y=\"27.88\" width=\"2\" height=\"2.16\"/>',\n    solid: '<path d=\"M15.08,31 L1.08,31 C0.513427197,31.0015564 0.0419663765,30.5650186 0,30 L0,9 C0,8.40353247 0.48353247,7.92 1.08,7.92 L15.08,7.92 C15.6764675,7.92 16.16,8.40353247 16.16,9 L16.16,30 C16.1180336,30.5650186 15.6465728,31.0015564 15.08,31 Z M4.08,11.96 L4.08,13.96 L12.08,13.96 L12.08,11.96 L4.08,11.96 Z M7.08,24.88 L7.08,27.04 L9.08,27.04 L9.08,24.88 L7.08,24.88 Z\"/><path d=\"M20.1,24.81 L18.1,24.81 L18.1,6 L6.1,6 L6.1,4 L19.1,4 C19.6522847,4 20.1,4.44771525 20.1,5 L20.1,24.81 Z\"/><path d=\"M24.08,20 L22.08,20 L22.08,2 L11.08,2 L11.08,0 L23.08,0 C23.6322847,0 24.08,0.44771525 24.08,1 L24.08,20 Z\"/>'\n  })];\nexport { t as hostGroupIcon, h as hostGroupIconName };", "map": {"version": 3, "names": ["renderIcon", "L", "h", "t", "outline", "solid", "hostGroupIcon", "hostGroupIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/host-group.js"], "sourcesContent": ["import{renderIcon as L}from\"../icon.renderer.js\";const h=\"host-group\",t=[\"host-group\",L({outline:'<path d=\"M21.08,34h-14A1.08,1.08,0,0,1,6,33V12a1.08,1.08,0,0,1,1.08-1.08h14A1.08,1.08,0,0,1,22.16,12V33A1.08,1.08,0,0,1,21.08,34ZM8.16,31.88H20V13H8.16Z\"/><rect x=\"10.08\" y=\"14.96\" width=\"8\" height=\"2\"/><path d=\"M26.1,27.81h-2V9h-12V7h13a1,1,0,0,1,1,1Z\"/><path d=\"M30.08,23h-2V5h-11V3h12a1,1,0,0,1,1,1Z\"/><rect x=\"13.08\" y=\"27.88\" width=\"2\" height=\"2.16\"/>',solid:'<path d=\"M15.08,31 L1.08,31 C0.513427197,31.0015564 0.0419663765,30.5650186 0,30 L0,9 C0,8.40353247 0.48353247,7.92 1.08,7.92 L15.08,7.92 C15.6764675,7.92 16.16,8.40353247 16.16,9 L16.16,30 C16.1180336,30.5650186 15.6465728,31.0015564 15.08,31 Z M4.08,11.96 L4.08,13.96 L12.08,13.96 L12.08,11.96 L4.08,11.96 Z M7.08,24.88 L7.08,27.04 L9.08,27.04 L9.08,24.88 L7.08,24.88 Z\"/><path d=\"M20.1,24.81 L18.1,24.81 L18.1,6 L6.1,6 L6.1,4 L19.1,4 C19.6522847,4 20.1,4.44771525 20.1,5 L20.1,24.81 Z\"/><path d=\"M24.08,20 L22.08,20 L22.08,2 L11.08,2 L11.08,0 L23.08,0 C23.6322847,0 24.08,0.44771525 24.08,1 L24.08,20 Z\"/>'})];export{t as hostGroupIcon,h as hostGroupIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,YAAY;EAACC,CAAC,GAAC,CAAC,YAAY,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,sWAAsW;IAACC,KAAK,EAAC;EAAkmB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,aAAa,EAACJ,CAAC,IAAIK,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}