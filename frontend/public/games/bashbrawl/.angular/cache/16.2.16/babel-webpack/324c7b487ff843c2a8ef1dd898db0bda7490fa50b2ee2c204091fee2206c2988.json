{"ast": null, "code": "const o = [{\n  opacity: 0\n}, {\n  opacity: 1\n}];\nexport { o as fadeInKeyframes };", "map": {"version": 3, "names": ["o", "opacity", "fadeInKeyframes"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/motion/animations/keyframes/fade-in.js"], "sourcesContent": ["const o=[{opacity:0},{opacity:1}];export{o as fadeInKeyframes};\n"], "mappings": "AAAA,MAAMA,CAAC,GAAC,CAAC;EAACC,OAAO,EAAC;AAAC,CAAC,EAAC;EAACA,OAAO,EAAC;AAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}