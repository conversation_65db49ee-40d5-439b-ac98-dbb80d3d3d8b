{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"download\",\n  d = [\"download\", C({\n    outline: '<path d=\"M18 28L27.71 18.29C28.1 17.9 28.1 17.27 27.71 16.88C27.32 16.49 26.69 16.49 26.3 16.88L19.01 24.17V5C19.01 4.45 18.56 4 18.01 4C17.46 4 17.01 4.45 17.01 5V24.17L9.72 16.88C9.33 16.49 8.7 16.49 8.31 16.88C7.92 17.27 7.92 17.9 8.31 18.29L18.02 28H18ZM31 30H5C4.45 30 4 30.45 4 31C4 31.55 4.45 32 5 32H31C31.55 32 32 31.55 32 31C32 30.45 31.55 30 31 30Z\"/>',\n    outlineAlerted: '<path d=\"M18 28L27.71 18.29C28.1 17.9 28.1 17.27 27.71 16.88C27.32 16.49 26.69 16.49 26.3 16.88L19.01 24.17V5C19.01 4.45 18.56 4 18.01 4C17.46 4 17.01 4.45 17.01 5V24.17L9.72 16.88C9.33 16.49 8.7 16.49 8.31 16.88C7.92 17.27 7.92 17.9 8.31 18.29L18.02 28H18ZM31 30H5C4.45 30 4 30.45 4 31C4 31.55 4.45 32 5 32H31C31.55 32 32 31.55 32 31C32 30.45 31.55 30 31 30Z\"/><path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M18 28L27.71 18.29C28.1 17.9 28.1 17.27 27.71 16.88C27.32 16.49 26.69 16.49 26.3 16.88L19.01 24.17V5C19.01 4.45 18.56 4 18.01 4C17.46 4 17.01 4.45 17.01 5V24.17L9.72 16.88C9.33 16.49 8.7 16.49 8.31 16.88C7.92 17.27 7.92 17.9 8.31 18.29L18.02 28H18ZM31 30H5C4.45 30 4 30.45 4 31C4 31.55 4.45 32 5 32H31C31.55 32 32 31.55 32 31C32 30.45 31.55 30 31 30Z\"/>'\n  })];\nexport { d as downloadIcon, L as downloadIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "d", "outline", "outlineAlerted", "outlineBadged", "downloadIcon", "downloadIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/download.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"download\",d=[\"download\",C({outline:'<path d=\"M18 28L27.71 18.29C28.1 17.9 28.1 17.27 27.71 16.88C27.32 16.49 26.69 16.49 26.3 16.88L19.01 24.17V5C19.01 4.45 18.56 4 18.01 4C17.46 4 17.01 4.45 17.01 5V24.17L9.72 16.88C9.33 16.49 8.7 16.49 8.31 16.88C7.92 17.27 7.92 17.9 8.31 18.29L18.02 28H18ZM31 30H5C4.45 30 4 30.45 4 31C4 31.55 4.45 32 5 32H31C31.55 32 32 31.55 32 31C32 30.45 31.55 30 31 30Z\"/>',outlineAlerted:'<path d=\"M18 28L27.71 18.29C28.1 17.9 28.1 17.27 27.71 16.88C27.32 16.49 26.69 16.49 26.3 16.88L19.01 24.17V5C19.01 4.45 18.56 4 18.01 4C17.46 4 17.01 4.45 17.01 5V24.17L9.72 16.88C9.33 16.49 8.7 16.49 8.31 16.88C7.92 17.27 7.92 17.9 8.31 18.29L18.02 28H18ZM31 30H5C4.45 30 4 30.45 4 31C4 31.55 4.45 32 5 32H31C31.55 32 32 31.55 32 31C32 30.45 31.55 30 31 30Z\"/><path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M18 28L27.71 18.29C28.1 17.9 28.1 17.27 27.71 16.88C27.32 16.49 26.69 16.49 26.3 16.88L19.01 24.17V5C19.01 4.45 18.56 4 18.01 4C17.46 4 17.01 4.45 17.01 5V24.17L9.72 16.88C9.33 16.49 8.7 16.49 8.31 16.88C7.92 17.27 7.92 17.9 8.31 18.29L18.02 28H18ZM31 30H5C4.45 30 4 30.45 4 31C4 31.55 4.45 32 5 32H31C31.55 32 32 31.55 32 31C32 30.45 31.55 30 31 30Z\"/>'})];export{d as downloadIcon,L as downloadIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,UAAU;EAACC,CAAC,GAAC,CAAC,UAAU,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,4WAA4W;IAACC,cAAc,EAAC,+sBAA+sB;IAACC,aAAa,EAAC;EAA0e,CAAC,CAAC,CAAC;AAAC,SAAOH,CAAC,IAAII,YAAY,EAACL,CAAC,IAAIM,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}