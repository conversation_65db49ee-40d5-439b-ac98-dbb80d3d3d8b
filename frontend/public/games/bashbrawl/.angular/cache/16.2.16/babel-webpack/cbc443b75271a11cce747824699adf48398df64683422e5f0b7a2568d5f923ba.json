{"ast": null, "code": "import { property as t } from \"lit/decorators/property.js\";\nimport { camelCaseToKebabCase as r, capitalizeFirstLetter as e, kebabCaseToPascalCase as i } from \"../utils/string.js\";\nimport { LogService as n } from \"../services/log.service.js\";\nimport { getAngularVersion as o, getVueVersion as u, getReactVersion as c } from \"../utils/framework.js\";\nimport { isNilOrEmpty as s } from \"../utils/identity.js\";\nimport { coerceBooleanProperty as a } from \"../utils/dom.js\";\nfunction f(t, e) {\n  switch (e ? e.type : e) {\n    case Array:\n    case Object:\n      return {\n        reflect: !1,\n        ...e\n      };\n    case String:\n      return {\n        reflect: !0,\n        attribute: r(t),\n        converter: {\n          toAttribute: t => t || null\n        },\n        ...e\n      };\n    case Number:\n      return {\n        reflect: !0,\n        attribute: r(t),\n        ...e\n      };\n    case Boolean:\n      return {\n        reflect: !0,\n        attribute: r(t),\n        converter: {\n          toAttribute: t => t ? \"\" : null,\n          fromAttribute: t => a(t)\n        },\n        ...e\n      };\n    case Date:\n      return {\n        reflect: !1,\n        converter: {\n          fromAttribute: t => new Date(t)\n        },\n        ...e\n      };\n    default:\n      return {\n        ...e\n      };\n  }\n}\nfunction l(t, a, f) {\n  const l = t.firstUpdated;\n  t.firstUpdated = function (t) {\n    if (f && f.required && s(this[a])) {\n      const t = f.requiredMessage || function (t = \"warning\", n, s) {\n        const a = s.toLocaleLowerCase();\n        return `${e(t)}: ${n} is required to use ${a} component. Set the JS Property or HTML Attribute.\\n\\n` + (o() ? `Angular: <${a} [${n}]=\"...\"></${a}>\\n` : \"\") + (u() ? `Vue: <${a} :${n}=\"...\"></${a}>\\n` : \"\") + (c() ? `React: <${i(a)} ${function (t) {\n          return t.startsWith(\"aria\") ? r(t) : t;\n        }(n)}={...} />\\n` : \"\") + `HTML: <${a} ${r(n)}=\"...\"></${a}>\\n` + `JavaScript: document.querySelector('${a}').${n} = '...';\\n\\n`;\n      }(f.required, a, this.tagName);\n      if (\"error\" === f.required) throw Error(t);\n      n.warn(t, this);\n    }\n    l && l.call(this, t);\n  };\n}\nfunction m(r) {\n  return (e, i) => (r?.required && l(e, i, r), t(f(i, r))(e, i));\n}\nfunction p(e) {\n  return (i, n) => {\n    const o = f(n, e);\n    return o && (o.reflect = !!e?.reflect && e.reflect, o.reflect && !e?.attribute && (o.attribute = \"_\" + r(n))), t(o)(i, n);\n  };\n}\nexport { f as getDefaultOptions, m as property, l as requirePropertyCheck, p as state };", "map": {"version": 3, "names": ["property", "t", "camelCaseToKebabCase", "r", "capitalizeFirstLetter", "e", "kebabCaseToPascalCase", "i", "LogService", "n", "getAngularVersion", "o", "getVueVersion", "u", "getReactVersion", "c", "isNilOrEmpty", "s", "coerceBooleanProperty", "a", "f", "type", "Array", "Object", "reflect", "String", "attribute", "converter", "toAttribute", "Number", "Boolean", "fromAttribute", "Date", "l", "firstUpdated", "required", "requiredMessage", "toLocaleLowerCase", "startsWith", "tagName", "Error", "warn", "call", "m", "p", "getDefaultOptions", "requireProperty<PERSON>heck", "state"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/decorators/property.js"], "sourcesContent": ["import{property as t}from\"lit/decorators/property.js\";import{camelCaseToKebabCase as r,capitalizeFirstLetter as e,kebabCaseToPascalCase as i}from\"../utils/string.js\";import{LogService as n}from\"../services/log.service.js\";import{getAngularVersion as o,getVueVersion as u,getReactVersion as c}from\"../utils/framework.js\";import{isNilOrEmpty as s}from\"../utils/identity.js\";import{coerceBooleanProperty as a}from\"../utils/dom.js\";function f(t,e){switch(e?e.type:e){case Array:case Object:return{reflect:!1,...e};case String:return{reflect:!0,attribute:r(t),converter:{toAttribute:t=>t||null},...e};case Number:return{reflect:!0,attribute:r(t),...e};case Boolean:return{reflect:!0,attribute:r(t),converter:{toAttribute:t=>t?\"\":null,fromAttribute:t=>a(t)},...e};case Date:return{reflect:!1,converter:{fromAttribute:t=>new Date(t)},...e};default:return{...e}}}function l(t,a,f){const l=t.firstUpdated;t.firstUpdated=function(t){if(f&&f.required&&s(this[a])){const t=f.requiredMessage||function(t=\"warning\",n,s){const a=s.toLocaleLowerCase();return`${e(t)}: ${n} is required to use ${a} component. Set the JS Property or HTML Attribute.\\n\\n`+(o()?`Angular: <${a} [${n}]=\"...\"></${a}>\\n`:\"\")+(u()?`Vue: <${a} :${n}=\"...\"></${a}>\\n`:\"\")+(c()?`React: <${i(a)} ${function(t){return t.startsWith(\"aria\")?r(t):t}(n)}={...} />\\n`:\"\")+`HTML: <${a} ${r(n)}=\"...\"></${a}>\\n`+`JavaScript: document.querySelector('${a}').${n} = '...';\\n\\n`}(f.required,a,this.tagName);if(\"error\"===f.required)throw Error(t);n.warn(t,this)}l&&l.call(this,t)}}function m(r){return(e,i)=>(r?.required&&l(e,i,r),t(f(i,r))(e,i))}function p(e){return(i,n)=>{const o=f(n,e);return o&&(o.reflect=!!e?.reflect&&e.reflect,o.reflect&&!e?.attribute&&(o.attribute=\"_\"+r(n))),t(o)(i,n)}}export{f as getDefaultOptions,m as property,l as requirePropertyCheck,p as state};\n"], "mappings": "AAAA,SAAOA,QAAQ,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,oBAAoB,IAAIC,CAAC,EAACC,qBAAqB,IAAIC,CAAC,EAACC,qBAAqB,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,iBAAiB,IAAIC,CAAC,EAACC,aAAa,IAAIC,CAAC,EAACC,eAAe,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,qBAAqB,IAAIC,CAAC,QAAK,iBAAiB;AAAC,SAASC,CAACA,CAACnB,CAAC,EAACI,CAAC,EAAC;EAAC,QAAOA,CAAC,GAACA,CAAC,CAACgB,IAAI,GAAChB,CAAC;IAAE,KAAKiB,KAAK;IAAC,KAAKC,MAAM;MAAC,OAAM;QAACC,OAAO,EAAC,CAAC,CAAC;QAAC,GAAGnB;MAAC,CAAC;IAAC,KAAKoB,MAAM;MAAC,OAAM;QAACD,OAAO,EAAC,CAAC,CAAC;QAACE,SAAS,EAACvB,CAAC,CAACF,CAAC,CAAC;QAAC0B,SAAS,EAAC;UAACC,WAAW,EAAC3B,CAAC,IAAEA,CAAC,IAAE;QAAI,CAAC;QAAC,GAAGI;MAAC,CAAC;IAAC,KAAKwB,MAAM;MAAC,OAAM;QAACL,OAAO,EAAC,CAAC,CAAC;QAACE,SAAS,EAACvB,CAAC,CAACF,CAAC,CAAC;QAAC,GAAGI;MAAC,CAAC;IAAC,KAAKyB,OAAO;MAAC,OAAM;QAACN,OAAO,EAAC,CAAC,CAAC;QAACE,SAAS,EAACvB,CAAC,CAACF,CAAC,CAAC;QAAC0B,SAAS,EAAC;UAACC,WAAW,EAAC3B,CAAC,IAAEA,CAAC,GAAC,EAAE,GAAC,IAAI;UAAC8B,aAAa,EAAC9B,CAAC,IAAEkB,CAAC,CAAClB,CAAC;QAAC,CAAC;QAAC,GAAGI;MAAC,CAAC;IAAC,KAAK2B,IAAI;MAAC,OAAM;QAACR,OAAO,EAAC,CAAC,CAAC;QAACG,SAAS,EAAC;UAACI,aAAa,EAAC9B,CAAC,IAAE,IAAI+B,IAAI,CAAC/B,CAAC;QAAC,CAAC;QAAC,GAAGI;MAAC,CAAC;IAAC;MAAQ,OAAM;QAAC,GAAGA;MAAC,CAAC;EAAA;AAAC;AAAC,SAAS4B,CAACA,CAAChC,CAAC,EAACkB,CAAC,EAACC,CAAC,EAAC;EAAC,MAAMa,CAAC,GAAChC,CAAC,CAACiC,YAAY;EAACjC,CAAC,CAACiC,YAAY,GAAC,UAASjC,CAAC,EAAC;IAAC,IAAGmB,CAAC,IAAEA,CAAC,CAACe,QAAQ,IAAElB,CAAC,CAAC,IAAI,CAACE,CAAC,CAAC,CAAC,EAAC;MAAC,MAAMlB,CAAC,GAACmB,CAAC,CAACgB,eAAe,IAAE,UAASnC,CAAC,GAAC,SAAS,EAACQ,CAAC,EAACQ,CAAC,EAAC;QAAC,MAAME,CAAC,GAACF,CAAC,CAACoB,iBAAiB,CAAC,CAAC;QAAC,OAAO,GAAEhC,CAAC,CAACJ,CAAC,CAAE,KAAIQ,CAAE,uBAAsBU,CAAE,wDAAuD,IAAER,CAAC,CAAC,CAAC,GAAE,aAAYQ,CAAE,KAAIV,CAAE,aAAYU,CAAE,KAAI,GAAC,EAAE,CAAC,IAAEN,CAAC,CAAC,CAAC,GAAE,SAAQM,CAAE,KAAIV,CAAE,YAAWU,CAAE,KAAI,GAAC,EAAE,CAAC,IAAEJ,CAAC,CAAC,CAAC,GAAE,WAAUR,CAAC,CAACY,CAAC,CAAE,IAAG,UAASlB,CAAC,EAAC;UAAC,OAAOA,CAAC,CAACqC,UAAU,CAAC,MAAM,CAAC,GAACnC,CAAC,CAACF,CAAC,CAAC,GAACA,CAAC;QAAA,CAAC,CAACQ,CAAC,CAAE,aAAY,GAAC,EAAE,CAAC,GAAE,UAASU,CAAE,IAAGhB,CAAC,CAACM,CAAC,CAAE,YAAWU,CAAE,KAAI,GAAE,uCAAsCA,CAAE,MAAKV,CAAE,eAAc;MAAA,CAAC,CAACW,CAAC,CAACe,QAAQ,EAAChB,CAAC,EAAC,IAAI,CAACoB,OAAO,CAAC;MAAC,IAAG,OAAO,KAAGnB,CAAC,CAACe,QAAQ,EAAC,MAAMK,KAAK,CAACvC,CAAC,CAAC;MAACQ,CAAC,CAACgC,IAAI,CAACxC,CAAC,EAAC,IAAI,CAAC;IAAA;IAACgC,CAAC,IAAEA,CAAC,CAACS,IAAI,CAAC,IAAI,EAACzC,CAAC,CAAC;EAAA,CAAC;AAAA;AAAC,SAAS0C,CAACA,CAACxC,CAAC,EAAC;EAAC,OAAM,CAACE,CAAC,EAACE,CAAC,MAAIJ,CAAC,EAAEgC,QAAQ,IAAEF,CAAC,CAAC5B,CAAC,EAACE,CAAC,EAACJ,CAAC,CAAC,EAACF,CAAC,CAACmB,CAAC,CAACb,CAAC,EAACJ,CAAC,CAAC,CAAC,CAACE,CAAC,EAACE,CAAC,CAAC,CAAC;AAAA;AAAC,SAASqC,CAACA,CAACvC,CAAC,EAAC;EAAC,OAAM,CAACE,CAAC,EAACE,CAAC,KAAG;IAAC,MAAME,CAAC,GAACS,CAAC,CAACX,CAAC,EAACJ,CAAC,CAAC;IAAC,OAAOM,CAAC,KAAGA,CAAC,CAACa,OAAO,GAAC,CAAC,CAACnB,CAAC,EAAEmB,OAAO,IAAEnB,CAAC,CAACmB,OAAO,EAACb,CAAC,CAACa,OAAO,IAAE,CAACnB,CAAC,EAAEqB,SAAS,KAAGf,CAAC,CAACe,SAAS,GAAC,GAAG,GAACvB,CAAC,CAACM,CAAC,CAAC,CAAC,CAAC,EAACR,CAAC,CAACU,CAAC,CAAC,CAACJ,CAAC,EAACE,CAAC,CAAC;EAAA,CAAC;AAAA;AAAC,SAAOW,CAAC,IAAIyB,iBAAiB,EAACF,CAAC,IAAI3C,QAAQ,EAACiC,CAAC,IAAIa,oBAAoB,EAACF,CAAC,IAAIG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}