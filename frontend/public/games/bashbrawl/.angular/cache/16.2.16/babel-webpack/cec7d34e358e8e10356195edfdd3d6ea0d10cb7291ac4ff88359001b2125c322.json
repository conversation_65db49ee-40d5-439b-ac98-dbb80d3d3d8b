{"ast": null, "code": "import add from \"./add.js\";\nimport reduce from \"./reduce.js\";\n/**\n * Adds together all the elements of a list.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Math\n * @sig [Number] -> Number\n * @param {Array} list An array of numbers\n * @return {Number} The sum of all the numbers in the list.\n * @see R.reduce\n * @example\n *\n *      R.sum([2,4,6,8,100,1]); //=> 121\n */\n\nvar sum = /*#__PURE__*/\nreduce(add, 0);\nexport default sum;", "map": {"version": 3, "names": ["add", "reduce", "sum"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/sum.js"], "sourcesContent": ["import add from \"./add.js\";\nimport reduce from \"./reduce.js\";\n/**\n * Adds together all the elements of a list.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Math\n * @sig [Number] -> Number\n * @param {Array} list An array of numbers\n * @return {Number} The sum of all the numbers in the list.\n * @see R.reduce\n * @example\n *\n *      R.sum([2,4,6,8,100,1]); //=> 121\n */\n\nvar sum =\n/*#__PURE__*/\nreduce(add, 0);\nexport default sum;"], "mappings": "AAAA,OAAOA,GAAG,MAAM,UAAU;AAC1B,OAAOC,MAAM,MAAM,aAAa;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,GAAG,GACP;AACAD,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;AACd,eAAeE,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}