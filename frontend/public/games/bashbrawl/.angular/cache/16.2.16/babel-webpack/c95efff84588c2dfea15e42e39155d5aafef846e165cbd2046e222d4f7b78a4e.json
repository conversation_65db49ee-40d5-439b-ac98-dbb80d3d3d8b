{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst V = \"bitcoin\",\n  H = [\"bitcoin\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M23.9389 16.88C25.9127 15.4827 26.7378 13.0609 25.9972 10.8386C25.2566 8.61626 23.1115 7.0775 20.6462 7V4C20.6462 3.44772 20.1722 3 19.5875 3C19.0027 3 18.5287 3.44772 18.5287 4V7H15.3525V4C15.3525 3.44772 14.8785 3 14.2937 3C13.709 3 13.235 3.44772 13.235 4V7H10.0587C9.47402 7 9 7.44772 9 8V28C9 28.5523 9.47402 29 10.0587 29H13.235V32C13.235 32.5523 13.709 33 14.2937 33C14.8785 33 15.3525 32.5523 15.3525 32V29H18.5287V32C18.5287 32.5523 19.0027 33 19.5875 33C20.1722 33 20.6462 32.5523 20.6462 32V29H20.7309C24.268 28.9181 27.0697 26.1512 26.9987 22.81V22.19C27.0132 20.0408 25.8576 18.0354 23.9389 16.88ZM11.1175 9H20.318C21.9372 8.97566 23.3949 9.92298 23.9495 11.36C24.3357 12.4329 24.1498 13.6159 23.4503 14.5365C22.7508 15.4572 21.6236 16.0022 20.4239 16H11.1175V9ZM20.7097 27C23.0833 26.9239 24.9466 25.0523 24.8812 22.81V22.19C24.9466 19.9477 23.0833 18.0761 20.7097 18H11.1175V27H20.7097Z\"/>',\n    solid: '<path d=\"M23 13.75C23.0027 13.1559 22.7692 12.5851 22.351 12.1631C21.9328 11.7412 21.3641 11.5026 20.77 11.5H14.5V16H20.8C21.3905 15.9947 21.9546 15.7544 22.3675 15.3321C22.7804 14.9099 23.008 14.3405 23 13.75Z\"/><path d=\"M14.5 18.47H21.18C22.661 18.5136 23.8325 19.7385 23.81 21.22V21.7C23.8288 22.4162 23.5622 23.1104 23.069 23.6299C22.5757 24.1495 21.8962 24.4517 21.18 24.47H14.5V18.47Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 18C2 9.16344 9.16344 2 18 2C22.2435 2 26.3131 3.68571 29.3137 6.68629C32.3143 9.68687 34 13.7565 34 18C34 26.8366 26.8366 34 18 34C9.16344 34 2 26.8366 2 18ZM21.18 27C24.0473 26.9507 26.3379 24.5976 26.31 21.73V21.24C26.3108 19.5739 25.5296 18.004 24.2 17C25.4946 15.6634 25.8831 13.6909 25.192 11.9632C24.5009 10.2355 22.8593 9.07506 21 9V7.07C21 6.51772 20.5523 6.07 20 6.07C19.4477 6.07 19 6.51772 19 7.07V9H17V7.07C17 6.51772 16.5523 6.07 16 6.07C15.4477 6.07 15 6.51772 15 7.07V9H13.25C12.5674 8.99991 12.0109 9.54747 12 10.23V25.75C12 26.4404 12.5596 27 13.25 27H15V28.9C15 29.4523 15.4477 29.9 16 29.9C16.5523 29.9 17 29.4523 17 28.9V27H19V28.9C19 29.4523 19.4477 29.9 20 29.9C20.5523 29.9 21 29.4523 21 28.9V27H21.18Z\"/>'\n  })];\nexport { H as bitcoinIcon, V as bitcoinIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "V", "H", "outline", "solid", "bitcoinIcon", "bitcoinIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/bitcoin.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const V=\"bitcoin\",H=[\"bitcoin\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M23.9389 16.88C25.9127 15.4827 26.7378 13.0609 25.9972 10.8386C25.2566 8.61626 23.1115 7.0775 20.6462 7V4C20.6462 3.44772 20.1722 3 19.5875 3C19.0027 3 18.5287 3.44772 18.5287 4V7H15.3525V4C15.3525 3.44772 14.8785 3 14.2937 3C13.709 3 13.235 3.44772 13.235 4V7H10.0587C9.47402 7 9 7.44772 9 8V28C9 28.5523 9.47402 29 10.0587 29H13.235V32C13.235 32.5523 13.709 33 14.2937 33C14.8785 33 15.3525 32.5523 15.3525 32V29H18.5287V32C18.5287 32.5523 19.0027 33 19.5875 33C20.1722 33 20.6462 32.5523 20.6462 32V29H20.7309C24.268 28.9181 27.0697 26.1512 26.9987 22.81V22.19C27.0132 20.0408 25.8576 18.0354 23.9389 16.88ZM11.1175 9H20.318C21.9372 8.97566 23.3949 9.92298 23.9495 11.36C24.3357 12.4329 24.1498 13.6159 23.4503 14.5365C22.7508 15.4572 21.6236 16.0022 20.4239 16H11.1175V9ZM20.7097 27C23.0833 26.9239 24.9466 25.0523 24.8812 22.81V22.19C24.9466 19.9477 23.0833 18.0761 20.7097 18H11.1175V27H20.7097Z\"/>',solid:'<path d=\"M23 13.75C23.0027 13.1559 22.7692 12.5851 22.351 12.1631C21.9328 11.7412 21.3641 11.5026 20.77 11.5H14.5V16H20.8C21.3905 15.9947 21.9546 15.7544 22.3675 15.3321C22.7804 14.9099 23.008 14.3405 23 13.75Z\"/><path d=\"M14.5 18.47H21.18C22.661 18.5136 23.8325 19.7385 23.81 21.22V21.7C23.8288 22.4162 23.5622 23.1104 23.069 23.6299C22.5757 24.1495 21.8962 24.4517 21.18 24.47H14.5V18.47Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 18C2 9.16344 9.16344 2 18 2C22.2435 2 26.3131 3.68571 29.3137 6.68629C32.3143 9.68687 34 13.7565 34 18C34 26.8366 26.8366 34 18 34C9.16344 34 2 26.8366 2 18ZM21.18 27C24.0473 26.9507 26.3379 24.5976 26.31 21.73V21.24C26.3108 19.5739 25.5296 18.004 24.2 17C25.4946 15.6634 25.8831 13.6909 25.192 11.9632C24.5009 10.2355 22.8593 9.07506 21 9V7.07C21 6.51772 20.5523 6.07 20 6.07C19.4477 6.07 19 6.51772 19 7.07V9H17V7.07C17 6.51772 16.5523 6.07 16 6.07C15.4477 6.07 15 6.51772 15 7.07V9H13.25C12.5674 8.99991 12.0109 9.54747 12 10.23V25.75C12 26.4404 12.5596 27 13.25 27H15V28.9C15 29.4523 15.4477 29.9 16 29.9C16.5523 29.9 17 29.4523 17 28.9V27H19V28.9C19 29.4523 19.4477 29.9 20 29.9C20.5523 29.9 21 29.4523 21 28.9V27H21.18Z\"/>'})];export{H as bitcoinIcon,V as bitcoinIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,SAAS;EAACC,CAAC,GAAC,CAAC,SAAS,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,27BAA27B;IAACC,KAAK,EAAC;EAAupC,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,WAAW,EAACJ,CAAC,IAAIK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}