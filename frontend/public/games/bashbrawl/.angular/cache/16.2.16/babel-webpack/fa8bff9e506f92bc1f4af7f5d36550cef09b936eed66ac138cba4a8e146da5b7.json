{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst o = \"block-quote\",\n  t = [\"block-quote\", C({\n    outline: '<path d=\"M21.5392 18.1566C22.1663 17.789 22.8749 17.5949 23.5961 17.5933V17.5732C25.119 17.4968 26.5589 18.2916 27.3425 19.641C28.1261 20.9904 28.1261 22.6753 27.3425 24.0247C26.5589 25.3741 25.119 26.1689 23.5961 26.0925C22.9444 26.0954 22.3007 25.944 21.7147 25.65C19.943 24.8296 18.7995 23.0166 18.7901 21.0131C18.7901 18.2069 20.5253 13.6807 24.4248 10.4117C24.7175 10.1325 25.1362 10.0438 25.5119 10.1816C25.8876 10.3194 26.1581 10.6608 26.2144 11.068C26.2706 11.4753 26.1032 11.8811 25.7798 12.1216C23.8835 13.7203 22.4222 15.7999 21.5392 18.1566Z\"/><path d=\"M10.6793 18.1563C11.3067 17.7896 12.0151 17.5956 12.7362 17.5931H12.7557C14.2786 17.5167 15.7185 18.3115 16.5021 19.6609C17.2857 21.0103 17.2857 22.6952 16.5021 24.0446C15.7185 25.394 14.2786 26.1888 12.7557 26.1124C12.1206 26.1067 11.4946 25.9555 10.923 25.6698C9.11822 24.8676 7.9439 23.0403 7.93018 21.0129C7.93018 18.2066 9.66542 13.6804 13.5648 10.4115C14.0225 10.0227 14.699 10.0902 15.0759 10.5624C15.4527 11.0346 15.3873 11.7326 14.9296 12.1214C13.0278 13.7169 11.5626 15.7972 10.6793 18.1563Z\"/>'\n  })];\nexport { t as blockQuoteIcon, o as blockQuoteIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "o", "t", "outline", "blockQuoteIcon", "blockQuoteIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/block-quote.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const o=\"block-quote\",t=[\"block-quote\",C({outline:'<path d=\"M21.5392 18.1566C22.1663 17.789 22.8749 17.5949 23.5961 17.5933V17.5732C25.119 17.4968 26.5589 18.2916 27.3425 19.641C28.1261 20.9904 28.1261 22.6753 27.3425 24.0247C26.5589 25.3741 25.119 26.1689 23.5961 26.0925C22.9444 26.0954 22.3007 25.944 21.7147 25.65C19.943 24.8296 18.7995 23.0166 18.7901 21.0131C18.7901 18.2069 20.5253 13.6807 24.4248 10.4117C24.7175 10.1325 25.1362 10.0438 25.5119 10.1816C25.8876 10.3194 26.1581 10.6608 26.2144 11.068C26.2706 11.4753 26.1032 11.8811 25.7798 12.1216C23.8835 13.7203 22.4222 15.7999 21.5392 18.1566Z\"/><path d=\"M10.6793 18.1563C11.3067 17.7896 12.0151 17.5956 12.7362 17.5931H12.7557C14.2786 17.5167 15.7185 18.3115 16.5021 19.6609C17.2857 21.0103 17.2857 22.6952 16.5021 24.0446C15.7185 25.394 14.2786 26.1888 12.7557 26.1124C12.1206 26.1067 11.4946 25.9555 10.923 25.6698C9.11822 24.8676 7.9439 23.0403 7.93018 21.0129C7.93018 18.2066 9.66542 13.6804 13.5648 10.4115C14.0225 10.0227 14.699 10.0902 15.0759 10.5624C15.4527 11.0346 15.3873 11.7326 14.9296 12.1214C13.0278 13.7169 11.5626 15.7972 10.6793 18.1563Z\"/>'})];export{t as blockQuoteIcon,o as blockQuoteIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,aAAa;EAACC,CAAC,GAAC,CAAC,aAAa,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAA+iC,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,cAAc,EAACH,CAAC,IAAII,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}