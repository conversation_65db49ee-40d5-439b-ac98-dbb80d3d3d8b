{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"upload-cloud\",\n  d = [\"upload-cloud\", C({\n    outline: '<path d=\"M22.44 21.985C22.87 21.985 23.25 21.7353 23.42 21.3458C23.59 20.9563 23.5 20.4969 23.19 20.1973L17.97 14.9838L12.75 20.1973C12.4 20.6067 12.42 21.226 12.8 21.6155C13.18 22.005 13.8 22.025 14.22 21.6654L16.92 19.0087V32.9613C16.92 33.5406 17.39 34 17.96 34C18.53 34 19 33.5306 19 32.9613V19.0087L21.68 21.6854C21.88 21.8752 22.15 21.985 22.42 21.975L22.44 21.985ZM29.92 13.8152C29.98 13.3758 30 12.9263 30 12.4869C30 6.70412 25.29 2 19.5 2C14.95 2 10.94 4.93633 9.54 9.201C5.2 10.1199 2 13.995 2 18.4794C2 23.7129 6.26 27.9675 11.5 27.9675H14.93V25.97H11.5C7.36 25.97 4 22.6142 4 18.4794C4 14.764 6.78 11.578 10.46 11.0687L11.12 10.9788L11.29 10.3296C12.27 6.59426 15.64 3.9975 19.5 3.9975C24.19 3.9975 28 7.80275 28 12.4869C28 13.0662 27.94 13.6454 27.82 14.2147L27.65 15.0437L28.45 15.3433C30.57 16.1523 32 18.2097 32 20.4769C32 23.5031 29.53 25.97 26.5 25.97H21.02V27.9675H26.5C30.64 27.9675 34 24.6117 34 20.4769C34 17.6704 32.39 15.0836 29.92 13.8152Z\"/>',\n    outlineAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M23.8053 2.92235C22.4909 2.32989 21.0333 2 19.5 2C14.95 2 10.94 4.93633 9.54 9.201C5.2 10.1199 2 13.995 2 18.4794C2 23.7129 6.26 27.9675 11.5 27.9675H14.93V25.97H11.5C7.36 25.97 4 22.6142 4 18.4794C4 14.764 6.78 11.578 10.46 11.0687L11.12 10.9788L11.29 10.3296C12.27 6.59426 15.64 3.9975 19.5 3.9975C20.6576 3.9975 21.7616 4.22934 22.768 4.649L23.8053 2.92235Z\"/><path d=\"M27.6548 15.0204H31.625C33.1046 16.4133 34 18.384 34 20.4769C34 24.6117 30.64 27.9675 26.5 27.9675H21.02V25.97H26.5C29.53 25.97 32 23.5031 32 20.4769C32 18.2097 30.57 16.1523 28.45 15.3433L27.65 15.0437L27.6548 15.0204Z\"/><path d=\"M23.42 21.3458C23.25 21.7353 22.87 21.985 22.44 21.985L22.42 21.975C22.15 21.985 21.88 21.8752 21.68 21.6854L19 19.0087V32.9613C19 33.5306 18.53 34 17.96 34C17.39 34 16.92 33.5406 16.92 32.9613V19.0087L14.22 21.6654C13.8 22.025 13.18 22.005 12.8 21.6155C12.42 21.226 12.4 20.6067 12.75 20.1973L17.97 14.9838L23.19 20.1973C23.5 20.4969 23.59 20.9563 23.42 21.3458Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M23.7276 2.88775C22.4336 2.31706 21.0032 2 19.5 2C14.95 2 10.94 4.93633 9.54 9.201C5.2 10.1199 2 13.995 2 18.4794C2 23.7129 6.26 27.9675 11.5 27.9675H14.93V25.97H11.5C7.36 25.97 4 22.6142 4 18.4794C4 14.764 6.78 11.578 10.46 11.0687L11.12 10.9788L11.29 10.3296C12.27 6.59426 15.64 3.9975 19.5 3.9975C20.7873 3.9975 22.0083 4.28417 23.1024 4.79701C23.219 4.12258 23.4323 3.48123 23.7276 2.88775Z\"/><path d=\"M27.9974 12.696C28.629 12.884 29.2979 12.9853 29.9904 12.9863C29.9793 13.2632 29.9574 13.5412 29.92 13.8152C32.39 15.0836 34 17.6704 34 20.4769C34 24.6117 30.64 27.9675 26.5 27.9675H21.02V25.97H26.5C29.53 25.97 32 23.5031 32 20.4769C32 18.2097 30.57 16.1523 28.45 15.3433L27.65 15.0437L27.82 14.2147C27.9256 13.7139 27.9847 13.2054 27.9974 12.696Z\"/><path d=\"M23.42 21.3458C23.25 21.7353 22.87 21.985 22.44 21.985L22.42 21.975C22.15 21.985 21.88 21.8752 21.68 21.6854L19 19.0087V32.9613C19 33.5306 18.53 34 17.96 34C17.39 34 16.92 33.5406 16.92 32.9613V19.0087L14.22 21.6654C13.8 22.025 13.18 22.005 12.8 21.6155C12.42 21.226 12.4 20.6067 12.75 20.1973L17.97 14.9838L23.19 20.1973C23.5 20.4969 23.59 20.9563 23.42 21.3458Z\"/>'\n  })];\nexport { d as uploadCloudIcon, L as uploadCloudIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "d", "outline", "outlineAlerted", "outlineBadged", "uploadCloudIcon", "uploadCloudIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/upload-cloud.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"upload-cloud\",d=[\"upload-cloud\",C({outline:'<path d=\"M22.44 21.985C22.87 21.985 23.25 21.7353 23.42 21.3458C23.59 20.9563 23.5 20.4969 23.19 20.1973L17.97 14.9838L12.75 20.1973C12.4 20.6067 12.42 21.226 12.8 21.6155C13.18 22.005 13.8 22.025 14.22 21.6654L16.92 19.0087V32.9613C16.92 33.5406 17.39 34 17.96 34C18.53 34 19 33.5306 19 32.9613V19.0087L21.68 21.6854C21.88 21.8752 22.15 21.985 22.42 21.975L22.44 21.985ZM29.92 13.8152C29.98 13.3758 30 12.9263 30 12.4869C30 6.70412 25.29 2 19.5 2C14.95 2 10.94 4.93633 9.54 9.201C5.2 10.1199 2 13.995 2 18.4794C2 23.7129 6.26 27.9675 11.5 27.9675H14.93V25.97H11.5C7.36 25.97 4 22.6142 4 18.4794C4 14.764 6.78 11.578 10.46 11.0687L11.12 10.9788L11.29 10.3296C12.27 6.59426 15.64 3.9975 19.5 3.9975C24.19 3.9975 28 7.80275 28 12.4869C28 13.0662 27.94 13.6454 27.82 14.2147L27.65 15.0437L28.45 15.3433C30.57 16.1523 32 18.2097 32 20.4769C32 23.5031 29.53 25.97 26.5 25.97H21.02V27.9675H26.5C30.64 27.9675 34 24.6117 34 20.4769C34 17.6704 32.39 15.0836 29.92 13.8152Z\"/>',outlineAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M23.8053 2.92235C22.4909 2.32989 21.0333 2 19.5 2C14.95 2 10.94 4.93633 9.54 9.201C5.2 10.1199 2 13.995 2 18.4794C2 23.7129 6.26 27.9675 11.5 27.9675H14.93V25.97H11.5C7.36 25.97 4 22.6142 4 18.4794C4 14.764 6.78 11.578 10.46 11.0687L11.12 10.9788L11.29 10.3296C12.27 6.59426 15.64 3.9975 19.5 3.9975C20.6576 3.9975 21.7616 4.22934 22.768 4.649L23.8053 2.92235Z\"/><path d=\"M27.6548 15.0204H31.625C33.1046 16.4133 34 18.384 34 20.4769C34 24.6117 30.64 27.9675 26.5 27.9675H21.02V25.97H26.5C29.53 25.97 32 23.5031 32 20.4769C32 18.2097 30.57 16.1523 28.45 15.3433L27.65 15.0437L27.6548 15.0204Z\"/><path d=\"M23.42 21.3458C23.25 21.7353 22.87 21.985 22.44 21.985L22.42 21.975C22.15 21.985 21.88 21.8752 21.68 21.6854L19 19.0087V32.9613C19 33.5306 18.53 34 17.96 34C17.39 34 16.92 33.5406 16.92 32.9613V19.0087L14.22 21.6654C13.8 22.025 13.18 22.005 12.8 21.6155C12.42 21.226 12.4 20.6067 12.75 20.1973L17.97 14.9838L23.19 20.1973C23.5 20.4969 23.59 20.9563 23.42 21.3458Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M23.7276 2.88775C22.4336 2.31706 21.0032 2 19.5 2C14.95 2 10.94 4.93633 9.54 9.201C5.2 10.1199 2 13.995 2 18.4794C2 23.7129 6.26 27.9675 11.5 27.9675H14.93V25.97H11.5C7.36 25.97 4 22.6142 4 18.4794C4 14.764 6.78 11.578 10.46 11.0687L11.12 10.9788L11.29 10.3296C12.27 6.59426 15.64 3.9975 19.5 3.9975C20.7873 3.9975 22.0083 4.28417 23.1024 4.79701C23.219 4.12258 23.4323 3.48123 23.7276 2.88775Z\"/><path d=\"M27.9974 12.696C28.629 12.884 29.2979 12.9853 29.9904 12.9863C29.9793 13.2632 29.9574 13.5412 29.92 13.8152C32.39 15.0836 34 17.6704 34 20.4769C34 24.6117 30.64 27.9675 26.5 27.9675H21.02V25.97H26.5C29.53 25.97 32 23.5031 32 20.4769C32 18.2097 30.57 16.1523 28.45 15.3433L27.65 15.0437L27.82 14.2147C27.9256 13.7139 27.9847 13.2054 27.9974 12.696Z\"/><path d=\"M23.42 21.3458C23.25 21.7353 22.87 21.985 22.44 21.985L22.42 21.975C22.15 21.985 21.88 21.8752 21.68 21.6854L19 19.0087V32.9613C19 33.5306 18.53 34 17.96 34C17.39 34 16.92 33.5406 16.92 32.9613V19.0087L14.22 21.6654C13.8 22.025 13.18 22.005 12.8 21.6155C12.42 21.226 12.4 20.6067 12.75 20.1973L17.97 14.9838L23.19 20.1973C23.5 20.4969 23.59 20.9563 23.42 21.3458Z\"/>'})];export{d as uploadCloudIcon,L as uploadCloudIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,cAAc;EAACC,CAAC,GAAC,CAAC,cAAc,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,y8BAAy8B;IAACC,cAAc,EAAC,uzCAAuzC;IAACC,aAAa,EAAC;EAAovC,CAAC,CAAC,CAAC;AAAC,SAAOH,CAAC,IAAII,eAAe,EAACL,CAAC,IAAIM,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}