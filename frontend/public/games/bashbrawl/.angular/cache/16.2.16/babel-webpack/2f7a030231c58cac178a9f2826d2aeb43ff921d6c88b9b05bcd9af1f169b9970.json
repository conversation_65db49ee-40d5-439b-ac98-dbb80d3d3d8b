{"ast": null, "code": "import { renderIcon as V } from \"../icon.renderer.js\";\nconst C = \"factory\",\n  H = [\"factory\", V({\n    outline: '<path d=\"M6 26.0657H14V24.0152H6V26.0657ZM6 22.0147H14V19.9643H6V22.0147ZM20 30.0166H22V26.0157H20V30.0166ZM6 30.0666H14V28.0162H6V30.0666ZM26 26.0157H24V30.0166H26V26.0157ZM26 20.0143H24V24.0152H26V20.0143ZM33.86 7.51135C33.77 7.35131 33.63 7.22128 33.47 7.13126C33.31 7.05124 33.14 7.01123 32.96 7.02124C32.78 7.03124 32.61 7.09125 32.47 7.19128L23 13.6828V8.04147C23 7.85143 22.95 7.65138 22.84 7.49135C22.74 7.33131 22.59 7.19128 22.42 7.11126C22.25 7.03124 22.06 7.00123 21.87 7.02124C21.68 7.04124 21.5 7.12126 21.36 7.25129L13.48 13.9128H10V3.97052C10 3.73046 9.93 3.50041 9.79 3.32037C9.65 3.13032 9.45 3.00029 9.22 2.95028L5.22 2.02006C5.07 1.99006 4.92 1.99006 4.77 2.02006C4.62 2.06007 4.49 2.12009 4.37 2.22011C4.25 2.32013 4.15 2.45016 4.09 2.5902C4.03 2.73023 4 2.89027 4 3.05031V13.9128H3C2.73 13.9128 2.48 14.0229 2.29 14.2129C2.1 14.403 2 14.663 2 14.9331V32.9773C2 33.2474 2.11 33.5074 2.29 33.6975C2.48 33.8875 2.73 33.9976 3 33.9976H33C33.27 33.9976 33.52 33.8875 33.71 33.6975C33.9 33.5074 34 33.2474 34 32.9773V8.05148C34 7.86143 33.95 7.68139 33.86 7.52135V7.51135ZM6 4.36061L8 4.81072V13.9229H6V4.36061ZM32 31.9671H4V15.9733H13.83C14.06 15.9733 14.29 15.8933 14.47 15.7333L21 10.212V15.6132C21 15.8033 21.05 15.9833 21.14 16.1434C21.23 16.3034 21.37 16.4334 21.53 16.5235C21.69 16.6135 21.87 16.6535 22.05 16.6435C22.23 16.6435 22.41 16.5735 22.56 16.4735L32 9.98193V31.9771V31.9671ZM30 26.0257H28V30.0266H30V26.0257ZM30 20.0243H28V24.0252H30V20.0243ZM20 24.0252H22V20.0243H20V24.0252Z\"/>',\n    solid: '<path d=\"M33.86 7.51135C33.77 7.35131 33.63 7.22128 33.47 7.13126C33.31 7.05124 33.14 7.01123 32.96 7.02124C32.78 7.03124 32.61 7.09125 32.47 7.19128L23 13.6828V8.04147C23 7.85143 22.95 7.65138 22.84 7.49135C22.74 7.33131 22.59 7.19128 22.42 7.11126C22.25 7.03124 22.06 7.00123 21.87 7.02124C21.68 7.04124 21.5 7.12126 21.36 7.25129L13.48 13.9128H10V3.97052C10 3.73046 9.93 3.50041 9.79 3.32037C9.65 3.13032 9.45 3.00029 9.22 2.95028L5.22 2.02006C5.07 1.99006 4.92 1.99006 4.77 2.02006C4.62 2.06007 4.49 2.12009 4.37 2.22011C4.25 2.32013 4.15 2.45016 4.09 2.5902C4.03 2.73023 4 2.89027 4 3.05031V13.9128H3C2.73 13.9128 2.48 14.0229 2.29 14.2129C2.1 14.403 2 14.663 2 14.9331V32.9773C2 33.2474 2.11 33.5074 2.29 33.6975C2.48 33.8875 2.73 33.9976 3 33.9976H33C33.27 33.9976 33.52 33.8875 33.71 33.6975C33.9 33.5074 34 33.2474 34 32.9773V8.05148C34 7.86143 33.95 7.68139 33.86 7.52135V7.51135ZM14 30.0666H6V28.0162H14V30.0666ZM14 26.0657H6V24.0152H14V26.0657ZM14 22.0147H6V19.9643H14V22.0147ZM22 30.0166H20V26.0157H22V30.0166ZM22 24.0152H20V20.0143H22V24.0152ZM26 30.0166H24V26.0157H26V30.0166ZM26 24.0152H24V20.0143H26V24.0152ZM30 30.0166H28V26.0157H30V30.0166ZM30 24.0152H28V20.0143H30V24.0152Z\"/>'\n  })];\nexport { H as factoryIcon, C as factoryIconName };", "map": {"version": 3, "names": ["renderIcon", "V", "C", "H", "outline", "solid", "factoryIcon", "factoryIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/factory.js"], "sourcesContent": ["import{renderIcon as V}from\"../icon.renderer.js\";const C=\"factory\",H=[\"factory\",V({outline:'<path d=\"M6 26.0657H14V24.0152H6V26.0657ZM6 22.0147H14V19.9643H6V22.0147ZM20 30.0166H22V26.0157H20V30.0166ZM6 30.0666H14V28.0162H6V30.0666ZM26 26.0157H24V30.0166H26V26.0157ZM26 20.0143H24V24.0152H26V20.0143ZM33.86 7.51135C33.77 7.35131 33.63 7.22128 33.47 7.13126C33.31 7.05124 33.14 7.01123 32.96 7.02124C32.78 7.03124 32.61 7.09125 32.47 7.19128L23 13.6828V8.04147C23 7.85143 22.95 7.65138 22.84 7.49135C22.74 7.33131 22.59 7.19128 22.42 7.11126C22.25 7.03124 22.06 7.00123 21.87 7.02124C21.68 7.04124 21.5 7.12126 21.36 7.25129L13.48 13.9128H10V3.97052C10 3.73046 9.93 3.50041 9.79 3.32037C9.65 3.13032 9.45 3.00029 9.22 2.95028L5.22 2.02006C5.07 1.99006 4.92 1.99006 4.77 2.02006C4.62 2.06007 4.49 2.12009 4.37 2.22011C4.25 2.32013 4.15 2.45016 4.09 2.5902C4.03 2.73023 4 2.89027 4 3.05031V13.9128H3C2.73 13.9128 2.48 14.0229 2.29 14.2129C2.1 14.403 2 14.663 2 14.9331V32.9773C2 33.2474 2.11 33.5074 2.29 33.6975C2.48 33.8875 2.73 33.9976 3 33.9976H33C33.27 33.9976 33.52 33.8875 33.71 33.6975C33.9 33.5074 34 33.2474 34 32.9773V8.05148C34 7.86143 33.95 7.68139 33.86 7.52135V7.51135ZM6 4.36061L8 4.81072V13.9229H6V4.36061ZM32 31.9671H4V15.9733H13.83C14.06 15.9733 14.29 15.8933 14.47 15.7333L21 10.212V15.6132C21 15.8033 21.05 15.9833 21.14 16.1434C21.23 16.3034 21.37 16.4334 21.53 16.5235C21.69 16.6135 21.87 16.6535 22.05 16.6435C22.23 16.6435 22.41 16.5735 22.56 16.4735L32 9.98193V31.9771V31.9671ZM30 26.0257H28V30.0266H30V26.0257ZM30 20.0243H28V24.0252H30V20.0243ZM20 24.0252H22V20.0243H20V24.0252Z\"/>',solid:'<path d=\"M33.86 7.51135C33.77 7.35131 33.63 7.22128 33.47 7.13126C33.31 7.05124 33.14 7.01123 32.96 7.02124C32.78 7.03124 32.61 7.09125 32.47 7.19128L23 13.6828V8.04147C23 7.85143 22.95 7.65138 22.84 7.49135C22.74 7.33131 22.59 7.19128 22.42 7.11126C22.25 7.03124 22.06 7.00123 21.87 7.02124C21.68 7.04124 21.5 7.12126 21.36 7.25129L13.48 13.9128H10V3.97052C10 3.73046 9.93 3.50041 9.79 3.32037C9.65 3.13032 9.45 3.00029 9.22 2.95028L5.22 2.02006C5.07 1.99006 4.92 1.99006 4.77 2.02006C4.62 2.06007 4.49 2.12009 4.37 2.22011C4.25 2.32013 4.15 2.45016 4.09 2.5902C4.03 2.73023 4 2.89027 4 3.05031V13.9128H3C2.73 13.9128 2.48 14.0229 2.29 14.2129C2.1 14.403 2 14.663 2 14.9331V32.9773C2 33.2474 2.11 33.5074 2.29 33.6975C2.48 33.8875 2.73 33.9976 3 33.9976H33C33.27 33.9976 33.52 33.8875 33.71 33.6975C33.9 33.5074 34 33.2474 34 32.9773V8.05148C34 7.86143 33.95 7.68139 33.86 7.52135V7.51135ZM14 30.0666H6V28.0162H14V30.0666ZM14 26.0657H6V24.0152H14V26.0657ZM14 22.0147H6V19.9643H14V22.0147ZM22 30.0166H20V26.0157H22V30.0166ZM22 24.0152H20V20.0143H22V24.0152ZM26 30.0166H24V26.0157H26V30.0166ZM26 24.0152H24V20.0143H26V24.0152ZM30 30.0166H28V26.0157H30V30.0166ZM30 24.0152H28V20.0143H30V24.0152Z\"/>'})];export{H as factoryIcon,C as factoryIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,SAAS;EAACC,CAAC,GAAC,CAAC,SAAS,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,0+CAA0+C;IAACC,KAAK,EAAC;EAA8qC,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,WAAW,EAACJ,CAAC,IAAIK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}