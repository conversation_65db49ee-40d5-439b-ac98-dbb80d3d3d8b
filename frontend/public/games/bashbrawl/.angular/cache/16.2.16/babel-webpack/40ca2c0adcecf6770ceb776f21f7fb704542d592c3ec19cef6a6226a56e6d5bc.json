{"ast": null, "code": "import { renderIcon as n } from \"../icon.renderer.js\";\nconst o = \"window-min\",\n  i = [\"window-min\", n({\n    outline: '<path d=\"M27 27H9C8.44772 27 8 26.5523 8 26C8 25.4477 8.44772 25 9 25H27C27.5523 25 28 25.4477 28 26C28 26.5523 27.5523 27 27 27Z\"/>'\n  })];\nexport { i as windowMinIcon, o as windowMinIconName };", "map": {"version": 3, "names": ["renderIcon", "n", "o", "i", "outline", "windowMinIcon", "windowMinIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/window-min.js"], "sourcesContent": ["import{renderIcon as n}from\"../icon.renderer.js\";const o=\"window-min\",i=[\"window-min\",n({outline:'<path d=\"M27 27H9C8.44772 27 8 26.5523 8 26C8 25.4477 8.44772 25 9 25H27C27.5523 25 28 25.4477 28 26C28 26.5523 27.5523 27 27 27Z\"/>'})];export{i as windowMinIcon,o as windowMinIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,YAAY;EAACC,CAAC,GAAC,CAAC,YAAY,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAsI,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,aAAa,EAACH,CAAC,IAAII,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}