{"ast": null, "code": "class e {\n  constructor(e, t) {\n    this.target = e, this.eventName = t;\n  }\n  emit(e, t) {\n    this.target.dispatchEvent(new CustomEvent(this.eventName, {\n      detail: e,\n      ...t\n    }));\n  }\n}\nfunction t() {\n  return (t, n) => {\n    const r = {\n      get() {\n        return new e(this, void 0 !== n ? n : t.key);\n      },\n      enumerable: !0,\n      configurable: !0\n    };\n    return void 0 !== n ? function (e, t, n) {\n      Object.defineProperty(t, n, e);\n    }(r, t, n) : function (e, t) {\n      return {\n        kind: \"method\",\n        placement: \"prototype\",\n        key: t.key,\n        descriptor: e\n      };\n    }(r, t);\n  };\n}\nexport { e as EventEmitter, t as event };", "map": {"version": 3, "names": ["e", "constructor", "t", "target", "eventName", "emit", "dispatchEvent", "CustomEvent", "detail", "n", "r", "get", "key", "enumerable", "configurable", "Object", "defineProperty", "kind", "placement", "descriptor", "EventEmitter", "event"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/decorators/event.js"], "sourcesContent": ["class e{constructor(e,t){this.target=e,this.eventName=t}emit(e,t){this.target.dispatchEvent(new CustomEvent(this.eventName,{detail:e,...t}))}}function t(){return(t,n)=>{const r={get(){return new e(this,void 0!==n?n:t.key)},enumerable:!0,configurable:!0};return void 0!==n?function(e,t,n){Object.defineProperty(t,n,e)}(r,t,n):function(e,t){return{kind:\"method\",placement:\"prototype\",key:t.key,descriptor:e}}(r,t)}}export{e as EventEmitter,t as event};\n"], "mappings": "AAAA,MAAMA,CAAC;EAACC,WAAWA,CAACD,CAAC,EAACE,CAAC,EAAC;IAAC,IAAI,CAACC,MAAM,GAACH,CAAC,EAAC,IAAI,CAACI,SAAS,GAACF,CAAC;EAAA;EAACG,IAAIA,CAACL,CAAC,EAACE,CAAC,EAAC;IAAC,IAAI,CAACC,MAAM,CAACG,aAAa,CAAC,IAAIC,WAAW,CAAC,IAAI,CAACH,SAAS,EAAC;MAACI,MAAM,EAACR,CAAC;MAAC,GAAGE;IAAC,CAAC,CAAC,CAAC;EAAA;AAAC;AAAC,SAASA,CAACA,CAAA,EAAE;EAAC,OAAM,CAACA,CAAC,EAACO,CAAC,KAAG;IAAC,MAAMC,CAAC,GAAC;MAACC,GAAGA,CAAA,EAAE;QAAC,OAAO,IAAIX,CAAC,CAAC,IAAI,EAAC,KAAK,CAAC,KAAGS,CAAC,GAACA,CAAC,GAACP,CAAC,CAACU,GAAG,CAAC;MAAA,CAAC;MAACC,UAAU,EAAC,CAAC,CAAC;MAACC,YAAY,EAAC,CAAC;IAAC,CAAC;IAAC,OAAO,KAAK,CAAC,KAAGL,CAAC,GAAC,UAAST,CAAC,EAACE,CAAC,EAACO,CAAC,EAAC;MAACM,MAAM,CAACC,cAAc,CAACd,CAAC,EAACO,CAAC,EAACT,CAAC,CAAC;IAAA,CAAC,CAACU,CAAC,EAACR,CAAC,EAACO,CAAC,CAAC,GAAC,UAAST,CAAC,EAACE,CAAC,EAAC;MAAC,OAAM;QAACe,IAAI,EAAC,QAAQ;QAACC,SAAS,EAAC,WAAW;QAACN,GAAG,EAACV,CAAC,CAACU,GAAG;QAACO,UAAU,EAACnB;MAAC,CAAC;IAAA,CAAC,CAACU,CAAC,EAACR,CAAC,CAAC;EAAA,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAIoB,YAAY,EAAClB,CAAC,IAAImB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}