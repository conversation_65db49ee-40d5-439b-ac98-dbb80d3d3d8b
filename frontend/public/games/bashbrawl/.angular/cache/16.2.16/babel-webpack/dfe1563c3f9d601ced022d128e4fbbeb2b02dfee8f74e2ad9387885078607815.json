{"ast": null, "code": "import { PRIVATE_ANIMATION_STATUS_ATTR_NAME as t, AnimationStatus as i } from \"../motion/interfaces.js\";\nimport { runPropertyAnimations as o } from \"../motion/utils.js\";\nimport { isJsdomTest as n } from \"../utils/environment.js\";\nfunction e(e) {\n  return n() ? function () {} : function (n) {\n    return class extends n {\n      constructor() {\n        super(...arguments);\n        this._animationReady = !1, this._animationDemoMode = !1, this._animations = e;\n      }\n      updated(n) {\n        if (super.updated(n), !this._animationReady && !this.hasAttribute(t)) return this.setAttribute(t, i.ready), void (this._animationReady = !0);\n        !0 === this._animationDemoMode || this.hasAttribute(\"_demo-mode\") ? this._animationDemoMode = !0 : o(n, this);\n      }\n    };\n  };\n}\nexport { e as animate };", "map": {"version": 3, "names": ["PRIVATE_ANIMATION_STATUS_ATTR_NAME", "t", "AnimationStatus", "i", "runPropertyAnimations", "o", "isJsdomTest", "n", "e", "constructor", "arguments", "_animationReady", "_animationDemoMode", "_animations", "updated", "hasAttribute", "setAttribute", "ready", "animate"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/decorators/animate.js"], "sourcesContent": ["import{PRIVATE_ANIMATION_STATUS_ATTR_NAME as t,AnimationStatus as i}from\"../motion/interfaces.js\";import{runPropertyAnimations as o}from\"../motion/utils.js\";import{isJsdomTest as n}from\"../utils/environment.js\";function e(e){return n()?function(){}:function(n){return class extends n{constructor(){super(...arguments);this._animationReady=!1,this._animationDemoMode=!1,this._animations=e}updated(n){if(super.updated(n),!this._animationReady&&!this.hasAttribute(t))return this.setAttribute(t,i.ready),void(this._animationReady=!0);!0===this._animationDemoMode||this.hasAttribute(\"_demo-mode\")?this._animationDemoMode=!0:o(n,this)}}}}export{e as animate};\n"], "mappings": "AAAA,SAAOA,kCAAkC,IAAIC,CAAC,EAACC,eAAe,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,qBAAqB,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAASC,CAACA,CAACA,CAAC,EAAC;EAAC,OAAOD,CAAC,CAAC,CAAC,GAAC,YAAU,CAAC,CAAC,GAAC,UAASA,CAAC,EAAC;IAAC,OAAO,cAAcA,CAAC;MAACE,WAAWA,CAAA,EAAE;QAAC,KAAK,CAAC,GAAGC,SAAS,CAAC;QAAC,IAAI,CAACC,eAAe,GAAC,CAAC,CAAC,EAAC,IAAI,CAACC,kBAAkB,GAAC,CAAC,CAAC,EAAC,IAAI,CAACC,WAAW,GAACL,CAAC;MAAA;MAACM,OAAOA,CAACP,CAAC,EAAC;QAAC,IAAG,KAAK,CAACO,OAAO,CAACP,CAAC,CAAC,EAAC,CAAC,IAAI,CAACI,eAAe,IAAE,CAAC,IAAI,CAACI,YAAY,CAACd,CAAC,CAAC,EAAC,OAAO,IAAI,CAACe,YAAY,CAACf,CAAC,EAACE,CAAC,CAACc,KAAK,CAAC,EAAC,MAAK,IAAI,CAACN,eAAe,GAAC,CAAC,CAAC,CAAC;QAAC,CAAC,CAAC,KAAG,IAAI,CAACC,kBAAkB,IAAE,IAAI,CAACG,YAAY,CAAC,YAAY,CAAC,GAAC,IAAI,CAACH,kBAAkB,GAAC,CAAC,CAAC,GAACP,CAAC,CAACE,CAAC,EAAC,IAAI,CAAC;MAAA;IAAC,CAAC;EAAA,CAAC;AAAA;AAAC,SAAOC,CAAC,IAAIU,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}