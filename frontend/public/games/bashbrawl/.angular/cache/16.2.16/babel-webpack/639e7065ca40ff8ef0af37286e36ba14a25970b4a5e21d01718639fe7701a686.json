{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"vm\",\n  V = [\"vm\", C({\n    outline: '<path d=\"M10 4.6C10 4.27 10.27 4 10.6 4H23.4C23.73 4 24 4.27 24 4.6V8H26V4.6C26 3.17 24.83 2 23.4 2H10.6C9.17 2 8 3.17 8 4.6V14.01H10V4.6ZM29.4 10H16.6C15.17 10 14 11.17 14 12.6V13.97H16V12.6C16 12.27 16.27 12 16.6 12H24V17.41L26 17.51C26 17.51 26 17.42 26 17.4V12H29.4C29.73 12 30 12.27 30 12.6V25.4C30 25.73 29.73 26 29.4 26H22V18.6C22 17.17 20.83 16 19.4 16H6.6C5.17 16 4 17.17 4 18.6V31.4C4 32.83 5.17 34 6.6 34H19.4C20.83 34 22 32.83 22 31.4V28H29.4C30.83 28 32 26.83 32 25.4V12.6C32 11.17 30.83 10 29.4 10ZM20 26H16.6C16.27 26 16 25.73 16 25.4V19.99H14V25.4C14 26.83 15.17 28 16.6 28H20V31.4C20 31.73 19.73 32 19.4 32H6.6C6.27 32 6 31.73 6 31.4V18.6C6 18.27 6.27 18 6.6 18H19.4C19.73 18 20 18.27 20 18.6V26Z\"/>',\n    outlineAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.2692 2.1503L23.1594 4H10.6C10.27 4 10 4.27 10 4.6V14.01H8V4.6C8 3.17 9.17 2 10.6 2H23.4C23.7044 2 23.9971 2.05303 24.2692 2.1503ZM19.5594 10L19.5362 10.0387C19.1449 10.6284 18.9663 11.3177 19.0073 12H16.6C16.27 12 16 12.27 16 12.6V13.97H14V12.6C14 11.17 15.17 10 16.6 10H19.5594ZM24 15.0367V17.41L26 17.51V17.4V15.0367H24ZM30 15.0367H32V25.4C32 26.83 30.83 28 29.4 28H22V31.4C22 32.83 20.83 34 19.4 34H6.6C5.17 34 4 32.83 4 31.4V18.6C4 17.17 5.17 16 6.6 16H19.4C20.83 16 22 17.17 22 18.6V26H29.4C29.73 26 30 25.73 30 25.4V15.0367ZM16.6 26H20V18.6C20 18.27 19.73 18 19.4 18H6.6C6.27 18 6 18.27 6 18.6V31.4C6 31.73 6.27 32 6.6 32H19.4C19.73 32 20 31.73 20 31.4V28H16.6C15.17 28 14 26.83 14 25.4V19.99H16V25.4C16 25.73 16.27 26 16.6 26Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 12.7101C31.3663 12.8987 30.695 13 30 13V25.4C30 25.73 29.73 26 29.4 26H22V18.6C22 17.17 20.83 16 19.4 16H6.6C5.17 16 4 17.17 4 18.6V31.4C4 32.83 5.17 34 6.6 34H19.4C20.83 34 22 32.83 22 31.4V28H29.4C30.83 28 32 26.83 32 25.4V12.7101ZM16.6 26H20V18.6C20 18.27 19.73 18 19.4 18H6.6C6.27 18 6 18.27 6 18.6V31.4C6 31.73 6.27 32 6.6 32H19.4C19.73 32 20 31.73 20 31.4V28H16.6C15.17 28 14 26.83 14 25.4V19.99H16V25.4C16 25.73 16.27 26 16.6 26Z\"/><path d=\"M26.3924 12C25.5469 11.4905 24.8179 10.8074 24.2547 10H16.6C15.17 10 14 11.17 14 12.6V13.97H16V12.6C16 12.27 16.27 12 16.6 12H24V17.41L26 17.51V12H26.3924Z\"/><path d=\"M24.1739 2.11834C23.7913 2.69154 23.4909 3.3244 23.2899 4H10.6C10.27 4 10 4.27 10 4.6V14.01H8V4.6C8 3.17 9.17 2 10.6 2H23.4C23.6692 2 23.9292 2.04147 24.1739 2.11834Z\"/>',\n    solid: '<path d=\"M12 12.6C12 10.06 14.06 8 16.6 8H26V4.6C26 3.17 24.83 2 23.4 2H10.6C9.17 2 8 3.17 8 4.6V14H12V12.6ZM29.4 10H16.6C15.17 10 14 11.17 14 12.6V14H19.4C21.94 14 24 16.06 24 18.6V24H22V18.6C22 17.17 20.83 16 19.4 16H6.6C5.17 16 4 17.17 4 18.6V31.4C4 32.83 5.17 34 6.6 34H19.4C20.83 34 22 32.83 22 31.4V30H16.6C14.06 30 12 27.94 12 25.4V20H14V25.4C14 26.83 15.17 28 16.6 28H29.4C30.83 28 32 26.83 32 25.4V12.6C32 11.17 30.83 10 29.4 10Z\"/>',\n    solidAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M24.2692 2.1503L20.7594 8H16.6C14.06 8 12 10.06 12 12.6V14H8V4.6C8 3.17 9.17 2 10.6 2H23.4C23.7044 2 23.9971 2.05303 24.2692 2.1503Z\"/><path d=\"M19.5594 10L19.5362 10.0387C18.8703 11.0423 18.8204 12.3342 19.4206 13.3893C19.5543 13.6244 19.7141 13.8374 19.8949 14.0263C19.7323 14.0089 19.5672 14 19.4 14H14V12.6C14 11.17 15.17 10 16.6 10H19.5594Z\"/><path d=\"M22.3095 15.0371C22.3195 15.037 22.3295 15.0369 22.3395 15.0367H32V25.4C32 26.83 30.83 28 29.4 28H16.6C15.17 28 14 26.83 14 25.4V20H12V25.4C12 27.94 14.06 30 16.6 30H22V31.4C22 32.83 20.83 34 19.4 34H6.6C5.17 34 4 32.83 4 31.4V18.6C4 17.17 5.17 16 6.6 16H19.4C20.83 16 22 17.17 22 18.6V24H24V18.6C24 17.1636 23.3412 15.8808 22.3095 15.0371Z\"/>',\n    solidBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M32 12.7101C31.3663 12.8987 30.695 13 30 13C27.6213 13 25.5196 11.8135 24.2547 10H16.6C15.17 10 14 11.17 14 12.6V14H19.4C21.94 14 24 16.06 24 18.6V24H22V18.6C22 17.17 20.83 16 19.4 16H6.6C5.17 16 4 17.17 4 18.6V31.4C4 32.83 5.17 34 6.6 34H19.4C20.83 34 22 32.83 22 31.4V30H16.6C14.06 30 12 27.94 12 25.4V20H14V25.4C14 26.83 15.17 28 16.6 28H29.4C30.83 28 32 26.83 32 25.4V12.7101Z\"/><path d=\"M24.1739 2.11834C23.4323 3.2292 23 4.56411 23 6C23 6.69497 23.1013 7.36629 23.2899 8H16.6C14.06 8 12 10.06 12 12.6V14H8V4.6C8 3.17 9.17 2 10.6 2H23.4C23.6692 2 23.9292 2.04147 24.1739 2.11834Z\"/>'\n  })];\nexport { V as vmIcon, H as vmIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "V", "outline", "outlineAlerted", "outlineBadged", "solid", "solidAlerted", "solidBadged", "vmIcon", "vmIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/vm.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"vm\",V=[\"vm\",C({outline:'<path d=\"M10 4.6C10 4.27 10.27 4 10.6 4H23.4C23.73 4 24 4.27 24 4.6V8H26V4.6C26 3.17 24.83 2 23.4 2H10.6C9.17 2 8 3.17 8 4.6V14.01H10V4.6ZM29.4 10H16.6C15.17 10 14 11.17 14 12.6V13.97H16V12.6C16 12.27 16.27 12 16.6 12H24V17.41L26 17.51C26 17.51 26 17.42 26 17.4V12H29.4C29.73 12 30 12.27 30 12.6V25.4C30 25.73 29.73 26 29.4 26H22V18.6C22 17.17 20.83 16 19.4 16H6.6C5.17 16 4 17.17 4 18.6V31.4C4 32.83 5.17 34 6.6 34H19.4C20.83 34 22 32.83 22 31.4V28H29.4C30.83 28 32 26.83 32 25.4V12.6C32 11.17 30.83 10 29.4 10ZM20 26H16.6C16.27 26 16 25.73 16 25.4V19.99H14V25.4C14 26.83 15.17 28 16.6 28H20V31.4C20 31.73 19.73 32 19.4 32H6.6C6.27 32 6 31.73 6 31.4V18.6C6 18.27 6.27 18 6.6 18H19.4C19.73 18 20 18.27 20 18.6V26Z\"/>',outlineAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.2692 2.1503L23.1594 4H10.6C10.27 4 10 4.27 10 4.6V14.01H8V4.6C8 3.17 9.17 2 10.6 2H23.4C23.7044 2 23.9971 2.05303 24.2692 2.1503ZM19.5594 10L19.5362 10.0387C19.1449 10.6284 18.9663 11.3177 19.0073 12H16.6C16.27 12 16 12.27 16 12.6V13.97H14V12.6C14 11.17 15.17 10 16.6 10H19.5594ZM24 15.0367V17.41L26 17.51V17.4V15.0367H24ZM30 15.0367H32V25.4C32 26.83 30.83 28 29.4 28H22V31.4C22 32.83 20.83 34 19.4 34H6.6C5.17 34 4 32.83 4 31.4V18.6C4 17.17 5.17 16 6.6 16H19.4C20.83 16 22 17.17 22 18.6V26H29.4C29.73 26 30 25.73 30 25.4V15.0367ZM16.6 26H20V18.6C20 18.27 19.73 18 19.4 18H6.6C6.27 18 6 18.27 6 18.6V31.4C6 31.73 6.27 32 6.6 32H19.4C19.73 32 20 31.73 20 31.4V28H16.6C15.17 28 14 26.83 14 25.4V19.99H16V25.4C16 25.73 16.27 26 16.6 26Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 12.7101C31.3663 12.8987 30.695 13 30 13V25.4C30 25.73 29.73 26 29.4 26H22V18.6C22 17.17 20.83 16 19.4 16H6.6C5.17 16 4 17.17 4 18.6V31.4C4 32.83 5.17 34 6.6 34H19.4C20.83 34 22 32.83 22 31.4V28H29.4C30.83 28 32 26.83 32 25.4V12.7101ZM16.6 26H20V18.6C20 18.27 19.73 18 19.4 18H6.6C6.27 18 6 18.27 6 18.6V31.4C6 31.73 6.27 32 6.6 32H19.4C19.73 32 20 31.73 20 31.4V28H16.6C15.17 28 14 26.83 14 25.4V19.99H16V25.4C16 25.73 16.27 26 16.6 26Z\"/><path d=\"M26.3924 12C25.5469 11.4905 24.8179 10.8074 24.2547 10H16.6C15.17 10 14 11.17 14 12.6V13.97H16V12.6C16 12.27 16.27 12 16.6 12H24V17.41L26 17.51V12H26.3924Z\"/><path d=\"M24.1739 2.11834C23.7913 2.69154 23.4909 3.3244 23.2899 4H10.6C10.27 4 10 4.27 10 4.6V14.01H8V4.6C8 3.17 9.17 2 10.6 2H23.4C23.6692 2 23.9292 2.04147 24.1739 2.11834Z\"/>',solid:'<path d=\"M12 12.6C12 10.06 14.06 8 16.6 8H26V4.6C26 3.17 24.83 2 23.4 2H10.6C9.17 2 8 3.17 8 4.6V14H12V12.6ZM29.4 10H16.6C15.17 10 14 11.17 14 12.6V14H19.4C21.94 14 24 16.06 24 18.6V24H22V18.6C22 17.17 20.83 16 19.4 16H6.6C5.17 16 4 17.17 4 18.6V31.4C4 32.83 5.17 34 6.6 34H19.4C20.83 34 22 32.83 22 31.4V30H16.6C14.06 30 12 27.94 12 25.4V20H14V25.4C14 26.83 15.17 28 16.6 28H29.4C30.83 28 32 26.83 32 25.4V12.6C32 11.17 30.83 10 29.4 10Z\"/>',solidAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M24.2692 2.1503L20.7594 8H16.6C14.06 8 12 10.06 12 12.6V14H8V4.6C8 3.17 9.17 2 10.6 2H23.4C23.7044 2 23.9971 2.05303 24.2692 2.1503Z\"/><path d=\"M19.5594 10L19.5362 10.0387C18.8703 11.0423 18.8204 12.3342 19.4206 13.3893C19.5543 13.6244 19.7141 13.8374 19.8949 14.0263C19.7323 14.0089 19.5672 14 19.4 14H14V12.6C14 11.17 15.17 10 16.6 10H19.5594Z\"/><path d=\"M22.3095 15.0371C22.3195 15.037 22.3295 15.0369 22.3395 15.0367H32V25.4C32 26.83 30.83 28 29.4 28H16.6C15.17 28 14 26.83 14 25.4V20H12V25.4C12 27.94 14.06 30 16.6 30H22V31.4C22 32.83 20.83 34 19.4 34H6.6C5.17 34 4 32.83 4 31.4V18.6C4 17.17 5.17 16 6.6 16H19.4C20.83 16 22 17.17 22 18.6V24H24V18.6C24 17.1636 23.3412 15.8808 22.3095 15.0371Z\"/>',solidBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M32 12.7101C31.3663 12.8987 30.695 13 30 13C27.6213 13 25.5196 11.8135 24.2547 10H16.6C15.17 10 14 11.17 14 12.6V14H19.4C21.94 14 24 16.06 24 18.6V24H22V18.6C22 17.17 20.83 16 19.4 16H6.6C5.17 16 4 17.17 4 18.6V31.4C4 32.83 5.17 34 6.6 34H19.4C20.83 34 22 32.83 22 31.4V30H16.6C14.06 30 12 27.94 12 25.4V20H14V25.4C14 26.83 15.17 28 16.6 28H29.4C30.83 28 32 26.83 32 25.4V12.7101Z\"/><path d=\"M24.1739 2.11834C23.4323 3.2292 23 4.56411 23 6C23 6.69497 23.1013 7.36629 23.2899 8H16.6C14.06 8 12 10.06 12 12.6V14H8V4.6C8 3.17 9.17 2 10.6 2H23.4C23.6692 2 23.9292 2.04147 24.1739 2.11834Z\"/>'})];export{V as vmIcon,H as vmIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,IAAI;EAACC,CAAC,GAAC,CAAC,IAAI,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,8sBAA8sB;IAACC,cAAc,EAAC,0nCAA0nC;IAACC,aAAa,EAAC,q8BAAq8B;IAACC,KAAK,EAAC,2bAA2b;IAACC,YAAY,EAAC,0iCAA0iC;IAACC,WAAW,EAAC;EAAotB,CAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,MAAM,EAACR,CAAC,IAAIS,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}