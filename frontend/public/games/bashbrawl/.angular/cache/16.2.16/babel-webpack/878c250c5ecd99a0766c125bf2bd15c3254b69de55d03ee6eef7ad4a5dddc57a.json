{"ast": null, "code": "function n(n) {\n  return n;\n}\nexport { n as renderIcon };", "map": {"version": 3, "names": ["n", "renderIcon"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/icon.renderer.js"], "sourcesContent": ["function n(n){return n}export{n as renderIcon};\n"], "mappings": "AAAA,SAASA,CAACA,CAACA,CAAC,EAAC;EAAC,OAAOA,CAAC;AAAA;AAAC,SAAOA,CAAC,IAAIC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}