{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"scissors\",\n  s = [\"scissors\", C({\n    outline: '<path d=\"M33.9976 6.91084L31.0823 4.73025C29.8244 3.79 28.1771 3.77 27.039 4.71025L15.3182 15.2231L11.205 11.6821C11.7141 10.8919 11.9937 9.97166 11.9937 9.0014C11.9937 7.66104 11.4745 6.4107 10.5361 5.46045C8.56928 3.50993 5.40447 3.50993 3.45766 5.46045C1.51086 7.41097 1.51086 10.5818 3.45766 12.5323C4.42607 13.5026 5.70398 13.9927 6.99187 13.9927C7.98024 13.9927 8.95864 13.6927 9.80725 13.1125L13.8107 16.5634L7.75062 21.9949L7.82051 22.0749C6.28303 21.8148 4.64571 22.265 3.45766 23.4553C1.51086 25.4058 1.51086 28.5766 3.45766 30.5272C4.42607 31.4974 5.70398 31.9876 6.99187 31.9876C8.27975 31.9876 9.54767 31.4974 10.5261 30.5272C11.4645 29.5869 11.9837 28.3266 11.9837 26.9862C11.9837 25.6459 11.4645 24.3955 10.5261 23.4453C10.2964 23.2152 10.0369 23.0152 9.7773 22.8351L28.2869 6.24066C28.7062 5.90057 29.3751 5.94058 29.9043 6.34069L30.9126 7.09089L19.3815 18.0038L30.9126 28.9167L29.9043 29.6669C29.3652 30.067 28.7062 30.1071 28.3169 29.797L17.245 19.9143L15.7974 21.2847L27.0589 31.3274C27.608 31.7775 28.2869 31.9976 28.9758 31.9976C29.6647 31.9976 30.4434 31.7575 31.0823 31.2774L33.9976 29.0968L22.2768 18.0038L33.9976 6.91084ZM9.10839 11.122C7.94031 12.2923 6.04342 12.2923 4.87534 11.122C4.30627 10.5518 3.99678 9.80161 3.99678 9.0014C3.99678 8.20118 4.30627 7.45098 4.87534 6.88083C5.4444 6.31068 6.22313 6.0006 6.99187 6.0006C7.7606 6.0006 8.52934 6.29067 9.10839 6.88083C9.68744 7.47099 9.98695 8.20118 9.98695 9.0014C9.98695 9.80161 9.67746 10.5518 9.10839 11.122ZM9.98695 27.0062C9.98695 27.8064 9.67746 28.5566 9.10839 29.1268C7.94031 30.2971 6.04342 30.2971 4.87534 29.1268C4.30627 28.5566 3.99678 27.8064 3.99678 27.0062C3.99678 26.206 4.30627 25.4558 4.87534 24.8857C5.4444 24.3155 6.22313 24.0054 6.99187 24.0054C7.7606 24.0054 8.52934 24.2955 9.10839 24.8857C9.68744 25.4758 9.98695 26.206 9.98695 27.0062Z\"/>',\n    solid: '<path d=\"M33.9976 5.98959L32.0114 4.51993C31.1032 3.85008 29.9355 3.82009 29.157 4.44994L20.7834 11.6983L20.7634 11.6783C15.7632 16.0073 8.52731 22.1259 8.40754 22.2259C6.71085 21.726 4.80458 22.1259 3.46719 23.4656C1.52099 25.4151 1.52099 28.5844 3.46719 30.5339C4.4353 31.5037 5.7128 31.9936 7.00029 31.9936C8.28777 31.9936 9.5553 31.5037 10.5334 30.5339C12.4796 28.5844 12.4796 25.4151 10.5334 23.4656C10.4535 23.3856 10.3637 23.3356 10.2839 23.2656C11.1522 22.5258 12.9087 21.0461 14.9747 19.2765L29.147 31.5437C29.9255 32.1736 31.0932 32.1436 32.0015 31.4737L33.9876 30.0041L21.1327 17.9968L33.9976 5.98959ZM9.11616 29.1143C7.94844 30.284 6.05214 30.284 4.88442 29.1143C4.31553 28.5444 4.00614 27.7946 4.00614 26.9948C4.00614 26.1949 4.31553 25.4451 4.88442 24.8752C5.45331 24.3054 6.23179 23.9954 7.00029 23.9954C7.76879 23.9954 8.53729 24.2854 9.11616 24.8752C9.69502 25.4651 9.99444 26.1949 9.99444 26.9948C9.99444 27.7946 9.68504 28.5444 9.11616 29.1143ZM6.99031 13.9977C7.99834 13.9977 9.00637 13.6778 9.86469 13.068L12.9487 15.8273L14.4857 14.4976L11.232 11.6083C12.4097 9.67874 12.1802 7.12933 10.5234 5.45971C8.56723 3.52016 5.40341 3.52016 3.45721 5.46971C1.51101 7.41926 1.51101 10.5885 3.45721 12.5381C4.42532 13.5079 5.70282 13.9977 6.99031 13.9977ZM4.87444 6.87938C5.44333 6.30952 6.19187 5.99959 6.99031 5.99959C7.78875 5.99959 8.53729 6.30952 9.10617 6.87938C10.2739 8.04912 10.2739 9.94868 9.10617 11.1184C7.93846 12.2881 6.00224 12.2481 4.87444 11.1184C3.74664 9.98867 3.70672 8.04912 4.87444 6.87938Z\"/>'\n  })];\nexport { s as scissorsIcon, L as scissorsIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "s", "outline", "solid", "scissorsIcon", "scissorsIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/scissors.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"scissors\",s=[\"scissors\",C({outline:'<path d=\"M33.9976 6.91084L31.0823 4.73025C29.8244 3.79 28.1771 3.77 27.039 4.71025L15.3182 15.2231L11.205 11.6821C11.7141 10.8919 11.9937 9.97166 11.9937 9.0014C11.9937 7.66104 11.4745 6.4107 10.5361 5.46045C8.56928 3.50993 5.40447 3.50993 3.45766 5.46045C1.51086 7.41097 1.51086 10.5818 3.45766 12.5323C4.42607 13.5026 5.70398 13.9927 6.99187 13.9927C7.98024 13.9927 8.95864 13.6927 9.80725 13.1125L13.8107 16.5634L7.75062 21.9949L7.82051 22.0749C6.28303 21.8148 4.64571 22.265 3.45766 23.4553C1.51086 25.4058 1.51086 28.5766 3.45766 30.5272C4.42607 31.4974 5.70398 31.9876 6.99187 31.9876C8.27975 31.9876 9.54767 31.4974 10.5261 30.5272C11.4645 29.5869 11.9837 28.3266 11.9837 26.9862C11.9837 25.6459 11.4645 24.3955 10.5261 23.4453C10.2964 23.2152 10.0369 23.0152 9.7773 22.8351L28.2869 6.24066C28.7062 5.90057 29.3751 5.94058 29.9043 6.34069L30.9126 7.09089L19.3815 18.0038L30.9126 28.9167L29.9043 29.6669C29.3652 30.067 28.7062 30.1071 28.3169 29.797L17.245 19.9143L15.7974 21.2847L27.0589 31.3274C27.608 31.7775 28.2869 31.9976 28.9758 31.9976C29.6647 31.9976 30.4434 31.7575 31.0823 31.2774L33.9976 29.0968L22.2768 18.0038L33.9976 6.91084ZM9.10839 11.122C7.94031 12.2923 6.04342 12.2923 4.87534 11.122C4.30627 10.5518 3.99678 9.80161 3.99678 9.0014C3.99678 8.20118 4.30627 7.45098 4.87534 6.88083C5.4444 6.31068 6.22313 6.0006 6.99187 6.0006C7.7606 6.0006 8.52934 6.29067 9.10839 6.88083C9.68744 7.47099 9.98695 8.20118 9.98695 9.0014C9.98695 9.80161 9.67746 10.5518 9.10839 11.122ZM9.98695 27.0062C9.98695 27.8064 9.67746 28.5566 9.10839 29.1268C7.94031 30.2971 6.04342 30.2971 4.87534 29.1268C4.30627 28.5566 3.99678 27.8064 3.99678 27.0062C3.99678 26.206 4.30627 25.4558 4.87534 24.8857C5.4444 24.3155 6.22313 24.0054 6.99187 24.0054C7.7606 24.0054 8.52934 24.2955 9.10839 24.8857C9.68744 25.4758 9.98695 26.206 9.98695 27.0062Z\"/>',solid:'<path d=\"M33.9976 5.98959L32.0114 4.51993C31.1032 3.85008 29.9355 3.82009 29.157 4.44994L20.7834 11.6983L20.7634 11.6783C15.7632 16.0073 8.52731 22.1259 8.40754 22.2259C6.71085 21.726 4.80458 22.1259 3.46719 23.4656C1.52099 25.4151 1.52099 28.5844 3.46719 30.5339C4.4353 31.5037 5.7128 31.9936 7.00029 31.9936C8.28777 31.9936 9.5553 31.5037 10.5334 30.5339C12.4796 28.5844 12.4796 25.4151 10.5334 23.4656C10.4535 23.3856 10.3637 23.3356 10.2839 23.2656C11.1522 22.5258 12.9087 21.0461 14.9747 19.2765L29.147 31.5437C29.9255 32.1736 31.0932 32.1436 32.0015 31.4737L33.9876 30.0041L21.1327 17.9968L33.9976 5.98959ZM9.11616 29.1143C7.94844 30.284 6.05214 30.284 4.88442 29.1143C4.31553 28.5444 4.00614 27.7946 4.00614 26.9948C4.00614 26.1949 4.31553 25.4451 4.88442 24.8752C5.45331 24.3054 6.23179 23.9954 7.00029 23.9954C7.76879 23.9954 8.53729 24.2854 9.11616 24.8752C9.69502 25.4651 9.99444 26.1949 9.99444 26.9948C9.99444 27.7946 9.68504 28.5444 9.11616 29.1143ZM6.99031 13.9977C7.99834 13.9977 9.00637 13.6778 9.86469 13.068L12.9487 15.8273L14.4857 14.4976L11.232 11.6083C12.4097 9.67874 12.1802 7.12933 10.5234 5.45971C8.56723 3.52016 5.40341 3.52016 3.45721 5.46971C1.51101 7.41926 1.51101 10.5885 3.45721 12.5381C4.42532 13.5079 5.70282 13.9977 6.99031 13.9977ZM4.87444 6.87938C5.44333 6.30952 6.19187 5.99959 6.99031 5.99959C7.78875 5.99959 8.53729 6.30952 9.10617 6.87938C10.2739 8.04912 10.2739 9.94868 9.10617 11.1184C7.93846 12.2881 6.00224 12.2481 4.87444 11.1184C3.74664 9.98867 3.70672 8.04912 4.87444 6.87938Z\"/>'})];export{s as scissorsIcon,L as scissorsIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,UAAU;EAACC,CAAC,GAAC,CAAC,UAAU,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,mzDAAmzD;IAACC,KAAK,EAAC;EAAw/C,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,YAAY,EAACJ,CAAC,IAAIK,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}