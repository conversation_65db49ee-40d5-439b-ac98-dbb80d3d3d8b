{"ast": null, "code": "import { renderIcon as a } from \"../icon.renderer.js\";\nconst t = \"align-top\",\n  o = [\"align-top\", a({\n    outline: '<path d=\"M34,4H2A1,1,0,0,0,2,6H34a1,1,0,0,0,0-2Z\"/><path d=\"M6,31a1,1,0,0,0,1,1h8a1,1,0,0,0,1-1V8H6ZM8,10h6V30H8Z\"/><path d=\"M20,23a1,1,0,0,0,1,1h8a1,1,0,0,0,1-1V8H20Zm2-13h6V22H22Z\"/>'\n  })];\nexport { o as alignTopIcon, t as alignTopIconName };", "map": {"version": 3, "names": ["renderIcon", "a", "t", "o", "outline", "alignTopIcon", "alignTopIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/align-top.js"], "sourcesContent": ["import{renderIcon as a}from\"../icon.renderer.js\";const t=\"align-top\",o=[\"align-top\",a({outline:'<path d=\"M34,4H2A1,1,0,0,0,2,6H34a1,1,0,0,0,0-2Z\"/><path d=\"M6,31a1,1,0,0,0,1,1h8a1,1,0,0,0,1-1V8H6ZM8,10h6V30H8Z\"/><path d=\"M20,23a1,1,0,0,0,1,1h8a1,1,0,0,0,1-1V8H20Zm2-13h6V22H22Z\"/>'})];export{o as alignTopIcon,t as alignTopIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,WAAW;EAACC,CAAC,GAAC,CAAC,WAAW,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAA0L,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,YAAY,EAACH,CAAC,IAAII,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}