{"ast": null, "code": "import { renderIcon as e } from \"../icon.renderer.js\";\nconst C = \"underline\",\n  n = [\"underline\", e({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M26 17.96C26 23.88 22.79 26.96 17.99 26.96C13.19 26.96 9.99 23.88 10 18.13V8.15C10 7.51 10.49 7 11.09 7C11.69 7 12.18 7.51 12.18 8.15V17.99C12.18 22.37 14.5 24.84 18.05 24.84C21.6 24.84 23.84 22.57 23.84 18.13V8.15C23.84 7.51 24.33 7 24.93 7C25.53 7 26.02 7.51 26.02 8.15V17.96H26ZM4 29.9C4 29.33 4.44 28.85 5 28.8H31C31.57 28.85 32 29.33 32 29.9C32 30.47 31.56 30.95 31 31H5C4.43 30.95 4 30.47 4 29.9Z\"/>'\n  })];\nexport { n as underlineIcon, C as underlineIconName };", "map": {"version": 3, "names": ["renderIcon", "e", "C", "n", "outline", "underlineIcon", "underlineIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/underline.js"], "sourcesContent": ["import{renderIcon as e}from\"../icon.renderer.js\";const C=\"underline\",n=[\"underline\",e({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M26 17.96C26 23.88 22.79 26.96 17.99 26.96C13.19 26.96 9.99 23.88 10 18.13V8.15C10 7.51 10.49 7 11.09 7C11.69 7 12.18 7.51 12.18 8.15V17.99C12.18 22.37 14.5 24.84 18.05 24.84C21.6 24.84 23.84 22.57 23.84 18.13V8.15C23.84 7.51 24.33 7 24.93 7C25.53 7 26.02 7.51 26.02 8.15V17.96H26ZM4 29.9C4 29.33 4.44 28.85 5 28.8H31C31.57 28.85 32 29.33 32 29.9C32 30.47 31.56 30.95 31 31H5C4.43 30.95 4 30.47 4 29.9Z\"/>'})];export{n as underlineIcon,C as underlineIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,WAAW;EAACC,CAAC,GAAC,CAAC,WAAW,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAwc,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,aAAa,EAACH,CAAC,IAAII,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}