{"ast": null, "code": "import _curry2 from \"./internal/_curry2.js\";\n/**\n * See if an object (i.e. `val`) is an instance of the supplied constructor. This\n * function will check up the inheritance chain, if any.\n * If `val` was created using `Object.create`, `R.is(Object, val) === true`.\n *\n * @func\n * @memberOf R\n * @since v0.3.0\n * @category Type\n * @sig (* -> {*}) -> a -> Boolean\n * @param {Object} ctor A constructor\n * @param {*} val The value to test\n * @return {Boolean}\n * @example\n *\n *      R.is(Object, {}); //=> true\n *      R.is(Number, 1); //=> true\n *      R.is(Object, 1); //=> false\n *      R.is(String, 's'); //=> true\n *      R.is(String, new String('')); //=> true\n *      R.is(Object, new String('')); //=> true\n *      R.is(Object, 's'); //=> false\n *      R.is(Number, {}); //=> false\n */\n\nvar is = /*#__PURE__*/\n_curry2(function is(Ctor, val) {\n  return val instanceof Ctor || val != null && (val.constructor === Ctor || Ctor.name === 'Object' && typeof val === 'object');\n});\nexport default is;", "map": {"version": 3, "names": ["_curry2", "is", "Ctor", "val", "constructor", "name"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/is.js"], "sourcesContent": ["import _curry2 from \"./internal/_curry2.js\";\n/**\n * See if an object (i.e. `val`) is an instance of the supplied constructor. This\n * function will check up the inheritance chain, if any.\n * If `val` was created using `Object.create`, `R.is(Object, val) === true`.\n *\n * @func\n * @memberOf R\n * @since v0.3.0\n * @category Type\n * @sig (* -> {*}) -> a -> Boolean\n * @param {Object} ctor A constructor\n * @param {*} val The value to test\n * @return {Boolean}\n * @example\n *\n *      R.is(Object, {}); //=> true\n *      R.is(Number, 1); //=> true\n *      R.is(Object, 1); //=> false\n *      R.is(String, 's'); //=> true\n *      R.is(String, new String('')); //=> true\n *      R.is(Object, new String('')); //=> true\n *      R.is(Object, 's'); //=> false\n *      R.is(Number, {}); //=> false\n */\n\nvar is =\n/*#__PURE__*/\n_curry2(function is(Ctor, val) {\n  return val instanceof Ctor || val != null && (val.constructor === Ctor || Ctor.name === 'Object' && typeof val === 'object');\n});\n\nexport default is;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,uBAAuB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,EAAE,GACN;AACAD,OAAO,CAAC,SAASC,EAAEA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAC7B,OAAOA,GAAG,YAAYD,IAAI,IAAIC,GAAG,IAAI,IAAI,KAAKA,GAAG,CAACC,WAAW,KAAKF,IAAI,IAAIA,IAAI,CAACG,IAAI,KAAK,QAAQ,IAAI,OAAOF,GAAG,KAAK,QAAQ,CAAC;AAC9H,CAAC,CAAC;AAEF,eAAeF,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}