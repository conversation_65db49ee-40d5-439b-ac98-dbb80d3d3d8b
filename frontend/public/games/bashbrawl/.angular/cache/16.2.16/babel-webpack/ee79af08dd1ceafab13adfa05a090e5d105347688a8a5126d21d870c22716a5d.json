{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ignoreFocus as s, focusable as t, focusElement as o } from \"../utils/focus.js\";\nimport { listenForAttributeChange as i } from \"../utils/events.js\";\nimport { getFlattenedDOMTree as c } from \"../utils/traversal.js\";\nfunction h(s = {\n  fallback: \"focusable\"\n}) {\n  return t => t.addInitializer(t => new e(t, s));\n}\nclass e {\n  constructor(s, t = {\n    fallback: \"focusable\"\n  }) {\n    this.host = s, this.config = t, this.host.addController(this);\n  }\n  hostConnected() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.host.updateComplete, _this.observer = i(_this.host, \"hidden\", () => _this.cdsFocusFirst()), _this.cdsFocusFirst();\n    })();\n  }\n  hostDisconnected() {\n    this.observer?.disconnect();\n  }\n  cdsFocusFirst() {\n    if (!s(this.host)) {\n      const s = this.host.shadowRoot ? this.host.shadowRoot : this.host,\n        i = s.querySelector(\".private-host\") ?? this.host,\n        h = c(s).filter(s => !s.hasAttribute(\"cds-focus-boundary\")),\n        e = h.find(s => s.hasAttribute(\"cds-first-focus\")),\n        r = \"focusable\" === this.config.fallback ? h.find(s => t(s) && !s.classList.contains(\"private-host\")) : null,\n        a = \"none\" === this.config.fallback ? null : i,\n        n = e ?? r ?? a;\n      n && o(n);\n    }\n  }\n}\nexport { e as FirstFocusController, h as firstFocus };", "map": {"version": 3, "names": ["ignoreFocus", "s", "focusable", "t", "focusElement", "o", "listenForAttributeChange", "i", "getFlattenedDOMTree", "c", "h", "fallback", "addInitializer", "e", "constructor", "host", "config", "addController", "hostConnected", "_this", "_asyncToGenerator", "updateComplete", "observer", "cdsFocusFirst", "hostDisconnected", "disconnect", "shadowRoot", "querySelector", "filter", "hasAttribute", "find", "r", "classList", "contains", "a", "n", "FirstFocusController", "firstFocus"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/first-focus.controller.js"], "sourcesContent": ["import{ignoreFocus as s,focusable as t,focusElement as o}from\"../utils/focus.js\";import{listenForAttributeChange as i}from\"../utils/events.js\";import{getFlattenedDOMTree as c}from\"../utils/traversal.js\";function h(s={fallback:\"focusable\"}){return t=>t.addInitializer((t=>new e(t,s)))}class e{constructor(s,t={fallback:\"focusable\"}){this.host=s,this.config=t,this.host.addController(this)}async hostConnected(){await this.host.updateComplete,this.observer=i(this.host,\"hidden\",(()=>this.cdsFocusFirst())),this.cdsFocusFirst()}hostDisconnected(){this.observer?.disconnect()}cdsFocusFirst(){if(!s(this.host)){const s=this.host.shadowRoot?this.host.shadowRoot:this.host,i=s.querySelector(\".private-host\")??this.host,h=c(s).filter((s=>!s.hasAttribute(\"cds-focus-boundary\"))),e=h.find((s=>s.hasAttribute(\"cds-first-focus\"))),r=\"focusable\"===this.config.fallback?h.find((s=>t(s)&&!s.classList.contains(\"private-host\"))):null,a=\"none\"===this.config.fallback?null:i,n=e??r??a;n&&o(n)}}}export{e as FirstFocusController,h as firstFocus};\n"], "mappings": ";AAAA,SAAOA,WAAW,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,EAACC,YAAY,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,wBAAwB,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAASC,CAACA,CAACT,CAAC,GAAC;EAACU,QAAQ,EAAC;AAAW,CAAC,EAAC;EAAC,OAAOR,CAAC,IAAEA,CAAC,CAACS,cAAc,CAAET,CAAC,IAAE,IAAIU,CAAC,CAACV,CAAC,EAACF,CAAC,CAAE,CAAC;AAAA;AAAC,MAAMY,CAAC;EAACC,WAAWA,CAACb,CAAC,EAACE,CAAC,GAAC;IAACQ,QAAQ,EAAC;EAAW,CAAC,EAAC;IAAC,IAAI,CAACI,IAAI,GAACd,CAAC,EAAC,IAAI,CAACe,MAAM,GAACb,CAAC,EAAC,IAAI,CAACY,IAAI,CAACE,aAAa,CAAC,IAAI,CAAC;EAAA;EAAOC,aAAaA,CAAA,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAAC,MAAMD,KAAI,CAACJ,IAAI,CAACM,cAAc,EAACF,KAAI,CAACG,QAAQ,GAACf,CAAC,CAACY,KAAI,CAACJ,IAAI,EAAC,QAAQ,EAAE,MAAII,KAAI,CAACI,aAAa,CAAC,CAAE,CAAC,EAACJ,KAAI,CAACI,aAAa,CAAC,CAAC;IAAA;EAAA;EAACC,gBAAgBA,CAAA,EAAE;IAAC,IAAI,CAACF,QAAQ,EAAEG,UAAU,CAAC,CAAC;EAAA;EAACF,aAAaA,CAAA,EAAE;IAAC,IAAG,CAACtB,CAAC,CAAC,IAAI,CAACc,IAAI,CAAC,EAAC;MAAC,MAAMd,CAAC,GAAC,IAAI,CAACc,IAAI,CAACW,UAAU,GAAC,IAAI,CAACX,IAAI,CAACW,UAAU,GAAC,IAAI,CAACX,IAAI;QAACR,CAAC,GAACN,CAAC,CAAC0B,aAAa,CAAC,eAAe,CAAC,IAAE,IAAI,CAACZ,IAAI;QAACL,CAAC,GAACD,CAAC,CAACR,CAAC,CAAC,CAAC2B,MAAM,CAAE3B,CAAC,IAAE,CAACA,CAAC,CAAC4B,YAAY,CAAC,oBAAoB,CAAE,CAAC;QAAChB,CAAC,GAACH,CAAC,CAACoB,IAAI,CAAE7B,CAAC,IAAEA,CAAC,CAAC4B,YAAY,CAAC,iBAAiB,CAAE,CAAC;QAACE,CAAC,GAAC,WAAW,KAAG,IAAI,CAACf,MAAM,CAACL,QAAQ,GAACD,CAAC,CAACoB,IAAI,CAAE7B,CAAC,IAAEE,CAAC,CAACF,CAAC,CAAC,IAAE,CAACA,CAAC,CAAC+B,SAAS,CAACC,QAAQ,CAAC,cAAc,CAAE,CAAC,GAAC,IAAI;QAACC,CAAC,GAAC,MAAM,KAAG,IAAI,CAAClB,MAAM,CAACL,QAAQ,GAAC,IAAI,GAACJ,CAAC;QAAC4B,CAAC,GAACtB,CAAC,IAAEkB,CAAC,IAAEG,CAAC;MAACC,CAAC,IAAE9B,CAAC,CAAC8B,CAAC,CAAC;IAAA;EAAC;AAAC;AAAC,SAAOtB,CAAC,IAAIuB,oBAAoB,EAAC1B,CAAC,IAAI2B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}