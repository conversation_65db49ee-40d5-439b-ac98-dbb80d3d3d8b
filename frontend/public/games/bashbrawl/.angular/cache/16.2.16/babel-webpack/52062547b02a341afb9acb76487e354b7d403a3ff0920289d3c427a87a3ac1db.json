{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"form\",\n  V = [\"form\", C({\n    outline: '<path d=\"M21.0224 12.0582H7.0059C6.74037 12.0582 6.48572 11.9522 6.29796 11.7636C6.1102 11.5749 6.00472 11.3191 6.00472 11.0523V7.02908C6.00472 6.76232 6.1102 6.50649 6.29796 6.31786C6.48572 6.12923 6.74037 6.02326 7.0059 6.02326H21.0224C21.288 6.02326 21.5426 6.12923 21.7304 6.31786C21.9181 6.50649 22.0236 6.76232 22.0236 7.02908V11.0523C22.0236 11.3191 21.9181 11.5749 21.7304 11.7636C21.5426 11.9522 21.288 12.0582 21.0224 12.0582ZM8.00708 10.0465H20.0212V7.97455H8.00708V10.0465Z\"/><path d=\"M21.0224 14.1503H7.0059C6.74037 14.1503 6.48572 14.2562 6.29796 14.4448C6.1102 14.6335 6.00472 14.8893 6.00472 15.1561V19.0989C6.00472 19.3656 6.1102 19.6215 6.29796 19.8101C6.48572 19.9987 6.74037 20.1047 7.0059 20.1047H18.3793L22.0236 16.3832V15.1561C22.0236 14.8893 21.9181 14.6335 21.7304 14.4448C21.5426 14.2562 21.288 14.1503 21.0224 14.1503ZM20.0212 18.0931H8.00708V16.0814H20.0212V18.0931Z\"/><path d=\"M11.0707 31.6816V31.6213L11.3911 30.2232H4.00236V4.01163H24.026V14.3212L26.0283 12.4202V3.00582C26.0283 2.73906 25.9229 2.48322 25.7351 2.2946C25.5473 2.10597 25.2927 2 25.0272 2H3.00118C2.73565 2 2.481 2.10597 2.29324 2.2946C2.10548 2.48322 2 2.73906 2 3.00582V31.1687C2 31.4354 2.10548 31.6912 2.29324 31.8799C2.481 32.0685 2.73565 32.1745 3.00118 32.1745H11.0106C11.0189 32.009 11.0389 31.8442 11.0707 31.6816Z\"/><path d=\"M22.0236 19.2699L21.2427 20.0644C21.4364 20.0243 21.614 19.9274 21.753 19.786C21.892 19.6446 21.9861 19.465 22.0236 19.2699Z\"/><path d=\"M6.00472 27.085C6.00472 27.3518 6.1102 27.6076 6.29796 27.7963C6.48572 27.9849 6.74037 28.0909 7.0059 28.0909H11.8516L12.152 26.7833L12.2821 26.2301V26.1798H8.00708V24.1279H14.3546L16.3569 22.1163H7.0059C6.74037 22.1163 6.48572 22.2223 6.29796 22.4109C6.1102 22.5995 6.00472 22.8554 6.00472 23.1221V27.085Z\"/><path d=\"M33.5272 16.7553L30.1532 13.3657C30.0035 13.2149 29.8256 13.0952 29.6297 13.0135C29.4339 12.9319 29.2239 12.8898 29.0119 12.8898C28.7998 12.8898 28.5898 12.9319 28.394 13.0135C28.1981 13.0952 28.0202 13.2149 27.8705 13.3657L14.1443 27.2359L13.013 32.0739C12.9706 32.2826 12.9697 32.4977 13.0102 32.7068C13.0507 32.9158 13.1319 33.1148 13.249 33.2924C13.3662 33.4699 13.5171 33.6224 13.6931 33.7413C13.869 33.8601 14.0666 33.9429 14.2745 33.9849C14.3777 33.9954 14.4816 33.9954 14.5848 33.9849C14.7075 34.005 14.8326 34.005 14.9553 33.9849L19.811 32.9087L33.5272 19.0989C33.677 18.9493 33.7959 18.7714 33.8771 18.5755C33.9582 18.3796 34 18.1695 34 17.9573C34 17.7451 33.9582 17.5349 33.8771 17.339C33.7959 17.1431 33.677 16.9652 33.5272 16.8157V16.7553ZM18.7898 31.0781L15.1255 31.8928L16.0165 28.2417L26.3087 17.7913L29.132 20.6277L18.7898 31.0781ZM30.2633 19.4911L27.44 16.6547L29.0319 15.0756L31.8752 17.9321L30.2633 19.4911Z\"/>'\n  })];\nexport { V as formIcon, L as formIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "V", "outline", "formIcon", "formIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/form.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"form\",V=[\"form\",C({outline:'<path d=\"M21.0224 12.0582H7.0059C6.74037 12.0582 6.48572 11.9522 6.29796 11.7636C6.1102 11.5749 6.00472 11.3191 6.00472 11.0523V7.02908C6.00472 6.76232 6.1102 6.50649 6.29796 6.31786C6.48572 6.12923 6.74037 6.02326 7.0059 6.02326H21.0224C21.288 6.02326 21.5426 6.12923 21.7304 6.31786C21.9181 6.50649 22.0236 6.76232 22.0236 7.02908V11.0523C22.0236 11.3191 21.9181 11.5749 21.7304 11.7636C21.5426 11.9522 21.288 12.0582 21.0224 12.0582ZM8.00708 10.0465H20.0212V7.97455H8.00708V10.0465Z\"/><path d=\"M21.0224 14.1503H7.0059C6.74037 14.1503 6.48572 14.2562 6.29796 14.4448C6.1102 14.6335 6.00472 14.8893 6.00472 15.1561V19.0989C6.00472 19.3656 6.1102 19.6215 6.29796 19.8101C6.48572 19.9987 6.74037 20.1047 7.0059 20.1047H18.3793L22.0236 16.3832V15.1561C22.0236 14.8893 21.9181 14.6335 21.7304 14.4448C21.5426 14.2562 21.288 14.1503 21.0224 14.1503ZM20.0212 18.0931H8.00708V16.0814H20.0212V18.0931Z\"/><path d=\"M11.0707 31.6816V31.6213L11.3911 30.2232H4.00236V4.01163H24.026V14.3212L26.0283 12.4202V3.00582C26.0283 2.73906 25.9229 2.48322 25.7351 2.2946C25.5473 2.10597 25.2927 2 25.0272 2H3.00118C2.73565 2 2.481 2.10597 2.29324 2.2946C2.10548 2.48322 2 2.73906 2 3.00582V31.1687C2 31.4354 2.10548 31.6912 2.29324 31.8799C2.481 32.0685 2.73565 32.1745 3.00118 32.1745H11.0106C11.0189 32.009 11.0389 31.8442 11.0707 31.6816Z\"/><path d=\"M22.0236 19.2699L21.2427 20.0644C21.4364 20.0243 21.614 19.9274 21.753 19.786C21.892 19.6446 21.9861 19.465 22.0236 19.2699Z\"/><path d=\"M6.00472 27.085C6.00472 27.3518 6.1102 27.6076 6.29796 27.7963C6.48572 27.9849 6.74037 28.0909 7.0059 28.0909H11.8516L12.152 26.7833L12.2821 26.2301V26.1798H8.00708V24.1279H14.3546L16.3569 22.1163H7.0059C6.74037 22.1163 6.48572 22.2223 6.29796 22.4109C6.1102 22.5995 6.00472 22.8554 6.00472 23.1221V27.085Z\"/><path d=\"M33.5272 16.7553L30.1532 13.3657C30.0035 13.2149 29.8256 13.0952 29.6297 13.0135C29.4339 12.9319 29.2239 12.8898 29.0119 12.8898C28.7998 12.8898 28.5898 12.9319 28.394 13.0135C28.1981 13.0952 28.0202 13.2149 27.8705 13.3657L14.1443 27.2359L13.013 32.0739C12.9706 32.2826 12.9697 32.4977 13.0102 32.7068C13.0507 32.9158 13.1319 33.1148 13.249 33.2924C13.3662 33.4699 13.5171 33.6224 13.6931 33.7413C13.869 33.8601 14.0666 33.9429 14.2745 33.9849C14.3777 33.9954 14.4816 33.9954 14.5848 33.9849C14.7075 34.005 14.8326 34.005 14.9553 33.9849L19.811 32.9087L33.5272 19.0989C33.677 18.9493 33.7959 18.7714 33.8771 18.5755C33.9582 18.3796 34 18.1695 34 17.9573C34 17.7451 33.9582 17.5349 33.8771 17.339C33.7959 17.1431 33.677 16.9652 33.5272 16.8157V16.7553ZM18.7898 31.0781L15.1255 31.8928L16.0165 28.2417L26.3087 17.7913L29.132 20.6277L18.7898 31.0781ZM30.2633 19.4911L27.44 16.6547L29.0319 15.0756L31.8752 17.9321L30.2633 19.4911Z\"/>'})];export{V as formIcon,L as formIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,MAAM;EAACC,CAAC,GAAC,CAAC,MAAM,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAA6pF,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,QAAQ,EAACH,CAAC,IAAII,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}