{"ast": null, "code": "import { renderIcon as r } from \"../icon.renderer.js\";\nconst i = \"error-mini\",\n  t = [\"error-mini\", r({\n    outline: '<path d=\"M18,4A14,14,0,1,0,32,18,14,14,0,0,0,18,4Zm0,24A10,10,0,1,1,28,18,10,10,0,0,1,18,28Z\"/><rect x=\"16\" y=\"12\" width=\"4\" height=\"6\"/><rect x=\"16\" y=\"20.8\" width=\"4\" height=\"3.2\"/>',\n    solid: '<path d=\"M18,4A14,14,0,1,0,32,18,14,14,0,0,0,18,4Zm2,20H16V20h4Zm0-8H16V8h4Z\"/>'\n  })];\nexport { t as errorMiniIcon, i as errorMiniIconName };", "map": {"version": 3, "names": ["renderIcon", "r", "i", "t", "outline", "solid", "errorMiniIcon", "errorMiniIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/error-mini.js"], "sourcesContent": ["import{renderIcon as r}from\"../icon.renderer.js\";const i=\"error-mini\",t=[\"error-mini\",r({outline:'<path d=\"M18,4A14,14,0,1,0,32,18,14,14,0,0,0,18,4Zm0,24A10,10,0,1,1,28,18,10,10,0,0,1,18,28Z\"/><rect x=\"16\" y=\"12\" width=\"4\" height=\"6\"/><rect x=\"16\" y=\"20.8\" width=\"4\" height=\"3.2\"/>',solid:'<path d=\"M18,4A14,14,0,1,0,32,18,14,14,0,0,0,18,4Zm2,20H16V20h4Zm0-8H16V8h4Z\"/>'})];export{t as errorMiniIcon,i as errorMiniIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,YAAY;EAACC,CAAC,GAAC,CAAC,YAAY,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,yLAAyL;IAACC,KAAK,EAAC;EAAiF,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,aAAa,EAACJ,CAAC,IAAIK,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}