{"ast": null, "code": "import _xfBase from \"./_xfBase.js\";\nvar XFilter = /*#__PURE__*/\nfunction () {\n  function XFilter(f, xf) {\n    this.xf = xf;\n    this.f = f;\n  }\n  XFilter.prototype['@@transducer/init'] = _xfBase.init;\n  XFilter.prototype['@@transducer/result'] = _xfBase.result;\n  XFilter.prototype['@@transducer/step'] = function (result, input) {\n    return this.f(input) ? this.xf['@@transducer/step'](result, input) : result;\n  };\n  return XFilter;\n}();\nexport default function _xfilter(f) {\n  return function (xf) {\n    return new XFilter(f, xf);\n  };\n}", "map": {"version": 3, "names": ["_xfBase", "<PERSON><PERSON><PERSON><PERSON>", "f", "xf", "prototype", "init", "result", "input", "_xfilter"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_xfilter.js"], "sourcesContent": ["import _xfBase from \"./_xfBase.js\";\n\nvar XFilter =\n/*#__PURE__*/\nfunction () {\n  function XFilter(f, xf) {\n    this.xf = xf;\n    this.f = f;\n  }\n\n  XFilter.prototype['@@transducer/init'] = _xfBase.init;\n  XFilter.prototype['@@transducer/result'] = _xfBase.result;\n\n  XFilter.prototype['@@transducer/step'] = function (result, input) {\n    return this.f(input) ? this.xf['@@transducer/step'](result, input) : result;\n  };\n\n  return XFilter;\n}();\n\nexport default function _xfilter(f) {\n  return function (xf) {\n    return new XFilter(f, xf);\n  };\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,cAAc;AAElC,IAAIC,OAAO,GACX;AACA,YAAY;EACV,SAASA,OAAOA,CAACC,CAAC,EAAEC,EAAE,EAAE;IACtB,IAAI,CAACA,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACD,CAAC,GAAGA,CAAC;EACZ;EAEAD,OAAO,CAACG,SAAS,CAAC,mBAAmB,CAAC,GAAGJ,OAAO,CAACK,IAAI;EACrDJ,OAAO,CAACG,SAAS,CAAC,qBAAqB,CAAC,GAAGJ,OAAO,CAACM,MAAM;EAEzDL,OAAO,CAACG,SAAS,CAAC,mBAAmB,CAAC,GAAG,UAAUE,MAAM,EAAEC,KAAK,EAAE;IAChE,OAAO,IAAI,CAACL,CAAC,CAACK,KAAK,CAAC,GAAG,IAAI,CAACJ,EAAE,CAAC,mBAAmB,CAAC,CAACG,MAAM,EAAEC,KAAK,CAAC,GAAGD,MAAM;EAC7E,CAAC;EAED,OAAOL,OAAO;AAChB,CAAC,CAAC,CAAC;AAEH,eAAe,SAASO,QAAQA,CAACN,CAAC,EAAE;EAClC,OAAO,UAAUC,EAAE,EAAE;IACnB,OAAO,IAAIF,OAAO,CAACC,CAAC,EAAEC,EAAE,CAAC;EAC3B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}