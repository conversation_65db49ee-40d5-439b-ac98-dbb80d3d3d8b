{"ast": null, "code": "import _curry3 from \"./internal/_curry3.js\";\nimport _xReduce from \"./internal/_xReduce.js\";\nimport _xwrap from \"./internal/_xwrap.js\";\n/**\n * Returns a single item by iterating through the list, successively calling\n * the iterator function and passing it an accumulator value and the current\n * value from the array, and then passing the result to the next call.\n *\n * The iterator function receives two values: *(acc, value)*. It may use\n * [`R.reduced`](#reduced) to shortcut the iteration.\n *\n * The arguments' order of [`reduceRight`](#reduceRight)'s iterator function\n * is *(value, acc)*.\n *\n * Note: `R.reduce` does not skip deleted or unassigned indices (sparse\n * arrays), unlike the native `Array.prototype.reduce` method. For more details\n * on this behavior, see:\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/reduce#Description\n *\n * Be cautious of mutating and returning the accumulator. If you reuse it across\n * invocations, it will continue to accumulate onto the same value. The general\n * recommendation is to always return a new value. If you can't do so for\n * performance reasons, then be sure to reinitialize the accumulator on each\n * invocation.\n *\n * Dispatches to the `reduce` method of the third argument, if present. When\n * doing so, it is up to the user to handle the [`R.reduced`](#reduced)\n * shortcuting, as this is not implemented by `reduce`.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig ((a, b) -> a) -> a -> [b] -> a\n * @param {Function} fn The iterator function. Receives two values, the accumulator and the\n *        current element from the array.\n * @param {*} acc The accumulator value.\n * @param {Array} list The list to iterate over.\n * @return {*} The final, accumulated value.\n * @see R.reduced, R.addIndex, R.reduceRight\n * @example\n *\n *      R.reduce(R.subtract, 0, [1, 2, 3, 4]) // => ((((0 - 1) - 2) - 3) - 4) = -10\n *      //          -               -10\n *      //         / \\              / \\\n *      //        -   4           -6   4\n *      //       / \\              / \\\n *      //      -   3   ==>     -3   3\n *      //     / \\              / \\\n *      //    -   2           -1   2\n *      //   / \\              / \\\n *      //  0   1            0   1\n *\n * @symb R.reduce(f, a, [b, c, d]) = f(f(f(a, b), c), d)\n */\n\nvar reduce = /*#__PURE__*/\n_curry3(function (xf, acc, list) {\n  return _xReduce(typeof xf === 'function' ? _xwrap(xf) : xf, acc, list);\n});\nexport default reduce;", "map": {"version": 3, "names": ["_curry3", "_xReduce", "_xwrap", "reduce", "xf", "acc", "list"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/reduce.js"], "sourcesContent": ["import _curry3 from \"./internal/_curry3.js\";\nimport _xReduce from \"./internal/_xReduce.js\";\nimport _xwrap from \"./internal/_xwrap.js\";\n/**\n * Returns a single item by iterating through the list, successively calling\n * the iterator function and passing it an accumulator value and the current\n * value from the array, and then passing the result to the next call.\n *\n * The iterator function receives two values: *(acc, value)*. It may use\n * [`R.reduced`](#reduced) to shortcut the iteration.\n *\n * The arguments' order of [`reduceRight`](#reduceRight)'s iterator function\n * is *(value, acc)*.\n *\n * Note: `R.reduce` does not skip deleted or unassigned indices (sparse\n * arrays), unlike the native `Array.prototype.reduce` method. For more details\n * on this behavior, see:\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/reduce#Description\n *\n * Be cautious of mutating and returning the accumulator. If you reuse it across\n * invocations, it will continue to accumulate onto the same value. The general\n * recommendation is to always return a new value. If you can't do so for\n * performance reasons, then be sure to reinitialize the accumulator on each\n * invocation.\n *\n * Dispatches to the `reduce` method of the third argument, if present. When\n * doing so, it is up to the user to handle the [`R.reduced`](#reduced)\n * shortcuting, as this is not implemented by `reduce`.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @sig ((a, b) -> a) -> a -> [b] -> a\n * @param {Function} fn The iterator function. Receives two values, the accumulator and the\n *        current element from the array.\n * @param {*} acc The accumulator value.\n * @param {Array} list The list to iterate over.\n * @return {*} The final, accumulated value.\n * @see R.reduced, R.addIndex, R.reduceRight\n * @example\n *\n *      R.reduce(R.subtract, 0, [1, 2, 3, 4]) // => ((((0 - 1) - 2) - 3) - 4) = -10\n *      //          -               -10\n *      //         / \\              / \\\n *      //        -   4           -6   4\n *      //       / \\              / \\\n *      //      -   3   ==>     -3   3\n *      //     / \\              / \\\n *      //    -   2           -1   2\n *      //   / \\              / \\\n *      //  0   1            0   1\n *\n * @symb R.reduce(f, a, [b, c, d]) = f(f(f(a, b), c), d)\n */\n\nvar reduce =\n/*#__PURE__*/\n_curry3(function (xf, acc, list) {\n  return _xReduce(typeof xf === 'function' ? _xwrap(xf) : xf, acc, list);\n});\n\nexport default reduce;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,MAAM,MAAM,sBAAsB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,MAAM,GACV;AACAH,OAAO,CAAC,UAAUI,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAE;EAC/B,OAAOL,QAAQ,CAAC,OAAOG,EAAE,KAAK,UAAU,GAAGF,MAAM,CAACE,EAAE,CAAC,GAAGA,EAAE,EAAEC,GAAG,EAAEC,IAAI,CAAC;AACxE,CAAC,CAAC;AAEF,eAAeH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}