{"ast": null, "code": "export default {\n  background: '#000000',\n  foreground: '#ffffff',\n  cursor: '#ffffff',\n  black: '#2e3436',\n  brightBlack: '#555753',\n  red: '#cc0000',\n  brightRed: '#ef2929',\n  green: '#008700',\n  brightGreen: '#5FAF87',\n  yellow: '#AFAF00',\n  brightYellow: '#DFDF87',\n  blue: '#00005F',\n  brightBlue: '#729fcf',\n  magenta: '#87005F',\n  brightMagenta: '#ad7fa8',\n  cyan: '#87D7D7',\n  brightCyan: '#34e2e2',\n  white: '#d3d7cf',\n  brightWhite: '#FFFFFF'\n};", "map": {"version": 3, "names": ["background", "foreground", "cursor", "black", "brightBlack", "red", "brightRed", "green", "bright<PERSON><PERSON>", "yellow", "<PERSON><PERSON><PERSON><PERSON>", "blue", "brightBlue", "magenta", "brightMagenta", "cyan", "bright<PERSON>yan", "white", "bright<PERSON><PERSON>e"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/src/app/terminals/terminal-themes/Dichromatic.ts"], "sourcesContent": ["export default {\n  background: '#000000',\n  foreground: '#ffffff',\n  cursor: '#ffffff',\n\n  black: '#2e3436',\n  brightBlack: '#555753',\n\n  red: '#cc0000',\n  brightRed: '#ef2929',\n\n  green: '#008700',\n  brightGreen: '#5FAF87',\n\n  yellow: '#AFAF00',\n  brightYellow: '#DFDF87',\n\n  blue: '#00005F',\n  brightBlue: '#729fcf',\n\n  magenta: '#87005F',\n  brightMagenta: '#ad7fa8',\n\n  cyan: '#87D7D7',\n  brightCyan: '#34e2e2',\n\n  white: '#d3d7cf',\n  brightWhite: '#FFFFFF',\n};\n"], "mappings": "AAAA,eAAe;EACbA,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,SAAS;EACrBC,MAAM,EAAE,SAAS;EAEjBC,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE,SAAS;EAEtBC,GAAG,EAAE,SAAS;EACdC,SAAS,EAAE,SAAS;EAEpBC,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE,SAAS;EAEtBC,MAAM,EAAE,SAAS;EACjBC,YAAY,EAAE,SAAS;EAEvBC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,SAAS;EAErBC,OAAO,EAAE,SAAS;EAClBC,aAAa,EAAE,SAAS;EAExBC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,SAAS;EAErBC,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE;CACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}