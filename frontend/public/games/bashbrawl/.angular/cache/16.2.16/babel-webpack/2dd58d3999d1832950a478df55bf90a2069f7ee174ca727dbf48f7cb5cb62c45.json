{"ast": null, "code": "import { renderIcon as e } from \"../icon.renderer.js\";\nconst n = \"angle\",\n  o = [\"angle\", e({\n    outline: '<path d=\"M29.52,22.52,18,10.6,6.48,22.52a1.7,1.7,0,0,0,2.45,2.36L18,15.49l9.08,9.39a1.7,1.7,0,0,0,2.45-2.36Z\"/>'\n  })];\nexport { o as angleIcon, n as angleIconName };", "map": {"version": 3, "names": ["renderIcon", "e", "n", "o", "outline", "angleIcon", "angleIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/angle.js"], "sourcesContent": ["import{renderIcon as e}from\"../icon.renderer.js\";const n=\"angle\",o=[\"angle\",e({outline:'<path d=\"M29.52,22.52,18,10.6,6.48,22.52a1.7,1.7,0,0,0,2.45,2.36L18,15.49l9.08,9.39a1.7,1.7,0,0,0,2.45-2.36Z\"/>'})];export{o as angleIcon,n as angleIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,OAAO;EAACC,CAAC,GAAC,CAAC,OAAO,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAiH,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,SAAS,EAACH,CAAC,IAAII,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}