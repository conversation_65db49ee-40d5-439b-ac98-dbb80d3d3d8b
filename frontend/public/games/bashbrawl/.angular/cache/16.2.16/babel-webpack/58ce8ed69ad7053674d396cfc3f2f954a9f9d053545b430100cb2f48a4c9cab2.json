{"ast": null, "code": "import { fadeInKeyframes as a } from \"./keyframes/fade-in.js\";\nimport { fadeAndSlideInKeyframes as i } from \"./keyframes/fade-in-and-slide-down.js\";\nconst n = \"cds-modal-enter\",\n  o = [{\n    target: \".overlay-backdrop\",\n    onlyIf: \"isLayered:false\",\n    animation: a,\n    options: {\n      duration: \"--backdrop-animation-duration\",\n      easing: \"--animation-easing\",\n      fill: \"forwards\"\n    }\n  }, {\n    target: \".private-host\",\n    animation: i,\n    options: {\n      duration: \"--animation-duration\",\n      easing: \"--animation-easing\",\n      fill: \"forwards\",\n      endDelay: 50\n    }\n  }];\nexport { o as AnimationModalEnterConfig, n as AnimationModalEnterName };", "map": {"version": 3, "names": ["fadeInKeyframes", "a", "fadeAndSlideInKeyframes", "i", "n", "o", "target", "onlyIf", "animation", "options", "duration", "easing", "fill", "endDelay", "AnimationModalEnterConfig", "AnimationModalEnterName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/motion/animations/cds-modal-enter.js"], "sourcesContent": ["import{fadeInKeyframes as a}from\"./keyframes/fade-in.js\";import{fadeAndSlideInKeyframes as i}from\"./keyframes/fade-in-and-slide-down.js\";const n=\"cds-modal-enter\",o=[{target:\".overlay-backdrop\",onlyIf:\"isLayered:false\",animation:a,options:{duration:\"--backdrop-animation-duration\",easing:\"--animation-easing\",fill:\"forwards\"}},{target:\".private-host\",animation:i,options:{duration:\"--animation-duration\",easing:\"--animation-easing\",fill:\"forwards\",endDelay:50}}];export{o as AnimationModalEnterConfig,n as AnimationModalEnterName};\n"], "mappings": "AAAA,SAAOA,eAAe,IAAIC,CAAC,QAAK,wBAAwB;AAAC,SAAOC,uBAAuB,IAAIC,CAAC,QAAK,uCAAuC;AAAC,MAAMC,CAAC,GAAC,iBAAiB;EAACC,CAAC,GAAC,CAAC;IAACC,MAAM,EAAC,mBAAmB;IAACC,MAAM,EAAC,iBAAiB;IAACC,SAAS,EAACP,CAAC;IAACQ,OAAO,EAAC;MAACC,QAAQ,EAAC,+BAA+B;MAACC,MAAM,EAAC,oBAAoB;MAACC,IAAI,EAAC;IAAU;EAAC,CAAC,EAAC;IAACN,MAAM,EAAC,eAAe;IAACE,SAAS,EAACL,CAAC;IAACM,OAAO,EAAC;MAACC,QAAQ,EAAC,sBAAsB;MAACC,MAAM,EAAC,oBAAoB;MAACC,IAAI,EAAC,UAAU;MAACC,QAAQ,EAAC;IAAE;EAAC,CAAC,CAAC;AAAC,SAAOR,CAAC,IAAIS,yBAAyB,EAACV,CAAC,IAAIW,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}