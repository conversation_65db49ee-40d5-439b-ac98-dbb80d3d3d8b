{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"coin-bag\",\n  M = [\"coin-bag\", C({\n    outline: '<path d=\"M21.0002 28H15.0033C14.4536 28 14.0038 28.45 14.0038 29C14.0038 29.55 14.4536 30 15.0033 30H21.0002C21.5499 30 21.9997 29.55 21.9997 29C21.9997 28.45 21.5499 28 21.0002 28ZM19.9907 24C19.441 24 18.9912 24.45 18.9912 25C18.9912 25.55 19.441 26 19.9907 26H25.9876C26.5374 26 26.9871 25.55 26.9871 25C26.9871 24.45 26.5374 24 25.9876 24H19.9907ZM16.9923 10C17.6619 11.6 18.1417 13.28 18.4016 15C18.6214 16.28 18.7214 17.57 18.7214 18.86L20.3006 17.75C20.2606 16.74 20.1507 15.74 19.9807 14.75C19.7109 13.13 19.2811 11.54 18.7014 10H19.481L20.3905 8H13.1542L11.3052 4H24.5184L22.0197 9.47C22.4495 9.7 22.8593 9.96 23.2491 10.25C25.6978 11.94 27.7068 14.19 29.106 16.82C30.5053 19.7 31.3749 22.81 31.6747 26H33.6837C33.3938 22.48 32.4343 19.05 30.8651 15.88C29.3559 13.02 27.197 10.56 24.5484 8.71L26.9472 3.42C27.1071 3.1 27.0771 2.72 26.8772 2.42C26.6973 2.16 26.3974 2 26.0776 2H9.746C9.40618 2 9.08634 2.17 8.90643 2.46C8.72653 2.75 8.69654 3.11 8.83647 3.42L11.2852 8.73C8.65656 10.58 6.50767 13.03 5.00844 15.88C2.85954 19.88 2.18989 24.77 1.99998 28.16C1.94002 29.16 2.29983 30.15 2.99947 30.87C3.7291 31.58 4.71859 31.97 5.73806 31.94H11.9349V30H5.6681C5.20833 30 4.77856 29.81 4.45872 29.48C4.13889 29.15 3.97897 28.71 4.00895 28.25C4.14888 25.64 4.6986 20.67 6.76753 16.8C8.22678 14.03 10.3857 11.68 13.0243 10H14.0238C13.3442 10.94 12.7245 11.91 12.1548 12.92C11.5751 13.99 11.0853 15.11 10.6855 16.26L12.0548 17.18C12.4546 15.99 12.9544 14.83 13.544 13.71C14.2637 12.41 15.0833 11.17 15.9928 10H16.9923ZM32.994 28H25.9976C25.4479 28 24.9982 28.45 24.9982 29C24.9982 29.55 25.4479 30 25.9976 30H32.994C33.5438 30 33.9935 29.55 33.9935 29C33.9935 28.45 33.5438 28 32.994 28ZM18.9912 32H15.9428C15.3931 32 14.9433 32.45 14.9433 33C14.9433 33.55 15.3931 34 15.9428 34H18.9912C19.541 34 19.9907 33.55 19.9907 33C19.9907 32.45 19.541 32 18.9912 32ZM30.9851 32H23.0392C22.4894 32 22.0397 32.45 22.0397 33C22.0397 33.55 22.4894 34 23.0392 34H30.9851C31.5348 34 31.9846 33.55 31.9846 33C31.9846 32.45 31.5348 32 30.9851 32Z\"/>',\n    solid: '<path d=\"M21.9397 29C21.9397 28.45 21.49 28 20.9402 28H14.9433C14.3936 28 13.9438 28.45 13.9438 29C13.9438 29.55 14.3936 30 14.9433 30H20.9402C21.49 30 21.9397 29.55 21.9397 29Z\"/><path d=\"M19.9308 24H25.9277C26.4774 24 26.9272 24.45 26.9272 25C26.9272 25.55 26.4774 26 25.9277 26H19.9308C19.381 26 18.9313 25.55 18.9313 25C18.9313 24.45 19.381 24 19.9308 24Z\"/><path d=\"M21.0002 30C21.5499 30 21.9997 29.55 21.9997 29C21.9997 28.45 21.5499 28 21.0002 28H15.0033C14.4536 28 14.0038 28.45 14.0038 29C14.0038 29.55 14.4536 30 15.0033 30H21.0002ZM19.9907 26H25.9876C26.5374 26 26.9871 25.55 26.9871 25C26.9871 24.45 26.5374 24 25.9876 24H19.9907C19.441 24 18.9912 24.45 18.9912 25C18.9912 25.55 19.441 26 19.9907 26ZM18.9912 32H15.9428C15.3931 32 14.9433 32.45 14.9433 33C14.9433 33.55 15.3931 34 15.9428 34H18.9912C19.541 34 19.9907 33.55 19.9907 33C19.9907 32.45 19.541 32 18.9912 32ZM30.9851 32H23.0392C22.4894 32 22.0397 32.45 22.0397 33C22.0397 33.55 22.4894 34 23.0392 34H30.9851C31.5348 34 31.9846 33.55 31.9846 33C31.9846 32.45 31.5348 32 30.9851 32ZM32.994 28H25.9976C25.4479 28 24.9982 28.45 24.9982 29C24.9982 29.55 25.4479 30 25.9976 30H32.994C33.5438 30 33.9935 29.55 33.9935 29C33.9935 28.45 33.5438 28 32.994 28Z\"/><path d=\"M33.6837 26C33.3838 22.49 32.4343 19.06 30.8651 15.91C29.3559 13.04 27.197 10.58 24.5484 8.73L26.9472 3.43C27.1071 3.11 27.0771 2.73 26.8772 2.43C26.6973 2.16 26.3974 2 26.0776 2H9.746C9.40618 2 9.08634 2.17 8.90643 2.46C8.72653 2.75 8.69654 3.11 8.83647 3.42L10.9454 8H20.4005L19.491 10H18.7114C19.2911 11.54 19.7209 13.13 19.9907 14.75C20.1606 15.74 20.2706 16.74 20.3106 17.75L18.7314 18.86C18.7314 17.57 18.6314 16.28 18.4115 15C18.1417 13.28 17.6719 11.6 17.0023 10H16.0028C15.0933 11.17 14.2737 12.41 13.554 13.71C12.9643 14.83 12.4646 15.99 12.0648 17.18L10.6955 16.26C11.0953 15.11 11.5851 13.99 12.1648 12.92C12.7345 11.91 13.3541 10.94 14.0338 10H9.70602C7.797 11.66 6.19783 13.66 5.01843 15.91C2.85954 19.92 2.18989 24.81 1.99998 28.21C1.94002 29.22 2.29983 30.2 2.99947 30.93C3.60916 31.52 4.39875 31.87 5.22832 31.96L5.25831 32H13.1343C13.1942 31.83 13.2542 31.66 13.3442 31.5C12.5346 30.96 11.9948 30.04 11.9948 29C11.9948 27.35 13.3442 26 14.9933 26H17.1622C17.0522 25.69 16.9923 25.35 16.9923 25C16.9923 23.35 18.3416 22 19.9907 22H25.9876C27.6368 22 28.9861 23.35 28.9861 25C28.9861 25.35 28.9261 25.69 28.8162 26H33.6837Z\"/>'\n  })];\nexport { M as coinBagIcon, H as coinBagIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "M", "outline", "solid", "coinBagIcon", "coinBagIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/coin-bag.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"coin-bag\",M=[\"coin-bag\",C({outline:'<path d=\"M21.0002 28H15.0033C14.4536 28 14.0038 28.45 14.0038 29C14.0038 29.55 14.4536 30 15.0033 30H21.0002C21.5499 30 21.9997 29.55 21.9997 29C21.9997 28.45 21.5499 28 21.0002 28ZM19.9907 24C19.441 24 18.9912 24.45 18.9912 25C18.9912 25.55 19.441 26 19.9907 26H25.9876C26.5374 26 26.9871 25.55 26.9871 25C26.9871 24.45 26.5374 24 25.9876 24H19.9907ZM16.9923 10C17.6619 11.6 18.1417 13.28 18.4016 15C18.6214 16.28 18.7214 17.57 18.7214 18.86L20.3006 17.75C20.2606 16.74 20.1507 15.74 19.9807 14.75C19.7109 13.13 19.2811 11.54 18.7014 10H19.481L20.3905 8H13.1542L11.3052 4H24.5184L22.0197 9.47C22.4495 9.7 22.8593 9.96 23.2491 10.25C25.6978 11.94 27.7068 14.19 29.106 16.82C30.5053 19.7 31.3749 22.81 31.6747 26H33.6837C33.3938 22.48 32.4343 19.05 30.8651 15.88C29.3559 13.02 27.197 10.56 24.5484 8.71L26.9472 3.42C27.1071 3.1 27.0771 2.72 26.8772 2.42C26.6973 2.16 26.3974 2 26.0776 2H9.746C9.40618 2 9.08634 2.17 8.90643 2.46C8.72653 2.75 8.69654 3.11 8.83647 3.42L11.2852 8.73C8.65656 10.58 6.50767 13.03 5.00844 15.88C2.85954 19.88 2.18989 24.77 1.99998 28.16C1.94002 29.16 2.29983 30.15 2.99947 30.87C3.7291 31.58 4.71859 31.97 5.73806 31.94H11.9349V30H5.6681C5.20833 30 4.77856 29.81 4.45872 29.48C4.13889 29.15 3.97897 28.71 4.00895 28.25C4.14888 25.64 4.6986 20.67 6.76753 16.8C8.22678 14.03 10.3857 11.68 13.0243 10H14.0238C13.3442 10.94 12.7245 11.91 12.1548 12.92C11.5751 13.99 11.0853 15.11 10.6855 16.26L12.0548 17.18C12.4546 15.99 12.9544 14.83 13.544 13.71C14.2637 12.41 15.0833 11.17 15.9928 10H16.9923ZM32.994 28H25.9976C25.4479 28 24.9982 28.45 24.9982 29C24.9982 29.55 25.4479 30 25.9976 30H32.994C33.5438 30 33.9935 29.55 33.9935 29C33.9935 28.45 33.5438 28 32.994 28ZM18.9912 32H15.9428C15.3931 32 14.9433 32.45 14.9433 33C14.9433 33.55 15.3931 34 15.9428 34H18.9912C19.541 34 19.9907 33.55 19.9907 33C19.9907 32.45 19.541 32 18.9912 32ZM30.9851 32H23.0392C22.4894 32 22.0397 32.45 22.0397 33C22.0397 33.55 22.4894 34 23.0392 34H30.9851C31.5348 34 31.9846 33.55 31.9846 33C31.9846 32.45 31.5348 32 30.9851 32Z\"/>',solid:'<path d=\"M21.9397 29C21.9397 28.45 21.49 28 20.9402 28H14.9433C14.3936 28 13.9438 28.45 13.9438 29C13.9438 29.55 14.3936 30 14.9433 30H20.9402C21.49 30 21.9397 29.55 21.9397 29Z\"/><path d=\"M19.9308 24H25.9277C26.4774 24 26.9272 24.45 26.9272 25C26.9272 25.55 26.4774 26 25.9277 26H19.9308C19.381 26 18.9313 25.55 18.9313 25C18.9313 24.45 19.381 24 19.9308 24Z\"/><path d=\"M21.0002 30C21.5499 30 21.9997 29.55 21.9997 29C21.9997 28.45 21.5499 28 21.0002 28H15.0033C14.4536 28 14.0038 28.45 14.0038 29C14.0038 29.55 14.4536 30 15.0033 30H21.0002ZM19.9907 26H25.9876C26.5374 26 26.9871 25.55 26.9871 25C26.9871 24.45 26.5374 24 25.9876 24H19.9907C19.441 24 18.9912 24.45 18.9912 25C18.9912 25.55 19.441 26 19.9907 26ZM18.9912 32H15.9428C15.3931 32 14.9433 32.45 14.9433 33C14.9433 33.55 15.3931 34 15.9428 34H18.9912C19.541 34 19.9907 33.55 19.9907 33C19.9907 32.45 19.541 32 18.9912 32ZM30.9851 32H23.0392C22.4894 32 22.0397 32.45 22.0397 33C22.0397 33.55 22.4894 34 23.0392 34H30.9851C31.5348 34 31.9846 33.55 31.9846 33C31.9846 32.45 31.5348 32 30.9851 32ZM32.994 28H25.9976C25.4479 28 24.9982 28.45 24.9982 29C24.9982 29.55 25.4479 30 25.9976 30H32.994C33.5438 30 33.9935 29.55 33.9935 29C33.9935 28.45 33.5438 28 32.994 28Z\"/><path d=\"M33.6837 26C33.3838 22.49 32.4343 19.06 30.8651 15.91C29.3559 13.04 27.197 10.58 24.5484 8.73L26.9472 3.43C27.1071 3.11 27.0771 2.73 26.8772 2.43C26.6973 2.16 26.3974 2 26.0776 2H9.746C9.40618 2 9.08634 2.17 8.90643 2.46C8.72653 2.75 8.69654 3.11 8.83647 3.42L10.9454 8H20.4005L19.491 10H18.7114C19.2911 11.54 19.7209 13.13 19.9907 14.75C20.1606 15.74 20.2706 16.74 20.3106 17.75L18.7314 18.86C18.7314 17.57 18.6314 16.28 18.4115 15C18.1417 13.28 17.6719 11.6 17.0023 10H16.0028C15.0933 11.17 14.2737 12.41 13.554 13.71C12.9643 14.83 12.4646 15.99 12.0648 17.18L10.6955 16.26C11.0953 15.11 11.5851 13.99 12.1648 12.92C12.7345 11.91 13.3541 10.94 14.0338 10H9.70602C7.797 11.66 6.19783 13.66 5.01843 15.91C2.85954 19.92 2.18989 24.81 1.99998 28.21C1.94002 29.22 2.29983 30.2 2.99947 30.93C3.60916 31.52 4.39875 31.87 5.22832 31.96L5.25831 32H13.1343C13.1942 31.83 13.2542 31.66 13.3442 31.5C12.5346 30.96 11.9948 30.04 11.9948 29C11.9948 27.35 13.3442 26 14.9933 26H17.1622C17.0522 25.69 16.9923 25.35 16.9923 25C16.9923 23.35 18.3416 22 19.9907 22H25.9876C27.6368 22 28.9861 23.35 28.9861 25C28.9861 25.35 28.9261 25.69 28.8162 26H33.6837Z\"/>'})];export{M as coinBagIcon,H as coinBagIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,UAAU;EAACC,CAAC,GAAC,CAAC,UAAU,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,m/DAAm/D;IAACC,KAAK,EAAC;EAA40E,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,WAAW,EAACJ,CAAC,IAAIK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}