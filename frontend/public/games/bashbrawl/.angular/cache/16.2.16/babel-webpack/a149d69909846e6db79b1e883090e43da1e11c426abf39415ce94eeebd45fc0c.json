{"ast": null, "code": "import { renderIcon as t } from \"../icon.renderer.js\";\nconst a = \"align-right-text\",\n  r = [\"align-right-text\", t({\n    outline: '<path d=\"M14.65,27.1a1.1,1.1,0,0,0,1.1,1.1H30V26H15.75A1.1,1.1,0,0,0,14.65,27.1Z\"/><path d=\"M6.9,21.1A1.1,1.1,0,0,0,8,22.2H30V20H8A1.1,1.1,0,0,0,6.9,21.1Z\"/><path d=\"M13.4,15.1a1.1,1.1,0,0,0,1.1,1.1H30V14H14.5A1.1,1.1,0,0,0,13.4,15.1Z\"/><path d=\"M6.75,8a1.1,1.1,0,1,0,0,2.2H30V8Z\"/>'\n  })];\nexport { r as alignRightTextIcon, a as alignRightTextIconName };", "map": {"version": 3, "names": ["renderIcon", "t", "a", "r", "outline", "alignRightTextIcon", "alignRightTextIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/align-right-text.js"], "sourcesContent": ["import{renderIcon as t}from\"../icon.renderer.js\";const a=\"align-right-text\",r=[\"align-right-text\",t({outline:'<path d=\"M14.65,27.1a1.1,1.1,0,0,0,1.1,1.1H30V26H15.75A1.1,1.1,0,0,0,14.65,27.1Z\"/><path d=\"M6.9,21.1A1.1,1.1,0,0,0,8,22.2H30V20H8A1.1,1.1,0,0,0,6.9,21.1Z\"/><path d=\"M13.4,15.1a1.1,1.1,0,0,0,1.1,1.1H30V14H14.5A1.1,1.1,0,0,0,13.4,15.1Z\"/><path d=\"M6.75,8a1.1,1.1,0,1,0,0,2.2H30V8Z\"/>'})];export{r as alignRightTextIcon,a as alignRightTextIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,kBAAkB;EAACC,CAAC,GAAC,CAAC,kBAAkB,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAA4R,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,kBAAkB,EAACH,CAAC,IAAII,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}