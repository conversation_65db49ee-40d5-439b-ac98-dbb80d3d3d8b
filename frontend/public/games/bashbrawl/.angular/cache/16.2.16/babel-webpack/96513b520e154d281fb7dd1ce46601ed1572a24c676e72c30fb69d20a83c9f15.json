{"ast": null, "code": "import { camelCaseToKebabCase as t } from \"./string.js\";\nconst s = new class {\n  constructor() {\n    if (this.supports = {\n      js: !0\n    }, !document.body.hasAttribute(\"cds-supports\") || \"no-js\" === document.body.getAttribute(\"cds-supports\")) {\n      const s = t(Object.keys(this.supports).reduce((t, s) => `${t} ${this.supports[s] ? s : \"no-\" + s}`, \"\")).trim();\n      document.body.setAttribute(\"cds-supports\", s);\n    }\n  }\n}();\nexport { s as browserFeatures };", "map": {"version": 3, "names": ["camelCaseToKebabCase", "t", "s", "constructor", "supports", "js", "document", "body", "hasAttribute", "getAttribute", "Object", "keys", "reduce", "trim", "setAttribute", "browserFeatures"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/supports.js"], "sourcesContent": ["import{camelCaseToKebabCase as t}from\"./string.js\";const s=new class{constructor(){if(this.supports={js:!0},!document.body.hasAttribute(\"cds-supports\")||\"no-js\"===document.body.getAttribute(\"cds-supports\")){const s=t(Object.keys(this.supports).reduce(((t,s)=>`${t} ${this.supports[s]?s:\"no-\"+s}`),\"\")).trim();document.body.setAttribute(\"cds-supports\",s)}}};export{s as browserFeatures};\n"], "mappings": "AAAA,SAAOA,oBAAoB,IAAIC,CAAC,QAAK,aAAa;AAAC,MAAMC,CAAC,GAAC,IAAI,MAAK;EAACC,WAAWA,CAAA,EAAE;IAAC,IAAG,IAAI,CAACC,QAAQ,GAAC;MAACC,EAAE,EAAC,CAAC;IAAC,CAAC,EAAC,CAACC,QAAQ,CAACC,IAAI,CAACC,YAAY,CAAC,cAAc,CAAC,IAAE,OAAO,KAAGF,QAAQ,CAACC,IAAI,CAACE,YAAY,CAAC,cAAc,CAAC,EAAC;MAAC,MAAMP,CAAC,GAACD,CAAC,CAACS,MAAM,CAACC,IAAI,CAAC,IAAI,CAACP,QAAQ,CAAC,CAACQ,MAAM,CAAE,CAACX,CAAC,EAACC,CAAC,KAAI,GAAED,CAAE,IAAG,IAAI,CAACG,QAAQ,CAACF,CAAC,CAAC,GAACA,CAAC,GAAC,KAAK,GAACA,CAAE,EAAC,EAAE,EAAE,CAAC,CAAC,CAACW,IAAI,CAAC,CAAC;MAACP,QAAQ,CAACC,IAAI,CAACO,YAAY,CAAC,cAAc,EAACZ,CAAC,CAAC;IAAA;EAAC;AAAC,CAAC,CAAD,CAAC;AAAC,SAAOA,CAAC,IAAIa,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}