{"ast": null, "code": "export const environment = {\n  production: false,\n  server: 'http://localhost:16210'\n};", "map": {"version": 3, "names": ["environment", "production", "server"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/src/environments/environment.local.ts"], "sourcesContent": ["export const environment = {\n  production: false,\n  server: 'http://localhost:16210',\n};\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE;CACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}