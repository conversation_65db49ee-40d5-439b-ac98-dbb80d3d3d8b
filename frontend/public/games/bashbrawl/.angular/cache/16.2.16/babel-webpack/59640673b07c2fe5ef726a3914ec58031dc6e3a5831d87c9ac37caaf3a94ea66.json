{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"clipboard\",\n  V = [\"clipboard\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M25.0079 7.34C25.0079 6.06 23.9575 5.02 22.6769 5.01H21.8766C21.4264 3.24 19.8258 2 18.005 2C16.1842 2 14.5836 3.24 14.1334 5.01H13.3431C12.0625 5.01 11.0221 6.06 11.0121 7.34V11.01H25.0179V7.34H25.0079ZM13.0029 9.01V7.34C13.0029 7.25 13.0329 7.17 13.103 7.11C13.163 7.05 13.253 7.01 13.3331 7.01H16.0042V6.01C16.0042 4.91 16.9045 4.01 18.005 4.01C19.1055 4.01 20.0058 4.91 20.0058 6.01V7.01H22.6769C22.857 7.01 23.0071 7.16 23.0071 7.34V9.01H13.0029ZM26.0083 5.01H28.2993C28.7495 5.01 29.1797 5.18 29.4998 5.5C29.8199 5.82 30 6.25 30 6.7V32.31C30 32.76 29.8199 33.19 29.4998 33.51C29.1797 33.83 28.7495 34 28.2993 34H7.71071C7.26053 34 6.83035 33.83 6.50021 33.51C6.18008 33.19 6 32.76 6 32.31V6.7C6.01 6.2 6.2401 5.73 6.62026 5.41C7.00042 5.09 7.50063 4.94 8.00083 5.01H10.0017V7.01H8.00083V32H28.0092V7.01H26.0083V5.01ZM25.0079 14.01V16.01H11.0021V14.01H25.0079ZM25.0079 20.01V18.01H11.0021V20.01H25.0079ZM25.0079 22.01V24.01H11.0021V22.01H25.0079ZM25.0079 28.01V26.01H11.0021V28.01H25.0079Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M28.0092 12.7128C28.6402 12.8997 29.3084 13 30 13V32.31C30 32.76 29.8199 33.19 29.4998 33.51C29.1797 33.83 28.7495 34 28.2993 34H7.71071C7.26053 34 6.83035 33.83 6.50021 33.51C6.18008 33.19 6 32.76 6 32.31V6.7C6.01 6.2 6.2401 5.73 6.62026 5.41C7.00042 5.09 7.50063 4.94 8.00083 5.01H10.0017V7.01H8.00083V32H28.0092V12.7128Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M23.0646 5.04504C23.022 5.35728 23 5.67607 23 6C23 7.9165 23.7702 9.65311 25.0179 10.9172V11.01H11.0121V7.34C11.0221 6.06 12.0625 5.01 13.3431 5.01H14.1334C14.5836 3.24 16.1842 2 18.005 2C19.8258 2 21.4264 3.24 21.8766 5.01H22.6769C22.8089 5.01103 22.9384 5.023 23.0646 5.04504ZM13.0029 7.34V9.01H23.0071V7.34C23.0071 7.16 22.857 7.01 22.6769 7.01H20.0058V6.01C20.0058 4.91 19.1055 4.01 18.005 4.01C16.9045 4.01 16.0042 4.91 16.0042 6.01V7.01H13.3331C13.253 7.01 13.163 7.05 13.103 7.11C13.0329 7.17 13.0029 7.25 13.0029 7.34Z\"/><path d=\"M25.0079 16.01V14.01H11.0021V16.01H25.0079Z\"/><path d=\"M25.0079 18.01V20.01H11.0021V18.01H25.0079Z\"/><path d=\"M25.0079 24.01V22.01H11.0021V24.01H25.0079Z\"/><path d=\"M25.0079 26.01V28.01H11.0021V26.01H25.0079Z\"/>',\n    solid: '<path d=\"M29.4704 5.5C29.1507 5.18 28.7211 5.01 28.2614 5.01H21.8468C21.3972 3.23 19.7985 2 17.98 2C16.1615 2 14.5629 3.24 14.1132 5.01H7.99833C7.49875 4.95 6.99917 5.09 6.61948 5.41C6.2398 5.73 6.00999 6.2 6 6.7V32.31C6 32.76 6.17985 33.19 6.50958 33.51C6.82931 33.83 7.25895 34 7.71857 34H28.2814C28.7311 34 29.1607 33.83 29.4904 33.51C29.8201 33.19 29.99 32.76 30 32.31V6.7C30 6.25 29.8201 5.82 29.4904 5.5H29.4704ZM24.9842 28H10.9958V26H24.9842V28ZM24.9842 24H10.9958V22H24.9842V24ZM24.9842 20H10.9958V18H24.9842V20ZM24.9842 16H10.9958V14H24.9842V16ZM24.9842 10H10.9958V7.33C10.9958 7.24 11.0258 7.16 11.0958 7.1C11.1557 7.04 11.2456 7 11.3256 7H15.9917V6C15.9917 5.83 16.0216 5.66 16.0616 5.5C16.0616 5.48 16.0716 5.45 16.0816 5.43C16.1316 5.28 16.1915 5.13 16.2714 5C16.6211 4.41 17.2506 4 17.99 4C18.7294 4 19.3689 4.4 19.7086 5C19.7885 5.14 19.8485 5.28 19.8984 5.43C19.8984 5.45 19.9184 5.48 19.9184 5.5C19.9584 5.66 19.9883 5.83 19.9883 6V7H24.6545C24.8343 7 24.9842 7.15 24.9842 7.33V10Z\"/>',\n    solidBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M30 13C29.9933 13 29.9867 13 29.98 13C27.6033 13 25.5034 11.8135 24.2395 10H10.9958V7.33C10.9958 7.24 11.0258 7.16 11.0958 7.1C11.1557 7.04 11.2456 7 11.3256 7H15.9917V6C15.9917 5.83 16.0216 5.66 16.0616 5.5C16.0616 5.48 16.0716 5.45 16.0816 5.43C16.1316 5.28 16.1915 5.13 16.2714 5C16.6211 4.41 17.2506 4 17.99 4C18.7294 4 19.3689 4.4 19.7086 5C19.7885 5.14 19.8485 5.28 19.8984 5.43C19.8984 5.44 19.9034 5.4525 19.9084 5.465C19.9134 5.4775 19.9184 5.49 19.9184 5.5C19.9584 5.66 19.9883 5.83 19.9883 6V7H23.0567C23.01 6.6734 22.9858 6.33952 22.9858 6C22.9858 5.66394 23.0095 5.33342 23.0553 5.01H21.8468C21.3972 3.23 19.7985 2 17.98 2C16.1615 2 14.5629 3.24 14.1132 5.01H7.99833C7.49875 4.95 6.99917 5.09 6.61948 5.41C6.2398 5.73 6.00999 6.2 6 6.7V32.31C6 32.76 6.17985 33.19 6.50958 33.51C6.82931 33.83 7.25895 34 7.71857 34H28.2814C28.7311 34 29.1607 33.83 29.4904 33.51C29.8201 33.19 29.99 32.76 30 32.31V13ZM10.9958 26V28H24.9842V26H10.9958ZM10.9958 22V24H24.9842V22H10.9958ZM10.9958 18V20H24.9842V18H10.9958ZM10.9958 14V16H24.9842V14H10.9958Z\"/>'\n  })];\nexport { V as clipboardIcon, H as clipboardIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "V", "outline", "outlineBadged", "solid", "solidBadged", "clipboardIcon", "clipboardIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/clipboard.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"clipboard\",V=[\"clipboard\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M25.0079 7.34C25.0079 6.06 23.9575 5.02 22.6769 5.01H21.8766C21.4264 3.24 19.8258 2 18.005 2C16.1842 2 14.5836 3.24 14.1334 5.01H13.3431C12.0625 5.01 11.0221 6.06 11.0121 7.34V11.01H25.0179V7.34H25.0079ZM13.0029 9.01V7.34C13.0029 7.25 13.0329 7.17 13.103 7.11C13.163 7.05 13.253 7.01 13.3331 7.01H16.0042V6.01C16.0042 4.91 16.9045 4.01 18.005 4.01C19.1055 4.01 20.0058 4.91 20.0058 6.01V7.01H22.6769C22.857 7.01 23.0071 7.16 23.0071 7.34V9.01H13.0029ZM26.0083 5.01H28.2993C28.7495 5.01 29.1797 5.18 29.4998 5.5C29.8199 5.82 30 6.25 30 6.7V32.31C30 32.76 29.8199 33.19 29.4998 33.51C29.1797 33.83 28.7495 34 28.2993 34H7.71071C7.26053 34 6.83035 33.83 6.50021 33.51C6.18008 33.19 6 32.76 6 32.31V6.7C6.01 6.2 6.2401 5.73 6.62026 5.41C7.00042 5.09 7.50063 4.94 8.00083 5.01H10.0017V7.01H8.00083V32H28.0092V7.01H26.0083V5.01ZM25.0079 14.01V16.01H11.0021V14.01H25.0079ZM25.0079 20.01V18.01H11.0021V20.01H25.0079ZM25.0079 22.01V24.01H11.0021V22.01H25.0079ZM25.0079 28.01V26.01H11.0021V28.01H25.0079Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M28.0092 12.7128C28.6402 12.8997 29.3084 13 30 13V32.31C30 32.76 29.8199 33.19 29.4998 33.51C29.1797 33.83 28.7495 34 28.2993 34H7.71071C7.26053 34 6.83035 33.83 6.50021 33.51C6.18008 33.19 6 32.76 6 32.31V6.7C6.01 6.2 6.2401 5.73 6.62026 5.41C7.00042 5.09 7.50063 4.94 8.00083 5.01H10.0017V7.01H8.00083V32H28.0092V12.7128Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M23.0646 5.04504C23.022 5.35728 23 5.67607 23 6C23 7.9165 23.7702 9.65311 25.0179 10.9172V11.01H11.0121V7.34C11.0221 6.06 12.0625 5.01 13.3431 5.01H14.1334C14.5836 3.24 16.1842 2 18.005 2C19.8258 2 21.4264 3.24 21.8766 5.01H22.6769C22.8089 5.01103 22.9384 5.023 23.0646 5.04504ZM13.0029 7.34V9.01H23.0071V7.34C23.0071 7.16 22.857 7.01 22.6769 7.01H20.0058V6.01C20.0058 4.91 19.1055 4.01 18.005 4.01C16.9045 4.01 16.0042 4.91 16.0042 6.01V7.01H13.3331C13.253 7.01 13.163 7.05 13.103 7.11C13.0329 7.17 13.0029 7.25 13.0029 7.34Z\"/><path d=\"M25.0079 16.01V14.01H11.0021V16.01H25.0079Z\"/><path d=\"M25.0079 18.01V20.01H11.0021V18.01H25.0079Z\"/><path d=\"M25.0079 24.01V22.01H11.0021V24.01H25.0079Z\"/><path d=\"M25.0079 26.01V28.01H11.0021V26.01H25.0079Z\"/>',solid:'<path d=\"M29.4704 5.5C29.1507 5.18 28.7211 5.01 28.2614 5.01H21.8468C21.3972 3.23 19.7985 2 17.98 2C16.1615 2 14.5629 3.24 14.1132 5.01H7.99833C7.49875 4.95 6.99917 5.09 6.61948 5.41C6.2398 5.73 6.00999 6.2 6 6.7V32.31C6 32.76 6.17985 33.19 6.50958 33.51C6.82931 33.83 7.25895 34 7.71857 34H28.2814C28.7311 34 29.1607 33.83 29.4904 33.51C29.8201 33.19 29.99 32.76 30 32.31V6.7C30 6.25 29.8201 5.82 29.4904 5.5H29.4704ZM24.9842 28H10.9958V26H24.9842V28ZM24.9842 24H10.9958V22H24.9842V24ZM24.9842 20H10.9958V18H24.9842V20ZM24.9842 16H10.9958V14H24.9842V16ZM24.9842 10H10.9958V7.33C10.9958 7.24 11.0258 7.16 11.0958 7.1C11.1557 7.04 11.2456 7 11.3256 7H15.9917V6C15.9917 5.83 16.0216 5.66 16.0616 5.5C16.0616 5.48 16.0716 5.45 16.0816 5.43C16.1316 5.28 16.1915 5.13 16.2714 5C16.6211 4.41 17.2506 4 17.99 4C18.7294 4 19.3689 4.4 19.7086 5C19.7885 5.14 19.8485 5.28 19.8984 5.43C19.8984 5.45 19.9184 5.48 19.9184 5.5C19.9584 5.66 19.9883 5.83 19.9883 6V7H24.6545C24.8343 7 24.9842 7.15 24.9842 7.33V10Z\"/>',solidBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M30 13C29.9933 13 29.9867 13 29.98 13C27.6033 13 25.5034 11.8135 24.2395 10H10.9958V7.33C10.9958 7.24 11.0258 7.16 11.0958 7.1C11.1557 7.04 11.2456 7 11.3256 7H15.9917V6C15.9917 5.83 16.0216 5.66 16.0616 5.5C16.0616 5.48 16.0716 5.45 16.0816 5.43C16.1316 5.28 16.1915 5.13 16.2714 5C16.6211 4.41 17.2506 4 17.99 4C18.7294 4 19.3689 4.4 19.7086 5C19.7885 5.14 19.8485 5.28 19.8984 5.43C19.8984 5.44 19.9034 5.4525 19.9084 5.465C19.9134 5.4775 19.9184 5.49 19.9184 5.5C19.9584 5.66 19.9883 5.83 19.9883 6V7H23.0567C23.01 6.6734 22.9858 6.33952 22.9858 6C22.9858 5.66394 23.0095 5.33342 23.0553 5.01H21.8468C21.3972 3.23 19.7985 2 17.98 2C16.1615 2 14.5629 3.24 14.1132 5.01H7.99833C7.49875 4.95 6.99917 5.09 6.61948 5.41C6.2398 5.73 6.00999 6.2 6 6.7V32.31C6 32.76 6.17985 33.19 6.50958 33.51C6.82931 33.83 7.25895 34 7.71857 34H28.2814C28.7311 34 29.1607 33.83 29.4904 33.51C29.8201 33.19 29.99 32.76 30 32.31V13ZM10.9958 26V28H24.9842V26H10.9958ZM10.9958 22V24H24.9842V22H10.9958ZM10.9958 18V20H24.9842V18H10.9958ZM10.9958 14V16H24.9842V14H10.9958Z\"/>'})];export{V as clipboardIcon,H as clipboardIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,WAAW;EAACC,CAAC,GAAC,CAAC,WAAW,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,whCAAwhC;IAACC,aAAa,EAAC,6uCAA6uC;IAACC,KAAK,EAAC,2+BAA2+B;IAACC,WAAW,EAAC;EAA4sC,CAAC,CAAC,CAAC;AAAC,SAAOJ,CAAC,IAAIK,aAAa,EAACN,CAAC,IAAIO,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}