{"ast": null, "code": "import r from \"ramda/es/isNil\";\nimport m from \"ramda/es/sum\";\nimport t from \"ramda/es/curryN\";\nfunction o(m) {\n  return r(m) ? 0 : 1e3 * Number(m);\n}\nconst a = t(3, (r, t, o) => (r || 0) + m(t || []) - m(o || [])),\n  e = t(4, (r, m, t, o) => !!o && o(a(r, m, t)));\nfunction i(r, m) {\n  return Math.sign(m - r) * Math.abs(r - m);\n}\nexport { e as compareSumTo, o as getMillisecondsFromSeconds, i as getOffesetDifference, a as sumAndSubtract };", "map": {"version": 3, "names": ["r", "m", "t", "o", "Number", "a", "e", "i", "Math", "sign", "abs", "compareSumTo", "getMillisecondsFromSeconds", "getOffesetDifference", "sumAndSubtract"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/math.js"], "sourcesContent": ["import r from\"ramda/es/isNil\";import m from\"ramda/es/sum\";import t from\"ramda/es/curryN\";function o(m){return r(m)?0:1e3*Number(m)}const a=t(3,((r,t,o)=>(r||0)+m(t||[])-m(o||[]))),e=t(4,((r,m,t,o)=>!!o&&o(a(r,m,t))));function i(r,m){return Math.sign(m-r)*Math.abs(r-m)}export{e as compareSumTo,o as getMillisecondsFromSeconds,i as getOffesetDifference,a as sumAndSubtract};\n"], "mappings": "AAAA,OAAOA,CAAC,MAAK,gBAAgB;AAAC,OAAOC,CAAC,MAAK,cAAc;AAAC,OAAOC,CAAC,MAAK,iBAAiB;AAAC,SAASC,CAACA,CAACF,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACC,CAAC,CAAC,GAAC,CAAC,GAAC,GAAG,GAACG,MAAM,CAACH,CAAC,CAAC;AAAA;AAAC,MAAMI,CAAC,GAACH,CAAC,CAAC,CAAC,EAAE,CAACF,CAAC,EAACE,CAAC,EAACC,CAAC,KAAG,CAACH,CAAC,IAAE,CAAC,IAAEC,CAAC,CAACC,CAAC,IAAE,EAAE,CAAC,GAACD,CAAC,CAACE,CAAC,IAAE,EAAE,CAAE,CAAC;EAACG,CAAC,GAACJ,CAAC,CAAC,CAAC,EAAE,CAACF,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,KAAG,CAAC,CAACA,CAAC,IAAEA,CAAC,CAACE,CAAC,CAACL,CAAC,EAACC,CAAC,EAACC,CAAC,CAAC,CAAE,CAAC;AAAC,SAASK,CAACA,CAACP,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOO,IAAI,CAACC,IAAI,CAACR,CAAC,GAACD,CAAC,CAAC,GAACQ,IAAI,CAACE,GAAG,CAACV,CAAC,GAACC,CAAC,CAAC;AAAA;AAAC,SAAOK,CAAC,IAAIK,YAAY,EAACR,CAAC,IAAIS,0BAA0B,EAACL,CAAC,IAAIM,oBAAoB,EAACR,CAAC,IAAIS,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}