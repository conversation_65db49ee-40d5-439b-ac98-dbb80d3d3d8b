{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst o = \"music-note\",\n  t = [\"music-note\", C({\n    outline: '<path d=\"M30.968 6.4C27.7058 2.05 19.3602 2 19.01 2H18.0093V23.41C16.2281 22.11 13.4662 21.65 10.5543 22.37C8.52293 22.88 6.73173 23.9 5.52092 25.26C4.22005 26.72 3.72972 28.39 4.12998 29.97C4.53025 31.55 5.77108 32.79 7.62232 33.47C8.58297 33.82 9.64368 34 10.7544 34C11.635 34 12.5456 33.89 13.4462 33.66C15.4776 33.15 17.2688 32.13 18.4796 30.77C19.5103 29.61 20.0306 28.31 20.0006 27.04C20.0006 27.02 20.0006 27.01 20.0006 27V10C23.6331 10 26.4249 11.82 26.4549 11.83C26.6251 11.94 26.8152 12 27.0053 12C27.3255 12 27.6457 11.84 27.8359 11.55C28.1461 11.09 28.016 10.47 27.5557 10.16C27.4256 10.07 24.2535 7.99 20.0006 7.99V4.04C22.1421 4.19 27.2855 4.83 29.3569 7.59C31.7985 10.85 26.505 16.23 26.4449 16.28C26.0547 16.67 26.0547 17.3 26.4449 17.69C26.8352 18.08 27.4656 18.08 27.8559 17.69C28.1261 17.42 34.4303 11.02 30.948 6.38L30.968 6.4ZM16.9486 29.45C16.0079 30.51 14.587 31.31 12.9459 31.72C11.3048 32.13 9.66369 32.08 8.3328 31.59C7.132 31.15 6.33146 30.4 6.10131 29.48C5.87115 28.56 6.20137 27.53 7.05194 26.58C7.99257 25.52 9.41353 24.72 11.0546 24.31C11.7951 24.13 12.5256 24.04 13.2261 24.04C15.5576 24.04 17.5089 25 17.9092 26.55C18.1494 27.47 17.8091 28.5 16.9586 29.45H16.9486Z\"/>',\n    solid: '<path d=\"M30.9583 6.4C27.6973 2.05 19.3547 2 19.0046 2C18.4544 2 18.0043 2.45 18.0043 3V23.41C16.2237 22.11 13.4629 21.65 10.5519 22.37C8.52131 22.88 6.73075 23.9 5.52037 25.26C4.21996 26.72 3.72981 28.39 4.12994 29.97C4.53006 31.55 5.77045 32.79 7.62103 33.47C8.58133 33.82 9.64166 34 10.752 34C11.6323 34 12.5426 33.89 13.4428 33.66C15.4735 33.15 17.264 32.13 18.4744 30.77C19.5047 29.61 20.0249 28.31 19.9949 27.04C19.9949 27.02 19.9949 27.01 19.9949 27V10C23.1159 10 27.5773 11.09 28.2575 12.45C28.5476 13.03 28.4275 13.31 26.807 15.72L26.3169 16.45C26.0268 16.88 26.1168 17.45 26.5169 17.78C26.9171 18.1 27.4972 18.08 27.8574 17.71C28.1274 17.44 34.4294 11.04 30.9483 6.4H30.9583Z\"/>'\n  })];\nexport { t as musicNoteIcon, o as musicNoteIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "o", "t", "outline", "solid", "musicNoteIcon", "musicNoteIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/music-note.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const o=\"music-note\",t=[\"music-note\",C({outline:'<path d=\"M30.968 6.4C27.7058 2.05 19.3602 2 19.01 2H18.0093V23.41C16.2281 22.11 13.4662 21.65 10.5543 22.37C8.52293 22.88 6.73173 23.9 5.52092 25.26C4.22005 26.72 3.72972 28.39 4.12998 29.97C4.53025 31.55 5.77108 32.79 7.62232 33.47C8.58297 33.82 9.64368 34 10.7544 34C11.635 34 12.5456 33.89 13.4462 33.66C15.4776 33.15 17.2688 32.13 18.4796 30.77C19.5103 29.61 20.0306 28.31 20.0006 27.04C20.0006 27.02 20.0006 27.01 20.0006 27V10C23.6331 10 26.4249 11.82 26.4549 11.83C26.6251 11.94 26.8152 12 27.0053 12C27.3255 12 27.6457 11.84 27.8359 11.55C28.1461 11.09 28.016 10.47 27.5557 10.16C27.4256 10.07 24.2535 7.99 20.0006 7.99V4.04C22.1421 4.19 27.2855 4.83 29.3569 7.59C31.7985 10.85 26.505 16.23 26.4449 16.28C26.0547 16.67 26.0547 17.3 26.4449 17.69C26.8352 18.08 27.4656 18.08 27.8559 17.69C28.1261 17.42 34.4303 11.02 30.948 6.38L30.968 6.4ZM16.9486 29.45C16.0079 30.51 14.587 31.31 12.9459 31.72C11.3048 32.13 9.66369 32.08 8.3328 31.59C7.132 31.15 6.33146 30.4 6.10131 29.48C5.87115 28.56 6.20137 27.53 7.05194 26.58C7.99257 25.52 9.41353 24.72 11.0546 24.31C11.7951 24.13 12.5256 24.04 13.2261 24.04C15.5576 24.04 17.5089 25 17.9092 26.55C18.1494 27.47 17.8091 28.5 16.9586 29.45H16.9486Z\"/>',solid:'<path d=\"M30.9583 6.4C27.6973 2.05 19.3547 2 19.0046 2C18.4544 2 18.0043 2.45 18.0043 3V23.41C16.2237 22.11 13.4629 21.65 10.5519 22.37C8.52131 22.88 6.73075 23.9 5.52037 25.26C4.21996 26.72 3.72981 28.39 4.12994 29.97C4.53006 31.55 5.77045 32.79 7.62103 33.47C8.58133 33.82 9.64166 34 10.752 34C11.6323 34 12.5426 33.89 13.4428 33.66C15.4735 33.15 17.264 32.13 18.4744 30.77C19.5047 29.61 20.0249 28.31 19.9949 27.04C19.9949 27.02 19.9949 27.01 19.9949 27V10C23.1159 10 27.5773 11.09 28.2575 12.45C28.5476 13.03 28.4275 13.31 26.807 15.72L26.3169 16.45C26.0268 16.88 26.1168 17.45 26.5169 17.78C26.9171 18.1 27.4972 18.08 27.8574 17.71C28.1274 17.44 34.4294 11.04 30.9483 6.4H30.9583Z\"/>'})];export{t as musicNoteIcon,o as musicNoteIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,YAAY;EAACC,CAAC,GAAC,CAAC,YAAY,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,krCAAkrC;IAACC,KAAK,EAAC;EAAkrB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,aAAa,EAACJ,CAAC,IAAIK,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}