{"ast": null, "code": "import _curry2 from \"./internal/_curry2.js\";\nimport _equals from \"./internal/_equals.js\";\n/**\n * Returns `true` if its arguments are equivalent, `false` otherwise. Handles\n * cyclical data structures.\n *\n * Dispatches symmetrically to the `equals` methods of both arguments, if\n * present.\n *\n * @func\n * @memberOf R\n * @since v0.15.0\n * @category Relation\n * @sig a -> b -> Boolean\n * @param {*} a\n * @param {*} b\n * @return {Boolean}\n * @example\n *\n *      R.equals(1, 1); //=> true\n *      R.equals(1, '1'); //=> false\n *      R.equals([1, 2, 3], [1, 2, 3]); //=> true\n *\n *      const a = {}; a.v = a;\n *      const b = {}; b.v = b;\n *      R.equals(a, b); //=> true\n */\n\nvar equals = /*#__PURE__*/\n_curry2(function equals(a, b) {\n  return _equals(a, b, [], []);\n});\nexport default equals;", "map": {"version": 3, "names": ["_curry2", "_equals", "equals", "a", "b"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/equals.js"], "sourcesContent": ["import _curry2 from \"./internal/_curry2.js\";\nimport _equals from \"./internal/_equals.js\";\n/**\n * Returns `true` if its arguments are equivalent, `false` otherwise. Handles\n * cyclical data structures.\n *\n * Dispatches symmetrically to the `equals` methods of both arguments, if\n * present.\n *\n * @func\n * @memberOf R\n * @since v0.15.0\n * @category Relation\n * @sig a -> b -> Boolean\n * @param {*} a\n * @param {*} b\n * @return {Boolean}\n * @example\n *\n *      R.equals(1, 1); //=> true\n *      R.equals(1, '1'); //=> false\n *      R.equals([1, 2, 3], [1, 2, 3]); //=> true\n *\n *      const a = {}; a.v = a;\n *      const b = {}; b.v = b;\n *      R.equals(a, b); //=> true\n */\n\nvar equals =\n/*#__PURE__*/\n_curry2(function equals(a, b) {\n  return _equals(a, b, [], []);\n});\n\nexport default equals;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,OAAO,MAAM,uBAAuB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,MAAM,GACV;AACAF,OAAO,CAAC,SAASE,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC5B,OAAOH,OAAO,CAACE,CAAC,EAAEC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;AAC9B,CAAC,CAAC;AAEF,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}