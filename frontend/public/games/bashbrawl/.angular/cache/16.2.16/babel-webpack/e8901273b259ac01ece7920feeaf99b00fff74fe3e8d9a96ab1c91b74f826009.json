{"ast": null, "code": "const i = \"cds-navigation-group-open\",\n  a = [{\n    target: \".navigation-group-items\",\n    animation: [{\n      opacity: 0,\n      height: \"0\"\n    }, {\n      opacity: 1,\n      height: \"from:cds-navigation-group\"\n    }],\n    options: {\n      duration: \"--animation-duration\",\n      easing: \"--animation-easing\",\n      fill: \"forwards\"\n    }\n  }];\nexport { a as AnimationNavigationGroupOpenConfig, i as AnimationNavigationGroupOpenName };", "map": {"version": 3, "names": ["i", "a", "target", "animation", "opacity", "height", "options", "duration", "easing", "fill", "AnimationNavigationGroupOpenConfig", "AnimationNavigationGroupOpenName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/motion/animations/cds-navigation-group-open.js"], "sourcesContent": ["const i=\"cds-navigation-group-open\",a=[{target:\".navigation-group-items\",animation:[{opacity:0,height:\"0\"},{opacity:1,height:\"from:cds-navigation-group\"}],options:{duration:\"--animation-duration\",easing:\"--animation-easing\",fill:\"forwards\"}}];export{a as AnimationNavigationGroupOpenConfig,i as AnimationNavigationGroupOpenName};\n"], "mappings": "AAAA,MAAMA,CAAC,GAAC,2BAA2B;EAACC,CAAC,GAAC,CAAC;IAACC,MAAM,EAAC,yBAAyB;IAACC,SAAS,EAAC,CAAC;MAACC,OAAO,EAAC,CAAC;MAACC,MAAM,EAAC;IAAG,CAAC,EAAC;MAACD,OAAO,EAAC,CAAC;MAACC,MAAM,EAAC;IAA2B,CAAC,CAAC;IAACC,OAAO,EAAC;MAACC,QAAQ,EAAC,sBAAsB;MAACC,MAAM,EAAC,oBAAoB;MAACC,IAAI,EAAC;IAAU;EAAC,CAAC,CAAC;AAAC,SAAOR,CAAC,IAAIS,kCAAkC,EAACV,CAAC,IAAIW,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}