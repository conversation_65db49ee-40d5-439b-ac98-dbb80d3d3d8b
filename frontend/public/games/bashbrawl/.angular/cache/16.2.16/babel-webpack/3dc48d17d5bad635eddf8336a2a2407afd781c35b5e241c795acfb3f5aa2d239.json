{"ast": null, "code": "function t() {\n  return (t, e) => {\n    const n = t.connectedCallback;\n    t.connectedCallback = function () {\n      this[`__${e}StyleTag`] = document.createElement(\"style\"), this.prepend(this[`__${e}StyleTag`]), this[`__${e}StyleTag`].innerText = this[`__${e}GlobalStyle`].toString(), n && n.apply(this);\n    };\n    const o = {\n      set: function (t) {\n        this[`__${e}GlobalStyle`] = t;\n      },\n      enumerable: !0,\n      configurable: !0\n    };\n    return void 0 !== e ? function (t, e, n) {\n      Object.defineProperty(e, n, t);\n    }(o, t, e) : function (t, e) {\n      return {\n        kind: \"method\",\n        placement: \"prototype\",\n        key: e.key,\n        descriptor: t\n      };\n    }(o, t);\n  };\n}\nexport { t as globalStyle };", "map": {"version": 3, "names": ["t", "e", "n", "connectedCallback", "document", "createElement", "prepend", "innerText", "toString", "apply", "o", "set", "enumerable", "configurable", "Object", "defineProperty", "kind", "placement", "key", "descriptor", "globalStyle"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/decorators/global-style.js"], "sourcesContent": ["function t(){return(t,e)=>{const n=t.connectedCallback;t.connectedCallback=function(){this[`__${e}StyleTag`]=document.createElement(\"style\"),this.prepend(this[`__${e}StyleTag`]),this[`__${e}StyleTag`].innerText=this[`__${e}GlobalStyle`].toString(),n&&n.apply(this)};const o={set:function(t){this[`__${e}GlobalStyle`]=t},enumerable:!0,configurable:!0};return void 0!==e?function(t,e,n){Object.defineProperty(e,n,t)}(o,t,e):function(t,e){return{kind:\"method\",placement:\"prototype\",key:e.key,descriptor:t}}(o,t)}}export{t as globalStyle};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAM,CAACA,CAAC,EAACC,CAAC,KAAG;IAAC,MAAMC,CAAC,GAACF,CAAC,CAACG,iBAAiB;IAACH,CAAC,CAACG,iBAAiB,GAAC,YAAU;MAAC,IAAI,CAAE,KAAIF,CAAE,UAAS,CAAC,GAACG,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC,EAAC,IAAI,CAACC,OAAO,CAAC,IAAI,CAAE,KAAIL,CAAE,UAAS,CAAC,CAAC,EAAC,IAAI,CAAE,KAAIA,CAAE,UAAS,CAAC,CAACM,SAAS,GAAC,IAAI,CAAE,KAAIN,CAAE,aAAY,CAAC,CAACO,QAAQ,CAAC,CAAC,EAACN,CAAC,IAAEA,CAAC,CAACO,KAAK,CAAC,IAAI,CAAC;IAAA,CAAC;IAAC,MAAMC,CAAC,GAAC;MAACC,GAAG,EAAC,SAAAA,CAASX,CAAC,EAAC;QAAC,IAAI,CAAE,KAAIC,CAAE,aAAY,CAAC,GAACD,CAAC;MAAA,CAAC;MAACY,UAAU,EAAC,CAAC,CAAC;MAACC,YAAY,EAAC,CAAC;IAAC,CAAC;IAAC,OAAO,KAAK,CAAC,KAAGZ,CAAC,GAAC,UAASD,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;MAACY,MAAM,CAACC,cAAc,CAACd,CAAC,EAACC,CAAC,EAACF,CAAC,CAAC;IAAA,CAAC,CAACU,CAAC,EAACV,CAAC,EAACC,CAAC,CAAC,GAAC,UAASD,CAAC,EAACC,CAAC,EAAC;MAAC,OAAM;QAACe,IAAI,EAAC,QAAQ;QAACC,SAAS,EAAC,WAAW;QAACC,GAAG,EAACjB,CAAC,CAACiB,GAAG;QAACC,UAAU,EAACnB;MAAC,CAAC;IAAA,CAAC,CAACU,CAAC,EAACV,CAAC,CAAC;EAAA,CAAC;AAAA;AAAC,SAAOA,CAAC,IAAIoB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}