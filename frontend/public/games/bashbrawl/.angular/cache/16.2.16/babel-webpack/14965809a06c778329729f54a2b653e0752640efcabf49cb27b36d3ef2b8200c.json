{"ast": null, "code": "import { __decorate as t } from \"tslib\";\nimport { property as e } from \"@cds/core/internal\";\nimport { CdsButtonAction as o } from \"@cds/core/button-action\";\nconst s = \"cds-internal-close-button\";\nclass r extends o {\n  constructor() {\n    super(...arguments);\n    this.shape = \"close\";\n  }\n  connectedCallback() {\n    super.connectedCallback(), this.ariaLabel = this.ariaLabel ? this.ariaLabel : this.i18n.close;\n  }\n}\nt([e({\n  type: String\n})], r.prototype, \"shape\", void 0);\nexport { s as CdsCloseButtonTagName, r as CdsInternalCloseButton };", "map": {"version": 3, "names": ["__decorate", "t", "property", "e", "CdsButtonAction", "o", "s", "r", "constructor", "arguments", "shape", "connectedCallback", "aria<PERSON><PERSON><PERSON>", "i18n", "close", "type", "String", "prototype", "CdsCloseButtonTagName", "CdsInternalCloseButton"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal-components/close-button/close-button.element.js"], "sourcesContent": ["import{__decorate as t}from\"tslib\";import{property as e}from\"@cds/core/internal\";import{CdsButtonAction as o}from\"@cds/core/button-action\";const s=\"cds-internal-close-button\";class r extends o{constructor(){super(...arguments);this.shape=\"close\"}connectedCallback(){super.connectedCallback(),this.ariaLabel=this.ariaLabel?this.ariaLabel:this.i18n.close}}t([e({type:String})],r.prototype,\"shape\",void 0);export{s as CdsCloseButtonTagName,r as CdsInternalCloseButton};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,yBAAyB;AAAC,MAAMC,CAAC,GAAC,2BAA2B;AAAC,MAAMC,CAAC,SAASF,CAAC;EAACG,WAAWA,CAAA,EAAE;IAAC,KAAK,CAAC,GAAGC,SAAS,CAAC;IAAC,IAAI,CAACC,KAAK,GAAC,OAAO;EAAA;EAACC,iBAAiBA,CAAA,EAAE;IAAC,KAAK,CAACA,iBAAiB,CAAC,CAAC,EAAC,IAAI,CAACC,SAAS,GAAC,IAAI,CAACA,SAAS,GAAC,IAAI,CAACA,SAAS,GAAC,IAAI,CAACC,IAAI,CAACC,KAAK;EAAA;AAAC;AAACb,CAAC,CAAC,CAACE,CAAC,CAAC;EAACY,IAAI,EAACC;AAAM,CAAC,CAAC,CAAC,EAACT,CAAC,CAACU,SAAS,EAAC,OAAO,EAAC,KAAK,CAAC,CAAC;AAAC,SAAOX,CAAC,IAAIY,qBAAqB,EAACX,CAAC,IAAIY,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}