{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"alarm-clock\",\n  d = [\"alarm-clock\", C({\n    outline: '<path d=\"M9.63 4.39014C9.13 4.17006 8.58 4.03001 8 4.03001C5.79 4.03001 4 5.82065 4 8.03144C4 8.62165 4.13 9.17185 4.36 9.67203C5.68 7.53126 7.49 5.71061 9.63 4.39014ZM18 4C10.28 4 4 10.2822 4 18.005C4 21.4262 5.24 24.5673 7.29 26.9982L4.26 30.3294C3.89 30.7395 3.92 31.3698 4.33 31.7399C4.52 31.91 4.76 32 5 32C5.27 32 5.54 31.89 5.74 31.6699L8.69 28.4187C11.17 30.6395 14.43 32 18 32C21.57 32 24.84 30.6395 27.31 28.4187L30.26 31.6699C30.46 31.89 30.73 32 31 32C31.24 32 31.48 31.91 31.67 31.7399C32.08 31.3698 32.11 30.7395 31.74 30.3294L28.71 26.9982C30.76 24.5673 32 21.4262 32 18.005C32 10.2822 25.72 4 18 4ZM18 30.0093C11.38 30.0093 6 24.6274 6 18.005C6 11.3826 11.38 6.00071 18 6.00071C24.62 6.00071 30 11.3826 30 18.005C30 24.6274 24.62 30.0093 18 30.0093ZM31.64 9.68203C31.87 9.18185 32 8.62165 32 8.04144C32 5.82065 30.21 4.03001 28 4.03001C27.42 4.03001 26.87 4.16006 26.37 4.39014C28.52 5.71061 30.32 7.53126 31.64 9.68203ZM19 17.5949V10.0021C19 9.45195 18.55 9.00179 18 9.00179C17.45 9.00179 17 9.45195 17 10.0021V18.005C17 18.2751 17.11 18.5252 17.29 18.7153L20.29 21.7163C20.49 21.9164 20.74 22.0064 21 22.0064C21.26 22.0064 21.51 21.9064 21.71 21.7163C22.1 21.3262 22.1 20.696 21.71 20.3058L19 17.5949Z\"/>',\n    outlineAlerted: '<path d=\"M26.9039 1.64597L21.2222 11.1156C20.9526 11.4981 20.9281 11.9946 21.1588 12.4002C21.3896 12.8058 21.8363 13.0517 22.3148 13.0364H33.6881C34.1666 13.0517 34.6134 12.8058 34.8441 12.4002C35.0748 11.9946 35.0503 11.4981 34.7808 11.1156L29.0991 1.64597C28.8711 1.26889 28.4532 1.03711 28.0015 1.03711C27.5497 1.03711 27.1319 1.26889 26.9039 1.64597Z\"/><path d=\"M31.0914 13.0398C31.0914 13.0398 31.0914 13.0399 31.0914 13.0398L28.9261 13.0399C28.9261 13.0399 28.9261 13.0399 28.9261 13.0399L31.0914 13.0398Z\"/><path d=\"M22.6774 4.80366C21.2141 4.28334 19.6395 4 18 4C10.28 4 4 10.2822 4 18.005C4 21.4262 5.24 24.5673 7.29 26.9982L4.26 30.3294C3.89 30.7396 3.92 31.3698 4.33 31.7399C4.52 31.91 4.76 32 5 32C5.27 32 5.54 31.89 5.74 31.6699L8.69 28.4187C11.17 30.6395 14.43 32 18 32C21.57 32 24.84 30.6395 27.31 28.4187L30.26 31.6699C30.46 31.89 30.73 32 31 32C31.24 32 31.48 31.91 31.67 31.7399C32.08 31.3698 32.11 30.7396 31.74 30.3294L28.71 26.9982C30.76 24.5673 32 21.4262 32 18.005C32 16.9882 31.8911 15.9964 31.6845 15.0406H29.6303C29.8717 15.989 30 16.9822 30 18.005C30 24.6274 24.62 30.0093 18 30.0093C11.38 30.0093 6 24.6274 6 18.005C6 11.3826 11.38 6.00071 18 6.00071C19.2628 6.00071 20.4805 6.19655 21.6243 6.55946L22.6774 4.80366Z\"/><path d=\"M8 4.03001C8.58 4.03001 9.13 4.17006 9.63 4.39014C7.49 5.71061 5.68 7.53126 4.36 9.67203C4.13 9.17185 4 8.62165 4 8.03144C4 5.82065 5.79 4.03001 8 4.03001Z\"/><path d=\"M19 10.0021V17.5949L21.71 20.3058C22.1 20.696 22.1 21.3262 21.71 21.7163C21.51 21.9064 21.26 22.0064 21 22.0064C20.74 22.0064 20.49 21.9164 20.29 21.7163L17.29 18.7153C17.11 18.5252 17 18.2751 17 18.005V10.0021C17 9.45195 17.45 9.00179 18 9.00179C18.55 9.00179 19 9.45195 19 10.0021Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M31.0461 12.9224C30.7049 12.9735 30.3555 13 30 13C29.6136 13 29.2346 12.9687 28.8652 12.9085C29.593 14.456 30 16.1836 30 18.005C30 24.6274 24.62 30.0093 18 30.0093C11.38 30.0093 6 24.6274 6 18.005C6 11.3826 11.38 6.00071 18 6.00071C19.8195 6.00071 21.5453 6.40726 23.0914 7.13437C23.0313 6.76515 23 6.38623 23 6C23 5.64418 23.0265 5.29456 23.0778 4.95302C21.5028 4.3378 19.7901 4 18 4C10.28 4 4 10.2822 4 18.005C4 21.4262 5.24 24.5673 7.29 26.9982L4.26 30.3294C3.89 30.7395 3.92 31.3698 4.33 31.7399C4.52 31.91 4.76 32 5 32C5.27 32 5.54 31.89 5.74 31.6699L8.69 28.4187C11.17 30.6395 14.43 32 18 32C21.57 32 24.84 30.6395 27.31 28.4187L30.26 31.6699C30.46 31.89 30.73 32 31 32C31.24 32 31.48 31.91 31.67 31.7399C32.08 31.3698 32.11 30.7395 31.74 30.3294L28.71 26.9982C30.76 24.5673 32 21.4262 32 18.005C32 16.2131 31.6619 14.4987 31.0461 12.9224Z\"/><path d=\"M8 4.03001C8.58 4.03001 9.13 4.17006 9.63 4.39014C7.49 5.71061 5.68 7.53126 4.36 9.67203C4.13 9.17185 4 8.62165 4 8.03144C4 5.82065 5.79 4.03001 8 4.03001Z\"/><path d=\"M19 10.0021V17.5949L21.71 20.3058C22.1 20.696 22.1 21.3262 21.71 21.7163C21.51 21.9064 21.26 22.0064 21 22.0064C20.74 22.0064 20.49 21.9164 20.29 21.7163L17.29 18.7153C17.11 18.5252 17 18.2751 17 18.005V10.0021C17 9.45195 17.45 9.00179 18 9.00179C18.55 9.00179 19 9.45195 19 10.0021Z\"/>',\n    solid: '<path d=\"M31.64 9.68203C31.87 9.18185 32 8.62165 32 8.04144C32 5.82065 30.21 4.03001 28 4.03001C27.42 4.03001 26.87 4.16006 26.37 4.39014C28.52 5.71061 30.32 7.53126 31.64 9.68203ZM9.63 4.39014C9.13 4.17006 8.58 4.03001 8 4.03001C5.79 4.03001 4 5.82065 4 8.03144C4 8.62165 4.13 9.17185 4.36 9.67203C5.68 7.53126 7.49 5.71061 9.63 4.39014ZM18 4C10.28 4 4 10.2822 4 18.005C4 21.4262 5.24 24.5673 7.29 26.9982L4.26 30.3294C3.89 30.7395 3.92 31.3698 4.33 31.7399C4.52 31.91 4.76 32 5 32C5.27 32 5.54 31.89 5.74 31.6699L8.69 28.4187C11.17 30.6395 14.43 32 18 32C21.57 32 24.84 30.6395 27.31 28.4187L30.26 31.6699C30.46 31.89 30.73 32 31 32C31.24 32 31.48 31.91 31.67 31.7399C32.08 31.3698 32.11 30.7395 31.74 30.3294L28.71 26.9982C30.76 24.5673 32 21.4262 32 18.005C32 10.2822 25.72 4 18 4ZM21.85 21.8564C21.62 22.0865 21.32 22.2065 21 22.2065C20.68 22.2065 20.38 22.0865 20.15 21.8564L17.15 18.8553C16.92 18.6252 16.8 18.3251 16.8 18.005V10.0021C16.8 9.34191 17.34 8.80171 18 8.80171C18.66 8.80171 19.2 9.34191 19.2 10.0021V17.5048L21.85 20.1558C22.08 20.3859 22.2 20.686 22.2 21.0061C22.2 21.3262 22.08 21.6263 21.85 21.8564Z\"/>',\n    solidAlerted: '<path d=\"M26.9039 1.64597L21.2222 11.1156C20.9526 11.4981 20.9281 11.9946 21.1588 12.4002C21.3896 12.8058 21.8363 13.0517 22.3148 13.0364H33.6881C34.1666 13.0517 34.6134 12.8058 34.8441 12.4002C35.0748 11.9946 35.0503 11.4981 34.7808 11.1156L29.0991 1.64597C28.8711 1.26889 28.4532 1.03711 28.0015 1.03711C27.5497 1.03711 27.1319 1.26889 26.9039 1.64597Z\"/><path d=\"M31.0914 13.0399C31.0914 13.0399 31.0914 13.0399 31.0914 13.0399V13.0399Z\"/><path d=\"M22.6774 4.80366C21.2141 4.28334 19.6395 4 18 4C10.28 4 4 10.2822 4 18.005C4 21.4262 5.24 24.5673 7.29 26.9982L4.26 30.3294C3.89 30.7395 3.92 31.3698 4.33 31.7399C4.52 31.91 4.76 32 5 32C5.27 32 5.54 31.89 5.74 31.6699L8.69 28.4187C11.17 30.6395 14.43 32 18 32C21.57 32 24.84 30.6395 27.31 28.4187L30.26 31.6699C30.46 31.89 30.73 32 31 32C31.24 32 31.48 31.91 31.67 31.7399C32.08 31.3698 32.11 30.7395 31.74 30.3294L28.71 26.9982C30.76 24.5673 32 21.4262 32 18.005C32 16.9882 31.8911 15.9964 31.6845 15.0406H22.3395C21.1577 15.0643 20.0233 14.4527 19.4206 13.3927C19.3333 13.2393 19.2599 13.0809 19.2 12.9192V17.5048L21.85 20.1558C22.08 20.3859 22.2 20.686 22.2 21.0061C22.2 21.3262 22.08 21.6263 21.85 21.8564C21.62 22.0865 21.32 22.2065 21 22.2065C20.68 22.2065 20.38 22.0865 20.15 21.8564L17.15 18.8553C16.92 18.6252 16.8 18.3251 16.8 18.005V10.0021C16.8 9.34191 17.34 8.80171 18 8.80171C18.66 8.80171 19.2 9.34191 19.2 10.0021V10.7026C19.285 10.4726 19.3972 10.2505 19.5362 10.0409L22.6774 4.80366Z\"/><path d=\"M8 4.03001C8.58 4.03001 9.13 4.17006 9.63 4.39014C7.49 5.71061 5.68 7.53126 4.36 9.67203C4.13 9.17185 4 8.62165 4 8.03144C4 5.82065 5.79 4.03001 8 4.03001Z\"/>',\n    solidBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M31.0461 12.9224C30.7049 12.9735 30.3555 13 30 13C26.134 13 23 9.86599 23 6C23 5.64418 23.0265 5.29456 23.0778 4.95302C21.5028 4.3378 19.7901 4 18 4C10.28 4 4 10.2822 4 18.005C4 21.4262 5.24 24.5673 7.29 26.9982L4.26 30.3294C3.89 30.7395 3.92 31.3698 4.33 31.7399C4.52 31.91 4.76 32 5 32C5.27 32 5.54 31.89 5.74 31.6699L8.69 28.4187C11.17 30.6395 14.43 32 18 32C21.57 32 24.84 30.6395 27.31 28.4187L30.26 31.6699C30.46 31.89 30.73 32 31 32C31.24 32 31.48 31.91 31.67 31.7399C32.08 31.3698 32.11 30.7395 31.74 30.3294L28.71 26.9982C30.76 24.5673 32 21.4262 32 18.005C32 16.2131 31.6619 14.4987 31.0461 12.9224ZM21 22.2065C21.32 22.2065 21.62 22.0865 21.85 21.8564C22.08 21.6263 22.2 21.3262 22.2 21.0061C22.2 20.686 22.08 20.3859 21.85 20.1558L19.2 17.5048V10.0021C19.2 9.34191 18.66 8.80171 18 8.80171C17.34 8.80171 16.8 9.34191 16.8 10.0021V18.005C16.8 18.3251 16.92 18.6252 17.15 18.8553L20.15 21.8564C20.38 22.0865 20.68 22.2065 21 22.2065Z\"/><path d=\"M8 4.03001C8.58 4.03001 9.13 4.17006 9.63 4.39014C7.49 5.71061 5.68 7.53126 4.36 9.67203C4.13 9.17185 4 8.62165 4 8.03144C4 5.82065 5.79 4.03001 8 4.03001Z\"/>'\n  })];\nexport { d as alarmClockIcon, L as alarmClockIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "d", "outline", "outlineAlerted", "outlineBadged", "solid", "solidAlerted", "solidBadged", "alarmClockIcon", "alarmClockIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/alarm-clock.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"alarm-clock\",d=[\"alarm-clock\",C({outline:'<path d=\"M9.63 4.39014C9.13 4.17006 8.58 4.03001 8 4.03001C5.79 4.03001 4 5.82065 4 8.03144C4 8.62165 4.13 9.17185 4.36 9.67203C5.68 7.53126 7.49 5.71061 9.63 4.39014ZM18 4C10.28 4 4 10.2822 4 18.005C4 21.4262 5.24 24.5673 7.29 26.9982L4.26 30.3294C3.89 30.7395 3.92 31.3698 4.33 31.7399C4.52 31.91 4.76 32 5 32C5.27 32 5.54 31.89 5.74 31.6699L8.69 28.4187C11.17 30.6395 14.43 32 18 32C21.57 32 24.84 30.6395 27.31 28.4187L30.26 31.6699C30.46 31.89 30.73 32 31 32C31.24 32 31.48 31.91 31.67 31.7399C32.08 31.3698 32.11 30.7395 31.74 30.3294L28.71 26.9982C30.76 24.5673 32 21.4262 32 18.005C32 10.2822 25.72 4 18 4ZM18 30.0093C11.38 30.0093 6 24.6274 6 18.005C6 11.3826 11.38 6.00071 18 6.00071C24.62 6.00071 30 11.3826 30 18.005C30 24.6274 24.62 30.0093 18 30.0093ZM31.64 9.68203C31.87 9.18185 32 8.62165 32 8.04144C32 5.82065 30.21 4.03001 28 4.03001C27.42 4.03001 26.87 4.16006 26.37 4.39014C28.52 5.71061 30.32 7.53126 31.64 9.68203ZM19 17.5949V10.0021C19 9.45195 18.55 9.00179 18 9.00179C17.45 9.00179 17 9.45195 17 10.0021V18.005C17 18.2751 17.11 18.5252 17.29 18.7153L20.29 21.7163C20.49 21.9164 20.74 22.0064 21 22.0064C21.26 22.0064 21.51 21.9064 21.71 21.7163C22.1 21.3262 22.1 20.696 21.71 20.3058L19 17.5949Z\"/>',outlineAlerted:'<path d=\"M26.9039 1.64597L21.2222 11.1156C20.9526 11.4981 20.9281 11.9946 21.1588 12.4002C21.3896 12.8058 21.8363 13.0517 22.3148 13.0364H33.6881C34.1666 13.0517 34.6134 12.8058 34.8441 12.4002C35.0748 11.9946 35.0503 11.4981 34.7808 11.1156L29.0991 1.64597C28.8711 1.26889 28.4532 1.03711 28.0015 1.03711C27.5497 1.03711 27.1319 1.26889 26.9039 1.64597Z\"/><path d=\"M31.0914 13.0398C31.0914 13.0398 31.0914 13.0399 31.0914 13.0398L28.9261 13.0399C28.9261 13.0399 28.9261 13.0399 28.9261 13.0399L31.0914 13.0398Z\"/><path d=\"M22.6774 4.80366C21.2141 4.28334 19.6395 4 18 4C10.28 4 4 10.2822 4 18.005C4 21.4262 5.24 24.5673 7.29 26.9982L4.26 30.3294C3.89 30.7396 3.92 31.3698 4.33 31.7399C4.52 31.91 4.76 32 5 32C5.27 32 5.54 31.89 5.74 31.6699L8.69 28.4187C11.17 30.6395 14.43 32 18 32C21.57 32 24.84 30.6395 27.31 28.4187L30.26 31.6699C30.46 31.89 30.73 32 31 32C31.24 32 31.48 31.91 31.67 31.7399C32.08 31.3698 32.11 30.7396 31.74 30.3294L28.71 26.9982C30.76 24.5673 32 21.4262 32 18.005C32 16.9882 31.8911 15.9964 31.6845 15.0406H29.6303C29.8717 15.989 30 16.9822 30 18.005C30 24.6274 24.62 30.0093 18 30.0093C11.38 30.0093 6 24.6274 6 18.005C6 11.3826 11.38 6.00071 18 6.00071C19.2628 6.00071 20.4805 6.19655 21.6243 6.55946L22.6774 4.80366Z\"/><path d=\"M8 4.03001C8.58 4.03001 9.13 4.17006 9.63 4.39014C7.49 5.71061 5.68 7.53126 4.36 9.67203C4.13 9.17185 4 8.62165 4 8.03144C4 5.82065 5.79 4.03001 8 4.03001Z\"/><path d=\"M19 10.0021V17.5949L21.71 20.3058C22.1 20.696 22.1 21.3262 21.71 21.7163C21.51 21.9064 21.26 22.0064 21 22.0064C20.74 22.0064 20.49 21.9164 20.29 21.7163L17.29 18.7153C17.11 18.5252 17 18.2751 17 18.005V10.0021C17 9.45195 17.45 9.00179 18 9.00179C18.55 9.00179 19 9.45195 19 10.0021Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M31.0461 12.9224C30.7049 12.9735 30.3555 13 30 13C29.6136 13 29.2346 12.9687 28.8652 12.9085C29.593 14.456 30 16.1836 30 18.005C30 24.6274 24.62 30.0093 18 30.0093C11.38 30.0093 6 24.6274 6 18.005C6 11.3826 11.38 6.00071 18 6.00071C19.8195 6.00071 21.5453 6.40726 23.0914 7.13437C23.0313 6.76515 23 6.38623 23 6C23 5.64418 23.0265 5.29456 23.0778 4.95302C21.5028 4.3378 19.7901 4 18 4C10.28 4 4 10.2822 4 18.005C4 21.4262 5.24 24.5673 7.29 26.9982L4.26 30.3294C3.89 30.7395 3.92 31.3698 4.33 31.7399C4.52 31.91 4.76 32 5 32C5.27 32 5.54 31.89 5.74 31.6699L8.69 28.4187C11.17 30.6395 14.43 32 18 32C21.57 32 24.84 30.6395 27.31 28.4187L30.26 31.6699C30.46 31.89 30.73 32 31 32C31.24 32 31.48 31.91 31.67 31.7399C32.08 31.3698 32.11 30.7395 31.74 30.3294L28.71 26.9982C30.76 24.5673 32 21.4262 32 18.005C32 16.2131 31.6619 14.4987 31.0461 12.9224Z\"/><path d=\"M8 4.03001C8.58 4.03001 9.13 4.17006 9.63 4.39014C7.49 5.71061 5.68 7.53126 4.36 9.67203C4.13 9.17185 4 8.62165 4 8.03144C4 5.82065 5.79 4.03001 8 4.03001Z\"/><path d=\"M19 10.0021V17.5949L21.71 20.3058C22.1 20.696 22.1 21.3262 21.71 21.7163C21.51 21.9064 21.26 22.0064 21 22.0064C20.74 22.0064 20.49 21.9164 20.29 21.7163L17.29 18.7153C17.11 18.5252 17 18.2751 17 18.005V10.0021C17 9.45195 17.45 9.00179 18 9.00179C18.55 9.00179 19 9.45195 19 10.0021Z\"/>',solid:'<path d=\"M31.64 9.68203C31.87 9.18185 32 8.62165 32 8.04144C32 5.82065 30.21 4.03001 28 4.03001C27.42 4.03001 26.87 4.16006 26.37 4.39014C28.52 5.71061 30.32 7.53126 31.64 9.68203ZM9.63 4.39014C9.13 4.17006 8.58 4.03001 8 4.03001C5.79 4.03001 4 5.82065 4 8.03144C4 8.62165 4.13 9.17185 4.36 9.67203C5.68 7.53126 7.49 5.71061 9.63 4.39014ZM18 4C10.28 4 4 10.2822 4 18.005C4 21.4262 5.24 24.5673 7.29 26.9982L4.26 30.3294C3.89 30.7395 3.92 31.3698 4.33 31.7399C4.52 31.91 4.76 32 5 32C5.27 32 5.54 31.89 5.74 31.6699L8.69 28.4187C11.17 30.6395 14.43 32 18 32C21.57 32 24.84 30.6395 27.31 28.4187L30.26 31.6699C30.46 31.89 30.73 32 31 32C31.24 32 31.48 31.91 31.67 31.7399C32.08 31.3698 32.11 30.7395 31.74 30.3294L28.71 26.9982C30.76 24.5673 32 21.4262 32 18.005C32 10.2822 25.72 4 18 4ZM21.85 21.8564C21.62 22.0865 21.32 22.2065 21 22.2065C20.68 22.2065 20.38 22.0865 20.15 21.8564L17.15 18.8553C16.92 18.6252 16.8 18.3251 16.8 18.005V10.0021C16.8 9.34191 17.34 8.80171 18 8.80171C18.66 8.80171 19.2 9.34191 19.2 10.0021V17.5048L21.85 20.1558C22.08 20.3859 22.2 20.686 22.2 21.0061C22.2 21.3262 22.08 21.6263 21.85 21.8564Z\"/>',solidAlerted:'<path d=\"M26.9039 1.64597L21.2222 11.1156C20.9526 11.4981 20.9281 11.9946 21.1588 12.4002C21.3896 12.8058 21.8363 13.0517 22.3148 13.0364H33.6881C34.1666 13.0517 34.6134 12.8058 34.8441 12.4002C35.0748 11.9946 35.0503 11.4981 34.7808 11.1156L29.0991 1.64597C28.8711 1.26889 28.4532 1.03711 28.0015 1.03711C27.5497 1.03711 27.1319 1.26889 26.9039 1.64597Z\"/><path d=\"M31.0914 13.0399C31.0914 13.0399 31.0914 13.0399 31.0914 13.0399V13.0399Z\"/><path d=\"M22.6774 4.80366C21.2141 4.28334 19.6395 4 18 4C10.28 4 4 10.2822 4 18.005C4 21.4262 5.24 24.5673 7.29 26.9982L4.26 30.3294C3.89 30.7395 3.92 31.3698 4.33 31.7399C4.52 31.91 4.76 32 5 32C5.27 32 5.54 31.89 5.74 31.6699L8.69 28.4187C11.17 30.6395 14.43 32 18 32C21.57 32 24.84 30.6395 27.31 28.4187L30.26 31.6699C30.46 31.89 30.73 32 31 32C31.24 32 31.48 31.91 31.67 31.7399C32.08 31.3698 32.11 30.7395 31.74 30.3294L28.71 26.9982C30.76 24.5673 32 21.4262 32 18.005C32 16.9882 31.8911 15.9964 31.6845 15.0406H22.3395C21.1577 15.0643 20.0233 14.4527 19.4206 13.3927C19.3333 13.2393 19.2599 13.0809 19.2 12.9192V17.5048L21.85 20.1558C22.08 20.3859 22.2 20.686 22.2 21.0061C22.2 21.3262 22.08 21.6263 21.85 21.8564C21.62 22.0865 21.32 22.2065 21 22.2065C20.68 22.2065 20.38 22.0865 20.15 21.8564L17.15 18.8553C16.92 18.6252 16.8 18.3251 16.8 18.005V10.0021C16.8 9.34191 17.34 8.80171 18 8.80171C18.66 8.80171 19.2 9.34191 19.2 10.0021V10.7026C19.285 10.4726 19.3972 10.2505 19.5362 10.0409L22.6774 4.80366Z\"/><path d=\"M8 4.03001C8.58 4.03001 9.13 4.17006 9.63 4.39014C7.49 5.71061 5.68 7.53126 4.36 9.67203C4.13 9.17185 4 8.62165 4 8.03144C4 5.82065 5.79 4.03001 8 4.03001Z\"/>',solidBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M31.0461 12.9224C30.7049 12.9735 30.3555 13 30 13C26.134 13 23 9.86599 23 6C23 5.64418 23.0265 5.29456 23.0778 4.95302C21.5028 4.3378 19.7901 4 18 4C10.28 4 4 10.2822 4 18.005C4 21.4262 5.24 24.5673 7.29 26.9982L4.26 30.3294C3.89 30.7395 3.92 31.3698 4.33 31.7399C4.52 31.91 4.76 32 5 32C5.27 32 5.54 31.89 5.74 31.6699L8.69 28.4187C11.17 30.6395 14.43 32 18 32C21.57 32 24.84 30.6395 27.31 28.4187L30.26 31.6699C30.46 31.89 30.73 32 31 32C31.24 32 31.48 31.91 31.67 31.7399C32.08 31.3698 32.11 30.7395 31.74 30.3294L28.71 26.9982C30.76 24.5673 32 21.4262 32 18.005C32 16.2131 31.6619 14.4987 31.0461 12.9224ZM21 22.2065C21.32 22.2065 21.62 22.0865 21.85 21.8564C22.08 21.6263 22.2 21.3262 22.2 21.0061C22.2 20.686 22.08 20.3859 21.85 20.1558L19.2 17.5048V10.0021C19.2 9.34191 18.66 8.80171 18 8.80171C17.34 8.80171 16.8 9.34191 16.8 10.0021V18.005C16.8 18.3251 16.92 18.6252 17.15 18.8553L20.15 21.8564C20.38 22.0865 20.68 22.2065 21 22.2065Z\"/><path d=\"M8 4.03001C8.58 4.03001 9.13 4.17006 9.63 4.39014C7.49 5.71061 5.68 7.53126 4.36 9.67203C4.13 9.17185 4 8.62165 4 8.03144C4 5.82065 5.79 4.03001 8 4.03001Z\"/>'})];export{d as alarmClockIcon,L as alarmClockIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,aAAa;EAACC,CAAC,GAAC,CAAC,aAAa,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,wsCAAwsC;IAACC,cAAc,EAAC,8qDAA8qD;IAACC,aAAa,EAAC,u6CAAu6C;IAACC,KAAK,EAAC,umCAAumC;IAACC,YAAY,EAAC,ylDAAylD;IAACC,WAAW,EAAC;EAA0wC,CAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,cAAc,EAACR,CAAC,IAAIS,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}