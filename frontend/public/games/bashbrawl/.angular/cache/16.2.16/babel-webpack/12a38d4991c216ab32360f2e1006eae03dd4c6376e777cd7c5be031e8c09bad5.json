{"ast": null, "code": "import { renderIcon as t } from \"../icon.renderer.js\";\nconst o = \"stop\",\n  r = [\"stop\", t({\n    outline: '<path d=\"M30,32H6a2,2,0,0,1-2-2V6A2,2,0,0,1,6,4H30a2,2,0,0,1,2,2V30A2,2,0,0,1,30,32ZM6,6V30H30V6Z\"/>',\n    solid: '<rect x=\"3.96\" y=\"4\" width=\"27.99\" height=\"28\" rx=\"2\" ry=\"2\"/>'\n  })];\nexport { r as stopIcon, o as stopIconName };", "map": {"version": 3, "names": ["renderIcon", "t", "o", "r", "outline", "solid", "stopIcon", "stopIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/stop.js"], "sourcesContent": ["import{renderIcon as t}from\"../icon.renderer.js\";const o=\"stop\",r=[\"stop\",t({outline:'<path d=\"M30,32H6a2,2,0,0,1-2-2V6A2,2,0,0,1,6,4H30a2,2,0,0,1,2,2V30A2,2,0,0,1,30,32ZM6,6V30H30V6Z\"/>',solid:'<rect x=\"3.96\" y=\"4\" width=\"27.99\" height=\"28\" rx=\"2\" ry=\"2\"/>'})];export{r as stopIcon,o as stopIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,MAAM;EAACC,CAAC,GAAC,CAAC,MAAM,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,sGAAsG;IAACC,KAAK,EAAC;EAAgE,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,QAAQ,EAACJ,CAAC,IAAIK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}