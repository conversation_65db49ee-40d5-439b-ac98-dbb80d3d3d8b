{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { onFirstInteraction as t, onChildListMutation as s } from \"../utils/events.js\";\nimport { getFlattenedDOMTree as e, getFlattenedFocusableItems as i } from \"../utils/traversal.js\";\nimport { contextMenuClick as o } from \"../utils/dom.js\";\nimport { validKeyNavigationCode as h } from \"../utils/keycodes.js\";\nimport { initializeKeyListItems as l, simpleFocusable as r, getActiveElement as n, setActiveKeyListItem as c, focusElement as d } from \"../utils/focus.js\";\nimport { getNextKeyGridItem as a } from \"./key-navigation.utils.js\";\nfunction u() {\n  return t => t.addInitializer(t => new f(t));\n}\nclass f {\n  constructor(t) {\n    this.host = t, this.observers = [], this.host.addController(this);\n  }\n  get hostGrid() {\n    return this.host.keyNavGrid ? this.host.keyNavGrid : this.host;\n  }\n  get hostRows() {\n    const t = Array.from(this.host.rows);\n    return this.host.columnRow && t.unshift(this.host.columnRow), t;\n  }\n  get hostCells() {\n    const t = Array.from(this.host.cells);\n    return this.host.columns && t.unshift(...Array.from(this.host.columns)), t;\n  }\n  get activeCell() {\n    return Array.from(this.hostCells).find(t => 0 === t.tabIndex);\n  }\n  hostConnected() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.host.updateComplete, t(_this.host).then(() => {\n        l(_this.hostCells), _this.hostGrid.addEventListener(\"mouseup\", t => _this.clickCell(t)), _this.hostGrid.addEventListener(\"keydown\", t => _this.keynavCell(t)), _this.hostGrid.addEventListener(\"keyup\", t => _this.updateCellActivation(t));\n      }), _this.observers.push(s(_this.host, () => l(_this.hostCells)));\n    })();\n  }\n  hostDisconnected() {\n    this.observers.forEach(t => t?.disconnect());\n  }\n  clickCell(t) {\n    if (!o(t)) {\n      const s = t.composedPath().find(t => this.hostCells.find(s => s === t));\n      s && this.setActiveCell(t, s);\n    }\n  }\n  keynavCell(t) {\n    if (h(t) && r(n())) {\n      const {\n          x: s,\n          y: i\n        } = a(this.hostCells, this.hostRows, {\n          code: t.code,\n          ctrlKey: t.ctrlKey,\n          dir: this.host.dir\n        }),\n        o = Array.from(e(this.hostRows[i])).filter(t => !!this.hostCells.find(s => s === t))[s];\n      this.setActiveCell(t, o), t.preventDefault();\n    }\n  }\n  setActiveCell(t, s) {\n    c(this.hostCells, s);\n    const e = i(s),\n      o = e.filter(t => r(t));\n    1 === o.length && 1 === e.length ? d(o[0]) : d(s), s.dispatchEvent(new CustomEvent(\"cdsKeyChange\", {\n      bubbles: !0,\n      detail: {\n        code: t.code,\n        shiftKey: t.shiftKey,\n        activeItem: s\n      }\n    }));\n  }\n  updateCellActivation(t) {\n    \"Escape\" === t.code && this.activeCell?.focus(), \"Enter\" === t.code && this.activeCell === t.composedPath()[0] && i(this.activeCell)[0]?.focus();\n  }\n}\nexport { f as KeyNavigationGridController, u as keyNavigationGrid };", "map": {"version": 3, "names": ["onFirstInteraction", "t", "onChildListMutation", "s", "getFlattenedDOMTree", "e", "getFlattenedFocusableItems", "i", "contextMenuClick", "o", "validKeyNavigationCode", "h", "initializeKeyListItems", "l", "simpleFocusable", "r", "getActiveElement", "n", "setActiveKeyListItem", "c", "focusElement", "d", "getNextKeyGridItem", "a", "u", "addInitializer", "f", "constructor", "host", "observers", "addController", "hostGrid", "keyNavGrid", "hostRows", "Array", "from", "rows", "columnRow", "unshift", "host<PERSON>ells", "cells", "columns", "activeCell", "find", "tabIndex", "hostConnected", "_this", "_asyncToGenerator", "updateComplete", "then", "addEventListener", "clickCell", "keynavCell", "updateCellActivation", "push", "hostDisconnected", "for<PERSON>ach", "disconnect", "<PERSON><PERSON><PERSON>", "setActiveCell", "x", "y", "code", "ctrl<PERSON>ey", "dir", "filter", "preventDefault", "length", "dispatchEvent", "CustomEvent", "bubbles", "detail", "shift<PERSON>ey", "activeItem", "focus", "KeyNavigationGridController", "keyNavigationGrid"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/key-navigation-grid.controller.js"], "sourcesContent": ["import{onFirstInteraction as t,onChildListMutation as s}from\"../utils/events.js\";import{getFlattenedDOMTree as e,getFlattenedFocusableItems as i}from\"../utils/traversal.js\";import{contextMenuClick as o}from\"../utils/dom.js\";import{validKeyNavigationCode as h}from\"../utils/keycodes.js\";import{initializeKeyListItems as l,simpleFocusable as r,getActiveElement as n,setActiveKeyListItem as c,focusElement as d}from\"../utils/focus.js\";import{getNextKeyGridItem as a}from\"./key-navigation.utils.js\";function u(){return t=>t.addInitializer((t=>new f(t)))}class f{constructor(t){this.host=t,this.observers=[],this.host.addController(this)}get hostGrid(){return this.host.keyNavGrid?this.host.keyNavGrid:this.host}get hostRows(){const t=Array.from(this.host.rows);return this.host.columnRow&&t.unshift(this.host.columnRow),t}get hostCells(){const t=Array.from(this.host.cells);return this.host.columns&&t.unshift(...Array.from(this.host.columns)),t}get activeCell(){return Array.from(this.hostCells).find((t=>0===t.tabIndex))}async hostConnected(){await this.host.updateComplete,t(this.host).then((()=>{l(this.hostCells),this.hostGrid.addEventListener(\"mouseup\",(t=>this.clickCell(t))),this.hostGrid.addEventListener(\"keydown\",(t=>this.keynavCell(t))),this.hostGrid.addEventListener(\"keyup\",(t=>this.updateCellActivation(t)))})),this.observers.push(s(this.host,(()=>l(this.hostCells))))}hostDisconnected(){this.observers.forEach((t=>t?.disconnect()))}clickCell(t){if(!o(t)){const s=t.composedPath().find((t=>this.hostCells.find((s=>s===t))));s&&this.setActiveCell(t,s)}}keynavCell(t){if(h(t)&&r(n())){const{x:s,y:i}=a(this.hostCells,this.hostRows,{code:t.code,ctrlKey:t.ctrlKey,dir:this.host.dir}),o=Array.from(e(this.hostRows[i])).filter((t=>!!this.hostCells.find((s=>s===t))))[s];this.setActiveCell(t,o),t.preventDefault()}}setActiveCell(t,s){c(this.hostCells,s);const e=i(s),o=e.filter((t=>r(t)));1===o.length&&1===e.length?d(o[0]):d(s),s.dispatchEvent(new CustomEvent(\"cdsKeyChange\",{bubbles:!0,detail:{code:t.code,shiftKey:t.shiftKey,activeItem:s}}))}updateCellActivation(t){\"Escape\"===t.code&&this.activeCell?.focus(),\"Enter\"===t.code&&this.activeCell===t.composedPath()[0]&&i(this.activeCell)[0]?.focus()}}export{f as KeyNavigationGridController,u as keyNavigationGrid};\n"], "mappings": ";AAAA,SAAOA,kBAAkB,IAAIC,CAAC,EAACC,mBAAmB,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,EAACC,0BAA0B,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,iBAAiB;AAAC,SAAOC,sBAAsB,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,sBAAsB,IAAIC,CAAC,EAACC,eAAe,IAAIC,CAAC,EAACC,gBAAgB,IAAIC,CAAC,EAACC,oBAAoB,IAAIC,CAAC,EAACC,YAAY,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,kBAAkB,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,OAAOvB,CAAC,IAAEA,CAAC,CAACwB,cAAc,CAAExB,CAAC,IAAE,IAAIyB,CAAC,CAACzB,CAAC,CAAE,CAAC;AAAA;AAAC,MAAMyB,CAAC;EAACC,WAAWA,CAAC1B,CAAC,EAAC;IAAC,IAAI,CAAC2B,IAAI,GAAC3B,CAAC,EAAC,IAAI,CAAC4B,SAAS,GAAC,EAAE,EAAC,IAAI,CAACD,IAAI,CAACE,aAAa,CAAC,IAAI,CAAC;EAAA;EAAC,IAAIC,QAAQA,CAAA,EAAE;IAAC,OAAO,IAAI,CAACH,IAAI,CAACI,UAAU,GAAC,IAAI,CAACJ,IAAI,CAACI,UAAU,GAAC,IAAI,CAACJ,IAAI;EAAA;EAAC,IAAIK,QAAQA,CAAA,EAAE;IAAC,MAAMhC,CAAC,GAACiC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAC;IAAC,OAAO,IAAI,CAACR,IAAI,CAACS,SAAS,IAAEpC,CAAC,CAACqC,OAAO,CAAC,IAAI,CAACV,IAAI,CAACS,SAAS,CAAC,EAACpC,CAAC;EAAA;EAAC,IAAIsC,SAASA,CAAA,EAAE;IAAC,MAAMtC,CAAC,GAACiC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACP,IAAI,CAACY,KAAK,CAAC;IAAC,OAAO,IAAI,CAACZ,IAAI,CAACa,OAAO,IAAExC,CAAC,CAACqC,OAAO,CAAC,GAAGJ,KAAK,CAACC,IAAI,CAAC,IAAI,CAACP,IAAI,CAACa,OAAO,CAAC,CAAC,EAACxC,CAAC;EAAA;EAAC,IAAIyC,UAAUA,CAAA,EAAE;IAAC,OAAOR,KAAK,CAACC,IAAI,CAAC,IAAI,CAACI,SAAS,CAAC,CAACI,IAAI,CAAE1C,CAAC,IAAE,CAAC,KAAGA,CAAC,CAAC2C,QAAS,CAAC;EAAA;EAAOC,aAAaA,CAAA,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAAC,MAAMD,KAAI,CAAClB,IAAI,CAACoB,cAAc,EAAC/C,CAAC,CAAC6C,KAAI,CAAClB,IAAI,CAAC,CAACqB,IAAI,CAAE,MAAI;QAACpC,CAAC,CAACiC,KAAI,CAACP,SAAS,CAAC,EAACO,KAAI,CAACf,QAAQ,CAACmB,gBAAgB,CAAC,SAAS,EAAEjD,CAAC,IAAE6C,KAAI,CAACK,SAAS,CAAClD,CAAC,CAAE,CAAC,EAAC6C,KAAI,CAACf,QAAQ,CAACmB,gBAAgB,CAAC,SAAS,EAAEjD,CAAC,IAAE6C,KAAI,CAACM,UAAU,CAACnD,CAAC,CAAE,CAAC,EAAC6C,KAAI,CAACf,QAAQ,CAACmB,gBAAgB,CAAC,OAAO,EAAEjD,CAAC,IAAE6C,KAAI,CAACO,oBAAoB,CAACpD,CAAC,CAAE,CAAC;MAAA,CAAE,CAAC,EAAC6C,KAAI,CAACjB,SAAS,CAACyB,IAAI,CAACnD,CAAC,CAAC2C,KAAI,CAAClB,IAAI,EAAE,MAAIf,CAAC,CAACiC,KAAI,CAACP,SAAS,CAAE,CAAC,CAAC;IAAA;EAAA;EAACgB,gBAAgBA,CAAA,EAAE;IAAC,IAAI,CAAC1B,SAAS,CAAC2B,OAAO,CAAEvD,CAAC,IAAEA,CAAC,EAAEwD,UAAU,CAAC,CAAE,CAAC;EAAA;EAACN,SAASA,CAAClD,CAAC,EAAC;IAAC,IAAG,CAACQ,CAAC,CAACR,CAAC,CAAC,EAAC;MAAC,MAAME,CAAC,GAACF,CAAC,CAACyD,YAAY,CAAC,CAAC,CAACf,IAAI,CAAE1C,CAAC,IAAE,IAAI,CAACsC,SAAS,CAACI,IAAI,CAAExC,CAAC,IAAEA,CAAC,KAAGF,CAAE,CAAE,CAAC;MAACE,CAAC,IAAE,IAAI,CAACwD,aAAa,CAAC1D,CAAC,EAACE,CAAC,CAAC;IAAA;EAAC;EAACiD,UAAUA,CAACnD,CAAC,EAAC;IAAC,IAAGU,CAAC,CAACV,CAAC,CAAC,IAAEc,CAAC,CAACE,CAAC,CAAC,CAAC,CAAC,EAAC;MAAC,MAAK;UAAC2C,CAAC,EAACzD,CAAC;UAAC0D,CAAC,EAACtD;QAAC,CAAC,GAACgB,CAAC,CAAC,IAAI,CAACgB,SAAS,EAAC,IAAI,CAACN,QAAQ,EAAC;UAAC6B,IAAI,EAAC7D,CAAC,CAAC6D,IAAI;UAACC,OAAO,EAAC9D,CAAC,CAAC8D,OAAO;UAACC,GAAG,EAAC,IAAI,CAACpC,IAAI,CAACoC;QAAG,CAAC,CAAC;QAACvD,CAAC,GAACyB,KAAK,CAACC,IAAI,CAAC9B,CAAC,CAAC,IAAI,CAAC4B,QAAQ,CAAC1B,CAAC,CAAC,CAAC,CAAC,CAAC0D,MAAM,CAAEhE,CAAC,IAAE,CAAC,CAAC,IAAI,CAACsC,SAAS,CAACI,IAAI,CAAExC,CAAC,IAAEA,CAAC,KAAGF,CAAE,CAAE,CAAC,CAACE,CAAC,CAAC;MAAC,IAAI,CAACwD,aAAa,CAAC1D,CAAC,EAACQ,CAAC,CAAC,EAACR,CAAC,CAACiE,cAAc,CAAC,CAAC;IAAA;EAAC;EAACP,aAAaA,CAAC1D,CAAC,EAACE,CAAC,EAAC;IAACgB,CAAC,CAAC,IAAI,CAACoB,SAAS,EAACpC,CAAC,CAAC;IAAC,MAAME,CAAC,GAACE,CAAC,CAACJ,CAAC,CAAC;MAACM,CAAC,GAACJ,CAAC,CAAC4D,MAAM,CAAEhE,CAAC,IAAEc,CAAC,CAACd,CAAC,CAAE,CAAC;IAAC,CAAC,KAAGQ,CAAC,CAAC0D,MAAM,IAAE,CAAC,KAAG9D,CAAC,CAAC8D,MAAM,GAAC9C,CAAC,CAACZ,CAAC,CAAC,CAAC,CAAC,CAAC,GAACY,CAAC,CAAClB,CAAC,CAAC,EAACA,CAAC,CAACiE,aAAa,CAAC,IAAIC,WAAW,CAAC,cAAc,EAAC;MAACC,OAAO,EAAC,CAAC,CAAC;MAACC,MAAM,EAAC;QAACT,IAAI,EAAC7D,CAAC,CAAC6D,IAAI;QAACU,QAAQ,EAACvE,CAAC,CAACuE,QAAQ;QAACC,UAAU,EAACtE;MAAC;IAAC,CAAC,CAAC,CAAC;EAAA;EAACkD,oBAAoBA,CAACpD,CAAC,EAAC;IAAC,QAAQ,KAAGA,CAAC,CAAC6D,IAAI,IAAE,IAAI,CAACpB,UAAU,EAAEgC,KAAK,CAAC,CAAC,EAAC,OAAO,KAAGzE,CAAC,CAAC6D,IAAI,IAAE,IAAI,CAACpB,UAAU,KAAGzC,CAAC,CAACyD,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,IAAEnD,CAAC,CAAC,IAAI,CAACmC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAEgC,KAAK,CAAC,CAAC;EAAA;AAAC;AAAC,SAAOhD,CAAC,IAAIiD,2BAA2B,EAACnD,CAAC,IAAIoD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}