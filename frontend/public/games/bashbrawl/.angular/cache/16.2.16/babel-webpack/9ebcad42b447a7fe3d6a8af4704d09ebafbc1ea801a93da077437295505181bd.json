{"ast": null, "code": "export default function _cloneRegExp(pattern) {\n  return new RegExp(pattern.source, pattern.flags ? pattern.flags : (pattern.global ? 'g' : '') + (pattern.ignoreCase ? 'i' : '') + (pattern.multiline ? 'm' : '') + (pattern.sticky ? 'y' : '') + (pattern.unicode ? 'u' : '') + (pattern.dotAll ? 's' : ''));\n}", "map": {"version": 3, "names": ["_cloneRegExp", "pattern", "RegExp", "source", "flags", "global", "ignoreCase", "multiline", "sticky", "unicode", "dotAll"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_cloneRegExp.js"], "sourcesContent": ["export default function _cloneRegExp(pattern) {\n  return new RegExp(pattern.source, pattern.flags ? pattern.flags : (pattern.global ? 'g' : '') + (pattern.ignoreCase ? 'i' : '') + (pattern.multiline ? 'm' : '') + (pattern.sticky ? 'y' : '') + (pattern.unicode ? 'u' : '') + (pattern.dotAll ? 's' : ''));\n}"], "mappings": "AAAA,eAAe,SAASA,YAAYA,CAACC,OAAO,EAAE;EAC5C,OAAO,IAAIC,MAAM,CAACD,OAAO,CAACE,MAAM,EAAEF,OAAO,CAACG,KAAK,GAAGH,OAAO,CAACG,KAAK,GAAG,CAACH,OAAO,CAACI,MAAM,GAAG,GAAG,GAAG,EAAE,KAAKJ,OAAO,CAACK,UAAU,GAAG,GAAG,GAAG,EAAE,CAAC,IAAIL,OAAO,CAACM,SAAS,GAAG,GAAG,GAAG,EAAE,CAAC,IAAIN,OAAO,CAACO,MAAM,GAAG,GAAG,GAAG,EAAE,CAAC,IAAIP,OAAO,CAACQ,OAAO,GAAG,GAAG,GAAG,EAAE,CAAC,IAAIR,OAAO,CAACS,MAAM,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AAC9P", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}