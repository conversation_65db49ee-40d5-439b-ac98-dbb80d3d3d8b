{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nvar _a, _b, _c;\n/**\n * The main LitElement module, which defines the {@linkcode LitElement} base\n * class and related APIs.\n *\n *  LitElement components can define a template and a set of observed\n * properties. Changing an observed property triggers a re-render of the\n * element.\n *\n *  Import {@linkcode LitElement} and {@linkcode html} from this module to\n * create a component:\n *\n *  ```js\n * import {LitElement, html} from 'lit-element';\n *\n * class MyElement extends LitElement {\n *\n *   // Declare observed properties\n *   static get properties() {\n *     return {\n *       adjective: {}\n *     }\n *   }\n *\n *   constructor() {\n *     this.adjective = 'awesome';\n *   }\n *\n *   // Define the element's template\n *   render() {\n *     return html`<p>your ${adjective} template here</p>`;\n *   }\n * }\n *\n * customElements.define('my-element', MyElement);\n * ```\n *\n * `LitElement` extends {@linkcode ReactiveElement} and adds lit-html\n * templating. The `ReactiveElement` class is provided for users that want to\n * build their own custom element base classes that don't use lit-html.\n *\n * @packageDocumentation\n */\nimport { ReactiveElement } from '@lit/reactive-element';\nimport { render, noChange } from 'lit-html';\nexport * from '@lit/reactive-element';\nexport * from 'lit-html';\n// For backwards compatibility export ReactiveElement as UpdatingElement. Note,\n// IE transpilation requires exporting like this.\nexport const UpdatingElement = ReactiveElement;\nconst DEV_MODE = true;\nlet issueWarning;\nif (DEV_MODE) {\n  // Ensure warnings are issued only 1x, even if multiple versions of Lit\n  // are loaded.\n  const issuedWarnings = (_a = globalThis.litIssuedWarnings) !== null && _a !== void 0 ? _a : globalThis.litIssuedWarnings = new Set();\n  // Issue a warning, if we haven't already.\n  issueWarning = (code, warning) => {\n    warning += ` See https://lit.dev/msg/${code} for more information.`;\n    if (!issuedWarnings.has(warning)) {\n      console.warn(warning);\n      issuedWarnings.add(warning);\n    }\n  };\n}\n/**\n * Base element class that manages element properties and attributes, and\n * renders a lit-html template.\n *\n * To define a component, subclass `LitElement` and implement a\n * `render` method to provide the component's template. Define properties\n * using the {@linkcode LitElement.properties properties} property or the\n * {@linkcode property} decorator.\n */\nexport class LitElement extends ReactiveElement {\n  constructor() {\n    super(...arguments);\n    /**\n     * @category rendering\n     */\n    this.renderOptions = {\n      host: this\n    };\n    this.__childPart = undefined;\n  }\n  /**\n   * @category rendering\n   */\n  createRenderRoot() {\n    var _a;\n    var _b;\n    const renderRoot = super.createRenderRoot();\n    // When adoptedStyleSheets are shimmed, they are inserted into the\n    // shadowRoot by createRenderRoot. Adjust the renderBefore node so that\n    // any styles in Lit content render before adoptedStyleSheets. This is\n    // important so that adoptedStyleSheets have precedence over styles in\n    // the shadowRoot.\n    (_a = (_b = this.renderOptions).renderBefore) !== null && _a !== void 0 ? _a : _b.renderBefore = renderRoot.firstChild;\n    return renderRoot;\n  }\n  /**\n   * Updates the element. This method reflects property values to attributes\n   * and calls `render` to render DOM via lit-html. Setting properties inside\n   * this method will *not* trigger another update.\n   * @param changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  update(changedProperties) {\n    // Setting properties in `render` should not trigger an update. Since\n    // updates are allowed after super.update, it's important to call `render`\n    // before that.\n    const value = this.render();\n    if (!this.hasUpdated) {\n      this.renderOptions.isConnected = this.isConnected;\n    }\n    super.update(changedProperties);\n    this.__childPart = render(value, this.renderRoot, this.renderOptions);\n  }\n  /**\n   * Invoked when the component is added to the document's DOM.\n   *\n   * In `connectedCallback()` you should setup tasks that should only occur when\n   * the element is connected to the document. The most common of these is\n   * adding event listeners to nodes external to the element, like a keydown\n   * event handler added to the window.\n   *\n   * ```ts\n   * connectedCallback() {\n   *   super.connectedCallback();\n   *   addEventListener('keydown', this._handleKeydown);\n   * }\n   * ```\n   *\n   * Typically, anything done in `connectedCallback()` should be undone when the\n   * element is disconnected, in `disconnectedCallback()`.\n   *\n   * @category lifecycle\n   */\n  connectedCallback() {\n    var _a;\n    super.connectedCallback();\n    (_a = this.__childPart) === null || _a === void 0 ? void 0 : _a.setConnected(true);\n  }\n  /**\n   * Invoked when the component is removed from the document's DOM.\n   *\n   * This callback is the main signal to the element that it may no longer be\n   * used. `disconnectedCallback()` should ensure that nothing is holding a\n   * reference to the element (such as event listeners added to nodes external\n   * to the element), so that it is free to be garbage collected.\n   *\n   * ```ts\n   * disconnectedCallback() {\n   *   super.disconnectedCallback();\n   *   window.removeEventListener('keydown', this._handleKeydown);\n   * }\n   * ```\n   *\n   * An element may be re-connected after being disconnected.\n   *\n   * @category lifecycle\n   */\n  disconnectedCallback() {\n    var _a;\n    super.disconnectedCallback();\n    (_a = this.__childPart) === null || _a === void 0 ? void 0 : _a.setConnected(false);\n  }\n  /**\n   * Invoked on each update to perform rendering tasks. This method may return\n   * any value renderable by lit-html's `ChildPart` - typically a\n   * `TemplateResult`. Setting properties inside this method will *not* trigger\n   * the element to update.\n   * @category rendering\n   */\n  render() {\n    return noChange;\n  }\n}\n/**\n * Ensure this class is marked as `finalized` as an optimization ensuring\n * it will not needlessly try to `finalize`.\n *\n * Note this property name is a string to prevent breaking Closure JS Compiler\n * optimizations. See @lit/reactive-element for more information.\n */\nLitElement['finalized'] = true;\n// This property needs to remain unminified.\nLitElement['_$litElement$'] = true;\n// Install hydration if available\n(_b = globalThis.litElementHydrateSupport) === null || _b === void 0 ? void 0 : _b.call(globalThis, {\n  LitElement\n});\n// Apply polyfills if available\nconst polyfillSupport = DEV_MODE ? globalThis.litElementPolyfillSupportDevMode : globalThis.litElementPolyfillSupport;\npolyfillSupport === null || polyfillSupport === void 0 ? void 0 : polyfillSupport({\n  LitElement\n});\n// DEV mode warnings\nif (DEV_MODE) {\n  /* eslint-disable @typescript-eslint/no-explicit-any */\n  // Note, for compatibility with closure compilation, this access\n  // needs to be as a string property index.\n  LitElement['finalize'] = function () {\n    const finalized = ReactiveElement.finalize.call(this);\n    if (!finalized) {\n      return false;\n    }\n    const warnRemovedOrRenamed = (obj, name, renamed = false) => {\n      if (obj.hasOwnProperty(name)) {\n        const ctorName = (typeof obj === 'function' ? obj : obj.constructor).name;\n        issueWarning(renamed ? 'renamed-api' : 'removed-api', `\\`${name}\\` is implemented on class ${ctorName}. It ` + `has been ${renamed ? 'renamed' : 'removed'} ` + `in this version of LitElement.`);\n      }\n    };\n    warnRemovedOrRenamed(this, 'render');\n    warnRemovedOrRenamed(this, 'getStyles', true);\n    warnRemovedOrRenamed(this.prototype, 'adoptStyles');\n    return true;\n  };\n  /* eslint-enable @typescript-eslint/no-explicit-any */\n}\n/**\n * END USERS SHOULD NOT RELY ON THIS OBJECT.\n *\n * Private exports for use by other Lit packages, not intended for use by\n * external users.\n *\n * We currently do not make a mangled rollup build of the lit-ssr code. In order\n * to keep a number of (otherwise private) top-level exports  mangled in the\n * client side code, we export a _$LE object containing those members (or\n * helper methods for accessing private fields of those members), and then\n * re-export them for use in lit-ssr. This keeps lit-ssr agnostic to whether the\n * client-side code is being used in `dev` mode or `prod` mode.\n *\n * This has a unique name, to disambiguate it from private exports in\n * lit-html, since this module re-exports all of lit-html.\n *\n * @private\n */\nexport const _$LE = {\n  _$attributeToProperty: (el, name, value) => {\n    // eslint-disable-next-line\n    el._$attributeToProperty(name, value);\n  },\n  // eslint-disable-next-line\n  _$changedProperties: el => el._$changedProperties\n};\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for LitElement usage.\n((_c = globalThis.litElementVersions) !== null && _c !== void 0 ? _c : globalThis.litElementVersions = []).push('3.3.3');\nif (DEV_MODE && globalThis.litElementVersions.length > 1) {\n  issueWarning('multiple-versions', `Multiple versions of Lit loaded. Loading multiple versions ` + `is not recommended.`);\n}", "map": {"version": 3, "names": ["_a", "_b", "_c", "ReactiveElement", "render", "noChange", "UpdatingElement", "DEV_MODE", "issueWarning", "issuedWarnings", "globalThis", "litIssuedWarnings", "Set", "code", "warning", "has", "console", "warn", "add", "LitElement", "constructor", "arguments", "renderOptions", "host", "__child<PERSON><PERSON>", "undefined", "createRenderRoot", "renderRoot", "renderBefore", "<PERSON><PERSON><PERSON><PERSON>", "update", "changedProperties", "value", "hasUpdated", "isConnected", "connectedCallback", "setConnected", "disconnectedCallback", "litElementHydrateSupport", "call", "polyfillSupport", "litElementPolyfillSupportDevMode", "litElementPolyfillSupport", "finalized", "finalize", "warnRemovedOrRenamed", "obj", "name", "renamed", "hasOwnProperty", "ctorName", "prototype", "_$LE", "_$attributeToProperty", "el", "_$changedProperties", "litElementVersions", "push", "length"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/lit-element/development/lit-element.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nvar _a, _b, _c;\n/**\n * The main LitElement module, which defines the {@linkcode LitElement} base\n * class and related APIs.\n *\n *  LitElement components can define a template and a set of observed\n * properties. Changing an observed property triggers a re-render of the\n * element.\n *\n *  Import {@linkcode LitElement} and {@linkcode html} from this module to\n * create a component:\n *\n *  ```js\n * import {LitElement, html} from 'lit-element';\n *\n * class MyElement extends LitElement {\n *\n *   // Declare observed properties\n *   static get properties() {\n *     return {\n *       adjective: {}\n *     }\n *   }\n *\n *   constructor() {\n *     this.adjective = 'awesome';\n *   }\n *\n *   // Define the element's template\n *   render() {\n *     return html`<p>your ${adjective} template here</p>`;\n *   }\n * }\n *\n * customElements.define('my-element', MyElement);\n * ```\n *\n * `LitElement` extends {@linkcode ReactiveElement} and adds lit-html\n * templating. The `ReactiveElement` class is provided for users that want to\n * build their own custom element base classes that don't use lit-html.\n *\n * @packageDocumentation\n */\nimport { ReactiveElement } from '@lit/reactive-element';\nimport { render, noChange } from 'lit-html';\nexport * from '@lit/reactive-element';\nexport * from 'lit-html';\n// For backwards compatibility export ReactiveElement as UpdatingElement. Note,\n// IE transpilation requires exporting like this.\nexport const UpdatingElement = ReactiveElement;\nconst DEV_MODE = true;\nlet issueWarning;\nif (DEV_MODE) {\n    // Ensure warnings are issued only 1x, even if multiple versions of Lit\n    // are loaded.\n    const issuedWarnings = ((_a = globalThis.litIssuedWarnings) !== null && _a !== void 0 ? _a : (globalThis.litIssuedWarnings = new Set()));\n    // Issue a warning, if we haven't already.\n    issueWarning = (code, warning) => {\n        warning += ` See https://lit.dev/msg/${code} for more information.`;\n        if (!issuedWarnings.has(warning)) {\n            console.warn(warning);\n            issuedWarnings.add(warning);\n        }\n    };\n}\n/**\n * Base element class that manages element properties and attributes, and\n * renders a lit-html template.\n *\n * To define a component, subclass `LitElement` and implement a\n * `render` method to provide the component's template. Define properties\n * using the {@linkcode LitElement.properties properties} property or the\n * {@linkcode property} decorator.\n */\nexport class LitElement extends ReactiveElement {\n    constructor() {\n        super(...arguments);\n        /**\n         * @category rendering\n         */\n        this.renderOptions = { host: this };\n        this.__childPart = undefined;\n    }\n    /**\n     * @category rendering\n     */\n    createRenderRoot() {\n        var _a;\n        var _b;\n        const renderRoot = super.createRenderRoot();\n        // When adoptedStyleSheets are shimmed, they are inserted into the\n        // shadowRoot by createRenderRoot. Adjust the renderBefore node so that\n        // any styles in Lit content render before adoptedStyleSheets. This is\n        // important so that adoptedStyleSheets have precedence over styles in\n        // the shadowRoot.\n        (_a = (_b = this.renderOptions).renderBefore) !== null && _a !== void 0 ? _a : (_b.renderBefore = renderRoot.firstChild);\n        return renderRoot;\n    }\n    /**\n     * Updates the element. This method reflects property values to attributes\n     * and calls `render` to render DOM via lit-html. Setting properties inside\n     * this method will *not* trigger another update.\n     * @param changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    update(changedProperties) {\n        // Setting properties in `render` should not trigger an update. Since\n        // updates are allowed after super.update, it's important to call `render`\n        // before that.\n        const value = this.render();\n        if (!this.hasUpdated) {\n            this.renderOptions.isConnected = this.isConnected;\n        }\n        super.update(changedProperties);\n        this.__childPart = render(value, this.renderRoot, this.renderOptions);\n    }\n    /**\n     * Invoked when the component is added to the document's DOM.\n     *\n     * In `connectedCallback()` you should setup tasks that should only occur when\n     * the element is connected to the document. The most common of these is\n     * adding event listeners to nodes external to the element, like a keydown\n     * event handler added to the window.\n     *\n     * ```ts\n     * connectedCallback() {\n     *   super.connectedCallback();\n     *   addEventListener('keydown', this._handleKeydown);\n     * }\n     * ```\n     *\n     * Typically, anything done in `connectedCallback()` should be undone when the\n     * element is disconnected, in `disconnectedCallback()`.\n     *\n     * @category lifecycle\n     */\n    connectedCallback() {\n        var _a;\n        super.connectedCallback();\n        (_a = this.__childPart) === null || _a === void 0 ? void 0 : _a.setConnected(true);\n    }\n    /**\n     * Invoked when the component is removed from the document's DOM.\n     *\n     * This callback is the main signal to the element that it may no longer be\n     * used. `disconnectedCallback()` should ensure that nothing is holding a\n     * reference to the element (such as event listeners added to nodes external\n     * to the element), so that it is free to be garbage collected.\n     *\n     * ```ts\n     * disconnectedCallback() {\n     *   super.disconnectedCallback();\n     *   window.removeEventListener('keydown', this._handleKeydown);\n     * }\n     * ```\n     *\n     * An element may be re-connected after being disconnected.\n     *\n     * @category lifecycle\n     */\n    disconnectedCallback() {\n        var _a;\n        super.disconnectedCallback();\n        (_a = this.__childPart) === null || _a === void 0 ? void 0 : _a.setConnected(false);\n    }\n    /**\n     * Invoked on each update to perform rendering tasks. This method may return\n     * any value renderable by lit-html's `ChildPart` - typically a\n     * `TemplateResult`. Setting properties inside this method will *not* trigger\n     * the element to update.\n     * @category rendering\n     */\n    render() {\n        return noChange;\n    }\n}\n/**\n * Ensure this class is marked as `finalized` as an optimization ensuring\n * it will not needlessly try to `finalize`.\n *\n * Note this property name is a string to prevent breaking Closure JS Compiler\n * optimizations. See @lit/reactive-element for more information.\n */\nLitElement['finalized'] = true;\n// This property needs to remain unminified.\nLitElement['_$litElement$'] = true;\n// Install hydration if available\n(_b = globalThis.litElementHydrateSupport) === null || _b === void 0 ? void 0 : _b.call(globalThis, { LitElement });\n// Apply polyfills if available\nconst polyfillSupport = DEV_MODE\n    ? globalThis.litElementPolyfillSupportDevMode\n    : globalThis.litElementPolyfillSupport;\npolyfillSupport === null || polyfillSupport === void 0 ? void 0 : polyfillSupport({ LitElement });\n// DEV mode warnings\nif (DEV_MODE) {\n    /* eslint-disable @typescript-eslint/no-explicit-any */\n    // Note, for compatibility with closure compilation, this access\n    // needs to be as a string property index.\n    LitElement['finalize'] = function () {\n        const finalized = ReactiveElement.finalize.call(this);\n        if (!finalized) {\n            return false;\n        }\n        const warnRemovedOrRenamed = (obj, name, renamed = false) => {\n            if (obj.hasOwnProperty(name)) {\n                const ctorName = (typeof obj === 'function' ? obj : obj.constructor)\n                    .name;\n                issueWarning(renamed ? 'renamed-api' : 'removed-api', `\\`${name}\\` is implemented on class ${ctorName}. It ` +\n                    `has been ${renamed ? 'renamed' : 'removed'} ` +\n                    `in this version of LitElement.`);\n            }\n        };\n        warnRemovedOrRenamed(this, 'render');\n        warnRemovedOrRenamed(this, 'getStyles', true);\n        warnRemovedOrRenamed(this.prototype, 'adoptStyles');\n        return true;\n    };\n    /* eslint-enable @typescript-eslint/no-explicit-any */\n}\n/**\n * END USERS SHOULD NOT RELY ON THIS OBJECT.\n *\n * Private exports for use by other Lit packages, not intended for use by\n * external users.\n *\n * We currently do not make a mangled rollup build of the lit-ssr code. In order\n * to keep a number of (otherwise private) top-level exports  mangled in the\n * client side code, we export a _$LE object containing those members (or\n * helper methods for accessing private fields of those members), and then\n * re-export them for use in lit-ssr. This keeps lit-ssr agnostic to whether the\n * client-side code is being used in `dev` mode or `prod` mode.\n *\n * This has a unique name, to disambiguate it from private exports in\n * lit-html, since this module re-exports all of lit-html.\n *\n * @private\n */\nexport const _$LE = {\n    _$attributeToProperty: (el, name, value) => {\n        // eslint-disable-next-line\n        el._$attributeToProperty(name, value);\n    },\n    // eslint-disable-next-line\n    _$changedProperties: (el) => el._$changedProperties,\n};\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for LitElement usage.\n((_c = globalThis.litElementVersions) !== null && _c !== void 0 ? _c : (globalThis.litElementVersions = [])).push('3.3.3');\nif (DEV_MODE && globalThis.litElementVersions.length > 1) {\n    issueWarning('multiple-versions', `Multiple versions of Lit loaded. Loading multiple versions ` +\n        `is not recommended.`);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,IAAIA,EAAE,EAAEC,EAAE,EAAEC,EAAE;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,MAAM,EAAEC,QAAQ,QAAQ,UAAU;AAC3C,cAAc,uBAAuB;AACrC,cAAc,UAAU;AACxB;AACA;AACA,OAAO,MAAMC,eAAe,GAAGH,eAAe;AAC9C,MAAMI,QAAQ,GAAG,IAAI;AACrB,IAAIC,YAAY;AAChB,IAAID,QAAQ,EAAE;EACV;EACA;EACA,MAAME,cAAc,GAAI,CAACT,EAAE,GAAGU,UAAU,CAACC,iBAAiB,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAIU,UAAU,CAACC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAG;EACxI;EACAJ,YAAY,GAAGA,CAACK,IAAI,EAAEC,OAAO,KAAK;IAC9BA,OAAO,IAAK,4BAA2BD,IAAK,wBAAuB;IACnE,IAAI,CAACJ,cAAc,CAACM,GAAG,CAACD,OAAO,CAAC,EAAE;MAC9BE,OAAO,CAACC,IAAI,CAACH,OAAO,CAAC;MACrBL,cAAc,CAACS,GAAG,CAACJ,OAAO,CAAC;IAC/B;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,UAAU,SAAShB,eAAe,CAAC;EAC5CiB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG;MAAEC,IAAI,EAAE;IAAK,CAAC;IACnC,IAAI,CAACC,WAAW,GAAGC,SAAS;EAChC;EACA;AACJ;AACA;EACIC,gBAAgBA,CAAA,EAAG;IACf,IAAI1B,EAAE;IACN,IAAIC,EAAE;IACN,MAAM0B,UAAU,GAAG,KAAK,CAACD,gBAAgB,CAAC,CAAC;IAC3C;IACA;IACA;IACA;IACA;IACA,CAAC1B,EAAE,GAAG,CAACC,EAAE,GAAG,IAAI,CAACqB,aAAa,EAAEM,YAAY,MAAM,IAAI,IAAI5B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAIC,EAAE,CAAC2B,YAAY,GAAGD,UAAU,CAACE,UAAW;IACxH,OAAOF,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIG,MAAMA,CAACC,iBAAiB,EAAE;IACtB;IACA;IACA;IACA,MAAMC,KAAK,GAAG,IAAI,CAAC5B,MAAM,CAAC,CAAC;IAC3B,IAAI,CAAC,IAAI,CAAC6B,UAAU,EAAE;MAClB,IAAI,CAACX,aAAa,CAACY,WAAW,GAAG,IAAI,CAACA,WAAW;IACrD;IACA,KAAK,CAACJ,MAAM,CAACC,iBAAiB,CAAC;IAC/B,IAAI,CAACP,WAAW,GAAGpB,MAAM,CAAC4B,KAAK,EAAE,IAAI,CAACL,UAAU,EAAE,IAAI,CAACL,aAAa,CAAC;EACzE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIa,iBAAiBA,CAAA,EAAG;IAChB,IAAInC,EAAE;IACN,KAAK,CAACmC,iBAAiB,CAAC,CAAC;IACzB,CAACnC,EAAE,GAAG,IAAI,CAACwB,WAAW,MAAM,IAAI,IAAIxB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoC,YAAY,CAAC,IAAI,CAAC;EACtF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,oBAAoBA,CAAA,EAAG;IACnB,IAAIrC,EAAE;IACN,KAAK,CAACqC,oBAAoB,CAAC,CAAC;IAC5B,CAACrC,EAAE,GAAG,IAAI,CAACwB,WAAW,MAAM,IAAI,IAAIxB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoC,YAAY,CAAC,KAAK,CAAC;EACvF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIhC,MAAMA,CAAA,EAAG;IACL,OAAOC,QAAQ;EACnB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAc,UAAU,CAAC,WAAW,CAAC,GAAG,IAAI;AAC9B;AACAA,UAAU,CAAC,eAAe,CAAC,GAAG,IAAI;AAClC;AACA,CAAClB,EAAE,GAAGS,UAAU,CAAC4B,wBAAwB,MAAM,IAAI,IAAIrC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsC,IAAI,CAAC7B,UAAU,EAAE;EAAES;AAAW,CAAC,CAAC;AACnH;AACA,MAAMqB,eAAe,GAAGjC,QAAQ,GAC1BG,UAAU,CAAC+B,gCAAgC,GAC3C/B,UAAU,CAACgC,yBAAyB;AAC1CF,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAC;EAAErB;AAAW,CAAC,CAAC;AACjG;AACA,IAAIZ,QAAQ,EAAE;EACV;EACA;EACA;EACAY,UAAU,CAAC,UAAU,CAAC,GAAG,YAAY;IACjC,MAAMwB,SAAS,GAAGxC,eAAe,CAACyC,QAAQ,CAACL,IAAI,CAAC,IAAI,CAAC;IACrD,IAAI,CAACI,SAAS,EAAE;MACZ,OAAO,KAAK;IAChB;IACA,MAAME,oBAAoB,GAAGA,CAACC,GAAG,EAAEC,IAAI,EAAEC,OAAO,GAAG,KAAK,KAAK;MACzD,IAAIF,GAAG,CAACG,cAAc,CAACF,IAAI,CAAC,EAAE;QAC1B,MAAMG,QAAQ,GAAG,CAAC,OAAOJ,GAAG,KAAK,UAAU,GAAGA,GAAG,GAAGA,GAAG,CAAC1B,WAAW,EAC9D2B,IAAI;QACTvC,YAAY,CAACwC,OAAO,GAAG,aAAa,GAAG,aAAa,EAAG,KAAID,IAAK,8BAA6BG,QAAS,OAAM,GACvG,YAAWF,OAAO,GAAG,SAAS,GAAG,SAAU,GAAE,GAC7C,gCAA+B,CAAC;MACzC;IACJ,CAAC;IACDH,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC;IACpCA,oBAAoB,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC;IAC7CA,oBAAoB,CAAC,IAAI,CAACM,SAAS,EAAE,aAAa,CAAC;IACnD,OAAO,IAAI;EACf,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAChBC,qBAAqB,EAAEA,CAACC,EAAE,EAAEP,IAAI,EAAEf,KAAK,KAAK;IACxC;IACAsB,EAAE,CAACD,qBAAqB,CAACN,IAAI,EAAEf,KAAK,CAAC;EACzC,CAAC;EACD;EACAuB,mBAAmB,EAAGD,EAAE,IAAKA,EAAE,CAACC;AACpC,CAAC;AACD;AACA;AACA,CAAC,CAACrD,EAAE,GAAGQ,UAAU,CAAC8C,kBAAkB,MAAM,IAAI,IAAItD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAIQ,UAAU,CAAC8C,kBAAkB,GAAG,EAAG,EAAEC,IAAI,CAAC,OAAO,CAAC;AAC1H,IAAIlD,QAAQ,IAAIG,UAAU,CAAC8C,kBAAkB,CAACE,MAAM,GAAG,CAAC,EAAE;EACtDlD,YAAY,CAAC,mBAAmB,EAAG,6DAA4D,GAC1F,qBAAoB,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}