{"ast": null, "code": "import { isNumericString as i, isTshirtSize as e, isNilOrEmpty as t, replace<PERSON>ord as n, isNil as r, unsetElementStyles as a, pxToRem as u, updateElementStyles as l } from \"@cds/core/internal\";\nvar d;\nfunction c(n) {\n  switch (!0) {\n    case t(n):\n      return d.NilSizeValue;\n    case e(n):\n      return d.ValidSizeString;\n    case i(n):\n      return d.ValidNumericString;\n    default:\n      return d.BadSizeValue;\n  }\n}\nfunction h(i) {\n  return t(i) ? \"\" : n(i, \"fit\");\n}\nfunction s(i, e) {\n  return !r(i) && i.indexOf(\"fit\") > -1 ? [[\"width\", \"auto\"], [\"height\", \"auto\"], [\"min-width\", e], [\"min-height\", e]] : [[\"width\", e], [\"height\", e], [\"min-width\", e], [\"min-height\", e]];\n}\nfunction S(i, e) {\n  let t = \"\";\n  switch (c(h(e))) {\n    case d.ValidNumericString:\n      return t = u(parseInt(e)), void l(i, ...s(e, t));\n    case d.ValidSizeString:\n    case d.NilSizeValue:\n      return void a(i, \"width\", \"height\", \"min-width\", \"min-height\");\n    case d.BadSizeValue:\n    default:\n      return;\n  }\n}\n!function (i) {\n  i.BadSizeValue = \"bad-value\", i.ValidSizeString = \"value-is-string\", i.ValidNumericString = \"value-is-numeric\", i.NilSizeValue = \"value-is-nil\";\n}(d || (d = {}));\nexport { d as SizeUpdateStrategies, s as getIconSizeStylesToUpdate, h as getSizeValue, c as getUpdateSizeStrategy, S as updateIconSizeStyle };", "map": {"version": 3, "names": ["isNumericString", "i", "isTshirtSize", "e", "isNilOrEmpty", "t", "replace<PERSON><PERSON>", "n", "isNil", "r", "unsetElementStyles", "a", "pxToRem", "u", "updateElementStyles", "l", "d", "c", "NilSizeValue", "ValidSizeString", "ValidNumericString", "BadSizeValue", "h", "s", "indexOf", "S", "parseInt", "SizeUpdateStrategies", "getIconSizeStylesToUpdate", "getSizeValue", "getUpdateSizeStrategy", "updateIconSizeStyle"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/utils/icon.classnames.js"], "sourcesContent": ["import{isNumericString as i,isTshirtSize as e,isNilOrEmpty as t,replace<PERSON>ord as n,isNil as r,unsetElementStyles as a,pxToRem as u,updateElementStyles as l}from\"@cds/core/internal\";var d;function c(n){switch(!0){case t(n):return d.NilSizeValue;case e(n):return d.ValidSizeString;case i(n):return d.ValidNumericString;default:return d.BadSizeValue}}function h(i){return t(i)?\"\":n(i,\"fit\")}function s(i,e){return!r(i)&&i.indexOf(\"fit\")>-1?[[\"width\",\"auto\"],[\"height\",\"auto\"],[\"min-width\",e],[\"min-height\",e]]:[[\"width\",e],[\"height\",e],[\"min-width\",e],[\"min-height\",e]]}function S(i,e){let t=\"\";switch(c(h(e))){case d.ValidNumericString:return t=u(parseInt(e)),void l(i,...s(e,t));case d.ValidSizeString:case d.NilSizeValue:return void a(i,\"width\",\"height\",\"min-width\",\"min-height\");case d.BadSizeValue:default:return}}!function(i){i.BadSizeValue=\"bad-value\",i.ValidSizeString=\"value-is-string\",i.ValidNumericString=\"value-is-numeric\",i.NilSizeValue=\"value-is-nil\"}(d||(d={}));export{d as SizeUpdateStrategies,s as getIconSizeStylesToUpdate,h as getSizeValue,c as getUpdateSizeStrategy,S as updateIconSizeStyle};\n"], "mappings": "AAAA,SAAOA,eAAe,IAAIC,CAAC,EAACC,YAAY,IAAIC,CAAC,EAACC,YAAY,IAAIC,CAAC,EAACC,WAAW,IAAIC,CAAC,EAACC,KAAK,IAAIC,CAAC,EAACC,kBAAkB,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,EAACC,mBAAmB,IAAIC,CAAC,QAAK,oBAAoB;AAAC,IAAIC,CAAC;AAAC,SAASC,CAACA,CAACV,CAAC,EAAC;EAAC,QAAO,CAAC,CAAC;IAAE,KAAKF,CAAC,CAACE,CAAC,CAAC;MAAC,OAAOS,CAAC,CAACE,YAAY;IAAC,KAAKf,CAAC,CAACI,CAAC,CAAC;MAAC,OAAOS,CAAC,CAACG,eAAe;IAAC,KAAKlB,CAAC,CAACM,CAAC,CAAC;MAAC,OAAOS,CAAC,CAACI,kBAAkB;IAAC;MAAQ,OAAOJ,CAAC,CAACK,YAAY;EAAA;AAAC;AAAC,SAASC,CAACA,CAACrB,CAAC,EAAC;EAAC,OAAOI,CAAC,CAACJ,CAAC,CAAC,GAAC,EAAE,GAACM,CAAC,CAACN,CAAC,EAAC,KAAK,CAAC;AAAA;AAAC,SAASsB,CAACA,CAACtB,CAAC,EAACE,CAAC,EAAC;EAAC,OAAM,CAACM,CAAC,CAACR,CAAC,CAAC,IAAEA,CAAC,CAACuB,OAAO,CAAC,KAAK,CAAC,GAAC,CAAC,CAAC,GAAC,CAAC,CAAC,OAAO,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,MAAM,CAAC,EAAC,CAAC,WAAW,EAACrB,CAAC,CAAC,EAAC,CAAC,YAAY,EAACA,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,OAAO,EAACA,CAAC,CAAC,EAAC,CAAC,QAAQ,EAACA,CAAC,CAAC,EAAC,CAAC,WAAW,EAACA,CAAC,CAAC,EAAC,CAAC,YAAY,EAACA,CAAC,CAAC,CAAC;AAAA;AAAC,SAASsB,CAACA,CAACxB,CAAC,EAACE,CAAC,EAAC;EAAC,IAAIE,CAAC,GAAC,EAAE;EAAC,QAAOY,CAAC,CAACK,CAAC,CAACnB,CAAC,CAAC,CAAC;IAAE,KAAKa,CAAC,CAACI,kBAAkB;MAAC,OAAOf,CAAC,GAACQ,CAAC,CAACa,QAAQ,CAACvB,CAAC,CAAC,CAAC,EAAC,KAAKY,CAAC,CAACd,CAAC,EAAC,GAAGsB,CAAC,CAACpB,CAAC,EAACE,CAAC,CAAC,CAAC;IAAC,KAAKW,CAAC,CAACG,eAAe;IAAC,KAAKH,CAAC,CAACE,YAAY;MAAC,OAAO,KAAKP,CAAC,CAACV,CAAC,EAAC,OAAO,EAAC,QAAQ,EAAC,WAAW,EAAC,YAAY,CAAC;IAAC,KAAKe,CAAC,CAACK,YAAY;IAAC;MAAQ;EAAM;AAAC;AAAC,CAAC,UAASpB,CAAC,EAAC;EAACA,CAAC,CAACoB,YAAY,GAAC,WAAW,EAACpB,CAAC,CAACkB,eAAe,GAAC,iBAAiB,EAAClB,CAAC,CAACmB,kBAAkB,GAAC,kBAAkB,EAACnB,CAAC,CAACiB,YAAY,GAAC,cAAc;AAAA,CAAC,CAACF,CAAC,KAAGA,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC;AAAC,SAAOA,CAAC,IAAIW,oBAAoB,EAACJ,CAAC,IAAIK,yBAAyB,EAACN,CAAC,IAAIO,YAAY,EAACZ,CAAC,IAAIa,qBAAqB,EAACL,CAAC,IAAIM,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}