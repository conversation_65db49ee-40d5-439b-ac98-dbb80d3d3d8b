{"ast": null, "code": "export function createInvalidObservableTypeError(input) {\n  return new TypeError(`You provided ${input !== null && typeof input === 'object' ? 'an invalid object' : `'${input}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`);\n}", "map": {"version": 3, "names": ["createInvalidObservableTypeError", "input", "TypeError"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/rxjs/dist/esm/internal/util/throwUnobservableError.js"], "sourcesContent": ["export function createInvalidObservableTypeError(input) {\n    return new TypeError(`You provided ${input !== null && typeof input === 'object' ? 'an invalid object' : `'${input}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`);\n}\n"], "mappings": "AAAA,OAAO,SAASA,gCAAgCA,CAACC,KAAK,EAAE;EACpD,OAAO,IAAIC,SAAS,CAAE,gBAAeD,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAG,mBAAmB,GAAI,IAAGA,KAAM,GAAG,0HAAyH,CAAC;AACpP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}