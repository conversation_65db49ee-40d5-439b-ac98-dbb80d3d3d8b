{"ast": null, "code": "import { ClarityIcons as e } from \"../icon.service.js\";\nfunction i(e) {\n  return e.badge && (\"inherit-triangle\" === e.badge || \"warning-triangle\" === e.badge);\n}\nfunction r(e) {\n  let r = \"\";\n  return e.badge && i(e) ? r = '<path d=\"M26.85 1.14L21.13 11a1.28 1.28 0 001.1 2h11.45a1.28 1.28 0 001.1-2l-5.72-9.86a1.28 1.28 0 00-2.21 0z\" class=\"alert\" />' : e.badge && (r = '<circle cx=\"30\" cy=\"6\" r=\"5\" class=\"badge\" />'), r;\n}\nfunction t(r) {\n  const t = e.registry[r.shape] ?? e.registry.unknown;\n  let n = r.solid && t.solid ? t.solid : t.outline;\n  return r.badge && !i(r) && (n = r.solid ? t.solidBadged ?? n : t.outlineBadged ?? n), i(r) && (n = r.solid ? t.solidAlerted ?? n : t.outlineAlerted ?? n), n;\n}\nexport { r as getIconBadgeSVG, t as getIconSVG, i as hasAlertBadge };", "map": {"version": 3, "names": ["ClarityIcons", "e", "i", "badge", "r", "t", "registry", "shape", "unknown", "n", "solid", "outline", "solidBadged", "outlineBadged", "solidAlerted", "outlineAlerted", "getIconBadgeSVG", "getIconSVG", "hasAlertBadge"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/utils/icon.svg-helpers.js"], "sourcesContent": ["import{ClarityIcons as e}from\"../icon.service.js\";function i(e){return e.badge&&(\"inherit-triangle\"===e.badge||\"warning-triangle\"===e.badge)}function r(e){let r=\"\";return e.badge&&i(e)?r='<path d=\"M26.85 1.14L21.13 11a1.28 1.28 0 001.1 2h11.45a1.28 1.28 0 001.1-2l-5.72-9.86a1.28 1.28 0 00-2.21 0z\" class=\"alert\" />':e.badge&&(r='<circle cx=\"30\" cy=\"6\" r=\"5\" class=\"badge\" />'),r}function t(r){const t=e.registry[r.shape]??e.registry.unknown;let n=r.solid&&t.solid?t.solid:t.outline;return r.badge&&!i(r)&&(n=r.solid?t.solidBadged??n:t.outlineBadged??n),i(r)&&(n=r.solid?t.solidAlerted??n:t.outlineAlerted??n),n}export{r as getIconBadgeSVG,t as getIconSVG,i as hasAlertBadge};\n"], "mappings": "AAAA,SAAOA,YAAY,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAASC,CAACA,CAACD,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACE,KAAK,KAAG,kBAAkB,KAAGF,CAAC,CAACE,KAAK,IAAE,kBAAkB,KAAGF,CAAC,CAACE,KAAK,CAAC;AAAA;AAAC,SAASC,CAACA,CAACH,CAAC,EAAC;EAAC,IAAIG,CAAC,GAAC,EAAE;EAAC,OAAOH,CAAC,CAACE,KAAK,IAAED,CAAC,CAACD,CAAC,CAAC,GAACG,CAAC,GAAC,iIAAiI,GAACH,CAAC,CAACE,KAAK,KAAGC,CAAC,GAAC,+CAA+C,CAAC,EAACA,CAAC;AAAA;AAAC,SAASC,CAACA,CAACD,CAAC,EAAC;EAAC,MAAMC,CAAC,GAACJ,CAAC,CAACK,QAAQ,CAACF,CAAC,CAACG,KAAK,CAAC,IAAEN,CAAC,CAACK,QAAQ,CAACE,OAAO;EAAC,IAAIC,CAAC,GAACL,CAAC,CAACM,KAAK,IAAEL,CAAC,CAACK,KAAK,GAACL,CAAC,CAACK,KAAK,GAACL,CAAC,CAACM,OAAO;EAAC,OAAOP,CAAC,CAACD,KAAK,IAAE,CAACD,CAAC,CAACE,CAAC,CAAC,KAAGK,CAAC,GAACL,CAAC,CAACM,KAAK,GAACL,CAAC,CAACO,WAAW,IAAEH,CAAC,GAACJ,CAAC,CAACQ,aAAa,IAAEJ,CAAC,CAAC,EAACP,CAAC,CAACE,CAAC,CAAC,KAAGK,CAAC,GAACL,CAAC,CAACM,KAAK,GAACL,CAAC,CAACS,YAAY,IAAEL,CAAC,GAACJ,CAAC,CAACU,cAAc,IAAEN,CAAC,CAAC,EAACA,CAAC;AAAA;AAAC,SAAOL,CAAC,IAAIY,eAAe,EAACX,CAAC,IAAIY,UAAU,EAACf,CAAC,IAAIgB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}