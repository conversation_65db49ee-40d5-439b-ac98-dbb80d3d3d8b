{"ast": null, "code": "export default function _arrayReduce(reducer, acc, list) {\n  var index = 0;\n  var length = list.length;\n  while (index < length) {\n    acc = reducer(acc, list[index]);\n    index += 1;\n  }\n  return acc;\n}", "map": {"version": 3, "names": ["_arrayReduce", "reducer", "acc", "list", "index", "length"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_arrayReduce.js"], "sourcesContent": ["export default function _arrayReduce(reducer, acc, list) {\n  var index = 0;\n  var length = list.length;\n\n  while (index < length) {\n    acc = reducer(acc, list[index]);\n    index += 1;\n  }\n\n  return acc;\n}"], "mappings": "AAAA,eAAe,SAASA,YAAYA,CAACC,OAAO,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACvD,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAM;EAExB,OAAOD,KAAK,GAAGC,MAAM,EAAE;IACrBH,GAAG,GAAGD,OAAO,CAACC,GAAG,EAAEC,IAAI,CAACC,KAAK,CAAC,CAAC;IAC/BA,KAAK,IAAI,CAAC;EACZ;EAEA,OAAOF,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}