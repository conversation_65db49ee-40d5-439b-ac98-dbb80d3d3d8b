{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nexport const PartType = {\n  ATTRIBUTE: 1,\n  CHILD: 2,\n  PROPERTY: 3,\n  BOOLEAN_ATTRIBUTE: 4,\n  EVENT: 5,\n  ELEMENT: 6\n};\n/**\n * Creates a user-facing directive function from a Directive class. This\n * function has the same parameters as the directive's render() method.\n */\nexport const directive = c => (...values) => ({\n  // This property needs to remain unminified.\n  ['_$litDirective$']: c,\n  values\n});\n/**\n * Base class for creating custom directives. Users should extend this class,\n * implement `render` and/or `update`, and then pass their subclass to\n * `directive`.\n */\nexport class Directive {\n  constructor(_partInfo) {}\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n  /** @internal */\n  _$initialize(part, parent, attributeIndex) {\n    this.__part = part;\n    this._$parent = parent;\n    this.__attributeIndex = attributeIndex;\n  }\n  /** @internal */\n  _$resolve(part, props) {\n    return this.update(part, props);\n  }\n  update(_part, props) {\n    return this.render(...props);\n  }\n}", "map": {"version": 3, "names": ["PartType", "ATTRIBUTE", "CHILD", "PROPERTY", "BOOLEAN_ATTRIBUTE", "EVENT", "ELEMENT", "directive", "c", "values", "Directive", "constructor", "_partInfo", "_$isConnected", "_$parent", "_$initialize", "part", "parent", "attributeIndex", "__part", "__attributeIndex", "_$resolve", "props", "update", "_part", "render"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/lit-html/development/directive.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nexport const PartType = {\n    ATTRIBUTE: 1,\n    CHILD: 2,\n    PROPERTY: 3,\n    BOOLEAN_ATTRIBUTE: 4,\n    EVENT: 5,\n    ELEMENT: 6,\n};\n/**\n * Creates a user-facing directive function from a Directive class. This\n * function has the same parameters as the directive's render() method.\n */\nexport const directive = (c) => (...values) => ({\n    // This property needs to remain unminified.\n    ['_$litDirective$']: c,\n    values,\n});\n/**\n * Base class for creating custom directives. Users should extend this class,\n * implement `render` and/or `update`, and then pass their subclass to\n * `directive`.\n */\nexport class Directive {\n    constructor(_partInfo) { }\n    // See comment in Disconnectable interface for why this is a getter\n    get _$isConnected() {\n        return this._$parent._$isConnected;\n    }\n    /** @internal */\n    _$initialize(part, parent, attributeIndex) {\n        this.__part = part;\n        this._$parent = parent;\n        this.__attributeIndex = attributeIndex;\n    }\n    /** @internal */\n    _$resolve(part, props) {\n        return this.update(part, props);\n    }\n    update(_part, props) {\n        return this.render(...props);\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,QAAQ,GAAG;EACpBC,SAAS,EAAE,CAAC;EACZC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,CAAC;EACXC,iBAAiB,EAAE,CAAC;EACpBC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE;AACb,CAAC;AACD;AACA;AACA;AACA;AACA,OAAO,MAAMC,SAAS,GAAIC,CAAC,IAAK,CAAC,GAAGC,MAAM,MAAM;EAC5C;EACA,CAAC,iBAAiB,GAAGD,CAAC;EACtBC;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,SAAS,CAAC;EACnBC,WAAWA,CAACC,SAAS,EAAE,CAAE;EACzB;EACA,IAAIC,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,QAAQ,CAACD,aAAa;EACtC;EACA;EACAE,YAAYA,CAACC,IAAI,EAAEC,MAAM,EAAEC,cAAc,EAAE;IACvC,IAAI,CAACC,MAAM,GAAGH,IAAI;IAClB,IAAI,CAACF,QAAQ,GAAGG,MAAM;IACtB,IAAI,CAACG,gBAAgB,GAAGF,cAAc;EAC1C;EACA;EACAG,SAASA,CAACL,IAAI,EAAEM,KAAK,EAAE;IACnB,OAAO,IAAI,CAACC,MAAM,CAACP,IAAI,EAAEM,KAAK,CAAC;EACnC;EACAC,MAAMA,CAACC,KAAK,EAAEF,KAAK,EAAE;IACjB,OAAO,IAAI,CAACG,MAAM,CAAC,GAAGH,KAAK,CAAC;EAChC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}