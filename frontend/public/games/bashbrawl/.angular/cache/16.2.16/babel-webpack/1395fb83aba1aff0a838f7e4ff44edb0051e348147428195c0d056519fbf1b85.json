{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"backup\",\n  V = [\"backup\", C({\n    outline: '<path d=\"M18 20L23.71 14.61C23.91 14.43 24 14.18 24 13.94C24 13.7 23.9 13.46 23.71 13.27C23.32 12.9 22.69 12.9 22.3 13.27L19.01 16.38V3C19.01 2.45 18.56 2 18.01 2C17.46 2 17.01 2.45 17.01 3V16.39L13.72 13.28C13.33 12.91 12.7 12.91 12.31 13.28C11.92 13.65 11.92 14.25 12.31 14.62L18.02 20.01L18 20ZM30 24H6V26H30V24ZM30 29.98V27.97H26V29.98H30ZM33.95 24.69L29.95 12.69C29.81 12.28 29.43 12.01 29 12.01H25.23C25.71 12.55 26 13.24 26 13.95C26 13.97 26 13.99 26 14.01H28.29L32.01 25.17V32.01H4V25.17L7.72 14.01H10.01C10.01 14.01 10.01 13.97 10.01 13.95C10.01 13.23 10.29 12.54 10.78 12.01H7.01C6.58 12.01 6.2 12.29 6.06 12.69L2.05 24.68C2.02 24.78 2 24.89 2 25V32C2 33.1 2.9 34 4 34H32C33.1 34 34 33.1 34 32V25C34 24.89 33.98 24.79 33.95 24.68V24.69Z\"/>',\n    outlineAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M28.6322 15.0367H30.7322L33.95 24.69V24.68C33.98 24.79 34 24.89 34 25V32C34 33.1 33.1 34 32 34H4C2.9 34 2 33.1 2 32V25C2 24.89 2.02 24.78 2.05 24.68L6.06 12.69C6.2 12.29 6.58 12.01 7.01 12.01H10.78C10.29 12.54 10.01 13.23 10.01 13.95V14.01H7.72L4 25.17V32.01H32.01V25.17L28.6322 15.0367Z\"/><path d=\"M20.7976 14.6902C21.271 14.9261 21.8003 15.0475 22.3395 15.0367H23.258L18.0047 19.9956L12.31 14.62C11.92 14.25 11.92 13.65 12.31 13.28C12.7 12.91 13.33 12.91 13.72 13.28L17.01 16.39V3C17.01 2.45 17.46 2 18.01 2C18.56 2 19.01 2.45 19.01 3V11.5766C18.9988 11.7308 18.9987 11.8858 19.01 12.0403V16.38L20.7976 14.6902Z\"/><path d=\"M18.0047 19.9956L18 20L18.02 20.01L18.0047 19.9956Z\"/><path d=\"M30 27.97V29.98H26V27.97H30Z\"/><path d=\"M6 26V24H30V26H6Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M30.0533 12.9998C30.0355 12.9999 30.0178 13 30 13C28.6873 13 27.459 12.6387 26.4091 12.01H25.23C25.71 12.55 26 13.24 26 13.95V14.01H28.29L32.01 25.17V32.01H4V25.17L7.72 14.01H10.01V13.95C10.01 13.23 10.29 12.54 10.78 12.01H7.01C6.58 12.01 6.2 12.29 6.06 12.69L2.05 24.68C2.02 24.78 2 24.89 2 25V32C2 33.1 2.9 34 4 34H32C33.1 34 34 33.1 34 32V25C34 24.89 33.98 24.79 33.95 24.68V24.69L30.0533 12.9998Z\"/><path d=\"M18.0047 19.9956L18 20L18.02 20.01L18.0047 19.9956Z\"/><path d=\"M23.71 14.61L18.0047 19.9956L12.31 14.62C11.92 14.25 11.92 13.65 12.31 13.28C12.7 12.91 13.33 12.91 13.72 13.28L17.01 16.39V3C17.01 2.45 17.46 2 18.01 2C18.56 2 19.01 2.45 19.01 3V16.38L22.3 13.27C22.69 12.9 23.32 12.9 23.71 13.27C23.9 13.46 24 13.7 24 13.94C24 14.18 23.91 14.43 23.71 14.61Z\"/><path d=\"M30 27.97V29.98H26V27.97H30Z\"/><path d=\"M6 26V24H30V26H6Z\"/>',\n    solid: '<path d=\"M18 20L23.71 14.61C23.91 14.43 24 14.18 24 13.94C24 13.7 23.9 13.46 23.71 13.27C23.32 12.9 22.69 12.9 22.3 13.27L19.01 16.38V3C19.01 2.45 18.56 2 18.01 2C17.46 2 17.01 2.45 17.01 3V16.39L13.72 13.28C13.33 12.91 12.7 12.91 12.31 13.28C11.92 13.65 11.92 14.25 12.31 14.62L18.02 20.01L18 20ZM29.95 12.68C29.81 12.27 29.43 12 29 12H25.23C25.71 12.54 26 13.23 26 13.94C26 14.74 25.66 15.51 25.08 16.06L19.37 21.45L18 22.75L16.63 21.45L10.92 16.06C10.34 15.51 10 14.73 10 13.94C10 13.22 10.28 12.53 10.77 12H7C6.57 12 6.19 12.28 6.05 12.68L2.28 24H33.72L29.95 12.68ZM2 26V32C2 33.1 2.9 34 4 34H32C33.1 34 34 33.1 34 32V26H2ZM30 30.01H26V28H30V30.01Z\"/>',\n    solidAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M25.7817 15.0367H30.7349L33.72 24H2.28L6.05 12.68C6.19 12.28 6.57 12 7 12H10.77C10.28 12.53 10 13.22 10 13.94C10 14.73 10.34 15.51 10.92 16.06L16.63 21.45L18 22.75L19.37 21.45L25.08 16.06C25.3852 15.7706 25.6239 15.4203 25.7817 15.0367Z\"/><path d=\"M20.7976 14.6902C21.271 14.9261 21.8003 15.0475 22.3395 15.0367H23.258L18.0047 19.9956L12.31 14.62C11.92 14.25 11.92 13.65 12.31 13.28C12.7 12.91 13.33 12.91 13.72 13.28L17.01 16.39V3C17.01 2.45 17.46 2 18.01 2C18.56 2 19.01 2.45 19.01 3V11.5766C18.9988 11.7308 18.9987 11.8858 19.01 12.0403V16.38L20.7976 14.6902Z\"/><path d=\"M18.0047 19.9956L18 20L18.02 20.01L18.0047 19.9956Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 32V26H34V32C34 33.1 33.1 34 32 34H4C2.9 34 2 33.1 2 32ZM26 30.01H30V28H26V30.01Z\"/>',\n    solidBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M30.0565 12.9998C30.0377 12.9999 30.0189 13 30 13C28.6803 13 27.446 12.6348 26.3924 12H25.23C25.71 12.54 26 13.23 26 13.94C26 14.74 25.66 15.51 25.08 16.06L19.37 21.45L18 22.75L16.63 21.45L10.92 16.06C10.34 15.51 10 14.73 10 13.94C10 13.22 10.28 12.53 10.77 12H7C6.57 12 6.19 12.28 6.05 12.68L2.28 24H33.72L30.0565 12.9998Z\"/><path d=\"M18.0047 19.9956L18 20L18.02 20.01L18.0047 19.9956Z\"/><path d=\"M23.71 14.61L18.0047 19.9956L12.31 14.62C11.92 14.25 11.92 13.65 12.31 13.28C12.7 12.91 13.33 12.91 13.72 13.28L17.01 16.39V3C17.01 2.45 17.46 2 18.01 2C18.56 2 19.01 2.45 19.01 3V16.38L22.3 13.27C22.69 12.9 23.32 12.9 23.71 13.27C23.9 13.46 24 13.7 24 13.94C24 14.18 23.91 14.43 23.71 14.61Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 32V26H34V32C34 33.1 33.1 34 32 34H4C2.9 34 2 33.1 2 32ZM26 30.01H30V28H26V30.01Z\"/>'\n  })];\nexport { V as backupIcon, L as backupIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "V", "outline", "outlineAlerted", "outlineBadged", "solid", "solidAlerted", "solidBadged", "backupIcon", "backupIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/backup.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"backup\",V=[\"backup\",C({outline:'<path d=\"M18 20L23.71 14.61C23.91 14.43 24 14.18 24 13.94C24 13.7 23.9 13.46 23.71 13.27C23.32 12.9 22.69 12.9 22.3 13.27L19.01 16.38V3C19.01 2.45 18.56 2 18.01 2C17.46 2 17.01 2.45 17.01 3V16.39L13.72 13.28C13.33 12.91 12.7 12.91 12.31 13.28C11.92 13.65 11.92 14.25 12.31 14.62L18.02 20.01L18 20ZM30 24H6V26H30V24ZM30 29.98V27.97H26V29.98H30ZM33.95 24.69L29.95 12.69C29.81 12.28 29.43 12.01 29 12.01H25.23C25.71 12.55 26 13.24 26 13.95C26 13.97 26 13.99 26 14.01H28.29L32.01 25.17V32.01H4V25.17L7.72 14.01H10.01C10.01 14.01 10.01 13.97 10.01 13.95C10.01 13.23 10.29 12.54 10.78 12.01H7.01C6.58 12.01 6.2 12.29 6.06 12.69L2.05 24.68C2.02 24.78 2 24.89 2 25V32C2 33.1 2.9 34 4 34H32C33.1 34 34 33.1 34 32V25C34 24.89 33.98 24.79 33.95 24.68V24.69Z\"/>',outlineAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M28.6322 15.0367H30.7322L33.95 24.69V24.68C33.98 24.79 34 24.89 34 25V32C34 33.1 33.1 34 32 34H4C2.9 34 2 33.1 2 32V25C2 24.89 2.02 24.78 2.05 24.68L6.06 12.69C6.2 12.29 6.58 12.01 7.01 12.01H10.78C10.29 12.54 10.01 13.23 10.01 13.95V14.01H7.72L4 25.17V32.01H32.01V25.17L28.6322 15.0367Z\"/><path d=\"M20.7976 14.6902C21.271 14.9261 21.8003 15.0475 22.3395 15.0367H23.258L18.0047 19.9956L12.31 14.62C11.92 14.25 11.92 13.65 12.31 13.28C12.7 12.91 13.33 12.91 13.72 13.28L17.01 16.39V3C17.01 2.45 17.46 2 18.01 2C18.56 2 19.01 2.45 19.01 3V11.5766C18.9988 11.7308 18.9987 11.8858 19.01 12.0403V16.38L20.7976 14.6902Z\"/><path d=\"M18.0047 19.9956L18 20L18.02 20.01L18.0047 19.9956Z\"/><path d=\"M30 27.97V29.98H26V27.97H30Z\"/><path d=\"M6 26V24H30V26H6Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M30.0533 12.9998C30.0355 12.9999 30.0178 13 30 13C28.6873 13 27.459 12.6387 26.4091 12.01H25.23C25.71 12.55 26 13.24 26 13.95V14.01H28.29L32.01 25.17V32.01H4V25.17L7.72 14.01H10.01V13.95C10.01 13.23 10.29 12.54 10.78 12.01H7.01C6.58 12.01 6.2 12.29 6.06 12.69L2.05 24.68C2.02 24.78 2 24.89 2 25V32C2 33.1 2.9 34 4 34H32C33.1 34 34 33.1 34 32V25C34 24.89 33.98 24.79 33.95 24.68V24.69L30.0533 12.9998Z\"/><path d=\"M18.0047 19.9956L18 20L18.02 20.01L18.0047 19.9956Z\"/><path d=\"M23.71 14.61L18.0047 19.9956L12.31 14.62C11.92 14.25 11.92 13.65 12.31 13.28C12.7 12.91 13.33 12.91 13.72 13.28L17.01 16.39V3C17.01 2.45 17.46 2 18.01 2C18.56 2 19.01 2.45 19.01 3V16.38L22.3 13.27C22.69 12.9 23.32 12.9 23.71 13.27C23.9 13.46 24 13.7 24 13.94C24 14.18 23.91 14.43 23.71 14.61Z\"/><path d=\"M30 27.97V29.98H26V27.97H30Z\"/><path d=\"M6 26V24H30V26H6Z\"/>',solid:'<path d=\"M18 20L23.71 14.61C23.91 14.43 24 14.18 24 13.94C24 13.7 23.9 13.46 23.71 13.27C23.32 12.9 22.69 12.9 22.3 13.27L19.01 16.38V3C19.01 2.45 18.56 2 18.01 2C17.46 2 17.01 2.45 17.01 3V16.39L13.72 13.28C13.33 12.91 12.7 12.91 12.31 13.28C11.92 13.65 11.92 14.25 12.31 14.62L18.02 20.01L18 20ZM29.95 12.68C29.81 12.27 29.43 12 29 12H25.23C25.71 12.54 26 13.23 26 13.94C26 14.74 25.66 15.51 25.08 16.06L19.37 21.45L18 22.75L16.63 21.45L10.92 16.06C10.34 15.51 10 14.73 10 13.94C10 13.22 10.28 12.53 10.77 12H7C6.57 12 6.19 12.28 6.05 12.68L2.28 24H33.72L29.95 12.68ZM2 26V32C2 33.1 2.9 34 4 34H32C33.1 34 34 33.1 34 32V26H2ZM30 30.01H26V28H30V30.01Z\"/>',solidAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M25.7817 15.0367H30.7349L33.72 24H2.28L6.05 12.68C6.19 12.28 6.57 12 7 12H10.77C10.28 12.53 10 13.22 10 13.94C10 14.73 10.34 15.51 10.92 16.06L16.63 21.45L18 22.75L19.37 21.45L25.08 16.06C25.3852 15.7706 25.6239 15.4203 25.7817 15.0367Z\"/><path d=\"M20.7976 14.6902C21.271 14.9261 21.8003 15.0475 22.3395 15.0367H23.258L18.0047 19.9956L12.31 14.62C11.92 14.25 11.92 13.65 12.31 13.28C12.7 12.91 13.33 12.91 13.72 13.28L17.01 16.39V3C17.01 2.45 17.46 2 18.01 2C18.56 2 19.01 2.45 19.01 3V11.5766C18.9988 11.7308 18.9987 11.8858 19.01 12.0403V16.38L20.7976 14.6902Z\"/><path d=\"M18.0047 19.9956L18 20L18.02 20.01L18.0047 19.9956Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 32V26H34V32C34 33.1 33.1 34 32 34H4C2.9 34 2 33.1 2 32ZM26 30.01H30V28H26V30.01Z\"/>',solidBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M30.0565 12.9998C30.0377 12.9999 30.0189 13 30 13C28.6803 13 27.446 12.6348 26.3924 12H25.23C25.71 12.54 26 13.23 26 13.94C26 14.74 25.66 15.51 25.08 16.06L19.37 21.45L18 22.75L16.63 21.45L10.92 16.06C10.34 15.51 10 14.73 10 13.94C10 13.22 10.28 12.53 10.77 12H7C6.57 12 6.19 12.28 6.05 12.68L2.28 24H33.72L30.0565 12.9998Z\"/><path d=\"M18.0047 19.9956L18 20L18.02 20.01L18.0047 19.9956Z\"/><path d=\"M23.71 14.61L18.0047 19.9956L12.31 14.62C11.92 14.25 11.92 13.65 12.31 13.28C12.7 12.91 13.33 12.91 13.72 13.28L17.01 16.39V3C17.01 2.45 17.46 2 18.01 2C18.56 2 19.01 2.45 19.01 3V16.38L22.3 13.27C22.69 12.9 23.32 12.9 23.71 13.27C23.9 13.46 24 13.7 24 13.94C24 14.18 23.91 14.43 23.71 14.61Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 32V26H34V32C34 33.1 33.1 34 32 34H4C2.9 34 2 33.1 2 32ZM26 30.01H30V28H26V30.01Z\"/>'})];export{V as backupIcon,L as backupIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,+uBAA+uB;IAACC,cAAc,EAAC,0lCAA0lC;IAACC,aAAa,EAAC,g9BAAg9B;IAACC,KAAK,EAAC,ipBAAipB;IAACC,YAAY,EAAC,ymCAAymC;IAACC,WAAW,EAAC;EAAq8B,CAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,UAAU,EAACR,CAAC,IAAIS,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}