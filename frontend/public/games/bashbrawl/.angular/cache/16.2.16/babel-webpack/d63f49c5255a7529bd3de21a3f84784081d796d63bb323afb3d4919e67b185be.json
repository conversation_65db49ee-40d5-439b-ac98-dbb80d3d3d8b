{"ast": null, "code": "/**\n * Tests whether or not an object is a typed array.\n *\n * @private\n * @param {*} val The object to test.\n * @return {Boolean} `true` if `val` is a typed array, `false` otherwise.\n * @example\n *\n *      _isTypedArray(new Uint8Array([])); //=> true\n *      _isTypedArray(new Float32Array([])); //=> true\n *      _isTypedArray([]); //=> false\n *      _isTypedArray(null); //=> false\n *      _isTypedArray({}); //=> false\n */\nexport default function _isTypedArray(val) {\n  var type = Object.prototype.toString.call(val);\n  return type === '[object Uint8ClampedArray]' || type === '[object Int8Array]' || type === '[object Uint8Array]' || type === '[object Int16Array]' || type === '[object Uint16Array]' || type === '[object Int32Array]' || type === '[object Uint32Array]' || type === '[object Float32Array]' || type === '[object Float64Array]' || type === '[object BigInt64Array]' || type === '[object BigUint64Array]';\n}", "map": {"version": 3, "names": ["_isTypedArray", "val", "type", "Object", "prototype", "toString", "call"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_isTypedArray.js"], "sourcesContent": ["/**\n * Tests whether or not an object is a typed array.\n *\n * @private\n * @param {*} val The object to test.\n * @return {Boolean} `true` if `val` is a typed array, `false` otherwise.\n * @example\n *\n *      _isTypedArray(new Uint8Array([])); //=> true\n *      _isTypedArray(new Float32Array([])); //=> true\n *      _isTypedArray([]); //=> false\n *      _isTypedArray(null); //=> false\n *      _isTypedArray({}); //=> false\n */\nexport default function _isTypedArray(val) {\n  var type = Object.prototype.toString.call(val);\n  return type === '[object Uint8ClampedArray]' || type === '[object Int8Array]' || type === '[object Uint8Array]' || type === '[object Int16Array]' || type === '[object Uint16Array]' || type === '[object Int32Array]' || type === '[object Uint32Array]' || type === '[object Float32Array]' || type === '[object Float64Array]' || type === '[object BigInt64Array]' || type === '[object BigUint64Array]';\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,aAAaA,CAACC,GAAG,EAAE;EACzC,IAAIC,IAAI,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,GAAG,CAAC;EAC9C,OAAOC,IAAI,KAAK,4BAA4B,IAAIA,IAAI,KAAK,oBAAoB,IAAIA,IAAI,KAAK,qBAAqB,IAAIA,IAAI,KAAK,qBAAqB,IAAIA,IAAI,KAAK,sBAAsB,IAAIA,IAAI,KAAK,qBAAqB,IAAIA,IAAI,KAAK,sBAAsB,IAAIA,IAAI,KAAK,uBAAuB,IAAIA,IAAI,KAAK,uBAAuB,IAAIA,IAAI,KAAK,wBAAwB,IAAIA,IAAI,KAAK,yBAAyB;AAC9Y", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}