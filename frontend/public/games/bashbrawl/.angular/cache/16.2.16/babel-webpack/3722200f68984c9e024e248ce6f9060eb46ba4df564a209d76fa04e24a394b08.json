{"ast": null, "code": "import _arrayReduce from \"./internal/_arrayReduce.js\";\nimport _curry2 from \"./internal/_curry2.js\";\nimport _dispatchable from \"./internal/_dispatchable.js\";\nimport _filter from \"./internal/_filter.js\";\nimport _isObject from \"./internal/_isObject.js\";\nimport _xfilter from \"./internal/_xfilter.js\";\nimport keys from \"./keys.js\";\n/**\n * Takes a predicate and a `Filterable`, and returns a new filterable of the\n * same type containing the members of the given filterable which satisfy the\n * given predicate. Filterable objects include plain objects or any object\n * that has a filter method such as `Array`.\n *\n * Dispatches to the `filter` method of the second argument, if present.\n *\n * Acts as a transducer if a transformer is given in list position.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @category Object\n * @sig Filterable f => (a -> Boolean) -> f a -> f a\n * @param {Function} pred\n * @param {Array} filterable\n * @return {Array} Filterable\n * @see <PERSON><PERSON>reject, <PERSON><PERSON>transduce, <PERSON>.addIndex\n * @example\n *\n *      const isEven = n => n % 2 === 0;\n *\n *      R.filter(isEven, [1, 2, 3, 4]); //=> [2, 4]\n *\n *      R.filter(isEven, {a: 1, b: 2, c: 3, d: 4}); //=> {b: 2, d: 4}\n */\n\nvar filter = /*#__PURE__*/\n_curry2( /*#__PURE__*/\n_dispatchable(['fantasy-land/filter', 'filter'], _xfilter, function (pred, filterable) {\n  return _isObject(filterable) ? _arrayReduce(function (acc, key) {\n    if (pred(filterable[key])) {\n      acc[key] = filterable[key];\n    }\n    return acc;\n  }, {}, keys(filterable)) :\n  // else\n  _filter(pred, filterable);\n}));\nexport default filter;", "map": {"version": 3, "names": ["_arrayReduce", "_curry2", "_dispatchable", "_filter", "_isObject", "_xfilter", "keys", "filter", "pred", "filterable", "acc", "key"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/filter.js"], "sourcesContent": ["import _arrayReduce from \"./internal/_arrayReduce.js\";\nimport _curry2 from \"./internal/_curry2.js\";\nimport _dispatchable from \"./internal/_dispatchable.js\";\nimport _filter from \"./internal/_filter.js\";\nimport _isObject from \"./internal/_isObject.js\";\nimport _xfilter from \"./internal/_xfilter.js\";\nimport keys from \"./keys.js\";\n/**\n * Takes a predicate and a `Filterable`, and returns a new filterable of the\n * same type containing the members of the given filterable which satisfy the\n * given predicate. Filterable objects include plain objects or any object\n * that has a filter method such as `Array`.\n *\n * Dispatches to the `filter` method of the second argument, if present.\n *\n * Acts as a transducer if a transformer is given in list position.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category List\n * @category Object\n * @sig Filterable f => (a -> Boolean) -> f a -> f a\n * @param {Function} pred\n * @param {Array} filterable\n * @return {Array} Filterable\n * @see <PERSON><PERSON>reject, <PERSON><PERSON>transduce, <PERSON>.addIndex\n * @example\n *\n *      const isEven = n => n % 2 === 0;\n *\n *      R.filter(isEven, [1, 2, 3, 4]); //=> [2, 4]\n *\n *      R.filter(isEven, {a: 1, b: 2, c: 3, d: 4}); //=> {b: 2, d: 4}\n */\n\nvar filter =\n/*#__PURE__*/\n_curry2(\n/*#__PURE__*/\n_dispatchable(['fantasy-land/filter', 'filter'], _xfilter, function (pred, filterable) {\n  return _isObject(filterable) ? _arrayReduce(function (acc, key) {\n    if (pred(filterable[key])) {\n      acc[key] = filterable[key];\n    }\n\n    return acc;\n  }, {}, keys(filterable)) : // else\n  _filter(pred, filterable);\n}));\n\nexport default filter;"], "mappings": "AAAA,OAAOA,YAAY,MAAM,4BAA4B;AACrD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,IAAI,MAAM,WAAW;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,MAAM,GACV;AACAN,OAAO,EACP;AACAC,aAAa,CAAC,CAAC,qBAAqB,EAAE,QAAQ,CAAC,EAAEG,QAAQ,EAAE,UAAUG,IAAI,EAAEC,UAAU,EAAE;EACrF,OAAOL,SAAS,CAACK,UAAU,CAAC,GAAGT,YAAY,CAAC,UAAUU,GAAG,EAAEC,GAAG,EAAE;IAC9D,IAAIH,IAAI,CAACC,UAAU,CAACE,GAAG,CAAC,CAAC,EAAE;MACzBD,GAAG,CAACC,GAAG,CAAC,GAAGF,UAAU,CAACE,GAAG,CAAC;IAC5B;IAEA,OAAOD,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,EAAEJ,IAAI,CAACG,UAAU,CAAC,CAAC;EAAG;EAC3BN,OAAO,CAACK,IAAI,EAAEC,UAAU,CAAC;AAC3B,CAAC,CAAC,CAAC;AAEH,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}