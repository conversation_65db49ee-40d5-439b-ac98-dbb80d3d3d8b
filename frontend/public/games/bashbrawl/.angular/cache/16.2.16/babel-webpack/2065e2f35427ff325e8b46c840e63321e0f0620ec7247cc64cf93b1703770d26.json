{"ast": null, "code": "import { renderIcon as L } from \"../icon.renderer.js\";\nconst C = \"unlink\",\n  n = [\"unlink\", L({\n    outline: '<path d=\"M17.3801 13.1357L19.8501 10.6767C19.8501 10.6767 19.8501 10.6767 19.8401 10.6767L21.2301 9.28721C22.7901 7.73782 25.3001 7.73782 26.8501 9.28721C27.5901 10.0269 28.0001 11.0365 28.0001 12.0761C28.0001 13.1157 27.5901 14.1253 26.8501 14.865L26.2201 15.5348L22.9901 18.7435L24.3901 20.143L27.5601 16.9842L28.2501 16.2945C29.3701 15.1749 29.9901 13.6555 29.9901 12.0661C29.9901 10.4767 29.3601 8.95734 28.2501 7.84777L28.2701 7.80779C25.9301 5.4787 22.1601 5.4787 19.8201 7.80779L16.1701 11.4564L15.9301 11.6963L17.3701 13.1357H17.3801ZM33.7101 32.2782L3.71006 2.28994C3.32006 1.9001 2.68006 1.9001 2.29006 2.28994C1.90006 2.67979 1.90006 3.31954 2.29006 3.70939L13.1101 14.5252L7.74006 19.8731C6.62006 20.9926 5.99006 22.512 5.99006 24.1014C5.99006 25.6908 6.62006 27.2102 7.74006 28.3298C10.0801 30.6589 13.8501 30.6589 16.1901 28.3298L21.5601 22.9819L32.2901 33.7077C32.4901 33.9076 32.7401 33.9976 33.0001 33.9976C33.2601 33.9976 33.5101 33.8976 33.7101 33.7077C34.1001 33.3178 34.1001 32.6881 33.7101 32.2982V32.2782ZM14.8001 26.8803C13.2401 28.4297 10.7301 28.4297 9.18006 26.8803C8.44006 26.1406 8.03006 25.131 8.03006 24.0914C8.03006 23.0518 8.44006 22.0422 9.18006 21.3025L14.5601 15.9546L20.1701 21.5624L14.8101 26.8803H14.8001Z\"/>'\n  })];\nexport { n as unlinkIcon, C as unlinkIconName };", "map": {"version": 3, "names": ["renderIcon", "L", "C", "n", "outline", "unlinkIcon", "unlinkIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/unlink.js"], "sourcesContent": ["import{renderIcon as L}from\"../icon.renderer.js\";const C=\"unlink\",n=[\"unlink\",L({outline:'<path d=\"M17.3801 13.1357L19.8501 10.6767C19.8501 10.6767 19.8501 10.6767 19.8401 10.6767L21.2301 9.28721C22.7901 7.73782 25.3001 7.73782 26.8501 9.28721C27.5901 10.0269 28.0001 11.0365 28.0001 12.0761C28.0001 13.1157 27.5901 14.1253 26.8501 14.865L26.2201 15.5348L22.9901 18.7435L24.3901 20.143L27.5601 16.9842L28.2501 16.2945C29.3701 15.1749 29.9901 13.6555 29.9901 12.0661C29.9901 10.4767 29.3601 8.95734 28.2501 7.84777L28.2701 7.80779C25.9301 5.4787 22.1601 5.4787 19.8201 7.80779L16.1701 11.4564L15.9301 11.6963L17.3701 13.1357H17.3801ZM33.7101 32.2782L3.71006 2.28994C3.32006 1.9001 2.68006 1.9001 2.29006 2.28994C1.90006 2.67979 1.90006 3.31954 2.29006 3.70939L13.1101 14.5252L7.74006 19.8731C6.62006 20.9926 5.99006 22.512 5.99006 24.1014C5.99006 25.6908 6.62006 27.2102 7.74006 28.3298C10.0801 30.6589 13.8501 30.6589 16.1901 28.3298L21.5601 22.9819L32.2901 33.7077C32.4901 33.9076 32.7401 33.9976 33.0001 33.9976C33.2601 33.9976 33.5101 33.8976 33.7101 33.7077C34.1001 33.3178 34.1001 32.6881 33.7101 32.2982V32.2782ZM14.8001 26.8803C13.2401 28.4297 10.7301 28.4297 9.18006 26.8803C8.44006 26.1406 8.03006 25.131 8.03006 24.0914C8.03006 23.0518 8.44006 22.0422 9.18006 21.3025L14.5601 15.9546L20.1701 21.5624L14.8101 26.8803H14.8001Z\"/>'})];export{n as unlinkIcon,C as unlinkIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAiuC,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,UAAU,EAACH,CAAC,IAAII,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}