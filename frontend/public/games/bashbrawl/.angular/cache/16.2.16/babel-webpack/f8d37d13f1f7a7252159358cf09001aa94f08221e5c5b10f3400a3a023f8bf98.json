{"ast": null, "code": "function t() {\n  return t => t.addInitializer(t => new s(t));\n}\nclass s {\n  constructor(t) {\n    this.host = t, this.host.addController(this);\n  }\n  hostUpdated() {\n    null !== this.host.disabled && (this.host.ariaDisabled = this.host.disabled), this.host.readonly && (this.host.ariaDisabled = null);\n  }\n}\nexport { s as AriaDisabledController, t as ariaDisabled };", "map": {"version": 3, "names": ["t", "addInitializer", "s", "constructor", "host", "addController", "hostUpdated", "disabled", "ariaDisabled", "readonly", "AriaDisabledController"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/aria-disabled.controller.js"], "sourcesContent": ["function t(){return t=>t.addInitializer((t=>new s(t)))}class s{constructor(t){this.host=t,this.host.addController(this)}hostUpdated(){null!==this.host.disabled&&(this.host.ariaDisabled=this.host.disabled),this.host.readonly&&(this.host.ariaDisabled=null)}}export{s as AriaDisabledController,t as ariaDisabled};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAOA,CAAC,IAAEA,CAAC,CAACC,cAAc,CAAED,CAAC,IAAE,IAAIE,CAAC,CAACF,CAAC,CAAE,CAAC;AAAA;AAAC,MAAME,CAAC;EAACC,WAAWA,CAACH,CAAC,EAAC;IAAC,IAAI,CAACI,IAAI,GAACJ,CAAC,EAAC,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;EAAA;EAACC,WAAWA,CAAA,EAAE;IAAC,IAAI,KAAG,IAAI,CAACF,IAAI,CAACG,QAAQ,KAAG,IAAI,CAACH,IAAI,CAACI,YAAY,GAAC,IAAI,CAACJ,IAAI,CAACG,QAAQ,CAAC,EAAC,IAAI,CAACH,IAAI,CAACK,QAAQ,KAAG,IAAI,CAACL,IAAI,CAACI,YAAY,GAAC,IAAI,CAAC;EAAA;AAAC;AAAC,SAAON,CAAC,IAAIQ,sBAAsB,EAACV,CAAC,IAAIQ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}