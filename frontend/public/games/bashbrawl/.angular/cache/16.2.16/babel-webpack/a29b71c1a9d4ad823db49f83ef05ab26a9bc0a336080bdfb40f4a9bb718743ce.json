{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst M = \"wifi\",\n  Z = [\"wifi\", C({\n    outline: '<path d=\"M18.0001 24C15.8001 24 14.0101 25.79 14.0101 28C14.0101 30.21 15.7901 32 18.0001 32C20.2101 32 21.9901 30.21 21.9901 28C21.9901 25.79 20.2101 24 18.0001 24ZM18.0001 30C16.9001 30 16.0101 29.1 16.0101 28C16.0101 26.9 16.9001 26 18.0001 26C19.1001 26 19.9901 26.9 19.9901 28C19.9901 29.1 19.1001 30 18.0001 30ZM10.2701 19.26C9.88006 19.66 9.88006 20.3 10.2701 20.7C10.6601 21.1 11.2901 21.1 11.6801 20.7C15.1701 17.14 20.8401 17.14 24.3301 20.7C24.5201 20.9 24.7801 21 25.0301 21C25.2801 21 25.5401 20.9 25.7301 20.7C26.1201 20.3 26.1201 19.66 25.7301 19.26C21.4701 14.91 14.5301 14.91 10.2601 19.26H10.2701ZM33.7101 10.34C25.0501 1.89 10.9501 1.89 2.29006 10.34C1.90006 10.72 1.90006 11.34 2.29006 11.72C2.68006 12.1 3.31006 12.1 3.70006 11.72C11.5901 4.02 24.4201 4.02 32.3001 11.71C32.4901 11.9 32.7501 12 33.0001 12C33.2501 12 33.5101 11.91 33.7001 11.71C34.0901 11.33 34.0901 10.71 33.7001 10.33L33.7101 10.34ZM6.29006 14.87C5.90006 15.26 5.90006 15.89 6.29006 16.28C6.68006 16.67 7.31006 16.67 7.70006 16.28C10.4501 13.52 14.1101 12 18.0001 12C21.8901 12 25.5501 13.52 28.3001 16.28C28.4901 16.48 28.7501 16.57 29.0001 16.57C29.2501 16.57 29.5101 16.47 29.7001 16.28C30.0901 15.89 30.0901 15.26 29.7001 14.87C23.2401 8.39 12.7301 8.39 6.27006 14.87H6.29006Z\"/>',\n    solid: '<path d=\"M18.005 24.0054C15.7943 24.0054 14.0038 25.7958 14.0038 28.0064C14.0038 30.217 15.7943 32.0075 18.005 32.0075C20.2157 32.0075 22.0063 30.217 22.0063 28.0064C22.0063 25.7958 20.2157 24.0054 18.005 24.0054ZM18.005 15.8232C15.0341 15.8232 12.2532 17.0135 10.1525 19.1841C9.59237 19.7642 9.59237 20.7145 10.1525 21.2946C10.4226 21.5747 10.7927 21.7348 11.1829 21.7348C11.573 21.7348 11.9431 21.5747 12.2132 21.2946C13.7537 19.6942 15.8143 18.814 18.005 18.814C20.1957 18.814 22.2463 19.6942 23.7968 21.2946C24.347 21.8648 25.3073 21.8648 25.8575 21.2946C26.4176 20.7145 26.4176 19.7642 25.8575 19.1841C23.7668 17.0135 20.9759 15.8232 18.015 15.8232H18.005ZM18.005 9.92159C13.5736 9.92159 9.42232 11.6721 6.30134 14.8529C5.73117 15.4331 5.73117 16.3633 6.30134 16.9435C6.85152 17.5036 7.80181 17.5036 8.35198 16.9435C10.9328 14.3128 14.3539 12.8724 18.005 12.8724C21.6561 12.8724 25.0772 14.3228 27.658 16.9435C27.9281 17.2235 28.2982 17.3736 28.6883 17.3736C29.0785 17.3736 29.4386 17.2235 29.7187 16.9435C30.2888 16.3633 30.2888 15.4331 29.7187 14.8529C26.5977 11.6721 22.4364 9.92159 18.015 9.92159H18.005ZM33.5799 10.3917C24.9872 1.87944 11.0128 1.87944 2.43013 10.3917C2.15005 10.6718 2 11.0319 2 11.422C2 11.8121 2.15005 12.1822 2.43013 12.4523C2.99031 13.0124 3.9106 13.0124 4.47077 12.4523C11.9331 5.06029 24.0769 5.06029 31.5292 12.4523C31.7993 12.7223 32.1694 12.8724 32.5495 12.8724C32.9297 12.8724 33.2998 12.7223 33.5699 12.4523C33.85 12.1822 34 11.8121 34 11.422C34 11.0319 33.85 10.6618 33.5699 10.3917H33.5799Z\"/>'\n  })];\nexport { Z as wifiIcon, M as wifiIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "M", "Z", "outline", "solid", "wifiIcon", "wifiIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/wifi.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const M=\"wifi\",Z=[\"wifi\",C({outline:'<path d=\"M18.0001 24C15.8001 24 14.0101 25.79 14.0101 28C14.0101 30.21 15.7901 32 18.0001 32C20.2101 32 21.9901 30.21 21.9901 28C21.9901 25.79 20.2101 24 18.0001 24ZM18.0001 30C16.9001 30 16.0101 29.1 16.0101 28C16.0101 26.9 16.9001 26 18.0001 26C19.1001 26 19.9901 26.9 19.9901 28C19.9901 29.1 19.1001 30 18.0001 30ZM10.2701 19.26C9.88006 19.66 9.88006 20.3 10.2701 20.7C10.6601 21.1 11.2901 21.1 11.6801 20.7C15.1701 17.14 20.8401 17.14 24.3301 20.7C24.5201 20.9 24.7801 21 25.0301 21C25.2801 21 25.5401 20.9 25.7301 20.7C26.1201 20.3 26.1201 19.66 25.7301 19.26C21.4701 14.91 14.5301 14.91 10.2601 19.26H10.2701ZM33.7101 10.34C25.0501 1.89 10.9501 1.89 2.29006 10.34C1.90006 10.72 1.90006 11.34 2.29006 11.72C2.68006 12.1 3.31006 12.1 3.70006 11.72C11.5901 4.02 24.4201 4.02 32.3001 11.71C32.4901 11.9 32.7501 12 33.0001 12C33.2501 12 33.5101 11.91 33.7001 11.71C34.0901 11.33 34.0901 10.71 33.7001 10.33L33.7101 10.34ZM6.29006 14.87C5.90006 15.26 5.90006 15.89 6.29006 16.28C6.68006 16.67 7.31006 16.67 7.70006 16.28C10.4501 13.52 14.1101 12 18.0001 12C21.8901 12 25.5501 13.52 28.3001 16.28C28.4901 16.48 28.7501 16.57 29.0001 16.57C29.2501 16.57 29.5101 16.47 29.7001 16.28C30.0901 15.89 30.0901 15.26 29.7001 14.87C23.2401 8.39 12.7301 8.39 6.27006 14.87H6.29006Z\"/>',solid:'<path d=\"M18.005 24.0054C15.7943 24.0054 14.0038 25.7958 14.0038 28.0064C14.0038 30.217 15.7943 32.0075 18.005 32.0075C20.2157 32.0075 22.0063 30.217 22.0063 28.0064C22.0063 25.7958 20.2157 24.0054 18.005 24.0054ZM18.005 15.8232C15.0341 15.8232 12.2532 17.0135 10.1525 19.1841C9.59237 19.7642 9.59237 20.7145 10.1525 21.2946C10.4226 21.5747 10.7927 21.7348 11.1829 21.7348C11.573 21.7348 11.9431 21.5747 12.2132 21.2946C13.7537 19.6942 15.8143 18.814 18.005 18.814C20.1957 18.814 22.2463 19.6942 23.7968 21.2946C24.347 21.8648 25.3073 21.8648 25.8575 21.2946C26.4176 20.7145 26.4176 19.7642 25.8575 19.1841C23.7668 17.0135 20.9759 15.8232 18.015 15.8232H18.005ZM18.005 9.92159C13.5736 9.92159 9.42232 11.6721 6.30134 14.8529C5.73117 15.4331 5.73117 16.3633 6.30134 16.9435C6.85152 17.5036 7.80181 17.5036 8.35198 16.9435C10.9328 14.3128 14.3539 12.8724 18.005 12.8724C21.6561 12.8724 25.0772 14.3228 27.658 16.9435C27.9281 17.2235 28.2982 17.3736 28.6883 17.3736C29.0785 17.3736 29.4386 17.2235 29.7187 16.9435C30.2888 16.3633 30.2888 15.4331 29.7187 14.8529C26.5977 11.6721 22.4364 9.92159 18.015 9.92159H18.005ZM33.5799 10.3917C24.9872 1.87944 11.0128 1.87944 2.43013 10.3917C2.15005 10.6718 2 11.0319 2 11.422C2 11.8121 2.15005 12.1822 2.43013 12.4523C2.99031 13.0124 3.9106 13.0124 4.47077 12.4523C11.9331 5.06029 24.0769 5.06029 31.5292 12.4523C31.7993 12.7223 32.1694 12.8724 32.5495 12.8724C32.9297 12.8724 33.2998 12.7223 33.5699 12.4523C33.85 12.1822 34 11.8121 34 11.422C34 11.0319 33.85 10.6618 33.5699 10.3917H33.5799Z\"/>'})];export{Z as wifiIcon,M as wifiIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,MAAM;EAACC,CAAC,GAAC,CAAC,MAAM,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,2vCAA2vC;IAACC,KAAK,EAAC;EAA+/C,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,QAAQ,EAACJ,CAAC,IAAIK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}