{"ast": null, "code": "import { renderIcon as r } from \"../icon.renderer.js\";\nconst C = \"child-arrow\",\n  o = [\"child-arrow\", r({\n    outline: '<path d=\"M31.6976 24.2973L25.7003 18.2951C25.3105 17.905 24.6808 17.905 24.2909 18.2951C23.9011 18.6852 23.9011 19.3155 24.2909 19.7056L28.579 23.9971H8.99777C7.34851 23.9971 5.99911 22.7367 5.99911 21.1861V5.00036C5.99911 4.45016 5.54931 4 4.99955 4C4.4498 4 4 4.45016 4 5.00036V21.1961C4 23.8471 6.239 26.0079 8.99777 26.0079H28.579L24.2909 30.2994C23.9011 30.6895 23.9011 31.3198 24.2909 31.7099C24.4909 31.91 24.7407 32 25.0006 32C25.2605 32 25.5104 31.9 25.7103 31.7099L31.7076 25.7078C32.0975 25.3176 32.0975 24.6874 31.7076 24.2973H31.6976Z\"/>'\n  })];\nexport { o as childArrowIcon, C as childArrowIconName };", "map": {"version": 3, "names": ["renderIcon", "r", "C", "o", "outline", "childArrowIcon", "childArrowIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/child-arrow.js"], "sourcesContent": ["import{renderIcon as r}from\"../icon.renderer.js\";const C=\"child-arrow\",o=[\"child-arrow\",r({outline:'<path d=\"M31.6976 24.2973L25.7003 18.2951C25.3105 17.905 24.6808 17.905 24.2909 18.2951C23.9011 18.6852 23.9011 19.3155 24.2909 19.7056L28.579 23.9971H8.99777C7.34851 23.9971 5.99911 22.7367 5.99911 21.1861V5.00036C5.99911 4.45016 5.54931 4 4.99955 4C4.4498 4 4 4.45016 4 5.00036V21.1961C4 23.8471 6.239 26.0079 8.99777 26.0079H28.579L24.2909 30.2994C23.9011 30.6895 23.9011 31.3198 24.2909 31.7099C24.4909 31.91 24.7407 32 25.0006 32C25.2605 32 25.5104 31.9 25.7103 31.7099L31.7076 25.7078C32.0975 25.3176 32.0975 24.6874 31.7076 24.2973H31.6976Z\"/>'})];export{o as childArrowIcon,C as childArrowIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,aAAa;EAACC,CAAC,GAAC,CAAC,aAAa,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAwiB,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,cAAc,EAACH,CAAC,IAAII,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}