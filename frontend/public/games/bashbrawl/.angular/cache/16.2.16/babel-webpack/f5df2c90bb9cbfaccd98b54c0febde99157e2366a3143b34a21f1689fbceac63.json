{"ast": null, "code": "import _curry1 from \"./internal/_curry1.js\";\nimport empty from \"./empty.js\";\nimport equals from \"./equals.js\";\n/**\n * Returns `true` if the given value is its type's empty value; `false`\n * otherwise.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Logic\n * @sig a -> Boolean\n * @param {*} x\n * @return {Boolean}\n * @see R.empty\n * @example\n *\n *      R.isEmpty([1, 2, 3]);           //=> false\n *      R.isEmpty([]);                  //=> true\n *      R.isEmpty('');                  //=> true\n *      R.isEmpty(null);                //=> false\n *      R.isEmpty({});                  //=> true\n *      R.isEmpty({length: 0});         //=> false\n *      R.isEmpty(Uint8Array.from('')); //=> true\n */\n\nvar isEmpty = /*#__PURE__*/\n_curry1(function isEmpty(x) {\n  return x != null && equals(x, empty(x));\n});\nexport default isEmpty;", "map": {"version": 3, "names": ["_curry1", "empty", "equals", "isEmpty", "x"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/isEmpty.js"], "sourcesContent": ["import _curry1 from \"./internal/_curry1.js\";\nimport empty from \"./empty.js\";\nimport equals from \"./equals.js\";\n/**\n * Returns `true` if the given value is its type's empty value; `false`\n * otherwise.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Logic\n * @sig a -> Boolean\n * @param {*} x\n * @return {Boolean}\n * @see R.empty\n * @example\n *\n *      R.isEmpty([1, 2, 3]);           //=> false\n *      R.isEmpty([]);                  //=> true\n *      R.isEmpty('');                  //=> true\n *      R.isEmpty(null);                //=> false\n *      R.isEmpty({});                  //=> true\n *      R.isEmpty({length: 0});         //=> false\n *      R.isEmpty(Uint8Array.from('')); //=> true\n */\n\nvar isEmpty =\n/*#__PURE__*/\n_curry1(function isEmpty(x) {\n  return x != null && equals(x, empty(x));\n});\n\nexport default isEmpty;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,KAAK,MAAM,YAAY;AAC9B,OAAOC,MAAM,MAAM,aAAa;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,OAAO,GACX;AACAH,OAAO,CAAC,SAASG,OAAOA,CAACC,CAAC,EAAE;EAC1B,OAAOA,CAAC,IAAI,IAAI,IAAIF,MAAM,CAACE,CAAC,EAAEH,KAAK,CAACG,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC;AAEF,eAAeD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}