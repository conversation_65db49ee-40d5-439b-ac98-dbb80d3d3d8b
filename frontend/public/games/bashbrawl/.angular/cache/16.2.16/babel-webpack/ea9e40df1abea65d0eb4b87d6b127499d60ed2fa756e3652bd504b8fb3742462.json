{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"airplane\",\n  H = [\"airplane\", C({\n    outline: '<path d=\"M5.56749 13.93L11.2875 16.22L12.7675 14.66L7.70749 12.64L8.33749 12.01H15.2775L17.1775 10.01H7.93749C7.66749 10.01 7.41749 10.12 7.22749 10.3L5.22749 12.3C4.98749 12.54 4.88749 12.88 4.95749 13.21C5.02749 13.54 5.25749 13.81 5.56749 13.94V13.93ZM31.9375 4H26.9375C26.1075 4 25.3075 4.35 24.7475 4.95L9.50749 21H3.93749C3.53749 21 3.16749 21.24 3.01749 21.62C2.85749 21.99 2.94749 22.42 3.23749 22.71L9.23749 28.71C9.60749 29.08 10.1875 29.1 10.5875 28.77L18.4275 22.21L23.0475 31.45C23.1875 31.74 23.4675 31.94 23.7775 31.99C23.8275 31.99 23.8875 32 23.9375 32C24.1975 32 24.4575 31.9 24.6475 31.71L26.6475 29.71C26.8375 29.52 26.9375 29.27 26.9375 29V15.09L33.7375 9.4C34.4875 8.84 34.9375 7.94 34.9375 7C34.9375 5.35 33.5875 4 31.9375 4ZM32.4975 7.83L25.2975 13.85C25.0675 14.04 24.9375 14.32 24.9375 14.62V28.58L24.2075 29.31L19.6375 20.18C19.4975 19.91 19.2475 19.71 18.9475 19.65C18.8775 19.64 18.8075 19.63 18.7475 19.63C18.5175 19.63 18.2875 19.71 18.1075 19.86L9.99749 26.65L6.34749 23H9.93749C10.2075 23 10.4775 22.89 10.6675 22.69L26.2075 6.32C26.3975 6.12 26.6675 6 26.9375 6H31.9375C32.4875 6 32.9375 6.45 32.9375 7C32.9375 7.31 32.7875 7.61 32.4975 7.83Z\"/>',\n    solid: '<path d=\"M5.56749 13.93L11.2875 16.22L17.1875 10H7.93749C7.66749 10 7.41749 10.11 7.22749 10.29L5.22749 12.29C4.98749 12.53 4.88749 12.87 4.95749 13.2C5.02749 13.53 5.25749 13.8 5.56749 13.93ZM31.9375 4H26.9375C26.1075 4 25.3075 4.35 24.7475 4.95L9.50749 21H3.93749C3.53749 21 3.16749 21.24 3.01749 21.62C2.85749 21.99 2.94749 22.42 3.23749 22.71L9.23749 28.71C9.60749 29.08 10.1875 29.1 10.5875 28.77L18.4275 22.21L23.0475 31.45C23.1875 31.74 23.4675 31.94 23.7775 31.99C23.8275 31.99 23.8875 32 23.9375 32C24.1975 32 24.4575 31.9 24.6475 31.71L26.6475 29.71C26.8375 29.52 26.9375 29.27 26.9375 29V15.09L33.7375 9.4C34.4875 8.84 34.9375 7.94 34.9375 7C34.9375 5.35 33.5875 4 31.9375 4Z\"/>'\n  })];\nexport { H as airplaneIcon, L as airplaneIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "H", "outline", "solid", "airplaneIcon", "airplaneIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/airplane.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"airplane\",H=[\"airplane\",C({outline:'<path d=\"M5.56749 13.93L11.2875 16.22L12.7675 14.66L7.70749 12.64L8.33749 12.01H15.2775L17.1775 10.01H7.93749C7.66749 10.01 7.41749 10.12 7.22749 10.3L5.22749 12.3C4.98749 12.54 4.88749 12.88 4.95749 13.21C5.02749 13.54 5.25749 13.81 5.56749 13.94V13.93ZM31.9375 4H26.9375C26.1075 4 25.3075 4.35 24.7475 4.95L9.50749 21H3.93749C3.53749 21 3.16749 21.24 3.01749 21.62C2.85749 21.99 2.94749 22.42 3.23749 22.71L9.23749 28.71C9.60749 29.08 10.1875 29.1 10.5875 28.77L18.4275 22.21L23.0475 31.45C23.1875 31.74 23.4675 31.94 23.7775 31.99C23.8275 31.99 23.8875 32 23.9375 32C24.1975 32 24.4575 31.9 24.6475 31.71L26.6475 29.71C26.8375 29.52 26.9375 29.27 26.9375 29V15.09L33.7375 9.4C34.4875 8.84 34.9375 7.94 34.9375 7C34.9375 5.35 33.5875 4 31.9375 4ZM32.4975 7.83L25.2975 13.85C25.0675 14.04 24.9375 14.32 24.9375 14.62V28.58L24.2075 29.31L19.6375 20.18C19.4975 19.91 19.2475 19.71 18.9475 19.65C18.8775 19.64 18.8075 19.63 18.7475 19.63C18.5175 19.63 18.2875 19.71 18.1075 19.86L9.99749 26.65L6.34749 23H9.93749C10.2075 23 10.4775 22.89 10.6675 22.69L26.2075 6.32C26.3975 6.12 26.6675 6 26.9375 6H31.9375C32.4875 6 32.9375 6.45 32.9375 7C32.9375 7.31 32.7875 7.61 32.4975 7.83Z\"/>',solid:'<path d=\"M5.56749 13.93L11.2875 16.22L17.1875 10H7.93749C7.66749 10 7.41749 10.11 7.22749 10.29L5.22749 12.29C4.98749 12.53 4.88749 12.87 4.95749 13.2C5.02749 13.53 5.25749 13.8 5.56749 13.93ZM31.9375 4H26.9375C26.1075 4 25.3075 4.35 24.7475 4.95L9.50749 21H3.93749C3.53749 21 3.16749 21.24 3.01749 21.62C2.85749 21.99 2.94749 22.42 3.23749 22.71L9.23749 28.71C9.60749 29.08 10.1875 29.1 10.5875 28.77L18.4275 22.21L23.0475 31.45C23.1875 31.74 23.4675 31.94 23.7775 31.99C23.8275 31.99 23.8875 32 23.9375 32C24.1975 32 24.4575 31.9 24.6475 31.71L26.6475 29.71C26.8375 29.52 26.9375 29.27 26.9375 29V15.09L33.7375 9.4C34.4875 8.84 34.9375 7.94 34.9375 7C34.9375 5.35 33.5875 4 31.9375 4Z\"/>'})];export{H as airplaneIcon,L as airplaneIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,UAAU;EAACC,CAAC,GAAC,CAAC,UAAU,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,6pCAA6pC;IAACC,KAAK,EAAC;EAAmrB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,YAAY,EAACJ,CAAC,IAAIK,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}