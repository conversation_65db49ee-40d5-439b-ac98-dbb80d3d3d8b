{"ast": null, "code": "export * from \"lit-html/directives/if-defined.js\";", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/lit/directives/if-defined.js"], "sourcesContent": ["export*from\"lit-html/directives/if-defined.js\";\n"], "mappings": "AAAA,cAAW,mCAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}