{"ast": null, "code": "import { renderIcon as L } from \"../icon.renderer.js\";\nconst C = \"tent\",\n  e = [\"tent\", L({\n    outline: '<path d=\"M18.9375 21.9999L15.4775 27.9999H13.1575L18.0675 19.4999C18.4175 18.8799 19.4375 18.8799 19.7975 19.4999L24.7075 27.9999H22.3975L18.9375 21.9999Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M20.1004 8.36779L32.9466 29.9999H33.9375C34.4875 29.9999 34.9375 30.4499 34.9375 30.9999C34.9375 31.5499 34.4875 31.9999 33.9375 31.9999H3.9375C3.3875 31.9999 2.9375 31.5499 2.9375 30.9999C2.9375 30.4499 3.3875 29.9999 3.9375 29.9999H4.92851L17.7747 8.36779L16.0775 5.50992C15.7975 5.02992 15.9575 4.41992 16.4275 4.13992C16.8975 3.85992 17.5175 4.00992 17.7975 4.48992L18.9375 6.40961L20.0775 4.48992C20.3575 4.01992 20.9775 3.85992 21.4475 4.13992C21.9175 4.41992 22.0775 5.03992 21.7975 5.50992L20.1004 8.36779ZM30.6208 29.9999H7.25424L18.9375 10.326L30.6208 29.9999Z\"/>',\n    solid: '<path d=\"M33.9375 30H32.9475L20.0975 8.36998L21.7975 5.50998C22.0775 5.02998 21.9175 4.41998 21.4475 4.13998C20.9775 3.85998 20.3575 4.01998 20.0775 4.48998L18.9375 6.40998L17.7975 4.48998C17.5175 4.01998 16.8975 3.85998 16.4275 4.13998C15.9575 4.41998 15.7975 5.03998 16.0775 5.50998L17.7775 8.36998L4.9275 30H3.9375C3.3875 30 2.9375 30.45 2.9375 31C2.9375 31.55 3.3875 32 3.9375 32H33.9375C34.4875 32 34.9375 31.55 34.9375 31C34.9375 30.45 34.4875 30 33.9375 30ZM22.3975 28L18.9375 22L15.4775 28H13.1675L18.0775 19.5C18.4375 18.88 19.4575 18.88 19.8075 19.5L24.7175 28H22.4075H22.3975Z\"/>'\n  })];\nexport { e as tentIcon, C as tentIconName };", "map": {"version": 3, "names": ["renderIcon", "L", "C", "e", "outline", "solid", "tentIcon", "tentIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/tent.js"], "sourcesContent": ["import{renderIcon as L}from\"../icon.renderer.js\";const C=\"tent\",e=[\"tent\",L({outline:'<path d=\"M18.9375 21.9999L15.4775 27.9999H13.1575L18.0675 19.4999C18.4175 18.8799 19.4375 18.8799 19.7975 19.4999L24.7075 27.9999H22.3975L18.9375 21.9999Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M20.1004 8.36779L32.9466 29.9999H33.9375C34.4875 29.9999 34.9375 30.4499 34.9375 30.9999C34.9375 31.5499 34.4875 31.9999 33.9375 31.9999H3.9375C3.3875 31.9999 2.9375 31.5499 2.9375 30.9999C2.9375 30.4499 3.3875 29.9999 3.9375 29.9999H4.92851L17.7747 8.36779L16.0775 5.50992C15.7975 5.02992 15.9575 4.41992 16.4275 4.13992C16.8975 3.85992 17.5175 4.00992 17.7975 4.48992L18.9375 6.40961L20.0775 4.48992C20.3575 4.01992 20.9775 3.85992 21.4475 4.13992C21.9175 4.41992 22.0775 5.03992 21.7975 5.50992L20.1004 8.36779ZM30.6208 29.9999H7.25424L18.9375 10.326L30.6208 29.9999Z\"/>',solid:'<path d=\"M33.9375 30H32.9475L20.0975 8.36998L21.7975 5.50998C22.0775 5.02998 21.9175 4.41998 21.4475 4.13998C20.9775 3.85998 20.3575 4.01998 20.0775 4.48998L18.9375 6.40998L17.7975 4.48998C17.5175 4.01998 16.8975 3.85998 16.4275 4.13998C15.9575 4.41998 15.7975 5.03998 16.0775 5.50998L17.7775 8.36998L4.9275 30H3.9375C3.3875 30 2.9375 30.45 2.9375 31C2.9375 31.55 3.3875 32 3.9375 32H33.9375C34.4875 32 34.9375 31.55 34.9375 31C34.9375 30.45 34.4875 30 33.9375 30ZM22.3975 28L18.9375 22L15.4775 28H13.1675L18.0775 19.5C18.4375 18.88 19.4575 18.88 19.8075 19.5L24.7175 28H22.4075H22.3975Z\"/>'})];export{e as tentIcon,C as tentIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,MAAM;EAACC,CAAC,GAAC,CAAC,MAAM,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,6wBAA6wB;IAACC,KAAK,EAAC;EAAglB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,QAAQ,EAACJ,CAAC,IAAIK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}