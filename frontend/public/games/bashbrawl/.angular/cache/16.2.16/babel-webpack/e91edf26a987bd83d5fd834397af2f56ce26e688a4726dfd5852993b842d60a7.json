{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { html as t } from \"lit\";\nimport { getFlattenedFocusableItems as s } from \"../utils/traversal.js\";\nimport { renderBefore as o, renderAfter as i } from \"../utils/lit.js\";\nimport { ignoreFocus as r } from \"../utils/focus.js\";\nfunction h() {\n  return t => t.addInitializer(t => new e(t));\n}\nclass e {\n  constructor(t) {\n    this.host = t, this.host.addController(this);\n  }\n  get focusableItems() {\n    return s(this.root).filter(t => !t.hasAttribute(\"cds-focus-boundary\") && (this.root.contains(t) || t.closest(\"[cds-focus-trap]\") === this.host));\n  }\n  get root() {\n    return this.host.shadowRoot ? this.host.shadowRoot : this.host;\n  }\n  get styles() {\n    return t`<style cds-focus-style>:host(:focus-within) [cds-focus-boundary],:host(:host:focus-within) [cds-focus-boundary]{display:block!important}</style>`;\n  }\n  boundary(s) {\n    return t`<div @focusin=\"${() => this.focusableItems.at(s)?.focus()}\" test=\"${s}\" cds-focus-boundary tabindex=\"0\" style=\"display:none;position:absolute;width:1px;height:1px;clip:rect(0,0,0,0)\">boundary</div>`;\n  }\n  hostConnected() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.host.updateComplete, r(_this.host) || (o(_this.styles, _this.root), o(_this.boundary(-1), _this.root), i(_this.boundary(0), _this.root), _this.host.setAttribute(\"cds-focus-trap\", \"\"));\n    })();\n  }\n}\nexport { e as InlineFocusTrapController, h as focusTrap };", "map": {"version": 3, "names": ["html", "t", "getFlattenedFocusableItems", "s", "renderBefore", "o", "renderAfter", "i", "ignoreFocus", "r", "h", "addInitializer", "e", "constructor", "host", "addController", "focusableItems", "root", "filter", "hasAttribute", "contains", "closest", "shadowRoot", "styles", "boundary", "at", "focus", "hostConnected", "_this", "_asyncToGenerator", "updateComplete", "setAttribute", "InlineFocusTrapController", "focusTrap"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/inline-focus-trap.controller.js"], "sourcesContent": ["import{html as t}from\"lit\";import{getFlattenedFocusableItems as s}from\"../utils/traversal.js\";import{renderBefore as o,renderAfter as i}from\"../utils/lit.js\";import{ignoreFocus as r}from\"../utils/focus.js\";function h(){return t=>t.addInitializer((t=>new e(t)))}class e{constructor(t){this.host=t,this.host.addController(this)}get focusableItems(){return s(this.root).filter((t=>!t.hasAttribute(\"cds-focus-boundary\")&&(this.root.contains(t)||t.closest(\"[cds-focus-trap]\")===this.host)))}get root(){return this.host.shadowRoot?this.host.shadowRoot:this.host}get styles(){return t`<style cds-focus-style>:host(:focus-within) [cds-focus-boundary],:host(:host:focus-within) [cds-focus-boundary]{display:block!important}</style>`}boundary(s){return t`<div @focusin=\"${()=>this.focusableItems.at(s)?.focus()}\" test=\"${s}\" cds-focus-boundary tabindex=\"0\" style=\"display:none;position:absolute;width:1px;height:1px;clip:rect(0,0,0,0)\">boundary</div>`}async hostConnected(){await this.host.updateComplete,r(this.host)||(o(this.styles,this.root),o(this.boundary(-1),this.root),i(this.boundary(0),this.root),this.host.setAttribute(\"cds-focus-trap\",\"\"))}}export{e as InlineFocusTrapController,h as focusTrap};\n"], "mappings": ";AAAA,SAAOA,IAAI,IAAIC,CAAC,QAAK,KAAK;AAAC,SAAOC,0BAA0B,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,YAAY,IAAIC,CAAC,EAACC,WAAW,IAAIC,CAAC,QAAK,iBAAiB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,OAAOT,CAAC,IAAEA,CAAC,CAACU,cAAc,CAAEV,CAAC,IAAE,IAAIW,CAAC,CAACX,CAAC,CAAE,CAAC;AAAA;AAAC,MAAMW,CAAC;EAACC,WAAWA,CAACZ,CAAC,EAAC;IAAC,IAAI,CAACa,IAAI,GAACb,CAAC,EAAC,IAAI,CAACa,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;EAAA;EAAC,IAAIC,cAAcA,CAAA,EAAE;IAAC,OAAOb,CAAC,CAAC,IAAI,CAACc,IAAI,CAAC,CAACC,MAAM,CAAEjB,CAAC,IAAE,CAACA,CAAC,CAACkB,YAAY,CAAC,oBAAoB,CAAC,KAAG,IAAI,CAACF,IAAI,CAACG,QAAQ,CAACnB,CAAC,CAAC,IAAEA,CAAC,CAACoB,OAAO,CAAC,kBAAkB,CAAC,KAAG,IAAI,CAACP,IAAI,CAAE,CAAC;EAAA;EAAC,IAAIG,IAAIA,CAAA,EAAE;IAAC,OAAO,IAAI,CAACH,IAAI,CAACQ,UAAU,GAAC,IAAI,CAACR,IAAI,CAACQ,UAAU,GAAC,IAAI,CAACR,IAAI;EAAA;EAAC,IAAIS,MAAMA,CAAA,EAAE;IAAC,OAAOtB,CAAE,kJAAiJ;EAAA;EAACuB,QAAQA,CAACrB,CAAC,EAAC;IAAC,OAAOF,CAAE,kBAAiB,MAAI,IAAI,CAACe,cAAc,CAACS,EAAE,CAACtB,CAAC,CAAC,EAAEuB,KAAK,CAAC,CAAE,WAAUvB,CAAE,iIAAgI;EAAA;EAAOwB,aAAaA,CAAA,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAAC,MAAMD,KAAI,CAACd,IAAI,CAACgB,cAAc,EAACrB,CAAC,CAACmB,KAAI,CAACd,IAAI,CAAC,KAAGT,CAAC,CAACuB,KAAI,CAACL,MAAM,EAACK,KAAI,CAACX,IAAI,CAAC,EAACZ,CAAC,CAACuB,KAAI,CAACJ,QAAQ,CAAC,CAAC,CAAC,CAAC,EAACI,KAAI,CAACX,IAAI,CAAC,EAACV,CAAC,CAACqB,KAAI,CAACJ,QAAQ,CAAC,CAAC,CAAC,EAACI,KAAI,CAACX,IAAI,CAAC,EAACW,KAAI,CAACd,IAAI,CAACiB,YAAY,CAAC,gBAAgB,EAAC,EAAE,CAAC,CAAC;IAAA;EAAA;AAAC;AAAC,SAAOnB,CAAC,IAAIoB,yBAAyB,EAACtB,CAAC,IAAIuB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}