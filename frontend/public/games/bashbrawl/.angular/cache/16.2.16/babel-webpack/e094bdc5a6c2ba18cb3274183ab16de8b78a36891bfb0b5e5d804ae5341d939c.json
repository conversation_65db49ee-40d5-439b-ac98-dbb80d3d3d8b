{"ast": null, "code": "var e;\n!function (e) {\n  e.ready = \"ready\", e.active = \"active\", e.start = \"starting\", e.end = \"done\";\n}(e || (e = {}));\nconst t = \"hidden\",\n  a = \"reverse\",\n  r = \"-reverse\",\n  n = 300,\n  i = \"linear\",\n  s = \"_cds-animation-status\";\nexport { e as AnimationStatus, t as CLARITY_MOTION_ENTER_LEAVE_PROPERTY, n as CLARITY_MOTION_FALLBACK_DURATION_IN_MS, i as CLARITY_MOTION_FALLBACK_EASING, a as CLARITY_MOTION_REVERSE_ANIMATION_LABEL, r as C<PERSON><PERSON><PERSON>_MOTION_REVERSE_ANIMATION_SUFFIX, s as PRIVATE_ANIMATION_STATUS_ATTR_NAME };", "map": {"version": 3, "names": ["e", "ready", "active", "start", "end", "t", "a", "r", "n", "i", "s", "AnimationStatus", "CLARITY_MOTION_ENTER_LEAVE_PROPERTY", "CLARITY_MOTION_FALLBACK_DURATION_IN_MS", "CLARITY_MOTION_FALLBACK_EASING", "CLARITY_MOTION_REVERSE_ANIMATION_LABEL", "CLARITY_MOTION_REVERSE_ANIMATION_SUFFIX", "PRIVATE_ANIMATION_STATUS_ATTR_NAME"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/motion/interfaces.js"], "sourcesContent": ["var e;!function(e){e.ready=\"ready\",e.active=\"active\",e.start=\"starting\",e.end=\"done\"}(e||(e={}));const t=\"hidden\",a=\"reverse\",r=\"-reverse\",n=300,i=\"linear\",s=\"_cds-animation-status\";export{e as AnimationStatus,t as CLARITY_MOTION_ENTER_LEAVE_PROPERTY,n as CLARITY_MOTION_FALLBACK_DURATION_IN_MS,i as CLARITY_MOTION_FALLBACK_EASING,a as CLARITY_MOTION_REVERSE_ANIMATION_LABEL,r as C<PERSON><PERSON><PERSON>_MOTION_REVERSE_ANIMATION_SUFFIX,s as PRIVA<PERSON>_ANIMATION_STATUS_ATTR_NAME};\n"], "mappings": "AAAA,IAAIA,CAAC;AAAC,CAAC,UAASA,CAAC,EAAC;EAACA,CAAC,CAACC,KAAK,GAAC,OAAO,EAACD,CAAC,CAACE,MAAM,GAAC,QAAQ,EAACF,CAAC,CAACG,KAAK,GAAC,UAAU,EAACH,CAAC,CAACI,GAAG,GAAC,MAAM;AAAA,CAAC,CAACJ,CAAC,KAAGA,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC;AAAC,MAAMK,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,SAAS;EAACC,CAAC,GAAC,UAAU;EAACC,CAAC,GAAC,GAAG;EAACC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,uBAAuB;AAAC,SAAOV,CAAC,IAAIW,eAAe,EAACN,CAAC,IAAIO,mCAAmC,EAACJ,CAAC,IAAIK,sCAAsC,EAACJ,CAAC,IAAIK,8BAA8B,EAACR,CAAC,IAAIS,sCAAsC,EAACR,CAAC,IAAIS,uCAAuC,EAACN,CAAC,IAAIO,kCAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}