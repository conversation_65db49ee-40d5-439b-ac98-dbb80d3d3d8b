{"ast": null, "code": "import { KeyCodeService as e } from \"../services/keycodes.service.js\";\nfunction r(r, t) {\n  return e.getCode(t) === r.key;\n}\nfunction t(e, t, n) {\n  r(t, e) && n();\n}\nfunction n(e, t, n) {\n  e.filter(e => r(t, e)).length > 0 && n();\n}\nfunction o(e, t, n) {\n  (function (e, r) {\n    const t = i(e);\n    return 0 === t.length || t.reduce((e, t) => e && function (e, r) {\n      switch (r) {\n        case \"ctrl\":\n          return e.ctrlKey;\n        case \"alt\":\n          return e.altKey;\n        case \"shift\":\n          return e.shiftKey;\n        case \"cmd\":\n        case \"win\":\n        case \"meta\":\n          return e.metaKey;\n        default:\n          return !1;\n      }\n    }(r, t), !0);\n  })(e, t) && function (e, t) {\n    const n = u(e);\n    return 0 === n.length || n.reduce((e, n) => e && r(t, n), !0);\n  }(e, t) && n();\n}\nconst c = [\"ctrl\", \"alt\", \"shift\", \"meta\", \"cmd\", \"win\"];\nfunction i(e) {\n  return e.split(\"+\").filter(e => c.indexOf(e) > -1);\n}\nfunction u(e) {\n  return e.split(\"+\").filter(e => c.indexOf(e) < 0);\n}\nfunction f(e) {\n  return e.code === w.ArrowUp || e.code === w.ArrowDown || e.code === w.ArrowLeft || e.code === w.ArrowRight || e.code === w.End || e.code === w.Home || e.code === w.PageUp || e.code === w.PageDown;\n}\nvar w;\n!function (e) {\n  e.ArrowUp = \"ArrowUp\", e.ArrowDown = \"ArrowDown\", e.ArrowLeft = \"ArrowLeft\", e.ArrowRight = \"ArrowRight\", e.End = \"End\", e.Home = \"Home\", e.PageUp = \"PageUp\", e.PageDown = \"PageDown\";\n}(w || (w = {}));\nexport { w as KeyNavigationCode, i as getModifierKeysFromKeyCombo, r as keyWasEvented, n as onAnyKey, t as onKey, o as onKeyCombo, u as removeModifierKeysFromKeyCombo, f as validKeyNavigationCode };", "map": {"version": 3, "names": ["KeyCodeService", "e", "r", "t", "getCode", "key", "n", "filter", "length", "o", "i", "reduce", "ctrl<PERSON>ey", "altKey", "shift<PERSON>ey", "metaKey", "u", "c", "split", "indexOf", "f", "code", "w", "ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight", "End", "Home", "PageUp", "PageDown", "KeyNavigationCode", "getModifierKeysFromKeyCombo", "keyWasEvented", "onAnyKey", "onKey", "onKeyCombo", "removeModifierKeysFromKeyCombo", "validKeyNavigationCode"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/keycodes.js"], "sourcesContent": ["import{KeyCodeService as e}from\"../services/keycodes.service.js\";function r(r,t){return e.getCode(t)===r.key}function t(e,t,n){r(t,e)&&n()}function n(e,t,n){e.filter((e=>r(t,e))).length>0&&n()}function o(e,t,n){(function(e,r){const t=i(e);return 0===t.length||t.reduce(((e,t)=>e&&function(e,r){switch(r){case\"ctrl\":return e.ctrlKey;case\"alt\":return e.altKey;case\"shift\":return e.shift<PERSON>ey;case\"cmd\":case\"win\":case\"meta\":return e.metaKey;default:return!1}}(r,t)),!0)})(e,t)&&function(e,t){const n=u(e);return 0===n.length||n.reduce(((e,n)=>e&&r(t,n)),!0)}(e,t)&&n()}const c=[\"ctrl\",\"alt\",\"shift\",\"meta\",\"cmd\",\"win\"];function i(e){return e.split(\"+\").filter((e=>c.indexOf(e)>-1))}function u(e){return e.split(\"+\").filter((e=>c.indexOf(e)<0))}function f(e){return e.code===w.ArrowUp||e.code===w.ArrowDown||e.code===w.ArrowLeft||e.code===w.ArrowRight||e.code===w.End||e.code===w.Home||e.code===w.PageUp||e.code===w.PageDown}var w;!function(e){e.ArrowUp=\"ArrowUp\",e.ArrowDown=\"ArrowDown\",e.ArrowLeft=\"ArrowLeft\",e.ArrowRight=\"ArrowRight\",e.End=\"End\",e.Home=\"Home\",e.PageUp=\"PageUp\",e.PageDown=\"PageDown\"}(w||(w={}));export{w as KeyNavigationCode,i as getModifierKeysFromKeyCombo,r as keyWasEvented,n as onAnyKey,t as onKey,o as onKeyCombo,u as removeModifierKeysFromKeyCombo,f as validKeyNavigationCode};\n"], "mappings": "AAAA,SAAOA,cAAc,IAAIC,CAAC,QAAK,iCAAiC;AAAC,SAASC,CAACA,CAACA,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOF,CAAC,CAACG,OAAO,CAACD,CAAC,CAAC,KAAGD,CAAC,CAACG,GAAG;AAAA;AAAC,SAASF,CAACA,CAACF,CAAC,EAACE,CAAC,EAACG,CAAC,EAAC;EAACJ,CAAC,CAACC,CAAC,EAACF,CAAC,CAAC,IAAEK,CAAC,CAAC,CAAC;AAAA;AAAC,SAASA,CAACA,CAACL,CAAC,EAACE,CAAC,EAACG,CAAC,EAAC;EAACL,CAAC,CAACM,MAAM,CAAEN,CAAC,IAAEC,CAAC,CAACC,CAAC,EAACF,CAAC,CAAE,CAAC,CAACO,MAAM,GAAC,CAAC,IAAEF,CAAC,CAAC,CAAC;AAAA;AAAC,SAASG,CAACA,CAACR,CAAC,EAACE,CAAC,EAACG,CAAC,EAAC;EAAC,CAAC,UAASL,CAAC,EAACC,CAAC,EAAC;IAAC,MAAMC,CAAC,GAACO,CAAC,CAACT,CAAC,CAAC;IAAC,OAAO,CAAC,KAAGE,CAAC,CAACK,MAAM,IAAEL,CAAC,CAACQ,MAAM,CAAE,CAACV,CAAC,EAACE,CAAC,KAAGF,CAAC,IAAE,UAASA,CAAC,EAACC,CAAC,EAAC;MAAC,QAAOA,CAAC;QAAE,KAAI,MAAM;UAAC,OAAOD,CAAC,CAACW,OAAO;QAAC,KAAI,KAAK;UAAC,OAAOX,CAAC,CAACY,MAAM;QAAC,KAAI,OAAO;UAAC,OAAOZ,CAAC,CAACa,QAAQ;QAAC,KAAI,KAAK;QAAC,KAAI,KAAK;QAAC,KAAI,MAAM;UAAC,OAAOb,CAAC,CAACc,OAAO;QAAC;UAAQ,OAAM,CAAC,CAAC;MAAA;IAAC,CAAC,CAACb,CAAC,EAACC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAA,CAAC,EAAEF,CAAC,EAACE,CAAC,CAAC,IAAE,UAASF,CAAC,EAACE,CAAC,EAAC;IAAC,MAAMG,CAAC,GAACU,CAAC,CAACf,CAAC,CAAC;IAAC,OAAO,CAAC,KAAGK,CAAC,CAACE,MAAM,IAAEF,CAAC,CAACK,MAAM,CAAE,CAACV,CAAC,EAACK,CAAC,KAAGL,CAAC,IAAEC,CAAC,CAACC,CAAC,EAACG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAA,CAAC,CAACL,CAAC,EAACE,CAAC,CAAC,IAAEG,CAAC,CAAC,CAAC;AAAA;AAAC,MAAMW,CAAC,GAAC,CAAC,MAAM,EAAC,KAAK,EAAC,OAAO,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,CAAC;AAAC,SAASP,CAACA,CAACT,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACiB,KAAK,CAAC,GAAG,CAAC,CAACX,MAAM,CAAEN,CAAC,IAAEgB,CAAC,CAACE,OAAO,CAAClB,CAAC,CAAC,GAAC,CAAC,CAAE,CAAC;AAAA;AAAC,SAASe,CAACA,CAACf,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACiB,KAAK,CAAC,GAAG,CAAC,CAACX,MAAM,CAAEN,CAAC,IAAEgB,CAAC,CAACE,OAAO,CAAClB,CAAC,CAAC,GAAC,CAAE,CAAC;AAAA;AAAC,SAASmB,CAACA,CAACnB,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACoB,IAAI,KAAGC,CAAC,CAACC,OAAO,IAAEtB,CAAC,CAACoB,IAAI,KAAGC,CAAC,CAACE,SAAS,IAAEvB,CAAC,CAACoB,IAAI,KAAGC,CAAC,CAACG,SAAS,IAAExB,CAAC,CAACoB,IAAI,KAAGC,CAAC,CAACI,UAAU,IAAEzB,CAAC,CAACoB,IAAI,KAAGC,CAAC,CAACK,GAAG,IAAE1B,CAAC,CAACoB,IAAI,KAAGC,CAAC,CAACM,IAAI,IAAE3B,CAAC,CAACoB,IAAI,KAAGC,CAAC,CAACO,MAAM,IAAE5B,CAAC,CAACoB,IAAI,KAAGC,CAAC,CAACQ,QAAQ;AAAA;AAAC,IAAIR,CAAC;AAAC,CAAC,UAASrB,CAAC,EAAC;EAACA,CAAC,CAACsB,OAAO,GAAC,SAAS,EAACtB,CAAC,CAACuB,SAAS,GAAC,WAAW,EAACvB,CAAC,CAACwB,SAAS,GAAC,WAAW,EAACxB,CAAC,CAACyB,UAAU,GAAC,YAAY,EAACzB,CAAC,CAAC0B,GAAG,GAAC,KAAK,EAAC1B,CAAC,CAAC2B,IAAI,GAAC,MAAM,EAAC3B,CAAC,CAAC4B,MAAM,GAAC,QAAQ,EAAC5B,CAAC,CAAC6B,QAAQ,GAAC,UAAU;AAAA,CAAC,CAACR,CAAC,KAAGA,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC;AAAC,SAAOA,CAAC,IAAIS,iBAAiB,EAACrB,CAAC,IAAIsB,2BAA2B,EAAC9B,CAAC,IAAI+B,aAAa,EAAC3B,CAAC,IAAI4B,QAAQ,EAAC/B,CAAC,IAAIgC,KAAK,EAAC1B,CAAC,IAAI2B,UAAU,EAACpB,CAAC,IAAIqB,8BAA8B,EAACjB,CAAC,IAAIkB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}