{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { getOffesetDifference as t } from \"../utils/math.js\";\nfunction e() {\n  return t => t.addInitializer(t => new s(t));\n}\nclass s {\n  constructor(t) {\n    this.host = t, this.moveHandler = this.move.bind(this), this.endHandler = this.end.bind(this), this.host.addController(this);\n  }\n  hostConnected() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.host.updateComplete, _this.host.addEventListener(\"pointerdown\", t => _this.start(t), {\n        passive: !0\n      });\n    })();\n  }\n  start(t) {\n    t.composedPath().find(t => t === this.host) && (this.startPosition = {\n      x: t.clientX,\n      y: t.clientY\n    }, document.addEventListener(\"pointerup\", this.endHandler, {\n      passive: !0\n    }), document.addEventListener(\"pointermove\", this.moveHandler, {\n      passive: !0\n    }), this.host.dispatchEvent(new CustomEvent(\"cdsTouchStart\", {\n      detail: {\n        ...this.startPosition\n      }\n    })));\n  }\n  end(t) {\n    if (this.startPosition) {\n      const e = this.getCoordinates(t);\n      document.removeEventListener(\"pointerup\", this.endHandler, !1), document.removeEventListener(\"pointermove\", this.moveHandler, !1), this.host.dispatchEvent(new CustomEvent(\"cdsTouchEnd\", {\n        detail: e\n      }));\n    }\n  }\n  move(t) {\n    requestAnimationFrame(() => {\n      const e = this.getCoordinates(t);\n      this.startPosition = {\n        x: t.clientX,\n        y: t.clientY\n      }, this.host.dispatchEvent(new CustomEvent(\"cdsTouchMove\", {\n        detail: e\n      }));\n    });\n  }\n  getCoordinates(e) {\n    return {\n      x: e.clientX,\n      y: e.clientY,\n      offsetX: t(this.startPosition.x, e.clientX),\n      offsetY: t(this.startPosition.y, e.clientY)\n    };\n  }\n}\nexport { s as TouchController, e as touch };", "map": {"version": 3, "names": ["getOffesetDifference", "t", "e", "addInitializer", "s", "constructor", "host", "<PERSON><PERSON><PERSON><PERSON>", "move", "bind", "end<PERSON><PERSON><PERSON>", "end", "addController", "hostConnected", "_this", "_asyncToGenerator", "updateComplete", "addEventListener", "start", "passive", "<PERSON><PERSON><PERSON>", "find", "startPosition", "x", "clientX", "y", "clientY", "document", "dispatchEvent", "CustomEvent", "detail", "getCoordinates", "removeEventListener", "requestAnimationFrame", "offsetX", "offsetY", "TouchController", "touch"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/touch.controller.js"], "sourcesContent": ["import{getOffesetDifference as t}from\"../utils/math.js\";function e(){return t=>t.addInitializer((t=>new s(t)))}class s{constructor(t){this.host=t,this.moveHandler=this.move.bind(this),this.endHandler=this.end.bind(this),this.host.addController(this)}async hostConnected(){await this.host.updateComplete,this.host.addEventListener(\"pointerdown\",(t=>this.start(t)),{passive:!0})}start(t){t.composedPath().find((t=>t===this.host))&&(this.startPosition={x:t.clientX,y:t.clientY},document.addEventListener(\"pointerup\",this.endHandler,{passive:!0}),document.addEventListener(\"pointermove\",this.moveHandler,{passive:!0}),this.host.dispatchEvent(new CustomEvent(\"cdsTouchStart\",{detail:{...this.startPosition}})))}end(t){if(this.startPosition){const e=this.getCoordinates(t);document.removeEventListener(\"pointerup\",this.endHandler,!1),document.removeEventListener(\"pointermove\",this.moveHandler,!1),this.host.dispatchEvent(new CustomEvent(\"cdsTouchEnd\",{detail:e}))}}move(t){requestAnimationFrame((()=>{const e=this.getCoordinates(t);this.startPosition={x:t.clientX,y:t.clientY},this.host.dispatchEvent(new CustomEvent(\"cdsTouchMove\",{detail:e}))}))}getCoordinates(e){return{x:e.clientX,y:e.clientY,offsetX:t(this.startPosition.x,e.clientX),offsetY:t(this.startPosition.y,e.clientY)}}}export{s as TouchController,e as touch};\n"], "mappings": ";AAAA,SAAOA,oBAAoB,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,OAAOD,CAAC,IAAEA,CAAC,CAACE,cAAc,CAAEF,CAAC,IAAE,IAAIG,CAAC,CAACH,CAAC,CAAE,CAAC;AAAA;AAAC,MAAMG,CAAC;EAACC,WAAWA,CAACJ,CAAC,EAAC;IAAC,IAAI,CAACK,IAAI,GAACL,CAAC,EAAC,IAAI,CAACM,WAAW,GAAC,IAAI,CAACC,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC,EAAC,IAAI,CAACC,UAAU,GAAC,IAAI,CAACC,GAAG,CAACF,IAAI,CAAC,IAAI,CAAC,EAAC,IAAI,CAACH,IAAI,CAACM,aAAa,CAAC,IAAI,CAAC;EAAA;EAAOC,aAAaA,CAAA,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAAC,MAAMD,KAAI,CAACR,IAAI,CAACU,cAAc,EAACF,KAAI,CAACR,IAAI,CAACW,gBAAgB,CAAC,aAAa,EAAEhB,CAAC,IAAEa,KAAI,CAACI,KAAK,CAACjB,CAAC,CAAC,EAAE;QAACkB,OAAO,EAAC,CAAC;MAAC,CAAC,CAAC;IAAA;EAAA;EAACD,KAAKA,CAACjB,CAAC,EAAC;IAACA,CAAC,CAACmB,YAAY,CAAC,CAAC,CAACC,IAAI,CAAEpB,CAAC,IAAEA,CAAC,KAAG,IAAI,CAACK,IAAK,CAAC,KAAG,IAAI,CAACgB,aAAa,GAAC;MAACC,CAAC,EAACtB,CAAC,CAACuB,OAAO;MAACC,CAAC,EAACxB,CAAC,CAACyB;IAAO,CAAC,EAACC,QAAQ,CAACV,gBAAgB,CAAC,WAAW,EAAC,IAAI,CAACP,UAAU,EAAC;MAACS,OAAO,EAAC,CAAC;IAAC,CAAC,CAAC,EAACQ,QAAQ,CAACV,gBAAgB,CAAC,aAAa,EAAC,IAAI,CAACV,WAAW,EAAC;MAACY,OAAO,EAAC,CAAC;IAAC,CAAC,CAAC,EAAC,IAAI,CAACb,IAAI,CAACsB,aAAa,CAAC,IAAIC,WAAW,CAAC,eAAe,EAAC;MAACC,MAAM,EAAC;QAAC,GAAG,IAAI,CAACR;MAAa;IAAC,CAAC,CAAC,CAAC,CAAC;EAAA;EAACX,GAAGA,CAACV,CAAC,EAAC;IAAC,IAAG,IAAI,CAACqB,aAAa,EAAC;MAAC,MAAMpB,CAAC,GAAC,IAAI,CAAC6B,cAAc,CAAC9B,CAAC,CAAC;MAAC0B,QAAQ,CAACK,mBAAmB,CAAC,WAAW,EAAC,IAAI,CAACtB,UAAU,EAAC,CAAC,CAAC,CAAC,EAACiB,QAAQ,CAACK,mBAAmB,CAAC,aAAa,EAAC,IAAI,CAACzB,WAAW,EAAC,CAAC,CAAC,CAAC,EAAC,IAAI,CAACD,IAAI,CAACsB,aAAa,CAAC,IAAIC,WAAW,CAAC,aAAa,EAAC;QAACC,MAAM,EAAC5B;MAAC,CAAC,CAAC,CAAC;IAAA;EAAC;EAACM,IAAIA,CAACP,CAAC,EAAC;IAACgC,qBAAqB,CAAE,MAAI;MAAC,MAAM/B,CAAC,GAAC,IAAI,CAAC6B,cAAc,CAAC9B,CAAC,CAAC;MAAC,IAAI,CAACqB,aAAa,GAAC;QAACC,CAAC,EAACtB,CAAC,CAACuB,OAAO;QAACC,CAAC,EAACxB,CAAC,CAACyB;MAAO,CAAC,EAAC,IAAI,CAACpB,IAAI,CAACsB,aAAa,CAAC,IAAIC,WAAW,CAAC,cAAc,EAAC;QAACC,MAAM,EAAC5B;MAAC,CAAC,CAAC,CAAC;IAAA,CAAE,CAAC;EAAA;EAAC6B,cAAcA,CAAC7B,CAAC,EAAC;IAAC,OAAM;MAACqB,CAAC,EAACrB,CAAC,CAACsB,OAAO;MAACC,CAAC,EAACvB,CAAC,CAACwB,OAAO;MAACQ,OAAO,EAACjC,CAAC,CAAC,IAAI,CAACqB,aAAa,CAACC,CAAC,EAACrB,CAAC,CAACsB,OAAO,CAAC;MAACW,OAAO,EAAClC,CAAC,CAAC,IAAI,CAACqB,aAAa,CAACG,CAAC,EAACvB,CAAC,CAACwB,OAAO;IAAC,CAAC;EAAA;AAAC;AAAC,SAAOtB,CAAC,IAAIgC,eAAe,EAAClC,CAAC,IAAImC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}