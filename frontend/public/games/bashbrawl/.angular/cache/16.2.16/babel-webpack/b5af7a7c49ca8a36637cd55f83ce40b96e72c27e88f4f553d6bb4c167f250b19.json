{"ast": null, "code": "import { renderIcon as H } from \"../icon.renderer.js\";\nconst V = \"keyboard\",\n  M = [\"keyboard\", H({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 8H4C2.89543 8 2 8.89543 2 10V26C2 27.1046 2.89543 28 4 28H32C33.1046 28 34 27.1046 34 26V10C34 8.89543 33.1046 8 32 8ZM27 13H29V15H27V13ZM27 17H29V19H27V17ZM7 22H9V24H7V22ZM11.13 22H24.88V24H11.13V22ZM28.94 22H27V24H28.94V22ZM25 17H23V19H25V17ZM19 17H21V19H19V17ZM17 17H15V19H17V17ZM11 17H13V19H11V17ZM9 17H7V19H9V17ZM25 13H23V15H25V13ZM19 13H21V15H19V13ZM17 13H15V15H17V13ZM11 13H13V15H11V13ZM9 13H7V15H9V13ZM4 26H32V10H4V26Z\"/>',\n    solid: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4 8H32C33.1046 8 34 8.89543 34 10V26C34 27.1046 33.1046 28 32 28H4C2.89543 28 2 27.1046 2 26V10C2 8.89543 2.89543 8 4 8ZM21 13H19V15H21V13ZM19 17H21V19H19V17ZM17 13H15V15H17V13ZM15 17H17V19H15V17ZM13 13H11V15H13V13ZM9 24H7V22H9V24ZM7 19H9V17H7V19ZM9 15H7V13H9V15ZM13 17H11V19H13V17ZM24.88 24H11.13V22H24.88V24ZM23 19H25V17H23V19ZM25 15H23V13H25V15ZM27 24H28.94V22H27V24ZM29 19H27V17H29V19ZM27 15H29V13H27V15Z\"/>'\n  })];\nexport { M as keyboardIcon, V as keyboardIconName };", "map": {"version": 3, "names": ["renderIcon", "H", "V", "M", "outline", "solid", "keyboardIcon", "keyboardIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/keyboard.js"], "sourcesContent": ["import{renderIcon as H}from\"../icon.renderer.js\";const V=\"keyboard\",M=[\"keyboard\",H({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 8H4C2.89543 8 2 8.89543 2 10V26C2 27.1046 2.89543 28 4 28H32C33.1046 28 34 27.1046 34 26V10C34 8.89543 33.1046 8 32 8ZM27 13H29V15H27V13ZM27 17H29V19H27V17ZM7 22H9V24H7V22ZM11.13 22H24.88V24H11.13V22ZM28.94 22H27V24H28.94V22ZM25 17H23V19H25V17ZM19 17H21V19H19V17ZM17 17H15V19H17V17ZM11 17H13V19H11V17ZM9 17H7V19H9V17ZM25 13H23V15H25V13ZM19 13H21V15H19V13ZM17 13H15V15H17V13ZM11 13H13V15H11V13ZM9 13H7V15H9V13ZM4 26H32V10H4V26Z\"/>',solid:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4 8H32C33.1046 8 34 8.89543 34 10V26C34 27.1046 33.1046 28 32 28H4C2.89543 28 2 27.1046 2 26V10C2 8.89543 2.89543 8 4 8ZM21 13H19V15H21V13ZM19 17H21V19H19V17ZM17 13H15V15H17V13ZM15 17H17V19H15V17ZM13 13H11V15H13V13ZM9 24H7V22H9V24ZM7 19H9V17H7V19ZM9 15H7V13H9V15ZM13 17H11V19H13V17ZM24.88 24H11.13V22H24.88V24ZM23 19H25V17H23V19ZM25 15H23V13H25V15ZM27 24H28.94V22H27V24ZM29 19H27V17H29V19ZM27 15H29V13H27V15Z\"/>'})];export{M as keyboardIcon,V as keyboardIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,UAAU;EAACC,CAAC,GAAC,CAAC,UAAU,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,oeAAoe;IAACC,KAAK,EAAC;EAA+c,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,YAAY,EAACJ,CAAC,IAAIK,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}