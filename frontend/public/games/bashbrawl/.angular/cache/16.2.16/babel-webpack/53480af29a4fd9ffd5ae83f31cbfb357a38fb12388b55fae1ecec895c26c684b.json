{"ast": null, "code": "import { isString as t, isNilOrEmpty as i, isNumericString as s } from \"./identity.js\";\nimport { updateElementStyles as e, pxToRem as n } from \"./css.js\";\nfunction o(t) {\n  return [\"xxs\", \"xs\", \"sm\", \"md\", \"lg\", \"xl\", \"xxl\"].indexOf(t) > -1;\n}\nfunction r(r, x) {\n  if (!t(x) || i(x) || o(x)) e(r, [\"width\", \"\"], [\"height\", \"\"]);else if (s(x)) {\n    const t = n(parseInt(x));\n    e(r, [\"width\", t], [\"height\", t]);\n  }\n}\nexport { o as isTshirtSize, r as updateEquilateralSizeStyles };", "map": {"version": 3, "names": ["isString", "t", "isNilOrEmpty", "i", "isNumericString", "s", "updateElementStyles", "e", "pxToRem", "n", "o", "indexOf", "r", "x", "parseInt", "isTshirtSize", "updateEquilateralSizeStyles"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/size.js"], "sourcesContent": ["import{isString as t,isNilOrEmpty as i,isNumericString as s}from\"./identity.js\";import{updateElementStyles as e,pxToRem as n}from\"./css.js\";function o(t){return[\"xxs\",\"xs\",\"sm\",\"md\",\"lg\",\"xl\",\"xxl\"].indexOf(t)>-1}function r(r,x){if(!t(x)||i(x)||o(x))e(r,[\"width\",\"\"],[\"height\",\"\"]);else if(s(x)){const t=n(parseInt(x));e(r,[\"width\",t],[\"height\",t])}}export{o as isTshirtSize,r as updateEquilateralSizeStyles};\n"], "mappings": "AAAA,SAAOA,QAAQ,IAAIC,CAAC,EAACC,YAAY,IAAIC,CAAC,EAACC,eAAe,IAAIC,CAAC,QAAK,eAAe;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,QAAK,UAAU;AAAC,SAASC,CAACA,CAACT,CAAC,EAAC;EAAC,OAAM,CAAC,KAAK,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,CAAC,CAACU,OAAO,CAACV,CAAC,CAAC,GAAC,CAAC,CAAC;AAAA;AAAC,SAASW,CAACA,CAACA,CAAC,EAACC,CAAC,EAAC;EAAC,IAAG,CAACZ,CAAC,CAACY,CAAC,CAAC,IAAEV,CAAC,CAACU,CAAC,CAAC,IAAEH,CAAC,CAACG,CAAC,CAAC,EAACN,CAAC,CAACK,CAAC,EAAC,CAAC,OAAO,EAAC,EAAE,CAAC,EAAC,CAAC,QAAQ,EAAC,EAAE,CAAC,CAAC,CAAC,KAAK,IAAGP,CAAC,CAACQ,CAAC,CAAC,EAAC;IAAC,MAAMZ,CAAC,GAACQ,CAAC,CAACK,QAAQ,CAACD,CAAC,CAAC,CAAC;IAACN,CAAC,CAACK,CAAC,EAAC,CAAC,OAAO,EAACX,CAAC,CAAC,EAAC,CAAC,QAAQ,EAACA,CAAC,CAAC,CAAC;EAAA;AAAC;AAAC,SAAOS,CAAC,IAAIK,YAAY,EAACH,CAAC,IAAII,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}