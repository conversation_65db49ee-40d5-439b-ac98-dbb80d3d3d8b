{"ast": null, "code": "function n(n, r, t = n => n, o = \"\") {\n  if (!r) return o;\n  const e = n[t(r)];\n  return void 0 !== e ? e : o;\n}\nexport { n as getEnumValueFromStringKey };", "map": {"version": 3, "names": ["n", "r", "t", "o", "e", "getEnumValueFromStringKey"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/enum.js"], "sourcesContent": ["function n(n,r,t=(n=>n),o=\"\"){if(!r)return o;const e=n[t(r)];return void 0!==e?e:o}export{n as getEnumValueFromStringKey};\n"], "mappings": "AAAA,SAASA,CAACA,CAACA,CAAC,EAACC,CAAC,EAACC,CAAC,GAAEF,CAAC,IAAEA,CAAE,EAACG,CAAC,GAAC,EAAE,EAAC;EAAC,IAAG,CAACF,CAAC,EAAC,OAAOE,CAAC;EAAC,MAAMC,CAAC,GAACJ,CAAC,CAACE,CAAC,CAACD,CAAC,CAAC,CAAC;EAAC,OAAO,KAAK,CAAC,KAAGG,CAAC,GAACA,CAAC,GAACD,CAAC;AAAA;AAAC,SAAOH,CAAC,IAAIK,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}