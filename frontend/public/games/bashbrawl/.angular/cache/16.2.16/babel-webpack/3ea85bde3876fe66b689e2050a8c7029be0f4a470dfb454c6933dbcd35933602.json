{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nimport { decorateProperty } from './base.js';\n/**\n * A property decorator that converts a class property into a getter that\n * executes a querySelector on the element's renderRoot.\n *\n * @param selector A DOMString containing one or more selectors to match.\n * @param cache An optional boolean which when true performs the DOM query only\n *     once and caches the result.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector\n *\n * ```ts\n * class MyElement {\n *   @query('#first')\n *   first: HTMLDivElement;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n * ```\n * @category Decorator\n */\nexport function query(selector, cache) {\n  return decorateProperty({\n    descriptor: name => {\n      const descriptor = {\n        get() {\n          var _a, _b;\n          return (_b = (_a = this.renderRoot) === null || _a === void 0 ? void 0 : _a.querySelector(selector)) !== null && _b !== void 0 ? _b : null;\n        },\n        enumerable: true,\n        configurable: true\n      };\n      if (cache) {\n        const key = typeof name === 'symbol' ? Symbol() : `__${name}`;\n        descriptor.get = function () {\n          var _a, _b;\n          if (this[key] === undefined) {\n            this[key] = (_b = (_a = this.renderRoot) === null || _a === void 0 ? void 0 : _a.querySelector(selector)) !== null && _b !== void 0 ? _b : null;\n          }\n          return this[key];\n        };\n      }\n      return descriptor;\n    }\n  });\n}", "map": {"version": 3, "names": ["decorateProperty", "query", "selector", "cache", "descriptor", "name", "get", "_a", "_b", "renderRoot", "querySelector", "enumerable", "configurable", "key", "Symbol", "undefined"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@lit/reactive-element/development/decorators/query.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nimport { decorateProperty } from './base.js';\n/**\n * A property decorator that converts a class property into a getter that\n * executes a querySelector on the element's renderRoot.\n *\n * @param selector A DOMString containing one or more selectors to match.\n * @param cache An optional boolean which when true performs the DOM query only\n *     once and caches the result.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelector\n *\n * ```ts\n * class MyElement {\n *   @query('#first')\n *   first: HTMLDivElement;\n *\n *   render() {\n *     return html`\n *       <div id=\"first\"></div>\n *       <div id=\"second\"></div>\n *     `;\n *   }\n * }\n * ```\n * @category Decorator\n */\nexport function query(selector, cache) {\n    return decorateProperty({\n        descriptor: (name) => {\n            const descriptor = {\n                get() {\n                    var _a, _b;\n                    return (_b = (_a = this.renderRoot) === null || _a === void 0 ? void 0 : _a.querySelector(selector)) !== null && _b !== void 0 ? _b : null;\n                },\n                enumerable: true,\n                configurable: true,\n            };\n            if (cache) {\n                const key = typeof name === 'symbol' ? Symbol() : `__${name}`;\n                descriptor.get = function () {\n                    var _a, _b;\n                    if (this[key] === undefined) {\n                        this[key] = (_b = (_a = this.renderRoot) === null || _a === void 0 ? void 0 : _a.querySelector(selector)) !== null && _b !== void 0 ? _b : null;\n                    }\n                    return this[key];\n                };\n            }\n            return descriptor;\n        },\n    });\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,gBAAgB,QAAQ,WAAW;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACC,QAAQ,EAAEC,KAAK,EAAE;EACnC,OAAOH,gBAAgB,CAAC;IACpBI,UAAU,EAAGC,IAAI,IAAK;MAClB,MAAMD,UAAU,GAAG;QACfE,GAAGA,CAAA,EAAG;UACF,IAAIC,EAAE,EAAEC,EAAE;UACV,OAAO,CAACA,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAACE,UAAU,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,aAAa,CAACR,QAAQ,CAAC,MAAM,IAAI,IAAIM,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;QAC9I,CAAC;QACDG,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE;MAClB,CAAC;MACD,IAAIT,KAAK,EAAE;QACP,MAAMU,GAAG,GAAG,OAAOR,IAAI,KAAK,QAAQ,GAAGS,MAAM,CAAC,CAAC,GAAI,KAAIT,IAAK,EAAC;QAC7DD,UAAU,CAACE,GAAG,GAAG,YAAY;UACzB,IAAIC,EAAE,EAAEC,EAAE;UACV,IAAI,IAAI,CAACK,GAAG,CAAC,KAAKE,SAAS,EAAE;YACzB,IAAI,CAACF,GAAG,CAAC,GAAG,CAACL,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAACE,UAAU,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,aAAa,CAACR,QAAQ,CAAC,MAAM,IAAI,IAAIM,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;UACnJ;UACA,OAAO,IAAI,CAACK,GAAG,CAAC;QACpB,CAAC;MACL;MACA,OAAOT,UAAU;IACrB;EACJ,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}