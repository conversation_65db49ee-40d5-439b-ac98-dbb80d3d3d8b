{"ast": null, "code": "export function atou(b64) {\n  return decodeURIComponent(escape(atob(b64)));\n}\nexport function utoa(data) {\n  return btoa(unescape(encodeURIComponent(data)));\n}", "map": {"version": 3, "names": ["atou", "b64", "decodeURIComponent", "escape", "atob", "utoa", "data", "btoa", "unescape", "encodeURIComponent"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/src/app/unicode.ts"], "sourcesContent": ["export function atou(b64: string) {\n  return decodeURIComponent(escape(atob(b64)));\n}\n\nexport function utoa(data: string) {\n  return btoa(unescape(encodeURIComponent(data)));\n}\n"], "mappings": "AAAA,OAAM,SAAUA,IAAIA,CAACC,GAAW;EAC9B,OAAOC,kBAAkB,CAACC,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,CAAC,CAAC;AAC9C;AAEA,OAAM,SAAUI,IAAIA,CAACC,IAAY;EAC/B,OAAOC,IAAI,CAACC,QAAQ,CAACC,kBAAkB,CAACH,IAAI,CAAC,CAAC,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}