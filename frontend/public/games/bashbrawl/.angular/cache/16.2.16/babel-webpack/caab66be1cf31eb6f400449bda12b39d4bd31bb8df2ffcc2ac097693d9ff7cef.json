{"ast": null, "code": "export default function _includesWith(pred, x, list) {\n  var idx = 0;\n  var len = list.length;\n  while (idx < len) {\n    if (pred(x, list[idx])) {\n      return true;\n    }\n    idx += 1;\n  }\n  return false;\n}", "map": {"version": 3, "names": ["_includesWith", "pred", "x", "list", "idx", "len", "length"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_includesWith.js"], "sourcesContent": ["export default function _includesWith(pred, x, list) {\n  var idx = 0;\n  var len = list.length;\n\n  while (idx < len) {\n    if (pred(x, list[idx])) {\n      return true;\n    }\n\n    idx += 1;\n  }\n\n  return false;\n}"], "mappings": "AAAA,eAAe,SAASA,aAAaA,CAACC,IAAI,EAAEC,CAAC,EAAEC,IAAI,EAAE;EACnD,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIC,GAAG,GAAGF,IAAI,CAACG,MAAM;EAErB,OAAOF,GAAG,GAAGC,GAAG,EAAE;IAChB,IAAIJ,IAAI,CAACC,CAAC,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACtB,OAAO,IAAI;IACb;IAEAA,GAAG,IAAI,CAAC;EACV;EAEA,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}