{"ast": null, "code": "import { ClarityIcons as s } from \"../icon.service.js\";\nimport { accessibility1Icon as r } from \"../shapes/accessibility-1.js\";\nimport { accessibility2Icon as o } from \"../shapes/accessibility-2.js\";\nimport { addTextIcon as p } from \"../shapes/add-text.js\";\nimport { alarmClockIcon as m } from \"../shapes/alarm-clock.js\";\nimport { alarmOffIcon as e } from \"../shapes/alarm-off.js\";\nimport { announcementIcon as i } from \"../shapes/announcement.js\";\nimport { asteriskIcon as a } from \"../shapes/asterisk.js\";\nimport { banIcon as t, banIconName as h } from \"../shapes/ban.js\";\nimport { betaIcon as f } from \"../shapes/beta.js\";\nimport { birthdayCakeIcon as j } from \"../shapes/birthday-cake.js\";\nimport { boltIcon as l, boltIconName as c } from \"../shapes/bolt.js\";\nimport { bookIcon as n } from \"../shapes/book.js\";\nimport { briefcaseIcon as d } from \"../shapes/briefcase.js\";\nimport { bubbleExclamationIcon as u, bubbleExclamationIconName as w } from \"../shapes/bubble-exclamation.js\";\nimport { bugIcon as b } from \"../shapes/bug.js\";\nimport { bullseyeIcon as g } from \"../shapes/bullseye.js\";\nimport { childArrowIcon as y } from \"../shapes/child-arrow.js\";\nimport { circleArrowIcon as k } from \"../shapes/circle-arrow.js\";\nimport { circleIcon as v } from \"../shapes/circle.js\";\nimport { clipboardIcon as z } from \"../shapes/clipboard.js\";\nimport { clockIcon as x } from \"../shapes/clock.js\";\nimport { cloneIcon as A } from \"../shapes/clone.js\";\nimport { collapseCardIcon as I } from \"../shapes/collapse-card.js\";\nimport { colorPaletteIcon as q } from \"../shapes/color-palette.js\";\nimport { colorPickerIcon as B } from \"../shapes/color-picker.js\";\nimport { copyToClipboardIcon as C } from \"../shapes/copy-to-clipboard.js\";\nimport { copyIcon as D } from \"../shapes/copy.js\";\nimport { crosshairsIcon as E } from \"../shapes/crosshairs.js\";\nimport { cursorArrowIcon as F } from \"../shapes/cursor-arrow.js\";\nimport { cursorHandClickIcon as G } from \"../shapes/cursor-hand-click.js\";\nimport { cursorHandGrabIcon as H } from \"../shapes/cursor-hand-grab.js\";\nimport { cursorHandOpenIcon as J } from \"../shapes/cursor-hand-open.js\";\nimport { cursorHandIcon as K } from \"../shapes/cursor-hand.js\";\nimport { cursorMoveIcon as L } from \"../shapes/cursor-move.js\";\nimport { detailsIcon as M } from \"../shapes/details.js\";\nimport { dotCircleIcon as N } from \"../shapes/dot-circle.js\";\nimport { downloadIcon as O } from \"../shapes/download.js\";\nimport { dragHandleCornerIcon as P } from \"../shapes/drag-handle-corner.js\";\nimport { dragHandleIcon as Q } from \"../shapes/drag-handle.js\";\nimport { eraserIcon as R } from \"../shapes/eraser.js\";\nimport { expandCardIcon as S } from \"../shapes/expand-card.js\";\nimport { fileGroupIcon as T } from \"../shapes/file-group.js\";\nimport { fileSettingsIcon as U } from \"../shapes/file-settings.js\";\nimport { fileZipIcon as V } from \"../shapes/file-zip.js\";\nimport { fileIcon as W, fileIconName as X } from \"../shapes/file.js\";\nimport { filter2Icon as Y } from \"../shapes/filter-2.js\";\nimport { filterOffIcon as Z } from \"../shapes/filter-off.js\";\nimport { filterIcon as $ } from \"../shapes/filter.js\";\nimport { firewallIcon as _ } from \"../shapes/firewall.js\";\nimport { firstAidIcon as ss } from \"../shapes/first-aid.js\";\nimport { fishIcon as rs } from \"../shapes/fish.js\";\nimport { flameIcon as os } from \"../shapes/flame.js\";\nimport { formIcon as ps } from \"../shapes/form.js\";\nimport { fuelIcon as ms } from \"../shapes/fuel.js\";\nimport { gridViewIcon as es } from \"../shapes/grid-view.js\";\nimport { helpIcon as is } from \"../shapes/help.js\";\nimport { historyIcon as as } from \"../shapes/history.js\";\nimport { hourglassIcon as ts } from \"../shapes/hourglass.js\";\nimport { idBadgeIcon as hs } from \"../shapes/id-badge.js\";\nimport { keyIcon as fs } from \"../shapes/key.js\";\nimport { launchpadIcon as js } from \"../shapes/launchpad.js\";\nimport { landscapeIcon as ls } from \"../shapes/landscape.js\";\nimport { libraryIcon as cs } from \"../shapes/library.js\";\nimport { lightbulbIcon as ns } from \"../shapes/lightbulb.js\";\nimport { listIcon as ds } from \"../shapes/list.js\";\nimport { lockIcon as us } from \"../shapes/lock.js\";\nimport { loginIcon as ws, loginIconName as bs } from \"../shapes/login.js\";\nimport { logoutIcon as gs, logoutIconName as ys } from \"../shapes/logout.js\";\nimport { minusCircleIcon as ks } from \"../shapes/minus-circle.js\";\nimport { minusIcon as vs } from \"../shapes/minus.js\";\nimport { moonIcon as zs } from \"../shapes/moon.js\";\nimport { newIcon as xs } from \"../shapes/new.js\";\nimport { noAccessIcon as As } from \"../shapes/no-access.js\";\nimport { noteIcon as Is, noteIconName as qs } from \"../shapes/note.js\";\nimport { objectsIcon as Bs } from \"../shapes/objects.js\";\nimport { organizationIcon as Cs, organizationIconName as Ds } from \"../shapes/organization.js\";\nimport { paperclipIcon as Es, paperclipIconName as Fs } from \"../shapes/paperclip.js\";\nimport { pasteIcon as Gs } from \"../shapes/paste.js\";\nimport { pencilIcon as Hs, pencilIconName as Js } from \"../shapes/pencil.js\";\nimport { pinIcon as Ks } from \"../shapes/pin.js\";\nimport { pinboardIcon as Ls, pinboardIconName as Ms } from \"../shapes/pinboard.js\";\nimport { plusCircleIcon as Ns } from \"../shapes/plus-circle.js\";\nimport { plusIcon as Os, plusIconName as Ps } from \"../shapes/plus.js\";\nimport { popOutIcon as Qs } from \"../shapes/pop-out.js\";\nimport { portraitIcon as Rs } from \"../shapes/portrait.js\";\nimport { printerIcon as Ss } from \"../shapes/printer.js\";\nimport { recycleIcon as Ts } from \"../shapes/recycle.js\";\nimport { redoIcon as Us } from \"../shapes/redo.js\";\nimport { refreshIcon as Vs } from \"../shapes/refresh.js\";\nimport { repeatIcon as Ws } from \"../shapes/repeat.js\";\nimport { resizeIcon as Xs, resizeIconName as Ys } from \"../shapes/resize.js\";\nimport { scissorsIcon as Zs } from \"../shapes/scissors.js\";\nimport { scrollIcon as $s } from \"../shapes/scroll.js\";\nimport { shrinkIcon as _s, shrinkIconName as sr } from \"../shapes/shrink.js\";\nimport { sliderIcon as rr } from \"../shapes/slider.js\";\nimport { snowflakeIcon as or } from \"../shapes/snowflake.js\";\nimport { sortByIcon as pr } from \"../shapes/sort-by.js\";\nimport { sunIcon as mr } from \"../shapes/sun.js\";\nimport { switchIcon as er } from \"../shapes/switch.js\";\nimport { syncIcon as ir } from \"../shapes/sync.js\";\nimport { tableIcon as ar } from \"../shapes/table.js\";\nimport { tagIcon as tr } from \"../shapes/tag.js\";\nimport { tagsIcon as hr } from \"../shapes/tags.js\";\nimport { targetIcon as fr } from \"../shapes/target.js\";\nimport { thermometerIcon as jr } from \"../shapes/thermometer.js\";\nimport { timelineIcon as lr } from \"../shapes/timeline.js\";\nimport { timesCircleIcon as cr, timesCircleIconName as nr } from \"../shapes/times-circle.js\";\nimport { toolsIcon as dr } from \"../shapes/tools.js\";\nimport { trashIcon as ur } from \"../shapes/trash.js\";\nimport { treeViewIcon as wr } from \"../shapes/tree-view.js\";\nimport { treeIcon as br } from \"../shapes/tree.js\";\nimport { twoWayArrowsIcon as gr } from \"../shapes/two-way-arrows.js\";\nimport { undoIcon as yr } from \"../shapes/undo.js\";\nimport { unpinIcon as kr } from \"../shapes/unpin.js\";\nimport { unlockIcon as vr } from \"../shapes/unlock.js\";\nimport { uploadIcon as zr } from \"../shapes/upload.js\";\nimport { usersIcon as xr, usersIconName as Ar } from \"../shapes/users.js\";\nimport { viewCardsIcon as Ir } from \"../shapes/view-cards.js\";\nimport { viewListIcon as qr } from \"../shapes/view-list.js\";\nimport { volumeIcon as Br } from \"../shapes/volume.js\";\nimport { wandIcon as Cr } from \"../shapes/wand.js\";\nimport { windowCloseIcon as Dr } from \"../shapes/window-close.js\";\nimport { windowMaxIcon as Er } from \"../shapes/window-max.js\";\nimport { windowMinIcon as Fr } from \"../shapes/window-min.js\";\nimport { windowRestoreIcon as Gr } from \"../shapes/window-restore.js\";\nimport { worldIcon as Hr } from \"../shapes/world.js\";\nimport { wrenchIcon as Jr } from \"../shapes/wrench.js\";\nimport { zoomInIcon as Kr } from \"../shapes/zoom-in.js\";\nimport { zoomOutIcon as Lr } from \"../shapes/zoom-out.js\";\nimport { gavelIcon as Mr } from \"../shapes/gavel.js\";\nconst Nr = [r, o, p, m, e, i, a, t, f, j, l, n, d, u, b, g, y, v, k, z, x, A, I, q, B, D, C, E, F, K, G, H, J, L, M, N, O, Q, P, R, S, T, W, U, V, $, Y, Z, _, ss, rs, os, ps, ms, Mr, es, is, as, ts, hs, fs, ls, js, cs, ns, ds, us, ws, gs, vs, ks, zs, xs, As, Is, Bs, Cs, Es, Gs, Hs, Ks, Ls, Os, Ns, Qs, Rs, Ss, Ts, Us, Vs, Ws, Xs, Zs, $s, _s, rr, or, pr, mr, er, ir, ar, tr, hr, fr, jr, cr, lr, dr, ur, br, wr, gr, yr, kr, vr, zr, xr, Ir, qr, Br, Cr, Dr, Er, Fr, Gr, Hr, Jr, Kr, Lr],\n  Or = [[Js, [\"edit\"]], [qs, [\"note-edit\"]], [Ar, [\"group\"]], [X, [\"document\"]], [Ps, [\"add\"]], [h, [\"cancel\"]], [nr, [\"remove\"]], [bs, [\"sign-in\"]], [ys, [\"sign-out\"]], [c, [\"lightning\"]], [Ds, [\"flow-chart\"]], [w, [\"alert\"]], [Ms, [\"pinned\"]], [Fs, [\"attachment\"]], [sr, [\"resize-down\"]], [Ys, [\"resize-up\"]]];\nfunction Pr() {\n  s.addIcons(...Nr), s.addAliases(...Or);\n}\nexport { Or as essentialCollectionAliases, Nr as essentialCollectionIcons, Pr as loadEssentialIconSet };", "map": {"version": 3, "names": ["ClarityIcons", "s", "accessibility1Icon", "r", "accessibility2Icon", "o", "addTextIcon", "p", "alarmClockIcon", "m", "alarmOffIcon", "e", "announcementIcon", "i", "asteriskIcon", "a", "banIcon", "t", "banIconName", "h", "betaIcon", "f", "birthdayCakeIcon", "j", "boltIcon", "l", "boltIconName", "c", "bookIcon", "n", "briefcaseIcon", "d", "bubbleExclamationIcon", "u", "bubbleExclamationIconName", "w", "bugIcon", "b", "bullseyeIcon", "g", "childArrowIcon", "y", "circleArrowIcon", "k", "circleIcon", "v", "clipboardIcon", "z", "clockIcon", "x", "cloneIcon", "A", "collapseCardIcon", "I", "colorPaletteIcon", "q", "colorPickerIcon", "B", "copyToClipboardIcon", "C", "copyIcon", "D", "crosshairsIcon", "E", "cursorArrowIcon", "F", "cursorHandClickIcon", "G", "cursorHandGrabIcon", "H", "cursorHandOpenIcon", "J", "cursorHandIcon", "K", "cursorMoveIcon", "L", "detailsIcon", "M", "dotCircleIcon", "N", "downloadIcon", "O", "dragHandleCornerIcon", "P", "dragHandleIcon", "Q", "eraserIcon", "R", "expandCardIcon", "S", "fileGroupIcon", "T", "fileSettingsIcon", "U", "fileZipIcon", "V", "fileIcon", "W", "fileIconName", "X", "filter2Icon", "Y", "filterOffIcon", "Z", "filterIcon", "$", "firewallIcon", "_", "firstAidIcon", "ss", "fishIcon", "rs", "flameIcon", "os", "formIcon", "ps", "fuelIcon", "ms", "gridViewIcon", "es", "helpIcon", "is", "historyIcon", "as", "hourglassIcon", "ts", "idBadgeIcon", "hs", "keyIcon", "fs", "launchpadIcon", "js", "landscapeIcon", "ls", "libraryIcon", "cs", "lightbulbIcon", "ns", "listIcon", "ds", "lockIcon", "us", "loginIcon", "ws", "loginIconName", "bs", "logoutIcon", "gs", "logoutIconName", "ys", "minusCircleIcon", "ks", "minusIcon", "vs", "moonIcon", "zs", "newIcon", "xs", "noAccessIcon", "As", "noteIcon", "Is", "noteIconName", "qs", "objectsIcon", "Bs", "organizationIcon", "Cs", "organizationIconName", "Ds", "paperclipIcon", "Es", "paperclipIconName", "Fs", "pasteIcon", "Gs", "pencilIcon", "Hs", "pencilIconName", "Js", "pinIcon", "Ks", "pinboardIcon", "Ls", "pinboardIconName", "Ms", "plusCircleIcon", "Ns", "plusIcon", "<PERSON><PERSON>", "plusIconName", "Ps", "popOutIcon", "Qs", "portraitIcon", "Rs", "printerIcon", "Ss", "recycleIcon", "Ts", "redoIcon", "Us", "refreshIcon", "Vs", "repeatIcon", "Ws", "resizeIcon", "Xs", "resizeIconName", "Ys", "scissorsIcon", "Zs", "scrollIcon", "$s", "shrinkIcon", "_s", "shrinkIconName", "sr", "sliderIcon", "rr", "snowflakeIcon", "or", "sortByIcon", "pr", "sunIcon", "mr", "switchIcon", "er", "syncIcon", "ir", "tableIcon", "ar", "tagIcon", "tr", "tagsIcon", "hr", "targetIcon", "fr", "thermometerIcon", "jr", "timelineIcon", "lr", "timesCircleIcon", "cr", "timesCircleIconName", "nr", "toolsIcon", "dr", "trashIcon", "ur", "treeViewIcon", "wr", "treeIcon", "br", "twoWayArrowsIcon", "gr", "undoIcon", "yr", "unpinIcon", "kr", "unlockIcon", "vr", "uploadIcon", "zr", "usersIcon", "xr", "usersIconName", "Ar", "viewCardsIcon", "<PERSON>r", "viewListIcon", "qr", "volumeIcon", "Br", "wandIcon", "Cr", "windowCloseIcon", "Dr", "windowMaxIcon", "Er", "windowMinIcon", "Fr", "windowRestoreIcon", "Gr", "worldIcon", "Hr", "wrenchIcon", "<PERSON>", "zoomInIcon", "Kr", "zoomOutIcon", "Lr", "gavelIcon", "Mr", "Nr", "Or", "Pr", "addIcons", "addAliases", "essentialCollectionAliases", "essentialCollectionIcons", "loadEssentialIconSet"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/collections/essential.js"], "sourcesContent": ["import{ClarityIcons as s}from\"../icon.service.js\";import{accessibility1Icon as r}from\"../shapes/accessibility-1.js\";import{accessibility2Icon as o}from\"../shapes/accessibility-2.js\";import{addTextIcon as p}from\"../shapes/add-text.js\";import{alarmClockIcon as m}from\"../shapes/alarm-clock.js\";import{alarmOffIcon as e}from\"../shapes/alarm-off.js\";import{announcementIcon as i}from\"../shapes/announcement.js\";import{asteriskIcon as a}from\"../shapes/asterisk.js\";import{banIcon as t,banIconName as h}from\"../shapes/ban.js\";import{betaIcon as f}from\"../shapes/beta.js\";import{birthdayCakeIcon as j}from\"../shapes/birthday-cake.js\";import{boltIcon as l,boltIconName as c}from\"../shapes/bolt.js\";import{bookIcon as n}from\"../shapes/book.js\";import{briefcaseIcon as d}from\"../shapes/briefcase.js\";import{bubbleExclamationIcon as u,bubbleExclamationIconName as w}from\"../shapes/bubble-exclamation.js\";import{bugIcon as b}from\"../shapes/bug.js\";import{bullseyeIcon as g}from\"../shapes/bullseye.js\";import{childArrowIcon as y}from\"../shapes/child-arrow.js\";import{circleArrowIcon as k}from\"../shapes/circle-arrow.js\";import{circleIcon as v}from\"../shapes/circle.js\";import{clipboardIcon as z}from\"../shapes/clipboard.js\";import{clockIcon as x}from\"../shapes/clock.js\";import{cloneIcon as A}from\"../shapes/clone.js\";import{collapseCardIcon as I}from\"../shapes/collapse-card.js\";import{colorPaletteIcon as q}from\"../shapes/color-palette.js\";import{colorPickerIcon as B}from\"../shapes/color-picker.js\";import{copyToClipboardIcon as C}from\"../shapes/copy-to-clipboard.js\";import{copyIcon as D}from\"../shapes/copy.js\";import{crosshairsIcon as E}from\"../shapes/crosshairs.js\";import{cursorArrowIcon as F}from\"../shapes/cursor-arrow.js\";import{cursorHandClickIcon as G}from\"../shapes/cursor-hand-click.js\";import{cursorHandGrabIcon as H}from\"../shapes/cursor-hand-grab.js\";import{cursorHandOpenIcon as J}from\"../shapes/cursor-hand-open.js\";import{cursorHandIcon as K}from\"../shapes/cursor-hand.js\";import{cursorMoveIcon as L}from\"../shapes/cursor-move.js\";import{detailsIcon as M}from\"../shapes/details.js\";import{dotCircleIcon as N}from\"../shapes/dot-circle.js\";import{downloadIcon as O}from\"../shapes/download.js\";import{dragHandleCornerIcon as P}from\"../shapes/drag-handle-corner.js\";import{dragHandleIcon as Q}from\"../shapes/drag-handle.js\";import{eraserIcon as R}from\"../shapes/eraser.js\";import{expandCardIcon as S}from\"../shapes/expand-card.js\";import{fileGroupIcon as T}from\"../shapes/file-group.js\";import{fileSettingsIcon as U}from\"../shapes/file-settings.js\";import{fileZipIcon as V}from\"../shapes/file-zip.js\";import{fileIcon as W,fileIconName as X}from\"../shapes/file.js\";import{filter2Icon as Y}from\"../shapes/filter-2.js\";import{filterOffIcon as Z}from\"../shapes/filter-off.js\";import{filterIcon as $}from\"../shapes/filter.js\";import{firewallIcon as _}from\"../shapes/firewall.js\";import{firstAidIcon as ss}from\"../shapes/first-aid.js\";import{fishIcon as rs}from\"../shapes/fish.js\";import{flameIcon as os}from\"../shapes/flame.js\";import{formIcon as ps}from\"../shapes/form.js\";import{fuelIcon as ms}from\"../shapes/fuel.js\";import{gridViewIcon as es}from\"../shapes/grid-view.js\";import{helpIcon as is}from\"../shapes/help.js\";import{historyIcon as as}from\"../shapes/history.js\";import{hourglassIcon as ts}from\"../shapes/hourglass.js\";import{idBadgeIcon as hs}from\"../shapes/id-badge.js\";import{keyIcon as fs}from\"../shapes/key.js\";import{launchpadIcon as js}from\"../shapes/launchpad.js\";import{landscapeIcon as ls}from\"../shapes/landscape.js\";import{libraryIcon as cs}from\"../shapes/library.js\";import{lightbulbIcon as ns}from\"../shapes/lightbulb.js\";import{listIcon as ds}from\"../shapes/list.js\";import{lockIcon as us}from\"../shapes/lock.js\";import{loginIcon as ws,loginIconName as bs}from\"../shapes/login.js\";import{logoutIcon as gs,logoutIconName as ys}from\"../shapes/logout.js\";import{minusCircleIcon as ks}from\"../shapes/minus-circle.js\";import{minusIcon as vs}from\"../shapes/minus.js\";import{moonIcon as zs}from\"../shapes/moon.js\";import{newIcon as xs}from\"../shapes/new.js\";import{noAccessIcon as As}from\"../shapes/no-access.js\";import{noteIcon as Is,noteIconName as qs}from\"../shapes/note.js\";import{objectsIcon as Bs}from\"../shapes/objects.js\";import{organizationIcon as Cs,organizationIconName as Ds}from\"../shapes/organization.js\";import{paperclipIcon as Es,paperclipIconName as Fs}from\"../shapes/paperclip.js\";import{pasteIcon as Gs}from\"../shapes/paste.js\";import{pencilIcon as Hs,pencilIconName as Js}from\"../shapes/pencil.js\";import{pinIcon as Ks}from\"../shapes/pin.js\";import{pinboardIcon as Ls,pinboardIconName as Ms}from\"../shapes/pinboard.js\";import{plusCircleIcon as Ns}from\"../shapes/plus-circle.js\";import{plusIcon as Os,plusIconName as Ps}from\"../shapes/plus.js\";import{popOutIcon as Qs}from\"../shapes/pop-out.js\";import{portraitIcon as Rs}from\"../shapes/portrait.js\";import{printerIcon as Ss}from\"../shapes/printer.js\";import{recycleIcon as Ts}from\"../shapes/recycle.js\";import{redoIcon as Us}from\"../shapes/redo.js\";import{refreshIcon as Vs}from\"../shapes/refresh.js\";import{repeatIcon as Ws}from\"../shapes/repeat.js\";import{resizeIcon as Xs,resizeIconName as Ys}from\"../shapes/resize.js\";import{scissorsIcon as Zs}from\"../shapes/scissors.js\";import{scrollIcon as $s}from\"../shapes/scroll.js\";import{shrinkIcon as _s,shrinkIconName as sr}from\"../shapes/shrink.js\";import{sliderIcon as rr}from\"../shapes/slider.js\";import{snowflakeIcon as or}from\"../shapes/snowflake.js\";import{sortByIcon as pr}from\"../shapes/sort-by.js\";import{sunIcon as mr}from\"../shapes/sun.js\";import{switchIcon as er}from\"../shapes/switch.js\";import{syncIcon as ir}from\"../shapes/sync.js\";import{tableIcon as ar}from\"../shapes/table.js\";import{tagIcon as tr}from\"../shapes/tag.js\";import{tagsIcon as hr}from\"../shapes/tags.js\";import{targetIcon as fr}from\"../shapes/target.js\";import{thermometerIcon as jr}from\"../shapes/thermometer.js\";import{timelineIcon as lr}from\"../shapes/timeline.js\";import{timesCircleIcon as cr,timesCircleIconName as nr}from\"../shapes/times-circle.js\";import{toolsIcon as dr}from\"../shapes/tools.js\";import{trashIcon as ur}from\"../shapes/trash.js\";import{treeViewIcon as wr}from\"../shapes/tree-view.js\";import{treeIcon as br}from\"../shapes/tree.js\";import{twoWayArrowsIcon as gr}from\"../shapes/two-way-arrows.js\";import{undoIcon as yr}from\"../shapes/undo.js\";import{unpinIcon as kr}from\"../shapes/unpin.js\";import{unlockIcon as vr}from\"../shapes/unlock.js\";import{uploadIcon as zr}from\"../shapes/upload.js\";import{usersIcon as xr,usersIconName as Ar}from\"../shapes/users.js\";import{viewCardsIcon as Ir}from\"../shapes/view-cards.js\";import{viewListIcon as qr}from\"../shapes/view-list.js\";import{volumeIcon as Br}from\"../shapes/volume.js\";import{wandIcon as Cr}from\"../shapes/wand.js\";import{windowCloseIcon as Dr}from\"../shapes/window-close.js\";import{windowMaxIcon as Er}from\"../shapes/window-max.js\";import{windowMinIcon as Fr}from\"../shapes/window-min.js\";import{windowRestoreIcon as Gr}from\"../shapes/window-restore.js\";import{worldIcon as Hr}from\"../shapes/world.js\";import{wrenchIcon as Jr}from\"../shapes/wrench.js\";import{zoomInIcon as Kr}from\"../shapes/zoom-in.js\";import{zoomOutIcon as Lr}from\"../shapes/zoom-out.js\";import{gavelIcon as Mr}from\"../shapes/gavel.js\";const Nr=[r,o,p,m,e,i,a,t,f,j,l,n,d,u,b,g,y,v,k,z,x,A,I,q,B,D,C,E,F,K,G,H,J,L,M,N,O,Q,P,R,S,T,W,U,V,$,Y,Z,_,ss,rs,os,ps,ms,Mr,es,is,as,ts,hs,fs,ls,js,cs,ns,ds,us,ws,gs,vs,ks,zs,xs,As,Is,Bs,Cs,Es,Gs,Hs,Ks,Ls,Os,Ns,Qs,Rs,Ss,Ts,Us,Vs,Ws,Xs,Zs,$s,_s,rr,or,pr,mr,er,ir,ar,tr,hr,fr,jr,cr,lr,dr,ur,br,wr,gr,yr,kr,vr,zr,xr,Ir,qr,Br,Cr,Dr,Er,Fr,Gr,Hr,Jr,Kr,Lr],Or=[[Js,[\"edit\"]],[qs,[\"note-edit\"]],[Ar,[\"group\"]],[X,[\"document\"]],[Ps,[\"add\"]],[h,[\"cancel\"]],[nr,[\"remove\"]],[bs,[\"sign-in\"]],[ys,[\"sign-out\"]],[c,[\"lightning\"]],[Ds,[\"flow-chart\"]],[w,[\"alert\"]],[Ms,[\"pinned\"]],[Fs,[\"attachment\"]],[sr,[\"resize-down\"]],[Ys,[\"resize-up\"]]];function Pr(){s.addIcons(...Nr),s.addAliases(...Or)}export{Or as essentialCollectionAliases,Nr as essentialCollectionIcons,Pr as loadEssentialIconSet};\n"], "mappings": "AAAA,SAAOA,YAAY,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,kBAAkB,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,kBAAkB,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,wBAAwB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,OAAO,IAAIC,CAAC,EAACC,WAAW,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,QAAQ,IAAIC,CAAC,EAACC,YAAY,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,wBAAwB;AAAC,SAAOC,qBAAqB,IAAIC,CAAC,EAACC,yBAAyB,IAAIC,CAAC,QAAK,iCAAiC;AAAC,SAAOC,OAAO,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,wBAAwB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,gCAAgC;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,gCAAgC;AAAC,SAAOC,kBAAkB,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,kBAAkB,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,oBAAoB,IAAIC,CAAC,QAAK,iCAAiC;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,EAACC,YAAY,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,YAAY,IAAIC,EAAE,QAAK,wBAAwB;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,SAAS,IAAIC,EAAE,QAAK,oBAAoB;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,YAAY,IAAIC,EAAE,QAAK,wBAAwB;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,WAAW,IAAIC,EAAE,QAAK,sBAAsB;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,wBAAwB;AAAC,SAAOC,WAAW,IAAIC,EAAE,QAAK,uBAAuB;AAAC,SAAOC,OAAO,IAAIC,EAAE,QAAK,kBAAkB;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,wBAAwB;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,wBAAwB;AAAC,SAAOC,WAAW,IAAIC,EAAE,QAAK,sBAAsB;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,wBAAwB;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,SAAS,IAAIC,EAAE,EAACC,aAAa,IAAIC,EAAE,QAAK,oBAAoB;AAAC,SAAOC,UAAU,IAAIC,EAAE,EAACC,cAAc,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,eAAe,IAAIC,EAAE,QAAK,2BAA2B;AAAC,SAAOC,SAAS,IAAIC,EAAE,QAAK,oBAAoB;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,OAAO,IAAIC,EAAE,QAAK,kBAAkB;AAAC,SAAOC,YAAY,IAAIC,EAAE,QAAK,wBAAwB;AAAC,SAAOC,QAAQ,IAAIC,EAAE,EAACC,YAAY,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,WAAW,IAAIC,EAAE,QAAK,sBAAsB;AAAC,SAAOC,gBAAgB,IAAIC,EAAE,EAACC,oBAAoB,IAAIC,EAAE,QAAK,2BAA2B;AAAC,SAAOC,aAAa,IAAIC,EAAE,EAACC,iBAAiB,IAAIC,EAAE,QAAK,wBAAwB;AAAC,SAAOC,SAAS,IAAIC,EAAE,QAAK,oBAAoB;AAAC,SAAOC,UAAU,IAAIC,EAAE,EAACC,cAAc,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,OAAO,IAAIC,EAAE,QAAK,kBAAkB;AAAC,SAAOC,YAAY,IAAIC,EAAE,EAACC,gBAAgB,IAAIC,EAAE,QAAK,uBAAuB;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,0BAA0B;AAAC,SAAOC,QAAQ,IAAIC,EAAE,EAACC,YAAY,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,UAAU,IAAIC,EAAE,QAAK,sBAAsB;AAAC,SAAOC,YAAY,IAAIC,EAAE,QAAK,uBAAuB;AAAC,SAAOC,WAAW,IAAIC,EAAE,QAAK,sBAAsB;AAAC,SAAOC,WAAW,IAAIC,EAAE,QAAK,sBAAsB;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,WAAW,IAAIC,EAAE,QAAK,sBAAsB;AAAC,SAAOC,UAAU,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,UAAU,IAAIC,EAAE,EAACC,cAAc,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,YAAY,IAAIC,EAAE,QAAK,uBAAuB;AAAC,SAAOC,UAAU,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,UAAU,IAAIC,EAAE,EAACC,cAAc,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,UAAU,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,wBAAwB;AAAC,SAAOC,UAAU,IAAIC,EAAE,QAAK,sBAAsB;AAAC,SAAOC,OAAO,IAAIC,EAAE,QAAK,kBAAkB;AAAC,SAAOC,UAAU,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,SAAS,IAAIC,EAAE,QAAK,oBAAoB;AAAC,SAAOC,OAAO,IAAIC,EAAE,QAAK,kBAAkB;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,UAAU,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,eAAe,IAAIC,EAAE,QAAK,0BAA0B;AAAC,SAAOC,YAAY,IAAIC,EAAE,QAAK,uBAAuB;AAAC,SAAOC,eAAe,IAAIC,EAAE,EAACC,mBAAmB,IAAIC,EAAE,QAAK,2BAA2B;AAAC,SAAOC,SAAS,IAAIC,EAAE,QAAK,oBAAoB;AAAC,SAAOC,SAAS,IAAIC,EAAE,QAAK,oBAAoB;AAAC,SAAOC,YAAY,IAAIC,EAAE,QAAK,wBAAwB;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,gBAAgB,IAAIC,EAAE,QAAK,6BAA6B;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,SAAS,IAAIC,EAAE,QAAK,oBAAoB;AAAC,SAAOC,UAAU,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,UAAU,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,SAAS,IAAIC,EAAE,EAACC,aAAa,IAAIC,EAAE,QAAK,oBAAoB;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,yBAAyB;AAAC,SAAOC,YAAY,IAAIC,EAAE,QAAK,wBAAwB;AAAC,SAAOC,UAAU,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,mBAAmB;AAAC,SAAOC,eAAe,IAAIC,EAAE,QAAK,2BAA2B;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,yBAAyB;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,yBAAyB;AAAC,SAAOC,iBAAiB,IAAIC,EAAE,QAAK,6BAA6B;AAAC,SAAOC,SAAS,IAAIC,EAAE,QAAK,oBAAoB;AAAC,SAAOC,UAAU,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,UAAU,IAAIC,EAAE,QAAK,sBAAsB;AAAC,SAAOC,WAAW,IAAIC,EAAE,QAAK,uBAAuB;AAAC,SAAOC,SAAS,IAAIC,EAAE,QAAK,oBAAoB;AAAC,MAAMC,EAAE,GAAC,CAACnS,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACF,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACF,CAAC,EAACI,CAAC,EAACE,CAAC,EAACQ,CAAC,EAACN,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACF,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACM,CAAC,EAACJ,CAAC,EAACE,CAAC,EAACU,CAAC,EAACJ,CAAC,EAACE,CAAC,EAACI,CAAC,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACgL,EAAE,EAAC9K,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACI,EAAE,EAACF,EAAE,EAACI,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACI,EAAE,EAACM,EAAE,EAACF,EAAE,EAACI,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACI,EAAE,EAACE,EAAE,EAACI,EAAE,EAACI,EAAE,EAACE,EAAE,EAACI,EAAE,EAACE,EAAE,EAACM,EAAE,EAACF,EAAE,EAACM,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACI,EAAE,EAACE,EAAE,EAACE,EAAE,EAACI,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACI,EAAE,EAACF,EAAE,EAACM,EAAE,EAACE,EAAE,EAACI,EAAE,EAACF,EAAE,EAACI,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACI,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,EAACE,EAAE,CAAC;EAACI,EAAE,GAAC,CAAC,CAACpH,EAAE,EAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAChB,EAAE,EAAC,CAAC,WAAW,CAAC,CAAC,EAAC,CAACwG,EAAE,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAACxK,CAAC,EAAC,CAAC,UAAU,CAAC,CAAC,EAAC,CAAC4F,EAAE,EAAC,CAAC,KAAK,CAAC,CAAC,EAAC,CAAC5K,CAAC,EAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAACkO,EAAE,EAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAACpG,EAAE,EAAC,CAAC,SAAS,CAAC,CAAC,EAAC,CAACI,EAAE,EAAC,CAAC,UAAU,CAAC,CAAC,EAAC,CAAC1H,CAAC,EAAC,CAAC,WAAW,CAAC,CAAC,EAAC,CAAC8I,EAAE,EAAC,CAAC,YAAY,CAAC,CAAC,EAAC,CAACtI,CAAC,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAACsJ,EAAE,EAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAACZ,EAAE,EAAC,CAAC,YAAY,CAAC,CAAC,EAAC,CAAC4C,EAAE,EAAC,CAAC,aAAa,CAAC,CAAC,EAAC,CAACR,EAAE,EAAC,CAAC,WAAW,CAAC,CAAC,CAAC;AAAC,SAASuF,EAAEA,CAAA,EAAE;EAACvS,CAAC,CAACwS,QAAQ,CAAC,GAAGH,EAAE,CAAC,EAACrS,CAAC,CAACyS,UAAU,CAAC,GAAGH,EAAE,CAAC;AAAA;AAAC,SAAOA,EAAE,IAAII,0BAA0B,EAACL,EAAE,IAAIM,wBAAwB,EAACJ,EAAE,IAAIK,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}