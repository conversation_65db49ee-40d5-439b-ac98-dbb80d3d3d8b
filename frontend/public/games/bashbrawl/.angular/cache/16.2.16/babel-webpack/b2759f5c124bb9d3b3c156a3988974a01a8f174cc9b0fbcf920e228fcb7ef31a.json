{"ast": null, "code": "import * as __NgCli_bootstrap_1 from \"@angular/platform-browser\";\nimport { enableProdMode } from '@angular/core';\nimport { AppModule } from './app/app.module';\nimport { environment } from './environments/environment';\nif (environment.production) {\n  enableProdMode();\n}\n__NgCli_bootstrap_1.platformBrowser().bootstrapModule(AppModule).catch(err => console.error(err));", "map": {"version": 3, "names": ["enableProdMode", "AppModule", "environment", "production", "__Ng<PERSON>li_bootstrap_1", "platformBrowser", "bootstrapModule", "catch", "err", "console", "error"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/src/main.ts"], "sourcesContent": ["import { enableProdMode } from '@angular/core';\nimport { platformBrowserDynamic } from '@angular/platform-browser-dynamic';\n\nimport { AppModule } from './app/app.module';\nimport { environment } from './environments/environment';\n\nif (environment.production) {\n  enableProdMode();\n}\n\nplatformBrowserDynamic()\n  .bootstrapModule(AppModule)\n  .catch((err) => console.error(err));\n"], "mappings": ";AAAA,SAASA,cAAc,QAAQ,eAAe;AAG9C,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,WAAW,QAAQ,4BAA4B;AAExD,IAAIA,WAAW,CAACC,UAAU,EAAE;EAC1BH,cAAc,EAAE;;AAGlBI,mBAAA,CAAAC,eAAA,EAAwB,CACrBC,eAAe,CAACL,SAAS,CAAC,CAC1BM,KAAK,CAAEC,GAAG,IAAKC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}