{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"campervan\",\n  V = [\"campervan\", C({\n    outline: '<path d=\"M10.94 24C8.72931 24 6.93875 25.79 6.93875 28C6.93875 30.21 8.72931 32 10.94 32C13.1507 32 14.9413 30.21 14.9413 28C14.9413 25.79 13.1507 24 10.94 24ZM10.94 30C9.83966 30 8.93938 29.1 8.93938 28C8.93938 26.9 9.83966 26 10.94 26C12.0403 26 12.9406 26.9 12.9406 28C12.9406 29.1 12.0403 30 10.94 30ZM6.93875 22H14.9413V14H6.93875V22ZM8.93938 16H12.9406V20H8.93938V16ZM24.9444 8H18.9425V10H24.9444V8ZM28.9556 14.44C30.206 13.31 30.9463 11.7 30.9463 10C30.9463 6.69 28.2554 4 24.9444 4H18.9425C16.3817 4 14.121 5.64 13.2807 8H5.93844C4.28792 8 2.9375 9.35 2.9375 11V27C2.9375 28.3 3.77776 29.4 4.93813 29.82V11C4.93813 10.45 5.38827 10 5.93844 10H14.8612L15.0213 9.2C15.4014 7.35 17.0519 6 18.9425 6H24.9444C27.1551 6 28.9456 7.79 28.9456 10C28.9456 11.43 28.1954 12.72 26.935 13.45L25.9747 14H24.9344V22H32.9369V27C32.9369 27.55 32.4867 28 31.9366 28H30.9362V30H31.9366C33.5871 30 34.9375 28.65 34.9375 27V20.59L28.9456 14.43L28.9556 14.44ZM26.945 20V16H27.6852L31.5764 20H26.945ZM20.9431 22H22.9438V14H16.9419V30H18.9425V16H20.9431V22ZM24.9444 24C22.7337 24 20.9431 25.79 20.9431 28C20.9431 30.21 22.7337 32 24.9444 32C27.1551 32 28.9456 30.21 28.9456 28C28.9456 25.79 27.1551 24 24.9444 24ZM24.9444 30C23.844 30 22.9438 29.1 22.9438 28C22.9438 26.9 23.844 26 24.9444 26C26.0447 26 26.945 26.9 26.945 28C26.945 29.1 26.0447 30 24.9444 30Z\"/>',\n    solid: '<path d=\"M6.93875 22H14.9413V14H6.93875V22ZM10.94 24C8.72931 24 6.93875 25.79 6.93875 28C6.93875 30.21 8.72931 32 10.94 32C13.1507 32 14.9413 30.21 14.9413 28C14.9413 25.79 13.1507 24 10.94 24ZM24.9444 8H18.9425V10H24.9444V8ZM20.9431 22H22.9438V14H16.9419V30H18.9425V16H20.9431V22ZM28.9556 14.44C30.206 13.31 30.9463 11.7 30.9463 10C30.9463 6.69 28.2554 4 24.9444 4H18.9425C16.3817 4 14.121 5.64 13.2807 8H5.93844C4.28792 8 2.9375 9.35 2.9375 11V27C2.9375 28.3 3.77776 29.4 4.93813 29.82V11C4.93813 10.45 5.38827 10 5.93844 10H14.8612L15.0213 9.2C15.4014 7.35 17.0519 6 18.9425 6H24.9444C27.1551 6 28.9456 7.79 28.9456 10C28.9456 11.43 28.1954 12.72 26.935 13.45L25.9747 14H24.9344V22H32.9369V27C32.9369 27.55 32.4867 28 31.9366 28H30.9362V30H31.9366C33.5871 30 34.9375 28.65 34.9375 27V20.59L28.9456 14.43L28.9556 14.44ZM24.9444 24C22.7337 24 20.9431 25.79 20.9431 28C20.9431 30.21 22.7337 32 24.9444 32C27.1551 32 28.9456 30.21 28.9456 28C28.9456 25.79 27.1551 24 24.9444 24Z\"/>'\n  })];\nexport { V as campervanIcon, H as campervanIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "V", "outline", "solid", "campervanIcon", "campervanIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/campervan.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"campervan\",V=[\"campervan\",C({outline:'<path d=\"M10.94 24C8.72931 24 6.93875 25.79 6.93875 28C6.93875 30.21 8.72931 32 10.94 32C13.1507 32 14.9413 30.21 14.9413 28C14.9413 25.79 13.1507 24 10.94 24ZM10.94 30C9.83966 30 8.93938 29.1 8.93938 28C8.93938 26.9 9.83966 26 10.94 26C12.0403 26 12.9406 26.9 12.9406 28C12.9406 29.1 12.0403 30 10.94 30ZM6.93875 22H14.9413V14H6.93875V22ZM8.93938 16H12.9406V20H8.93938V16ZM24.9444 8H18.9425V10H24.9444V8ZM28.9556 14.44C30.206 13.31 30.9463 11.7 30.9463 10C30.9463 6.69 28.2554 4 24.9444 4H18.9425C16.3817 4 14.121 5.64 13.2807 8H5.93844C4.28792 8 2.9375 9.35 2.9375 11V27C2.9375 28.3 3.77776 29.4 4.93813 29.82V11C4.93813 10.45 5.38827 10 5.93844 10H14.8612L15.0213 9.2C15.4014 7.35 17.0519 6 18.9425 6H24.9444C27.1551 6 28.9456 7.79 28.9456 10C28.9456 11.43 28.1954 12.72 26.935 13.45L25.9747 14H24.9344V22H32.9369V27C32.9369 27.55 32.4867 28 31.9366 28H30.9362V30H31.9366C33.5871 30 34.9375 28.65 34.9375 27V20.59L28.9456 14.43L28.9556 14.44ZM26.945 20V16H27.6852L31.5764 20H26.945ZM20.9431 22H22.9438V14H16.9419V30H18.9425V16H20.9431V22ZM24.9444 24C22.7337 24 20.9431 25.79 20.9431 28C20.9431 30.21 22.7337 32 24.9444 32C27.1551 32 28.9456 30.21 28.9456 28C28.9456 25.79 27.1551 24 24.9444 24ZM24.9444 30C23.844 30 22.9438 29.1 22.9438 28C22.9438 26.9 23.844 26 24.9444 26C26.0447 26 26.945 26.9 26.945 28C26.945 29.1 26.0447 30 24.9444 30Z\"/>',solid:'<path d=\"M6.93875 22H14.9413V14H6.93875V22ZM10.94 24C8.72931 24 6.93875 25.79 6.93875 28C6.93875 30.21 8.72931 32 10.94 32C13.1507 32 14.9413 30.21 14.9413 28C14.9413 25.79 13.1507 24 10.94 24ZM24.9444 8H18.9425V10H24.9444V8ZM20.9431 22H22.9438V14H16.9419V30H18.9425V16H20.9431V22ZM28.9556 14.44C30.206 13.31 30.9463 11.7 30.9463 10C30.9463 6.69 28.2554 4 24.9444 4H18.9425C16.3817 4 14.121 5.64 13.2807 8H5.93844C4.28792 8 2.9375 9.35 2.9375 11V27C2.9375 28.3 3.77776 29.4 4.93813 29.82V11C4.93813 10.45 5.38827 10 5.93844 10H14.8612L15.0213 9.2C15.4014 7.35 17.0519 6 18.9425 6H24.9444C27.1551 6 28.9456 7.79 28.9456 10C28.9456 11.43 28.1954 12.72 26.935 13.45L25.9747 14H24.9344V22H32.9369V27C32.9369 27.55 32.4867 28 31.9366 28H30.9362V30H31.9366C33.5871 30 34.9375 28.65 34.9375 27V20.59L28.9456 14.43L28.9556 14.44ZM24.9444 24C22.7337 24 20.9431 25.79 20.9431 28C20.9431 30.21 22.7337 32 24.9444 32C27.1551 32 28.9456 30.21 28.9456 28C28.9456 25.79 27.1551 24 24.9444 24Z\"/>'})];export{V as campervanIcon,H as campervanIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,WAAW;EAACC,CAAC,GAAC,CAAC,WAAW,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,q0CAAq0C;IAACC,KAAK,EAAC;EAAs9B,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,aAAa,EAACJ,CAAC,IAAIK,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}