{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst t = \"capacitor\",\n  a = [\"capacitor\", C({\n    outline: '<path d=\"M15 34.1499C14.7348 34.1499 14.4804 34.0442 14.2929 33.8562C14.1054 33.6681 14 33.413 14 33.1471V3.15272C14 2.88676 14.1054 2.63169 14.2929 2.44362C14.4804 2.25556 14.7348 2.1499 15 2.1499C15.2652 2.1499 15.5196 2.25556 15.7071 2.44362C15.8946 2.63169 16 2.88676 16 3.15272V33.1471C16 33.413 15.8946 33.6681 15.7071 33.8562C15.5196 34.0442 15.2652 34.1499 15 34.1499Z\"/><path d=\"M21 34.1499C20.7348 34.1499 20.4804 34.0442 20.2929 33.8562C20.1054 33.6681 20 33.413 20 33.1471V3.15272C20 2.88676 20.1054 2.63169 20.2929 2.44362C20.4804 2.25556 20.7348 2.1499 21 2.1499C21.2652 2.1499 21.5196 2.25556 21.7071 2.44362C21.8946 2.63169 22 2.88676 22 3.15272V33.1471C22 33.413 21.8946 33.6681 21.7071 33.8562C21.5196 34.0442 21.2652 34.1499 21 34.1499Z\"/><path d=\"M14.46 19.0474H3C2.73478 19.0474 2.48043 18.9418 2.29289 18.7537C2.10536 18.5656 2 18.3106 2 18.0446C2 17.7786 2.10536 17.5236 2.29289 17.3355C2.48043 17.1474 2.73478 17.0418 3 17.0418H14.46C14.7252 17.0418 14.9796 17.1474 15.1671 17.3355C15.3546 17.5236 15.46 17.7786 15.46 18.0446C15.46 18.3106 15.3546 18.5656 15.1671 18.7537C14.9796 18.9418 14.7252 19.0474 14.46 19.0474Z\"/><path d=\"M33 19.0474H21.54C21.2748 19.0474 21.0204 18.9418 20.8329 18.7537C20.6454 18.5656 20.54 18.3106 20.54 18.0446C20.54 17.7786 20.6454 17.5236 20.8329 17.3355C21.0204 17.1474 21.2748 17.0418 21.54 17.0418H33C33.2652 17.0418 33.5196 17.1474 33.7071 17.3355C33.8946 17.5236 34 17.7786 34 18.0446C34 18.3106 33.8946 18.5656 33.7071 18.7537C33.5196 18.9418 33.2652 19.0474 33 19.0474Z\"/>'\n  })];\nexport { a as capacitorIcon, t as capacitorIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "t", "a", "outline", "capacitorIcon", "capacitorIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/capacitor.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const t=\"capacitor\",a=[\"capacitor\",C({outline:'<path d=\"M15 34.1499C14.7348 34.1499 14.4804 34.0442 14.2929 33.8562C14.1054 33.6681 14 33.413 14 33.1471V3.15272C14 2.88676 14.1054 2.63169 14.2929 2.44362C14.4804 2.25556 14.7348 2.1499 15 2.1499C15.2652 2.1499 15.5196 2.25556 15.7071 2.44362C15.8946 2.63169 16 2.88676 16 3.15272V33.1471C16 33.413 15.8946 33.6681 15.7071 33.8562C15.5196 34.0442 15.2652 34.1499 15 34.1499Z\"/><path d=\"M21 34.1499C20.7348 34.1499 20.4804 34.0442 20.2929 33.8562C20.1054 33.6681 20 33.413 20 33.1471V3.15272C20 2.88676 20.1054 2.63169 20.2929 2.44362C20.4804 2.25556 20.7348 2.1499 21 2.1499C21.2652 2.1499 21.5196 2.25556 21.7071 2.44362C21.8946 2.63169 22 2.88676 22 3.15272V33.1471C22 33.413 21.8946 33.6681 21.7071 33.8562C21.5196 34.0442 21.2652 34.1499 21 34.1499Z\"/><path d=\"M14.46 19.0474H3C2.73478 19.0474 2.48043 18.9418 2.29289 18.7537C2.10536 18.5656 2 18.3106 2 18.0446C2 17.7786 2.10536 17.5236 2.29289 17.3355C2.48043 17.1474 2.73478 17.0418 3 17.0418H14.46C14.7252 17.0418 14.9796 17.1474 15.1671 17.3355C15.3546 17.5236 15.46 17.7786 15.46 18.0446C15.46 18.3106 15.3546 18.5656 15.1671 18.7537C14.9796 18.9418 14.7252 19.0474 14.46 19.0474Z\"/><path d=\"M33 19.0474H21.54C21.2748 19.0474 21.0204 18.9418 20.8329 18.7537C20.6454 18.5656 20.54 18.3106 20.54 18.0446C20.54 17.7786 20.6454 17.5236 20.8329 17.3355C21.0204 17.1474 21.2748 17.0418 21.54 17.0418H33C33.2652 17.0418 33.5196 17.1474 33.7071 17.3355C33.8946 17.5236 34 17.7786 34 18.0446C34 18.3106 33.8946 18.5656 33.7071 18.7537C33.5196 18.9418 33.2652 19.0474 33 19.0474Z\"/>'})];export{a as capacitorIcon,t as capacitorIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,WAAW;EAACC,CAAC,GAAC,CAAC,WAAW,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAggD,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,aAAa,EAACH,CAAC,IAAII,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}