{"ast": null, "code": "import { renderIcon as V } from \"../icon.renderer.js\";\nconst C = \"suitcase\",\n  H = [\"suitcase\", V({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24 8.00019H33C33.5523 8.00019 34 8.44791 34 9.00019V17.0002C34 19.7616 31.7614 22.0002 29 22.0002H28V20.0002H29C30.6569 20.0002 32 18.657 32 17.0002V10.0002H4V17.0002C4 18.657 5.34315 20.0002 7 20.0002H10V17.9402H12V23.4002C12 23.9525 11.5523 24.4002 11 24.4002C10.4477 24.4002 10 23.9525 10 23.4002V22.0002H7C4.23858 22.0002 2 19.7616 2 17.0002V9.00019C2 8.44791 2.44772 8.00019 3 8.00019H12V6.38019C12.0219 5.04755 13.1173 3.98349 14.45 4.00019H21.55C22.8827 3.98349 23.9781 5.04755 24 6.38019V8.00019ZM22 8.00019H14V6.43019C14.0107 6.18951 14.2091 5.99996 14.45 6.00019H21.56C21.6741 5.99751 21.7845 6.04026 21.867 6.11903C21.9495 6.1978 21.9974 6.30614 22 6.42019V8.00019Z\"/><path d=\"M4 30.0002H32V23.3102C32.7479 22.9632 33.4258 22.4819 34 21.8902V30.0002C34 31.1048 33.1046 32.0002 32 32.0002H4C2.89543 32.0002 2 31.1048 2 30.0002V21.8902C2.57843 22.4795 3.25525 22.9635 4 23.3202V30.0002Z\"/><path d=\"M26 23.4002C26 23.9525 25.5523 24.4002 25 24.4002C24.4477 24.4002 24 23.9525 24 23.4002V22.0002H14V20.0002H24V17.9402H26V23.4002Z\"/>',\n    solid: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M30 20.0344C32.1957 20.0021 33.9678 18.2262 34 16.0258V8.00861H24V6.43524C23.9974 5.78672 23.7377 5.16581 23.2783 4.70912C22.8188 4.25243 22.1971 3.99736 21.55 4.00002H14.45C13.8029 3.99736 13.1812 4.25243 12.7217 4.70912C12.2623 5.16581 12.0026 5.78672 12 6.43524V8.00861H2V16.0258C2.03273 18.2455 3.83484 20.0291 6.05 20.0344H10.05V17.9499H12.05V23.6621C12.05 24.2156 11.6023 24.6643 11.05 24.6643C10.4977 24.6643 10.05 24.2156 10.05 23.6621V22.0988H6.06C4.55893 22.0963 3.11218 21.5357 2 20.5254V29.9957C2 31.1026 2.89543 32 4 32H32C33.1046 32 34 31.1026 34 29.9957V20.5254C32.8909 21.5407 31.4422 22.1021 29.94 22.0988H28V20.0344H30ZM14 6.43522C14.0107 6.19402 14.2091 6.00406 14.45 6.00429H21.55C21.7909 6.00406 21.9893 6.19402 22 6.43522V8.00859H14V6.43522ZM25 24.6642C25.5523 24.6642 26 24.2155 26 23.662V17.9498H24V20.0343H14V22.0987H24V23.662C24 24.2155 24.4477 24.6642 25 24.6642Z\"/>'\n  })];\nexport { H as suitcaseIcon, C as suitcaseIconName };", "map": {"version": 3, "names": ["renderIcon", "V", "C", "H", "outline", "solid", "suitcaseIcon", "suitcaseIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/suitcase.js"], "sourcesContent": ["import{renderIcon as V}from\"../icon.renderer.js\";const C=\"suitcase\",H=[\"suitcase\",V({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24 8.00019H33C33.5523 8.00019 34 8.44791 34 9.00019V17.0002C34 19.7616 31.7614 22.0002 29 22.0002H28V20.0002H29C30.6569 20.0002 32 18.657 32 17.0002V10.0002H4V17.0002C4 18.657 5.34315 20.0002 7 20.0002H10V17.9402H12V23.4002C12 23.9525 11.5523 24.4002 11 24.4002C10.4477 24.4002 10 23.9525 10 23.4002V22.0002H7C4.23858 22.0002 2 19.7616 2 17.0002V9.00019C2 8.44791 2.44772 8.00019 3 8.00019H12V6.38019C12.0219 5.04755 13.1173 3.98349 14.45 4.00019H21.55C22.8827 3.98349 23.9781 5.04755 24 6.38019V8.00019ZM22 8.00019H14V6.43019C14.0107 6.18951 14.2091 5.99996 14.45 6.00019H21.56C21.6741 5.99751 21.7845 6.04026 21.867 6.11903C21.9495 6.1978 21.9974 6.30614 22 6.42019V8.00019Z\"/><path d=\"M4 30.0002H32V23.3102C32.7479 22.9632 33.4258 22.4819 34 21.8902V30.0002C34 31.1048 33.1046 32.0002 32 32.0002H4C2.89543 32.0002 2 31.1048 2 30.0002V21.8902C2.57843 22.4795 3.25525 22.9635 4 23.3202V30.0002Z\"/><path d=\"M26 23.4002C26 23.9525 25.5523 24.4002 25 24.4002C24.4477 24.4002 24 23.9525 24 23.4002V22.0002H14V20.0002H24V17.9402H26V23.4002Z\"/>',solid:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M30 20.0344C32.1957 20.0021 33.9678 18.2262 34 16.0258V8.00861H24V6.43524C23.9974 5.78672 23.7377 5.16581 23.2783 4.70912C22.8188 4.25243 22.1971 3.99736 21.55 4.00002H14.45C13.8029 3.99736 13.1812 4.25243 12.7217 4.70912C12.2623 5.16581 12.0026 5.78672 12 6.43524V8.00861H2V16.0258C2.03273 18.2455 3.83484 20.0291 6.05 20.0344H10.05V17.9499H12.05V23.6621C12.05 24.2156 11.6023 24.6643 11.05 24.6643C10.4977 24.6643 10.05 24.2156 10.05 23.6621V22.0988H6.06C4.55893 22.0963 3.11218 21.5357 2 20.5254V29.9957C2 31.1026 2.89543 32 4 32H32C33.1046 32 34 31.1026 34 29.9957V20.5254C32.8909 21.5407 31.4422 22.1021 29.94 22.0988H28V20.0344H30ZM14 6.43522C14.0107 6.19402 14.2091 6.00406 14.45 6.00429H21.55C21.7909 6.00406 21.9893 6.19402 22 6.43522V8.00859H14V6.43522ZM25 24.6642C25.5523 24.6642 26 24.2155 26 23.662V17.9498H24V20.0343H14V22.0987H24V23.662C24 24.2155 24.4477 24.6642 25 24.6642Z\"/>'})];export{H as suitcaseIcon,C as suitcaseIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,UAAU;EAACC,CAAC,GAAC,CAAC,UAAU,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,mkCAAmkC;IAACC,KAAK,EAAC;EAAg7B,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,YAAY,EAACJ,CAAC,IAAIK,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}