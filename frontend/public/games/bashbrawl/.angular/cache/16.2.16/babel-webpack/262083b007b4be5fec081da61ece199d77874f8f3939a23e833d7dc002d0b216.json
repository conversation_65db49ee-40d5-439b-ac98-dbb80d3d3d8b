{"ast": null, "code": "import _isArray from \"./_isArray.js\";\nimport _isTransformer from \"./_isTransformer.js\";\n/**\n * Returns a function that dispatches with different strategies based on the\n * object in list position (last argument). If it is an array, executes [fn].\n * Otherwise, if it has a function with one of the given method names, it will\n * execute that function (functor case). Otherwise, if it is a transformer,\n * uses transducer created by [transducerC<PERSON>] to return a new transformer\n * (transducer case).\n * Otherwise, it will default to executing [fn].\n *\n * @private\n * @param {Array} methodNames properties to check for a custom implementation\n * @param {Function} transducerCreator transducer factory if object is transformer\n * @param {Function} fn default ramda implementation\n * @return {Function} A function that dispatches on object in list position\n */\n\nexport default function _dispatchable(methodNames, transducerCreator, fn) {\n  return function () {\n    if (arguments.length === 0) {\n      return fn();\n    }\n    var obj = arguments[arguments.length - 1];\n    if (!_isArray(obj)) {\n      var idx = 0;\n      while (idx < methodNames.length) {\n        if (typeof obj[methodNames[idx]] === 'function') {\n          return obj[methodNames[idx]].apply(obj, Array.prototype.slice.call(arguments, 0, -1));\n        }\n        idx += 1;\n      }\n      if (_isTransformer(obj)) {\n        var transducer = transducerCreator.apply(null, Array.prototype.slice.call(arguments, 0, -1));\n        return transducer(obj);\n      }\n    }\n    return fn.apply(this, arguments);\n  };\n}", "map": {"version": 3, "names": ["_isArray", "_isTransformer", "_dispatchable", "methodNames", "transducerCreator", "fn", "arguments", "length", "obj", "idx", "apply", "Array", "prototype", "slice", "call", "transducer"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_dispatchable.js"], "sourcesContent": ["import _isArray from \"./_isArray.js\";\nimport _isTransformer from \"./_isTransformer.js\";\n/**\n * Returns a function that dispatches with different strategies based on the\n * object in list position (last argument). If it is an array, executes [fn].\n * Otherwise, if it has a function with one of the given method names, it will\n * execute that function (functor case). Otherwise, if it is a transformer,\n * uses transducer created by [transducerC<PERSON>] to return a new transformer\n * (transducer case).\n * Otherwise, it will default to executing [fn].\n *\n * @private\n * @param {Array} methodNames properties to check for a custom implementation\n * @param {Function} transducerCreator transducer factory if object is transformer\n * @param {Function} fn default ramda implementation\n * @return {Function} A function that dispatches on object in list position\n */\n\nexport default function _dispatchable(methodNames, transducerCreator, fn) {\n  return function () {\n    if (arguments.length === 0) {\n      return fn();\n    }\n\n    var obj = arguments[arguments.length - 1];\n\n    if (!_isArray(obj)) {\n      var idx = 0;\n\n      while (idx < methodNames.length) {\n        if (typeof obj[methodNames[idx]] === 'function') {\n          return obj[methodNames[idx]].apply(obj, Array.prototype.slice.call(arguments, 0, -1));\n        }\n\n        idx += 1;\n      }\n\n      if (_isTransformer(obj)) {\n        var transducer = transducerCreator.apply(null, Array.prototype.slice.call(arguments, 0, -1));\n        return transducer(obj);\n      }\n    }\n\n    return fn.apply(this, arguments);\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,OAAOC,cAAc,MAAM,qBAAqB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,aAAaA,CAACC,WAAW,EAAEC,iBAAiB,EAAEC,EAAE,EAAE;EACxE,OAAO,YAAY;IACjB,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;MAC1B,OAAOF,EAAE,CAAC,CAAC;IACb;IAEA,IAAIG,GAAG,GAAGF,SAAS,CAACA,SAAS,CAACC,MAAM,GAAG,CAAC,CAAC;IAEzC,IAAI,CAACP,QAAQ,CAACQ,GAAG,CAAC,EAAE;MAClB,IAAIC,GAAG,GAAG,CAAC;MAEX,OAAOA,GAAG,GAAGN,WAAW,CAACI,MAAM,EAAE;QAC/B,IAAI,OAAOC,GAAG,CAACL,WAAW,CAACM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;UAC/C,OAAOD,GAAG,CAACL,WAAW,CAACM,GAAG,CAAC,CAAC,CAACC,KAAK,CAACF,GAAG,EAAEG,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACR,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvF;QAEAG,GAAG,IAAI,CAAC;MACV;MAEA,IAAIR,cAAc,CAACO,GAAG,CAAC,EAAE;QACvB,IAAIO,UAAU,GAAGX,iBAAiB,CAACM,KAAK,CAAC,IAAI,EAAEC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACR,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5F,OAAOS,UAAU,CAACP,GAAG,CAAC;MACxB;IACF;IAEA,OAAOH,EAAE,CAACK,KAAK,CAAC,IAAI,EAAEJ,SAAS,CAAC;EAClC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}