{"ast": null, "code": "import _curry1 from \"./_curry1.js\";\nimport _isArray from \"./_isArray.js\";\nimport _isString from \"./_isString.js\";\n/**\n * Tests whether or not an object is similar to an array.\n *\n * @private\n * @category Type\n * @category List\n * @sig * -> Boolean\n * @param {*} x The object to test.\n * @return {Boolean} `true` if `x` has a numeric length property and extreme indices defined; `false` otherwise.\n * @example\n *\n *      _isArrayLike([]); //=> true\n *      _isArrayLike(true); //=> false\n *      _isArrayLike({}); //=> false\n *      _isArrayLike({length: 10}); //=> false\n *      _isArrayLike({0: 'zero', 9: 'nine', length: 10}); //=> true\n *      _isArrayLike({nodeType: 1, length: 1}) // => false\n */\n\nvar _isArrayLike = /*#__PURE__*/\n_curry1(function isArrayLike(x) {\n  if (_isArray(x)) {\n    return true;\n  }\n  if (!x) {\n    return false;\n  }\n  if (typeof x !== 'object') {\n    return false;\n  }\n  if (_isString(x)) {\n    return false;\n  }\n  if (x.length === 0) {\n    return true;\n  }\n  if (x.length > 0) {\n    return x.hasOwnProperty(0) && x.hasOwnProperty(x.length - 1);\n  }\n  return false;\n});\nexport default _isArrayLike;", "map": {"version": 3, "names": ["_curry1", "_isArray", "_isString", "_isArrayLike", "isArrayLike", "x", "length", "hasOwnProperty"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_isArrayLike.js"], "sourcesContent": ["import _curry1 from \"./_curry1.js\";\nimport _isArray from \"./_isArray.js\";\nimport _isString from \"./_isString.js\";\n/**\n * Tests whether or not an object is similar to an array.\n *\n * @private\n * @category Type\n * @category List\n * @sig * -> Boolean\n * @param {*} x The object to test.\n * @return {Boolean} `true` if `x` has a numeric length property and extreme indices defined; `false` otherwise.\n * @example\n *\n *      _isArrayLike([]); //=> true\n *      _isArrayLike(true); //=> false\n *      _isArrayLike({}); //=> false\n *      _isArrayLike({length: 10}); //=> false\n *      _isArrayLike({0: 'zero', 9: 'nine', length: 10}); //=> true\n *      _isArrayLike({nodeType: 1, length: 1}) // => false\n */\n\nvar _isArrayLike =\n/*#__PURE__*/\n_curry1(function isArrayLike(x) {\n  if (_isArray(x)) {\n    return true;\n  }\n\n  if (!x) {\n    return false;\n  }\n\n  if (typeof x !== 'object') {\n    return false;\n  }\n\n  if (_isString(x)) {\n    return false;\n  }\n\n  if (x.length === 0) {\n    return true;\n  }\n\n  if (x.length > 0) {\n    return x.hasOwnProperty(0) && x.hasOwnProperty(x.length - 1);\n  }\n\n  return false;\n});\n\nexport default _isArrayLike;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,cAAc;AAClC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,SAAS,MAAM,gBAAgB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,YAAY,GAChB;AACAH,OAAO,CAAC,SAASI,WAAWA,CAACC,CAAC,EAAE;EAC9B,IAAIJ,QAAQ,CAACI,CAAC,CAAC,EAAE;IACf,OAAO,IAAI;EACb;EAEA,IAAI,CAACA,CAAC,EAAE;IACN,OAAO,KAAK;EACd;EAEA,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;IACzB,OAAO,KAAK;EACd;EAEA,IAAIH,SAAS,CAACG,CAAC,CAAC,EAAE;IAChB,OAAO,KAAK;EACd;EAEA,IAAIA,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;IAClB,OAAO,IAAI;EACb;EAEA,IAAID,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;IAChB,OAAOD,CAAC,CAACE,cAAc,CAAC,CAAC,CAAC,IAAIF,CAAC,CAACE,cAAc,CAACF,CAAC,CAACC,MAAM,GAAG,CAAC,CAAC;EAC9D;EAEA,OAAO,KAAK;AACd,CAAC,CAAC;AAEF,eAAeH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}