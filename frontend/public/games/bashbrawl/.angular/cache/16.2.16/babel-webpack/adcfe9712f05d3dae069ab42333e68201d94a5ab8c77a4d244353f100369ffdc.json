{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst r = \"step-forward-2\",\n  o = [\"step-forward-2\", C({\n    outline: '<path d=\"M7.08893 6.52003C6.92643 6.67645 6.79734 6.86315 6.70921 7.06921C6.62108 7.27526 6.57568 7.49652 6.57568 7.72003C6.57568 7.94353 6.62108 8.16479 6.70921 8.37085C6.79734 8.5769 6.92643 8.76361 7.08893 8.92003L16.6872 18L7.12964 27.08C6.82977 27.3991 6.66554 27.8188 6.67065 28.253C6.67576 28.6872 6.84982 29.1031 7.15712 29.4152C7.46442 29.7274 7.88166 29.9121 8.32323 29.9316C8.7648 29.9511 9.19721 29.8038 9.53176 29.52L21.6645 18L9.53176 6.47003C9.36851 6.31482 9.17559 6.19293 8.96413 6.11137C8.75267 6.02982 8.52686 5.99022 8.29974 5.99487C8.07262 5.99952 7.84869 6.04832 7.64087 6.13846C7.43305 6.22859 7.24546 6.35828 7.08893 6.52003Z\"/><path d=\"M26.8453 5C26.3864 5 25.9463 5.17911 25.6218 5.49792C25.2973 5.81673 25.115 6.24913 25.115 6.7V29.3C25.115 29.7509 25.2973 30.1833 25.6218 30.5021C25.9463 30.8209 26.3864 31 26.8453 31C27.3043 31 27.7444 30.8209 28.0689 30.5021C28.3934 30.1833 28.5757 29.7509 28.5757 29.3V6.7C28.5757 6.24913 28.3934 5.81673 28.0689 5.49792C27.7444 5.17911 27.3043 5 26.8453 5Z\"/>'\n  })];\nexport { o as stepForward2Icon, r as stepForward2IconName };", "map": {"version": 3, "names": ["renderIcon", "C", "r", "o", "outline", "stepForward2Icon", "stepForward2IconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/step-forward-2.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const r=\"step-forward-2\",o=[\"step-forward-2\",C({outline:'<path d=\"M7.08893 6.52003C6.92643 6.67645 6.79734 6.86315 6.70921 7.06921C6.62108 7.27526 6.57568 7.49652 6.57568 7.72003C6.57568 7.94353 6.62108 8.16479 6.70921 8.37085C6.79734 8.5769 6.92643 8.76361 7.08893 8.92003L16.6872 18L7.12964 27.08C6.82977 27.3991 6.66554 27.8188 6.67065 28.253C6.67576 28.6872 6.84982 29.1031 7.15712 29.4152C7.46442 29.7274 7.88166 29.9121 8.32323 29.9316C8.7648 29.9511 9.19721 29.8038 9.53176 29.52L21.6645 18L9.53176 6.47003C9.36851 6.31482 9.17559 6.19293 8.96413 6.11137C8.75267 6.02982 8.52686 5.99022 8.29974 5.99487C8.07262 5.99952 7.84869 6.04832 7.64087 6.13846C7.43305 6.22859 7.24546 6.35828 7.08893 6.52003Z\"/><path d=\"M26.8453 5C26.3864 5 25.9463 5.17911 25.6218 5.49792C25.2973 5.81673 25.115 6.24913 25.115 6.7V29.3C25.115 29.7509 25.2973 30.1833 25.6218 30.5021C25.9463 30.8209 26.3864 31 26.8453 31C27.3043 31 27.7444 30.8209 28.0689 30.5021C28.3934 30.1833 28.5757 29.7509 28.5757 29.3V6.7C28.5757 6.24913 28.3934 5.81673 28.0689 5.49792C27.7444 5.17911 27.3043 5 26.8453 5Z\"/>'})];export{o as stepForward2Icon,r as stepForward2IconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,gBAAgB;EAACC,CAAC,GAAC,CAAC,gBAAgB,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAmgC,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,gBAAgB,EAACH,CAAC,IAAII,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}