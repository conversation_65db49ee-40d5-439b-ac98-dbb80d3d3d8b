{"ast": null, "code": "import { focusable as e } from \"./focus.js\";\nfunction r(r, t = 10) {\n  return n(r, t).filter(r => e(r));\n}\nfunction n(e, r = 10) {\n  return Array.from(t(e)).reduce((e, o) => [...e, [o, [...Array.from(t(o)).map(e => [e, n(e, r)])]]], []).flat(r);\n}\nfunction t(e) {\n  if (e.documentElement) return e.documentElement.children;\n  if (e.shadowRoot) return e.shadowRoot.children;\n  if (e.assignedElements) {\n    const r = e.assignedElements();\n    return r.length ? r : e.children;\n  }\n  return e.children;\n}\nexport { t as getChildren, n as getFlattenedDOMTree, r as getFlattenedFocusableItems };", "map": {"version": 3, "names": ["focusable", "e", "r", "t", "n", "filter", "Array", "from", "reduce", "o", "map", "flat", "documentElement", "children", "shadowRoot", "assignedElements", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getFlattenedDOMTree", "getFlattenedFocusableItems"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/traversal.js"], "sourcesContent": ["import{focusable as e}from\"./focus.js\";function r(r,t=10){return n(r,t).filter((r=>e(r)))}function n(e,r=10){return Array.from(t(e)).reduce(((e,o)=>[...e,[o,[...Array.from(t(o)).map((e=>[e,n(e,r)]))]]]),[]).flat(r)}function t(e){if(e.documentElement)return e.documentElement.children;if(e.shadowRoot)return e.shadowRoot.children;if(e.assignedElements){const r=e.assignedElements();return r.length?r:e.children}return e.children}export{t as getChildren,n as getFlattenedDOMTree,r as getFlattenedFocusableItems};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,QAAK,YAAY;AAAC,SAASC,CAACA,CAACA,CAAC,EAACC,CAAC,GAAC,EAAE,EAAC;EAAC,OAAOC,CAAC,CAACF,CAAC,EAACC,CAAC,CAAC,CAACE,MAAM,CAAEH,CAAC,IAAED,CAAC,CAACC,CAAC,CAAE,CAAC;AAAA;AAAC,SAASE,CAACA,CAACH,CAAC,EAACC,CAAC,GAAC,EAAE,EAAC;EAAC,OAAOI,KAAK,CAACC,IAAI,CAACJ,CAAC,CAACF,CAAC,CAAC,CAAC,CAACO,MAAM,CAAE,CAACP,CAAC,EAACQ,CAAC,KAAG,CAAC,GAAGR,CAAC,EAAC,CAACQ,CAAC,EAAC,CAAC,GAAGH,KAAK,CAACC,IAAI,CAACJ,CAAC,CAACM,CAAC,CAAC,CAAC,CAACC,GAAG,CAAET,CAAC,IAAE,CAACA,CAAC,EAACG,CAAC,CAACH,CAAC,EAACC,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAACS,IAAI,CAACT,CAAC,CAAC;AAAA;AAAC,SAASC,CAACA,CAACF,CAAC,EAAC;EAAC,IAAGA,CAAC,CAACW,eAAe,EAAC,OAAOX,CAAC,CAACW,eAAe,CAACC,QAAQ;EAAC,IAAGZ,CAAC,CAACa,UAAU,EAAC,OAAOb,CAAC,CAACa,UAAU,CAACD,QAAQ;EAAC,IAAGZ,CAAC,CAACc,gBAAgB,EAAC;IAAC,MAAMb,CAAC,GAACD,CAAC,CAACc,gBAAgB,CAAC,CAAC;IAAC,OAAOb,CAAC,CAACc,MAAM,GAACd,CAAC,GAACD,CAAC,CAACY,QAAQ;EAAA;EAAC,OAAOZ,CAAC,CAACY,QAAQ;AAAA;AAAC,SAAOV,CAAC,IAAIc,WAAW,EAACb,CAAC,IAAIc,mBAAmB,EAAChB,CAAC,IAAIiB,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}