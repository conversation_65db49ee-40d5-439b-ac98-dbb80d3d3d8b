{"ast": null, "code": "import { createId as e } from \"../utils/identity.js\";\nfunction t() {\n  return (t, r) => {\n    const i = {\n      get() {\n        const i = void 0 !== r ? r : t.key;\n        return this[\"__\" + i] || (this[\"__\" + i] = e()), this[\"__\" + i];\n      },\n      enumerable: !0,\n      configurable: !0\n    };\n    return void 0 !== r ? ((e, t, r) => {\n      Object.defineProperty(t, r, e);\n    })(i, t, r) : ((e, t) => ({\n      kind: \"method\",\n      placement: \"prototype\",\n      key: t.key,\n      descriptor: e\n    }))(i, t);\n  };\n}\nexport { t as id };", "map": {"version": 3, "names": ["createId", "e", "t", "r", "i", "get", "key", "enumerable", "configurable", "Object", "defineProperty", "kind", "placement", "descriptor", "id"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/decorators/id.js"], "sourcesContent": ["import{createId as e}from\"../utils/identity.js\";function t(){return(t,r)=>{const i={get(){const i=void 0!==r?r:t.key;return this[\"__\"+i]||(this[\"__\"+i]=e()),this[\"__\"+i]},enumerable:!0,configurable:!0};return void 0!==r?((e,t,r)=>{Object.defineProperty(t,r,e)})(i,t,r):((e,t)=>({kind:\"method\",placement:\"prototype\",key:t.key,descriptor:e}))(i,t)}}export{t as id};\n"], "mappings": "AAAA,SAAOA,QAAQ,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,OAAM,CAACA,CAAC,EAACC,CAAC,KAAG;IAAC,MAAMC,CAAC,GAAC;MAACC,GAAGA,CAAA,EAAE;QAAC,MAAMD,CAAC,GAAC,KAAK,CAAC,KAAGD,CAAC,GAACA,CAAC,GAACD,CAAC,CAACI,GAAG;QAAC,OAAO,IAAI,CAAC,IAAI,GAACF,CAAC,CAAC,KAAG,IAAI,CAAC,IAAI,GAACA,CAAC,CAAC,GAACH,CAAC,CAAC,CAAC,CAAC,EAAC,IAAI,CAAC,IAAI,GAACG,CAAC,CAAC;MAAA,CAAC;MAACG,UAAU,EAAC,CAAC,CAAC;MAACC,YAAY,EAAC,CAAC;IAAC,CAAC;IAAC,OAAO,KAAK,CAAC,KAAGL,CAAC,GAAC,CAAC,CAACF,CAAC,EAACC,CAAC,EAACC,CAAC,KAAG;MAACM,MAAM,CAACC,cAAc,CAACR,CAAC,EAACC,CAAC,EAACF,CAAC,CAAC;IAAA,CAAC,EAAEG,CAAC,EAACF,CAAC,EAACC,CAAC,CAAC,GAAC,CAAC,CAACF,CAAC,EAACC,CAAC,MAAI;MAACS,IAAI,EAAC,QAAQ;MAACC,SAAS,EAAC,WAAW;MAACN,GAAG,EAACJ,CAAC,CAACI,GAAG;MAACO,UAAU,EAACZ;IAAC,CAAC,CAAC,EAAEG,CAAC,EAACF,CAAC,CAAC;EAAA,CAAC;AAAA;AAAC,SAAOA,CAAC,IAAIY,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}