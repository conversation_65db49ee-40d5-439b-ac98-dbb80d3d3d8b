{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst d = \"cloud\",\n  t = [\"cloud\", C({\n    outline: '<path d=\"M26 32H12C6.49 32 2 27.51 2 22C2 17.5 5.04 13.55 9.33 12.36C10.53 7.48 14.91 4 20 4C26.07 4 31 8.93 31 15C31 15.86 30.9 16.71 30.71 17.54C32.76 19.04 34 21.45 34 24C34 28.41 30.41 32 26 32ZM20 6C15.66 6 11.94 9.1 11.16 13.37C11.09 13.77 10.78 14.09 10.38 14.17C6.69 14.93 4.01 18.23 4.01 22C4.01 26.41 7.6 30 12.01 30H26.01C29.32 30 32.01 27.31 32.01 24C32.01 21.89 30.88 19.91 29.05 18.84C28.65 18.6 28.47 18.13 28.6 17.68C28.87 16.81 29 15.91 29 15C29 10.04 24.96 6 20 6Z\"/>',\n    outlineAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M22.9233 4.39351C21.9922 4.13702 21.012 4 20 4C14.91 4 10.53 7.48 9.33 12.36C5.04 13.55 2 17.5 2 22C2 27.51 6.49 32 12 32H26C30.41 32 34 28.41 34 24C34 21.45 32.76 19.04 30.71 17.54C30.8973 16.7218 30.9971 15.8842 30.9999 15.0367H28.9999C28.9965 15.9342 28.8664 16.8217 28.6 17.68C28.47 18.13 28.65 18.6 29.05 18.84C30.88 19.91 32.01 21.89 32.01 24C32.01 27.31 29.32 30 26.01 30H12.01C7.6 30 4.01 26.41 4.01 22C4.01 18.23 6.69 14.93 10.38 14.17C10.78 14.09 11.09 13.77 11.16 13.37C11.94 9.1 15.66 6 20 6C20.6321 6 21.2493 6.06562 21.8451 6.19041L22.9233 4.39351Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M30.8096 12.9537C30.544 12.9843 30.2738 13 30 13C29.5732 13 29.1553 12.9618 28.7496 12.8886C28.9132 13.5661 29 14.2732 29 15C29 15.91 28.87 16.81 28.6 17.68C28.47 18.13 28.65 18.6 29.05 18.84C30.88 19.91 32.01 21.89 32.01 24C32.01 27.31 29.32 30 26.01 30H12.01C7.6 30 4.01 26.41 4.01 22C4.01 18.23 6.69 14.93 10.38 14.17C10.78 14.09 11.09 13.77 11.16 13.37C11.94 9.1 15.66 6 20 6C21.0584 6 22.0749 6.18395 23.0191 6.52153C23.0065 6.34935 23 6.17543 23 6C23 5.47244 23.0584 4.95851 23.169 4.46431C22.1653 4.16232 21.1014 4 20 4C14.91 4 10.53 7.48 9.33 12.36C5.04 13.55 2 17.5 2 22C2 27.51 6.49 32 12 32H26C30.41 32 34 28.41 34 24C34 21.45 32.76 19.04 30.71 17.54C30.9 16.71 31 15.86 31 15C31 14.3008 30.9346 13.6168 30.8096 12.9537Z\"/>',\n    solid: '<path d=\"M30.71 17.54C30.91 16.71 31 15.86 31 15C31 8.93 26.07 4 20 4C14.91 4 10.53 7.48 9.33 12.36C5.04 13.55 2 17.5 2 22C2 27.51 6.49 32 12 32H26C30.41 32 34 28.41 34 24C34 21.45 32.75 19.04 30.71 17.54Z\"/>',\n    solidAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M22.9233 4.39351L19.5362 10.0387C18.8703 11.0423 18.8204 12.3342 19.4206 13.3893C20.0233 14.4489 21.1577 15.0604 22.3395 15.0367H30.9999C30.9974 15.8842 30.9072 16.7218 30.71 17.54C32.75 19.04 34 21.45 34 24C34 28.41 30.41 32 26 32H12C6.49 32 2 27.51 2 22C2 17.5 5.04 13.55 9.33 12.36C10.53 7.48 14.91 4 20 4C21.012 4 21.9922 4.13703 22.9233 4.39351Z\"/>',\n    solidBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M30.8096 12.9537C30.544 12.9843 30.2738 13 30 13C26.134 13 23 9.86599 23 6C23 5.47244 23.0584 4.95851 23.169 4.46431C22.1653 4.16232 21.1014 4 20 4C14.91 4 10.53 7.48 9.33 12.36C5.04 13.55 2 17.5 2 22C2 27.51 6.49 32 12 32H26C30.41 32 34 28.41 34 24C34 21.45 32.75 19.04 30.71 17.54C30.91 16.71 31 15.86 31 15C31 14.3008 30.9346 13.6168 30.8096 12.9537Z\"/>'\n  })];\nexport { t as cloudIcon, d as cloudIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "d", "t", "outline", "outlineAlerted", "outlineBadged", "solid", "solidAlerted", "solidBadged", "cloudIcon", "cloudIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/cloud.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const d=\"cloud\",t=[\"cloud\",C({outline:'<path d=\"M26 32H12C6.49 32 2 27.51 2 22C2 17.5 5.04 13.55 9.33 12.36C10.53 7.48 14.91 4 20 4C26.07 4 31 8.93 31 15C31 15.86 30.9 16.71 30.71 17.54C32.76 19.04 34 21.45 34 24C34 28.41 30.41 32 26 32ZM20 6C15.66 6 11.94 9.1 11.16 13.37C11.09 13.77 10.78 14.09 10.38 14.17C6.69 14.93 4.01 18.23 4.01 22C4.01 26.41 7.6 30 12.01 30H26.01C29.32 30 32.01 27.31 32.01 24C32.01 21.89 30.88 19.91 29.05 18.84C28.65 18.6 28.47 18.13 28.6 17.68C28.87 16.81 29 15.91 29 15C29 10.04 24.96 6 20 6Z\"/>',outlineAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M22.9233 4.39351C21.9922 4.13702 21.012 4 20 4C14.91 4 10.53 7.48 9.33 12.36C5.04 13.55 2 17.5 2 22C2 27.51 6.49 32 12 32H26C30.41 32 34 28.41 34 24C34 21.45 32.76 19.04 30.71 17.54C30.8973 16.7218 30.9971 15.8842 30.9999 15.0367H28.9999C28.9965 15.9342 28.8664 16.8217 28.6 17.68C28.47 18.13 28.65 18.6 29.05 18.84C30.88 19.91 32.01 21.89 32.01 24C32.01 27.31 29.32 30 26.01 30H12.01C7.6 30 4.01 26.41 4.01 22C4.01 18.23 6.69 14.93 10.38 14.17C10.78 14.09 11.09 13.77 11.16 13.37C11.94 9.1 15.66 6 20 6C20.6321 6 21.2493 6.06562 21.8451 6.19041L22.9233 4.39351Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M30.8096 12.9537C30.544 12.9843 30.2738 13 30 13C29.5732 13 29.1553 12.9618 28.7496 12.8886C28.9132 13.5661 29 14.2732 29 15C29 15.91 28.87 16.81 28.6 17.68C28.47 18.13 28.65 18.6 29.05 18.84C30.88 19.91 32.01 21.89 32.01 24C32.01 27.31 29.32 30 26.01 30H12.01C7.6 30 4.01 26.41 4.01 22C4.01 18.23 6.69 14.93 10.38 14.17C10.78 14.09 11.09 13.77 11.16 13.37C11.94 9.1 15.66 6 20 6C21.0584 6 22.0749 6.18395 23.0191 6.52153C23.0065 6.34935 23 6.17543 23 6C23 5.47244 23.0584 4.95851 23.169 4.46431C22.1653 4.16232 21.1014 4 20 4C14.91 4 10.53 7.48 9.33 12.36C5.04 13.55 2 17.5 2 22C2 27.51 6.49 32 12 32H26C30.41 32 34 28.41 34 24C34 21.45 32.76 19.04 30.71 17.54C30.9 16.71 31 15.86 31 15C31 14.3008 30.9346 13.6168 30.8096 12.9537Z\"/>',solid:'<path d=\"M30.71 17.54C30.91 16.71 31 15.86 31 15C31 8.93 26.07 4 20 4C14.91 4 10.53 7.48 9.33 12.36C5.04 13.55 2 17.5 2 22C2 27.51 6.49 32 12 32H26C30.41 32 34 28.41 34 24C34 21.45 32.75 19.04 30.71 17.54Z\"/>',solidAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M22.9233 4.39351L19.5362 10.0387C18.8703 11.0423 18.8204 12.3342 19.4206 13.3893C20.0233 14.4489 21.1577 15.0604 22.3395 15.0367H30.9999C30.9974 15.8842 30.9072 16.7218 30.71 17.54C32.75 19.04 34 21.45 34 24C34 28.41 30.41 32 26 32H12C6.49 32 2 27.51 2 22C2 17.5 5.04 13.55 9.33 12.36C10.53 7.48 14.91 4 20 4C21.012 4 21.9922 4.13703 22.9233 4.39351Z\"/>',solidBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M30.8096 12.9537C30.544 12.9843 30.2738 13 30 13C26.134 13 23 9.86599 23 6C23 5.47244 23.0584 4.95851 23.169 4.46431C22.1653 4.16232 21.1014 4 20 4C14.91 4 10.53 7.48 9.33 12.36C5.04 13.55 2 17.5 2 22C2 27.51 6.49 32 12 32H26C30.41 32 34 28.41 34 24C34 21.45 32.75 19.04 30.71 17.54C30.91 16.71 31 15.86 31 15C31 14.3008 30.9346 13.6168 30.8096 12.9537Z\"/>'})];export{t as cloudIcon,d as cloudIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,OAAO;EAACC,CAAC,GAAC,CAAC,OAAO,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,ueAAue;IAACC,cAAc,EAAC,m6BAAm6B;IAACC,aAAa,EAAC,u2BAAu2B;IAACC,KAAK,EAAC,kNAAkN;IAACC,YAAY,EAAC,+sBAA+sB;IAACC,WAAW,EAAC;EAA6e,CAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,SAAS,EAACR,CAAC,IAAIS,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}