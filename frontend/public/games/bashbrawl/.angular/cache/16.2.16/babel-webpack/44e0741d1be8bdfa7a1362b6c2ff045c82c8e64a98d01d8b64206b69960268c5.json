{"ast": null, "code": "import { renderIcon as r } from \"../icon.renderer.js\";\nconst o = \"arrow\",\n  C = [\"arrow\", r({\n    outline: '<path d=\"M27.6504 15.6341L17.9951 6L8.33981 15.6341C8.02869 15.8717 7.88432 16.2695 7.9703 16.6521C8.05629 17.0347 8.35685 17.3319 8.73952 17.4127C9.12219 17.4935 9.51667 17.343 9.74912 17.0276L16.9956 9.81955V28.9975C16.9956 29.5512 17.4431 30 17.9951 30C18.5471 30 18.9946 29.5512 18.9946 28.9975V9.81955L26.2411 17.0276C26.633 17.4179 27.2662 17.4157 27.6554 17.0226C28.0445 16.6295 28.0423 15.9943 27.6504 15.604V15.6341Z\"/>'\n  })];\nexport { C as arrowIcon, o as arrowIconName };", "map": {"version": 3, "names": ["renderIcon", "r", "o", "C", "outline", "arrowIcon", "arrowIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/arrow.js"], "sourcesContent": ["import{renderIcon as r}from\"../icon.renderer.js\";const o=\"arrow\",C=[\"arrow\",r({outline:'<path d=\"M27.6504 15.6341L17.9951 6L8.33981 15.6341C8.02869 15.8717 7.88432 16.2695 7.9703 16.6521C8.05629 17.0347 8.35685 17.3319 8.73952 17.4127C9.12219 17.4935 9.51667 17.343 9.74912 17.0276L16.9956 9.81955V28.9975C16.9956 29.5512 17.4431 30 17.9951 30C18.5471 30 18.9946 29.5512 18.9946 28.9975V9.81955L26.2411 17.0276C26.633 17.4179 27.2662 17.4157 27.6554 17.0226C28.0445 16.6295 28.0423 15.9943 27.6504 15.604V15.6341Z\"/>'})];export{C as arrowIcon,o as arrowIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,OAAO;EAACC,CAAC,GAAC,CAAC,OAAO,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAA8a,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,SAAS,EAACH,CAAC,IAAII,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}