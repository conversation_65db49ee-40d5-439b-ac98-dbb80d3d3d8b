{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"align-left\",\n  e = [\"align-left\", C({\n    outline: '<path d=\"M23 26.5H6V28.5H23C23.55 28.5 24 28.05 24 27.5C24 26.95 23.55 26.5 23 26.5ZM29 20.5H6V22.5H29C29.55 22.5 30 22.05 30 21.5C30 20.95 29.55 20.5 29 20.5ZM6 8.5V10.5H29C29.55 10.5 30 10.05 30 9.5C30 8.95 29.55 8.5 29 8.5H6ZM23 14.5H6V16.5H23C23.55 16.5 24 16.05 24 15.5C24 14.95 23.55 14.5 23 14.5Z\"/>'\n  })];\nexport { e as alignLeftIcon, H as alignLeftIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "e", "outline", "alignLeftIcon", "alignLeftIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/align-left.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"align-left\",e=[\"align-left\",C({outline:'<path d=\"M23 26.5H6V28.5H23C23.55 28.5 24 28.05 24 27.5C24 26.95 23.55 26.5 23 26.5ZM29 20.5H6V22.5H29C29.55 22.5 30 22.05 30 21.5C30 20.95 29.55 20.5 29 20.5ZM6 8.5V10.5H29C29.55 10.5 30 10.05 30 9.5C30 8.95 29.55 8.5 29 8.5H6ZM23 14.5H6V16.5H23C23.55 16.5 24 16.05 24 15.5C24 14.95 23.55 14.5 23 14.5Z\"/>'})];export{e as alignLeftIcon,H as alignLeftIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,YAAY;EAACC,CAAC,GAAC,CAAC,YAAY,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAoT,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,aAAa,EAACH,CAAC,IAAII,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}