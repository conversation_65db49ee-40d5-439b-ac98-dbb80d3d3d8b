{"ast": null, "code": "function n() {\n  return (navigator.vendor.match(/apple/i) || \"\").length > 0;\n}\nfunction t() {\n  return (navigator.platform.match(/Win/i) || \"\").length > 0;\n}\nexport { n as isSafari, t as isWindows };", "map": {"version": 3, "names": ["n", "navigator", "vendor", "match", "length", "t", "platform", "<PERSON><PERSON><PERSON><PERSON>", "isWindows"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/browser.js"], "sourcesContent": ["function n(){return(navigator.vendor.match(/apple/i)||\"\").length>0}function t(){return(navigator.platform.match(/Win/i)||\"\").length>0}export{n as isSafari,t as isWindows};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAM,CAACC,SAAS,CAACC,MAAM,CAACC,KAAK,CAAC,QAAQ,CAAC,IAAE,EAAE,EAAEC,MAAM,GAAC,CAAC;AAAA;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,OAAM,CAACJ,SAAS,CAACK,QAAQ,CAACH,KAAK,CAAC,MAAM,CAAC,IAAE,EAAE,EAAEC,MAAM,GAAC,CAAC;AAAA;AAAC,SAAOJ,CAAC,IAAIO,QAAQ,EAACF,CAAC,IAAIG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}