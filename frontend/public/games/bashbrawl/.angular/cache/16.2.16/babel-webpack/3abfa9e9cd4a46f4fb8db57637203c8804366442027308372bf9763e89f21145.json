{"ast": null, "code": "export default function _xArrayReduce(xf, acc, list) {\n  var idx = 0;\n  var len = list.length;\n  while (idx < len) {\n    acc = xf['@@transducer/step'](acc, list[idx]);\n    if (acc && acc['@@transducer/reduced']) {\n      acc = acc['@@transducer/value'];\n      break;\n    }\n    idx += 1;\n  }\n  return xf['@@transducer/result'](acc);\n}", "map": {"version": 3, "names": ["_xArrayReduce", "xf", "acc", "list", "idx", "len", "length"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_xArrayReduce.js"], "sourcesContent": ["export default function _xArrayReduce(xf, acc, list) {\n  var idx = 0;\n  var len = list.length;\n\n  while (idx < len) {\n    acc = xf['@@transducer/step'](acc, list[idx]);\n\n    if (acc && acc['@@transducer/reduced']) {\n      acc = acc['@@transducer/value'];\n      break;\n    }\n\n    idx += 1;\n  }\n\n  return xf['@@transducer/result'](acc);\n}"], "mappings": "AAAA,eAAe,SAASA,aAAaA,CAACC,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnD,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIC,GAAG,GAAGF,IAAI,CAACG,MAAM;EAErB,OAAOF,GAAG,GAAGC,GAAG,EAAE;IAChBH,GAAG,GAAGD,EAAE,CAAC,mBAAmB,CAAC,CAACC,GAAG,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;IAE7C,IAAIF,GAAG,IAAIA,GAAG,CAAC,sBAAsB,CAAC,EAAE;MACtCA,GAAG,GAAGA,GAAG,CAAC,oBAAoB,CAAC;MAC/B;IACF;IAEAE,GAAG,IAAI,CAAC;EACV;EAEA,OAAOH,EAAE,CAAC,qBAAqB,CAAC,CAACC,GAAG,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}