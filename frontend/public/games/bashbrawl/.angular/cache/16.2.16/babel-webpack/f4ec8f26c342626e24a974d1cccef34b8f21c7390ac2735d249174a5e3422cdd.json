{"ast": null, "code": "import { GlobalStateService as e } from \"./global.service.js\";\nimport { interpolateNaively as t } from \"../utils/string.js\";\nimport { mergeObjects as o, objectNaiveDeepEquals as n } from \"../utils/identity.js\";\nconst i = {\n  actions: {\n    sort: \"Sort\",\n    expand: \"Expand\",\n    close: \"Close\",\n    resize: \"Resize\",\n    filter: \"Filter\"\n  },\n  alert: {\n    closeButtonAriaLabel: \"Close\",\n    loading: \"Loading\",\n    info: \"Info\",\n    success: \"Success\",\n    warning: \"Warning\",\n    danger: \"Error\"\n  },\n  dropdown: {\n    open: \"Open\"\n  },\n  file: {\n    browse: \"browse\",\n    files: \"files\",\n    removeFile: \"remove file\"\n  },\n  modal: {\n    closeButtonAriaLabel: \"Close modal\",\n    contentStart: \"Beginning of modal content\",\n    contentBox: \"Scrollable modal body\",\n    contentEnd: \"End of modal content\"\n  },\n  navigation: {\n    navigationElement: \"navigation\",\n    navigationLabel: \"navigation menu\",\n    navigationAbridgedText: \"View abridged menu\",\n    navigationUnabridgedText: \"View unabridged menu\"\n  },\n  overlay: {\n    closeButtonAriaLabel: \"Close dialog\",\n    contentStart: \"Beginning of dialog content\",\n    contentEnd: \"End of dialog content\"\n  },\n  popup: {\n    closeButtonAriaLabel: \"Close popup\",\n    contentStart: \"Beginning of popup content\",\n    contentEnd: \"End of popup content\"\n  },\n  password: {\n    showButtonAriaLabel: \"Show password\",\n    hideButtonAriaLabel: \"Hide password\"\n  },\n  progress: {\n    loading: \"Loading\",\n    looping: \"Loading\"\n  },\n  treeview: {\n    loading: \"Loading\"\n  },\n  grid: {\n    resizeColumn: \"Resize Column\",\n    closeDetails: \"Close Details\",\n    noData: \"No Results Found\",\n    rowDetailStart: \"Start of row details\",\n    rowDetailEnd: \"End of row details\",\n    footerEnd: \"End of grid rows\",\n    action: \"Action\",\n    dropTarget: \"Drop Item\",\n    pagination: {\n      label: \"grid pagination\",\n      firstPage: \"go to first page\",\n      previousPage: \"go to previous page\",\n      nextPage: \"go to next page\",\n      lastPage: \"go to last page\",\n      pageSize: \"rows per page\",\n      page: \"page\"\n    }\n  }\n};\nclass a {\n  static get keys() {\n    return 0 === Object.keys(e.state.i18nRegistry).length && (e.state.i18nRegistry = o(i, e.state.i18nRegistry)), o(i, e.state.i18nRegistry);\n  }\n  static findKey(e) {\n    const t = a.keys;\n    return Object.keys(t).find(o => n(t[o], e));\n  }\n  static get(e) {\n    return e && a.keys[e] || {};\n  }\n  static reset() {\n    e.state.i18nRegistry = o({}, i);\n  }\n  static hydrate(e, o) {\n    return JSON.parse(t(JSON.stringify(e), o));\n  }\n  static localize(t) {\n    e.state.i18nRegistry = o(i, e.state.i18nRegistry, t);\n  }\n}\nexport { a as I18nService, i as componentStringsDefault };", "map": {"version": 3, "names": ["GlobalStateService", "e", "interpolate<PERSON><PERSON><PERSON><PERSON>", "t", "mergeObjects", "o", "objectNaiveDeepEquals", "n", "i", "actions", "sort", "expand", "close", "resize", "filter", "alert", "closeButtonAriaLabel", "loading", "info", "success", "warning", "danger", "dropdown", "open", "file", "browse", "files", "removeFile", "modal", "contentStart", "contentBox", "contentEnd", "navigation", "navigationElement", "navigationLabel", "navigationAbridgedText", "navigationUnabridgedText", "overlay", "popup", "password", "showButtonAriaLabel", "hideButtonAriaLabel", "progress", "looping", "treeview", "grid", "resizeColumn", "closeDetails", "noData", "rowDetailStart", "rowDetailEnd", "footerEnd", "action", "drop<PERSON>ar<PERSON>", "pagination", "label", "firstPage", "previousPage", "nextPage", "lastPage", "pageSize", "page", "a", "keys", "Object", "state", "i18nRegistry", "length", "<PERSON><PERSON><PERSON>", "find", "get", "reset", "hydrate", "JSON", "parse", "stringify", "localize", "I18nService", "componentStringsDefault"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/services/i18n.service.js"], "sourcesContent": ["import{GlobalStateService as e}from\"./global.service.js\";import{interpolateNaively as t}from\"../utils/string.js\";import{mergeObjects as o,objectNaiveDeepEquals as n}from\"../utils/identity.js\";const i={actions:{sort:\"Sort\",expand:\"Expand\",close:\"Close\",resize:\"Resize\",filter:\"Filter\"},alert:{closeButtonAriaLabel:\"Close\",loading:\"Loading\",info:\"Info\",success:\"Success\",warning:\"Warning\",danger:\"Error\"},dropdown:{open:\"Open\"},file:{browse:\"browse\",files:\"files\",removeFile:\"remove file\"},modal:{closeButtonAriaLabel:\"Close modal\",contentStart:\"Beginning of modal content\",contentBox:\"Scrollable modal body\",contentEnd:\"End of modal content\"},navigation:{navigationElement:\"navigation\",navigationLabel:\"navigation menu\",navigationAbridgedText:\"View abridged menu\",navigationUnabridgedText:\"View unabridged menu\"},overlay:{closeButtonAriaLabel:\"Close dialog\",contentStart:\"Beginning of dialog content\",contentEnd:\"End of dialog content\"},popup:{closeButtonAriaLabel:\"Close popup\",contentStart:\"Beginning of popup content\",contentEnd:\"End of popup content\"},password:{showButtonAriaLabel:\"Show password\",hideButtonAriaLabel:\"Hide password\"},progress:{loading:\"Loading\",looping:\"Loading\"},treeview:{loading:\"Loading\"},grid:{resizeColumn:\"Resize Column\",closeDetails:\"Close Details\",noData:\"No Results Found\",rowDetailStart:\"Start of row details\",rowDetailEnd:\"End of row details\",footerEnd:\"End of grid rows\",action:\"Action\",dropTarget:\"Drop Item\",pagination:{label:\"grid pagination\",firstPage:\"go to first page\",previousPage:\"go to previous page\",nextPage:\"go to next page\",lastPage:\"go to last page\",pageSize:\"rows per page\",page:\"page\"}}};class a{static get keys(){return 0===Object.keys(e.state.i18nRegistry).length&&(e.state.i18nRegistry=o(i,e.state.i18nRegistry)),o(i,e.state.i18nRegistry)}static findKey(e){const t=a.keys;return Object.keys(t).find((o=>n(t[o],e)))}static get(e){return e&&a.keys[e]||{}}static reset(){e.state.i18nRegistry=o({},i)}static hydrate(e,o){return JSON.parse(t(JSON.stringify(e),o))}static localize(t){e.state.i18nRegistry=o(i,e.state.i18nRegistry,t)}}export{a as I18nService,i as componentStringsDefault};\n"], "mappings": "AAAA,SAAOA,kBAAkB,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,kBAAkB,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,YAAY,IAAIC,CAAC,EAACC,qBAAqB,IAAIC,CAAC,QAAK,sBAAsB;AAAC,MAAMC,CAAC,GAAC;EAACC,OAAO,EAAC;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,QAAQ;IAACC,KAAK,EAAC,OAAO;IAACC,MAAM,EAAC,QAAQ;IAACC,MAAM,EAAC;EAAQ,CAAC;EAACC,KAAK,EAAC;IAACC,oBAAoB,EAAC,OAAO;IAACC,OAAO,EAAC,SAAS;IAACC,IAAI,EAAC,MAAM;IAACC,OAAO,EAAC,SAAS;IAACC,OAAO,EAAC,SAAS;IAACC,MAAM,EAAC;EAAO,CAAC;EAACC,QAAQ,EAAC;IAACC,IAAI,EAAC;EAAM,CAAC;EAACC,IAAI,EAAC;IAACC,MAAM,EAAC,QAAQ;IAACC,KAAK,EAAC,OAAO;IAACC,UAAU,EAAC;EAAa,CAAC;EAACC,KAAK,EAAC;IAACZ,oBAAoB,EAAC,aAAa;IAACa,YAAY,EAAC,4BAA4B;IAACC,UAAU,EAAC,uBAAuB;IAACC,UAAU,EAAC;EAAsB,CAAC;EAACC,UAAU,EAAC;IAACC,iBAAiB,EAAC,YAAY;IAACC,eAAe,EAAC,iBAAiB;IAACC,sBAAsB,EAAC,oBAAoB;IAACC,wBAAwB,EAAC;EAAsB,CAAC;EAACC,OAAO,EAAC;IAACrB,oBAAoB,EAAC,cAAc;IAACa,YAAY,EAAC,6BAA6B;IAACE,UAAU,EAAC;EAAuB,CAAC;EAACO,KAAK,EAAC;IAACtB,oBAAoB,EAAC,aAAa;IAACa,YAAY,EAAC,4BAA4B;IAACE,UAAU,EAAC;EAAsB,CAAC;EAACQ,QAAQ,EAAC;IAACC,mBAAmB,EAAC,eAAe;IAACC,mBAAmB,EAAC;EAAe,CAAC;EAACC,QAAQ,EAAC;IAACzB,OAAO,EAAC,SAAS;IAAC0B,OAAO,EAAC;EAAS,CAAC;EAACC,QAAQ,EAAC;IAAC3B,OAAO,EAAC;EAAS,CAAC;EAAC4B,IAAI,EAAC;IAACC,YAAY,EAAC,eAAe;IAACC,YAAY,EAAC,eAAe;IAACC,MAAM,EAAC,kBAAkB;IAACC,cAAc,EAAC,sBAAsB;IAACC,YAAY,EAAC,oBAAoB;IAACC,SAAS,EAAC,kBAAkB;IAACC,MAAM,EAAC,QAAQ;IAACC,UAAU,EAAC,WAAW;IAACC,UAAU,EAAC;MAACC,KAAK,EAAC,iBAAiB;MAACC,SAAS,EAAC,kBAAkB;MAACC,YAAY,EAAC,qBAAqB;MAACC,QAAQ,EAAC,iBAAiB;MAACC,QAAQ,EAAC,iBAAiB;MAACC,QAAQ,EAAC,eAAe;MAACC,IAAI,EAAC;IAAM;EAAC;AAAC,CAAC;AAAC,MAAMC,CAAC;EAAC,WAAWC,IAAIA,CAAA,EAAE;IAAC,OAAO,CAAC,KAAGC,MAAM,CAACD,IAAI,CAAC9D,CAAC,CAACgE,KAAK,CAACC,YAAY,CAAC,CAACC,MAAM,KAAGlE,CAAC,CAACgE,KAAK,CAACC,YAAY,GAAC7D,CAAC,CAACG,CAAC,EAACP,CAAC,CAACgE,KAAK,CAACC,YAAY,CAAC,CAAC,EAAC7D,CAAC,CAACG,CAAC,EAACP,CAAC,CAACgE,KAAK,CAACC,YAAY,CAAC;EAAA;EAAC,OAAOE,OAAOA,CAACnE,CAAC,EAAC;IAAC,MAAME,CAAC,GAAC2D,CAAC,CAACC,IAAI;IAAC,OAAOC,MAAM,CAACD,IAAI,CAAC5D,CAAC,CAAC,CAACkE,IAAI,CAAEhE,CAAC,IAAEE,CAAC,CAACJ,CAAC,CAACE,CAAC,CAAC,EAACJ,CAAC,CAAE,CAAC;EAAA;EAAC,OAAOqE,GAAGA,CAACrE,CAAC,EAAC;IAAC,OAAOA,CAAC,IAAE6D,CAAC,CAACC,IAAI,CAAC9D,CAAC,CAAC,IAAE,CAAC,CAAC;EAAA;EAAC,OAAOsE,KAAKA,CAAA,EAAE;IAACtE,CAAC,CAACgE,KAAK,CAACC,YAAY,GAAC7D,CAAC,CAAC,CAAC,CAAC,EAACG,CAAC,CAAC;EAAA;EAAC,OAAOgE,OAAOA,CAACvE,CAAC,EAACI,CAAC,EAAC;IAAC,OAAOoE,IAAI,CAACC,KAAK,CAACvE,CAAC,CAACsE,IAAI,CAACE,SAAS,CAAC1E,CAAC,CAAC,EAACI,CAAC,CAAC,CAAC;EAAA;EAAC,OAAOuE,QAAQA,CAACzE,CAAC,EAAC;IAACF,CAAC,CAACgE,KAAK,CAACC,YAAY,GAAC7D,CAAC,CAACG,CAAC,EAACP,CAAC,CAACgE,KAAK,CAACC,YAAY,EAAC/D,CAAC,CAAC;EAAA;AAAC;AAAC,SAAO2D,CAAC,IAAIe,WAAW,EAACrE,CAAC,IAAIsE,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}