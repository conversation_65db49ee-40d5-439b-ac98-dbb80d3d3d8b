{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nfunction t() {\n  return t => t.addInitializer(t => new e(t));\n}\nclass e {\n  constructor(t) {\n    this.host = t, this.selectionActive = !1, t.addController(this);\n  }\n  get enabled() {\n    return !1 !== this.host.rangeSelection && !Array.from(this.host.rows).find(t => t.draggable);\n  }\n  hostConnected() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.host.updateComplete, _this.setupKeyboardListeners(), _this.setupMouseEvents();\n    })();\n  }\n  setupMouseEvents() {\n    const t = this.host.grid.shadowRoot ? this.host.grid.shadowRoot : this.host.grid;\n    t.addEventListener(\"mousedown\", t => {\n      this.enabled && 1 === t.buttons && !t.ctrlKey && this.setFirstCell(t);\n    }), t.addEventListener(\"mouseover\", t => {\n      this.enabled && this.setActiveCell(t.composedPath().find(t => Array.from(this.host.cells).includes(t)));\n    }), t.addEventListener(\"mouseup\", () => {\n      this.enabled && this.stopSelection();\n    });\n  }\n  setupKeyboardListeners() {\n    this.host.addEventListener(\"cdsKeyChange\", t => {\n      this.enabled && t.detail.code && (this.setActiveCell(t.detail.activeItem), t.detail.shiftKey || (this.stopSelection(), this.resetAllActiveCells(), this.host.dispatchEvent(new CustomEvent(\"rangeSelectionChange\", {\n        detail: []\n      }))));\n    }), this.host.addEventListener(\"keydown\", t => {\n      this.enabled && \"ShiftLeft\" === t.code && t.shiftKey && !this.selectionActive && this.setFirstCell(t);\n    });\n  }\n  setFirstCell(t) {\n    const e = t.composedPath().find(t => Array.from(this.host.cells).includes(t));\n    e && (this.firstCell = e, this.selectionActive = !0, this.resetAllActiveCells());\n  }\n  setActiveCell(t) {\n    t && this.selectionActive && (this.activeCell = t, this.calculateSelection());\n  }\n  stopSelection() {\n    this.selectionActive = !1;\n  }\n  resetAllActiveCells() {\n    this.host.cells.forEach(t => t.removeAttribute(\"highlight\"));\n  }\n  calculateSelection() {\n    const t = parseInt(this.firstCell.ariaColIndex),\n      e = parseInt(this.activeCell.ariaColIndex),\n      s = parseInt(this.firstCell.parentElement?.ariaRowIndex),\n      i = parseInt(this.activeCell.parentElement?.ariaRowIndex);\n    this.resetAllActiveCells(), this.host.cells.forEach(l => {\n      const h = parseInt(l.ariaColIndex),\n        n = parseInt(l.parentElement?.ariaRowIndex);\n      (t <= e && h >= t && h <= e || t >= e && h <= t && h >= e) && (s <= i && n >= s && n <= i || s >= i && n <= s && n >= i) && l.setAttribute(\"highlight\", \"\");\n    }), this.host.dispatchEvent(new CustomEvent(\"rangeSelectionChange\", {\n      detail: Array.from(this.host.cells).filter(t => t.hasAttribute(\"highlight\"))\n    }));\n  }\n}\nexport { e as GridRangeSelectionController, t as gridRangeSelection };", "map": {"version": 3, "names": ["t", "addInitializer", "e", "constructor", "host", "selectionActive", "addController", "enabled", "rangeSelection", "Array", "from", "rows", "find", "draggable", "hostConnected", "_this", "_asyncToGenerator", "updateComplete", "setupKeyboardListeners", "setupMouseEvents", "grid", "shadowRoot", "addEventListener", "buttons", "ctrl<PERSON>ey", "setFirstCell", "setActiveCell", "<PERSON><PERSON><PERSON>", "cells", "includes", "stopSelection", "detail", "code", "activeItem", "shift<PERSON>ey", "resetAllActiveCells", "dispatchEvent", "CustomEvent", "firstCell", "activeCell", "calculateSelection", "for<PERSON>ach", "removeAttribute", "parseInt", "ariaColIndex", "s", "parentElement", "ariaRowIndex", "i", "l", "h", "n", "setAttribute", "filter", "hasAttribute", "GridRangeSelectionController", "gridRangeSelection"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/grid-range-selection.controller.js"], "sourcesContent": ["function t(){return t=>t.addInitializer((t=>new e(t)))}class e{constructor(t){this.host=t,this.selectionActive=!1,t.addController(this)}get enabled(){return!1!==this.host.rangeSelection&&!Array.from(this.host.rows).find((t=>t.draggable))}async hostConnected(){await this.host.updateComplete,this.setupKeyboardListeners(),this.setupMouseEvents()}setupMouseEvents(){const t=this.host.grid.shadowRoot?this.host.grid.shadowRoot:this.host.grid;t.addEventListener(\"mousedown\",(t=>{this.enabled&&1===t.buttons&&!t.ctrlKey&&this.setFirstCell(t)})),t.addEventListener(\"mouseover\",(t=>{this.enabled&&this.setActiveCell(t.composedPath().find((t=>Array.from(this.host.cells).includes(t))))})),t.addEventListener(\"mouseup\",(()=>{this.enabled&&this.stopSelection()}))}setupKeyboardListeners(){this.host.addEventListener(\"cdsKeyChange\",(t=>{this.enabled&&t.detail.code&&(this.setActiveCell(t.detail.activeItem),t.detail.shiftKey||(this.stopSelection(),this.resetAllActiveCells(),this.host.dispatchEvent(new CustomEvent(\"rangeSelectionChange\",{detail:[]}))))})),this.host.addEventListener(\"keydown\",(t=>{this.enabled&&\"ShiftLeft\"===t.code&&t.shiftKey&&!this.selectionActive&&this.setFirstCell(t)}))}setFirstCell(t){const e=t.composedPath().find((t=>Array.from(this.host.cells).includes(t)));e&&(this.firstCell=e,this.selectionActive=!0,this.resetAllActiveCells())}setActiveCell(t){t&&this.selectionActive&&(this.activeCell=t,this.calculateSelection())}stopSelection(){this.selectionActive=!1}resetAllActiveCells(){this.host.cells.forEach((t=>t.removeAttribute(\"highlight\")))}calculateSelection(){const t=parseInt(this.firstCell.ariaColIndex),e=parseInt(this.activeCell.ariaColIndex),s=parseInt(this.firstCell.parentElement?.ariaRowIndex),i=parseInt(this.activeCell.parentElement?.ariaRowIndex);this.resetAllActiveCells(),this.host.cells.forEach((l=>{const h=parseInt(l.ariaColIndex),n=parseInt(l.parentElement?.ariaRowIndex);(t<=e&&h>=t&&h<=e||t>=e&&h<=t&&h>=e)&&(s<=i&&n>=s&&n<=i||s>=i&&n<=s&&n>=i)&&l.setAttribute(\"highlight\",\"\")})),this.host.dispatchEvent(new CustomEvent(\"rangeSelectionChange\",{detail:Array.from(this.host.cells).filter((t=>t.hasAttribute(\"highlight\")))}))}}export{e as GridRangeSelectionController,t as gridRangeSelection};\n"], "mappings": ";AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAOA,CAAC,IAAEA,CAAC,CAACC,cAAc,CAAED,CAAC,IAAE,IAAIE,CAAC,CAACF,CAAC,CAAE,CAAC;AAAA;AAAC,MAAME,CAAC;EAACC,WAAWA,CAACH,CAAC,EAAC;IAAC,IAAI,CAACI,IAAI,GAACJ,CAAC,EAAC,IAAI,CAACK,eAAe,GAAC,CAAC,CAAC,EAACL,CAAC,CAACM,aAAa,CAAC,IAAI,CAAC;EAAA;EAAC,IAAIC,OAAOA,CAAA,EAAE;IAAC,OAAM,CAAC,CAAC,KAAG,IAAI,CAACH,IAAI,CAACI,cAAc,IAAE,CAACC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACN,IAAI,CAACO,IAAI,CAAC,CAACC,IAAI,CAAEZ,CAAC,IAAEA,CAAC,CAACa,SAAU,CAAC;EAAA;EAAOC,aAAaA,CAAA,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAAC,MAAMD,KAAI,CAACX,IAAI,CAACa,cAAc,EAACF,KAAI,CAACG,sBAAsB,CAAC,CAAC,EAACH,KAAI,CAACI,gBAAgB,CAAC,CAAC;IAAA;EAAA;EAACA,gBAAgBA,CAAA,EAAE;IAAC,MAAMnB,CAAC,GAAC,IAAI,CAACI,IAAI,CAACgB,IAAI,CAACC,UAAU,GAAC,IAAI,CAACjB,IAAI,CAACgB,IAAI,CAACC,UAAU,GAAC,IAAI,CAACjB,IAAI,CAACgB,IAAI;IAACpB,CAAC,CAACsB,gBAAgB,CAAC,WAAW,EAAEtB,CAAC,IAAE;MAAC,IAAI,CAACO,OAAO,IAAE,CAAC,KAAGP,CAAC,CAACuB,OAAO,IAAE,CAACvB,CAAC,CAACwB,OAAO,IAAE,IAAI,CAACC,YAAY,CAACzB,CAAC,CAAC;IAAA,CAAE,CAAC,EAACA,CAAC,CAACsB,gBAAgB,CAAC,WAAW,EAAEtB,CAAC,IAAE;MAAC,IAAI,CAACO,OAAO,IAAE,IAAI,CAACmB,aAAa,CAAC1B,CAAC,CAAC2B,YAAY,CAAC,CAAC,CAACf,IAAI,CAAEZ,CAAC,IAAES,KAAK,CAACC,IAAI,CAAC,IAAI,CAACN,IAAI,CAACwB,KAAK,CAAC,CAACC,QAAQ,CAAC7B,CAAC,CAAE,CAAC,CAAC;IAAA,CAAE,CAAC,EAACA,CAAC,CAACsB,gBAAgB,CAAC,SAAS,EAAE,MAAI;MAAC,IAAI,CAACf,OAAO,IAAE,IAAI,CAACuB,aAAa,CAAC,CAAC;IAAA,CAAE,CAAC;EAAA;EAACZ,sBAAsBA,CAAA,EAAE;IAAC,IAAI,CAACd,IAAI,CAACkB,gBAAgB,CAAC,cAAc,EAAEtB,CAAC,IAAE;MAAC,IAAI,CAACO,OAAO,IAAEP,CAAC,CAAC+B,MAAM,CAACC,IAAI,KAAG,IAAI,CAACN,aAAa,CAAC1B,CAAC,CAAC+B,MAAM,CAACE,UAAU,CAAC,EAACjC,CAAC,CAAC+B,MAAM,CAACG,QAAQ,KAAG,IAAI,CAACJ,aAAa,CAAC,CAAC,EAAC,IAAI,CAACK,mBAAmB,CAAC,CAAC,EAAC,IAAI,CAAC/B,IAAI,CAACgC,aAAa,CAAC,IAAIC,WAAW,CAAC,sBAAsB,EAAC;QAACN,MAAM,EAAC;MAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAAA,CAAE,CAAC,EAAC,IAAI,CAAC3B,IAAI,CAACkB,gBAAgB,CAAC,SAAS,EAAEtB,CAAC,IAAE;MAAC,IAAI,CAACO,OAAO,IAAE,WAAW,KAAGP,CAAC,CAACgC,IAAI,IAAEhC,CAAC,CAACkC,QAAQ,IAAE,CAAC,IAAI,CAAC7B,eAAe,IAAE,IAAI,CAACoB,YAAY,CAACzB,CAAC,CAAC;IAAA,CAAE,CAAC;EAAA;EAACyB,YAAYA,CAACzB,CAAC,EAAC;IAAC,MAAME,CAAC,GAACF,CAAC,CAAC2B,YAAY,CAAC,CAAC,CAACf,IAAI,CAAEZ,CAAC,IAAES,KAAK,CAACC,IAAI,CAAC,IAAI,CAACN,IAAI,CAACwB,KAAK,CAAC,CAACC,QAAQ,CAAC7B,CAAC,CAAE,CAAC;IAACE,CAAC,KAAG,IAAI,CAACoC,SAAS,GAACpC,CAAC,EAAC,IAAI,CAACG,eAAe,GAAC,CAAC,CAAC,EAAC,IAAI,CAAC8B,mBAAmB,CAAC,CAAC,CAAC;EAAA;EAACT,aAAaA,CAAC1B,CAAC,EAAC;IAACA,CAAC,IAAE,IAAI,CAACK,eAAe,KAAG,IAAI,CAACkC,UAAU,GAACvC,CAAC,EAAC,IAAI,CAACwC,kBAAkB,CAAC,CAAC,CAAC;EAAA;EAACV,aAAaA,CAAA,EAAE;IAAC,IAAI,CAACzB,eAAe,GAAC,CAAC,CAAC;EAAA;EAAC8B,mBAAmBA,CAAA,EAAE;IAAC,IAAI,CAAC/B,IAAI,CAACwB,KAAK,CAACa,OAAO,CAAEzC,CAAC,IAAEA,CAAC,CAAC0C,eAAe,CAAC,WAAW,CAAE,CAAC;EAAA;EAACF,kBAAkBA,CAAA,EAAE;IAAC,MAAMxC,CAAC,GAAC2C,QAAQ,CAAC,IAAI,CAACL,SAAS,CAACM,YAAY,CAAC;MAAC1C,CAAC,GAACyC,QAAQ,CAAC,IAAI,CAACJ,UAAU,CAACK,YAAY,CAAC;MAACC,CAAC,GAACF,QAAQ,CAAC,IAAI,CAACL,SAAS,CAACQ,aAAa,EAAEC,YAAY,CAAC;MAACC,CAAC,GAACL,QAAQ,CAAC,IAAI,CAACJ,UAAU,CAACO,aAAa,EAAEC,YAAY,CAAC;IAAC,IAAI,CAACZ,mBAAmB,CAAC,CAAC,EAAC,IAAI,CAAC/B,IAAI,CAACwB,KAAK,CAACa,OAAO,CAAEQ,CAAC,IAAE;MAAC,MAAMC,CAAC,GAACP,QAAQ,CAACM,CAAC,CAACL,YAAY,CAAC;QAACO,CAAC,GAACR,QAAQ,CAACM,CAAC,CAACH,aAAa,EAAEC,YAAY,CAAC;MAAC,CAAC/C,CAAC,IAAEE,CAAC,IAAEgD,CAAC,IAAElD,CAAC,IAAEkD,CAAC,IAAEhD,CAAC,IAAEF,CAAC,IAAEE,CAAC,IAAEgD,CAAC,IAAElD,CAAC,IAAEkD,CAAC,IAAEhD,CAAC,MAAI2C,CAAC,IAAEG,CAAC,IAAEG,CAAC,IAAEN,CAAC,IAAEM,CAAC,IAAEH,CAAC,IAAEH,CAAC,IAAEG,CAAC,IAAEG,CAAC,IAAEN,CAAC,IAAEM,CAAC,IAAEH,CAAC,CAAC,IAAEC,CAAC,CAACG,YAAY,CAAC,WAAW,EAAC,EAAE,CAAC;IAAA,CAAE,CAAC,EAAC,IAAI,CAAChD,IAAI,CAACgC,aAAa,CAAC,IAAIC,WAAW,CAAC,sBAAsB,EAAC;MAACN,MAAM,EAACtB,KAAK,CAACC,IAAI,CAAC,IAAI,CAACN,IAAI,CAACwB,KAAK,CAAC,CAACyB,MAAM,CAAErD,CAAC,IAAEA,CAAC,CAACsD,YAAY,CAAC,WAAW,CAAE;IAAC,CAAC,CAAC,CAAC;EAAA;AAAC;AAAC,SAAOpD,CAAC,IAAIqD,4BAA4B,EAACvD,CAAC,IAAIwD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}