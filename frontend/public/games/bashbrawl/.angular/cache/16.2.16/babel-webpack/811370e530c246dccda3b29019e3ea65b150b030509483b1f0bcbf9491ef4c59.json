{"ast": null, "code": "import _indexOf from \"./_indexOf.js\";\nexport default function _includes(a, list) {\n  return _indexOf(list, a, 0) >= 0;\n}", "map": {"version": 3, "names": ["_indexOf", "_includes", "a", "list"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_includes.js"], "sourcesContent": ["import _indexOf from \"./_indexOf.js\";\nexport default function _includes(a, list) {\n  return _indexOf(list, a, 0) >= 0;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,eAAe,SAASC,SAASA,CAACC,CAAC,EAAEC,IAAI,EAAE;EACzC,OAAOH,QAAQ,CAACG,IAAI,EAAED,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}