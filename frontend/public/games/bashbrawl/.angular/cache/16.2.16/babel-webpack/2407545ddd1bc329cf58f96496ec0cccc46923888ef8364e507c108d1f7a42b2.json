{"ast": null, "code": "import { renderIcon as i } from \"../icon.renderer.js\";\nconst r = \"filter-grid-mini\",\n  l = [\"filter-grid-mini\", i({\n    outline: '<path d=\"M12,19v8.8l4,2.05V18.27A2,2,0,0,0,15.55,17L8.18,8H27.74l-7.29,8.93A2,2,0,0,0,20,18.19V31.88l4,2v-15L33.51,7.26A2,2,0,0,0,32,4H4A2,2,0,0,0,2.41,7.27Z\"/>',\n    solid: '<path d=\"M32.13,4H3.92A2,2,0,0,0,2.53,7.44L14,18.54v9.52l8,4.08V18.58L33.52,7.44A2,2,0,0,0,32.13,4Z\"/>'\n  })];\nexport { l as filterGridMiniIcon, r as filterGridMiniIconName };", "map": {"version": 3, "names": ["renderIcon", "i", "r", "l", "outline", "solid", "filterGridMiniIcon", "filterGridMiniIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/filter-grid-mini.js"], "sourcesContent": ["import{renderIcon as i}from\"../icon.renderer.js\";const r=\"filter-grid-mini\",l=[\"filter-grid-mini\",i({outline:'<path d=\"M12,19v8.8l4,2.05V18.27A2,2,0,0,0,15.55,17L8.18,8H27.74l-7.29,8.93A2,2,0,0,0,20,18.19V31.88l4,2v-15L33.51,7.26A2,2,0,0,0,32,4H4A2,2,0,0,0,2.41,7.27Z\"/>',solid:'<path d=\"M32.13,4H3.92A2,2,0,0,0,2.53,7.44L14,18.54v9.52l8,4.08V18.58L33.52,7.44A2,2,0,0,0,32.13,4Z\"/>'})];export{l as filterGridMiniIcon,r as filterGridMiniIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,kBAAkB;EAACC,CAAC,GAAC,CAAC,kBAAkB,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,kKAAkK;IAACC,KAAK,EAAC;EAAwG,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,kBAAkB,EAACJ,CAAC,IAAIK,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}