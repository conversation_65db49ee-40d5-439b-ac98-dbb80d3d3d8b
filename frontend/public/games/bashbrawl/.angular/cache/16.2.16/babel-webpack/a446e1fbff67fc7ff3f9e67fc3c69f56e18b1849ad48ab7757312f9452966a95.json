{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"rewind\",\n  V = [\"rewind\", C({\n    outline: '<path d=\"M33.44 4.1C33.1 3.93 32.69 3.97 32.39 4.2L21 12.91V5C21 4.62 20.78 4.27 20.44 4.1C20.1 3.93 19.69 3.97 19.39 4.2L2.39 17.21C2.14 17.4 2 17.69 2 18C2 18.31 2.15 18.61 2.39 18.79L19.39 31.79C19.57 31.93 19.78 32 20 32C20.15 32 20.3 31.97 20.44 31.9C20.78 31.73 21 31.38 21 31V23.08L32.39 31.79C32.69 32.02 33.1 32.06 33.44 31.89C33.78 31.72 34 31.37 34 30.99V5C34 4.62 33.78 4.27 33.44 4.1ZM32 28.97L20.61 20.26C20.31 20.03 19.9 19.99 19.56 20.16C19.22 20.33 19 20.68 19 21.06V28.98L4.65 18L19 7.02V14.94C19 15.32 19.22 15.67 19.56 15.84C19.9 16.01 20.31 15.97 20.61 15.74L32 7.02V28.97Z\"/>',\n    solid: '<path d=\"M33.44 4.1C33.1 3.93 32.69 3.97 32.39 4.2L21 12.91V5C21 4.62 20.78 4.27 20.44 4.1C20.1 3.93 19.69 3.97 19.39 4.2L2.39 17.21C2.14 17.4 2 17.69 2 18C2 18.31 2.15 18.61 2.39 18.79L19.39 31.79C19.57 31.93 19.78 32 20 32C20.15 32 20.3 31.97 20.44 31.9C20.78 31.73 21 31.38 21 31V23.08L32.39 31.79C32.69 32.02 33.1 32.06 33.44 31.89C33.78 31.72 34 31.37 34 30.99V5C34 4.62 33.78 4.27 33.44 4.1Z\"/>'\n  })];\nexport { V as rewindIcon, L as rewindIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "V", "outline", "solid", "rewindIcon", "rewindIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/rewind.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"rewind\",V=[\"rewind\",C({outline:'<path d=\"M33.44 4.1C33.1 3.93 32.69 3.97 32.39 4.2L21 12.91V5C21 4.62 20.78 4.27 20.44 4.1C20.1 3.93 19.69 3.97 19.39 4.2L2.39 17.21C2.14 17.4 2 17.69 2 18C2 18.31 2.15 18.61 2.39 18.79L19.39 31.79C19.57 31.93 19.78 32 20 32C20.15 32 20.3 31.97 20.44 31.9C20.78 31.73 21 31.38 21 31V23.08L32.39 31.79C32.69 32.02 33.1 32.06 33.44 31.89C33.78 31.72 34 31.37 34 30.99V5C34 4.62 33.78 4.27 33.44 4.1ZM32 28.97L20.61 20.26C20.31 20.03 19.9 19.99 19.56 20.16C19.22 20.33 19 20.68 19 21.06V28.98L4.65 18L19 7.02V14.94C19 15.32 19.22 15.67 19.56 15.84C19.9 16.01 20.31 15.97 20.61 15.74L32 7.02V28.97Z\"/>',solid:'<path d=\"M33.44 4.1C33.1 3.93 32.69 3.97 32.39 4.2L21 12.91V5C21 4.62 20.78 4.27 20.44 4.1C20.1 3.93 19.69 3.97 19.39 4.2L2.39 17.21C2.14 17.4 2 17.69 2 18C2 18.31 2.15 18.61 2.39 18.79L19.39 31.79C19.57 31.93 19.78 32 20 32C20.15 32 20.3 31.97 20.44 31.9C20.78 31.73 21 31.38 21 31V23.08L32.39 31.79C32.69 32.02 33.1 32.06 33.44 31.89C33.78 31.72 34 31.37 34 30.99V5C34 4.62 33.78 4.27 33.44 4.1Z\"/>'})];export{V as rewindIcon,L as rewindIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,ulBAAulB;IAACC,KAAK,EAAC;EAAkZ,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,UAAU,EAACJ,CAAC,IAAIK,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}