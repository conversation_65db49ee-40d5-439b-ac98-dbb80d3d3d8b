{"ast": null, "code": "import { html as t } from \"lit\";\nimport { ifDefined as e } from \"lit/directives/if-defined.js\";\nimport { stopEvent as i } from \"../utils/events.js\";\nimport { onAnyKey as s } from \"../utils/keycodes.js\";\nimport { renderAfter as o } from \"../utils/lit.js\";\nfunction r() {\n  return t => t.addInitializer(t => new h(t));\n}\nclass h {\n  constructor(t) {\n    this.host = t, this.triggerNativeButtonBehaviorHandler = this.triggerNativeButtonBehavior.bind(this), this.emulateKeyBoardEventBehaviorHandler = this.emulateKeyBoardEventBehavior.bind(this), this.host.addController(this);\n  }\n  hostUpdated() {\n    this.setButtonType(), this.setupNativeButtonBehavior();\n  }\n  setButtonType() {\n    !this.host.type && this.host.closest(\"form\") && (this.host.type = \"submit\");\n  }\n  setupNativeButtonBehavior() {\n    this.host.readonly || this.host.disabled ? (this.host.removeEventListener(\"click\", this.triggerNativeButtonBehaviorHandler), this.host.removeEventListener(\"keyup\", this.emulateKeyBoardEventBehaviorHandler)) : (this.host.addEventListener(\"click\", this.triggerNativeButtonBehaviorHandler), this.host.addEventListener(\"keyup\", this.emulateKeyBoardEventBehaviorHandler));\n  }\n  emulateKeyBoardEventBehavior(t) {\n    s([\"enter\", \"space\"], t, () => {\n      \"submit\" === this.host.type ? this.triggerNativeButtonBehavior(t) : this.host.click(), i(t);\n    });\n  }\n  triggerNativeButtonBehavior(s) {\n    if (this.host.disabled) i(s);else if (!s.defaultPrevented) {\n      const i = o(t`<button aria-hidden=\"true\" role=\"presentation\" ?disabled=\"${this.host.disabled}\" tabindex=\"-1\" style=\"display:none!important\" value=\"${e(this.host.value)}\" name=\"${e(this.host.name)}\" type=\"${e(this.host.type)}\"></button>`, this.host);\n      i?.dispatchEvent(new MouseEvent(\"click\", {\n        relatedTarget: this.host,\n        composed: !0\n      })), i?.remove();\n    }\n  }\n}\nexport { h as ButtonSubmitController, r as buttonSubmit };", "map": {"version": 3, "names": ["html", "t", "ifDefined", "e", "stopEvent", "i", "onAnyKey", "s", "renderAfter", "o", "r", "addInitializer", "h", "constructor", "host", "triggerNativeButtonBehaviorHandler", "triggerNativeButtonBehavior", "bind", "emulateKeyBoardEventBehaviorHandler", "emulateKeyBoardEventBehavior", "addController", "hostUpdated", "setButtonType", "setupNativeButtonBehavior", "type", "closest", "readonly", "disabled", "removeEventListener", "addEventListener", "click", "defaultPrevented", "value", "name", "dispatchEvent", "MouseEvent", "relatedTarget", "composed", "remove", "ButtonSubmitController", "buttonSubmit"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/button-submit.controller.js"], "sourcesContent": ["import{html as t}from\"lit\";import{ifDefined as e}from\"lit/directives/if-defined.js\";import{stopEvent as i}from\"../utils/events.js\";import{onAnyKey as s}from\"../utils/keycodes.js\";import{renderAfter as o}from\"../utils/lit.js\";function r(){return t=>t.addInitializer((t=>new h(t)))}class h{constructor(t){this.host=t,this.triggerNativeButtonBehaviorHandler=this.triggerNativeButtonBehavior.bind(this),this.emulateKeyBoardEventBehaviorHandler=this.emulateKeyBoardEventBehavior.bind(this),this.host.addController(this)}hostUpdated(){this.setButtonType(),this.setupNativeButtonBehavior()}setButtonType(){!this.host.type&&this.host.closest(\"form\")&&(this.host.type=\"submit\")}setupNativeButtonBehavior(){this.host.readonly||this.host.disabled?(this.host.removeEventListener(\"click\",this.triggerNativeButtonBehaviorHandler),this.host.removeEventListener(\"keyup\",this.emulateKeyBoardEventBehaviorHandler)):(this.host.addEventListener(\"click\",this.triggerNativeButtonBehaviorHandler),this.host.addEventListener(\"keyup\",this.emulateKeyBoardEventBehaviorHandler))}emulateKeyBoardEventBehavior(t){s([\"enter\",\"space\"],t,(()=>{\"submit\"===this.host.type?this.triggerNativeButtonBehavior(t):this.host.click(),i(t)}))}triggerNativeButtonBehavior(s){if(this.host.disabled)i(s);else if(!s.defaultPrevented){const i=o(t`<button aria-hidden=\"true\" role=\"presentation\" ?disabled=\"${this.host.disabled}\" tabindex=\"-1\" style=\"display:none!important\" value=\"${e(this.host.value)}\" name=\"${e(this.host.name)}\" type=\"${e(this.host.type)}\"></button>`,this.host);i?.dispatchEvent(new MouseEvent(\"click\",{relatedTarget:this.host,composed:!0})),i?.remove()}}}export{h as ButtonSubmitController,r as buttonSubmit};\n"], "mappings": "AAAA,SAAOA,IAAI,IAAIC,CAAC,QAAK,KAAK;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,iBAAiB;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,OAAOT,CAAC,IAAEA,CAAC,CAACU,cAAc,CAAEV,CAAC,IAAE,IAAIW,CAAC,CAACX,CAAC,CAAE,CAAC;AAAA;AAAC,MAAMW,CAAC;EAACC,WAAWA,CAACZ,CAAC,EAAC;IAAC,IAAI,CAACa,IAAI,GAACb,CAAC,EAAC,IAAI,CAACc,kCAAkC,GAAC,IAAI,CAACC,2BAA2B,CAACC,IAAI,CAAC,IAAI,CAAC,EAAC,IAAI,CAACC,mCAAmC,GAAC,IAAI,CAACC,4BAA4B,CAACF,IAAI,CAAC,IAAI,CAAC,EAAC,IAAI,CAACH,IAAI,CAACM,aAAa,CAAC,IAAI,CAAC;EAAA;EAACC,WAAWA,CAAA,EAAE;IAAC,IAAI,CAACC,aAAa,CAAC,CAAC,EAAC,IAAI,CAACC,yBAAyB,CAAC,CAAC;EAAA;EAACD,aAAaA,CAAA,EAAE;IAAC,CAAC,IAAI,CAACR,IAAI,CAACU,IAAI,IAAE,IAAI,CAACV,IAAI,CAACW,OAAO,CAAC,MAAM,CAAC,KAAG,IAAI,CAACX,IAAI,CAACU,IAAI,GAAC,QAAQ,CAAC;EAAA;EAACD,yBAAyBA,CAAA,EAAE;IAAC,IAAI,CAACT,IAAI,CAACY,QAAQ,IAAE,IAAI,CAACZ,IAAI,CAACa,QAAQ,IAAE,IAAI,CAACb,IAAI,CAACc,mBAAmB,CAAC,OAAO,EAAC,IAAI,CAACb,kCAAkC,CAAC,EAAC,IAAI,CAACD,IAAI,CAACc,mBAAmB,CAAC,OAAO,EAAC,IAAI,CAACV,mCAAmC,CAAC,KAAG,IAAI,CAACJ,IAAI,CAACe,gBAAgB,CAAC,OAAO,EAAC,IAAI,CAACd,kCAAkC,CAAC,EAAC,IAAI,CAACD,IAAI,CAACe,gBAAgB,CAAC,OAAO,EAAC,IAAI,CAACX,mCAAmC,CAAC,CAAC;EAAA;EAACC,4BAA4BA,CAAClB,CAAC,EAAC;IAACM,CAAC,CAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAACN,CAAC,EAAE,MAAI;MAAC,QAAQ,KAAG,IAAI,CAACa,IAAI,CAACU,IAAI,GAAC,IAAI,CAACR,2BAA2B,CAACf,CAAC,CAAC,GAAC,IAAI,CAACa,IAAI,CAACgB,KAAK,CAAC,CAAC,EAACzB,CAAC,CAACJ,CAAC,CAAC;IAAA,CAAE,CAAC;EAAA;EAACe,2BAA2BA,CAACT,CAAC,EAAC;IAAC,IAAG,IAAI,CAACO,IAAI,CAACa,QAAQ,EAACtB,CAAC,CAACE,CAAC,CAAC,CAAC,KAAK,IAAG,CAACA,CAAC,CAACwB,gBAAgB,EAAC;MAAC,MAAM1B,CAAC,GAACI,CAAC,CAACR,CAAE,6DAA4D,IAAI,CAACa,IAAI,CAACa,QAAS,yDAAwDxB,CAAC,CAAC,IAAI,CAACW,IAAI,CAACkB,KAAK,CAAE,WAAU7B,CAAC,CAAC,IAAI,CAACW,IAAI,CAACmB,IAAI,CAAE,WAAU9B,CAAC,CAAC,IAAI,CAACW,IAAI,CAACU,IAAI,CAAE,aAAY,EAAC,IAAI,CAACV,IAAI,CAAC;MAACT,CAAC,EAAE6B,aAAa,CAAC,IAAIC,UAAU,CAAC,OAAO,EAAC;QAACC,aAAa,EAAC,IAAI,CAACtB,IAAI;QAACuB,QAAQ,EAAC,CAAC;MAAC,CAAC,CAAC,CAAC,EAAChC,CAAC,EAAEiC,MAAM,CAAC,CAAC;IAAA;EAAC;AAAC;AAAC,SAAO1B,CAAC,IAAI2B,sBAAsB,EAAC7B,CAAC,IAAI8B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}