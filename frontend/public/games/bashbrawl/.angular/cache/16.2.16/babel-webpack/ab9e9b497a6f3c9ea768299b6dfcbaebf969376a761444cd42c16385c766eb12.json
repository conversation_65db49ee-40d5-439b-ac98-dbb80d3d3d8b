{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"on-holiday\",\n  H = [\"on-holiday\", C({\n    outline: '<path d=\"M31.1275 14.83L27.5775 11.88C27.0975 11.46 26.4575 11.25 25.8275 11.32L21.8875 11.68L17.5575 8.22C17.3775 8.08 17.1575 8 16.9375 8H13.9375C13.5575 8 13.2175 8.21 13.0475 8.55C12.8775 8.89 12.9175 9.29 13.1375 9.6L15.1675 12.31L11.8375 12.62L12.6075 14.56L21.2675 13.76L21.3075 13.79L21.3375 13.75L26.0175 13.32C26.1075 13.32 26.1875 13.34 26.2775 13.41L29.2875 15.92L29.2475 16.01H21.9275C21.6275 16.01 21.3475 16.14 21.1575 16.37L16.4575 22.01H15.3175L16.8775 17.33C16.9775 17.03 16.9275 16.69 16.7375 16.43C16.5475 16.17 16.2475 16.01 15.9275 16.01H10.6075L8.8575 11.64C8.6475 11.13 8.0675 10.88 7.5575 11.08C7.0475 11.29 6.7975 11.87 6.9975 12.38L8.9975 17.38C9.1475 17.76 9.5175 18.01 9.9275 18.01H14.5375L12.9775 22.69C12.8775 22.99 12.9275 23.33 13.1175 23.59C13.3075 23.85 13.6075 24.01 13.9275 24.01H16.9275C17.2275 24.01 17.5075 23.88 17.6975 23.65L22.3975 18.01H29.9275C30.3375 18.01 30.7075 17.76 30.8575 17.37L31.3975 15.97C31.5575 15.57 31.4375 15.11 31.1075 14.84L31.1275 14.83ZM17.5075 12.09L15.9375 10H16.5875L19.0275 11.95L17.5075 12.09ZM18.9375 3C10.1175 3 2.9375 8.83 2.9375 16C2.9375 23.17 10.1175 29 18.9375 29C20.9075 29 22.8275 28.71 24.6575 28.14L29.2275 32.71C29.4175 32.9 29.6775 33 29.9375 33C30.0675 33 30.1975 32.98 30.3175 32.92C30.6875 32.77 30.9375 32.4 30.9375 32V24.59C33.5175 22.21 34.9375 19.18 34.9375 16C34.9375 8.83 27.7575 3 18.9375 3ZM29.2875 23.38C29.0675 23.57 28.9375 23.85 28.9375 24.14V29.59L25.6375 26.29C25.4475 26.1 25.1875 26 24.9275 26C24.8175 26 24.7075 26.02 24.5975 26.06C22.7975 26.69 20.8975 27.01 18.9275 27.01C11.2075 27.01 4.9275 22.08 4.9275 16.01C4.9275 9.94 11.2175 5 18.9375 5C26.6575 5 32.9375 9.93 32.9375 16C32.9375 18.72 31.6375 21.35 29.2875 23.38Z\"/>',\n    solid: '<path d=\"M18.9375 3C10.1175 3 2.9375 8.83 2.9375 16C2.9375 23.17 10.1175 29 18.9375 29C20.9075 29 22.8275 28.71 24.6575 28.14L29.2275 32.71C29.4175 32.9 29.6775 33 29.9375 33C30.0675 33 30.1975 32.98 30.3175 32.92C30.6875 32.77 30.9375 32.4 30.9375 32V24.59C33.5175 22.21 34.9375 19.18 34.9375 16C34.9375 8.83 27.7575 3 18.9375 3ZM29.9375 17H21.9375L16.9375 23H13.9375L15.9375 17H9.9375L7.9375 12L11.1475 13.68L16.8075 13.16L13.9375 9H16.9375L21.8675 12.69L25.9275 12.31C26.2875 12.27 26.6475 12.39 26.9275 12.63L30.4875 15.6L29.9475 17H29.9375Z\"/>'\n  })];\nexport { H as onHolidayIcon, L as onHolidayIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "H", "outline", "solid", "onHolidayIcon", "onHolidayIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/on-holiday.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"on-holiday\",H=[\"on-holiday\",C({outline:'<path d=\"M31.1275 14.83L27.5775 11.88C27.0975 11.46 26.4575 11.25 25.8275 11.32L21.8875 11.68L17.5575 8.22C17.3775 8.08 17.1575 8 16.9375 8H13.9375C13.5575 8 13.2175 8.21 13.0475 8.55C12.8775 8.89 12.9175 9.29 13.1375 9.6L15.1675 12.31L11.8375 12.62L12.6075 14.56L21.2675 13.76L21.3075 13.79L21.3375 13.75L26.0175 13.32C26.1075 13.32 26.1875 13.34 26.2775 13.41L29.2875 15.92L29.2475 16.01H21.9275C21.6275 16.01 21.3475 16.14 21.1575 16.37L16.4575 22.01H15.3175L16.8775 17.33C16.9775 17.03 16.9275 16.69 16.7375 16.43C16.5475 16.17 16.2475 16.01 15.9275 16.01H10.6075L8.8575 11.64C8.6475 11.13 8.0675 10.88 7.5575 11.08C7.0475 11.29 6.7975 11.87 6.9975 12.38L8.9975 17.38C9.1475 17.76 9.5175 18.01 9.9275 18.01H14.5375L12.9775 22.69C12.8775 22.99 12.9275 23.33 13.1175 23.59C13.3075 23.85 13.6075 24.01 13.9275 24.01H16.9275C17.2275 24.01 17.5075 23.88 17.6975 23.65L22.3975 18.01H29.9275C30.3375 18.01 30.7075 17.76 30.8575 17.37L31.3975 15.97C31.5575 15.57 31.4375 15.11 31.1075 14.84L31.1275 14.83ZM17.5075 12.09L15.9375 10H16.5875L19.0275 11.95L17.5075 12.09ZM18.9375 3C10.1175 3 2.9375 8.83 2.9375 16C2.9375 23.17 10.1175 29 18.9375 29C20.9075 29 22.8275 28.71 24.6575 28.14L29.2275 32.71C29.4175 32.9 29.6775 33 29.9375 33C30.0675 33 30.1975 32.98 30.3175 32.92C30.6875 32.77 30.9375 32.4 30.9375 32V24.59C33.5175 22.21 34.9375 19.18 34.9375 16C34.9375 8.83 27.7575 3 18.9375 3ZM29.2875 23.38C29.0675 23.57 28.9375 23.85 28.9375 24.14V29.59L25.6375 26.29C25.4475 26.1 25.1875 26 24.9275 26C24.8175 26 24.7075 26.02 24.5975 26.06C22.7975 26.69 20.8975 27.01 18.9275 27.01C11.2075 27.01 4.9275 22.08 4.9275 16.01C4.9275 9.94 11.2175 5 18.9375 5C26.6575 5 32.9375 9.93 32.9375 16C32.9375 18.72 31.6375 21.35 29.2875 23.38Z\"/>',solid:'<path d=\"M18.9375 3C10.1175 3 2.9375 8.83 2.9375 16C2.9375 23.17 10.1175 29 18.9375 29C20.9075 29 22.8275 28.71 24.6575 28.14L29.2275 32.71C29.4175 32.9 29.6775 33 29.9375 33C30.0675 33 30.1975 32.98 30.3175 32.92C30.6875 32.77 30.9375 32.4 30.9375 32V24.59C33.5175 22.21 34.9375 19.18 34.9375 16C34.9375 8.83 27.7575 3 18.9375 3ZM29.9375 17H21.9375L16.9375 23H13.9375L15.9375 17H9.9375L7.9375 12L11.1475 13.68L16.8075 13.16L13.9375 9H16.9375L21.8675 12.69L25.9275 12.31C26.2875 12.27 26.6475 12.39 26.9275 12.63L30.4875 15.6L29.9475 17H29.9375Z\"/>'})];export{H as onHolidayIcon,L as onHolidayIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,YAAY;EAACC,CAAC,GAAC,CAAC,YAAY,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,ksDAAksD;IAACC,KAAK,EAAC;EAAsiB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,aAAa,EAACJ,CAAC,IAAIK,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}