{"ast": null, "code": "export default {\n  foreground: '#9cc2c3',\n  background: '#01162a',\n  cursor: '#74e2cd',\n  black: '#002831',\n  brightBlack: '#006488',\n  red: '#d11c24',\n  brightRed: '#f5163b',\n  green: '#6cbe6c',\n  brightGreen: '#51ef84',\n  yellow: '#a57706',\n  brightYellow: '#b27e28',\n  blue: '#2176c7',\n  brightBlue: '#178ec8',\n  magenta: '#c61c6f',\n  brightMagenta: '#e24d8e',\n  cyan: '#259286',\n  brightCyan: '#00b39e',\n  white: '#eae3cb',\n  brightWhite: '#fcf4dc'\n};", "map": {"version": 3, "names": ["foreground", "background", "cursor", "black", "brightBlack", "red", "brightRed", "green", "bright<PERSON><PERSON>", "yellow", "<PERSON><PERSON><PERSON><PERSON>", "blue", "brightBlue", "magenta", "brightMagenta", "cyan", "bright<PERSON>yan", "white", "bright<PERSON><PERSON>e"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/src/app/terminals/terminal-themes/BashBrawl.ts"], "sourcesContent": ["export default {\n  foreground: '#9cc2c3',\n  background: '#01162a',\n  cursor: '#74e2cd',\n\n  black: '#002831',\n  brightBlack: '#006488',\n\n  red: '#d11c24',\n  brightRed: '#f5163b',\n\n  green: '#6cbe6c',\n  brightGreen: '#51ef84',\n\n  yellow: '#a57706',\n  brightYellow: '#b27e28',\n\n  blue: '#2176c7',\n  brightBlue: '#178ec8',\n\n  magenta: '#c61c6f',\n  brightMagenta: '#e24d8e',\n\n  cyan: '#259286',\n  brightCyan: '#00b39e',\n\n  white: '#eae3cb',\n  brightWhite: '#fcf4dc',\n};\n"], "mappings": "AAAA,eAAe;EACbA,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,SAAS;EACrBC,MAAM,EAAE,SAAS;EAEjBC,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE,SAAS;EAEtBC,GAAG,EAAE,SAAS;EACdC,SAAS,EAAE,SAAS;EAEpBC,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE,SAAS;EAEtBC,MAAM,EAAE,SAAS;EACjBC,YAAY,EAAE,SAAS;EAEvBC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,SAAS;EAErBC,OAAO,EAAE,SAAS;EAClBC,aAAa,EAAE,SAAS;EAExBC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,SAAS;EAErBC,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE;CACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}