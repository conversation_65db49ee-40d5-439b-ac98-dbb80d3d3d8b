{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { initializeKeyListItems as t, setActiveKeyListItem as e, focusElement as s } from \"../utils/focus.js\";\nimport { validKeyNavigationCode as i } from \"../utils/keycodes.js\";\nimport { getFlattenedFocusableItems as o } from \"../utils/traversal.js\";\nimport { getNextKeyListItem as n } from \"./key-navigation.utils.js\";\nfunction a(t = {}) {\n  return e => {\n    e.addInitializer(e => {\n      e.keyNavigationListController || (e.keyNavigationListController = new c(e, t));\n    });\n  };\n}\nclass c {\n  constructor(t, e = {}) {\n    this.host = t, this.host.addController(this), this.config = {\n      keyListItems: \"keyListItems\",\n      layout: \"horizontal\",\n      manageFocus: !0,\n      manageTabindex: !0,\n      loop: !1,\n      dir: this.host.getAttribute(\"rtl\"),\n      ...e\n    };\n  }\n  get listItems() {\n    return this.host[this.config.keyListItems];\n  }\n  hostConnected() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.host.updateComplete, _this.initializeTabIndex(), _this.host.addEventListener(\"click\", t => _this.clickItem(t)), _this.host.addEventListener(\"keydown\", t => _this.focusItem(t)), _this.host.shadowRoot?.addEventListener(\"click\", t => _this.clickItem(t)), _this.host.shadowRoot?.addEventListener(\"keydown\", t => _this.focusItem(t));\n    })();\n  }\n  initializeTabIndex() {\n    this.config.manageFocus && this.config.manageTabindex && t(this.listItems);\n  }\n  clickItem(t) {\n    const e = this.getActiveItem(t);\n    e && this.setActiveItem(t, e);\n  }\n  focusItem(t) {\n    if (i(t)) {\n      const e = this.getActiveItem(t);\n      if (e) {\n        const {\n          next: s,\n          previous: i\n        } = n(e, Array.from(this.listItems), {\n          ...this.config,\n          code: t.code\n        });\n        s !== i && this.setActiveItem(t, this.listItems[s], this.listItems[i]);\n      }\n    }\n  }\n  getActiveItem(t) {\n    return Array.from(this.listItems).find(e => e === t.target.closest(this.listItems[0].tagName.toLocaleLowerCase()) ?? e === t.target);\n  }\n  setActiveItem(t, i, n) {\n    if (this.config.manageFocus) {\n      this.config.manageTabindex && e(this.listItems, i);\n      const n = o(i)[0] ?? i;\n      s(n), t.preventDefault();\n    }\n    i.dispatchEvent(new CustomEvent(\"cdsKeyChange\", {\n      bubbles: !0,\n      detail: {\n        activeItem: i,\n        previousItem: n,\n        code: t.code,\n        metaKey: t.ctrlKey || t.metaKey,\n        keyListItems: this.config.keyListItems\n      }\n    }));\n  }\n}\nexport { c as KeyNavigationListController, a as keyNavigationList };", "map": {"version": 3, "names": ["initializeKeyListItems", "t", "setActiveKeyListItem", "e", "focusElement", "s", "validKeyNavigationCode", "i", "getFlattenedFocusableItems", "o", "getNextKeyListItem", "n", "a", "addInitializer", "keyNavigationListController", "c", "constructor", "host", "addController", "config", "keyListItems", "layout", "manageFocus", "manageTabindex", "loop", "dir", "getAttribute", "listItems", "hostConnected", "_this", "_asyncToGenerator", "updateComplete", "initializeTabIndex", "addEventListener", "clickItem", "focusItem", "shadowRoot", "getActiveItem", "setActiveItem", "next", "previous", "Array", "from", "code", "find", "target", "closest", "tagName", "toLocaleLowerCase", "preventDefault", "dispatchEvent", "CustomEvent", "bubbles", "detail", "activeItem", "previousItem", "metaKey", "ctrl<PERSON>ey", "KeyNavigationListController", "keyNavigationList"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/key-navigation-list.controller.js"], "sourcesContent": ["import{initializeKeyListItems as t,setActiveKeyListItem as e,focusElement as s}from\"../utils/focus.js\";import{validKeyNavigationCode as i}from\"../utils/keycodes.js\";import{getFlattenedFocusableItems as o}from\"../utils/traversal.js\";import{getNextKeyListItem as n}from\"./key-navigation.utils.js\";function a(t={}){return e=>{e.addInitializer((e=>{e.keyNavigationListController||(e.keyNavigationListController=new c(e,t))}))}}class c{constructor(t,e={}){this.host=t,this.host.addController(this),this.config={keyListItems:\"keyListItems\",layout:\"horizontal\",manageFocus:!0,manageTabindex:!0,loop:!1,dir:this.host.getAttribute(\"rtl\"),...e}}get listItems(){return this.host[this.config.keyListItems]}async hostConnected(){await this.host.updateComplete,this.initializeTabIndex(),this.host.addEventListener(\"click\",(t=>this.clickItem(t))),this.host.addEventListener(\"keydown\",(t=>this.focusItem(t))),this.host.shadowRoot?.addEventListener(\"click\",(t=>this.clickItem(t))),this.host.shadowRoot?.addEventListener(\"keydown\",(t=>this.focusItem(t)))}initializeTabIndex(){this.config.manageFocus&&this.config.manageTabindex&&t(this.listItems)}clickItem(t){const e=this.getActiveItem(t);e&&this.setActiveItem(t,e)}focusItem(t){if(i(t)){const e=this.getActiveItem(t);if(e){const{next:s,previous:i}=n(e,Array.from(this.listItems),{...this.config,code:t.code});s!==i&&this.setActiveItem(t,this.listItems[s],this.listItems[i])}}}getActiveItem(t){return Array.from(this.listItems).find((e=>e===t.target.closest(this.listItems[0].tagName.toLocaleLowerCase())??e===t.target))}setActiveItem(t,i,n){if(this.config.manageFocus){this.config.manageTabindex&&e(this.listItems,i);const n=o(i)[0]??i;s(n),t.preventDefault()}i.dispatchEvent(new CustomEvent(\"cdsKeyChange\",{bubbles:!0,detail:{activeItem:i,previousItem:n,code:t.code,metaKey:t.ctrlKey||t.metaKey,keyListItems:this.config.keyListItems}}))}}export{c as KeyNavigationListController,a as keyNavigationList};\n"], "mappings": ";AAAA,SAAOA,sBAAsB,IAAIC,CAAC,EAACC,oBAAoB,IAAIC,CAAC,EAACC,YAAY,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,sBAAsB,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,0BAA0B,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,kBAAkB,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAASC,CAACA,CAACX,CAAC,GAAC,CAAC,CAAC,EAAC;EAAC,OAAOE,CAAC,IAAE;IAACA,CAAC,CAACU,cAAc,CAAEV,CAAC,IAAE;MAACA,CAAC,CAACW,2BAA2B,KAAGX,CAAC,CAACW,2BAA2B,GAAC,IAAIC,CAAC,CAACZ,CAAC,EAACF,CAAC,CAAC,CAAC;IAAA,CAAE,CAAC;EAAA,CAAC;AAAA;AAAC,MAAMc,CAAC;EAACC,WAAWA,CAACf,CAAC,EAACE,CAAC,GAAC,CAAC,CAAC,EAAC;IAAC,IAAI,CAACc,IAAI,GAAChB,CAAC,EAAC,IAAI,CAACgB,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC,EAAC,IAAI,CAACC,MAAM,GAAC;MAACC,YAAY,EAAC,cAAc;MAACC,MAAM,EAAC,YAAY;MAACC,WAAW,EAAC,CAAC,CAAC;MAACC,cAAc,EAAC,CAAC,CAAC;MAACC,IAAI,EAAC,CAAC,CAAC;MAACC,GAAG,EAAC,IAAI,CAACR,IAAI,CAACS,YAAY,CAAC,KAAK,CAAC;MAAC,GAAGvB;IAAC,CAAC;EAAA;EAAC,IAAIwB,SAASA,CAAA,EAAE;IAAC,OAAO,IAAI,CAACV,IAAI,CAAC,IAAI,CAACE,MAAM,CAACC,YAAY,CAAC;EAAA;EAAOQ,aAAaA,CAAA,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAAC,MAAMD,KAAI,CAACZ,IAAI,CAACc,cAAc,EAACF,KAAI,CAACG,kBAAkB,CAAC,CAAC,EAACH,KAAI,CAACZ,IAAI,CAACgB,gBAAgB,CAAC,OAAO,EAAEhC,CAAC,IAAE4B,KAAI,CAACK,SAAS,CAACjC,CAAC,CAAE,CAAC,EAAC4B,KAAI,CAACZ,IAAI,CAACgB,gBAAgB,CAAC,SAAS,EAAEhC,CAAC,IAAE4B,KAAI,CAACM,SAAS,CAAClC,CAAC,CAAE,CAAC,EAAC4B,KAAI,CAACZ,IAAI,CAACmB,UAAU,EAAEH,gBAAgB,CAAC,OAAO,EAAEhC,CAAC,IAAE4B,KAAI,CAACK,SAAS,CAACjC,CAAC,CAAE,CAAC,EAAC4B,KAAI,CAACZ,IAAI,CAACmB,UAAU,EAAEH,gBAAgB,CAAC,SAAS,EAAEhC,CAAC,IAAE4B,KAAI,CAACM,SAAS,CAAClC,CAAC,CAAE,CAAC;IAAA;EAAA;EAAC+B,kBAAkBA,CAAA,EAAE;IAAC,IAAI,CAACb,MAAM,CAACG,WAAW,IAAE,IAAI,CAACH,MAAM,CAACI,cAAc,IAAEtB,CAAC,CAAC,IAAI,CAAC0B,SAAS,CAAC;EAAA;EAACO,SAASA,CAACjC,CAAC,EAAC;IAAC,MAAME,CAAC,GAAC,IAAI,CAACkC,aAAa,CAACpC,CAAC,CAAC;IAACE,CAAC,IAAE,IAAI,CAACmC,aAAa,CAACrC,CAAC,EAACE,CAAC,CAAC;EAAA;EAACgC,SAASA,CAAClC,CAAC,EAAC;IAAC,IAAGM,CAAC,CAACN,CAAC,CAAC,EAAC;MAAC,MAAME,CAAC,GAAC,IAAI,CAACkC,aAAa,CAACpC,CAAC,CAAC;MAAC,IAAGE,CAAC,EAAC;QAAC,MAAK;UAACoC,IAAI,EAAClC,CAAC;UAACmC,QAAQ,EAACjC;QAAC,CAAC,GAACI,CAAC,CAACR,CAAC,EAACsC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACf,SAAS,CAAC,EAAC;UAAC,GAAG,IAAI,CAACR,MAAM;UAACwB,IAAI,EAAC1C,CAAC,CAAC0C;QAAI,CAAC,CAAC;QAACtC,CAAC,KAAGE,CAAC,IAAE,IAAI,CAAC+B,aAAa,CAACrC,CAAC,EAAC,IAAI,CAAC0B,SAAS,CAACtB,CAAC,CAAC,EAAC,IAAI,CAACsB,SAAS,CAACpB,CAAC,CAAC,CAAC;MAAA;IAAC;EAAC;EAAC8B,aAAaA,CAACpC,CAAC,EAAC;IAAC,OAAOwC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACf,SAAS,CAAC,CAACiB,IAAI,CAAEzC,CAAC,IAAEA,CAAC,KAAGF,CAAC,CAAC4C,MAAM,CAACC,OAAO,CAAC,IAAI,CAACnB,SAAS,CAAC,CAAC,CAAC,CAACoB,OAAO,CAACC,iBAAiB,CAAC,CAAC,CAAC,IAAE7C,CAAC,KAAGF,CAAC,CAAC4C,MAAO,CAAC;EAAA;EAACP,aAAaA,CAACrC,CAAC,EAACM,CAAC,EAACI,CAAC,EAAC;IAAC,IAAG,IAAI,CAACQ,MAAM,CAACG,WAAW,EAAC;MAAC,IAAI,CAACH,MAAM,CAACI,cAAc,IAAEpB,CAAC,CAAC,IAAI,CAACwB,SAAS,EAACpB,CAAC,CAAC;MAAC,MAAMI,CAAC,GAACF,CAAC,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,IAAEA,CAAC;MAACF,CAAC,CAACM,CAAC,CAAC,EAACV,CAAC,CAACgD,cAAc,CAAC,CAAC;IAAA;IAAC1C,CAAC,CAAC2C,aAAa,CAAC,IAAIC,WAAW,CAAC,cAAc,EAAC;MAACC,OAAO,EAAC,CAAC,CAAC;MAACC,MAAM,EAAC;QAACC,UAAU,EAAC/C,CAAC;QAACgD,YAAY,EAAC5C,CAAC;QAACgC,IAAI,EAAC1C,CAAC,CAAC0C,IAAI;QAACa,OAAO,EAACvD,CAAC,CAACwD,OAAO,IAAExD,CAAC,CAACuD,OAAO;QAACpC,YAAY,EAAC,IAAI,CAACD,MAAM,CAACC;MAAY;IAAC,CAAC,CAAC,CAAC;EAAA;AAAC;AAAC,SAAOL,CAAC,IAAI2C,2BAA2B,EAAC9C,CAAC,IAAI+C,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}