{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst V = \"car\",\n  H = [\"car\", C({\n    outline: '<path d=\"M26.6275 15.15C22.9475 7.96 19.3075 5 14.1275 5C8.9475 5 4.9375 9.12 4.9375 14.19V15C3.8375 15 2.9375 15.9 2.9375 17V25C2.9375 26.1 3.8375 27 4.9375 27V17H24.9375C29.3475 17 32.9375 20.59 32.9375 25H28.3775C27.6875 23.81 26.4075 23 24.9375 23C22.7275 23 20.9375 24.79 20.9375 27C20.9375 29.21 22.7275 31 24.9375 31C27.1475 31 28.9375 29.21 28.9375 27H32.9375C34.0375 27 34.9375 26.1 34.9375 25C34.9375 20.06 31.3375 15.96 26.6275 15.15ZM10.9375 15H6.9375V14.19C6.9375 11.37 8.5675 8.93 10.9375 7.76V15ZM12.9375 15V7.11C13.3275 7.05 13.7175 7 14.1275 7C17.6075 7 20.6975 8.41 24.2875 15H12.9375ZM24.9375 29C23.8375 29 22.9375 28.1 22.9375 27C22.9375 25.9 23.8375 25 24.9375 25C26.0375 25 26.9375 25.9 26.9375 27C26.9375 28.1 26.0375 29 24.9375 29ZM10.9375 23C8.7275 23 6.9375 24.79 6.9375 27C6.9375 29.21 8.7275 31 10.9375 31C13.1475 31 14.9375 29.21 14.9375 27H18.9375C18.9375 26.3 19.0675 25.63 19.2875 25H14.3775C13.6875 23.81 12.4075 23 10.9375 23ZM10.9375 29C9.8375 29 8.9375 28.1 8.9375 27C8.9375 25.9 9.8375 25 10.9375 25C12.0375 25 12.9375 25.9 12.9375 27C12.9375 28.1 12.0375 29 10.9375 29ZM16.9375 19H12.9375V21H16.9375V19Z\"/>',\n    solid: '<path d=\"M26.6275 15.15C22.9475 7.96 19.3075 5 14.1275 5C8.9475 5 4.9375 9.12 4.9375 14.19V15C3.8375 15 2.9375 15.9 2.9375 17V25C2.9375 26.1 3.8375 27 4.9375 27C4.9375 23.69 7.6275 21 10.9375 21C10.9575 21 10.9775 21 10.9875 21V23C10.9875 23 10.9475 23 10.9375 23C8.7275 23 6.9375 24.79 6.9375 27C6.9375 29.21 8.7275 31 10.9375 31C13.1475 31 14.9375 29.21 14.9375 27H18.9375C18.9375 23.69 21.6275 21 24.9375 21V23C22.7275 23 20.9375 24.79 20.9375 27C20.9375 29.21 22.7275 31 24.9375 31C27.1475 31 28.9375 29.21 28.9375 27H32.9375C34.0375 27 34.9375 26.1 34.9375 25C34.9375 20.06 31.3375 15.96 26.6275 15.15ZM10.9375 15H6.9375V14.19C6.9375 11.37 8.5675 8.93 10.9375 7.76V15ZM10.9375 29C9.8375 29 8.9375 28.1 8.9375 27C8.9375 25.9 9.8375 25 10.9375 25C12.0375 25 12.9375 25.9 12.9375 27C12.9375 28.1 12.0375 29 10.9375 29ZM16.9375 21H12.9375V19H16.9375V21ZM12.9375 15V7.11C13.3275 7.05 13.7175 7 14.1275 7C17.6075 7 20.6975 8.41 24.2875 15H12.9375ZM24.9375 29C23.8375 29 22.9375 28.1 22.9375 27C22.9375 25.9 23.8375 25 24.9375 25C26.0375 25 26.9375 25.9 26.9375 27C26.9375 28.1 26.0375 29 24.9375 29Z\"/>'\n  })];\nexport { H as carIcon, V as carIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "V", "H", "outline", "solid", "carIcon", "carIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/car.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const V=\"car\",H=[\"car\",C({outline:'<path d=\"M26.6275 15.15C22.9475 7.96 19.3075 5 14.1275 5C8.9475 5 4.9375 9.12 4.9375 14.19V15C3.8375 15 2.9375 15.9 2.9375 17V25C2.9375 26.1 3.8375 27 4.9375 27V17H24.9375C29.3475 17 32.9375 20.59 32.9375 25H28.3775C27.6875 23.81 26.4075 23 24.9375 23C22.7275 23 20.9375 24.79 20.9375 27C20.9375 29.21 22.7275 31 24.9375 31C27.1475 31 28.9375 29.21 28.9375 27H32.9375C34.0375 27 34.9375 26.1 34.9375 25C34.9375 20.06 31.3375 15.96 26.6275 15.15ZM10.9375 15H6.9375V14.19C6.9375 11.37 8.5675 8.93 10.9375 7.76V15ZM12.9375 15V7.11C13.3275 7.05 13.7175 7 14.1275 7C17.6075 7 20.6975 8.41 24.2875 15H12.9375ZM24.9375 29C23.8375 29 22.9375 28.1 22.9375 27C22.9375 25.9 23.8375 25 24.9375 25C26.0375 25 26.9375 25.9 26.9375 27C26.9375 28.1 26.0375 29 24.9375 29ZM10.9375 23C8.7275 23 6.9375 24.79 6.9375 27C6.9375 29.21 8.7275 31 10.9375 31C13.1475 31 14.9375 29.21 14.9375 27H18.9375C18.9375 26.3 19.0675 25.63 19.2875 25H14.3775C13.6875 23.81 12.4075 23 10.9375 23ZM10.9375 29C9.8375 29 8.9375 28.1 8.9375 27C8.9375 25.9 9.8375 25 10.9375 25C12.0375 25 12.9375 25.9 12.9375 27C12.9375 28.1 12.0375 29 10.9375 29ZM16.9375 19H12.9375V21H16.9375V19Z\"/>',solid:'<path d=\"M26.6275 15.15C22.9475 7.96 19.3075 5 14.1275 5C8.9475 5 4.9375 9.12 4.9375 14.19V15C3.8375 15 2.9375 15.9 2.9375 17V25C2.9375 26.1 3.8375 27 4.9375 27C4.9375 23.69 7.6275 21 10.9375 21C10.9575 21 10.9775 21 10.9875 21V23C10.9875 23 10.9475 23 10.9375 23C8.7275 23 6.9375 24.79 6.9375 27C6.9375 29.21 8.7275 31 10.9375 31C13.1475 31 14.9375 29.21 14.9375 27H18.9375C18.9375 23.69 21.6275 21 24.9375 21V23C22.7275 23 20.9375 24.79 20.9375 27C20.9375 29.21 22.7275 31 24.9375 31C27.1475 31 28.9375 29.21 28.9375 27H32.9375C34.0375 27 34.9375 26.1 34.9375 25C34.9375 20.06 31.3375 15.96 26.6275 15.15ZM10.9375 15H6.9375V14.19C6.9375 11.37 8.5675 8.93 10.9375 7.76V15ZM10.9375 29C9.8375 29 8.9375 28.1 8.9375 27C8.9375 25.9 9.8375 25 10.9375 25C12.0375 25 12.9375 25.9 12.9375 27C12.9375 28.1 12.0375 29 10.9375 29ZM16.9375 21H12.9375V19H16.9375V21ZM12.9375 15V7.11C13.3275 7.05 13.7175 7 14.1275 7C17.6075 7 20.6975 8.41 24.2875 15H12.9375ZM24.9375 29C23.8375 29 22.9375 28.1 22.9375 27C22.9375 25.9 23.8375 25 24.9375 25C26.0375 25 26.9375 25.9 26.9375 27C26.9375 28.1 26.0375 29 24.9375 29Z\"/>'})];export{H as carIcon,V as carIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,CAAC,KAAK,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,0nCAA0nC;IAACC,KAAK,EAAC;EAA+kC,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,OAAO,EAACJ,CAAC,IAAIK,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}