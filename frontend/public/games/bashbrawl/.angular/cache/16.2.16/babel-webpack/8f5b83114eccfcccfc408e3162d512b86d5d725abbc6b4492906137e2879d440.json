{"ast": null, "code": "function t(t, e, o = !0) {\n  const n = new ResizeObserver(() => {\n    o ? window.requestAnimationFrame(() => e()) : e();\n  });\n  return n.observe(t), n.__testTrigger = e, n;\n}\nfunction e(t, e) {\n  const o = new IntersectionObserver(t => {\n    !0 === t[0].isIntersecting && e();\n  }, {\n    threshold: [0]\n  });\n  return o.observe(t), o;\n}\nfunction o(t, e) {\n  return t.updateComplete.then(() => {\n    const o = t.layout;\n    return t.layout = e.layouts[0], e.layouts.reduce((o, n) => o.then(() => {\n      if (t.layout === e.initialLayout) return n;\n      {\n        const e = t.layout;\n        return t.layout = n, t.updateComplete.then(() => (t.layout = t.layoutStable ? t.layout : e, n));\n      }\n    }), Promise.resolve(e.layouts[0])).then(() => o !== t.layout);\n  });\n}\nexport { o as calculateOptimalLayout, t as elementResize, e as elementVisible };", "map": {"version": 3, "names": ["t", "e", "o", "n", "ResizeObserver", "window", "requestAnimationFrame", "observe", "__testTrigger", "IntersectionObserver", "isIntersecting", "threshold", "updateComplete", "then", "layout", "layouts", "reduce", "initialLayout", "layoutStable", "Promise", "resolve", "calculateOptimalLayout", "elementResize", "elementVisible"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/responsive.js"], "sourcesContent": ["function t(t,e,o=!0){const n=new ResizeObserver((()=>{o?window.requestAnimationFrame((()=>e())):e()}));return n.observe(t),n.__testTrigger=e,n}function e(t,e){const o=new IntersectionObserver((t=>{!0===t[0].isIntersecting&&e()}),{threshold:[0]});return o.observe(t),o}function o(t,e){return t.updateComplete.then((()=>{const o=t.layout;return t.layout=e.layouts[0],e.layouts.reduce(((o,n)=>o.then((()=>{if(t.layout===e.initialLayout)return n;{const e=t.layout;return t.layout=n,t.updateComplete.then((()=>(t.layout=t.layoutStable?t.layout:e,n)))}}))),Promise.resolve(e.layouts[0])).then((()=>o!==t.layout))}))}export{o as calculateOptimalLayout,t as elementResize,e as elementVisible};\n"], "mappings": "AAAA,SAASA,CAACA,CAACA,CAAC,EAACC,CAAC,EAACC,CAAC,GAAC,CAAC,CAAC,EAAC;EAAC,MAAMC,CAAC,GAAC,IAAIC,cAAc,CAAE,MAAI;IAACF,CAAC,GAACG,MAAM,CAACC,qBAAqB,CAAE,MAAIL,CAAC,CAAC,CAAE,CAAC,GAACA,CAAC,CAAC,CAAC;EAAA,CAAE,CAAC;EAAC,OAAOE,CAAC,CAACI,OAAO,CAACP,CAAC,CAAC,EAACG,CAAC,CAACK,aAAa,GAACP,CAAC,EAACE,CAAC;AAAA;AAAC,SAASF,CAACA,CAACD,CAAC,EAACC,CAAC,EAAC;EAAC,MAAMC,CAAC,GAAC,IAAIO,oBAAoB,CAAET,CAAC,IAAE;IAAC,CAAC,CAAC,KAAGA,CAAC,CAAC,CAAC,CAAC,CAACU,cAAc,IAAET,CAAC,CAAC,CAAC;EAAA,CAAC,EAAE;IAACU,SAAS,EAAC,CAAC,CAAC;EAAC,CAAC,CAAC;EAAC,OAAOT,CAAC,CAACK,OAAO,CAACP,CAAC,CAAC,EAACE,CAAC;AAAA;AAAC,SAASA,CAACA,CAACF,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACY,cAAc,CAACC,IAAI,CAAE,MAAI;IAAC,MAAMX,CAAC,GAACF,CAAC,CAACc,MAAM;IAAC,OAAOd,CAAC,CAACc,MAAM,GAACb,CAAC,CAACc,OAAO,CAAC,CAAC,CAAC,EAACd,CAAC,CAACc,OAAO,CAACC,MAAM,CAAE,CAACd,CAAC,EAACC,CAAC,KAAGD,CAAC,CAACW,IAAI,CAAE,MAAI;MAAC,IAAGb,CAAC,CAACc,MAAM,KAAGb,CAAC,CAACgB,aAAa,EAAC,OAAOd,CAAC;MAAC;QAAC,MAAMF,CAAC,GAACD,CAAC,CAACc,MAAM;QAAC,OAAOd,CAAC,CAACc,MAAM,GAACX,CAAC,EAACH,CAAC,CAACY,cAAc,CAACC,IAAI,CAAE,OAAKb,CAAC,CAACc,MAAM,GAACd,CAAC,CAACkB,YAAY,GAAClB,CAAC,CAACc,MAAM,GAACb,CAAC,EAACE,CAAC,CAAE,CAAC;MAAA;IAAC,CAAE,CAAC,EAAEgB,OAAO,CAACC,OAAO,CAACnB,CAAC,CAACc,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAAE,MAAIX,CAAC,KAAGF,CAAC,CAACc,MAAO,CAAC;EAAA,CAAE,CAAC;AAAA;AAAC,SAAOZ,CAAC,IAAImB,sBAAsB,EAACrB,CAAC,IAAIsB,aAAa,EAACrB,CAAC,IAAIsB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}