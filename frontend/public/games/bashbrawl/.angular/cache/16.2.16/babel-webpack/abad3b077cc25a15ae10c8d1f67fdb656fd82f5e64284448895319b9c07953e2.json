{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst t = \"ellipsis-vertical\",\n  e = [\"ellipsis-vertical\", C({\n    outline: '<path d=\"M21.1001 4.9C21.1001 6.50163 19.757 7.8 18.1001 7.8C16.4432 7.8 15.1001 6.50163 15.1001 4.9C15.1001 3.29837 16.4432 2 18.1001 2C19.757 2 21.1001 3.29837 21.1001 4.9Z\"/><path d=\"M18.1001 20.9C19.757 20.9 21.1001 19.6016 21.1001 18C21.1001 16.3984 19.757 15.1 18.1001 15.1C16.4432 15.1 15.1001 16.3984 15.1001 18C15.1001 19.6016 16.4432 20.9 18.1001 20.9Z\"/><path d=\"M18.1001 34C19.757 34 21.1001 32.7016 21.1001 31.1C21.1001 29.4984 19.757 28.2 18.1001 28.2C16.4432 28.2 15.1001 29.4984 15.1001 31.1C15.1001 32.7016 16.4432 34 18.1001 34Z\"/>',\n    outlineBadged: '<path d=\"M21.1001 4.9C21.1001 6.50163 19.757 7.8 18.1001 7.8C16.4432 7.8 15.1001 6.50163 15.1001 4.9C15.1001 3.29837 16.4432 2 18.1001 2C19.757 2 21.1001 3.29837 21.1001 4.9Z\"/><path d=\"M18.1001 20.9C19.757 20.9 21.1001 19.6016 21.1001 18C21.1001 16.3984 19.757 15.1 18.1001 15.1C16.4432 15.1 15.1001 16.3984 15.1001 18C15.1001 19.6016 16.4432 20.9 18.1001 20.9Z\"/><path d=\"M18.1001 34C19.757 34 21.1001 32.7016 21.1001 31.1C21.1001 29.4984 19.757 28.2 18.1001 28.2C16.4432 28.2 15.1001 29.4984 15.1001 31.1C15.1001 32.7016 16.4432 34 18.1001 34Z\"/><path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/>'\n  })];\nexport { e as ellipsisVerticalIcon, t as ellipsisVerticalIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "t", "e", "outline", "outlineBadged", "ellipsisVerticalIcon", "ellipsisVerticalIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/ellipsis-vertical.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const t=\"ellipsis-vertical\",e=[\"ellipsis-vertical\",C({outline:'<path d=\"M21.1001 4.9C21.1001 6.50163 19.757 7.8 18.1001 7.8C16.4432 7.8 15.1001 6.50163 15.1001 4.9C15.1001 3.29837 16.4432 2 18.1001 2C19.757 2 21.1001 3.29837 21.1001 4.9Z\"/><path d=\"M18.1001 20.9C19.757 20.9 21.1001 19.6016 21.1001 18C21.1001 16.3984 19.757 15.1 18.1001 15.1C16.4432 15.1 15.1001 16.3984 15.1001 18C15.1001 19.6016 16.4432 20.9 18.1001 20.9Z\"/><path d=\"M18.1001 34C19.757 34 21.1001 32.7016 21.1001 31.1C21.1001 29.4984 19.757 28.2 18.1001 28.2C16.4432 28.2 15.1001 29.4984 15.1001 31.1C15.1001 32.7016 16.4432 34 18.1001 34Z\"/>',outlineBadged:'<path d=\"M21.1001 4.9C21.1001 6.50163 19.757 7.8 18.1001 7.8C16.4432 7.8 15.1001 6.50163 15.1001 4.9C15.1001 3.29837 16.4432 2 18.1001 2C19.757 2 21.1001 3.29837 21.1001 4.9Z\"/><path d=\"M18.1001 20.9C19.757 20.9 21.1001 19.6016 21.1001 18C21.1001 16.3984 19.757 15.1 18.1001 15.1C16.4432 15.1 15.1001 16.3984 15.1001 18C15.1001 19.6016 16.4432 20.9 18.1001 20.9Z\"/><path d=\"M18.1001 34C19.757 34 21.1001 32.7016 21.1001 31.1C21.1001 29.4984 19.757 28.2 18.1001 28.2C16.4432 28.2 15.1001 29.4984 15.1001 31.1C15.1001 32.7016 16.4432 34 18.1001 34Z\"/><path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/>'})];export{e as ellipsisVerticalIcon,t as ellipsisVerticalIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,mBAAmB;EAACC,CAAC,GAAC,CAAC,mBAAmB,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,uiBAAuiB;IAACC,aAAa,EAAC;EAAqqB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,oBAAoB,EAACJ,CAAC,IAAIK,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}