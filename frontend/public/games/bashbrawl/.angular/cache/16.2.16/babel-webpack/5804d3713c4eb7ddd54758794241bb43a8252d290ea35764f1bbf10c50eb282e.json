{"ast": null, "code": "function t(t, n, r) {\n  const o = (n /= 100) * Math.min(r /= 100, 1 - r),\n    h = (n, h = (n + t / 30) % 12) => r - o * Math.max(Math.min(h - 3, 9 - h, 1), -1);\n  return [Math.round(255 * h(0)), Math.round(255 * h(8)), Math.round(255 * h(4))];\n}\nfunction n(t, n, r) {\n  let o = t.toString(16),\n    h = n.toString(16),\n    a = r.toString(16);\n  return 1 === o.length && (o = \"0\" + o), 1 === h.length && (h = \"0\" + h), 1 === a.length && (a = \"0\" + a), `#${o}${h}${a}`;\n}\nexport { t as hslToRgb, n as rgbToHex };", "map": {"version": 3, "names": ["t", "n", "r", "o", "Math", "min", "h", "max", "round", "toString", "a", "length", "hslToRgb", "rgbToHex"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/color.js"], "sourcesContent": ["function t(t,n,r){const o=(n/=100)*Math.min(r/=100,1-r),h=(n,h=(n+t/30)%12)=>r-o*Math.max(Math.min(h-3,9-h,1),-1);return[Math.round(255*h(0)),Math.round(255*h(8)),Math.round(255*h(4))]}function n(t,n,r){let o=t.toString(16),h=n.toString(16),a=r.toString(16);return 1===o.length&&(o=\"0\"+o),1===h.length&&(h=\"0\"+h),1===a.length&&(a=\"0\"+a),`#${o}${h}${a}`}export{t as hslToRgb,n as rgbToHex};\n"], "mappings": "AAAA,SAASA,CAACA,CAACA,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,MAAMC,CAAC,GAAC,CAACF,CAAC,IAAE,GAAG,IAAEG,IAAI,CAACC,GAAG,CAACH,CAAC,IAAE,GAAG,EAAC,CAAC,GAACA,CAAC,CAAC;IAACI,CAAC,GAACA,CAACL,CAAC,EAACK,CAAC,GAAC,CAACL,CAAC,GAACD,CAAC,GAAC,EAAE,IAAE,EAAE,KAAGE,CAAC,GAACC,CAAC,GAACC,IAAI,CAACG,GAAG,CAACH,IAAI,CAACC,GAAG,CAACC,CAAC,GAAC,CAAC,EAAC,CAAC,GAACA,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;EAAC,OAAM,CAACF,IAAI,CAACI,KAAK,CAAC,GAAG,GAACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAACF,IAAI,CAACI,KAAK,CAAC,GAAG,GAACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAACF,IAAI,CAACI,KAAK,CAAC,GAAG,GAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,SAASL,CAACA,CAACD,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACH,CAAC,CAACS,QAAQ,CAAC,EAAE,CAAC;IAACH,CAAC,GAACL,CAAC,CAACQ,QAAQ,CAAC,EAAE,CAAC;IAACC,CAAC,GAACR,CAAC,CAACO,QAAQ,CAAC,EAAE,CAAC;EAAC,OAAO,CAAC,KAAGN,CAAC,CAACQ,MAAM,KAAGR,CAAC,GAAC,GAAG,GAACA,CAAC,CAAC,EAAC,CAAC,KAAGG,CAAC,CAACK,MAAM,KAAGL,CAAC,GAAC,GAAG,GAACA,CAAC,CAAC,EAAC,CAAC,KAAGI,CAAC,CAACC,MAAM,KAAGD,CAAC,GAAC,GAAG,GAACA,CAAC,CAAC,EAAE,IAAGP,CAAE,GAAEG,CAAE,GAAEI,CAAE,EAAC;AAAA;AAAC,SAAOV,CAAC,IAAIY,QAAQ,EAACX,CAAC,IAAIY,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}