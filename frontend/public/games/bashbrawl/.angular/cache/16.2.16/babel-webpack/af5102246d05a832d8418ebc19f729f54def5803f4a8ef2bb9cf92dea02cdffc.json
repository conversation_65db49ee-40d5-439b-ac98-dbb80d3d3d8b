{"ast": null, "code": "import { arrayToObject as e } from \"./array.js\";\nimport { setPropStyles as t } from \"./string.js\";\nfunction r(t, r) {\n  const n = e(r.tags, \"name\")[t];\n  return {\n    ...(s = n, s.properties?.filter(e => e?.type?.includes(\" | \")).reduce((e, t) => {\n      const r = t.type.split(\"|\").map(e => e.replace(/\"/g, \"\").replace(/\\s/g, \"\"));\n      return {\n        ...e,\n        [t.name]: {\n          control: {\n            type: \"select\",\n            options: r\n          },\n          defaultValue: r[0]\n        }\n      };\n    }, {})),\n    ...o(n),\n    ...c(n)\n  };\n  var s;\n}\nfunction n(e) {\n  return {\n    ...e,\n    style: t(e)\n  };\n}\nfunction o(e) {\n  return e.cssProperties?.reduce((e, t) => {\n    const r = t?.name?.includes(\"color\") || t?.name?.endsWith(\"background\") ? \"color\" : \"text\";\n    return {\n      ...e,\n      [t.name]: {\n        control: {\n          type: r\n        }\n      }\n    };\n  }, {});\n}\nfunction c(e) {\n  return e.slots?.reduce((e, t) => ({\n    ...e,\n    [t.name]: {\n      control: {\n        type: \"text\"\n      },\n      defaultValue: \"\" + t.name\n    }\n  }), {});\n}\nexport { r as getElementStorybookArgTypes, n as getElementStorybookArgs };", "map": {"version": 3, "names": ["arrayToObject", "e", "setPropStyles", "t", "r", "n", "tags", "s", "properties", "filter", "type", "includes", "reduce", "split", "map", "replace", "name", "control", "options", "defaultValue", "o", "c", "style", "cssProperties", "endsWith", "slots", "getElementStorybookArgTypes", "getElementStorybookArgs"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/metadata.js"], "sourcesContent": ["import{arrayToObject as e}from\"./array.js\";import{setPropStyles as t}from\"./string.js\";function r(t,r){const n=e(r.tags,\"name\")[t];return{...(s=n,s.properties?.filter((e=>e?.type?.includes(\" | \"))).reduce(((e,t)=>{const r=t.type.split(\"|\").map((e=>e.replace(/\"/g,\"\").replace(/\\s/g,\"\")));return{...e,[t.name]:{control:{type:\"select\",options:r},defaultValue:r[0]}}}),{})),...o(n),...c(n)};var s}function n(e){return{...e,style:t(e)}}function o(e){return e.cssProperties?.reduce(((e,t)=>{const r=t?.name?.includes(\"color\")||t?.name?.endsWith(\"background\")?\"color\":\"text\";return{...e,[t.name]:{control:{type:r}}}}),{})}function c(e){return e.slots?.reduce(((e,t)=>({...e,[t.name]:{control:{type:\"text\"},defaultValue:\"\"+t.name}})),{})}export{r as getElementStorybookArgTypes,n as getElementStorybookArgs};\n"], "mappings": "AAAA,SAAOA,aAAa,IAAIC,CAAC,QAAK,YAAY;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,aAAa;AAAC,SAASC,CAACA,CAACD,CAAC,EAACC,CAAC,EAAC;EAAC,MAAMC,CAAC,GAACJ,CAAC,CAACG,CAAC,CAACE,IAAI,EAAC,MAAM,CAAC,CAACH,CAAC,CAAC;EAAC,OAAM;IAAC,IAAII,CAAC,GAACF,CAAC,EAACE,CAAC,CAACC,UAAU,EAAEC,MAAM,CAAER,CAAC,IAAEA,CAAC,EAAES,IAAI,EAAEC,QAAQ,CAAC,KAAK,CAAE,CAAC,CAACC,MAAM,CAAE,CAACX,CAAC,EAACE,CAAC,KAAG;MAAC,MAAMC,CAAC,GAACD,CAAC,CAACO,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEb,CAAC,IAAEA,CAAC,CAACc,OAAO,CAAC,IAAI,EAAC,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAC,EAAE,CAAE,CAAC;MAAC,OAAM;QAAC,GAAGd,CAAC;QAAC,CAACE,CAAC,CAACa,IAAI,GAAE;UAACC,OAAO,EAAC;YAACP,IAAI,EAAC,QAAQ;YAACQ,OAAO,EAACd;UAAC,CAAC;UAACe,YAAY,EAACf,CAAC,CAAC,CAAC;QAAC;MAAC,CAAC;IAAA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAAC,GAAGgB,CAAC,CAACf,CAAC,CAAC;IAAC,GAAGgB,CAAC,CAAChB,CAAC;EAAC,CAAC;EAAC,IAAIE,CAAC;AAAA;AAAC,SAASF,CAACA,CAACJ,CAAC,EAAC;EAAC,OAAM;IAAC,GAAGA,CAAC;IAACqB,KAAK,EAACnB,CAAC,CAACF,CAAC;EAAC,CAAC;AAAA;AAAC,SAASmB,CAACA,CAACnB,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACsB,aAAa,EAAEX,MAAM,CAAE,CAACX,CAAC,EAACE,CAAC,KAAG;IAAC,MAAMC,CAAC,GAACD,CAAC,EAAEa,IAAI,EAAEL,QAAQ,CAAC,OAAO,CAAC,IAAER,CAAC,EAAEa,IAAI,EAAEQ,QAAQ,CAAC,YAAY,CAAC,GAAC,OAAO,GAAC,MAAM;IAAC,OAAM;MAAC,GAAGvB,CAAC;MAAC,CAACE,CAAC,CAACa,IAAI,GAAE;QAACC,OAAO,EAAC;UAACP,IAAI,EAACN;QAAC;MAAC;IAAC,CAAC;EAAA,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA;AAAC,SAASiB,CAACA,CAACpB,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACwB,KAAK,EAAEb,MAAM,CAAE,CAACX,CAAC,EAACE,CAAC,MAAI;IAAC,GAAGF,CAAC;IAAC,CAACE,CAAC,CAACa,IAAI,GAAE;MAACC,OAAO,EAAC;QAACP,IAAI,EAAC;MAAM,CAAC;MAACS,YAAY,EAAC,EAAE,GAAChB,CAAC,CAACa;IAAI;EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOZ,CAAC,IAAIsB,2BAA2B,EAACrB,CAAC,IAAIsB,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}