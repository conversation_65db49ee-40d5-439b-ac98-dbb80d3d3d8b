{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst o = \"play\",\n  p = [\"play\", C({\n    outline: '<path d=\"M31.45 17.11L5.45 4.11002C5.14 3.95002 4.77 3.97002 4.47 4.15002C4.18 4.33002 4 4.65002 4 5.00002V31C4 31.35 4.18 31.67 4.47 31.85C4.63 31.95 4.81 32 5 32C5.15 32 5.31 31.96 5.45 31.89L31.45 18.89C31.79 18.72 32 18.37 32 18C32 17.63 31.79 17.28 31.45 17.11ZM6 29.38V6.62002L28.76 18L6 29.38Z\"/>',\n    solid: '<path d=\"M31.45 17.11L5.45 4.11002C5.14 3.95002 4.77 3.97002 4.47 4.15002C4.18 4.33002 4 4.65002 4 5.00002V31C4 31.35 4.18 31.67 4.47 31.85C4.63 31.95 4.81 32 5 32C5.15 32 5.31 31.96 5.45 31.89L31.45 18.89C31.79 18.72 32 18.37 32 18C32 17.63 31.79 17.28 31.45 17.11Z\"/>'\n  })];\nexport { p as playIcon, o as playIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "o", "p", "outline", "solid", "playIcon", "playIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/play.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const o=\"play\",p=[\"play\",C({outline:'<path d=\"M31.45 17.11L5.45 4.11002C5.14 3.95002 4.77 3.97002 4.47 4.15002C4.18 4.33002 4 4.65002 4 5.00002V31C4 31.35 4.18 31.67 4.47 31.85C4.63 31.95 4.81 32 5 32C5.15 32 5.31 31.96 5.45 31.89L31.45 18.89C31.79 18.72 32 18.37 32 18C32 17.63 31.79 17.28 31.45 17.11ZM6 29.38V6.62002L28.76 18L6 29.38Z\"/>',solid:'<path d=\"M31.45 17.11L5.45 4.11002C5.14 3.95002 4.77 3.97002 4.47 4.15002C4.18 4.33002 4 4.65002 4 5.00002V31C4 31.35 4.18 31.67 4.47 31.85C4.63 31.95 4.81 32 5 32C5.15 32 5.31 31.96 5.45 31.89L31.45 18.89C31.79 18.72 32 18.37 32 18C32 17.63 31.79 17.28 31.45 17.11Z\"/>'})];export{p as playIcon,o as playIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,MAAM;EAACC,CAAC,GAAC,CAAC,MAAM,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,iTAAiT;IAACC,KAAK,EAAC;EAA+Q,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,QAAQ,EAACJ,CAAC,IAAIK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}