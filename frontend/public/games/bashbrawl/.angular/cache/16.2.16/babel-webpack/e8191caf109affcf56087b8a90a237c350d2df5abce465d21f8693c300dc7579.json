{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst r = \"step-forward\",\n  o = [\"step-forward\", C({\n    outline: '<path d=\"M33 4H27C26.45 4 26 4.45 26 5V31C26 31.55 26.45 32 27 32H33C33.55 32 34 31.55 34 31V5C34 4.45 33.55 4 33 4ZM32 30H28V6H32V30ZM23.54 17.16L3.54 4.16C3.24 3.96 2.84 3.95 2.52 4.12C2.2 4.29 2 4.63 2 5V31C2 31.37 2.2 31.7 2.52 31.88C2.67 31.96 2.83 32 3 32C3.19 32 3.38 31.95 3.54 31.84L23.54 18.84C23.82 18.66 24 18.34 24 18C24 17.66 23.83 17.35 23.54 17.16ZM4 29.16V6.84L21.17 18L4 29.16Z\"/>',\n    solid: '<path d=\"M33 4H27C26.45 4 26 4.45 26 5V31C26 31.55 26.45 32 27 32H33C33.55 32 34 31.55 34 31V5C34 4.45 33.55 4 33 4ZM23.54 17.16L3.54 4.16C3.24 3.96 2.84 3.95 2.52 4.12C2.2 4.29 2 4.63 2 5V31C2 31.37 2.2 31.7 2.52 31.88C2.67 31.96 2.83 32 3 32C3.19 32 3.38 31.95 3.54 31.84L23.54 18.84C23.82 18.66 24 18.34 24 18C24 17.66 23.83 17.35 23.54 17.16Z\"/>'\n  })];\nexport { o as stepForwardIcon, r as stepForwardIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "r", "o", "outline", "solid", "stepForwardIcon", "stepForwardIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/step-forward.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const r=\"step-forward\",o=[\"step-forward\",C({outline:'<path d=\"M33 4H27C26.45 4 26 4.45 26 5V31C26 31.55 26.45 32 27 32H33C33.55 32 34 31.55 34 31V5C34 4.45 33.55 4 33 4ZM32 30H28V6H32V30ZM23.54 17.16L3.54 4.16C3.24 3.96 2.84 3.95 2.52 4.12C2.2 4.29 2 4.63 2 5V31C2 31.37 2.2 31.7 2.52 31.88C2.67 31.96 2.83 32 3 32C3.19 32 3.38 31.95 3.54 31.84L23.54 18.84C23.82 18.66 24 18.34 24 18C24 17.66 23.83 17.35 23.54 17.16ZM4 29.16V6.84L21.17 18L4 29.16Z\"/>',solid:'<path d=\"M33 4H27C26.45 4 26 4.45 26 5V31C26 31.55 26.45 32 27 32H33C33.55 32 34 31.55 34 31V5C34 4.45 33.55 4 33 4ZM23.54 17.16L3.54 4.16C3.24 3.96 2.84 3.95 2.52 4.12C2.2 4.29 2 4.63 2 5V31C2 31.37 2.2 31.7 2.52 31.88C2.67 31.96 2.83 32 3 32C3.19 32 3.38 31.95 3.54 31.84L23.54 18.84C23.82 18.66 24 18.34 24 18C24 17.66 23.83 17.35 23.54 17.16Z\"/>'})];export{o as stepForwardIcon,r as stepForwardIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,cAAc;EAACC,CAAC,GAAC,CAAC,cAAc,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,gZAAgZ;IAACC,KAAK,EAAC;EAA+V,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,eAAe,EAACJ,CAAC,IAAIK,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}