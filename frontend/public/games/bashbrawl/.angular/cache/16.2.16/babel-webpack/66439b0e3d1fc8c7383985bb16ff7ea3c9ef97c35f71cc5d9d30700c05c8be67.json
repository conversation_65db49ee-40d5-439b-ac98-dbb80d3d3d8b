{"ast": null, "code": "function t() {\n  return t => t.addInitializer(t => new o(t));\n}\nclass o {\n  constructor(t) {\n    this.host = t, this.host.addController(this);\n  }\n  hostConnected() {\n    this.host.ariaModal = \"true\", this.host.role = \"dialog\";\n  }\n}\nexport { o as AriaModalController, t as ariaModal };", "map": {"version": 3, "names": ["t", "addInitializer", "o", "constructor", "host", "addController", "hostConnected", "ariaModal", "role", "AriaModalController"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/aria-modal.controller.js"], "sourcesContent": ["function t(){return t=>t.addInitializer((t=>new o(t)))}class o{constructor(t){this.host=t,this.host.addController(this)}hostConnected(){this.host.ariaModal=\"true\",this.host.role=\"dialog\"}}export{o as AriaModalController,t as ariaModal};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAOA,CAAC,IAAEA,CAAC,CAACC,cAAc,CAAED,CAAC,IAAE,IAAIE,CAAC,CAACF,CAAC,CAAE,CAAC;AAAA;AAAC,MAAME,CAAC;EAACC,WAAWA,CAACH,CAAC,EAAC;IAAC,IAAI,CAACI,IAAI,GAACJ,CAAC,EAAC,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;EAAA;EAACC,aAAaA,CAAA,EAAE;IAAC,IAAI,CAACF,IAAI,CAACG,SAAS,GAAC,MAAM,EAAC,IAAI,CAACH,IAAI,CAACI,IAAI,GAAC,QAAQ;EAAA;AAAC;AAAC,SAAON,CAAC,IAAIO,mBAAmB,EAACT,CAAC,IAAIO,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}