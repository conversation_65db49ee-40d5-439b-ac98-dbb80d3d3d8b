{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"peso\",\n  e = [\"peso\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M27.0289 13.2003H30.1875C30.6362 13.2003 31 13.5584 31 14.0003C31 14.4421 30.6362 14.8003 30.1875 14.8003H26.5922C25.247 17.9796 22.0767 20.0369 18.5789 20.0003H10.8906V31.0003C10.8906 31.5525 10.4359 32.0003 9.875 32.0003C9.31409 32.0003 8.85938 31.5525 8.85938 31.0003V14.8003H5.8125C5.36377 14.8003 5 14.4421 5 14.0003C5 13.5584 5.36377 13.2003 5.8125 13.2003H8.85938V10.8003H5.8125C5.36377 10.8003 5 10.4421 5 10.0003C5 9.55844 5.36377 9.20026 5.8125 9.20026H8.85938V5.00026C8.85938 4.44798 9.31409 4.00026 9.875 4.00026H18.5789C22.08 3.96256 25.2524 6.02519 26.5922 9.21026H30.0148C30.4636 9.21026 30.8273 9.56844 30.8273 10.0103C30.8273 10.4521 30.4636 10.8103 30.0148 10.8103H27.0391C27.1036 11.2038 27.1375 11.6016 27.1406 12.0003C27.1395 12.4027 27.1021 12.8043 27.0289 13.2003ZM18.5789 6.00024H10.8906V9.20024H24.3476C23.1448 7.19409 20.9448 5.97374 18.5789 6.00024ZM10.8906 10.8003H24.9773C25.1535 11.5909 25.1535 12.4097 24.9773 13.2003H10.8906V10.8003ZM10.8906 18.0003H18.5789C20.9448 18.0268 23.1448 16.8064 24.3476 14.8003H10.8906V18.0003Z\"/>',\n    solid: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 18C2 9.16344 9.16344 2 18 2C22.2435 2 26.3131 3.68571 29.3137 6.68629C32.3143 9.68687 34 13.7565 34 18C34 26.8366 26.8366 34 18 34C9.16344 34 2 26.8366 2 18ZM28.48 17.8H26.03C25.0193 20.4239 22.5116 22.1669 19.7 22.2H14.51V28.74C14.51 29.4304 13.9504 29.99 13.26 29.99C12.5696 29.99 12.01 29.4304 12.01 28.74V17.8H9.09C8.59295 17.8 8.19 17.3971 8.19 16.9C8.19 16.4029 8.59295 16 9.09 16H12.01V13.8H9.09C8.59295 13.8 8.19 13.3971 8.19 12.9C8.19 12.4029 8.59295 12 9.09 12H12.01V9.26C12.0073 8.92675 12.1379 8.60623 12.3726 8.36965C12.6073 8.13306 12.9267 7.99999 13.26 8H19.7C22.3521 8.0282 24.7489 9.58707 25.85 12H28.5C28.9971 12 29.4 12.4029 29.4 12.9C29.4 13.3971 28.9971 13.8 28.5 13.8H26.42C26.5009 14.2286 26.5411 14.6639 26.54 15.1C26.5399 15.401 26.5199 15.7017 26.48 16H28.48C28.9771 16 29.38 16.4029 29.38 16.9C29.38 17.3971 28.9771 17.8 28.48 17.8ZM14.51 19.7H19.7C21.1082 19.6811 22.417 18.9706 23.2 17.8H14.51V19.7ZM14.51 10.51H19.7C20.9264 10.5197 22.0876 11.0638 22.88 12H14.51V10.51ZM14.51 13.8V16H23.96C24.0119 15.7061 24.0386 15.4084 24.04 15.11C24.0353 14.6661 23.9679 14.2251 23.84 13.8H14.51Z\"/>'\n  })];\nexport { e as pesoIcon, H as pesoIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "e", "outline", "solid", "pesoIcon", "pesoIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/peso.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"peso\",e=[\"peso\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M27.0289 13.2003H30.1875C30.6362 13.2003 31 13.5584 31 14.0003C31 14.4421 30.6362 14.8003 30.1875 14.8003H26.5922C25.247 17.9796 22.0767 20.0369 18.5789 20.0003H10.8906V31.0003C10.8906 31.5525 10.4359 32.0003 9.875 32.0003C9.31409 32.0003 8.85938 31.5525 8.85938 31.0003V14.8003H5.8125C5.36377 14.8003 5 14.4421 5 14.0003C5 13.5584 5.36377 13.2003 5.8125 13.2003H8.85938V10.8003H5.8125C5.36377 10.8003 5 10.4421 5 10.0003C5 9.55844 5.36377 9.20026 5.8125 9.20026H8.85938V5.00026C8.85938 4.44798 9.31409 4.00026 9.875 4.00026H18.5789C22.08 3.96256 25.2524 6.02519 26.5922 9.21026H30.0148C30.4636 9.21026 30.8273 9.56844 30.8273 10.0103C30.8273 10.4521 30.4636 10.8103 30.0148 10.8103H27.0391C27.1036 11.2038 27.1375 11.6016 27.1406 12.0003C27.1395 12.4027 27.1021 12.8043 27.0289 13.2003ZM18.5789 6.00024H10.8906V9.20024H24.3476C23.1448 7.19409 20.9448 5.97374 18.5789 6.00024ZM10.8906 10.8003H24.9773C25.1535 11.5909 25.1535 12.4097 24.9773 13.2003H10.8906V10.8003ZM10.8906 18.0003H18.5789C20.9448 18.0268 23.1448 16.8064 24.3476 14.8003H10.8906V18.0003Z\"/>',solid:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 18C2 9.16344 9.16344 2 18 2C22.2435 2 26.3131 3.68571 29.3137 6.68629C32.3143 9.68687 34 13.7565 34 18C34 26.8366 26.8366 34 18 34C9.16344 34 2 26.8366 2 18ZM28.48 17.8H26.03C25.0193 20.4239 22.5116 22.1669 19.7 22.2H14.51V28.74C14.51 29.4304 13.9504 29.99 13.26 29.99C12.5696 29.99 12.01 29.4304 12.01 28.74V17.8H9.09C8.59295 17.8 8.19 17.3971 8.19 16.9C8.19 16.4029 8.59295 16 9.09 16H12.01V13.8H9.09C8.59295 13.8 8.19 13.3971 8.19 12.9C8.19 12.4029 8.59295 12 9.09 12H12.01V9.26C12.0073 8.92675 12.1379 8.60623 12.3726 8.36965C12.6073 8.13306 12.9267 7.99999 13.26 8H19.7C22.3521 8.0282 24.7489 9.58707 25.85 12H28.5C28.9971 12 29.4 12.4029 29.4 12.9C29.4 13.3971 28.9971 13.8 28.5 13.8H26.42C26.5009 14.2286 26.5411 14.6639 26.54 15.1C26.5399 15.401 26.5199 15.7017 26.48 16H28.48C28.9771 16 29.38 16.4029 29.38 16.9C29.38 17.3971 28.9771 17.8 28.48 17.8ZM14.51 19.7H19.7C21.1082 19.6811 22.417 18.9706 23.2 17.8H14.51V19.7ZM14.51 10.51H19.7C20.9264 10.5197 22.0876 11.0638 22.88 12H14.51V10.51ZM14.51 13.8V16H23.96C24.0119 15.7061 24.0386 15.4084 24.04 15.11C24.0353 14.6661 23.9679 14.2251 23.84 13.8H14.51Z\"/>'})];export{e as pesoIcon,H as pesoIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,MAAM;EAACC,CAAC,GAAC,CAAC,MAAM,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,olCAAolC;IAACC,KAAK,EAAC;EAAkpC,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,QAAQ,EAACJ,CAAC,IAAIK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}