{"ast": null, "code": "const t = [{\n  transform: \"translate3d(0, 0, 0)\"\n}, {\n  transform: \"translate3d(-10px, 0, 0)\",\n  offset: .1\n}, {\n  transform: \"translate3d(10px, 0, 0)\",\n  offset: .2\n}, {\n  transform: \"translate3d(-10px, 0, 0)\",\n  offset: .3\n}, {\n  transform: \"translate3d(10px, 0, 0)\",\n  offset: .4\n}, {\n  transform: \"translate3d(-10px, 0, 0)\",\n  offset: .5\n}, {\n  transform: \"translate3d(10px, 0, 0)\",\n  offset: .6\n}, {\n  transform: \"translate3d(-10px, 0, 0)\",\n  offset: .7\n}, {\n  transform: \"translate3d(10px, 0, 0)\",\n  offset: .8\n}, {\n  transform: \"translate3d(-10px, 0, 0)\",\n  offset: .9\n}, {\n  transform: \"translate3d(0, 0, 0)\"\n}];\nexport { t as shakeKeyframes };", "map": {"version": 3, "names": ["t", "transform", "offset", "shakeKeyframes"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/motion/animations/keyframes/shake.js"], "sourcesContent": ["const t=[{transform:\"translate3d(0, 0, 0)\"},{transform:\"translate3d(-10px, 0, 0)\",offset:.1},{transform:\"translate3d(10px, 0, 0)\",offset:.2},{transform:\"translate3d(-10px, 0, 0)\",offset:.3},{transform:\"translate3d(10px, 0, 0)\",offset:.4},{transform:\"translate3d(-10px, 0, 0)\",offset:.5},{transform:\"translate3d(10px, 0, 0)\",offset:.6},{transform:\"translate3d(-10px, 0, 0)\",offset:.7},{transform:\"translate3d(10px, 0, 0)\",offset:.8},{transform:\"translate3d(-10px, 0, 0)\",offset:.9},{transform:\"translate3d(0, 0, 0)\"}];export{t as shakeKeyframes};\n"], "mappings": "AAAA,MAAMA,CAAC,GAAC,CAAC;EAACC,SAAS,EAAC;AAAsB,CAAC,EAAC;EAACA,SAAS,EAAC,0BAA0B;EAACC,MAAM,EAAC;AAAE,CAAC,EAAC;EAACD,SAAS,EAAC,yBAAyB;EAACC,MAAM,EAAC;AAAE,CAAC,EAAC;EAACD,SAAS,EAAC,0BAA0B;EAACC,MAAM,EAAC;AAAE,CAAC,EAAC;EAACD,SAAS,EAAC,yBAAyB;EAACC,MAAM,EAAC;AAAE,CAAC,EAAC;EAACD,SAAS,EAAC,0BAA0B;EAACC,MAAM,EAAC;AAAE,CAAC,EAAC;EAACD,SAAS,EAAC,yBAAyB;EAACC,MAAM,EAAC;AAAE,CAAC,EAAC;EAACD,SAAS,EAAC,0BAA0B;EAACC,MAAM,EAAC;AAAE,CAAC,EAAC;EAACD,SAAS,EAAC,yBAAyB;EAACC,MAAM,EAAC;AAAE,CAAC,EAAC;EAACD,SAAS,EAAC,0BAA0B;EAACC,MAAM,EAAC;AAAE,CAAC,EAAC;EAACD,SAAS,EAAC;AAAsB,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}