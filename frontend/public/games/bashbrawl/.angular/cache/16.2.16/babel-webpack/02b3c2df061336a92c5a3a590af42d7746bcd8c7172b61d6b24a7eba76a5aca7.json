{"ast": null, "code": "import _curry2 from \"./internal/_curry2.js\";\nimport _isInteger from \"./internal/_isInteger.js\";\nimport nth from \"./nth.js\";\n/**\n * Retrieves the values at given paths of an object.\n *\n * @func\n * @memberOf R\n * @since v0.27.1\n * @category Object\n * @typedefn Idx = [String | Int | Symbol]\n * @sig [Idx] -> {a} -> [a | Undefined]\n * @param {Array} pathsArray The array of paths to be fetched.\n * @param {Object} obj The object to retrieve the nested properties from.\n * @return {Array} A list consisting of values at paths specified by \"pathsArray\".\n * @see R.path\n * @example\n *\n *      R.paths([['a', 'b'], ['p', 0, 'q']], {a: {b: 2}, p: [{q: 3}]}); //=> [2, 3]\n *      R.paths([['a', 'b'], ['p', 'r']], {a: {b: 2}, p: [{q: 3}]}); //=> [2, undefined]\n */\n\nvar paths = /*#__PURE__*/\n_curry2(function paths(pathsArray, obj) {\n  return pathsArray.map(function (paths) {\n    var val = obj;\n    var idx = 0;\n    var p;\n    while (idx < paths.length) {\n      if (val == null) {\n        return;\n      }\n      p = paths[idx];\n      val = _isInteger(p) ? nth(p, val) : val[p];\n      idx += 1;\n    }\n    return val;\n  });\n});\nexport default paths;", "map": {"version": 3, "names": ["_curry2", "_isInteger", "nth", "paths", "pathsArray", "obj", "map", "val", "idx", "p", "length"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/paths.js"], "sourcesContent": ["import _curry2 from \"./internal/_curry2.js\";\nimport _isInteger from \"./internal/_isInteger.js\";\nimport nth from \"./nth.js\";\n/**\n * Retrieves the values at given paths of an object.\n *\n * @func\n * @memberOf R\n * @since v0.27.1\n * @category Object\n * @typedefn Idx = [String | Int | Symbol]\n * @sig [Idx] -> {a} -> [a | Undefined]\n * @param {Array} pathsArray The array of paths to be fetched.\n * @param {Object} obj The object to retrieve the nested properties from.\n * @return {Array} A list consisting of values at paths specified by \"pathsArray\".\n * @see R.path\n * @example\n *\n *      R.paths([['a', 'b'], ['p', 0, 'q']], {a: {b: 2}, p: [{q: 3}]}); //=> [2, 3]\n *      R.paths([['a', 'b'], ['p', 'r']], {a: {b: 2}, p: [{q: 3}]}); //=> [2, undefined]\n */\n\nvar paths =\n/*#__PURE__*/\n_curry2(function paths(pathsArray, obj) {\n  return pathsArray.map(function (paths) {\n    var val = obj;\n    var idx = 0;\n    var p;\n\n    while (idx < paths.length) {\n      if (val == null) {\n        return;\n      }\n\n      p = paths[idx];\n      val = _isInteger(p) ? nth(p, val) : val[p];\n      idx += 1;\n    }\n\n    return val;\n  });\n});\n\nexport default paths;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,GAAG,MAAM,UAAU;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,KAAK,GACT;AACAH,OAAO,CAAC,SAASG,KAAKA,CAACC,UAAU,EAAEC,GAAG,EAAE;EACtC,OAAOD,UAAU,CAACE,GAAG,CAAC,UAAUH,KAAK,EAAE;IACrC,IAAII,GAAG,GAAGF,GAAG;IACb,IAAIG,GAAG,GAAG,CAAC;IACX,IAAIC,CAAC;IAEL,OAAOD,GAAG,GAAGL,KAAK,CAACO,MAAM,EAAE;MACzB,IAAIH,GAAG,IAAI,IAAI,EAAE;QACf;MACF;MAEAE,CAAC,GAAGN,KAAK,CAACK,GAAG,CAAC;MACdD,GAAG,GAAGN,UAAU,CAACQ,CAAC,CAAC,GAAGP,GAAG,CAACO,CAAC,EAAEF,GAAG,CAAC,GAAGA,GAAG,CAACE,CAAC,CAAC;MAC1CD,GAAG,IAAI,CAAC;IACV;IAEA,OAAOD,GAAG;EACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,eAAeJ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}