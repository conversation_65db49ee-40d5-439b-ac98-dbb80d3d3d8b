{"ast": null, "code": "function t() {\n  return t => t.addInitializer(t => new e(t));\n}\nclass e {\n  constructor(t) {\n    this.host = t, this.host.addController(this);\n  }\n  hostUpdated() {\n    void 0 !== this.host.selectable && null !== this.host.selectable ? this.host.ariaMultiSelectable = \"multi\" === this.host.selectable ? \"true\" : \"false\" : this.host.ariaMultiSelectable = null;\n  }\n}\nexport { e as AriaMultiSelectableController, t as ariaMultiSelectable };", "map": {"version": 3, "names": ["t", "addInitializer", "e", "constructor", "host", "addController", "hostUpdated", "selectable", "ariaMultiSelectable", "AriaMultiSelectableController"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/aria-multiselectable.controller.js"], "sourcesContent": ["function t(){return t=>t.addInitializer((t=>new e(t)))}class e{constructor(t){this.host=t,this.host.addController(this)}hostUpdated(){void 0!==this.host.selectable&&null!==this.host.selectable?this.host.ariaMultiSelectable=\"multi\"===this.host.selectable?\"true\":\"false\":this.host.ariaMultiSelectable=null}}export{e as AriaMultiSelectableController,t as ariaMultiSelectable};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAOA,CAAC,IAAEA,CAAC,CAACC,cAAc,CAAED,CAAC,IAAE,IAAIE,CAAC,CAACF,CAAC,CAAE,CAAC;AAAA;AAAC,MAAME,CAAC;EAACC,WAAWA,CAACH,CAAC,EAAC;IAAC,IAAI,CAACI,IAAI,GAACJ,CAAC,EAAC,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;EAAA;EAACC,WAAWA,CAAA,EAAE;IAAC,KAAK,CAAC,KAAG,IAAI,CAACF,IAAI,CAACG,UAAU,IAAE,IAAI,KAAG,IAAI,CAACH,IAAI,CAACG,UAAU,GAAC,IAAI,CAACH,IAAI,CAACI,mBAAmB,GAAC,OAAO,KAAG,IAAI,CAACJ,IAAI,CAACG,UAAU,GAAC,MAAM,GAAC,OAAO,GAAC,IAAI,CAACH,IAAI,CAACI,mBAAmB,GAAC,IAAI;EAAA;AAAC;AAAC,SAAON,CAAC,IAAIO,6BAA6B,EAACT,CAAC,IAAIQ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}