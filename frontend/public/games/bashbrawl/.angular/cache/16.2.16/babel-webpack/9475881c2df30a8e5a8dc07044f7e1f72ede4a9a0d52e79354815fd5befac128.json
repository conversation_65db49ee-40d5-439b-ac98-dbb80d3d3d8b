{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"palmTree\",\n  L = [\"palmTree\", C({\n    outline: '<path d=\"M31.6573 31.2C31.0473 31.65 30.5973 31.99 29.6073 31.99C28.6173 31.99 28.1673 31.66 27.5573 31.2H27.5373V31.18C26.8273 30.65 25.9373 29.99 24.2773 29.99C22.6173 29.99 21.7173 30.65 21.0073 31.19C20.3873 31.65 19.9273 31.99 18.9373 31.99C17.9473 31.99 17.5073 31.66 16.8973 31.2H16.8773L16.8373 31.16C16.1273 30.63 15.2473 29.99 13.6073 29.99C11.9673 29.99 11.0473 30.65 10.3373 31.19C9.72727 31.65 9.27727 31.99 8.27727 31.99C7.27727 31.99 6.83727 31.66 6.22727 31.2H6.20727L6.16727 31.16C5.45727 30.64 4.57727 29.99 2.94727 29.99V31.99C3.92727 31.99 4.37727 32.32 4.98727 32.78H5.00727L5.04727 32.82C5.75727 33.35 6.63727 33.99 8.27727 33.99C9.91727 33.99 10.8373 33.33 11.5473 32.79C12.1673 32.33 12.6273 31.99 13.6173 31.99C14.6073 31.99 15.0473 32.32 15.6573 32.78H15.6773L15.6973 32.81C16.4073 33.34 17.2773 33.99 18.9373 33.99C20.5973 33.99 21.4973 33.33 22.2073 32.79H22.2273C22.8373 32.32 23.2873 31.99 24.2773 31.99C25.2673 31.99 25.7173 32.32 26.3273 32.78H26.3473V32.8C27.0573 33.33 27.9473 33.99 29.6073 33.99C31.2673 33.99 32.1573 33.33 32.8673 32.8L32.8873 32.78C33.4973 32.33 33.9473 31.99 34.9373 31.99V29.99C33.2673 29.99 32.3773 30.65 31.6673 31.19H31.6473L31.6573 31.2ZM23.2873 13.54C24.0173 12.05 25.8173 10.99 27.9373 10.99C30.2673 10.99 32.2273 12.27 32.7773 13.99H26.9373C26.3873 13.99 25.9373 14.44 25.9373 14.99C25.9373 15.54 26.3873 15.99 26.9373 15.99H33.9373C34.4873 15.99 34.9373 15.54 34.9373 14.99C34.9373 11.68 31.7973 8.99003 27.9373 8.99003C26.4373 8.99003 25.0473 9.40003 23.9073 10.09C24.0573 8.14003 23.2673 5.98003 21.6073 4.32003C18.8773 1.59003 14.8173 1.21003 12.5573 3.48003C12.1673 3.87003 12.1673 4.50003 12.5573 4.89003L15.3873 7.72003C15.7773 8.11003 16.4073 8.11003 16.7973 7.72003C17.1873 7.33003 17.1873 6.70003 16.7973 6.31003L14.7973 4.31003C16.3473 3.58003 18.5573 4.11003 20.1873 5.74003C21.8973 7.45003 22.3973 9.81003 21.4973 11.36C20.2173 9.92003 18.2073 8.99003 15.9373 8.99003C12.0773 8.99003 8.93727 11.68 8.93727 14.99C8.93727 15.54 9.38727 15.99 9.93727 15.99H17.9373C18.4873 15.99 18.9373 15.54 18.9373 14.99C18.9373 14.44 18.4873 13.99 17.9373 13.99H11.0973C11.6573 12.27 13.6173 10.99 15.9373 10.99C18.6973 10.99 20.9373 12.78 20.9373 14.99V24.32C18.5173 25.09 16.2773 26.34 14.4673 28.07C15.3573 28.22 15.9873 28.57 16.4973 28.93C21.3673 24.84 29.1373 24.22 34.9373 27.53V25.26C32.7073 24.18 30.2573 23.59 27.7873 23.46L23.2873 13.55V13.54ZM22.9373 23.79V17.6L25.5973 23.44C24.6973 23.49 23.8173 23.61 22.9373 23.78V23.79Z\"/>',\n    solid: '<path d=\"M31.6573 31.2C31.0473 31.65 30.5973 31.99 29.6073 31.99C28.6173 31.99 28.1673 31.66 27.5573 31.2H27.5373V31.18C26.8273 30.65 25.9373 29.99 24.2773 29.99C22.6173 29.99 21.7173 30.65 21.0073 31.19C20.3873 31.65 19.9273 31.99 18.9373 31.99C17.9473 31.99 17.5073 31.66 16.8973 31.2H16.8773L16.8373 31.16C16.1273 30.63 15.2473 29.99 13.6073 29.99C11.9673 29.99 11.0473 30.65 10.3373 31.19C9.72727 31.65 9.27727 31.99 8.27727 31.99C7.27727 31.99 6.83727 31.66 6.22727 31.2H6.20727L6.16727 31.16C5.45727 30.64 4.57727 29.99 2.94727 29.99V31.99C3.92727 31.99 4.37727 32.32 4.98727 32.78H5.00727L5.04727 32.82C5.75727 33.35 6.63727 33.99 8.27727 33.99C9.91727 33.99 10.8373 33.33 11.5473 32.79C12.1673 32.33 12.6273 31.99 13.6173 31.99C14.6073 31.99 15.0473 32.32 15.6573 32.78H15.6773L15.6973 32.81C16.4073 33.34 17.2773 33.99 18.9373 33.99C20.5973 33.99 21.4973 33.33 22.2073 32.79H22.2273C22.8373 32.32 23.2873 31.99 24.2773 31.99C25.2673 31.99 25.7173 32.32 26.3273 32.78H26.3473V32.8C27.0573 33.33 27.9473 33.99 29.6073 33.99C31.2673 33.99 32.1573 33.33 32.8673 32.8L32.8873 32.78C33.4973 32.33 33.9473 31.99 34.9373 31.99V29.99C33.2673 29.99 32.3773 30.65 31.6673 31.19H31.6473L31.6573 31.2ZM23.2873 13.54C24.0173 12.05 25.8173 10.99 27.9373 10.99C30.2673 10.99 32.2273 12.27 32.7773 13.99H26.9373C26.3873 13.99 25.9373 14.44 25.9373 14.99C25.9373 15.54 26.3873 15.99 26.9373 15.99H33.9373C34.4873 15.99 34.9373 15.54 34.9373 14.99C34.9373 11.68 31.7973 8.99003 27.9373 8.99003C26.4373 8.99003 25.0473 9.40003 23.9073 10.09C24.0573 8.14003 23.2673 5.98003 21.6073 4.32003C18.8773 1.59003 14.8173 1.21003 12.5573 3.48003C12.1673 3.87003 12.1673 4.50003 12.5573 4.89003L15.3873 7.72003C15.7773 8.11003 16.4073 8.11003 16.7973 7.72003C17.1873 7.33003 17.1873 6.70003 16.7973 6.31003L14.7973 4.31003C16.3473 3.58003 18.5573 4.11003 20.1873 5.74003C21.8973 7.45003 22.3973 9.81003 21.4973 11.36C20.2173 9.92003 18.2073 8.99003 15.9373 8.99003C12.0773 8.99003 8.93727 11.68 8.93727 14.99C8.93727 15.54 9.38727 15.99 9.93727 15.99H17.9373C18.4873 15.99 18.9373 15.54 18.9373 14.99C18.9373 14.44 18.4873 13.99 17.9373 13.99H11.0973C11.6573 12.27 13.6173 10.99 15.9373 10.99C18.6973 10.99 20.9373 12.78 20.9373 14.99V24.32C18.5173 25.09 16.2773 26.34 14.4673 28.07C15.3573 28.22 15.9873 28.57 16.4973 28.93C21.3673 24.84 29.1373 24.22 34.9373 27.53V25.26C32.7073 24.18 30.2573 23.59 27.7873 23.46L23.2873 13.55V13.54Z\"/>'\n  })];\nexport { L as palmTreeIcon, H as palmTreeIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "L", "outline", "solid", "palmTreeIcon", "palmTreeIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/palm-tree.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"palmTree\",L=[\"palmTree\",C({outline:'<path d=\"M31.6573 31.2C31.0473 31.65 30.5973 31.99 29.6073 31.99C28.6173 31.99 28.1673 31.66 27.5573 31.2H27.5373V31.18C26.8273 30.65 25.9373 29.99 24.2773 29.99C22.6173 29.99 21.7173 30.65 21.0073 31.19C20.3873 31.65 19.9273 31.99 18.9373 31.99C17.9473 31.99 17.5073 31.66 16.8973 31.2H16.8773L16.8373 31.16C16.1273 30.63 15.2473 29.99 13.6073 29.99C11.9673 29.99 11.0473 30.65 10.3373 31.19C9.72727 31.65 9.27727 31.99 8.27727 31.99C7.27727 31.99 6.83727 31.66 6.22727 31.2H6.20727L6.16727 31.16C5.45727 30.64 4.57727 29.99 2.94727 29.99V31.99C3.92727 31.99 4.37727 32.32 4.98727 32.78H5.00727L5.04727 32.82C5.75727 33.35 6.63727 33.99 8.27727 33.99C9.91727 33.99 10.8373 33.33 11.5473 32.79C12.1673 32.33 12.6273 31.99 13.6173 31.99C14.6073 31.99 15.0473 32.32 15.6573 32.78H15.6773L15.6973 32.81C16.4073 33.34 17.2773 33.99 18.9373 33.99C20.5973 33.99 21.4973 33.33 22.2073 32.79H22.2273C22.8373 32.32 23.2873 31.99 24.2773 31.99C25.2673 31.99 25.7173 32.32 26.3273 32.78H26.3473V32.8C27.0573 33.33 27.9473 33.99 29.6073 33.99C31.2673 33.99 32.1573 33.33 32.8673 32.8L32.8873 32.78C33.4973 32.33 33.9473 31.99 34.9373 31.99V29.99C33.2673 29.99 32.3773 30.65 31.6673 31.19H31.6473L31.6573 31.2ZM23.2873 13.54C24.0173 12.05 25.8173 10.99 27.9373 10.99C30.2673 10.99 32.2273 12.27 32.7773 13.99H26.9373C26.3873 13.99 25.9373 14.44 25.9373 14.99C25.9373 15.54 26.3873 15.99 26.9373 15.99H33.9373C34.4873 15.99 34.9373 15.54 34.9373 14.99C34.9373 11.68 31.7973 8.99003 27.9373 8.99003C26.4373 8.99003 25.0473 9.40003 23.9073 10.09C24.0573 8.14003 23.2673 5.98003 21.6073 4.32003C18.8773 1.59003 14.8173 1.21003 12.5573 3.48003C12.1673 3.87003 12.1673 4.50003 12.5573 4.89003L15.3873 7.72003C15.7773 8.11003 16.4073 8.11003 16.7973 7.72003C17.1873 7.33003 17.1873 6.70003 16.7973 6.31003L14.7973 4.31003C16.3473 3.58003 18.5573 4.11003 20.1873 5.74003C21.8973 7.45003 22.3973 9.81003 21.4973 11.36C20.2173 9.92003 18.2073 8.99003 15.9373 8.99003C12.0773 8.99003 8.93727 11.68 8.93727 14.99C8.93727 15.54 9.38727 15.99 9.93727 15.99H17.9373C18.4873 15.99 18.9373 15.54 18.9373 14.99C18.9373 14.44 18.4873 13.99 17.9373 13.99H11.0973C11.6573 12.27 13.6173 10.99 15.9373 10.99C18.6973 10.99 20.9373 12.78 20.9373 14.99V24.32C18.5173 25.09 16.2773 26.34 14.4673 28.07C15.3573 28.22 15.9873 28.57 16.4973 28.93C21.3673 24.84 29.1373 24.22 34.9373 27.53V25.26C32.7073 24.18 30.2573 23.59 27.7873 23.46L23.2873 13.55V13.54ZM22.9373 23.79V17.6L25.5973 23.44C24.6973 23.49 23.8173 23.61 22.9373 23.78V23.79Z\"/>',solid:'<path d=\"M31.6573 31.2C31.0473 31.65 30.5973 31.99 29.6073 31.99C28.6173 31.99 28.1673 31.66 27.5573 31.2H27.5373V31.18C26.8273 30.65 25.9373 29.99 24.2773 29.99C22.6173 29.99 21.7173 30.65 21.0073 31.19C20.3873 31.65 19.9273 31.99 18.9373 31.99C17.9473 31.99 17.5073 31.66 16.8973 31.2H16.8773L16.8373 31.16C16.1273 30.63 15.2473 29.99 13.6073 29.99C11.9673 29.99 11.0473 30.65 10.3373 31.19C9.72727 31.65 9.27727 31.99 8.27727 31.99C7.27727 31.99 6.83727 31.66 6.22727 31.2H6.20727L6.16727 31.16C5.45727 30.64 4.57727 29.99 2.94727 29.99V31.99C3.92727 31.99 4.37727 32.32 4.98727 32.78H5.00727L5.04727 32.82C5.75727 33.35 6.63727 33.99 8.27727 33.99C9.91727 33.99 10.8373 33.33 11.5473 32.79C12.1673 32.33 12.6273 31.99 13.6173 31.99C14.6073 31.99 15.0473 32.32 15.6573 32.78H15.6773L15.6973 32.81C16.4073 33.34 17.2773 33.99 18.9373 33.99C20.5973 33.99 21.4973 33.33 22.2073 32.79H22.2273C22.8373 32.32 23.2873 31.99 24.2773 31.99C25.2673 31.99 25.7173 32.32 26.3273 32.78H26.3473V32.8C27.0573 33.33 27.9473 33.99 29.6073 33.99C31.2673 33.99 32.1573 33.33 32.8673 32.8L32.8873 32.78C33.4973 32.33 33.9473 31.99 34.9373 31.99V29.99C33.2673 29.99 32.3773 30.65 31.6673 31.19H31.6473L31.6573 31.2ZM23.2873 13.54C24.0173 12.05 25.8173 10.99 27.9373 10.99C30.2673 10.99 32.2273 12.27 32.7773 13.99H26.9373C26.3873 13.99 25.9373 14.44 25.9373 14.99C25.9373 15.54 26.3873 15.99 26.9373 15.99H33.9373C34.4873 15.99 34.9373 15.54 34.9373 14.99C34.9373 11.68 31.7973 8.99003 27.9373 8.99003C26.4373 8.99003 25.0473 9.40003 23.9073 10.09C24.0573 8.14003 23.2673 5.98003 21.6073 4.32003C18.8773 1.59003 14.8173 1.21003 12.5573 3.48003C12.1673 3.87003 12.1673 4.50003 12.5573 4.89003L15.3873 7.72003C15.7773 8.11003 16.4073 8.11003 16.7973 7.72003C17.1873 7.33003 17.1873 6.70003 16.7973 6.31003L14.7973 4.31003C16.3473 3.58003 18.5573 4.11003 20.1873 5.74003C21.8973 7.45003 22.3973 9.81003 21.4973 11.36C20.2173 9.92003 18.2073 8.99003 15.9373 8.99003C12.0773 8.99003 8.93727 11.68 8.93727 14.99C8.93727 15.54 9.38727 15.99 9.93727 15.99H17.9373C18.4873 15.99 18.9373 15.54 18.9373 14.99C18.9373 14.44 18.4873 13.99 17.9373 13.99H11.0973C11.6573 12.27 13.6173 10.99 15.9373 10.99C18.6973 10.99 20.9373 12.78 20.9373 14.99V24.32C18.5173 25.09 16.2773 26.34 14.4673 28.07C15.3573 28.22 15.9873 28.57 16.4973 28.93C21.3673 24.84 29.1373 24.22 34.9373 27.53V25.26C32.7073 24.18 30.2573 23.59 27.7873 23.46L23.2873 13.55V13.54Z\"/>'})];export{L as palmTreeIcon,H as palmTreeIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,UAAU;EAACC,CAAC,GAAC,CAAC,UAAU,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,q8EAAq8E;IAACC,KAAK,EAAC;EAAm3E,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,YAAY,EAACJ,CAAC,IAAIK,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}