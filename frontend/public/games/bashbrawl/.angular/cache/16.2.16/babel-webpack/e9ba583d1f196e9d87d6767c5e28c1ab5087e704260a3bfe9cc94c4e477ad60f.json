{"ast": null, "code": "import { listenForAttributeChange as t } from \"../utils/events.js\";\nimport { GlobalStateService as e } from \"../services/global.service.js\";\nfunction s() {\n  return t => {\n    t.addInitializer(t => {\n      t.layerController || (t.layerController = new r(t));\n    });\n  };\n}\nclass r {\n  constructor(t) {\n    this.host = t, this.host.addController(this);\n  }\n  get isActiveLayer() {\n    return this.layers[this.layers.length - 1] === this.host;\n  }\n  get layerIndex() {\n    const t = this.layers.indexOf(this.host);\n    return -1 === t ? null : t;\n  }\n  get layers() {\n    return [...e.state.layerElements];\n  }\n  set layers(t) {\n    e.state.layerElements = [...t];\n  }\n  hostConnected() {\n    this.host.hasAttribute(\"_demo-mode\") || (this.updateLayer(), this.observer = t(this.host, \"hidden\", () => this.updateLayer()));\n  }\n  hostDisconnected() {\n    this.observer?.disconnect(), this.removeLayer();\n  }\n  updateLayer() {\n    this.host.hasAttribute(\"hidden\") ? this.removeLayer() : this.addLayer(), this.layers.forEach((t, e) => t.setAttribute(\"cds-layer\", \"\" + e));\n  }\n  addLayer() {\n    this.layers.find(t => this.host === t) || (this.layers = [...this.layers, this.host], \"true\" === this.host.ariaModal && (this.layers.filter(t => \"true\" === t.ariaModal).forEach(t => t.role = \"region\"), this.host.role = \"dialog\"));\n  }\n  removeLayer() {\n    this.layers = this.layers.filter(t => t !== this.host), this.host.removeAttribute(\"cds-layer\");\n    const t = this.layers.find(t => \"true\" === t.ariaModal);\n    t && (t.role = \"dialog\");\n  }\n}\nexport { r as LayerController, s as layer };", "map": {"version": 3, "names": ["listenForAttributeChange", "t", "GlobalStateService", "e", "s", "addInitializer", "layerController", "r", "constructor", "host", "addController", "isActiveLayer", "layers", "length", "layerIndex", "indexOf", "state", "layerElements", "hostConnected", "hasAttribute", "updateLayer", "observer", "hostDisconnected", "disconnect", "<PERSON><PERSON><PERSON>er", "add<PERSON><PERSON>er", "for<PERSON>ach", "setAttribute", "find", "ariaModal", "filter", "role", "removeAttribute", "LayerController", "layer"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/layer.controller.js"], "sourcesContent": ["import{listenForAttributeChange as t}from\"../utils/events.js\";import{GlobalStateService as e}from\"../services/global.service.js\";function s(){return t=>{t.addInitializer((t=>{t.layerController||(t.layerController=new r(t))}))}}class r{constructor(t){this.host=t,this.host.addController(this)}get isActiveLayer(){return this.layers[this.layers.length-1]===this.host}get layerIndex(){const t=this.layers.indexOf(this.host);return-1===t?null:t}get layers(){return[...e.state.layerElements]}set layers(t){e.state.layerElements=[...t]}hostConnected(){this.host.hasAttribute(\"_demo-mode\")||(this.updateLayer(),this.observer=t(this.host,\"hidden\",(()=>this.updateLayer())))}hostDisconnected(){this.observer?.disconnect(),this.removeLayer()}updateLayer(){this.host.hasAttribute(\"hidden\")?this.removeLayer():this.addLayer(),this.layers.forEach(((t,e)=>t.setAttribute(\"cds-layer\",\"\"+e)))}addLayer(){this.layers.find((t=>this.host===t))||(this.layers=[...this.layers,this.host],\"true\"===this.host.ariaModal&&(this.layers.filter((t=>\"true\"===t.ariaModal)).forEach((t=>t.role=\"region\")),this.host.role=\"dialog\"))}removeLayer(){this.layers=this.layers.filter((t=>t!==this.host)),this.host.removeAttribute(\"cds-layer\");const t=this.layers.find((t=>\"true\"===t.ariaModal));t&&(t.role=\"dialog\")}}export{r as LayerController,s as layer};\n"], "mappings": "AAAA,SAAOA,wBAAwB,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,kBAAkB,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,OAAOH,CAAC,IAAE;IAACA,CAAC,CAACI,cAAc,CAAEJ,CAAC,IAAE;MAACA,CAAC,CAACK,eAAe,KAAGL,CAAC,CAACK,eAAe,GAAC,IAAIC,CAAC,CAACN,CAAC,CAAC,CAAC;IAAA,CAAE,CAAC;EAAA,CAAC;AAAA;AAAC,MAAMM,CAAC;EAACC,WAAWA,CAACP,CAAC,EAAC;IAAC,IAAI,CAACQ,IAAI,GAACR,CAAC,EAAC,IAAI,CAACQ,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;EAAA;EAAC,IAAIC,aAAaA,CAAA,EAAE;IAAC,OAAO,IAAI,CAACC,MAAM,CAAC,IAAI,CAACA,MAAM,CAACC,MAAM,GAAC,CAAC,CAAC,KAAG,IAAI,CAACJ,IAAI;EAAA;EAAC,IAAIK,UAAUA,CAAA,EAAE;IAAC,MAAMb,CAAC,GAAC,IAAI,CAACW,MAAM,CAACG,OAAO,CAAC,IAAI,CAACN,IAAI,CAAC;IAAC,OAAM,CAAC,CAAC,KAAGR,CAAC,GAAC,IAAI,GAACA,CAAC;EAAA;EAAC,IAAIW,MAAMA,CAAA,EAAE;IAAC,OAAM,CAAC,GAAGT,CAAC,CAACa,KAAK,CAACC,aAAa,CAAC;EAAA;EAAC,IAAIL,MAAMA,CAACX,CAAC,EAAC;IAACE,CAAC,CAACa,KAAK,CAACC,aAAa,GAAC,CAAC,GAAGhB,CAAC,CAAC;EAAA;EAACiB,aAAaA,CAAA,EAAE;IAAC,IAAI,CAACT,IAAI,CAACU,YAAY,CAAC,YAAY,CAAC,KAAG,IAAI,CAACC,WAAW,CAAC,CAAC,EAAC,IAAI,CAACC,QAAQ,GAACpB,CAAC,CAAC,IAAI,CAACQ,IAAI,EAAC,QAAQ,EAAE,MAAI,IAAI,CAACW,WAAW,CAAC,CAAE,CAAC,CAAC;EAAA;EAACE,gBAAgBA,CAAA,EAAE;IAAC,IAAI,CAACD,QAAQ,EAAEE,UAAU,CAAC,CAAC,EAAC,IAAI,CAACC,WAAW,CAAC,CAAC;EAAA;EAACJ,WAAWA,CAAA,EAAE;IAAC,IAAI,CAACX,IAAI,CAACU,YAAY,CAAC,QAAQ,CAAC,GAAC,IAAI,CAACK,WAAW,CAAC,CAAC,GAAC,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAC,IAAI,CAACb,MAAM,CAACc,OAAO,CAAE,CAACzB,CAAC,EAACE,CAAC,KAAGF,CAAC,CAAC0B,YAAY,CAAC,WAAW,EAAC,EAAE,GAACxB,CAAC,CAAE,CAAC;EAAA;EAACsB,QAAQA,CAAA,EAAE;IAAC,IAAI,CAACb,MAAM,CAACgB,IAAI,CAAE3B,CAAC,IAAE,IAAI,CAACQ,IAAI,KAAGR,CAAE,CAAC,KAAG,IAAI,CAACW,MAAM,GAAC,CAAC,GAAG,IAAI,CAACA,MAAM,EAAC,IAAI,CAACH,IAAI,CAAC,EAAC,MAAM,KAAG,IAAI,CAACA,IAAI,CAACoB,SAAS,KAAG,IAAI,CAACjB,MAAM,CAACkB,MAAM,CAAE7B,CAAC,IAAE,MAAM,KAAGA,CAAC,CAAC4B,SAAU,CAAC,CAACH,OAAO,CAAEzB,CAAC,IAAEA,CAAC,CAAC8B,IAAI,GAAC,QAAS,CAAC,EAAC,IAAI,CAACtB,IAAI,CAACsB,IAAI,GAAC,QAAQ,CAAC,CAAC;EAAA;EAACP,WAAWA,CAAA,EAAE;IAAC,IAAI,CAACZ,MAAM,GAAC,IAAI,CAACA,MAAM,CAACkB,MAAM,CAAE7B,CAAC,IAAEA,CAAC,KAAG,IAAI,CAACQ,IAAK,CAAC,EAAC,IAAI,CAACA,IAAI,CAACuB,eAAe,CAAC,WAAW,CAAC;IAAC,MAAM/B,CAAC,GAAC,IAAI,CAACW,MAAM,CAACgB,IAAI,CAAE3B,CAAC,IAAE,MAAM,KAAGA,CAAC,CAAC4B,SAAU,CAAC;IAAC5B,CAAC,KAAGA,CAAC,CAAC8B,IAAI,GAAC,QAAQ,CAAC;EAAA;AAAC;AAAC,SAAOxB,CAAC,IAAI0B,eAAe,EAAC7B,CAAC,IAAI8B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}