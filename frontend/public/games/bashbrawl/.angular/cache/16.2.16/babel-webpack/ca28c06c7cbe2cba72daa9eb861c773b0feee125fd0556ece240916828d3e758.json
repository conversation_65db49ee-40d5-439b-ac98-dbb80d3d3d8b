{"ast": null, "code": "// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\nexport const environment = {\n  production: false,\n  server: 'http://localhost:16210',\n  imprint: '',\n  privacypolicy: ''\n};\n/*\n * For easier debugging in development mode, you can import the following file\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\n *\n * This import should be commented out in production mode because it will have a negative impact\n * on performance if an error is thrown.\n */\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.", "map": {"version": 3, "names": ["environment", "production", "server", "imprint", "privacypolicy"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/src/environments/environment.ts"], "sourcesContent": ["// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\nexport const environment = {\n  production: false,\n  server: 'http://localhost:16210',\n  imprint: '',\n  privacypolicy: '',\n};\n/*\n * For easier debugging in development mode, you can import the following file\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\n *\n * This import should be commented out in production mode because it will have a negative impact\n * on performance if an error is thrown.\n */\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,wBAAwB;EAChCC,OAAO,EAAE,EAAE;EACXC,aAAa,EAAE;CAChB;AACD;;;;;;;AAOA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}