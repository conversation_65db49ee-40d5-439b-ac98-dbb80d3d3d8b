{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"piggy-bank\",\n  p = [\"piggy-bank\", C({\n    outline: '<path d=\"M18.6015 9.58509C18.6015 9.58509 18.521 9.56507 18.4707 9.56507V9.60512C16.3491 9.19466 14.157 9.37486 12.1258 10.1257C11.7135 10.2859 11.5124 10.7564 11.6733 11.1669C11.8342 11.5773 12.3068 11.7775 12.7191 11.6174C14.4586 10.9566 16.3591 10.7964 18.1892 11.1368C18.6216 11.2469 19.054 10.9967 19.1746 10.5662C19.2852 10.1357 19.0338 9.70523 18.6015 9.58509Z\"/><path d=\"M23.2772 14.3003C22.5231 14.3003 21.9097 14.911 21.9097 15.6618C21.9097 16.4127 22.5231 17.0234 23.2772 17.0234C24.0314 17.0234 24.6447 16.4127 24.6447 15.6618C24.6447 14.911 24.0314 14.3003 23.2772 14.3003Z\"/><path d=\"M32.8097 17.564C32.317 17.3537 31.8746 17.0434 31.4824 16.6429C30.4568 15.4116 29.5819 14.0501 28.868 12.5884L31.5427 8.05339C31.8947 7.46273 31.9148 6.74193 31.6031 6.13126C31.2914 5.52058 30.688 5.11012 30.0043 5.05005C29.7127 5.02002 29.4211 5.01001 29.1294 5.01001C27.7519 5.01001 26.4145 5.31034 25.1576 5.901C24.2626 6.34149 23.4482 6.88209 22.694 7.51279C21.1857 6.802 19.5668 6.33148 17.8976 6.13126C17.425 6.0812 16.9725 6.06118 16.5401 6.06118C11.5225 6.06118 7.08805 8.93437 5.12725 13.0489C4.70492 12.7386 4.39321 12.3081 4.22226 11.8076C4.08149 11.337 4.13177 10.8365 4.36304 10.406C4.46359 10.2258 4.61442 10.0756 4.79542 9.97553C5.30824 9.66518 5.46913 9.00445 5.15741 8.49388C4.8457 7.98331 4.18204 7.82314 3.65916 8.13348C3.1765 8.43381 2.77429 8.84427 2.49274 9.34483C1.97991 10.2759 1.85925 11.3871 2.17097 12.4082C2.5229 13.5695 3.32733 14.5306 4.37309 15.1313C4.24237 15.6418 4.14182 16.1624 4.09154 16.703C3.80999 19.9566 4.38315 23.1902 5.76074 26.0734L5.91157 26.4338C6.65567 28.1457 7.58076 29.7375 8.6768 31.1891C9.0589 31.6897 9.65217 31.99 10.2857 31.99H12.7291C13.3827 31.99 13.9961 31.6696 14.3681 31.139C14.6698 30.7086 14.7905 30.178 14.71 29.6674C15.1223 29.7275 15.4943 29.7675 15.7658 29.7976C16.339 29.8476 16.9222 29.8776 17.4853 29.8776C18.1691 29.8776 18.8528 29.8376 19.5366 29.7575C19.8584 30.3281 20.1802 30.8988 20.3109 31.099C20.6829 31.6696 21.3164 32.01 22.0002 32.01H24.2626C24.9062 32.01 25.4995 31.7097 25.8816 31.1891C26.2637 30.6785 26.3743 30.0178 26.1832 29.4071C26.0726 29.0367 25.9218 28.436 25.781 27.8354C26.4245 27.465 27.0279 27.0345 27.5709 26.5239C27.8625 26.2536 28.0636 25.9132 28.1541 25.5428C28.7373 25.6229 29.3104 25.673 29.8735 25.673C30.1149 25.673 30.3663 25.673 30.6076 25.6529C31.412 25.6029 32.1159 25.0723 32.3773 24.3215L33.8856 20.0367C34.2376 19.0456 33.7549 17.9544 32.7896 17.5339L32.8097 17.564ZM30.497 23.6808C30.2959 23.6908 30.0847 23.7008 29.8836 23.7008C29.3406 23.7008 28.7976 23.6607 28.2647 23.5706C27.42 23.4205 26.6156 23.1101 25.8614 22.6796C25.7307 22.6096 25.5799 22.5695 25.4391 22.5695C25.0872 22.5695 24.7554 22.7798 24.5844 23.1402C24.3632 23.6307 24.5341 24.2314 24.9766 24.4917C25.3687 24.7219 25.781 24.9121 26.2033 25.0923C25.6905 25.5729 25.1073 25.9733 24.4939 26.2836L23.4884 26.7742C23.4884 26.7742 23.9509 29.0267 24.2626 30.0278H22.0002C21.7689 29.6774 20.6226 27.5951 20.6226 27.5951L19.9891 27.6852C19.1545 27.8153 18.3199 27.8754 17.4853 27.8754C16.9725 27.8754 16.4697 27.8554 15.967 27.8053C15.3435 27.7453 14.2978 27.5951 13.4833 27.4449L12.0152 27.1646C12.0152 27.1646 12.6085 29.6674 12.7291 29.9878H10.2857C9.29018 28.6563 8.43547 27.2047 7.76176 25.6529L7.59082 25.2525C6.354 22.6796 5.84118 19.7764 6.09257 16.8932C6.57522 11.8977 11.2309 8.05339 16.5401 8.05339C16.9122 8.05339 17.2842 8.07341 17.6563 8.11346C19.3355 8.31368 20.9645 8.83426 22.4728 9.63515L23.056 9.93548L23.5286 9.45495C24.2626 8.73415 25.0972 8.1535 26.0022 7.703C26.9876 7.24249 28.0435 7.00222 29.1194 7.00222C29.3507 7.00222 29.5719 7.01223 29.8032 7.03226L26.5955 12.4783L26.8368 12.9889C27.6614 14.8009 28.7172 16.4727 29.9741 17.9644C30.5573 18.5851 31.2511 19.0656 32.0053 19.396L30.497 23.6808Z\"/>',\n    solid: '<path d=\"M33.3805 18.431C33.3805 18.431 33.3504 18.421 33.3404 18.421C32.6286 18.1297 31.9669 17.6776 31.4155 17.105C30.1522 15.5881 29.0895 13.8904 28.2574 12.052L31.5157 6.53696C31.7864 6.105 31.6761 5.52235 31.2851 5.23103C31.1949 5.17075 31.1047 5.12053 30.9944 5.09039C29.3201 4.84929 27.6157 5.11048 26.0718 5.84381C24.979 6.36619 23.9664 7.06938 23.064 7.93331C21.3497 7.01916 19.4949 6.42646 17.59 6.19541C12.3065 5.65295 7.20337 8.53605 5.06789 13.0265C4.67689 12.715 4.37612 12.3032 4.21571 11.821C4.07535 11.3488 4.12548 10.8466 4.35607 10.4146C4.45633 10.2338 4.60671 10.0831 4.78717 9.98262C5.29849 9.67121 5.4589 9.0082 5.1481 8.49587C4.8373 7.98354 4.17561 7.82281 3.65427 8.13422C3.17303 8.43559 2.77201 8.84747 2.49129 9.34975C1.97997 10.294 1.85967 11.4091 2.17046 12.4338C2.52136 13.579 3.30337 14.5333 4.32599 15.136C4.22573 15.568 4.1355 16 4.08537 16.452C4.08537 16.452 3.96507 17.1351 3.96507 17.758V19.0036C4.11545 21.6557 4.77715 24.2475 5.89 26.6182C6.66198 28.4064 7.62445 30.084 8.76738 31.6109C8.94784 31.8621 9.23859 32.0127 9.53936 32.0027H13.0083C13.279 31.9625 13.4795 31.6913 13.4394 31.4C13.4394 31.3397 13.4193 31.2794 13.3892 31.2191L13.1787 30.4054C13.0484 30.0036 12.9481 29.5917 12.8579 29.1698C13.7301 29.3406 14.853 29.4913 15.6149 29.5616C16.6075 29.642 17.6101 29.642 18.6126 29.5616C18.9836 30.2949 19.3746 30.9479 19.7155 31.4904C19.8859 31.7616 20.1566 31.9223 20.4574 31.9223H23.445C23.7859 31.9223 24.0666 31.6109 24.0566 31.2392C24.0566 31.1689 24.0466 31.1086 24.0265 31.0383C23.806 30.2648 23.5152 29.21 23.3448 28.5671C24.618 28.1251 25.8211 27.5123 26.944 26.7287C27.0944 26.6182 27.2548 26.4575 27.4052 26.3169C26.6031 26.0456 25.8211 25.7041 25.0792 25.2721C24.6982 25.0612 24.5579 24.5589 24.7484 24.147C24.9489 23.7351 25.4101 23.5744 25.781 23.7954C26.7435 24.3379 27.7761 24.7196 28.8389 24.9406C29.691 25.0812 30.5533 25.1214 31.4155 25.0612C31.8065 25.0411 32.1473 24.7698 32.2777 24.368L33.9419 19.737C34.1224 19.1945 33.8718 18.5918 33.3805 18.3908V18.431ZM18.6327 10.3443C18.6327 10.3443 18.5625 10.3342 18.5324 10.3242C16.6676 9.9324 14.7527 10.1032 12.9682 10.8164C12.8779 10.8566 12.7777 10.8666 12.6774 10.8666C12.2263 10.8666 11.8653 10.4548 11.8754 9.96253C11.8754 9.60089 12.0859 9.27943 12.3867 9.14883C14.4419 8.31505 16.6576 8.12418 18.8131 8.57623C19.2643 8.63651 19.5851 9.06847 19.525 9.56071C19.4648 10.0529 19.0638 10.3945 18.6227 10.3342L18.6327 10.3443ZM24.8587 17.0648C24.1067 17.0648 23.4952 16.452 23.4952 15.6986C23.4952 14.9452 24.1067 14.3324 24.8587 14.3324C25.6106 14.3324 26.2221 14.9452 26.2221 15.6986C26.2221 16.452 25.6106 17.0648 24.8587 17.0648Z\"/>'\n  })];\nexport { p as piggyBankIcon, L as piggyBankIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "p", "outline", "solid", "piggyBankIcon", "piggyBankIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/piggy-bank.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"piggy-bank\",p=[\"piggy-bank\",C({outline:'<path d=\"M18.6015 9.58509C18.6015 9.58509 18.521 9.56507 18.4707 9.56507V9.60512C16.3491 9.19466 14.157 9.37486 12.1258 10.1257C11.7135 10.2859 11.5124 10.7564 11.6733 11.1669C11.8342 11.5773 12.3068 11.7775 12.7191 11.6174C14.4586 10.9566 16.3591 10.7964 18.1892 11.1368C18.6216 11.2469 19.054 10.9967 19.1746 10.5662C19.2852 10.1357 19.0338 9.70523 18.6015 9.58509Z\"/><path d=\"M23.2772 14.3003C22.5231 14.3003 21.9097 14.911 21.9097 15.6618C21.9097 16.4127 22.5231 17.0234 23.2772 17.0234C24.0314 17.0234 24.6447 16.4127 24.6447 15.6618C24.6447 14.911 24.0314 14.3003 23.2772 14.3003Z\"/><path d=\"M32.8097 17.564C32.317 17.3537 31.8746 17.0434 31.4824 16.6429C30.4568 15.4116 29.5819 14.0501 28.868 12.5884L31.5427 8.05339C31.8947 7.46273 31.9148 6.74193 31.6031 6.13126C31.2914 5.52058 30.688 5.11012 30.0043 5.05005C29.7127 5.02002 29.4211 5.01001 29.1294 5.01001C27.7519 5.01001 26.4145 5.31034 25.1576 5.901C24.2626 6.34149 23.4482 6.88209 22.694 7.51279C21.1857 6.802 19.5668 6.33148 17.8976 6.13126C17.425 6.0812 16.9725 6.06118 16.5401 6.06118C11.5225 6.06118 7.08805 8.93437 5.12725 13.0489C4.70492 12.7386 4.39321 12.3081 4.22226 11.8076C4.08149 11.337 4.13177 10.8365 4.36304 10.406C4.46359 10.2258 4.61442 10.0756 4.79542 9.97553C5.30824 9.66518 5.46913 9.00445 5.15741 8.49388C4.8457 7.98331 4.18204 7.82314 3.65916 8.13348C3.1765 8.43381 2.77429 8.84427 2.49274 9.34483C1.97991 10.2759 1.85925 11.3871 2.17097 12.4082C2.5229 13.5695 3.32733 14.5306 4.37309 15.1313C4.24237 15.6418 4.14182 16.1624 4.09154 16.703C3.80999 19.9566 4.38315 23.1902 5.76074 26.0734L5.91157 26.4338C6.65567 28.1457 7.58076 29.7375 8.6768 31.1891C9.0589 31.6897 9.65217 31.99 10.2857 31.99H12.7291C13.3827 31.99 13.9961 31.6696 14.3681 31.139C14.6698 30.7086 14.7905 30.178 14.71 29.6674C15.1223 29.7275 15.4943 29.7675 15.7658 29.7976C16.339 29.8476 16.9222 29.8776 17.4853 29.8776C18.1691 29.8776 18.8528 29.8376 19.5366 29.7575C19.8584 30.3281 20.1802 30.8988 20.3109 31.099C20.6829 31.6696 21.3164 32.01 22.0002 32.01H24.2626C24.9062 32.01 25.4995 31.7097 25.8816 31.1891C26.2637 30.6785 26.3743 30.0178 26.1832 29.4071C26.0726 29.0367 25.9218 28.436 25.781 27.8354C26.4245 27.465 27.0279 27.0345 27.5709 26.5239C27.8625 26.2536 28.0636 25.9132 28.1541 25.5428C28.7373 25.6229 29.3104 25.673 29.8735 25.673C30.1149 25.673 30.3663 25.673 30.6076 25.6529C31.412 25.6029 32.1159 25.0723 32.3773 24.3215L33.8856 20.0367C34.2376 19.0456 33.7549 17.9544 32.7896 17.5339L32.8097 17.564ZM30.497 23.6808C30.2959 23.6908 30.0847 23.7008 29.8836 23.7008C29.3406 23.7008 28.7976 23.6607 28.2647 23.5706C27.42 23.4205 26.6156 23.1101 25.8614 22.6796C25.7307 22.6096 25.5799 22.5695 25.4391 22.5695C25.0872 22.5695 24.7554 22.7798 24.5844 23.1402C24.3632 23.6307 24.5341 24.2314 24.9766 24.4917C25.3687 24.7219 25.781 24.9121 26.2033 25.0923C25.6905 25.5729 25.1073 25.9733 24.4939 26.2836L23.4884 26.7742C23.4884 26.7742 23.9509 29.0267 24.2626 30.0278H22.0002C21.7689 29.6774 20.6226 27.5951 20.6226 27.5951L19.9891 27.6852C19.1545 27.8153 18.3199 27.8754 17.4853 27.8754C16.9725 27.8754 16.4697 27.8554 15.967 27.8053C15.3435 27.7453 14.2978 27.5951 13.4833 27.4449L12.0152 27.1646C12.0152 27.1646 12.6085 29.6674 12.7291 29.9878H10.2857C9.29018 28.6563 8.43547 27.2047 7.76176 25.6529L7.59082 25.2525C6.354 22.6796 5.84118 19.7764 6.09257 16.8932C6.57522 11.8977 11.2309 8.05339 16.5401 8.05339C16.9122 8.05339 17.2842 8.07341 17.6563 8.11346C19.3355 8.31368 20.9645 8.83426 22.4728 9.63515L23.056 9.93548L23.5286 9.45495C24.2626 8.73415 25.0972 8.1535 26.0022 7.703C26.9876 7.24249 28.0435 7.00222 29.1194 7.00222C29.3507 7.00222 29.5719 7.01223 29.8032 7.03226L26.5955 12.4783L26.8368 12.9889C27.6614 14.8009 28.7172 16.4727 29.9741 17.9644C30.5573 18.5851 31.2511 19.0656 32.0053 19.396L30.497 23.6808Z\"/>',solid:'<path d=\"M33.3805 18.431C33.3805 18.431 33.3504 18.421 33.3404 18.421C32.6286 18.1297 31.9669 17.6776 31.4155 17.105C30.1522 15.5881 29.0895 13.8904 28.2574 12.052L31.5157 6.53696C31.7864 6.105 31.6761 5.52235 31.2851 5.23103C31.1949 5.17075 31.1047 5.12053 30.9944 5.09039C29.3201 4.84929 27.6157 5.11048 26.0718 5.84381C24.979 6.36619 23.9664 7.06938 23.064 7.93331C21.3497 7.01916 19.4949 6.42646 17.59 6.19541C12.3065 5.65295 7.20337 8.53605 5.06789 13.0265C4.67689 12.715 4.37612 12.3032 4.21571 11.821C4.07535 11.3488 4.12548 10.8466 4.35607 10.4146C4.45633 10.2338 4.60671 10.0831 4.78717 9.98262C5.29849 9.67121 5.4589 9.0082 5.1481 8.49587C4.8373 7.98354 4.17561 7.82281 3.65427 8.13422C3.17303 8.43559 2.77201 8.84747 2.49129 9.34975C1.97997 10.294 1.85967 11.4091 2.17046 12.4338C2.52136 13.579 3.30337 14.5333 4.32599 15.136C4.22573 15.568 4.1355 16 4.08537 16.452C4.08537 16.452 3.96507 17.1351 3.96507 17.758V19.0036C4.11545 21.6557 4.77715 24.2475 5.89 26.6182C6.66198 28.4064 7.62445 30.084 8.76738 31.6109C8.94784 31.8621 9.23859 32.0127 9.53936 32.0027H13.0083C13.279 31.9625 13.4795 31.6913 13.4394 31.4C13.4394 31.3397 13.4193 31.2794 13.3892 31.2191L13.1787 30.4054C13.0484 30.0036 12.9481 29.5917 12.8579 29.1698C13.7301 29.3406 14.853 29.4913 15.6149 29.5616C16.6075 29.642 17.6101 29.642 18.6126 29.5616C18.9836 30.2949 19.3746 30.9479 19.7155 31.4904C19.8859 31.7616 20.1566 31.9223 20.4574 31.9223H23.445C23.7859 31.9223 24.0666 31.6109 24.0566 31.2392C24.0566 31.1689 24.0466 31.1086 24.0265 31.0383C23.806 30.2648 23.5152 29.21 23.3448 28.5671C24.618 28.1251 25.8211 27.5123 26.944 26.7287C27.0944 26.6182 27.2548 26.4575 27.4052 26.3169C26.6031 26.0456 25.8211 25.7041 25.0792 25.2721C24.6982 25.0612 24.5579 24.5589 24.7484 24.147C24.9489 23.7351 25.4101 23.5744 25.781 23.7954C26.7435 24.3379 27.7761 24.7196 28.8389 24.9406C29.691 25.0812 30.5533 25.1214 31.4155 25.0612C31.8065 25.0411 32.1473 24.7698 32.2777 24.368L33.9419 19.737C34.1224 19.1945 33.8718 18.5918 33.3805 18.3908V18.431ZM18.6327 10.3443C18.6327 10.3443 18.5625 10.3342 18.5324 10.3242C16.6676 9.9324 14.7527 10.1032 12.9682 10.8164C12.8779 10.8566 12.7777 10.8666 12.6774 10.8666C12.2263 10.8666 11.8653 10.4548 11.8754 9.96253C11.8754 9.60089 12.0859 9.27943 12.3867 9.14883C14.4419 8.31505 16.6576 8.12418 18.8131 8.57623C19.2643 8.63651 19.5851 9.06847 19.525 9.56071C19.4648 10.0529 19.0638 10.3945 18.6227 10.3342L18.6327 10.3443ZM24.8587 17.0648C24.1067 17.0648 23.4952 16.452 23.4952 15.6986C23.4952 14.9452 24.1067 14.3324 24.8587 14.3324C25.6106 14.3324 26.2221 14.9452 26.2221 15.6986C26.2221 16.452 25.6106 17.0648 24.8587 17.0648Z\"/>'})];export{p as piggyBankIcon,L as piggyBankIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,YAAY;EAACC,CAAC,GAAC,CAAC,YAAY,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,ktHAAktH;IAACC,KAAK,EAAC;EAAulF,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,aAAa,EAACJ,CAAC,IAAIK,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}