{"ast": null, "code": "import { renderIcon as t } from \"../icon.renderer.js\";\nconst e = \"align-left-text\",\n  a = [\"align-left-text\", t({\n    outline: '<path d=\"M20.25,26H6v2.2H20.25a1.1,1.1,0,0,0,0-2.2Z\"/><path d=\"M28,20H6v2.2H28A1.1,1.1,0,0,0,28,20Z\"/><path d=\"M22.6,15.1A1.1,1.1,0,0,0,21.5,14H6v2.2H21.5A1.1,1.1,0,0,0,22.6,15.1Z\"/><path d=\"M29.25,8H6v2.2H29.25a1.1,1.1,0,1,0,0-2.2Z\"/>'\n  })];\nexport { a as alignLeftTextIcon, e as alignLeftTextIconName };", "map": {"version": 3, "names": ["renderIcon", "t", "e", "a", "outline", "alignLeftTextIcon", "alignLeftTextIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/align-left-text.js"], "sourcesContent": ["import{renderIcon as t}from\"../icon.renderer.js\";const e=\"align-left-text\",a=[\"align-left-text\",t({outline:'<path d=\"M20.25,26H6v2.2H20.25a1.1,1.1,0,0,0,0-2.2Z\"/><path d=\"M28,20H6v2.2H28A1.1,1.1,0,0,0,28,20Z\"/><path d=\"M22.6,15.1A1.1,1.1,0,0,0,21.5,14H6v2.2H21.5A1.1,1.1,0,0,0,22.6,15.1Z\"/><path d=\"M29.25,8H6v2.2H29.25a1.1,1.1,0,1,0,0-2.2Z\"/>'})];export{a as alignLeftTextIcon,e as alignLeftTextIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,iBAAiB;EAACC,CAAC,GAAC,CAAC,iBAAiB,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAA6O,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,iBAAiB,EAACH,CAAC,IAAII,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}