{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { APP_INITIALIZER } from '@angular/core';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { ClarityModule } from '@clr/angular';\nimport { RootComponent } from './root.component';\nimport { HomeComponent } from './home.component';\nimport { ConfigComponent } from './config/config.component';\nimport { LeaderboardComponent } from './leaderboard/leaderboard.component';\nimport { ScansComponent } from './scans/scans.component';\nimport { EmbeddedLeaderboardComponent } from './leaderboard/embedded/leaderboard-embedded.component';\nimport { BashbrawlterminalComponent } from './terminals/bashbrawl/bashbrawlterminal.component';\nimport { HttpClientModule } from '@angular/common/http';\nimport { AtobPipe } from './pipes/atob.pipe';\nimport { UppercasePipe } from './pipes/uppercase.pipe';\nimport { SettingsService } from './services/settings.service';\nimport { AppConfigService } from './app-config.service';\nimport { AngularSplitModule } from 'angular-split';\nimport { APIClientFactory } from './services/api.service';\nimport { LanguageCommandService } from './terminals/bashbrawl/languages/language-command.service';\nimport { ScoreService } from './services/score.service';\nimport '@cds/core/icon/register.js';\nimport { ClarityIcons, terminalIcon, starIcon, playIcon } from '@cds/core/icon';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nClarityIcons.addIcons(terminalIcon, starIcon, playIcon);\nconst appInitializerFn = appConfig => {\n  return () => {\n    return appConfig.loadAppConfig();\n  };\n};\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [RootComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [AppComponent, SettingsService, AppConfigService, APIClientFactory, LanguageCommandService, ScoreService, {\n        provide: APP_INITIALIZER,\n        useFactory: appInitializerFn,\n        multi: true,\n        deps: [AppConfigService]\n      }],\n      imports: [BrowserModule, AppRoutingModule, ClarityModule, HttpClientModule, AngularSplitModule, BrowserAnimationsModule, FormsModule, ReactiveFormsModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, RootComponent, HomeComponent, ConfigComponent, LeaderboardComponent, ScansComponent, EmbeddedLeaderboardComponent, BashbrawlterminalComponent, AtobPipe, UppercasePipe],\n    imports: [BrowserModule, AppRoutingModule, ClarityModule, HttpClientModule, AngularSplitModule, BrowserAnimationsModule, FormsModule, ReactiveFormsModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "BrowserAnimationsModule", "APP_INITIALIZER", "AppRoutingModule", "AppComponent", "ClarityModule", "RootComponent", "HomeComponent", "ConfigComponent", "LeaderboardComponent", "ScansComponent", "EmbeddedLeaderboardComponent", "BashbrawlterminalComponent", "HttpClientModule", "AtobPipe", "UppercasePipe", "SettingsService", "AppConfigService", "AngularSplitModule", "APIClientFactory", "LanguageCommandService", "ScoreService", "ClarityIcons", "terminalIcon", "starIcon", "playIcon", "FormsModule", "ReactiveFormsModule", "addIcons", "appInitializerFn", "appConfig", "loadAppConfig", "AppModule", "bootstrap", "provide", "useFactory", "multi", "deps", "imports", "declarations"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/src/app/app.module.ts"], "sourcesContent": ["import { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { NgModule, APP_INITIALIZER } from '@angular/core';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { ClarityModule } from '@clr/angular';\nimport { RootComponent } from './root.component';\nimport { HomeComponent } from './home.component';\nimport { ConfigComponent } from './config/config.component';\nimport { LeaderboardComponent } from './leaderboard/leaderboard.component';\nimport { ScansComponent } from './scans/scans.component';\nimport { EmbeddedLeaderboardComponent } from './leaderboard/embedded/leaderboard-embedded.component';\nimport { BashbrawlterminalComponent } from './terminals/bashbrawl/bashbrawlterminal.component';\nimport { HttpClientModule } from '@angular/common/http';\nimport { AtobPipe } from './pipes/atob.pipe';\nimport { UppercasePipe } from './pipes/uppercase.pipe';\nimport { SettingsService } from './services/settings.service';\nimport { AppConfigService } from './app-config.service';\nimport { AngularSplitModule } from 'angular-split';\nimport { APIClientFactory } from './services/api.service';\nimport { LanguageCommandService } from './terminals/bashbrawl/languages/language-command.service';\nimport { ScoreService } from './services/score.service';\nimport '@cds/core/icon/register.js';\nimport { ClarityIcons, terminalIcon, starIcon, playIcon } from '@cds/core/icon';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\n\nClarityIcons.addIcons(terminalIcon, starIcon, playIcon);\n\nconst appInitializerFn = (appConfig: AppConfigService) => {\n  return () => {\n    return appConfig.loadAppConfig();\n  };\n};\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    RootComponent,\n    HomeComponent,\n    ConfigComponent,\n    LeaderboardComponent,\n    ScansComponent,\n    EmbeddedLeaderboardComponent,\n    BashbrawlterminalComponent,\n    AtobPipe,\n    UppercasePipe,\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    ClarityModule,\n    HttpClientModule,\n    AngularSplitModule,\n    BrowserAnimationsModule,\n    FormsModule,\n    ReactiveFormsModule,\n  ],\n  providers: [\n    AppComponent,\n    SettingsService,\n    AppConfigService,\n    APIClientFactory,\n    LanguageCommandService,\n    ScoreService,\n    {\n      provide: APP_INITIALIZER,\n      useFactory: appInitializerFn,\n      multi: true,\n      deps: [AppConfigService],\n    },\n  ],\n  bootstrap: [RootComponent],\n})\nexport class AppModule {}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAAmBC,eAAe,QAAQ,eAAe;AACzD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,cAAc;AAC5C,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,4BAA4B,QAAQ,uDAAuD;AACpG,SAASC,0BAA0B,QAAQ,mDAAmD;AAC9F,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,sBAAsB,QAAQ,0DAA0D;AACjG,SAASC,YAAY,QAAQ,0BAA0B;AACvD,OAAO,4BAA4B;AACnC,SAASC,YAAY,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AAC/E,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;;AAEjEL,YAAY,CAACM,QAAQ,CAACL,YAAY,EAAEC,QAAQ,EAAEC,QAAQ,CAAC;AAEvD,MAAMI,gBAAgB,GAAIC,SAA2B,IAAI;EACvD,OAAO,MAAK;IACV,OAAOA,SAAS,CAACC,aAAa,EAAE;EAClC,CAAC;AACH,CAAC;AAyCD,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFR3B,aAAa;IAAA;EAAA;;;iBAdd,CACTF,YAAY,EACZY,eAAe,EACfC,gBAAgB,EAChBE,gBAAgB,EAChBC,sBAAsB,EACtBC,YAAY,EACZ;QACEa,OAAO,EAAEhC,eAAe;QACxBiC,UAAU,EAAEN,gBAAgB;QAC5BO,KAAK,EAAE,IAAI;QACXC,IAAI,EAAE,CAACpB,gBAAgB;OACxB,CACF;MAAAqB,OAAA,GAtBCtC,aAAa,EACbG,gBAAgB,EAChBE,aAAa,EACbQ,gBAAgB,EAChBK,kBAAkB,EAClBjB,uBAAuB,EACvByB,WAAW,EACXC,mBAAmB;IAAA;EAAA;;;2EAkBVK,SAAS;IAAAO,YAAA,GArClBnC,YAAY,EACZE,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,oBAAoB,EACpBC,cAAc,EACdC,4BAA4B,EAC5BC,0BAA0B,EAC1BE,QAAQ,EACRC,aAAa;IAAAuB,OAAA,GAGbtC,aAAa,EACbG,gBAAgB,EAChBE,aAAa,EACbQ,gBAAgB,EAChBK,kBAAkB,EAClBjB,uBAAuB,EACvByB,WAAW,EACXC,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}