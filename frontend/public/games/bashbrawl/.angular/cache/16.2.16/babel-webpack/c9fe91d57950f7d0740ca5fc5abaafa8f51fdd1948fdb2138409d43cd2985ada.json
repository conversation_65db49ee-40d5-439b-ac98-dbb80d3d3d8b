{"ast": null, "code": "import { renderIcon as c } from \"../icon.renderer.js\";\nconst n = \"unknown\",\n  r = [\"unknown\", c({\n    outline: '<circle class=\"cds-internal-dot-3\" cx=\"31.1\" cy=\"18\" r=\"2.9\"/><circle class=\"cds-internal-dot-2\" cx=\"18\" cy=\"18\" r=\"2.9\"/><circle class=\"cds-internal-dot-1\" cx=\"4.9\" cy=\"18\" r=\"2.9\"/>'\n  })];\nexport { r as unknownIcon, n as unknownIconName };", "map": {"version": 3, "names": ["renderIcon", "c", "n", "r", "outline", "unknownIcon", "unknownIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/unknown.js"], "sourcesContent": ["import{renderIcon as c}from\"../icon.renderer.js\";const n=\"unknown\",r=[\"unknown\",c({outline:'<circle class=\"cds-internal-dot-3\" cx=\"31.1\" cy=\"18\" r=\"2.9\"/><circle class=\"cds-internal-dot-2\" cx=\"18\" cy=\"18\" r=\"2.9\"/><circle class=\"cds-internal-dot-1\" cx=\"4.9\" cy=\"18\" r=\"2.9\"/>'})];export{r as unknownIcon,n as unknownIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,SAAS;EAACC,CAAC,GAAC,CAAC,SAAS,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAyL,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,WAAW,EAACH,CAAC,IAAII,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}