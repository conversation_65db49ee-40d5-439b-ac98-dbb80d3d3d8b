{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst t = \"bullet-list\",\n  e = [\"bullet-list\", C({\n    outline: '<path d=\"M6 16C4.9 16 4 16.9 4 18C4 19.1 4.9 20 6 20C7.1 20 8 19.1 8 18C8 16.9 7.1 16 6 16ZM6 8C4.9 8 4 8.9 4 10C4 11.1 4.9 12 6 12C7.1 12 8 11.1 8 10C8 8.9 7.1 8 6 8ZM6 24C4.9 24 4 24.9 4 26C4 27.1 4.9 28 6 28C7.1 28 8 27.1 8 26C8 24.9 7.1 24 6 24ZM31 25H10.02V27H31C31.55 27 32 26.55 32 26C32 25.45 31.55 25 31 25ZM31 17H10.02V19H31C31.55 19 32 18.55 32 18C32 17.45 31.55 17 31 17ZM31.98 10C31.98 9.45 31.53 9 30.98 9H10V11H30.98C31.53 11 31.98 10.55 31.98 10Z\"/>'\n  })];\nexport { e as bulletListIcon, t as bulletListIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "t", "e", "outline", "bulletListIcon", "bulletListIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/bullet-list.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const t=\"bullet-list\",e=[\"bullet-list\",C({outline:'<path d=\"M6 16C4.9 16 4 16.9 4 18C4 19.1 4.9 20 6 20C7.1 20 8 19.1 8 18C8 16.9 7.1 16 6 16ZM6 8C4.9 8 4 8.9 4 10C4 11.1 4.9 12 6 12C7.1 12 8 11.1 8 10C8 8.9 7.1 8 6 8ZM6 24C4.9 24 4 24.9 4 26C4 27.1 4.9 28 6 28C7.1 28 8 27.1 8 26C8 24.9 7.1 24 6 24ZM31 25H10.02V27H31C31.55 27 32 26.55 32 26C32 25.45 31.55 25 31 25ZM31 17H10.02V19H31C31.55 19 32 18.55 32 18C32 17.45 31.55 17 31 17ZM31.98 10C31.98 9.45 31.53 9 30.98 9H10V11H30.98C31.53 11 31.98 10.55 31.98 10Z\"/>'})];export{e as bulletListIcon,t as bulletListIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,aAAa;EAACC,CAAC,GAAC,CAAC,aAAa,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAmd,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,cAAc,EAACH,CAAC,IAAII,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}