{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"display\",\n  V = [\"display\", C({\n    outline: '<path d=\"M24.9928 30H10.9972C10.4474 30 9.9975 30.45 9.9975 31C9.9975 31.55 10.4474 32 10.9972 32H24.9928C25.5426 32 25.9925 31.55 25.9925 31C25.9925 30.45 25.5426 30 24.9928 30ZM7.59825 9.6H28.1218L29.9313 8H5.99875V24H7.59825V9.6ZM32.4905 4H3.49953C2.66979 4 2 4.67 2 5.5V26.5C2 27.33 2.66979 28 3.49953 28H11.627H13.8063H22.1937H24.373H32.5005C33.3302 28 34 27.33 34 26.5V5.5C34 4.67 33.3302 4 32.5005 4H32.4905ZM31.9906 26H3.99938V6H31.9906V26Z\"/>',\n    outlineAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M23.1594 4H3.49953C2.66979 4 2 4.67 2 5.5V26.5C2 27.33 2.66979 28 3.49953 28H32.5005C33.3302 28 34 27.33 34 26.5V15.0263C33.8886 15.0354 33.7763 15.0389 33.6637 15.0367H31.9906V26H3.99938V6H21.9594L23.1594 4Z\"/><path d=\"M20.7594 8H5.99875V24H7.59825V9.6H19.7994L20.7594 8Z\"/><path d=\"M10.9972 30H24.9928C25.5426 30 25.9925 30.45 25.9925 31C25.9925 31.55 25.5426 32 24.9928 32H10.9972C10.4474 32 9.9975 31.55 9.9975 31C9.9975 30.45 10.4474 30 10.9972 30Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M23.2899 8C23.4585 8.56674 23.6971 9.1034 23.9954 9.6H7.59825V24H5.99875V8H23.2899Z\"/><path d=\"M31.9906 12.7129V26H3.99938V6H23C23 5.30503 23.1013 4.63371 23.2899 4H3.49953C2.66979 4 2 4.67 2 5.5V26.5C2 27.33 2.66979 28 3.49953 28H32.5005C33.3302 28 34 27.33 34 26.5V11.7453C33.3934 12.1684 32.7166 12.498 31.9906 12.7129Z\"/><path d=\"M10.9972 30H24.9928C25.5426 30 25.9925 30.45 25.9925 31C25.9925 31.55 25.5426 32 24.9928 32H10.9972C10.4474 32 9.9975 31.55 9.9975 31C9.9975 30.45 10.4474 30 10.9972 30Z\"/>',\n    solid: '<path d=\"M24.9928 30H10.9972C10.4474 30 9.9975 30.45 9.9975 31C9.9975 31.55 10.4474 32 10.9972 32H24.9928C25.5426 32 25.9925 31.55 25.9925 31C25.9925 30.45 25.5426 30 24.9928 30ZM32.4905 4H3.49953C2.66979 4 2 4.67 2 5.5V26.5C2 27.33 2.66979 28 3.49953 28H11.627H13.8063H22.1937H24.373H32.5005C33.3302 28 34 27.33 34 26.5V5.5C34 4.67 33.3302 4 32.5005 4H32.4905ZM29.9913 24H5.99875V8H29.9913V24Z\"/>',\n    solidAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M23.1528 4H3.49953C2.66979 4 2 4.67 2 5.5V26.5C2 27.33 2.66979 28 3.49953 28H32.5005C33.3302 28 34 27.33 34 26.5V15.0255C33.8854 15.0352 33.7698 15.039 33.6538 15.0367H29.9913V24H5.99875V8H20.7535L23.1528 4Z\"/><path d=\"M10.9972 30H24.9928C25.5426 30 25.9925 30.45 25.9925 31C25.9925 31.55 25.5426 32 24.9928 32H10.9972C10.4474 32 9.9975 31.55 9.9975 31C9.9975 30.45 10.4474 30 10.9972 30Z\"/>',\n    solidBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M34 11.7383C32.8646 12.5335 31.4824 13 29.9913 13V24H5.99875V8H23.2832C23.0947 7.36629 22.9934 6.69497 22.9934 6C22.9934 5.30503 23.0947 4.63371 23.2832 4H3.49953C2.66979 4 2 4.67 2 5.5V26.5C2 27.33 2.66979 28 3.49953 28H32.5005C33.3302 28 34 27.33 34 26.5V11.7383Z\"/><path d=\"M10.9972 30H24.9928C25.5426 30 25.9925 30.45 25.9925 31C25.9925 31.55 25.5426 32 24.9928 32H10.9972C10.4474 32 9.9975 31.55 9.9975 31C9.9975 30.45 10.4474 30 10.9972 30Z\"/>'\n  })];\nexport { V as displayIcon, H as displayIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "V", "outline", "outlineAlerted", "outlineBadged", "solid", "solidAlerted", "solidBadged", "displayIcon", "displayIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/display.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"display\",V=[\"display\",C({outline:'<path d=\"M24.9928 30H10.9972C10.4474 30 9.9975 30.45 9.9975 31C9.9975 31.55 10.4474 32 10.9972 32H24.9928C25.5426 32 25.9925 31.55 25.9925 31C25.9925 30.45 25.5426 30 24.9928 30ZM7.59825 9.6H28.1218L29.9313 8H5.99875V24H7.59825V9.6ZM32.4905 4H3.49953C2.66979 4 2 4.67 2 5.5V26.5C2 27.33 2.66979 28 3.49953 28H11.627H13.8063H22.1937H24.373H32.5005C33.3302 28 34 27.33 34 26.5V5.5C34 4.67 33.3302 4 32.5005 4H32.4905ZM31.9906 26H3.99938V6H31.9906V26Z\"/>',outlineAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M23.1594 4H3.49953C2.66979 4 2 4.67 2 5.5V26.5C2 27.33 2.66979 28 3.49953 28H32.5005C33.3302 28 34 27.33 34 26.5V15.0263C33.8886 15.0354 33.7763 15.0389 33.6637 15.0367H31.9906V26H3.99938V6H21.9594L23.1594 4Z\"/><path d=\"M20.7594 8H5.99875V24H7.59825V9.6H19.7994L20.7594 8Z\"/><path d=\"M10.9972 30H24.9928C25.5426 30 25.9925 30.45 25.9925 31C25.9925 31.55 25.5426 32 24.9928 32H10.9972C10.4474 32 9.9975 31.55 9.9975 31C9.9975 30.45 10.4474 30 10.9972 30Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M23.2899 8C23.4585 8.56674 23.6971 9.1034 23.9954 9.6H7.59825V24H5.99875V8H23.2899Z\"/><path d=\"M31.9906 12.7129V26H3.99938V6H23C23 5.30503 23.1013 4.63371 23.2899 4H3.49953C2.66979 4 2 4.67 2 5.5V26.5C2 27.33 2.66979 28 3.49953 28H32.5005C33.3302 28 34 27.33 34 26.5V11.7453C33.3934 12.1684 32.7166 12.498 31.9906 12.7129Z\"/><path d=\"M10.9972 30H24.9928C25.5426 30 25.9925 30.45 25.9925 31C25.9925 31.55 25.5426 32 24.9928 32H10.9972C10.4474 32 9.9975 31.55 9.9975 31C9.9975 30.45 10.4474 30 10.9972 30Z\"/>',solid:'<path d=\"M24.9928 30H10.9972C10.4474 30 9.9975 30.45 9.9975 31C9.9975 31.55 10.4474 32 10.9972 32H24.9928C25.5426 32 25.9925 31.55 25.9925 31C25.9925 30.45 25.5426 30 24.9928 30ZM32.4905 4H3.49953C2.66979 4 2 4.67 2 5.5V26.5C2 27.33 2.66979 28 3.49953 28H11.627H13.8063H22.1937H24.373H32.5005C33.3302 28 34 27.33 34 26.5V5.5C34 4.67 33.3302 4 32.5005 4H32.4905ZM29.9913 24H5.99875V8H29.9913V24Z\"/>',solidAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M23.1528 4H3.49953C2.66979 4 2 4.67 2 5.5V26.5C2 27.33 2.66979 28 3.49953 28H32.5005C33.3302 28 34 27.33 34 26.5V15.0255C33.8854 15.0352 33.7698 15.039 33.6538 15.0367H29.9913V24H5.99875V8H20.7535L23.1528 4Z\"/><path d=\"M10.9972 30H24.9928C25.5426 30 25.9925 30.45 25.9925 31C25.9925 31.55 25.5426 32 24.9928 32H10.9972C10.4474 32 9.9975 31.55 9.9975 31C9.9975 30.45 10.4474 30 10.9972 30Z\"/>',solidBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M34 11.7383C32.8646 12.5335 31.4824 13 29.9913 13V24H5.99875V8H23.2832C23.0947 7.36629 22.9934 6.69497 22.9934 6C22.9934 5.30503 23.0947 4.63371 23.2832 4H3.49953C2.66979 4 2 4.67 2 5.5V26.5C2 27.33 2.66979 28 3.49953 28H32.5005C33.3302 28 34 27.33 34 26.5V11.7383Z\"/><path d=\"M10.9972 30H24.9928C25.5426 30 25.9925 30.45 25.9925 31C25.9925 31.55 25.5426 32 24.9928 32H10.9972C10.4474 32 9.9975 31.55 9.9975 31C9.9975 30.45 10.4474 30 10.9972 30Z\"/>'})];export{V as displayIcon,H as displayIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,SAAS;EAACC,CAAC,GAAC,CAAC,SAAS,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,qcAAqc;IAACC,cAAc,EAAC,szBAAszB;IAACC,aAAa,EAAC,moBAAmoB;IAACC,KAAK,EAAC,+YAA+Y;IAACC,YAAY,EAAC,qvBAAqvB;IAACC,WAAW,EAAC;EAA0kB,CAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,WAAW,EAACR,CAAC,IAAIS,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}