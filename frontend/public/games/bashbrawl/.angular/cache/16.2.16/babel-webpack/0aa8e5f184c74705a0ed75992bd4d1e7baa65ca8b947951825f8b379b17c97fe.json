{"ast": null, "code": "export * from \"@lit/reactive-element/decorators/property.js\";", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/lit/decorators/property.js"], "sourcesContent": ["export*from\"@lit/reactive-element/decorators/property.js\";\n"], "mappings": "AAAA,cAAW,8CAA8C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}