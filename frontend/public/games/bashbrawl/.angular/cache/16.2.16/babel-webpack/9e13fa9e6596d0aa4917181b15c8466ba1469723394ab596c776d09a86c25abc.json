{"ast": null, "code": "import { ClarityIcons as s } from \"../icon.service.js\";\nimport { angleDoubleIcon as r, angleDoubleIconName as o } from \"../shapes/angle-double.js\";\nimport { angleIcon as e, angleIconName as m } from \"../shapes/angle.js\";\nimport { arrowIcon as p } from \"../shapes/arrow.js\";\nimport { barsIcon as a, barsIconName as i } from \"../shapes/bars.js\";\nimport { bellIcon as t, bellIconName as f } from \"../shapes/bell.js\";\nimport { calendarIcon as h } from \"../shapes/calendar.js\";\nimport { checkCircleIcon as j } from \"../shapes/check-circle.js\";\nimport { checkIcon as n, checkIconName as c } from \"../shapes/check.js\";\nimport { cloudIcon as l } from \"../shapes/cloud.js\";\nimport { cogIcon as d, cogIconName as u } from \"../shapes/cog.js\";\nimport { ellipsisHorizontalIcon as g } from \"../shapes/ellipsis-horizontal.js\";\nimport { ellipsisVerticalIcon as v } from \"../shapes/ellipsis-vertical.js\";\nimport { errorStandardIcon as w } from \"../shapes/error-standard.js\";\nimport { eventIcon as b } from \"../shapes/event.js\";\nimport { exclamationCircleIcon as k, exclamationCircleIconName as x } from \"../shapes/exclamation-circle.js\";\nimport { exclamationTriangleIcon as y, exclamationTriangleIconName as z } from \"../shapes/exclamation-triangle.js\";\nimport { eyeHideIcon as A } from \"../shapes/eye-hide.js\";\nimport { eyeIcon as I } from \"../shapes/eye.js\";\nimport { filterGridCircleIcon as q } from \"../shapes/filter-grid-circle.js\";\nimport { filterGridIcon as B } from \"../shapes/filter-grid.js\";\nimport { folderOpenIcon as C } from \"../shapes/folder-open.js\";\nimport { folderIcon as D, folderIconName as E } from \"../shapes/folder.js\";\nimport { helpInfoIcon as F } from \"../shapes/help-info.js\";\nimport { homeIcon as G, homeIconName as H } from \"../shapes/home.js\";\nimport { imageIcon as J } from \"../shapes/image.js\";\nimport { infoCircleIcon as K, infoCircleIconName as L } from \"../shapes/info-circle.js\";\nimport { infoStandardIcon as M } from \"../shapes/info-standard.js\";\nimport { searchIcon as N } from \"../shapes/search.js\";\nimport { stepForward2Icon as O } from \"../shapes/step-forward-2.js\";\nimport { successStandardIcon as P } from \"../shapes/success-standard.js\";\nimport { timesIcon as Q, timesIconName as R } from \"../shapes/times.js\";\nimport { unknownStatusIcon as S } from \"../shapes/unknown-status.js\";\nimport \"../shapes/unknown.js\";\nimport { userIcon as T, userIconName as U } from \"../shapes/user.js\";\nimport { viewColumnsIcon as V } from \"../shapes/view-columns.js\";\nimport { vmBugInverseIcon as W } from \"../shapes/vm-bug-inverse.js\";\nimport { vmBugIcon as X } from \"../shapes/vm-bug.js\";\nimport { warningStandardIcon as Y } from \"../shapes/warning-standard.js\";\nimport { detailExpandIcon as Z } from \"../shapes/detail-expand.js\";\nimport { detailCollapseIcon as $ } from \"../shapes/detail-collapse.js\";\nconst _ = [e, r, p, a, t, h, n, j, l, d, g, v, w, b, k, y, I, A, B, q, D, C, F, G, J, K, M, N, O, P, Q, S, T, V, X, W, Y, Z, $],\n  ss = [[H, [\"house\"]], [u, [\"settings\"]], [c, [\"success\"]], [R, [\"close\"]], [z, [\"warning\"]], [x, [\"error\"]], [L, [\"info\"]], [i, [\"menu\"]], [U, [\"avatar\"]], [m, [\"caret\"]], [E, [\"directory\"]], [f, [\"notification\"]], [o, [\"collapse\"]]];\nfunction rs() {\n  s.addIcons(..._), s.addAliases(...ss);\n}\nexport { ss as coreCollectionAliases, _ as coreCollectionIcons, rs as loadCoreIconSet };", "map": {"version": 3, "names": ["ClarityIcons", "s", "angleDoubleIcon", "r", "angleDoubleIconName", "o", "angleIcon", "e", "angleIconName", "m", "arrowIcon", "p", "barsIcon", "a", "barsIconName", "i", "bellIcon", "t", "bellIconName", "f", "calendarIcon", "h", "checkCircleIcon", "j", "checkIcon", "n", "checkIconName", "c", "cloudIcon", "l", "cogIcon", "d", "cogIconName", "u", "ellipsisHorizontalIcon", "g", "ellipsisVerticalIcon", "v", "errorStandardIcon", "w", "eventIcon", "b", "exclamationCircleIcon", "k", "exclamationCircleIconName", "x", "exclamationTriangleIcon", "y", "exclamationTriangleIconName", "z", "eyeHideIcon", "A", "eyeIcon", "I", "filterGridCircleIcon", "q", "filterGridIcon", "B", "folderOpenIcon", "C", "folderIcon", "D", "folderIconName", "E", "helpInfoIcon", "F", "homeIcon", "G", "homeIconName", "H", "imageIcon", "J", "infoCircleIcon", "K", "infoCircleIconName", "L", "infoStandardIcon", "M", "searchIcon", "N", "stepForward2Icon", "O", "successStandardIcon", "P", "timesIcon", "Q", "timesIconName", "R", "unknownStatusIcon", "S", "userIcon", "T", "userIconName", "U", "viewColumnsIcon", "V", "vmBugInverseIcon", "W", "vmBugIcon", "X", "warningStandardIcon", "Y", "detailExpandIcon", "Z", "detailCollapseIcon", "$", "_", "ss", "rs", "addIcons", "addAliases", "coreCollectionAliases", "coreCollectionIcons", "loadCoreIconSet"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/collections/core.js"], "sourcesContent": ["import{ClarityIcons as s}from\"../icon.service.js\";import{angleDoubleIcon as r,angleDoubleIconName as o}from\"../shapes/angle-double.js\";import{angleIcon as e,angleIconName as m}from\"../shapes/angle.js\";import{arrowIcon as p}from\"../shapes/arrow.js\";import{barsIcon as a,barsIconName as i}from\"../shapes/bars.js\";import{bellIcon as t,bellIconName as f}from\"../shapes/bell.js\";import{calendarIcon as h}from\"../shapes/calendar.js\";import{checkCircleIcon as j}from\"../shapes/check-circle.js\";import{checkIcon as n,checkIconName as c}from\"../shapes/check.js\";import{cloudIcon as l}from\"../shapes/cloud.js\";import{cogIcon as d,cogIconName as u}from\"../shapes/cog.js\";import{ellipsisHorizontalIcon as g}from\"../shapes/ellipsis-horizontal.js\";import{ellipsisVerticalIcon as v}from\"../shapes/ellipsis-vertical.js\";import{errorStandardIcon as w}from\"../shapes/error-standard.js\";import{eventIcon as b}from\"../shapes/event.js\";import{exclamationCircleIcon as k,exclamationCircleIconName as x}from\"../shapes/exclamation-circle.js\";import{exclamationTriangleIcon as y,exclamationTriangleIconName as z}from\"../shapes/exclamation-triangle.js\";import{eyeHideIcon as A}from\"../shapes/eye-hide.js\";import{eyeIcon as I}from\"../shapes/eye.js\";import{filterGridCircleIcon as q}from\"../shapes/filter-grid-circle.js\";import{filterGridIcon as B}from\"../shapes/filter-grid.js\";import{folderOpenIcon as C}from\"../shapes/folder-open.js\";import{folderIcon as D,folderIconName as E}from\"../shapes/folder.js\";import{helpInfoIcon as F}from\"../shapes/help-info.js\";import{homeIcon as G,homeIconName as H}from\"../shapes/home.js\";import{imageIcon as J}from\"../shapes/image.js\";import{infoCircleIcon as K,infoCircleIconName as L}from\"../shapes/info-circle.js\";import{infoStandardIcon as M}from\"../shapes/info-standard.js\";import{searchIcon as N}from\"../shapes/search.js\";import{stepForward2Icon as O}from\"../shapes/step-forward-2.js\";import{successStandardIcon as P}from\"../shapes/success-standard.js\";import{timesIcon as Q,timesIconName as R}from\"../shapes/times.js\";import{unknownStatusIcon as S}from\"../shapes/unknown-status.js\";import\"../shapes/unknown.js\";import{userIcon as T,userIconName as U}from\"../shapes/user.js\";import{viewColumnsIcon as V}from\"../shapes/view-columns.js\";import{vmBugInverseIcon as W}from\"../shapes/vm-bug-inverse.js\";import{vmBugIcon as X}from\"../shapes/vm-bug.js\";import{warningStandardIcon as Y}from\"../shapes/warning-standard.js\";import{detailExpandIcon as Z}from\"../shapes/detail-expand.js\";import{detailCollapseIcon as $}from\"../shapes/detail-collapse.js\";const _=[e,r,p,a,t,h,n,j,l,d,g,v,w,b,k,y,I,A,B,q,D,C,F,G,J,K,M,N,O,P,Q,S,T,V,X,W,Y,Z,$],ss=[[H,[\"house\"]],[u,[\"settings\"]],[c,[\"success\"]],[R,[\"close\"]],[z,[\"warning\"]],[x,[\"error\"]],[L,[\"info\"]],[i,[\"menu\"]],[U,[\"avatar\"]],[m,[\"caret\"]],[E,[\"directory\"]],[f,[\"notification\"]],[o,[\"collapse\"]]];function rs(){s.addIcons(..._),s.addAliases(...ss)}export{ss as coreCollectionAliases,_ as coreCollectionIcons,rs as loadCoreIconSet};\n"], "mappings": "AAAA,SAAOA,YAAY,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,eAAe,IAAIC,CAAC,EAACC,mBAAmB,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,SAAS,IAAIC,CAAC,EAACC,aAAa,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,EAACC,YAAY,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,EAACC,YAAY,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,SAAS,IAAIC,CAAC,EAACC,aAAa,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,OAAO,IAAIC,CAAC,EAACC,WAAW,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAAOC,sBAAsB,IAAIC,CAAC,QAAK,kCAAkC;AAAC,SAAOC,oBAAoB,IAAIC,CAAC,QAAK,gCAAgC;AAAC,SAAOC,iBAAiB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,qBAAqB,IAAIC,CAAC,EAACC,yBAAyB,IAAIC,CAAC,QAAK,iCAAiC;AAAC,SAAOC,uBAAuB,IAAIC,CAAC,EAACC,2BAA2B,IAAIC,CAAC,QAAK,mCAAmC;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,OAAO,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAAOC,oBAAoB,IAAIC,CAAC,QAAK,iCAAiC;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,UAAU,IAAIC,CAAC,EAACC,cAAc,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,wBAAwB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,EAACC,YAAY,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,cAAc,IAAIC,CAAC,EAACC,kBAAkB,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,SAAS,IAAIC,CAAC,EAACC,aAAa,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,iBAAiB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,OAAM,sBAAsB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,EAACC,YAAY,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,kBAAkB,IAAIC,CAAC,QAAK,8BAA8B;AAAC,MAAMC,CAAC,GAAC,CAACnG,CAAC,EAACJ,CAAC,EAACQ,CAAC,EAACE,CAAC,EAACI,CAAC,EAACI,CAAC,EAACI,CAAC,EAACF,CAAC,EAACM,CAAC,EAACE,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACM,CAAC,EAACF,CAAC,EAACM,CAAC,EAACF,CAAC,EAACM,CAAC,EAACF,CAAC,EAACM,CAAC,EAACE,CAAC,EAACI,CAAC,EAACE,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACE,CAAC,EAACI,CAAC,EAACI,CAAC,EAACF,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,CAAC;EAACE,EAAE,GAAC,CAAC,CAACtC,CAAC,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAACpC,CAAC,EAAC,CAAC,UAAU,CAAC,CAAC,EAAC,CAACN,CAAC,EAAC,CAAC,SAAS,CAAC,CAAC,EAAC,CAAC4D,CAAC,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAACtC,CAAC,EAAC,CAAC,SAAS,CAAC,CAAC,EAAC,CAACJ,CAAC,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAAC8B,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC5D,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,EAAC,CAAC8E,CAAC,EAAC,CAAC,QAAQ,CAAC,CAAC,EAAC,CAACpF,CAAC,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAACsD,CAAC,EAAC,CAAC,WAAW,CAAC,CAAC,EAAC,CAAC5C,CAAC,EAAC,CAAC,cAAc,CAAC,CAAC,EAAC,CAACd,CAAC,EAAC,CAAC,UAAU,CAAC,CAAC,CAAC;AAAC,SAASuG,EAAEA,CAAA,EAAE;EAAC3G,CAAC,CAAC4G,QAAQ,CAAC,GAAGH,CAAC,CAAC,EAACzG,CAAC,CAAC6G,UAAU,CAAC,GAAGH,EAAE,CAAC;AAAA;AAAC,SAAOA,EAAE,IAAII,qBAAqB,EAACL,CAAC,IAAIM,mBAAmB,EAACJ,EAAE,IAAIK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}