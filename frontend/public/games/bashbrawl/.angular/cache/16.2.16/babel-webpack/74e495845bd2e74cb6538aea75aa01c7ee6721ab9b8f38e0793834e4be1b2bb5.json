{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"object\" == typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define([], t) : \"object\" == typeof exports ? exports.FitAddon = t() : e.Fit<PERSON>ddon = t();\n}(self, function () {\n  return (() => {\n    \"use strict\";\n\n    var e = {};\n    return (() => {\n      var t = e;\n      Object.defineProperty(t, \"__esModule\", {\n        value: !0\n      }), t.<PERSON>t<PERSON>ddon = void 0, t.FitAddon = class {\n        constructor() {}\n        activate(e) {\n          this._terminal = e;\n        }\n        dispose() {}\n        fit() {\n          const e = this.proposeDimensions();\n          if (!e || !this._terminal || isNaN(e.cols) || isNaN(e.rows)) return;\n          const t = this._terminal._core;\n          this._terminal.rows === e.rows && this._terminal.cols === e.cols || (t._renderService.clear(), this._terminal.resize(e.cols, e.rows));\n        }\n        proposeDimensions() {\n          if (!this._terminal) return;\n          if (!this._terminal.element || !this._terminal.element.parentElement) return;\n          const e = this._terminal._core,\n            t = e._renderService.dimensions;\n          if (0 === t.css.cell.width || 0 === t.css.cell.height) return;\n          const r = 0 === this._terminal.options.scrollback ? 0 : e.viewport.scrollBarWidth,\n            i = window.getComputedStyle(this._terminal.element.parentElement),\n            o = parseInt(i.getPropertyValue(\"height\")),\n            s = Math.max(0, parseInt(i.getPropertyValue(\"width\"))),\n            n = window.getComputedStyle(this._terminal.element),\n            l = o - (parseInt(n.getPropertyValue(\"padding-top\")) + parseInt(n.getPropertyValue(\"padding-bottom\"))),\n            a = s - (parseInt(n.getPropertyValue(\"padding-right\")) + parseInt(n.getPropertyValue(\"padding-left\"))) - r;\n          return {\n            cols: Math.max(2, Math.floor(a / t.css.cell.width)),\n            rows: Math.max(1, Math.floor(l / t.css.cell.height))\n          };\n        }\n      };\n    })(), e;\n  })();\n});", "map": {"version": 3, "names": ["e", "t", "exports", "module", "define", "amd", "FitAddon", "self", "Object", "defineProperty", "value", "constructor", "activate", "_terminal", "dispose", "fit", "proposeDimensions", "isNaN", "cols", "rows", "_core", "_renderService", "clear", "resize", "element", "parentElement", "dimensions", "css", "cell", "width", "height", "r", "options", "scrollback", "viewport", "scrollBarWidth", "i", "window", "getComputedStyle", "o", "parseInt", "getPropertyValue", "s", "Math", "max", "n", "l", "a", "floor"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/xterm-addon-fit/lib/xterm-addon-fit.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define([],t):\"object\"==typeof exports?exports.FitAddon=t():e.Fit<PERSON>ddon=t()}(self,(function(){return(()=>{\"use strict\";var e={};return(()=>{var t=e;Object.defineProperty(t,\"__esModule\",{value:!0}),t.<PERSON>t<PERSON>ddon=void 0,t.FitAddon=class{constructor(){}activate(e){this._terminal=e}dispose(){}fit(){const e=this.proposeDimensions();if(!e||!this._terminal||isNaN(e.cols)||isNaN(e.rows))return;const t=this._terminal._core;this._terminal.rows===e.rows&&this._terminal.cols===e.cols||(t._renderService.clear(),this._terminal.resize(e.cols,e.rows))}proposeDimensions(){if(!this._terminal)return;if(!this._terminal.element||!this._terminal.element.parentElement)return;const e=this._terminal._core,t=e._renderService.dimensions;if(0===t.css.cell.width||0===t.css.cell.height)return;const r=0===this._terminal.options.scrollback?0:e.viewport.scrollBarWidth,i=window.getComputedStyle(this._terminal.element.parentElement),o=parseInt(i.getPropertyValue(\"height\")),s=Math.max(0,parseInt(i.getPropertyValue(\"width\"))),n=window.getComputedStyle(this._terminal.element),l=o-(parseInt(n.getPropertyValue(\"padding-top\"))+parseInt(n.getPropertyValue(\"padding-bottom\"))),a=s-(parseInt(n.getPropertyValue(\"padding-right\"))+parseInt(n.getPropertyValue(\"padding-left\")))-r;return{cols:Math.max(2,Math.floor(a/t.css.cell.width)),rows:Math.max(1,Math.floor(l/t.css.cell.height))}}}})(),e})()}));\n"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,QAAQ,IAAE,OAAOC,OAAO,IAAE,QAAQ,IAAE,OAAOC,MAAM,GAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAAC,CAAC,GAAC,UAAU,IAAE,OAAOG,MAAM,IAAEA,MAAM,CAACC,GAAG,GAACD,MAAM,CAAC,EAAE,EAACH,CAAC,CAAC,GAAC,QAAQ,IAAE,OAAOC,OAAO,GAACA,OAAO,CAACI,QAAQ,GAACL,CAAC,CAAC,CAAC,GAACD,CAAC,CAACM,QAAQ,GAACL,CAAC,CAAC,CAAC;AAAA,CAAC,CAACM,IAAI,EAAE,YAAU;EAAC,OAAM,CAAC,MAAI;IAAC,YAAY;;IAAC,IAAIP,CAAC,GAAC,CAAC,CAAC;IAAC,OAAM,CAAC,MAAI;MAAC,IAAIC,CAAC,GAACD,CAAC;MAACQ,MAAM,CAACC,cAAc,CAACR,CAAC,EAAC,YAAY,EAAC;QAACS,KAAK,EAAC,CAAC;MAAC,CAAC,CAAC,EAACT,CAAC,CAACK,QAAQ,GAAC,KAAK,CAAC,EAACL,CAAC,CAACK,QAAQ,GAAC,MAAK;QAACK,WAAWA,CAAA,EAAE,CAAC;QAACC,QAAQA,CAACZ,CAAC,EAAC;UAAC,IAAI,CAACa,SAAS,GAACb,CAAC;QAAA;QAACc,OAAOA,CAAA,EAAE,CAAC;QAACC,GAAGA,CAAA,EAAE;UAAC,MAAMf,CAAC,GAAC,IAAI,CAACgB,iBAAiB,CAAC,CAAC;UAAC,IAAG,CAAChB,CAAC,IAAE,CAAC,IAAI,CAACa,SAAS,IAAEI,KAAK,CAACjB,CAAC,CAACkB,IAAI,CAAC,IAAED,KAAK,CAACjB,CAAC,CAACmB,IAAI,CAAC,EAAC;UAAO,MAAMlB,CAAC,GAAC,IAAI,CAACY,SAAS,CAACO,KAAK;UAAC,IAAI,CAACP,SAAS,CAACM,IAAI,KAAGnB,CAAC,CAACmB,IAAI,IAAE,IAAI,CAACN,SAAS,CAACK,IAAI,KAAGlB,CAAC,CAACkB,IAAI,KAAGjB,CAAC,CAACoB,cAAc,CAACC,KAAK,CAAC,CAAC,EAAC,IAAI,CAACT,SAAS,CAACU,MAAM,CAACvB,CAAC,CAACkB,IAAI,EAAClB,CAAC,CAACmB,IAAI,CAAC,CAAC;QAAA;QAACH,iBAAiBA,CAAA,EAAE;UAAC,IAAG,CAAC,IAAI,CAACH,SAAS,EAAC;UAAO,IAAG,CAAC,IAAI,CAACA,SAAS,CAACW,OAAO,IAAE,CAAC,IAAI,CAACX,SAAS,CAACW,OAAO,CAACC,aAAa,EAAC;UAAO,MAAMzB,CAAC,GAAC,IAAI,CAACa,SAAS,CAACO,KAAK;YAACnB,CAAC,GAACD,CAAC,CAACqB,cAAc,CAACK,UAAU;UAAC,IAAG,CAAC,KAAGzB,CAAC,CAAC0B,GAAG,CAACC,IAAI,CAACC,KAAK,IAAE,CAAC,KAAG5B,CAAC,CAAC0B,GAAG,CAACC,IAAI,CAACE,MAAM,EAAC;UAAO,MAAMC,CAAC,GAAC,CAAC,KAAG,IAAI,CAAClB,SAAS,CAACmB,OAAO,CAACC,UAAU,GAAC,CAAC,GAACjC,CAAC,CAACkC,QAAQ,CAACC,cAAc;YAACC,CAAC,GAACC,MAAM,CAACC,gBAAgB,CAAC,IAAI,CAACzB,SAAS,CAACW,OAAO,CAACC,aAAa,CAAC;YAACc,CAAC,GAACC,QAAQ,CAACJ,CAAC,CAACK,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAACC,CAAC,GAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAACJ,QAAQ,CAACJ,CAAC,CAACK,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;YAACI,CAAC,GAACR,MAAM,CAACC,gBAAgB,CAAC,IAAI,CAACzB,SAAS,CAACW,OAAO,CAAC;YAACsB,CAAC,GAACP,CAAC,IAAEC,QAAQ,CAACK,CAAC,CAACJ,gBAAgB,CAAC,aAAa,CAAC,CAAC,GAACD,QAAQ,CAACK,CAAC,CAACJ,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAACM,CAAC,GAACL,CAAC,IAAEF,QAAQ,CAACK,CAAC,CAACJ,gBAAgB,CAAC,eAAe,CAAC,CAAC,GAACD,QAAQ,CAACK,CAAC,CAACJ,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,GAACV,CAAC;UAAC,OAAM;YAACb,IAAI,EAACyB,IAAI,CAACC,GAAG,CAAC,CAAC,EAACD,IAAI,CAACK,KAAK,CAACD,CAAC,GAAC9C,CAAC,CAAC0B,GAAG,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;YAACV,IAAI,EAACwB,IAAI,CAACC,GAAG,CAAC,CAAC,EAACD,IAAI,CAACK,KAAK,CAACF,CAAC,GAAC7C,CAAC,CAAC0B,GAAG,CAACC,IAAI,CAACE,MAAM,CAAC;UAAC,CAAC;QAAA;MAAC,CAAC;IAAA,CAAC,EAAE,CAAC,EAAC9B,CAAC;EAAA,CAAC,EAAE,CAAC;AAAA,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}