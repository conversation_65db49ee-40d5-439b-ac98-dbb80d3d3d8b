{"ast": null, "code": "import { notProductionEnvironment as e, LogService as t } from \"../services/log.service.js\";\nconst r = (e, t, r) => {\n    Object.defineProperty(t, r, e);\n  },\n  s = (e, t) => ({\n    kind: \"method\",\n    placement: \"prototype\",\n    key: t.key,\n    descriptor: e\n  });\nfunction i(i, o) {\n  return (n, u) => {\n    const a = n.firstUpdated;\n    n.firstUpdated = function () {\n      const r = this.querySelector(i),\n        s = o?.exemptOn && o?.exemptOn(this);\n      if (!r && e() && o?.required && !s) {\n        const e = o.requiredMessage || `The <${i}> element is required to use <${this.tagName.toLocaleLowerCase()}>`;\n        if (\"error\" === o.required) throw Error(e);\n        t.warn(e, this);\n      }\n      o?.assign && !1 === r?.hasAttribute(\"slot\") && r.setAttribute(\"slot\", o.assign), a && a.apply(this);\n    };\n    const c = {\n      get() {\n        return this.querySelector(i);\n      },\n      enumerable: !0,\n      configurable: !0\n    };\n    return void 0 !== u ? r(c, n, u) : s(c, n);\n  };\n}\nfunction o(e, t) {\n  return (i, o) => {\n    const n = i.firstUpdated;\n    i.firstUpdated = function (r) {\n      t?.assign && Array.from(this.querySelectorAll(e)).filter(e => !e.hasAttribute(\"slot\")).forEach(e => e.setAttribute(\"slot\", t.assign)), n && n.call(this, r);\n    };\n    const u = {\n      get() {\n        return this.querySelectorAll(e);\n      },\n      enumerable: !0,\n      configurable: !0\n    };\n    return void 0 !== o ? r(u, i, o) : s(u, i);\n  };\n}\nexport { i as querySlot, o as querySlotAll };", "map": {"version": 3, "names": ["notProductionEnvironment", "e", "LogService", "t", "r", "Object", "defineProperty", "s", "kind", "placement", "key", "descriptor", "i", "o", "n", "u", "a", "firstUpdated", "querySelector", "exemptOn", "required", "requiredMessage", "tagName", "toLocaleLowerCase", "Error", "warn", "assign", "hasAttribute", "setAttribute", "apply", "c", "get", "enumerable", "configurable", "Array", "from", "querySelectorAll", "filter", "for<PERSON>ach", "call", "querySlot", "querySlotAll"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/decorators/query-slot.js"], "sourcesContent": ["import{notProductionEnvironment as e,LogService as t}from\"../services/log.service.js\";const r=(e,t,r)=>{Object.defineProperty(t,r,e)},s=(e,t)=>({kind:\"method\",placement:\"prototype\",key:t.key,descriptor:e});function i(i,o){return(n,u)=>{const a=n.firstUpdated;n.firstUpdated=function(){const r=this.querySelector(i),s=o?.exemptOn&&o?.exemptOn(this);if(!r&&e()&&o?.required&&!s){const e=o.requiredMessage||`The <${i}> element is required to use <${this.tagName.toLocaleLowerCase()}>`;if(\"error\"===o.required)throw Error(e);t.warn(e,this)}o?.assign&&!1===r?.hasAttribute(\"slot\")&&r.setAttribute(\"slot\",o.assign),a&&a.apply(this)};const c={get(){return this.querySelector(i)},enumerable:!0,configurable:!0};return void 0!==u?r(c,n,u):s(c,n)}}function o(e,t){return(i,o)=>{const n=i.firstUpdated;i.firstUpdated=function(r){t?.assign&&Array.from(this.querySelectorAll(e)).filter((e=>!e.hasAttribute(\"slot\"))).forEach((e=>e.setAttribute(\"slot\",t.assign))),n&&n.call(this,r)};const u={get(){return this.querySelectorAll(e)},enumerable:!0,configurable:!0};return void 0!==o?r(u,i,o):s(u,i)}}export{i as querySlot,o as querySlotAll};\n"], "mappings": "AAAA,SAAOA,wBAAwB,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,QAAK,4BAA4B;AAAC,MAAMC,CAAC,GAACA,CAACH,CAAC,EAACE,CAAC,EAACC,CAAC,KAAG;IAACC,MAAM,CAACC,cAAc,CAACH,CAAC,EAACC,CAAC,EAACH,CAAC,CAAC;EAAA,CAAC;EAACM,CAAC,GAACA,CAACN,CAAC,EAACE,CAAC,MAAI;IAACK,IAAI,EAAC,QAAQ;IAACC,SAAS,EAAC,WAAW;IAACC,GAAG,EAACP,CAAC,CAACO,GAAG;IAACC,UAAU,EAACV;EAAC,CAAC,CAAC;AAAC,SAASW,CAACA,CAACA,CAAC,EAACC,CAAC,EAAC;EAAC,OAAM,CAACC,CAAC,EAACC,CAAC,KAAG;IAAC,MAAMC,CAAC,GAACF,CAAC,CAACG,YAAY;IAACH,CA<PERSON>,<PERSON>ACG,YAAY,GAA<PERSON>,<PERSON><PERSON><PERSON>;MAAC,MAAMb,CAAC,GAAC,IAAI,CAACc,aAAa,CAACN,CAAC,CAAC;QAACL,CAAC,GAACM,CAAC,EAAEM,QAAQ,IAAEN,CAAC,EAAEM,QAAQ,CAAC,IAAI,CAAC;MAAC,IAAG,CAACf,CAAC,IAAEH,CAAC,CAAC,CAAC,IAAEY,CAAC,EAAEO,QAAQ,IAAE,CAACb,CAAC,EAAC;QAAC,MAAMN,CAAC,GAACY,CAAC,CAACQ,eAAe,IAAG,QAAOT,CAAE,iCAAgC,IAAI,CAACU,OAAO,CAACC,iBAAiB,CAAC,CAAE,GAAE;QAAC,IAAG,OAAO,KAAGV,CAAC,CAACO,QAAQ,EAAC,MAAMI,KAAK,CAACvB,CAAC,CAAC;QAACE,CAAC,CAACsB,IAAI,CAACxB,CAAC,EAAC,IAAI,CAAC;MAAA;MAACY,CAAC,EAAEa,MAAM,IAAE,CAAC,CAAC,KAAGtB,CAAC,EAAEuB,YAAY,CAAC,MAAM,CAAC,IAAEvB,CAAC,CAACwB,YAAY,CAAC,MAAM,EAACf,CAAC,CAACa,MAAM,CAAC,EAACV,CAAC,IAAEA,CAAC,CAACa,KAAK,CAAC,IAAI,CAAC;IAAA,CAAC;IAAC,MAAMC,CAAC,GAAC;MAACC,GAAGA,CAAA,EAAE;QAAC,OAAO,IAAI,CAACb,aAAa,CAACN,CAAC,CAAC;MAAA,CAAC;MAACoB,UAAU,EAAC,CAAC,CAAC;MAACC,YAAY,EAAC,CAAC;IAAC,CAAC;IAAC,OAAO,KAAK,CAAC,KAAGlB,CAAC,GAACX,CAAC,CAAC0B,CAAC,EAAChB,CAAC,EAACC,CAAC,CAAC,GAACR,CAAC,CAACuB,CAAC,EAAChB,CAAC,CAAC;EAAA,CAAC;AAAA;AAAC,SAASD,CAACA,CAACZ,CAAC,EAACE,CAAC,EAAC;EAAC,OAAM,CAACS,CAAC,EAACC,CAAC,KAAG;IAAC,MAAMC,CAAC,GAACF,CAAC,CAACK,YAAY;IAACL,CAAC,CAACK,YAAY,GAAC,UAASb,CAAC,EAAC;MAACD,CAAC,EAAEuB,MAAM,IAAEQ,KAAK,CAACC,IAAI,CAAC,IAAI,CAACC,gBAAgB,CAACnC,CAAC,CAAC,CAAC,CAACoC,MAAM,CAAEpC,CAAC,IAAE,CAACA,CAAC,CAAC0B,YAAY,CAAC,MAAM,CAAE,CAAC,CAACW,OAAO,CAAErC,CAAC,IAAEA,CAAC,CAAC2B,YAAY,CAAC,MAAM,EAACzB,CAAC,CAACuB,MAAM,CAAE,CAAC,EAACZ,CAAC,IAAEA,CAAC,CAACyB,IAAI,CAAC,IAAI,EAACnC,CAAC,CAAC;IAAA,CAAC;IAAC,MAAMW,CAAC,GAAC;MAACgB,GAAGA,CAAA,EAAE;QAAC,OAAO,IAAI,CAACK,gBAAgB,CAACnC,CAAC,CAAC;MAAA,CAAC;MAAC+B,UAAU,EAAC,CAAC,CAAC;MAACC,YAAY,EAAC,CAAC;IAAC,CAAC;IAAC,OAAO,KAAK,CAAC,KAAGpB,CAAC,GAACT,CAAC,CAACW,CAAC,EAACH,CAAC,EAACC,CAAC,CAAC,GAACN,CAAC,CAACQ,CAAC,EAACH,CAAC,CAAC;EAAA,CAAC;AAAA;AAAC,SAAOA,CAAC,IAAI4B,SAAS,EAAC3B,CAAC,IAAI4B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}