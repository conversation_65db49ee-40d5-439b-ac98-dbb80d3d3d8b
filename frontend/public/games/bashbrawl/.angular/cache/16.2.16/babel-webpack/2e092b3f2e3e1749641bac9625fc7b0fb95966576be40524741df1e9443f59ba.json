{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"talk-bubbles\",\n  V = [\"talk-bubbles\", C({\n    outline: '<path d=\"M21.9938 25.0075C21.9938 25.5577 21.5439 26.0079 20.9941 26.0079H6.99844C6.72852 26.0079 6.4786 26.1179 6.28866 26.298L3.99938 28.5888V12.9932C3.99938 12.443 4.44923 11.9929 4.99906 11.9929H9.9975V9.99214H4.99906C3.34958 9.99214 2 11.3426 2 12.9932V30.9996C2 31.3998 2.23993 31.7699 2.61981 31.92C2.73977 31.97 2.86973 32 2.99969 32C3.25961 32 3.51953 31.9 3.70947 31.7099L7.41831 27.9986H21.0041C22.6535 27.9986 24.0031 26.6481 24.0031 24.9975V23.9971H22.0037V24.9975L21.9938 25.0075ZM30.9909 4H14.9959C13.3465 4 11.9969 5.35048 11.9969 7.00107V19.0054C11.9969 20.6559 13.3465 22.0064 14.9959 22.0064H28.5817L32.2905 25.7178C32.4805 25.9078 32.7404 26.0079 33.0003 26.0079C33.1303 26.0079 33.2602 25.9879 33.3802 25.9278C33.7501 25.7778 34 25.4076 34 25.0075V7.00107C34 5.35048 32.6504 4 31.0009 4H30.9909ZM31.9906 22.5966L29.7013 20.3058C29.5114 20.1158 29.2615 20.0157 28.9916 20.0157H14.9959C14.4461 20.0157 13.9963 19.5656 13.9963 19.0154V7.00107C13.9963 6.45088 14.4461 6.00071 14.9959 6.00071H30.9909C31.5408 6.00071 31.9906 6.45088 31.9906 7.00107V22.5966Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M31.9906 12.7129V22.5966L29.7013 20.3058C29.5114 20.1158 29.2615 20.0157 28.9916 20.0157H14.9959C14.4461 20.0157 13.9963 19.5656 13.9963 19.0154V7.00107C13.9963 6.45088 14.4461 6.00071 14.9959 6.00071H23C23 6.00048 23 6.00095 23 6.00071C23 5.30574 23.1013 4.63371 23.2899 4H14.9959C13.3465 4 11.9969 5.35048 11.9969 7.00107V19.0054C11.9969 20.6559 13.3465 22.0064 14.9959 22.0064H28.5817L32.2905 25.7178C32.4805 25.9078 32.7404 26.0079 33.0003 26.0079C33.1303 26.0079 33.2602 25.9879 33.3802 25.9278C33.7501 25.7778 34 25.4076 34 25.0075V11.7453C33.3934 12.1684 32.7166 12.498 31.9906 12.7129Z\"/><path d=\"M20.9941 26.0079C21.5439 26.0079 21.9938 25.5577 21.9938 25.0075L22.0037 24.9975V23.9971H24.0031V24.9975C24.0031 26.6481 22.6535 27.9986 21.0041 27.9986H7.41831L3.70947 31.7099C3.51953 31.9 3.25961 32 2.99969 32C2.86973 32 2.73977 31.97 2.61981 31.92C2.23993 31.7699 2 31.3998 2 30.9996V12.9932C2 11.3426 3.34958 9.99214 4.99906 9.99214H9.9975V11.9929H4.99906C4.44923 11.9929 3.99938 12.443 3.99938 12.9932V28.5888L6.28866 26.298C6.4786 26.1179 6.72852 26.0079 6.99844 26.0079H20.9941Z\"/>',\n    solid: '<path d=\"M9.9975 19V10H4.99906C3.34958 10 2 11.35 2 13V31C2 31.4 2.23993 31.77 2.61981 31.92C2.73977 31.97 2.86973 32 2.99969 32C3.25961 32 3.51953 31.9 3.70947 31.71L7.41831 28H21.0041C22.6535 28 24.0031 26.65 24.0031 25V24H15.0059C12.2468 24 10.0075 21.76 10.0075 19H9.9975ZM30.9909 4H14.9959C13.3465 4 11.9969 5.35 11.9969 7V19C11.9969 20.65 13.3465 22 14.9959 22H28.5817L32.2905 25.71C32.4805 25.9 32.7404 26 33.0003 26C33.1303 26 33.2602 25.98 33.3802 25.92C33.7501 25.77 34 25.4 34 25V7C34 5.35 32.6504 4 31.0009 4H30.9909Z\"/>',\n    solidBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M34 11.7383C32.8646 12.5335 31.4824 13 29.9913 13C26.1265 13 22.9934 9.86599 22.9934 6C22.9934 5.30503 23.0947 4.63371 23.2832 4H14.9959C13.3465 4 11.9969 5.35 11.9969 7V19C11.9969 20.65 13.3465 22 14.9959 22H28.5817L32.2905 25.71C32.4805 25.9 32.7404 26 33.0003 26C33.1303 26 33.2602 25.98 33.3802 25.92C33.7501 25.77 34 25.4 34 25V11.7383Z\"/><path d=\"M9.9975 10V19H10.0075C10.0075 21.76 12.2468 24 15.0059 24H24.0031V25C24.0031 26.65 22.6535 28 21.0041 28H7.41831L3.70947 31.71C3.51953 31.9 3.25961 32 2.99969 32C2.86973 32 2.73977 31.97 2.61981 31.92C2.23993 31.77 2 31.4 2 31V13C2 11.35 3.34958 10 4.99906 10H9.9975Z\"/>'\n  })];\nexport { V as talkBubblesIcon, H as talkBubblesIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "V", "outline", "outlineBadged", "solid", "solidBadged", "talkBubblesIcon", "talkBubblesIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/talk-bubbles.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"talk-bubbles\",V=[\"talk-bubbles\",C({outline:'<path d=\"M21.9938 25.0075C21.9938 25.5577 21.5439 26.0079 20.9941 26.0079H6.99844C6.72852 26.0079 6.4786 26.1179 6.28866 26.298L3.99938 28.5888V12.9932C3.99938 12.443 4.44923 11.9929 4.99906 11.9929H9.9975V9.99214H4.99906C3.34958 9.99214 2 11.3426 2 12.9932V30.9996C2 31.3998 2.23993 31.7699 2.61981 31.92C2.73977 31.97 2.86973 32 2.99969 32C3.25961 32 3.51953 31.9 3.70947 31.7099L7.41831 27.9986H21.0041C22.6535 27.9986 24.0031 26.6481 24.0031 24.9975V23.9971H22.0037V24.9975L21.9938 25.0075ZM30.9909 4H14.9959C13.3465 4 11.9969 5.35048 11.9969 7.00107V19.0054C11.9969 20.6559 13.3465 22.0064 14.9959 22.0064H28.5817L32.2905 25.7178C32.4805 25.9078 32.7404 26.0079 33.0003 26.0079C33.1303 26.0079 33.2602 25.9879 33.3802 25.9278C33.7501 25.7778 34 25.4076 34 25.0075V7.00107C34 5.35048 32.6504 4 31.0009 4H30.9909ZM31.9906 22.5966L29.7013 20.3058C29.5114 20.1158 29.2615 20.0157 28.9916 20.0157H14.9959C14.4461 20.0157 13.9963 19.5656 13.9963 19.0154V7.00107C13.9963 6.45088 14.4461 6.00071 14.9959 6.00071H30.9909C31.5408 6.00071 31.9906 6.45088 31.9906 7.00107V22.5966Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M31.9906 12.7129V22.5966L29.7013 20.3058C29.5114 20.1158 29.2615 20.0157 28.9916 20.0157H14.9959C14.4461 20.0157 13.9963 19.5656 13.9963 19.0154V7.00107C13.9963 6.45088 14.4461 6.00071 14.9959 6.00071H23C23 6.00048 23 6.00095 23 6.00071C23 5.30574 23.1013 4.63371 23.2899 4H14.9959C13.3465 4 11.9969 5.35048 11.9969 7.00107V19.0054C11.9969 20.6559 13.3465 22.0064 14.9959 22.0064H28.5817L32.2905 25.7178C32.4805 25.9078 32.7404 26.0079 33.0003 26.0079C33.1303 26.0079 33.2602 25.9879 33.3802 25.9278C33.7501 25.7778 34 25.4076 34 25.0075V11.7453C33.3934 12.1684 32.7166 12.498 31.9906 12.7129Z\"/><path d=\"M20.9941 26.0079C21.5439 26.0079 21.9938 25.5577 21.9938 25.0075L22.0037 24.9975V23.9971H24.0031V24.9975C24.0031 26.6481 22.6535 27.9986 21.0041 27.9986H7.41831L3.70947 31.7099C3.51953 31.9 3.25961 32 2.99969 32C2.86973 32 2.73977 31.97 2.61981 31.92C2.23993 31.7699 2 31.3998 2 30.9996V12.9932C2 11.3426 3.34958 9.99214 4.99906 9.99214H9.9975V11.9929H4.99906C4.44923 11.9929 3.99938 12.443 3.99938 12.9932V28.5888L6.28866 26.298C6.4786 26.1179 6.72852 26.0079 6.99844 26.0079H20.9941Z\"/>',solid:'<path d=\"M9.9975 19V10H4.99906C3.34958 10 2 11.35 2 13V31C2 31.4 2.23993 31.77 2.61981 31.92C2.73977 31.97 2.86973 32 2.99969 32C3.25961 32 3.51953 31.9 3.70947 31.71L7.41831 28H21.0041C22.6535 28 24.0031 26.65 24.0031 25V24H15.0059C12.2468 24 10.0075 21.76 10.0075 19H9.9975ZM30.9909 4H14.9959C13.3465 4 11.9969 5.35 11.9969 7V19C11.9969 20.65 13.3465 22 14.9959 22H28.5817L32.2905 25.71C32.4805 25.9 32.7404 26 33.0003 26C33.1303 26 33.2602 25.98 33.3802 25.92C33.7501 25.77 34 25.4 34 25V7C34 5.35 32.6504 4 31.0009 4H30.9909Z\"/>',solidBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M34 11.7383C32.8646 12.5335 31.4824 13 29.9913 13C26.1265 13 22.9934 9.86599 22.9934 6C22.9934 5.30503 23.0947 4.63371 23.2832 4H14.9959C13.3465 4 11.9969 5.35 11.9969 7V19C11.9969 20.65 13.3465 22 14.9959 22H28.5817L32.2905 25.71C32.4805 25.9 32.7404 26 33.0003 26C33.1303 26 33.2602 25.98 33.3802 25.92C33.7501 25.77 34 25.4 34 25V11.7383Z\"/><path d=\"M9.9975 10V19H10.0075C10.0075 21.76 12.2468 24 15.0059 24H24.0031V25C24.0031 26.65 22.6535 28 21.0041 28H7.41831L3.70947 31.71C3.51953 31.9 3.25961 32 2.99969 32C2.86973 32 2.73977 31.97 2.61981 31.92C2.23993 31.77 2 31.4 2 31V13C2 11.35 3.34958 10 4.99906 10H9.9975Z\"/>'})];export{V as talkBubblesIcon,H as talkBubblesIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,cAAc;EAACC,CAAC,GAAC,CAAC,cAAc,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,sjCAAsjC;IAACC,aAAa,EAAC,8sCAA8sC;IAACC,KAAK,EAAC,shBAAshB;IAACC,WAAW,EAAC;EAAwvB,CAAC,CAAC,CAAC;AAAC,SAAOJ,CAAC,IAAIK,eAAe,EAACN,CAAC,IAAIO,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}