{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst V = \"cursor-hand\",\n  L = [\"cursor-hand\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.8699 11.3743C27.4859 11.9507 29.875 13.2846 31.7399 15.2101C31.8956 15.3808 31.9874 15.6002 31.9999 15.831V21.8401C32.0289 24.9 30.9649 27.8695 28.9999 30.2128V32.9169C29.0065 33.4863 28.5673 33.9613 27.9999 33.9985H14.0899C13.5226 33.9613 13.0834 33.4863 13.0899 32.9169V30.994C12.2915 30.2589 11.6162 29.3999 11.0899 28.4501C10.3696 27.215 9.88475 25.8566 9.65994 24.4441L4.99994 18.9157C4.34117 18.2272 3.9816 17.3053 3.99994 16.3518C4.00248 15.3583 4.4065 14.4081 5.11994 13.7178C6.58612 12.2774 8.93375 12.2774 10.3999 13.7178L11.9999 15.3002V5.28507C12.0651 3.41201 13.6294 1.94384 15.4999 2.0001C17.3705 1.94384 18.9347 3.41201 18.9999 5.28507V10.5931C20.9799 10.6288 22.9494 10.8909 24.8699 11.3743ZM27.3999 29.1612C29.1372 27.1444 30.0638 24.553 29.9999 21.8902V16.2517C29.2502 15.5616 28.4089 14.9786 27.4999 14.519V19.2963L25.8999 18.9457V13.7879C25.3991 13.5969 24.8884 13.4331 24.3699 13.2972C23.9399 13.187 23.5199 13.0969 23.1099 13.0168V18.3749L21.5099 18.0244V12.7864C20.6769 12.6829 19.8392 12.6227 18.9999 12.6062V17.5336L16.9999 17.103V5.28507C16.926 4.52304 16.263 3.95641 15.4999 4.00313C14.7369 3.95641 14.0739 4.52304 13.9999 5.28507V20.2778L11.9999 21.2793V18.1345L8.99994 15.13C8.31485 14.4542 7.21503 14.4542 6.52994 15.13C6.20625 15.4499 6.02405 15.8864 6.02405 16.3418C6.02405 16.7973 6.20625 17.2337 6.52994 17.5536L11.4499 23.3825C11.5885 23.5493 11.6692 23.7566 11.6799 23.9734C11.8345 25.2184 12.2429 26.4184 12.8799 27.4987C13.3317 28.3573 13.9505 29.1167 14.6999 29.7321C14.9755 29.9186 15.1404 30.2302 15.1399 30.5633V32.0456H26.9999V29.8823C27.036 29.5995 27.1792 29.3413 27.3999 29.1612Z\"/>',\n    solid: '<path d=\"M29.8039 14.33V19.16L27.795 18.73V13.24C27.0376 12.9009 26.2552 12.6201 25.4547 12.4V18.22L23.4459 17.79V12C22.341 11.82 21.2562 11.7 20.3523 11.64V17.15L18.3434 16.72V11.48V4.33998C18.2889 2.95431 17.1242 1.87055 15.7319 1.90998C14.3396 1.87055 13.1749 2.95431 13.1204 4.33998V17.27V19.86L11.1116 20.86V15.6L8.77127 13.21C7.66146 12.1063 5.8634 12.1063 4.75359 13.21C3.63975 14.3478 3.63975 16.1621 4.75359 17.3L10.7801 24.4C10.9473 25.8877 11.4227 27.3246 12.1763 28.62C12.7152 29.6752 13.4716 30.6055 14.396 31.35V33.91H28.8999V30.62C30.9278 28.2564 32.0016 25.2272 31.9132 22.12V16.12C31.2965 15.4291 30.5867 14.8267 29.8039 14.33Z\"/>'\n  })];\nexport { L as cursorHandIcon, V as cursorHandIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "V", "L", "outline", "solid", "cursorHandIcon", "cursorHandIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/cursor-hand.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const V=\"cursor-hand\",L=[\"cursor-hand\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.8699 11.3743C27.4859 11.9507 29.875 13.2846 31.7399 15.2101C31.8956 15.3808 31.9874 15.6002 31.9999 15.831V21.8401C32.0289 24.9 30.9649 27.8695 28.9999 30.2128V32.9169C29.0065 33.4863 28.5673 33.9613 27.9999 33.9985H14.0899C13.5226 33.9613 13.0834 33.4863 13.0899 32.9169V30.994C12.2915 30.2589 11.6162 29.3999 11.0899 28.4501C10.3696 27.215 9.88475 25.8566 9.65994 24.4441L4.99994 18.9157C4.34117 18.2272 3.9816 17.3053 3.99994 16.3518C4.00248 15.3583 4.4065 14.4081 5.11994 13.7178C6.58612 12.2774 8.93375 12.2774 10.3999 13.7178L11.9999 15.3002V5.28507C12.0651 3.41201 13.6294 1.94384 15.4999 2.0001C17.3705 1.94384 18.9347 3.41201 18.9999 5.28507V10.5931C20.9799 10.6288 22.9494 10.8909 24.8699 11.3743ZM27.3999 29.1612C29.1372 27.1444 30.0638 24.553 29.9999 21.8902V16.2517C29.2502 15.5616 28.4089 14.9786 27.4999 14.519V19.2963L25.8999 18.9457V13.7879C25.3991 13.5969 24.8884 13.4331 24.3699 13.2972C23.9399 13.187 23.5199 13.0969 23.1099 13.0168V18.3749L21.5099 18.0244V12.7864C20.6769 12.6829 19.8392 12.6227 18.9999 12.6062V17.5336L16.9999 17.103V5.28507C16.926 4.52304 16.263 3.95641 15.4999 4.00313C14.7369 3.95641 14.0739 4.52304 13.9999 5.28507V20.2778L11.9999 21.2793V18.1345L8.99994 15.13C8.31485 14.4542 7.21503 14.4542 6.52994 15.13C6.20625 15.4499 6.02405 15.8864 6.02405 16.3418C6.02405 16.7973 6.20625 17.2337 6.52994 17.5536L11.4499 23.3825C11.5885 23.5493 11.6692 23.7566 11.6799 23.9734C11.8345 25.2184 12.2429 26.4184 12.8799 27.4987C13.3317 28.3573 13.9505 29.1167 14.6999 29.7321C14.9755 29.9186 15.1404 30.2302 15.1399 30.5633V32.0456H26.9999V29.8823C27.036 29.5995 27.1792 29.3413 27.3999 29.1612Z\"/>',solid:'<path d=\"M29.8039 14.33V19.16L27.795 18.73V13.24C27.0376 12.9009 26.2552 12.6201 25.4547 12.4V18.22L23.4459 17.79V12C22.341 11.82 21.2562 11.7 20.3523 11.64V17.15L18.3434 16.72V11.48V4.33998C18.2889 2.95431 17.1242 1.87055 15.7319 1.90998C14.3396 1.87055 13.1749 2.95431 13.1204 4.33998V17.27V19.86L11.1116 20.86V15.6L8.77127 13.21C7.66146 12.1063 5.8634 12.1063 4.75359 13.21C3.63975 14.3478 3.63975 16.1621 4.75359 17.3L10.7801 24.4C10.9473 25.8877 11.4227 27.3246 12.1763 28.62C12.7152 29.6752 13.4716 30.6055 14.396 31.35V33.91H28.8999V30.62C30.9278 28.2564 32.0016 25.2272 31.9132 22.12V16.12C31.2965 15.4291 30.5867 14.8267 29.8039 14.33Z\"/>'})];export{L as cursorHandIcon,V as cursorHandIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,aAAa;EAACC,CAAC,GAAC,CAAC,aAAa,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,mpDAAmpD;IAACC,KAAK,EAAC;EAAyoB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,cAAc,EAACJ,CAAC,IAAIK,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}