{"ast": null, "code": "export { CdsButtonAction } from \"./button-action.element.js\";", "map": {"version": 3, "names": ["CdsButtonAction"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/button-action/index.js"], "sourcesContent": ["export{CdsButtonAction}from\"./button-action.element.js\";\n"], "mappings": "AAAA,SAAOA,eAAe,QAAK,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}