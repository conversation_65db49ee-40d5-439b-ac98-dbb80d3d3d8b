{"ast": null, "code": "import { renderIcon as V } from \"../icon.renderer.js\";\nconst a = \"calendar-mini\",\n  H = [\"calendar-mini\", V({\n    outline: '<path d=\"M29,8H27.6V4a1.6,1.6,0,0,0-3.2,0V8H11.6V4A1.6,1.6,0,0,0,8.4,4V8H7a3,3,0,0,0-3,3V29a3,3,0,0,0,3,3H29a3,3,0,0,0,3-3V11A3,3,0,0,0,29,8Zm-1,4v4.4H8V12ZM8,28V19.6H28V28Z\"/>',\n    solid: '<path d=\"M29,8H27.6V4a1.6,1.6,0,0,0-3.2,0V8H11.6V4A1.6,1.6,0,0,0,8.4,4V8H7a3,3,0,0,0-3,3V29a3,3,0,0,0,3,3H29a3,3,0,0,0,3-3V11A3,3,0,0,0,29,8ZM8,28V16H28V28Z\"/>'\n  })];\nexport { H as calendarMiniIcon, a as calendarMiniIconName };", "map": {"version": 3, "names": ["renderIcon", "V", "a", "H", "outline", "solid", "calendarMiniIcon", "calendarMiniIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/calendar-mini.js"], "sourcesContent": ["import{renderIcon as V}from\"../icon.renderer.js\";const a=\"calendar-mini\",H=[\"calendar-mini\",V({outline:'<path d=\"M29,8H27.6V4a1.6,1.6,0,0,0-3.2,0V8H11.6V4A1.6,1.6,0,0,0,8.4,4V8H7a3,3,0,0,0-3,3V29a3,3,0,0,0,3,3H29a3,3,0,0,0,3-3V11A3,3,0,0,0,29,8Zm-1,4v4.4H8V12ZM8,28V19.6H28V28Z\"/>',solid:'<path d=\"M29,8H27.6V4a1.6,1.6,0,0,0-3.2,0V8H11.6V4A1.6,1.6,0,0,0,8.4,4V8H7a3,3,0,0,0-3,3V29a3,3,0,0,0,3,3H29a3,3,0,0,0,3-3V11A3,3,0,0,0,29,8ZM8,28V16H28V28Z\"/>'})];export{H as calendarMiniIcon,a as calendarMiniIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,eAAe;EAACC,CAAC,GAAC,CAAC,eAAe,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,kLAAkL;IAACC,KAAK,EAAC;EAAiK,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,gBAAgB,EAACJ,CAAC,IAAIK,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}