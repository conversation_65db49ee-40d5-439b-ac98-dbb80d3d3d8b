{"ast": null, "code": "import { css as a } from \"lit\";\nvar s = a`@keyframes fadeInAndOut{0%,to{opacity:0}75%{opacity:1}}:host{--color:currentColor;display:inline-block;height:var(--cds-global-space-7,calc(16 * 1rem / var(--cds-global-base,20)));width:var(--cds-global-space-7,calc(16 * 1rem / var(--cds-global-base,20)));min-height:var(--cds-global-space-7,calc(16 * 1rem / var(--cds-global-base,20)));min-width:var(--cds-global-space-7,calc(16 * 1rem / var(--cds-global-base,20)));margin:0;vertical-align:middle;fill:var(--color);color:var(--color);contain:strict;cursor:inherit}svg{display:block}:host([size*=xs]){height:var(--cds-global-space-5,calc(8 * 1rem / var(--cds-global-base,20)));width:var(--cds-global-space-5,calc(8 * 1rem / var(--cds-global-base,20)));min-height:var(--cds-global-space-5,calc(8 * 1rem / var(--cds-global-base,20)));min-width:var(--cds-global-space-5,calc(8 * 1rem / var(--cds-global-base,20)))}:host([size*=sm]){height:var(--cds-global-space-7,calc(16 * 1rem / var(--cds-global-base,20)));width:var(--cds-global-space-7,calc(16 * 1rem / var(--cds-global-base,20)));min-height:var(--cds-global-space-7,calc(16 * 1rem / var(--cds-global-base,20)));min-width:var(--cds-global-space-7,calc(16 * 1rem / var(--cds-global-base,20)))}:host([size*=md]){height:var(--cds-global-space-9,calc(24 * 1rem / var(--cds-global-base,20)));width:var(--cds-global-space-9,calc(24 * 1rem / var(--cds-global-base,20)));min-height:var(--cds-global-space-9,calc(24 * 1rem / var(--cds-global-base,20)));min-width:var(--cds-global-space-9,calc(24 * 1rem / var(--cds-global-base,20)))}:host([size*=lg]){height:var(--cds-global-space-11,calc(36 * 1rem / var(--cds-global-base,20)));width:var(--cds-global-space-11,calc(36 * 1rem / var(--cds-global-base,20)));min-height:var(--cds-global-space-11,calc(36 * 1rem / var(--cds-global-base,20)));min-width:var(--cds-global-space-11,calc(36 * 1rem / var(--cds-global-base,20)))}:host([size*=xl]){height:var(--cds-global-space-12,calc(48 * 1rem / var(--cds-global-base,20)));width:var(--cds-global-space-12,calc(48 * 1rem / var(--cds-global-base,20)));min-height:var(--cds-global-space-12,calc(48 * 1rem / var(--cds-global-base,20)));min-width:var(--cds-global-space-12,calc(48 * 1rem / var(--cds-global-base,20)))}:host([size*=xxl]){height:calc(var(--cds-global-space-13,calc(64 * 1rem / var(--cds-global-base,20))) - var(--cds-global-space-5,calc(8 * 1rem / var(--cds-global-base,20))));width:calc(var(--cds-global-space-13,calc(64 * 1rem / var(--cds-global-base,20))) - var(--cds-global-space-5,calc(8 * 1rem / var(--cds-global-base,20))));min-height:calc(var(--cds-global-space-13,calc(64 * 1rem / var(--cds-global-base,20))) - var(--cds-global-space-5,calc(8 * 1rem / var(--cds-global-base,20))));min-width:calc(var(--cds-global-space-13,calc(64 * 1rem / var(--cds-global-base,20))) - var(--cds-global-space-5,calc(8 * 1rem / var(--cds-global-base,20))))}:host([size*=fit]){height:auto;width:auto;contain:layout}:host([status=success]){--color:var(--cds-alias-status-success, var(--cds-global-color-green-700, #42810e))}:host([status=danger]){--color:var(--cds-alias-status-danger, var(--cds-global-color-red-700, #e02200))}:host([status=warning]){--color:var(--cds-alias-status-warning-dark, var(--cds-global-color-ochre-800, #a36500))}:host([status=info]){--color:var(--cds-alias-status-info, var(--cds-global-color-blue-700, #0079ad))}:host([status=neutral]){--color:var(--cds-alias-status-neutral, var(--cds-global-color-construction-600, #4f6169))}:host([inverse]){--color:var(--cds-global-color-construction-200, #cbd4d8)}:host([direction=up]){transform:rotate(0)}:host([direction=down]){transform:rotate(180deg)}:host([direction=right]){transform:rotate(90deg)}:host([direction=left]){transform:rotate(270deg)}:host([flip=horizontal]){transform:scale(-1) rotateX(180deg)}:host([flip=vertical]){transform:scale(-1) rotateY(180deg)}.alert,.badge{fill:var(--badge-color,var(--cds-alias-status-danger,var(--cds-global-color-red-700,#e02200)))}:host([badge=success]){--badge-color:var(--cds-alias-status-success, var(--cds-global-color-green-700, #42810e))}:host([badge=danger]){--badge-color:var(--cds-alias-status-danger, var(--cds-global-color-red-700, #e02200))}:host([badge*=warning]){--badge-color:var(--cds-alias-status-warning-dark, var(--cds-global-color-ochre-800, #a36500))}:host([badge=inherit]){--badge-color:currentColor}:host([badge=info]){--badge-color:var(--cds-alias-status-info, var(--cds-global-color-blue-700, #0079ad))}:host([badge=neutral]){--badge-color:var(--cds-alias-status-neutral, var(--cds-global-color-construction-600, #4f6169))}:host([badge=inherit-triangle]){--badge-color:currentColor}:host([badge][inverse]){--badge-color:var(--cds-alias-status-danger, var(--cds-global-color-red-700, #e02200))}:host([badge=success][inverse]){--badge-color:var(--cds-alias-status-success, var(--cds-global-color-green-700, #42810e))}:host([badge*=warning][inverse]){--badge-color:var(--cds-alias-status-warning-dark, var(--cds-global-color-ochre-800, #a36500))}:host([badge*=inherit][inverse]){--badge-color:currentColor}:host([badge=info][inverse]){--badge-color:var(--cds-alias-status-info, var(--cds-global-color-blue-700, #0079ad))}.cds-internal-dot-1{animation:fadeInAndOut 1.8s ease-in 0s infinite}.cds-internal-dot-2{animation:fadeInAndOut 1.8s ease-out .422s infinite}.cds-internal-dot-3{animation:fadeInAndOut 1.8s ease-out .675s infinite}@media (prefers-reduced-motion){.cds-internal-dot-1,.cds-internal-dot-2,.cds-internal-dot-3{animation:none}}`;\nexport { s as default };", "map": {"version": 3, "names": ["css", "a", "s", "default"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/icon.element.scss.js"], "sourcesContent": ["import{css as a}from\"lit\";var s=a`@keyframes fadeInAndOut{0%,to{opacity:0}75%{opacity:1}}:host{--color:currentColor;display:inline-block;height:var(--cds-global-space-7,calc(16 * 1rem / var(--cds-global-base,20)));width:var(--cds-global-space-7,calc(16 * 1rem / var(--cds-global-base,20)));min-height:var(--cds-global-space-7,calc(16 * 1rem / var(--cds-global-base,20)));min-width:var(--cds-global-space-7,calc(16 * 1rem / var(--cds-global-base,20)));margin:0;vertical-align:middle;fill:var(--color);color:var(--color);contain:strict;cursor:inherit}svg{display:block}:host([size*=xs]){height:var(--cds-global-space-5,calc(8 * 1rem / var(--cds-global-base,20)));width:var(--cds-global-space-5,calc(8 * 1rem / var(--cds-global-base,20)));min-height:var(--cds-global-space-5,calc(8 * 1rem / var(--cds-global-base,20)));min-width:var(--cds-global-space-5,calc(8 * 1rem / var(--cds-global-base,20)))}:host([size*=sm]){height:var(--cds-global-space-7,calc(16 * 1rem / var(--cds-global-base,20)));width:var(--cds-global-space-7,calc(16 * 1rem / var(--cds-global-base,20)));min-height:var(--cds-global-space-7,calc(16 * 1rem / var(--cds-global-base,20)));min-width:var(--cds-global-space-7,calc(16 * 1rem / var(--cds-global-base,20)))}:host([size*=md]){height:var(--cds-global-space-9,calc(24 * 1rem / var(--cds-global-base,20)));width:var(--cds-global-space-9,calc(24 * 1rem / var(--cds-global-base,20)));min-height:var(--cds-global-space-9,calc(24 * 1rem / var(--cds-global-base,20)));min-width:var(--cds-global-space-9,calc(24 * 1rem / var(--cds-global-base,20)))}:host([size*=lg]){height:var(--cds-global-space-11,calc(36 * 1rem / var(--cds-global-base,20)));width:var(--cds-global-space-11,calc(36 * 1rem / var(--cds-global-base,20)));min-height:var(--cds-global-space-11,calc(36 * 1rem / var(--cds-global-base,20)));min-width:var(--cds-global-space-11,calc(36 * 1rem / var(--cds-global-base,20)))}:host([size*=xl]){height:var(--cds-global-space-12,calc(48 * 1rem / var(--cds-global-base,20)));width:var(--cds-global-space-12,calc(48 * 1rem / var(--cds-global-base,20)));min-height:var(--cds-global-space-12,calc(48 * 1rem / var(--cds-global-base,20)));min-width:var(--cds-global-space-12,calc(48 * 1rem / var(--cds-global-base,20)))}:host([size*=xxl]){height:calc(var(--cds-global-space-13,calc(64 * 1rem / var(--cds-global-base,20))) - var(--cds-global-space-5,calc(8 * 1rem / var(--cds-global-base,20))));width:calc(var(--cds-global-space-13,calc(64 * 1rem / var(--cds-global-base,20))) - var(--cds-global-space-5,calc(8 * 1rem / var(--cds-global-base,20))));min-height:calc(var(--cds-global-space-13,calc(64 * 1rem / var(--cds-global-base,20))) - var(--cds-global-space-5,calc(8 * 1rem / var(--cds-global-base,20))));min-width:calc(var(--cds-global-space-13,calc(64 * 1rem / var(--cds-global-base,20))) - var(--cds-global-space-5,calc(8 * 1rem / var(--cds-global-base,20))))}:host([size*=fit]){height:auto;width:auto;contain:layout}:host([status=success]){--color:var(--cds-alias-status-success, var(--cds-global-color-green-700, #42810e))}:host([status=danger]){--color:var(--cds-alias-status-danger, var(--cds-global-color-red-700, #e02200))}:host([status=warning]){--color:var(--cds-alias-status-warning-dark, var(--cds-global-color-ochre-800, #a36500))}:host([status=info]){--color:var(--cds-alias-status-info, var(--cds-global-color-blue-700, #0079ad))}:host([status=neutral]){--color:var(--cds-alias-status-neutral, var(--cds-global-color-construction-600, #4f6169))}:host([inverse]){--color:var(--cds-global-color-construction-200, #cbd4d8)}:host([direction=up]){transform:rotate(0)}:host([direction=down]){transform:rotate(180deg)}:host([direction=right]){transform:rotate(90deg)}:host([direction=left]){transform:rotate(270deg)}:host([flip=horizontal]){transform:scale(-1) rotateX(180deg)}:host([flip=vertical]){transform:scale(-1) rotateY(180deg)}.alert,.badge{fill:var(--badge-color,var(--cds-alias-status-danger,var(--cds-global-color-red-700,#e02200)))}:host([badge=success]){--badge-color:var(--cds-alias-status-success, var(--cds-global-color-green-700, #42810e))}:host([badge=danger]){--badge-color:var(--cds-alias-status-danger, var(--cds-global-color-red-700, #e02200))}:host([badge*=warning]){--badge-color:var(--cds-alias-status-warning-dark, var(--cds-global-color-ochre-800, #a36500))}:host([badge=inherit]){--badge-color:currentColor}:host([badge=info]){--badge-color:var(--cds-alias-status-info, var(--cds-global-color-blue-700, #0079ad))}:host([badge=neutral]){--badge-color:var(--cds-alias-status-neutral, var(--cds-global-color-construction-600, #4f6169))}:host([badge=inherit-triangle]){--badge-color:currentColor}:host([badge][inverse]){--badge-color:var(--cds-alias-status-danger, var(--cds-global-color-red-700, #e02200))}:host([badge=success][inverse]){--badge-color:var(--cds-alias-status-success, var(--cds-global-color-green-700, #42810e))}:host([badge*=warning][inverse]){--badge-color:var(--cds-alias-status-warning-dark, var(--cds-global-color-ochre-800, #a36500))}:host([badge*=inherit][inverse]){--badge-color:currentColor}:host([badge=info][inverse]){--badge-color:var(--cds-alias-status-info, var(--cds-global-color-blue-700, #0079ad))}.cds-internal-dot-1{animation:fadeInAndOut 1.8s ease-in 0s infinite}.cds-internal-dot-2{animation:fadeInAndOut 1.8s ease-out .422s infinite}.cds-internal-dot-3{animation:fadeInAndOut 1.8s ease-out .675s infinite}@media (prefers-reduced-motion){.cds-internal-dot-1,.cds-internal-dot-2,.cds-internal-dot-3{animation:none}}`;export{s as default};\n"], "mappings": "AAAA,SAAOA,GAAG,IAAIC,CAAC,QAAK,KAAK;AAAC,IAAIC,CAAC,GAACD,CAAE,21KAA01K;AAAC,SAAOC,CAAC,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}