{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"asterisk\",\n  e = [\"asterisk\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.1123 17.9942L29.3011 20.903H29.2596C30.3034 21.397 30.9744 22.4102 30.9993 23.5303C31.0242 24.6503 30.3989 25.6903 29.3779 26.2269C28.357 26.7634 27.1106 26.7071 26.1463 26.0809L21.1132 23.232V28.9896C21.1172 29.9652 20.6282 30.8816 19.8023 31.4464C18.9765 32.0111 17.9171 32.1537 16.9623 31.8285C15.6773 31.3919 14.8364 30.2012 14.8868 28.8897V23.232L9.85368 26.0709C8.62449 26.7701 7.05906 26.5745 6.05577 25.5963C5.05248 24.6181 4.86845 23.108 5.6093 21.9326C5.90128 21.4812 6.30861 21.1095 6.79233 20.853L11.9292 17.9942L6.74044 15.0854C5.69658 14.5914 5.02562 13.5782 5.00072 12.4581C4.97582 11.3381 5.60112 10.2981 6.62208 9.76154C7.64303 9.22496 8.88936 9.28127 9.85368 9.90755L14.8868 12.7564V6.99876C14.8868 5.34259 16.2806 4 18 4C19.7194 4 21.1132 5.34259 21.1132 6.99876V12.7564L26.1048 9.90755C27.3362 9.20107 28.9092 9.39568 29.9153 10.379C30.9214 11.3623 31.1005 12.8801 30.3492 14.0558C30.0572 14.5072 29.6499 14.8789 29.1662 15.1354L24.1123 17.9942ZM27.9932 24.4559C28.26 24.3884 28.4879 24.2214 28.6265 23.9917C28.9135 23.5217 28.7566 22.9169 28.2737 22.6323L20.0755 17.9942L28.2426 13.3461C28.6729 13.0446 28.7968 12.4784 28.5293 12.0353C28.2619 11.5922 27.6898 11.4157 27.2048 11.6268L19.0377 16.2449V6.99876C19.0377 6.4467 18.5731 5.99917 18 5.99917C17.4269 5.99917 16.9623 6.4467 16.9623 6.99876V16.2449L8.76405 11.6368C8.44369 11.4124 8.01864 11.3832 7.66849 11.5616C7.31833 11.74 7.10415 12.0949 7.11644 12.4762C7.12873 12.8576 7.36534 13.199 7.7263 13.3561L15.9245 17.9942L7.75744 22.6323C7.32706 22.9338 7.20319 23.5 7.47066 23.9431C7.73813 24.3862 8.31025 24.5627 8.79518 24.3516L16.9623 19.7435V28.9896C16.9623 29.5417 17.4269 29.9892 18 29.9892C18.5731 29.9892 19.0377 29.5417 19.0377 28.9896V19.7435L27.2048 24.3516C27.4428 24.4859 27.7265 24.5234 27.9932 24.4559Z\"/>',\n    solid: '<path d=\"M28.4525 20.9041L23.4525 17.9941L28.3225 15.1341C28.7887 14.8776 29.1812 14.5057 29.4625 14.0541C30.1865 12.878 30.0139 11.3595 29.0444 10.3758C28.075 9.39206 26.5591 9.19737 25.3725 9.90414L20.5625 12.7541V6.99414C20.5625 5.33729 19.2194 3.99414 17.5625 3.99414C15.9057 3.99414 14.5625 5.33729 14.5625 6.99414V12.7541L9.71254 9.90414C8.78329 9.2776 7.58229 9.22126 6.59848 9.75807C5.61466 10.2949 5.0121 11.3353 5.03609 12.4558C5.06009 13.5763 5.70664 14.5899 6.71254 15.0841L11.7125 17.9941L6.76254 20.8541C6.29641 21.1107 5.9039 21.4826 5.62254 21.9341C4.90864 23.11 5.08597 24.6208 6.05277 25.5994C7.01956 26.578 8.52805 26.7737 9.71254 26.0741L14.5625 23.2341V28.8941C14.514 30.2062 15.3243 31.3974 16.5625 31.8341C17.4827 32.1595 18.5035 32.0169 19.2993 31.4519C20.0951 30.8869 20.5663 29.9701 20.5625 28.9941V23.2341L25.4125 26.0841C26.3418 26.7107 27.5428 26.767 28.5266 26.2302C29.5104 25.6934 30.113 24.653 30.089 23.5325C30.065 22.412 29.4184 21.3983 28.4125 20.9041H28.4525Z\"/>'\n  })];\nexport { e as asteriskIcon, L as asteriskIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "e", "outline", "solid", "asteriskIcon", "asteriskIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/asterisk.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"asterisk\",e=[\"asterisk\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.1123 17.9942L29.3011 20.903H29.2596C30.3034 21.397 30.9744 22.4102 30.9993 23.5303C31.0242 24.6503 30.3989 25.6903 29.3779 26.2269C28.357 26.7634 27.1106 26.7071 26.1463 26.0809L21.1132 23.232V28.9896C21.1172 29.9652 20.6282 30.8816 19.8023 31.4464C18.9765 32.0111 17.9171 32.1537 16.9623 31.8285C15.6773 31.3919 14.8364 30.2012 14.8868 28.8897V23.232L9.85368 26.0709C8.62449 26.7701 7.05906 26.5745 6.05577 25.5963C5.05248 24.6181 4.86845 23.108 5.6093 21.9326C5.90128 21.4812 6.30861 21.1095 6.79233 20.853L11.9292 17.9942L6.74044 15.0854C5.69658 14.5914 5.02562 13.5782 5.00072 12.4581C4.97582 11.3381 5.60112 10.2981 6.62208 9.76154C7.64303 9.22496 8.88936 9.28127 9.85368 9.90755L14.8868 12.7564V6.99876C14.8868 5.34259 16.2806 4 18 4C19.7194 4 21.1132 5.34259 21.1132 6.99876V12.7564L26.1048 9.90755C27.3362 9.20107 28.9092 9.39568 29.9153 10.379C30.9214 11.3623 31.1005 12.8801 30.3492 14.0558C30.0572 14.5072 29.6499 14.8789 29.1662 15.1354L24.1123 17.9942ZM27.9932 24.4559C28.26 24.3884 28.4879 24.2214 28.6265 23.9917C28.9135 23.5217 28.7566 22.9169 28.2737 22.6323L20.0755 17.9942L28.2426 13.3461C28.6729 13.0446 28.7968 12.4784 28.5293 12.0353C28.2619 11.5922 27.6898 11.4157 27.2048 11.6268L19.0377 16.2449V6.99876C19.0377 6.4467 18.5731 5.99917 18 5.99917C17.4269 5.99917 16.9623 6.4467 16.9623 6.99876V16.2449L8.76405 11.6368C8.44369 11.4124 8.01864 11.3832 7.66849 11.5616C7.31833 11.74 7.10415 12.0949 7.11644 12.4762C7.12873 12.8576 7.36534 13.199 7.7263 13.3561L15.9245 17.9942L7.75744 22.6323C7.32706 22.9338 7.20319 23.5 7.47066 23.9431C7.73813 24.3862 8.31025 24.5627 8.79518 24.3516L16.9623 19.7435V28.9896C16.9623 29.5417 17.4269 29.9892 18 29.9892C18.5731 29.9892 19.0377 29.5417 19.0377 28.9896V19.7435L27.2048 24.3516C27.4428 24.4859 27.7265 24.5234 27.9932 24.4559Z\"/>',solid:'<path d=\"M28.4525 20.9041L23.4525 17.9941L28.3225 15.1341C28.7887 14.8776 29.1812 14.5057 29.4625 14.0541C30.1865 12.878 30.0139 11.3595 29.0444 10.3758C28.075 9.39206 26.5591 9.19737 25.3725 9.90414L20.5625 12.7541V6.99414C20.5625 5.33729 19.2194 3.99414 17.5625 3.99414C15.9057 3.99414 14.5625 5.33729 14.5625 6.99414V12.7541L9.71254 9.90414C8.78329 9.2776 7.58229 9.22126 6.59848 9.75807C5.61466 10.2949 5.0121 11.3353 5.03609 12.4558C5.06009 13.5763 5.70664 14.5899 6.71254 15.0841L11.7125 17.9941L6.76254 20.8541C6.29641 21.1107 5.9039 21.4826 5.62254 21.9341C4.90864 23.11 5.08597 24.6208 6.05277 25.5994C7.01956 26.578 8.52805 26.7737 9.71254 26.0741L14.5625 23.2341V28.8941C14.514 30.2062 15.3243 31.3974 16.5625 31.8341C17.4827 32.1595 18.5035 32.0169 19.2993 31.4519C20.0951 30.8869 20.5663 29.9701 20.5625 28.9941V23.2341L25.4125 26.0841C26.3418 26.7107 27.5428 26.767 28.5266 26.2302C29.5104 25.6934 30.113 24.653 30.089 23.5325C30.065 22.412 29.4184 21.3983 28.4125 20.9041H28.4525Z\"/>'})];export{e as asteriskIcon,L as asteriskIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,UAAU;EAACC,CAAC,GAAC,CAAC,UAAU,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,2zDAA2zD;IAACC,KAAK,EAAC;EAAw+B,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,YAAY,EAACJ,CAAC,IAAIK,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}