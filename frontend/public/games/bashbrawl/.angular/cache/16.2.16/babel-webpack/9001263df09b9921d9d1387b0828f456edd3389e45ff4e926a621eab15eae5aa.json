{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function throttle(durationSelector, config) {\n  return operate((source, subscriber) => {\n    const {\n      leading = true,\n      trailing = false\n    } = config !== null && config !== void 0 ? config : {};\n    let hasValue = false;\n    let sendValue = null;\n    let throttled = null;\n    let isComplete = false;\n    const endThrottling = () => {\n      throttled === null || throttled === void 0 ? void 0 : throttled.unsubscribe();\n      throttled = null;\n      if (trailing) {\n        send();\n        isComplete && subscriber.complete();\n      }\n    };\n    const cleanupThrottling = () => {\n      throttled = null;\n      isComplete && subscriber.complete();\n    };\n    const startThrottle = value => throttled = innerFrom(durationSelector(value)).subscribe(createOperatorSubscriber(subscriber, endThrottling, cleanupThrottling));\n    const send = () => {\n      if (hasValue) {\n        hasValue = false;\n        const value = sendValue;\n        sendValue = null;\n        subscriber.next(value);\n        !isComplete && startThrottle(value);\n      }\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      hasValue = true;\n      sendValue = value;\n      !(throttled && !throttled.closed) && (leading ? send() : startThrottle(value));\n    }, () => {\n      isComplete = true;\n      !(trailing && hasValue && throttled && !throttled.closed) && subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "innerFrom", "throttle", "durationSelector", "config", "source", "subscriber", "leading", "trailing", "hasValue", "sendValue", "throttled", "isComplete", "endThrottling", "unsubscribe", "send", "complete", "cleanupThrottling", "startThrottle", "value", "subscribe", "next", "closed"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/rxjs/dist/esm/internal/operators/throttle.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function throttle(durationSelector, config) {\n    return operate((source, subscriber) => {\n        const { leading = true, trailing = false } = config !== null && config !== void 0 ? config : {};\n        let hasValue = false;\n        let sendValue = null;\n        let throttled = null;\n        let isComplete = false;\n        const endThrottling = () => {\n            throttled === null || throttled === void 0 ? void 0 : throttled.unsubscribe();\n            throttled = null;\n            if (trailing) {\n                send();\n                isComplete && subscriber.complete();\n            }\n        };\n        const cleanupThrottling = () => {\n            throttled = null;\n            isComplete && subscriber.complete();\n        };\n        const startThrottle = (value) => (throttled = innerFrom(durationSelector(value)).subscribe(createOperatorSubscriber(subscriber, endThrottling, cleanupThrottling)));\n        const send = () => {\n            if (hasValue) {\n                hasValue = false;\n                const value = sendValue;\n                sendValue = null;\n                subscriber.next(value);\n                !isComplete && startThrottle(value);\n            }\n        };\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            hasValue = true;\n            sendValue = value;\n            !(throttled && !throttled.closed) && (leading ? send() : startThrottle(value));\n        }, () => {\n            isComplete = true;\n            !(trailing && hasValue && throttled && !throttled.closed) && subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAO,SAASC,QAAQA,CAACC,gBAAgB,EAAEC,MAAM,EAAE;EAC/C,OAAOL,OAAO,CAAC,CAACM,MAAM,EAAEC,UAAU,KAAK;IACnC,MAAM;MAAEC,OAAO,GAAG,IAAI;MAAEC,QAAQ,GAAG;IAAM,CAAC,GAAGJ,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG,CAAC,CAAC;IAC/F,IAAIK,QAAQ,GAAG,KAAK;IACpB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,UAAU,GAAG,KAAK;IACtB,MAAMC,aAAa,GAAGA,CAAA,KAAM;MACxBF,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACG,WAAW,CAAC,CAAC;MAC7EH,SAAS,GAAG,IAAI;MAChB,IAAIH,QAAQ,EAAE;QACVO,IAAI,CAAC,CAAC;QACNH,UAAU,IAAIN,UAAU,CAACU,QAAQ,CAAC,CAAC;MACvC;IACJ,CAAC;IACD,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC5BN,SAAS,GAAG,IAAI;MAChBC,UAAU,IAAIN,UAAU,CAACU,QAAQ,CAAC,CAAC;IACvC,CAAC;IACD,MAAME,aAAa,GAAIC,KAAK,IAAMR,SAAS,GAAGV,SAAS,CAACE,gBAAgB,CAACgB,KAAK,CAAC,CAAC,CAACC,SAAS,CAACpB,wBAAwB,CAACM,UAAU,EAAEO,aAAa,EAAEI,iBAAiB,CAAC,CAAE;IACnK,MAAMF,IAAI,GAAGA,CAAA,KAAM;MACf,IAAIN,QAAQ,EAAE;QACVA,QAAQ,GAAG,KAAK;QAChB,MAAMU,KAAK,GAAGT,SAAS;QACvBA,SAAS,GAAG,IAAI;QAChBJ,UAAU,CAACe,IAAI,CAACF,KAAK,CAAC;QACtB,CAACP,UAAU,IAAIM,aAAa,CAACC,KAAK,CAAC;MACvC;IACJ,CAAC;IACDd,MAAM,CAACe,SAAS,CAACpB,wBAAwB,CAACM,UAAU,EAAGa,KAAK,IAAK;MAC7DV,QAAQ,GAAG,IAAI;MACfC,SAAS,GAAGS,KAAK;MACjB,EAAER,SAAS,IAAI,CAACA,SAAS,CAACW,MAAM,CAAC,KAAKf,OAAO,GAAGQ,IAAI,CAAC,CAAC,GAAGG,aAAa,CAACC,KAAK,CAAC,CAAC;IAClF,CAAC,EAAE,MAAM;MACLP,UAAU,GAAG,IAAI;MACjB,EAAEJ,QAAQ,IAAIC,QAAQ,IAAIE,SAAS,IAAI,CAACA,SAAS,CAACW,MAAM,CAAC,IAAIhB,UAAU,CAACU,QAAQ,CAAC,CAAC;IACtF,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}