{"ast": null, "code": "var t;\n!function (t) {\n  t[t.Responsive = 0] = \"Responsive\", t[t.Top = 1] = \"Top\", t[t.Left = 2] = \"Left\", t[t.TopOrLeft = 3] = \"TopOrLeft\", t[t.Right = 4] = \"Right\", t[t.TopOrRight = 5] = \"TopOrRight\", t[t.Horizontal = 6] = \"Horizontal\", t[t.TopOrHorizontal = 7] = \"TopOrHorizontal\", t[t.Bottom = 8] = \"Bottom\", t[t.Vertical = 9] = \"Vertical\", t[t.BottomOrLeft = 10] = \"BottomOrLeft\", t[t.VerticalOrLeft = 11] = \"VerticalOrLeft\", t[t.BottomOrRight = 12] = \"BottomOrRight\", t[t.VerticalOrRight = 13] = \"VerticalOrRight\", t[t.BottomOrHorizontal = 14] = \"BottomOrHorizontal\", t[t.All = 15] = \"All\";\n}(t || (t = {}));\nexport { t as Positions };", "map": {"version": 3, "names": ["t", "Responsive", "Top", "Left", "TopOrLeft", "Right", "TopOrRight", "Horizontal", "TopOrHorizontal", "Bottom", "Vertical", "BottomOrLeft", "VerticalOrLeft", "BottomOrRight", "VerticalOrRight", "BottomOrHorizontal", "All", "Positions"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/positioning/interfaces.js"], "sourcesContent": ["var t;!function(t){t[t.Responsive=0]=\"Responsive\",t[t.Top=1]=\"Top\",t[t.Left=2]=\"Left\",t[t.TopOrLeft=3]=\"TopOrLeft\",t[t.Right=4]=\"Right\",t[t.TopOrRight=5]=\"TopOrRight\",t[t.Horizontal=6]=\"Horizontal\",t[t.TopOrHorizontal=7]=\"TopOrHorizontal\",t[t.Bottom=8]=\"Bottom\",t[t.Vertical=9]=\"Vertical\",t[t.BottomOrLeft=10]=\"BottomOrLeft\",t[t.VerticalOrLeft=11]=\"VerticalOrLeft\",t[t.BottomOrRight=12]=\"BottomOrRight\",t[t.VerticalOrRight=13]=\"VerticalOrRight\",t[t.BottomOrHorizontal=14]=\"BottomOrHorizontal\",t[t.All=15]=\"All\"}(t||(t={}));export{t as Positions};\n"], "mappings": "AAAA,IAAIA,CAAC;AAAC,CAAC,UAASA,CAAC,EAAC;EAACA,CAAC,CAACA,CAAC,CAACC,UAAU,GAAC,CAAC,CAAC,GAAC,YAAY,EAACD,CAAC,CAACA,CAAC,CAACE,GAAG,GAAC,CAAC,CAAC,GAAC,KAAK,EAACF,CAAC,CAACA,CAAC,CAACG,IAAI,GAAC,CAAC,CAAC,GAAC,MAAM,EAACH,CAAC,CAACA,CAAC,CAACI,SAAS,GAAC,CAAC,CAAC,GAAC,WAAW,EAACJ,CAAC,CAACA,CAAC,CAACK,KAAK,GAAC,CAAC,CAAC,GAAC,OAAO,EAACL,CAAC,CAACA,CAAC,CAACM,UAAU,GAAC,CAAC,CAAC,GAAC,YAAY,EAACN,CAAC,CAACA,CAAC,CAACO,UAAU,GAAC,CAAC,CAAC,GAAC,YAAY,EAACP,CAAC,CAACA,CAAC,CAACQ,eAAe,GAAC,CAAC,CAAC,GAAC,iBAAiB,EAACR,CAAC,CAACA,CAAC,CAACS,MAAM,GAAC,CAAC,CAAC,GAAC,QAAQ,EAACT,CAAC,CAACA,CAAC,CAACU,QAAQ,GAAC,CAAC,CAAC,GAAC,UAAU,EAACV,CAAC,CAACA,CAAC,CAACW,YAAY,GAAC,EAAE,CAAC,GAAC,cAAc,EAACX,CAAC,CAACA,CAAC,CAACY,cAAc,GAAC,EAAE,CAAC,GAAC,gBAAgB,EAACZ,CAAC,CAACA,CAAC,CAACa,aAAa,GAAC,EAAE,CAAC,GAAC,eAAe,EAACb,CAAC,CAACA,CAAC,CAACc,eAAe,GAAC,EAAE,CAAC,GAAC,iBAAiB,EAACd,CAAC,CAACA,CAAC,CAACe,kBAAkB,GAAC,EAAE,CAAC,GAAC,oBAAoB,EAACf,CAAC,CAACA,CAAC,CAACgB,GAAG,GAAC,EAAE,CAAC,GAAC,KAAK;AAAA,CAAC,CAAChB,CAAC,KAAGA,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC;AAAC,SAAOA,CAAC,IAAIiB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}