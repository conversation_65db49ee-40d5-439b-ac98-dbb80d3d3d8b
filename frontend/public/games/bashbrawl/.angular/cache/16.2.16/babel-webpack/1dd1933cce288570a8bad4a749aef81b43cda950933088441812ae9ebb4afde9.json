{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"first-aid\",\n  V = [\"first-aid\", C({\n    outline: '<path d=\"M32 6H23.91V4.5C23.91 3.83696 23.6466 3.20107 23.1778 2.73223C22.7089 2.26339 22.073 2 21.41 2H14.41C13.747 2 13.1111 2.26339 12.6422 2.73223C12.1734 3.20107 11.91 3.83696 11.91 4.5V6H4C3.46957 6 2.96086 6.21071 2.58579 6.58579C2.21071 6.96086 2 7.46957 2 8V28C2 28.5304 2.21071 29.0391 2.58579 29.4142C2.96086 29.7893 3.46957 30 4 30H32C32.5304 30 33.0391 29.7893 33.4142 29.4142C33.7893 29.0391 34 28.5304 34 28V8C34 7.46957 33.7893 6.96086 33.4142 6.58579C33.0391 6.21071 32.5304 6 32 6ZM13.91 4.5C13.91 4.36739 13.9627 4.24021 14.0564 4.14645C14.1502 4.05268 14.2774 4 14.41 4H21.41C21.5426 4 21.6698 4.05268 21.7636 4.14645C21.8573 4.24021 21.91 4.36739 21.91 4.5V6H13.91V4.5ZM4 28V8H32V28H4Z\"/><path d=\"M20.15 25.2H16.74C16.3952 25.2 16.0646 25.063 15.8208 24.8192C15.577 24.5754 15.44 24.2448 15.44 23.9V21.2H12.74C12.5693 21.2 12.4002 21.1664 12.2425 21.101C12.0848 21.0357 11.9415 20.94 11.8208 20.8192C11.7 20.6985 11.6043 20.5552 11.539 20.3975C11.4736 20.2398 11.44 20.0707 11.44 19.9V16.5C11.44 16.3293 11.4736 16.1602 11.539 16.0025C11.6043 15.8448 11.7 15.7015 11.8208 15.5808C11.9415 15.46 12.0848 15.3643 12.2425 15.299C12.4002 15.2336 12.5693 15.2 12.74 15.2H15.44V12.5C15.44 12.3293 15.4736 12.1602 15.539 12.0025C15.6043 11.8448 15.7 11.7015 15.8208 11.5808C15.9415 11.46 16.0848 11.3643 16.2425 11.299C16.4002 11.2336 16.5693 11.2 16.74 11.2H20.15C20.493 11.2026 20.8211 11.3408 21.0628 11.5843C21.3044 11.8278 21.44 12.1569 21.44 12.5V15.2H24.15C24.493 15.2026 24.8211 15.3408 25.0628 15.5843C25.3044 15.8278 25.44 16.1569 25.44 16.5V19.9C25.44 20.2431 25.3044 20.5722 25.0628 20.8157C24.8211 21.0592 24.493 21.1974 24.15 21.2H21.44V23.9C21.44 24.2431 21.3044 24.5722 21.0628 24.8157C20.8211 25.0592 20.493 25.1974 20.15 25.2ZM17 23.6H19.81V19.6H23.81V16.8H19.81V12.8H17V16.8H13V19.6H17V23.6Z\"/>',\n    solid: '<path d=\"M32 6H23.91V4.5C23.91 3.83696 23.6466 3.20107 23.1778 2.73223C22.7089 2.26339 22.073 2 21.41 2H14.41C13.747 2 13.1111 2.26339 12.6422 2.73223C12.1734 3.20107 11.91 3.83696 11.91 4.5V6H4C3.46957 6 2.96086 6.21071 2.58579 6.58579C2.21071 6.96086 2 7.46957 2 8V28C2 28.5304 2.21071 29.0391 2.58579 29.4142C2.96086 29.7893 3.46957 30 4 30H32C32.5304 30 33.0391 29.7893 33.4142 29.4142C33.7893 29.0391 34 28.5304 34 28V8C34 7.46957 33.7893 6.96086 33.4142 6.58579C33.0391 6.21071 32.5304 6 32 6ZM13.91 4.5C13.91 4.36739 13.9627 4.24021 14.0564 4.14645C14.1502 4.05268 14.2774 4 14.41 4H21.41C21.5426 4 21.6698 4.05268 21.7636 4.14645C21.8573 4.24021 21.91 4.36739 21.91 4.5V6H13.91V4.5ZM24.64 19.9C24.64 20.0326 24.5873 20.1598 24.4936 20.2536C24.3998 20.3473 24.2726 20.4 24.14 20.4H20.64V23.9C20.64 24.0326 20.5873 24.1598 20.4936 24.2536C20.3998 24.3473 20.2726 24.4 20.14 24.4H16.74C16.6074 24.4 16.4802 24.3473 16.3864 24.2536C16.2927 24.1598 16.24 24.0326 16.24 23.9V20.4H12.74C12.6074 20.4 12.4802 20.3473 12.3864 20.2536C12.2927 20.1598 12.24 20.0326 12.24 19.9V16.5C12.24 16.3674 12.2927 16.2402 12.3864 16.1464C12.4802 16.0527 12.6074 16 12.74 16H16.24V12.5C16.24 12.3674 16.2927 12.2402 16.3864 12.1464C16.4802 12.0527 16.6074 12 16.74 12H20.14C20.2726 12 20.3998 12.0527 20.4936 12.1464C20.5873 12.2402 20.64 12.3674 20.64 12.5V16H24.14C24.2726 16 24.3998 16.0527 24.4936 16.1464C24.5873 16.2402 24.64 16.3674 24.64 16.5V19.9Z\"/>'\n  })];\nexport { V as firstAidIcon, H as firstAidIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "V", "outline", "solid", "firstAidIcon", "firstAidIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/first-aid.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"first-aid\",V=[\"first-aid\",C({outline:'<path d=\"M32 6H23.91V4.5C23.91 3.83696 23.6466 3.20107 23.1778 2.73223C22.7089 2.26339 22.073 2 21.41 2H14.41C13.747 2 13.1111 2.26339 12.6422 2.73223C12.1734 3.20107 11.91 3.83696 11.91 4.5V6H4C3.46957 6 2.96086 6.21071 2.58579 6.58579C2.21071 6.96086 2 7.46957 2 8V28C2 28.5304 2.21071 29.0391 2.58579 29.4142C2.96086 29.7893 3.46957 30 4 30H32C32.5304 30 33.0391 29.7893 33.4142 29.4142C33.7893 29.0391 34 28.5304 34 28V8C34 7.46957 33.7893 6.96086 33.4142 6.58579C33.0391 6.21071 32.5304 6 32 6ZM13.91 4.5C13.91 4.36739 13.9627 4.24021 14.0564 4.14645C14.1502 4.05268 14.2774 4 14.41 4H21.41C21.5426 4 21.6698 4.05268 21.7636 4.14645C21.8573 4.24021 21.91 4.36739 21.91 4.5V6H13.91V4.5ZM4 28V8H32V28H4Z\"/><path d=\"M20.15 25.2H16.74C16.3952 25.2 16.0646 25.063 15.8208 24.8192C15.577 24.5754 15.44 24.2448 15.44 23.9V21.2H12.74C12.5693 21.2 12.4002 21.1664 12.2425 21.101C12.0848 21.0357 11.9415 20.94 11.8208 20.8192C11.7 20.6985 11.6043 20.5552 11.539 20.3975C11.4736 20.2398 11.44 20.0707 11.44 19.9V16.5C11.44 16.3293 11.4736 16.1602 11.539 16.0025C11.6043 15.8448 11.7 15.7015 11.8208 15.5808C11.9415 15.46 12.0848 15.3643 12.2425 15.299C12.4002 15.2336 12.5693 15.2 12.74 15.2H15.44V12.5C15.44 12.3293 15.4736 12.1602 15.539 12.0025C15.6043 11.8448 15.7 11.7015 15.8208 11.5808C15.9415 11.46 16.0848 11.3643 16.2425 11.299C16.4002 11.2336 16.5693 11.2 16.74 11.2H20.15C20.493 11.2026 20.8211 11.3408 21.0628 11.5843C21.3044 11.8278 21.44 12.1569 21.44 12.5V15.2H24.15C24.493 15.2026 24.8211 15.3408 25.0628 15.5843C25.3044 15.8278 25.44 16.1569 25.44 16.5V19.9C25.44 20.2431 25.3044 20.5722 25.0628 20.8157C24.8211 21.0592 24.493 21.1974 24.15 21.2H21.44V23.9C21.44 24.2431 21.3044 24.5722 21.0628 24.8157C20.8211 25.0592 20.493 25.1974 20.15 25.2ZM17 23.6H19.81V19.6H23.81V16.8H19.81V12.8H17V16.8H13V19.6H17V23.6Z\"/>',solid:'<path d=\"M32 6H23.91V4.5C23.91 3.83696 23.6466 3.20107 23.1778 2.73223C22.7089 2.26339 22.073 2 21.41 2H14.41C13.747 2 13.1111 2.26339 12.6422 2.73223C12.1734 3.20107 11.91 3.83696 11.91 4.5V6H4C3.46957 6 2.96086 6.21071 2.58579 6.58579C2.21071 6.96086 2 7.46957 2 8V28C2 28.5304 2.21071 29.0391 2.58579 29.4142C2.96086 29.7893 3.46957 30 4 30H32C32.5304 30 33.0391 29.7893 33.4142 29.4142C33.7893 29.0391 34 28.5304 34 28V8C34 7.46957 33.7893 6.96086 33.4142 6.58579C33.0391 6.21071 32.5304 6 32 6ZM13.91 4.5C13.91 4.36739 13.9627 4.24021 14.0564 4.14645C14.1502 4.05268 14.2774 4 14.41 4H21.41C21.5426 4 21.6698 4.05268 21.7636 4.14645C21.8573 4.24021 21.91 4.36739 21.91 4.5V6H13.91V4.5ZM24.64 19.9C24.64 20.0326 24.5873 20.1598 24.4936 20.2536C24.3998 20.3473 24.2726 20.4 24.14 20.4H20.64V23.9C20.64 24.0326 20.5873 24.1598 20.4936 24.2536C20.3998 24.3473 20.2726 24.4 20.14 24.4H16.74C16.6074 24.4 16.4802 24.3473 16.3864 24.2536C16.2927 24.1598 16.24 24.0326 16.24 23.9V20.4H12.74C12.6074 20.4 12.4802 20.3473 12.3864 20.2536C12.2927 20.1598 12.24 20.0326 12.24 19.9V16.5C12.24 16.3674 12.2927 16.2402 12.3864 16.1464C12.4802 16.0527 12.6074 16 12.74 16H16.24V12.5C16.24 12.3674 16.2927 12.2402 16.3864 12.1464C16.4802 12.0527 16.6074 12 16.74 12H20.14C20.2726 12 20.3998 12.0527 20.4936 12.1464C20.5873 12.2402 20.64 12.3674 20.64 12.5V16H24.14C24.2726 16 24.3998 16.0527 24.4936 16.1464C24.5873 16.2402 24.64 16.3674 24.64 16.5V19.9Z\"/>'})];export{V as firstAidIcon,H as firstAidIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,WAAW;EAACC,CAAC,GAAC,CAAC,WAAW,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,oyDAAoyD;IAACC,KAAK,EAAC;EAAu6C,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,YAAY,EAACJ,CAAC,IAAIK,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}