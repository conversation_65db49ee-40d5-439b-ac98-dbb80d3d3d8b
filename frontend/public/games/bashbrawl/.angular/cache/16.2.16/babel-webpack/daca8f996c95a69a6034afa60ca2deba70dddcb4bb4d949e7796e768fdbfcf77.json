{"ast": null, "code": "import _curry1 from \"./internal/_curry1.js\";\nimport _has from \"./internal/_has.js\";\nimport _isArguments from \"./internal/_isArguments.js\"; // cover IE < 9 keys issues\n\nvar hasEnumBug = ! /*#__PURE__*/\n{\n  toString: null\n}.propertyIsEnumerable('toString');\nvar nonEnumerableProps = ['constructor', 'valueOf', 'isPrototypeOf', 'toString', 'propertyIsEnumerable', 'hasOwnProperty', 'toLocaleString']; // Safari bug\n\nvar hasArgsEnumBug = /*#__PURE__*/\nfunction () {\n  'use strict';\n\n  return arguments.propertyIsEnumerable('length');\n}();\nvar contains = function contains(list, item) {\n  var idx = 0;\n  while (idx < list.length) {\n    if (list[idx] === item) {\n      return true;\n    }\n    idx += 1;\n  }\n  return false;\n};\n/**\n * Returns a list containing the names of all the enumerable own properties of\n * the supplied object.\n * Note that the order of the output array is not guaranteed to be consistent\n * across different JS platforms.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Object\n * @sig {k: v} -> [k]\n * @param {Object} obj The object to extract properties from\n * @return {Array} An array of the object's own properties.\n * @see R.keysIn, R.values, R.toPairs\n * @example\n *\n *      R.keys({a: 1, b: 2, c: 3}); //=> ['a', 'b', 'c']\n */\n\nvar keys = typeof Object.keys === 'function' && !hasArgsEnumBug ? /*#__PURE__*/\n_curry1(function keys(obj) {\n  return Object(obj) !== obj ? [] : Object.keys(obj);\n}) : /*#__PURE__*/\n_curry1(function keys(obj) {\n  if (Object(obj) !== obj) {\n    return [];\n  }\n  var prop, nIdx;\n  var ks = [];\n  var checkArgsLength = hasArgsEnumBug && _isArguments(obj);\n  for (prop in obj) {\n    if (_has(prop, obj) && (!checkArgsLength || prop !== 'length')) {\n      ks[ks.length] = prop;\n    }\n  }\n  if (hasEnumBug) {\n    nIdx = nonEnumerableProps.length - 1;\n    while (nIdx >= 0) {\n      prop = nonEnumerableProps[nIdx];\n      if (_has(prop, obj) && !contains(ks, prop)) {\n        ks[ks.length] = prop;\n      }\n      nIdx -= 1;\n    }\n  }\n  return ks;\n});\nexport default keys;", "map": {"version": 3, "names": ["_curry1", "_has", "_isArguments", "hasEnumBug", "toString", "propertyIsEnumerable", "nonEnumerableProps", "hasArgsEnumBug", "arguments", "contains", "list", "item", "idx", "length", "keys", "Object", "obj", "prop", "nIdx", "ks", "check<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/keys.js"], "sourcesContent": ["import _curry1 from \"./internal/_curry1.js\";\nimport _has from \"./internal/_has.js\";\nimport _isArguments from \"./internal/_isArguments.js\"; // cover IE < 9 keys issues\n\nvar hasEnumBug = !\n/*#__PURE__*/\n{\n  toString: null\n}.propertyIsEnumerable('toString');\nvar nonEnumerableProps = ['constructor', 'valueOf', 'isPrototypeOf', 'toString', 'propertyIsEnumerable', 'hasOwnProperty', 'toLocaleString']; // Safari bug\n\nvar hasArgsEnumBug =\n/*#__PURE__*/\nfunction () {\n  'use strict';\n\n  return arguments.propertyIsEnumerable('length');\n}();\n\nvar contains = function contains(list, item) {\n  var idx = 0;\n\n  while (idx < list.length) {\n    if (list[idx] === item) {\n      return true;\n    }\n\n    idx += 1;\n  }\n\n  return false;\n};\n/**\n * Returns a list containing the names of all the enumerable own properties of\n * the supplied object.\n * Note that the order of the output array is not guaranteed to be consistent\n * across different JS platforms.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Object\n * @sig {k: v} -> [k]\n * @param {Object} obj The object to extract properties from\n * @return {Array} An array of the object's own properties.\n * @see R.keysIn, R.values, R.toPairs\n * @example\n *\n *      R.keys({a: 1, b: 2, c: 3}); //=> ['a', 'b', 'c']\n */\n\n\nvar keys = typeof Object.keys === 'function' && !hasArgsEnumBug ?\n/*#__PURE__*/\n_curry1(function keys(obj) {\n  return Object(obj) !== obj ? [] : Object.keys(obj);\n}) :\n/*#__PURE__*/\n_curry1(function keys(obj) {\n  if (Object(obj) !== obj) {\n    return [];\n  }\n\n  var prop, nIdx;\n  var ks = [];\n\n  var checkArgsLength = hasArgsEnumBug && _isArguments(obj);\n\n  for (prop in obj) {\n    if (_has(prop, obj) && (!checkArgsLength || prop !== 'length')) {\n      ks[ks.length] = prop;\n    }\n  }\n\n  if (hasEnumBug) {\n    nIdx = nonEnumerableProps.length - 1;\n\n    while (nIdx >= 0) {\n      prop = nonEnumerableProps[nIdx];\n\n      if (_has(prop, obj) && !contains(ks, prop)) {\n        ks[ks.length] = prop;\n      }\n\n      nIdx -= 1;\n    }\n  }\n\n  return ks;\n});\nexport default keys;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,YAAY,MAAM,4BAA4B,CAAC,CAAC;;AAEvD,IAAIC,UAAU,GAAG,EACjB;AACA;EACEC,QAAQ,EAAE;AACZ,CAAC,CAACC,oBAAoB,CAAC,UAAU,CAAC;AAClC,IAAIC,kBAAkB,GAAG,CAAC,aAAa,EAAE,SAAS,EAAE,eAAe,EAAE,UAAU,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,gBAAgB,CAAC,CAAC,CAAC;;AAE9I,IAAIC,cAAc,GAClB;AACA,YAAY;EACV,YAAY;;EAEZ,OAAOC,SAAS,CAACH,oBAAoB,CAAC,QAAQ,CAAC;AACjD,CAAC,CAAC,CAAC;AAEH,IAAII,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC3C,IAAIC,GAAG,GAAG,CAAC;EAEX,OAAOA,GAAG,GAAGF,IAAI,CAACG,MAAM,EAAE;IACxB,IAAIH,IAAI,CAACE,GAAG,CAAC,KAAKD,IAAI,EAAE;MACtB,OAAO,IAAI;IACb;IAEAC,GAAG,IAAI,CAAC;EACV;EAEA,OAAO,KAAK;AACd,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,IAAIE,IAAI,GAAG,OAAOC,MAAM,CAACD,IAAI,KAAK,UAAU,IAAI,CAACP,cAAc,GAC/D;AACAP,OAAO,CAAC,SAASc,IAAIA,CAACE,GAAG,EAAE;EACzB,OAAOD,MAAM,CAACC,GAAG,CAAC,KAAKA,GAAG,GAAG,EAAE,GAAGD,MAAM,CAACD,IAAI,CAACE,GAAG,CAAC;AACpD,CAAC,CAAC,GACF;AACAhB,OAAO,CAAC,SAASc,IAAIA,CAACE,GAAG,EAAE;EACzB,IAAID,MAAM,CAACC,GAAG,CAAC,KAAKA,GAAG,EAAE;IACvB,OAAO,EAAE;EACX;EAEA,IAAIC,IAAI,EAAEC,IAAI;EACd,IAAIC,EAAE,GAAG,EAAE;EAEX,IAAIC,eAAe,GAAGb,cAAc,IAAIL,YAAY,CAACc,GAAG,CAAC;EAEzD,KAAKC,IAAI,IAAID,GAAG,EAAE;IAChB,IAAIf,IAAI,CAACgB,IAAI,EAAED,GAAG,CAAC,KAAK,CAACI,eAAe,IAAIH,IAAI,KAAK,QAAQ,CAAC,EAAE;MAC9DE,EAAE,CAACA,EAAE,CAACN,MAAM,CAAC,GAAGI,IAAI;IACtB;EACF;EAEA,IAAId,UAAU,EAAE;IACde,IAAI,GAAGZ,kBAAkB,CAACO,MAAM,GAAG,CAAC;IAEpC,OAAOK,IAAI,IAAI,CAAC,EAAE;MAChBD,IAAI,GAAGX,kBAAkB,CAACY,IAAI,CAAC;MAE/B,IAAIjB,IAAI,CAACgB,IAAI,EAAED,GAAG,CAAC,IAAI,CAACP,QAAQ,CAACU,EAAE,EAAEF,IAAI,CAAC,EAAE;QAC1CE,EAAE,CAACA,EAAE,CAACN,MAAM,CAAC,GAAGI,IAAI;MACtB;MAEAC,IAAI,IAAI,CAAC;IACX;EACF;EAEA,OAAOC,EAAE;AACX,CAAC,CAAC;AACF,eAAeL,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}