{"ast": null, "code": "import { renderIcon as o } from \"../icon.renderer.js\";\nconst a = \"bookmark\",\n  r = [\"bookmark\", o({\n    outline: '<path d=\"M26,34a2,2,0,0,1-1.41-.58L18,26.82l-6.54,6.52A2,2,0,0,1,8,31.93V4a2,2,0,0,1,2-2H26a2,2,0,0,1,2,2V32a2,2,0,0,1-2,2Zm0-2h0V4H10V31.93L18,24Z\"/>',\n    solid: '<path d=\"M26,2H10A2,2,0,0,0,8,4V31.93a2,2,0,0,0,3.42,1.41l6.54-6.52,6.63,6.6A2,2,0,0,0,28,32V4A2,2,0,0,0,26,2Z\"/>'\n  })];\nexport { r as bookmarkIcon, a as bookmarkIconName };", "map": {"version": 3, "names": ["renderIcon", "o", "a", "r", "outline", "solid", "bookmarkIcon", "bookmarkIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/bookmark.js"], "sourcesContent": ["import{renderIcon as o}from\"../icon.renderer.js\";const a=\"bookmark\",r=[\"bookmark\",o({outline:'<path d=\"M26,34a2,2,0,0,1-1.41-.58L18,26.82l-6.54,6.52A2,2,0,0,1,8,31.93V4a2,2,0,0,1,2-2H26a2,2,0,0,1,2,2V32a2,2,0,0,1-2,2Zm0-2h0V4H10V31.93L18,24Z\"/>',solid:'<path d=\"M26,2H10A2,2,0,0,0,8,4V31.93a2,2,0,0,0,3.42,1.41l6.54-6.52,6.63,6.6A2,2,0,0,0,28,32V4A2,2,0,0,0,26,2Z\"/>'})];export{r as bookmarkIcon,a as bookmarkIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,UAAU;EAACC,CAAC,GAAC,CAAC,UAAU,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,wJAAwJ;IAACC,KAAK,EAAC;EAAmH,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,YAAY,EAACJ,CAAC,IAAIK,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}