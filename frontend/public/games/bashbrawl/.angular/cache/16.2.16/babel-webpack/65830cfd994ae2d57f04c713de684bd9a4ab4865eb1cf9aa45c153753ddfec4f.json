{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst e = \"eye\",\n  o = [\"eye\", C({\n    outline: '<path d=\"M17.9976 11C14.1382 11 10.9987 14.14 10.9987 18C10.9987 21.86 14.1382 25 17.9976 25C21.857 25 24.9965 21.86 24.9965 18C24.9965 14.14 21.857 11 17.9976 11ZM17.9976 23C15.238 23 12.9983 20.76 12.9983 18C12.9983 15.24 15.238 13 17.9976 13C20.7571 13 22.9968 15.24 22.9968 18C22.9968 20.76 20.7571 23 17.9976 23ZM33.9151 17.61C31.1855 11.17 24.9365 7 17.9976 7C11.0586 7 4.80962 11.17 2.08005 17.61C1.97006 17.86 1.97006 18.14 2.08005 18.39C4.80962 24.84 11.0586 29 17.9976 29C24.9365 29 31.1855 24.83 33.9151 18.39C34.0251 18.14 34.0251 17.86 33.9151 17.61ZM17.9976 27C11.9985 27 6.58934 23.48 4.08973 18C6.58934 12.52 11.9985 9 17.9976 9C23.9966 9 29.4058 12.52 31.9054 18C29.4058 23.48 23.9966 27 17.9976 27Z\"/>',\n    solid: '<path d=\"M17.9976 11C14.1382 11 10.9987 14.14 10.9987 18C10.9987 21.86 14.1382 25 17.9976 25C21.857 25 24.9965 21.86 24.9965 18C24.9965 14.14 21.857 11 17.9976 11ZM33.9151 17.61C31.1855 11.17 24.9365 7 17.9976 7C11.0586 7 4.80962 11.17 2.08005 17.61C1.97006 17.86 1.97006 18.14 2.08005 18.39C4.80962 24.84 11.0586 29 17.9976 29C24.9365 29 31.1855 24.83 33.9151 18.39C34.0251 18.14 34.0251 17.86 33.9151 17.61ZM17.9976 27C11.9985 27 6.58934 23.48 4.08973 18C6.58934 12.52 11.9985 9 17.9976 9C23.9966 9 29.4058 12.52 31.9054 18C29.4058 23.48 23.9966 27 17.9976 27Z\"/>'\n  })];\nexport { o as eyeIcon, e as eyeIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "e", "o", "outline", "solid", "eyeIcon", "eyeIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/eye.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const e=\"eye\",o=[\"eye\",C({outline:'<path d=\"M17.9976 11C14.1382 11 10.9987 14.14 10.9987 18C10.9987 21.86 14.1382 25 17.9976 25C21.857 25 24.9965 21.86 24.9965 18C24.9965 14.14 21.857 11 17.9976 11ZM17.9976 23C15.238 23 12.9983 20.76 12.9983 18C12.9983 15.24 15.238 13 17.9976 13C20.7571 13 22.9968 15.24 22.9968 18C22.9968 20.76 20.7571 23 17.9976 23ZM33.9151 17.61C31.1855 11.17 24.9365 7 17.9976 7C11.0586 7 4.80962 11.17 2.08005 17.61C1.97006 17.86 1.97006 18.14 2.08005 18.39C4.80962 24.84 11.0586 29 17.9976 29C24.9365 29 31.1855 24.83 33.9151 18.39C34.0251 18.14 34.0251 17.86 33.9151 17.61ZM17.9976 27C11.9985 27 6.58934 23.48 4.08973 18C6.58934 12.52 11.9985 9 17.9976 9C23.9966 9 29.4058 12.52 31.9054 18C29.4058 23.48 23.9966 27 17.9976 27Z\"/>',solid:'<path d=\"M17.9976 11C14.1382 11 10.9987 14.14 10.9987 18C10.9987 21.86 14.1382 25 17.9976 25C21.857 25 24.9965 21.86 24.9965 18C24.9965 14.14 21.857 11 17.9976 11ZM33.9151 17.61C31.1855 11.17 24.9365 7 17.9976 7C11.0586 7 4.80962 11.17 2.08005 17.61C1.97006 17.86 1.97006 18.14 2.08005 18.39C4.80962 24.84 11.0586 29 17.9976 29C24.9365 29 31.1855 24.83 33.9151 18.39C34.0251 18.14 34.0251 17.86 33.9151 17.61ZM17.9976 27C11.9985 27 6.58934 23.48 4.08973 18C6.58934 12.52 11.9985 9 17.9976 9C23.9966 9 29.4058 12.52 31.9054 18C29.4058 23.48 23.9966 27 17.9976 27Z\"/>'})];export{o as eyeIcon,e as eyeIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,CAAC,KAAK,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,itBAAitB;IAACC,KAAK,EAAC;EAAujB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,OAAO,EAACJ,CAAC,IAAIK,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}