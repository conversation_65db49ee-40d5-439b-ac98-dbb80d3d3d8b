{"ast": null, "code": "import { coerceElement, coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { debounceTime } from 'rxjs/operators';\n\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n  create(callback) {\n    return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n  }\n  static {\n    this.ɵfac = function MutationObserverFactory_Factory(t) {\n      return new (t || MutationObserverFactory)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MutationObserverFactory,\n      factory: MutationObserverFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MutationObserverFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n  constructor(_mutationObserverFactory) {\n    this._mutationObserverFactory = _mutationObserverFactory;\n    /** Keeps track of the existing MutationObservers so they can be reused. */\n    this._observedElements = new Map();\n  }\n  ngOnDestroy() {\n    this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n  }\n  observe(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    return new Observable(observer => {\n      const stream = this._observeElement(element);\n      const subscription = stream.subscribe(observer);\n      return () => {\n        subscription.unsubscribe();\n        this._unobserveElement(element);\n      };\n    });\n  }\n  /**\n   * Observes the given element by using the existing MutationObserver if available, or creating a\n   * new one if not.\n   */\n  _observeElement(element) {\n    if (!this._observedElements.has(element)) {\n      const stream = new Subject();\n      const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n      if (observer) {\n        observer.observe(element, {\n          characterData: true,\n          childList: true,\n          subtree: true\n        });\n      }\n      this._observedElements.set(element, {\n        observer,\n        stream,\n        count: 1\n      });\n    } else {\n      this._observedElements.get(element).count++;\n    }\n    return this._observedElements.get(element).stream;\n  }\n  /**\n   * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n   * observing this element.\n   */\n  _unobserveElement(element) {\n    if (this._observedElements.has(element)) {\n      this._observedElements.get(element).count--;\n      if (!this._observedElements.get(element).count) {\n        this._cleanupObserver(element);\n      }\n    }\n  }\n  /** Clean up the underlying MutationObserver for the specified element. */\n  _cleanupObserver(element) {\n    if (this._observedElements.has(element)) {\n      const {\n        observer,\n        stream\n      } = this._observedElements.get(element);\n      if (observer) {\n        observer.disconnect();\n      }\n      stream.complete();\n      this._observedElements.delete(element);\n    }\n  }\n  static {\n    this.ɵfac = function ContentObserver_Factory(t) {\n      return new (t || ContentObserver)(i0.ɵɵinject(MutationObserverFactory));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ContentObserver,\n      factory: ContentObserver.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContentObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: MutationObserverFactory\n    }];\n  }, null);\n})();\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n  /**\n   * Whether observing content is disabled. This option can be used\n   * to disconnect the underlying MutationObserver until it is needed.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    this._disabled ? this._unsubscribe() : this._subscribe();\n  }\n  /** Debounce interval for emitting the changes. */\n  get debounce() {\n    return this._debounce;\n  }\n  set debounce(value) {\n    this._debounce = coerceNumberProperty(value);\n    this._subscribe();\n  }\n  constructor(_contentObserver, _elementRef, _ngZone) {\n    this._contentObserver = _contentObserver;\n    this._elementRef = _elementRef;\n    this._ngZone = _ngZone;\n    /** Event emitted for each change in the element's content. */\n    this.event = new EventEmitter();\n    this._disabled = false;\n    this._currentSubscription = null;\n  }\n  ngAfterContentInit() {\n    if (!this._currentSubscription && !this.disabled) {\n      this._subscribe();\n    }\n  }\n  ngOnDestroy() {\n    this._unsubscribe();\n  }\n  _subscribe() {\n    this._unsubscribe();\n    const stream = this._contentObserver.observe(this._elementRef);\n    // TODO(mmalerba): We shouldn't be emitting on this @Output() outside the zone.\n    // Consider brining it back inside the zone next time we're making breaking changes.\n    // Bringing it back inside can cause things like infinite change detection loops and changed\n    // after checked errors if people's code isn't handling it properly.\n    this._ngZone.runOutsideAngular(() => {\n      this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n    });\n  }\n  _unsubscribe() {\n    this._currentSubscription?.unsubscribe();\n  }\n  static {\n    this.ɵfac = function CdkObserveContent_Factory(t) {\n      return new (t || CdkObserveContent)(i0.ɵɵdirectiveInject(ContentObserver), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkObserveContent,\n      selectors: [[\"\", \"cdkObserveContent\", \"\"]],\n      inputs: {\n        disabled: [\"cdkObserveContentDisabled\", \"disabled\"],\n        debounce: \"debounce\"\n      },\n      outputs: {\n        event: \"cdkObserveContent\"\n      },\n      exportAs: [\"cdkObserveContent\"]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkObserveContent, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkObserveContent]',\n      exportAs: 'cdkObserveContent'\n    }]\n  }], function () {\n    return [{\n      type: ContentObserver\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    event: [{\n      type: Output,\n      args: ['cdkObserveContent']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['cdkObserveContentDisabled']\n    }],\n    debounce: [{\n      type: Input\n    }]\n  });\n})();\nclass ObserversModule {\n  static {\n    this.ɵfac = function ObserversModule_Factory(t) {\n      return new (t || ObserversModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ObserversModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MutationObserverFactory]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ObserversModule, [{\n    type: NgModule,\n    args: [{\n      exports: [CdkObserveContent],\n      declarations: [CdkObserveContent],\n      providers: [MutationObserverFactory]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };", "map": {"version": 3, "names": ["coerceElement", "coerceBooleanProperty", "coerceNumberProperty", "i0", "Injectable", "EventEmitter", "Directive", "Output", "Input", "NgModule", "Observable", "Subject", "debounceTime", "MutationObserverFactory", "create", "callback", "MutationObserver", "ɵfac", "MutationObserverFactory_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "ContentObserver", "constructor", "_mutationObserverFactory", "_observedElements", "Map", "ngOnDestroy", "for<PERSON>ach", "_", "element", "_cleanupObserver", "observe", "elementOrRef", "observer", "stream", "_observeElement", "subscription", "subscribe", "unsubscribe", "_unobserveElement", "has", "mutations", "next", "characterData", "childList", "subtree", "set", "count", "get", "disconnect", "complete", "delete", "ContentObserver_Factory", "ɵɵinject", "CdkObserveContent", "disabled", "_disabled", "value", "_unsubscribe", "_subscribe", "debounce", "_debounce", "_contentObserver", "_elementRef", "_ngZone", "event", "_currentSubscription", "ngAfterContentInit", "runOutsideAngular", "pipe", "CdkObserveContent_Factory", "ɵɵdirectiveInject", "ElementRef", "NgZone", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "outputs", "exportAs", "selector", "ObserversModule", "ObserversModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "exports", "declarations"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@angular/cdk/fesm2022/observers.mjs"], "sourcesContent": ["import { coerceElement, coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { debounceTime } from 'rxjs/operators';\n\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n    create(callback) {\n        return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MutationObserverFactory, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MutationObserverFactory, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MutationObserverFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n    constructor(_mutationObserverFactory) {\n        this._mutationObserverFactory = _mutationObserverFactory;\n        /** Keeps track of the existing MutationObservers so they can be reused. */\n        this._observedElements = new Map();\n    }\n    ngOnDestroy() {\n        this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n    }\n    observe(elementOrRef) {\n        const element = coerceElement(elementOrRef);\n        return new Observable((observer) => {\n            const stream = this._observeElement(element);\n            const subscription = stream.subscribe(observer);\n            return () => {\n                subscription.unsubscribe();\n                this._unobserveElement(element);\n            };\n        });\n    }\n    /**\n     * Observes the given element by using the existing MutationObserver if available, or creating a\n     * new one if not.\n     */\n    _observeElement(element) {\n        if (!this._observedElements.has(element)) {\n            const stream = new Subject();\n            const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n            if (observer) {\n                observer.observe(element, {\n                    characterData: true,\n                    childList: true,\n                    subtree: true,\n                });\n            }\n            this._observedElements.set(element, { observer, stream, count: 1 });\n        }\n        else {\n            this._observedElements.get(element).count++;\n        }\n        return this._observedElements.get(element).stream;\n    }\n    /**\n     * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n     * observing this element.\n     */\n    _unobserveElement(element) {\n        if (this._observedElements.has(element)) {\n            this._observedElements.get(element).count--;\n            if (!this._observedElements.get(element).count) {\n                this._cleanupObserver(element);\n            }\n        }\n    }\n    /** Clean up the underlying MutationObserver for the specified element. */\n    _cleanupObserver(element) {\n        if (this._observedElements.has(element)) {\n            const { observer, stream } = this._observedElements.get(element);\n            if (observer) {\n                observer.disconnect();\n            }\n            stream.complete();\n            this._observedElements.delete(element);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ContentObserver, deps: [{ token: MutationObserverFactory }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ContentObserver, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ContentObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: MutationObserverFactory }]; } });\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n    /**\n     * Whether observing content is disabled. This option can be used\n     * to disconnect the underlying MutationObserver until it is needed.\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        this._disabled ? this._unsubscribe() : this._subscribe();\n    }\n    /** Debounce interval for emitting the changes. */\n    get debounce() {\n        return this._debounce;\n    }\n    set debounce(value) {\n        this._debounce = coerceNumberProperty(value);\n        this._subscribe();\n    }\n    constructor(_contentObserver, _elementRef, _ngZone) {\n        this._contentObserver = _contentObserver;\n        this._elementRef = _elementRef;\n        this._ngZone = _ngZone;\n        /** Event emitted for each change in the element's content. */\n        this.event = new EventEmitter();\n        this._disabled = false;\n        this._currentSubscription = null;\n    }\n    ngAfterContentInit() {\n        if (!this._currentSubscription && !this.disabled) {\n            this._subscribe();\n        }\n    }\n    ngOnDestroy() {\n        this._unsubscribe();\n    }\n    _subscribe() {\n        this._unsubscribe();\n        const stream = this._contentObserver.observe(this._elementRef);\n        // TODO(mmalerba): We shouldn't be emitting on this @Output() outside the zone.\n        // Consider brining it back inside the zone next time we're making breaking changes.\n        // Bringing it back inside can cause things like infinite change detection loops and changed\n        // after checked errors if people's code isn't handling it properly.\n        this._ngZone.runOutsideAngular(() => {\n            this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n        });\n    }\n    _unsubscribe() {\n        this._currentSubscription?.unsubscribe();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkObserveContent, deps: [{ token: ContentObserver }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: { disabled: [\"cdkObserveContentDisabled\", \"disabled\"], debounce: \"debounce\" }, outputs: { event: \"cdkObserveContent\" }, exportAs: [\"cdkObserveContent\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkObserveContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkObserveContent]',\n                    exportAs: 'cdkObserveContent',\n                }]\n        }], ctorParameters: function () { return [{ type: ContentObserver }, { type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { event: [{\n                type: Output,\n                args: ['cdkObserveContent']\n            }], disabled: [{\n                type: Input,\n                args: ['cdkObserveContentDisabled']\n            }], debounce: [{\n                type: Input\n            }] } });\nclass ObserversModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ObserversModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: ObserversModule, declarations: [CdkObserveContent], exports: [CdkObserveContent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ObserversModule, providers: [MutationObserverFactory] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: ObserversModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkObserveContent],\n                    declarations: [CdkObserveContent],\n                    providers: [MutationObserverFactory],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,qBAAqB,EAAEC,oBAAoB,QAAQ,uBAAuB;AAClG,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC5F,SAASC,UAAU,EAAEC,OAAO,QAAQ,MAAM;AAC1C,SAASC,YAAY,QAAQ,gBAAgB;;AAE7C;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,CAAC;EAC1BC,MAAMA,CAACC,QAAQ,EAAE;IACb,OAAO,OAAOC,gBAAgB,KAAK,WAAW,GAAG,IAAI,GAAG,IAAIA,gBAAgB,CAACD,QAAQ,CAAC;EAC1F;EACA;IAAS,IAAI,CAACE,IAAI,YAAAC,gCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFN,uBAAuB;IAAA,CAAoD;EAAE;EACvL;IAAS,IAAI,CAACO,KAAK,kBAD6EjB,EAAE,CAAAkB,kBAAA;MAAAC,KAAA,EACYT,uBAAuB;MAAAU,OAAA,EAAvBV,uBAAuB,CAAAI,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAClK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGtB,EAAE,CAAAuB,iBAAA,CAGXb,uBAAuB,EAAc,CAAC;IACrHc,IAAI,EAAEvB,UAAU;IAChBwB,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA,MAAMK,eAAe,CAAC;EAClBC,WAAWA,CAACC,wBAAwB,EAAE;IAClC,IAAI,CAACA,wBAAwB,GAAGA,wBAAwB;IACxD;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACtC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,iBAAiB,CAACG,OAAO,CAAC,CAACC,CAAC,EAAEC,OAAO,KAAK,IAAI,CAACC,gBAAgB,CAACD,OAAO,CAAC,CAAC;EAClF;EACAE,OAAOA,CAACC,YAAY,EAAE;IAClB,MAAMH,OAAO,GAAGrC,aAAa,CAACwC,YAAY,CAAC;IAC3C,OAAO,IAAI9B,UAAU,CAAE+B,QAAQ,IAAK;MAChC,MAAMC,MAAM,GAAG,IAAI,CAACC,eAAe,CAACN,OAAO,CAAC;MAC5C,MAAMO,YAAY,GAAGF,MAAM,CAACG,SAAS,CAACJ,QAAQ,CAAC;MAC/C,OAAO,MAAM;QACTG,YAAY,CAACE,WAAW,CAAC,CAAC;QAC1B,IAAI,CAACC,iBAAiB,CAACV,OAAO,CAAC;MACnC,CAAC;IACL,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIM,eAAeA,CAACN,OAAO,EAAE;IACrB,IAAI,CAAC,IAAI,CAACL,iBAAiB,CAACgB,GAAG,CAACX,OAAO,CAAC,EAAE;MACtC,MAAMK,MAAM,GAAG,IAAI/B,OAAO,CAAC,CAAC;MAC5B,MAAM8B,QAAQ,GAAG,IAAI,CAACV,wBAAwB,CAACjB,MAAM,CAACmC,SAAS,IAAIP,MAAM,CAACQ,IAAI,CAACD,SAAS,CAAC,CAAC;MAC1F,IAAIR,QAAQ,EAAE;QACVA,QAAQ,CAACF,OAAO,CAACF,OAAO,EAAE;UACtBc,aAAa,EAAE,IAAI;UACnBC,SAAS,EAAE,IAAI;UACfC,OAAO,EAAE;QACb,CAAC,CAAC;MACN;MACA,IAAI,CAACrB,iBAAiB,CAACsB,GAAG,CAACjB,OAAO,EAAE;QAAEI,QAAQ;QAAEC,MAAM;QAAEa,KAAK,EAAE;MAAE,CAAC,CAAC;IACvE,CAAC,MACI;MACD,IAAI,CAACvB,iBAAiB,CAACwB,GAAG,CAACnB,OAAO,CAAC,CAACkB,KAAK,EAAE;IAC/C;IACA,OAAO,IAAI,CAACvB,iBAAiB,CAACwB,GAAG,CAACnB,OAAO,CAAC,CAACK,MAAM;EACrD;EACA;AACJ;AACA;AACA;EACIK,iBAAiBA,CAACV,OAAO,EAAE;IACvB,IAAI,IAAI,CAACL,iBAAiB,CAACgB,GAAG,CAACX,OAAO,CAAC,EAAE;MACrC,IAAI,CAACL,iBAAiB,CAACwB,GAAG,CAACnB,OAAO,CAAC,CAACkB,KAAK,EAAE;MAC3C,IAAI,CAAC,IAAI,CAACvB,iBAAiB,CAACwB,GAAG,CAACnB,OAAO,CAAC,CAACkB,KAAK,EAAE;QAC5C,IAAI,CAACjB,gBAAgB,CAACD,OAAO,CAAC;MAClC;IACJ;EACJ;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,IAAI,CAACL,iBAAiB,CAACgB,GAAG,CAACX,OAAO,CAAC,EAAE;MACrC,MAAM;QAAEI,QAAQ;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACV,iBAAiB,CAACwB,GAAG,CAACnB,OAAO,CAAC;MAChE,IAAII,QAAQ,EAAE;QACVA,QAAQ,CAACgB,UAAU,CAAC,CAAC;MACzB;MACAf,MAAM,CAACgB,QAAQ,CAAC,CAAC;MACjB,IAAI,CAAC1B,iBAAiB,CAAC2B,MAAM,CAACtB,OAAO,CAAC;IAC1C;EACJ;EACA;IAAS,IAAI,CAACpB,IAAI,YAAA2C,wBAAAzC,CAAA;MAAA,YAAAA,CAAA,IAAwFU,eAAe,EAzEzB1B,EAAE,CAAA0D,QAAA,CAyEyChD,uBAAuB;IAAA,CAA6C;EAAE;EACjN;IAAS,IAAI,CAACO,KAAK,kBA1E6EjB,EAAE,CAAAkB,kBAAA;MAAAC,KAAA,EA0EYO,eAAe;MAAAN,OAAA,EAAfM,eAAe,CAAAZ,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAC1J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5EoGtB,EAAE,CAAAuB,iBAAA,CA4EXG,eAAe,EAAc,CAAC;IAC7GF,IAAI,EAAEvB,UAAU;IAChBwB,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAEd;IAAwB,CAAC,CAAC;EAAE,CAAC;AAAA;AACvF;AACA;AACA;AACA;AACA,MAAMiD,iBAAiB,CAAC;EACpB;AACJ;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAG/D,qBAAqB,CAACgE,KAAK,CAAC;IAC7C,IAAI,CAACD,SAAS,GAAG,IAAI,CAACE,YAAY,CAAC,CAAC,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;EAC5D;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACH,KAAK,EAAE;IAChB,IAAI,CAACI,SAAS,GAAGnE,oBAAoB,CAAC+D,KAAK,CAAC;IAC5C,IAAI,CAACE,UAAU,CAAC,CAAC;EACrB;EACArC,WAAWA,CAACwC,gBAAgB,EAAEC,WAAW,EAAEC,OAAO,EAAE;IAChD,IAAI,CAACF,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACC,KAAK,GAAG,IAAIpE,YAAY,CAAC,CAAC;IAC/B,IAAI,CAAC2D,SAAS,GAAG,KAAK;IACtB,IAAI,CAACU,oBAAoB,GAAG,IAAI;EACpC;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACD,oBAAoB,IAAI,CAAC,IAAI,CAACX,QAAQ,EAAE;MAC9C,IAAI,CAACI,UAAU,CAAC,CAAC;IACrB;EACJ;EACAjC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgC,YAAY,CAAC,CAAC;EACvB;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACD,YAAY,CAAC,CAAC;IACnB,MAAMxB,MAAM,GAAG,IAAI,CAAC4B,gBAAgB,CAAC/B,OAAO,CAAC,IAAI,CAACgC,WAAW,CAAC;IAC9D;IACA;IACA;IACA;IACA,IAAI,CAACC,OAAO,CAACI,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAACF,oBAAoB,GAAG,CAAC,IAAI,CAACN,QAAQ,GAAG1B,MAAM,CAACmC,IAAI,CAACjE,YAAY,CAAC,IAAI,CAACwD,QAAQ,CAAC,CAAC,GAAG1B,MAAM,EAAEG,SAAS,CAAC,IAAI,CAAC4B,KAAK,CAAC;IACzH,CAAC,CAAC;EACN;EACAP,YAAYA,CAAA,EAAG;IACX,IAAI,CAACQ,oBAAoB,EAAE5B,WAAW,CAAC,CAAC;EAC5C;EACA;IAAS,IAAI,CAAC7B,IAAI,YAAA6D,0BAAA3D,CAAA;MAAA,YAAAA,CAAA,IAAwF2C,iBAAiB,EAvI3B3D,EAAE,CAAA4E,iBAAA,CAuI2ClD,eAAe,GAvI5D1B,EAAE,CAAA4E,iBAAA,CAuIuE5E,EAAE,CAAC6E,UAAU,GAvItF7E,EAAE,CAAA4E,iBAAA,CAuIiG5E,EAAE,CAAC8E,MAAM;IAAA,CAA4C;EAAE;EAC1P;IAAS,IAAI,CAACC,IAAI,kBAxI8E/E,EAAE,CAAAgF,iBAAA;MAAAxD,IAAA,EAwIJmC,iBAAiB;MAAAsB,SAAA;MAAAC,MAAA;QAAAtB,QAAA;QAAAK,QAAA;MAAA;MAAAkB,OAAA;QAAAb,KAAA;MAAA;MAAAc,QAAA;IAAA,EAAmN;EAAE;AACxU;AACA;EAAA,QAAA9D,SAAA,oBAAAA,SAAA,KA1IoGtB,EAAE,CAAAuB,iBAAA,CA0IXoC,iBAAiB,EAAc,CAAC;IAC/GnC,IAAI,EAAErB,SAAS;IACfsB,IAAI,EAAE,CAAC;MACC4D,QAAQ,EAAE,qBAAqB;MAC/BD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE5D,IAAI,EAAEE;IAAgB,CAAC,EAAE;MAAEF,IAAI,EAAExB,EAAE,CAAC6E;IAAW,CAAC,EAAE;MAAErD,IAAI,EAAExB,EAAE,CAAC8E;IAAO,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAER,KAAK,EAAE,CAAC;MACzI9C,IAAI,EAAEpB,MAAM;MACZqB,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEmC,QAAQ,EAAE,CAAC;MACXpC,IAAI,EAAEnB,KAAK;MACXoB,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAEwC,QAAQ,EAAE,CAAC;MACXzC,IAAI,EAAEnB;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMiF,eAAe,CAAC;EAClB;IAAS,IAAI,CAACxE,IAAI,YAAAyE,wBAAAvE,CAAA;MAAA,YAAAA,CAAA,IAAwFsE,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAACE,IAAI,kBA3J8ExF,EAAE,CAAAyF,gBAAA;MAAAjE,IAAA,EA2JS8D;IAAe,EAAoE;EAAE;EAChM;IAAS,IAAI,CAACI,IAAI,kBA5J8E1F,EAAE,CAAA2F,gBAAA;MAAAC,SAAA,EA4JqC,CAAClF,uBAAuB;IAAC,EAAG;EAAE;AACzK;AACA;EAAA,QAAAY,SAAA,oBAAAA,SAAA,KA9JoGtB,EAAE,CAAAuB,iBAAA,CA8JX+D,eAAe,EAAc,CAAC;IAC7G9D,IAAI,EAAElB,QAAQ;IACdmB,IAAI,EAAE,CAAC;MACCoE,OAAO,EAAE,CAAClC,iBAAiB,CAAC;MAC5BmC,YAAY,EAAE,CAACnC,iBAAiB,CAAC;MACjCiC,SAAS,EAAE,CAAClF,uBAAuB;IACvC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASiD,iBAAiB,EAAEjC,eAAe,EAAEhB,uBAAuB,EAAE4E,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}