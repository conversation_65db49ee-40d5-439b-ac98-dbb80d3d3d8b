{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"archive\",\n  V = [\"archive\", C({\n    outline: '<path d=\"M14 18H6V14H10C9.57 13.47 9.33 12.81 9.32 12.13V12H5.5C4.67 12 4 12.67 4 13.5V20H16L14 18ZM18 19.18L24.38 12.83C24.7 12.6 24.85 12.21 24.77 11.82C24.69 11.44 24.4 11.13 24.02 11.05C23.64 10.96 23.24 11.1 23 11.41L19 15.36V3C19 2.45 18.55 2 18 2C17.45 2 17 2.45 17 3V15.4L13 11.45C12.61 11.06 11.97 11.06 11.58 11.45C11.19 11.84 11.19 12.48 11.58 12.87L17.99 19.18H18ZM14 24C14 24.55 14.45 25 15 25H21C21.55 25 22 24.55 22 24C22 23.45 21.55 23 21 23H15C14.45 23 14 23.45 14 24ZM30.5 12H26.66V12.13C26.66 12.81 26.43 13.47 26 14H30V18H22L20 20H32V13.5C32 12.67 31.33 12 30.5 12ZM30 32H6V22H4V32C4 33.1 4.9 34 6 34H30C31.1 34 32 33.1 32 32V22H30V32Z\"/>',\n    solid: '<path d=\"M18 19.18L24.38 12.83C24.7 12.6 24.85 12.21 24.77 11.82C24.69 11.44 24.4 11.13 24.02 11.05C23.64 10.96 23.24 11.1 23 11.41L19 15.36V3C19 2.45 18.55 2 18 2C17.45 2 17 2.45 17 3V15.4L13 11.45C12.61 11.06 11.97 11.06 11.58 11.45C11.19 11.84 11.19 12.48 11.58 12.87L17.99 19.18H18ZM30.5 12H26.66V12.13C26.66 12.93 26.34 13.69 25.78 14.25L22 18H32V13.5C32 12.67 31.33 12 30.5 12ZM10.2 14.25C9.64 13.69 9.32 12.93 9.32 12.13V12H5.5C4.67 12 4 12.67 4 13.5V18H14L10.2 14.25ZM19.41 20.6L18 22L16.59 20.6L16 20H4V32C4 33.1 4.9 34 6 34H30C31.1 34 32 33.1 32 32V20H20L19.41 20.6ZM22 24C22 24.55 21.55 25 21 25H15C14.45 25 14 24.55 14 24C14 23.45 14.45 23 15 23H21C21.55 23 22 23.45 22 24Z\"/>'\n  })];\nexport { V as archiveIcon, H as archiveIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "V", "outline", "solid", "archiveIcon", "archiveIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/archive.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"archive\",V=[\"archive\",C({outline:'<path d=\"M14 18H6V14H10C9.57 13.47 9.33 12.81 9.32 12.13V12H5.5C4.67 12 4 12.67 4 13.5V20H16L14 18ZM18 19.18L24.38 12.83C24.7 12.6 24.85 12.21 24.77 11.82C24.69 11.44 24.4 11.13 24.02 11.05C23.64 10.96 23.24 11.1 23 11.41L19 15.36V3C19 2.45 18.55 2 18 2C17.45 2 17 2.45 17 3V15.4L13 11.45C12.61 11.06 11.97 11.06 11.58 11.45C11.19 11.84 11.19 12.48 11.58 12.87L17.99 19.18H18ZM14 24C14 24.55 14.45 25 15 25H21C21.55 25 22 24.55 22 24C22 23.45 21.55 23 21 23H15C14.45 23 14 23.45 14 24ZM30.5 12H26.66V12.13C26.66 12.81 26.43 13.47 26 14H30V18H22L20 20H32V13.5C32 12.67 31.33 12 30.5 12ZM30 32H6V22H4V32C4 33.1 4.9 34 6 34H30C31.1 34 32 33.1 32 32V22H30V32Z\"/>',solid:'<path d=\"M18 19.18L24.38 12.83C24.7 12.6 24.85 12.21 24.77 11.82C24.69 11.44 24.4 11.13 24.02 11.05C23.64 10.96 23.24 11.1 23 11.41L19 15.36V3C19 2.45 18.55 2 18 2C17.45 2 17 2.45 17 3V15.4L13 11.45C12.61 11.06 11.97 11.06 11.58 11.45C11.19 11.84 11.19 12.48 11.58 12.87L17.99 19.18H18ZM30.5 12H26.66V12.13C26.66 12.93 26.34 13.69 25.78 14.25L22 18H32V13.5C32 12.67 31.33 12 30.5 12ZM10.2 14.25C9.64 13.69 9.32 12.93 9.32 12.13V12H5.5C4.67 12 4 12.67 4 13.5V18H14L10.2 14.25ZM19.41 20.6L18 22L16.59 20.6L16 20H4V32C4 33.1 4.9 34 6 34H30C31.1 34 32 33.1 32 32V20H20L19.41 20.6ZM22 24C22 24.55 21.55 25 21 25H15C14.45 25 14 24.55 14 24C14 23.45 14.45 23 15 23H21C21.55 23 22 23.45 22 24Z\"/>'})];export{V as archiveIcon,H as archiveIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,SAAS;EAACC,CAAC,GAAC,CAAC,SAAS,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,opBAAopB;IAACC,KAAK,EAAC;EAAkrB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,WAAW,EAACJ,CAAC,IAAIK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}