{"ast": null, "code": "import { renderIcon as n } from \"../icon.renderer.js\";\nconst o = \"minus\",\n  r = [\"minus\", n({\n    outline: '<path d=\"M26 17H10C9.44772 17 9 17.4477 9 18C9 18.5523 9.44772 19 10 19H26C26.5523 19 27 18.5523 27 18C27 17.4477 26.5523 17 26 17Z\"/>'\n  })];\nexport { r as minusIcon, o as minusIconName };", "map": {"version": 3, "names": ["renderIcon", "n", "o", "r", "outline", "minusIcon", "minusIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/minus.js"], "sourcesContent": ["import{renderIcon as n}from\"../icon.renderer.js\";const o=\"minus\",r=[\"minus\",n({outline:'<path d=\"M26 17H10C9.44772 17 9 17.4477 9 18C9 18.5523 9.44772 19 10 19H26C26.5523 19 27 18.5523 27 18C27 17.4477 26.5523 17 26 17Z\"/>'})];export{r as minusIcon,o as minusIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,OAAO;EAACC,CAAC,GAAC,CAAC,OAAO,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAwI,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,SAAS,EAACH,CAAC,IAAII,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}