{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"network-settings\",\n  V = [\"network-settings\", C({\n    outline: '<path d=\"M24.83 21.08C22.71 21.1 21.01 22.84 21.03 24.96C21.05 27.08 22.78 28.78 24.9 28.76C27.02 28.74 28.72 27.01 28.71 24.89C28.68 22.77 26.95 21.07 24.83 21.08ZM26.44 26.5C26.01 26.93 25.43 27.16 24.83 27.16C23.6 27.14 22.62 26.13 22.63 24.9C22.63 23.67 23.63 22.68 24.86 22.68C26.09 22.68 27.09 23.66 27.11 24.89C27.11 25.49 26.87 26.08 26.44 26.5ZM33.3 22.93L31.74 22.47C31.67 22.26 31.58 22.05 31.48 21.84L32.2 20.51C32.43 20.07 32.35 19.52 31.99 19.17L30.65 17.85C30.3 17.5 29.75 17.41 29.31 17.65L27.97 18.36C27.75 18.26 27.53 18.16 27.3 18.08L26.83 16.6C26.67 16.14 26.23 15.84 25.75 15.84H23.83C23.33 15.84 22.9 16.17 22.75 16.64L22.31 18.07C22.08 18.15 21.85 18.25 21.63 18.35L20.31 17.65C19.87 17.42 19.33 17.49 18.98 17.84L17.61 19.15C17.25 19.5 17.16 20.05 17.4 20.5L18.1 21.78C17.99 21.99 17.9 22.21 17.82 22.43L16.41 22.89C15.93 23.03 15.6 23.48 15.6 23.98V25.85C15.62 26.34 15.95 26.75 16.42 26.89L17.89 27.33C17.97 27.54 18.06 27.76 18.17 27.97L17.45 29.32C17.21 29.76 17.29 30.31 17.65 30.67L18.99 31.99C19.34 32.34 19.89 32.42 20.33 32.19L21.7 31.47C21.91 31.56 22.12 31.65 22.33 31.73L22.77 33.2C22.92 33.68 23.36 34 23.86 34H25.76C26.25 34 26.68 33.67 26.83 33.2L27.27 31.73C27.48 31.66 27.69 31.57 27.89 31.48L29.27 32.21C29.71 32.44 30.25 32.36 30.6 32.01L31.94 30.69C32.3 30.34 32.39 29.79 32.15 29.34L31.42 28C31.51 27.8 31.6 27.6 31.67 27.4L33.17 26.96C33.65 26.83 33.99 26.39 34 25.89V24.03C34.02 23.55 33.74 23.11 33.3 22.93ZM30.4 26.11L30.28 26.53C30.17 26.91 30.02 27.27 29.83 27.62L29.62 28L30.62 29.79L29.76 30.63L27.94 29.63L27.57 29.83C27.21 30.02 26.84 30.18 26.45 30.29L26.03 30.41L25.44 32.41H24.21L23.62 30.46L23.2 30.34C22.81 30.23 22.43 30.08 22.07 29.89L21.7 29.69L19.89 30.69L19.03 29.84L20.03 28.02L19.81 27.64C19.61 27.28 19.44 26.9 19.32 26.51L19.19 26.1L17.24 25.52V24.31L19.18 23.73L19.3 23.32C19.42 22.92 19.58 22.54 19.79 22.18L20.01 21.79L19.01 20.06L19.88 19.22L21.65 20.16L22.03 19.95C22.4 19.74 22.79 19.58 23.2 19.46L23.61 19.34L24.2 17.43H25.43L26.01 19.33L26.42 19.45C26.82 19.57 27.21 19.73 27.58 19.93L27.96 20.14L29.76 19.19L30.62 20.04L29.62 21.81L29.83 22.19C30.03 22.55 30.19 22.93 30.3 23.32L30.42 23.74L32.35 24.31L32.4 25.53L30.4 26.11ZM10.19 26.47L10.47 26.41C11.28 26.14 11.88 25.46 12.04 24.62C12.57 24.48 13.09 24.31 13.6 24.13V23.99C13.6 23.38 13.77 22.81 14.08 22.32C13.35 22.63 12.6 22.91 11.82 23.11C11.28 22.1 10.08 21.63 9 22V21.98C8.2 22.25 7.6 22.92 7.44 23.74H6.8C5.19 21.81 4.22 19.43 4.04 16.92L4.15 16.73C5.4 14.79 7.04 13.12 8.96 11.84C9.55 12.45 10.42 12.69 11.24 12.47C12.48 14.57 14.16 16.37 16.15 17.79C16.18 17.76 16.2 17.73 16.22 17.71L17.22 16.76C15.32 15.44 13.71 13.72 12.54 11.72C12.94 11.22 13.11 10.58 13.01 9.95L12.99 9.88C16.62 8.66 20.56 8.71 24.16 10.01C23.89 11.4 23.44 12.74 22.84 14.02C23.15 13.92 23.48 13.86 23.82 13.85H24.49C24.94 12.78 25.31 11.67 25.56 10.53C26.06 10.76 26.55 11.02 27.04 11.31C27.5 12.41 27.79 13.56 27.91 14.72C28.21 15.01 28.47 15.36 28.64 15.76C29.02 15.6 29.43 15.52 29.84 15.52C29.89 15.52 29.94 15.53 29.99 15.53C29.74 7.98 23.55 2 16 2C8.27 2 2 8.27 2 16C2 16.45 2 16.89 2.07 17.33C2.74 24.33 8.46 29.65 15.32 29.98C15.3 29.55 15.37 29.11 15.53 28.7C15.08 28.51 14.69 28.22 14.38 27.87C12.91 27.67 11.49 27.2 10.18 26.47H10.19ZM16 4C19.16 4 22.2 5.25 24.44 7.48V8.48C20.49 7.12 16.19 7.14 12.25 8.53C11.94 8.22 11.54 8.01 11.11 7.92H10.86C10.74 7.5 10.63 7.08 10.54 6.65C10.45 6.22 10.4 5.84 10.35 5.43C12.09 4.49 14.03 4 16 4ZM9 6.28C9 6.45 9.04 6.61 9.07 6.77C9.08 6.82 9.09 6.87 9.1 6.92C9.19 7.36 9.31 7.79 9.43 8.22C8.65 8.68 8.22 9.57 8.33 10.47C6.75 11.5 5.34 12.77 4.15 14.23C4.63 11.04 6.38 8.17 9 6.29V6.28Z\"/>',\n    solid: '<path d=\"M33.8562 23.6532L31.8558 23.0534C31.7157 22.5636 31.5157 22.1037 31.2757 21.6539L32.2759 19.8144C32.3559 19.6745 32.3259 19.4945 32.2058 19.3746L30.7256 17.935C30.6155 17.8151 30.4355 17.7851 30.2855 17.865L28.4351 18.8547C27.985 18.5948 27.5049 18.3849 27.0048 18.2349L26.3947 16.2555C26.3447 16.1056 26.1947 16.0056 26.0347 16.0056H24.0343C23.8742 16.0056 23.7342 16.1156 23.6842 16.2655L23.0741 18.2449C22.574 18.3949 22.0939 18.5948 21.6338 18.8447L19.8134 17.855C19.6634 17.7751 19.4934 17.8051 19.3734 17.925L17.9031 19.3546C17.7831 19.4645 17.753 19.6445 17.8331 19.7944L18.8333 21.5939C18.5732 22.0437 18.3732 22.5236 18.2231 23.0234L16.2228 23.6232C16.0727 23.6732 15.9627 23.8132 15.9627 23.9731V25.9525C15.9627 26.1125 16.0727 26.2524 16.2228 26.3024L18.2231 26.9022C18.3732 27.3921 18.5732 27.8619 18.8333 28.3018L17.8331 30.1812C17.753 30.3212 17.7831 30.5011 17.9031 30.6211L19.3133 32.1106C19.4234 32.2306 19.6034 32.2605 19.7534 32.1806L21.6238 31.1909C22.0639 31.4308 22.534 31.6207 23.0141 31.7607L23.6242 33.7401C23.6742 33.89 23.8142 34 23.9743 34H25.9646C26.1247 34 26.2647 33.89 26.3147 33.7401L26.9248 31.7607C27.4049 31.6207 27.865 31.4308 28.3051 31.1909L30.1955 32.1806C30.3455 32.2605 30.5155 32.2306 30.6355 32.1106L32.0858 30.671C32.2058 30.5611 32.2358 30.3811 32.1558 30.2312L31.1556 28.3718C31.3957 27.9319 31.5957 27.472 31.7358 26.9922L33.7361 26.3924C33.8862 26.3424 33.9962 26.2024 33.9962 26.0425V24.0131C34.0162 23.8732 33.9562 23.7332 33.8462 23.6532H33.8562ZM25.0345 28.3318C23.6842 28.3418 22.464 27.552 21.9439 26.3124C21.4238 25.0828 21.6938 23.6532 22.644 22.7035C23.5942 21.7538 25.0245 21.4639 26.2647 21.9738C27.5149 22.4836 28.3251 23.6932 28.3251 25.0228C28.3251 26.8322 26.8548 28.3118 25.0345 28.3218V28.3318ZM15.0625 27.9119C13.3522 27.7819 11.6919 27.3021 10.1916 26.4624L10.4716 26.4024C11.2818 26.1325 11.8819 25.4527 12.0419 24.6129C12.7121 24.443 13.3622 24.2331 14.0023 23.9931V23.9531C14.0023 22.9235 14.6825 22.0237 15.6826 21.7138L16.5128 21.4639L16.3528 21.164C14.9525 22.0237 13.4322 22.6835 11.8419 23.1134C11.3018 22.1037 10.1016 21.6339 9.02136 22.0037V21.9838C8.2212 22.2537 7.62109 22.9235 7.46106 23.7432H6.82093C5.21062 21.8138 4.24043 19.4346 4.0604 16.9253L4.17042 16.7354C5.42066 14.796 7.06098 13.1265 8.98135 11.8469C9.57146 12.4567 10.4416 12.6967 11.2618 12.4767C12.572 14.686 14.3524 16.5754 16.4728 18.015C16.5028 17.985 16.5228 17.945 16.5528 17.915L17.533 16.9653C17.533 16.9653 17.503 16.9453 17.493 16.9353C15.4726 15.5858 13.7923 13.7963 12.562 11.707C12.9621 11.2071 13.1322 10.5673 13.0321 9.93752L13.0121 9.86754C16.6428 8.64792 20.5836 8.69791 24.1843 9.9975C23.8842 11.577 23.3341 13.0865 22.604 14.5161C23.0141 14.1962 23.5142 14.0062 24.0743 13.9963H24.4643C24.9444 12.8766 25.3345 11.717 25.5946 10.5173C26.0947 10.7473 26.5848 11.0072 27.0749 11.2971C27.5649 12.4667 27.855 13.6863 27.965 14.926C28.1151 15.1259 28.2351 15.3458 28.3251 15.5858V15.6157L28.3451 15.6457L28.6051 16.4955L29.3753 16.0756C29.5753 15.9656 29.7954 15.9057 30.0154 15.8557C30.0154 15.7457 30.0154 15.6357 30.0154 15.5258C29.7554 7.99813 23.5542 2 16.0027 2C8.27121 2 2 8.26804 2 15.9956C2 16.4455 2 16.8853 2.07001 17.3252C2.75015 24.503 8.76131 29.9113 15.8527 29.9813C15.8927 29.7313 15.9727 29.4814 16.0927 29.2515L16.5128 28.4717L15.6626 28.2118C15.4426 28.1418 15.2426 28.0419 15.0525 27.9219L15.0625 27.9119ZM16.0027 3.99938C19.1633 3.99938 22.2039 5.24898 24.4443 7.47829V8.47798C20.4936 7.1184 16.1927 7.13839 12.252 8.52796C11.9419 8.21806 11.5418 8.00812 11.1118 7.91815H10.8617C10.7417 7.49828 10.6317 7.07841 10.5417 6.64855C10.4516 6.21868 10.4016 5.8388 10.3516 5.42893C12.092 4.48922 14.0323 3.99938 16.0027 3.99938ZM9.00135 6.27866C9.00135 6.44861 9.04136 6.60856 9.07137 6.76851C9.08137 6.81849 9.09137 6.86848 9.10137 6.91846C9.19139 7.35833 9.31141 7.78819 9.43144 8.21806C8.65129 8.67791 8.2212 9.56764 8.33122 10.4674C6.75092 11.497 5.34065 12.7666 4.15042 14.2262C4.63051 11.0372 6.38085 8.16807 9.00135 6.28866V6.27866ZM21.6638 16.1056C21.5838 16.2156 21.5138 16.3355 21.4338 16.4455L21.5438 16.5055L21.6638 16.1056Z\"/>'\n  })];\nexport { V as networkSettingsIcon, L as networkSettingsIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "V", "outline", "solid", "networkSettingsIcon", "networkSettingsIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/network-settings.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"network-settings\",V=[\"network-settings\",C({outline:'<path d=\"M24.83 21.08C22.71 21.1 21.01 22.84 21.03 24.96C21.05 27.08 22.78 28.78 24.9 28.76C27.02 28.74 28.72 27.01 28.71 24.89C28.68 22.77 26.95 21.07 24.83 21.08ZM26.44 26.5C26.01 26.93 25.43 27.16 24.83 27.16C23.6 27.14 22.62 26.13 22.63 24.9C22.63 23.67 23.63 22.68 24.86 22.68C26.09 22.68 27.09 23.66 27.11 24.89C27.11 25.49 26.87 26.08 26.44 26.5ZM33.3 22.93L31.74 22.47C31.67 22.26 31.58 22.05 31.48 21.84L32.2 20.51C32.43 20.07 32.35 19.52 31.99 19.17L30.65 17.85C30.3 17.5 29.75 17.41 29.31 17.65L27.97 18.36C27.75 18.26 27.53 18.16 27.3 18.08L26.83 16.6C26.67 16.14 26.23 15.84 25.75 15.84H23.83C23.33 15.84 22.9 16.17 22.75 16.64L22.31 18.07C22.08 18.15 21.85 18.25 21.63 18.35L20.31 17.65C19.87 17.42 19.33 17.49 18.98 17.84L17.61 19.15C17.25 19.5 17.16 20.05 17.4 20.5L18.1 21.78C17.99 21.99 17.9 22.21 17.82 22.43L16.41 22.89C15.93 23.03 15.6 23.48 15.6 23.98V25.85C15.62 26.34 15.95 26.75 16.42 26.89L17.89 27.33C17.97 27.54 18.06 27.76 18.17 27.97L17.45 29.32C17.21 29.76 17.29 30.31 17.65 30.67L18.99 31.99C19.34 32.34 19.89 32.42 20.33 32.19L21.7 31.47C21.91 31.56 22.12 31.65 22.33 31.73L22.77 33.2C22.92 33.68 23.36 34 23.86 34H25.76C26.25 34 26.68 33.67 26.83 33.2L27.27 31.73C27.48 31.66 27.69 31.57 27.89 31.48L29.27 32.21C29.71 32.44 30.25 32.36 30.6 32.01L31.94 30.69C32.3 30.34 32.39 29.79 32.15 29.34L31.42 28C31.51 27.8 31.6 27.6 31.67 27.4L33.17 26.96C33.65 26.83 33.99 26.39 34 25.89V24.03C34.02 23.55 33.74 23.11 33.3 22.93ZM30.4 26.11L30.28 26.53C30.17 26.91 30.02 27.27 29.83 27.62L29.62 28L30.62 29.79L29.76 30.63L27.94 29.63L27.57 29.83C27.21 30.02 26.84 30.18 26.45 30.29L26.03 30.41L25.44 32.41H24.21L23.62 30.46L23.2 30.34C22.81 30.23 22.43 30.08 22.07 29.89L21.7 29.69L19.89 30.69L19.03 29.84L20.03 28.02L19.81 27.64C19.61 27.28 19.44 26.9 19.32 26.51L19.19 26.1L17.24 25.52V24.31L19.18 23.73L19.3 23.32C19.42 22.92 19.58 22.54 19.79 22.18L20.01 21.79L19.01 20.06L19.88 19.22L21.65 20.16L22.03 19.95C22.4 19.74 22.79 19.58 23.2 19.46L23.61 19.34L24.2 17.43H25.43L26.01 19.33L26.42 19.45C26.82 19.57 27.21 19.73 27.58 19.93L27.96 20.14L29.76 19.19L30.62 20.04L29.62 21.81L29.83 22.19C30.03 22.55 30.19 22.93 30.3 23.32L30.42 23.74L32.35 24.31L32.4 25.53L30.4 26.11ZM10.19 26.47L10.47 26.41C11.28 26.14 11.88 25.46 12.04 24.62C12.57 24.48 13.09 24.31 13.6 24.13V23.99C13.6 23.38 13.77 22.81 14.08 22.32C13.35 22.63 12.6 22.91 11.82 23.11C11.28 22.1 10.08 21.63 9 22V21.98C8.2 22.25 7.6 22.92 7.44 23.74H6.8C5.19 21.81 4.22 19.43 4.04 16.92L4.15 16.73C5.4 14.79 7.04 13.12 8.96 11.84C9.55 12.45 10.42 12.69 11.24 12.47C12.48 14.57 14.16 16.37 16.15 17.79C16.18 17.76 16.2 17.73 16.22 17.71L17.22 16.76C15.32 15.44 13.71 13.72 12.54 11.72C12.94 11.22 13.11 10.58 13.01 9.95L12.99 9.88C16.62 8.66 20.56 8.71 24.16 10.01C23.89 11.4 23.44 12.74 22.84 14.02C23.15 13.92 23.48 13.86 23.82 13.85H24.49C24.94 12.78 25.31 11.67 25.56 10.53C26.06 10.76 26.55 11.02 27.04 11.31C27.5 12.41 27.79 13.56 27.91 14.72C28.21 15.01 28.47 15.36 28.64 15.76C29.02 15.6 29.43 15.52 29.84 15.52C29.89 15.52 29.94 15.53 29.99 15.53C29.74 7.98 23.55 2 16 2C8.27 2 2 8.27 2 16C2 16.45 2 16.89 2.07 17.33C2.74 24.33 8.46 29.65 15.32 29.98C15.3 29.55 15.37 29.11 15.53 28.7C15.08 28.51 14.69 28.22 14.38 27.87C12.91 27.67 11.49 27.2 10.18 26.47H10.19ZM16 4C19.16 4 22.2 5.25 24.44 7.48V8.48C20.49 7.12 16.19 7.14 12.25 8.53C11.94 8.22 11.54 8.01 11.11 7.92H10.86C10.74 7.5 10.63 7.08 10.54 6.65C10.45 6.22 10.4 5.84 10.35 5.43C12.09 4.49 14.03 4 16 4ZM9 6.28C9 6.45 9.04 6.61 9.07 6.77C9.08 6.82 9.09 6.87 9.1 6.92C9.19 7.36 9.31 7.79 9.43 8.22C8.65 8.68 8.22 9.57 8.33 10.47C6.75 11.5 5.34 12.77 4.15 14.23C4.63 11.04 6.38 8.17 9 6.29V6.28Z\"/>',solid:'<path d=\"M33.8562 23.6532L31.8558 23.0534C31.7157 22.5636 31.5157 22.1037 31.2757 21.6539L32.2759 19.8144C32.3559 19.6745 32.3259 19.4945 32.2058 19.3746L30.7256 17.935C30.6155 17.8151 30.4355 17.7851 30.2855 17.865L28.4351 18.8547C27.985 18.5948 27.5049 18.3849 27.0048 18.2349L26.3947 16.2555C26.3447 16.1056 26.1947 16.0056 26.0347 16.0056H24.0343C23.8742 16.0056 23.7342 16.1156 23.6842 16.2655L23.0741 18.2449C22.574 18.3949 22.0939 18.5948 21.6338 18.8447L19.8134 17.855C19.6634 17.7751 19.4934 17.8051 19.3734 17.925L17.9031 19.3546C17.7831 19.4645 17.753 19.6445 17.8331 19.7944L18.8333 21.5939C18.5732 22.0437 18.3732 22.5236 18.2231 23.0234L16.2228 23.6232C16.0727 23.6732 15.9627 23.8132 15.9627 23.9731V25.9525C15.9627 26.1125 16.0727 26.2524 16.2228 26.3024L18.2231 26.9022C18.3732 27.3921 18.5732 27.8619 18.8333 28.3018L17.8331 30.1812C17.753 30.3212 17.7831 30.5011 17.9031 30.6211L19.3133 32.1106C19.4234 32.2306 19.6034 32.2605 19.7534 32.1806L21.6238 31.1909C22.0639 31.4308 22.534 31.6207 23.0141 31.7607L23.6242 33.7401C23.6742 33.89 23.8142 34 23.9743 34H25.9646C26.1247 34 26.2647 33.89 26.3147 33.7401L26.9248 31.7607C27.4049 31.6207 27.865 31.4308 28.3051 31.1909L30.1955 32.1806C30.3455 32.2605 30.5155 32.2306 30.6355 32.1106L32.0858 30.671C32.2058 30.5611 32.2358 30.3811 32.1558 30.2312L31.1556 28.3718C31.3957 27.9319 31.5957 27.472 31.7358 26.9922L33.7361 26.3924C33.8862 26.3424 33.9962 26.2024 33.9962 26.0425V24.0131C34.0162 23.8732 33.9562 23.7332 33.8462 23.6532H33.8562ZM25.0345 28.3318C23.6842 28.3418 22.464 27.552 21.9439 26.3124C21.4238 25.0828 21.6938 23.6532 22.644 22.7035C23.5942 21.7538 25.0245 21.4639 26.2647 21.9738C27.5149 22.4836 28.3251 23.6932 28.3251 25.0228C28.3251 26.8322 26.8548 28.3118 25.0345 28.3218V28.3318ZM15.0625 27.9119C13.3522 27.7819 11.6919 27.3021 10.1916 26.4624L10.4716 26.4024C11.2818 26.1325 11.8819 25.4527 12.0419 24.6129C12.7121 24.443 13.3622 24.2331 14.0023 23.9931V23.9531C14.0023 22.9235 14.6825 22.0237 15.6826 21.7138L16.5128 21.4639L16.3528 21.164C14.9525 22.0237 13.4322 22.6835 11.8419 23.1134C11.3018 22.1037 10.1016 21.6339 9.02136 22.0037V21.9838C8.2212 22.2537 7.62109 22.9235 7.46106 23.7432H6.82093C5.21062 21.8138 4.24043 19.4346 4.0604 16.9253L4.17042 16.7354C5.42066 14.796 7.06098 13.1265 8.98135 11.8469C9.57146 12.4567 10.4416 12.6967 11.2618 12.4767C12.572 14.686 14.3524 16.5754 16.4728 18.015C16.5028 17.985 16.5228 17.945 16.5528 17.915L17.533 16.9653C17.533 16.9653 17.503 16.9453 17.493 16.9353C15.4726 15.5858 13.7923 13.7963 12.562 11.707C12.9621 11.2071 13.1322 10.5673 13.0321 9.93752L13.0121 9.86754C16.6428 8.64792 20.5836 8.69791 24.1843 9.9975C23.8842 11.577 23.3341 13.0865 22.604 14.5161C23.0141 14.1962 23.5142 14.0062 24.0743 13.9963H24.4643C24.9444 12.8766 25.3345 11.717 25.5946 10.5173C26.0947 10.7473 26.5848 11.0072 27.0749 11.2971C27.5649 12.4667 27.855 13.6863 27.965 14.926C28.1151 15.1259 28.2351 15.3458 28.3251 15.5858V15.6157L28.3451 15.6457L28.6051 16.4955L29.3753 16.0756C29.5753 15.9656 29.7954 15.9057 30.0154 15.8557C30.0154 15.7457 30.0154 15.6357 30.0154 15.5258C29.7554 7.99813 23.5542 2 16.0027 2C8.27121 2 2 8.26804 2 15.9956C2 16.4455 2 16.8853 2.07001 17.3252C2.75015 24.503 8.76131 29.9113 15.8527 29.9813C15.8927 29.7313 15.9727 29.4814 16.0927 29.2515L16.5128 28.4717L15.6626 28.2118C15.4426 28.1418 15.2426 28.0419 15.0525 27.9219L15.0625 27.9119ZM16.0027 3.99938C19.1633 3.99938 22.2039 5.24898 24.4443 7.47829V8.47798C20.4936 7.1184 16.1927 7.13839 12.252 8.52796C11.9419 8.21806 11.5418 8.00812 11.1118 7.91815H10.8617C10.7417 7.49828 10.6317 7.07841 10.5417 6.64855C10.4516 6.21868 10.4016 5.8388 10.3516 5.42893C12.092 4.48922 14.0323 3.99938 16.0027 3.99938ZM9.00135 6.27866C9.00135 6.44861 9.04136 6.60856 9.07137 6.76851C9.08137 6.81849 9.09137 6.86848 9.10137 6.91846C9.19139 7.35833 9.31141 7.78819 9.43144 8.21806C8.65129 8.67791 8.2212 9.56764 8.33122 10.4674C6.75092 11.497 5.34065 12.7666 4.15042 14.2262C4.63051 11.0372 6.38085 8.16807 9.00135 6.28866V6.27866ZM21.6638 16.1056C21.5838 16.2156 21.5138 16.3355 21.4338 16.4455L21.5438 16.5055L21.6638 16.1056Z\"/>'})];export{V as networkSettingsIcon,L as networkSettingsIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,kBAAkB;EAACC,CAAC,GAAC,CAAC,kBAAkB,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,ukHAAukH;IAACC,KAAK,EAAC;EAAyhI,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,mBAAmB,EAACJ,CAAC,IAAIK,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}