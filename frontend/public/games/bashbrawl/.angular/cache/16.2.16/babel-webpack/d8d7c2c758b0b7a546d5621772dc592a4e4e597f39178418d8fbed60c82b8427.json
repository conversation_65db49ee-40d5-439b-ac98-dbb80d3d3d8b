{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"battery\",\n  H = [\"battery\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M26.12 4H24V3.58C24 2.71 23.29 2 22.42 2H13.58C12.71 2 12 2.71 12 3.58V4H9.88C9.38 4 8.9 4.2 8.55 4.55C8.2 4.9 8 5.38 8 5.88V32.12C8 32.62 8.2 33.1 8.55 33.45C8.9 33.8 9.38 34 9.88 34H26.12C27.16 34 28 33.16 28 32.12V5.88C28 4.84 27.16 4 26.12 4ZM26 32H10V6H14V4H22V6H26V32ZM17.25 11.44C17.71 11.17 18.31 11.32 18.59 11.77L23.22 19.79L16.84 18.73L20.37 25.81C20.6 26.3 20.39 26.88 19.91 27.12C19.43 27.36 18.84 27.17 18.58 26.7L13.29 16.11L19.36 17.11L16.86 12.77C16.61 12.3 16.78 11.71 17.25 11.44Z\"/>',\n    outlineAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M23.8338 2.87593C23.5741 2.35747 23.0373 2 22.42 2H13.58C12.71 2 12 2.71 12 3.58V4H9.88C9.38 4 8.9 4.2 8.55 4.55C8.2 4.9 8 5.38 8 5.88V32.12C8 32.62 8.2 33.1 8.55 33.45C8.9 33.8 9.38 34 9.88 34H26.12C27.16 34 28 33.16 28 32.12V15.0367H26V32H10V6H14V4H22V5.9323L23.8338 2.87593Z\"/><path d=\"M19.1305 12.7062L18.59 11.77C18.31 11.32 17.71 11.17 17.25 11.44C16.78 11.71 16.61 12.3 16.86 12.77L19.36 17.11L13.29 16.11L18.58 26.7C18.84 27.17 19.43 27.36 19.91 27.12C20.39 26.88 20.6 26.3 20.37 25.81L16.84 18.73L23.22 19.79L19.889 14.0202C19.7107 13.8328 19.5528 13.6219 19.4206 13.3893C19.2959 13.1703 19.1994 12.941 19.1305 12.7062Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M23.7823 2.78128C23.5071 2.31447 22.999 2 22.42 2H13.58C12.71 2 12 2.71 12 3.58V4H9.88C9.38 4 8.9 4.2 8.55 4.55C8.2 4.9 8 5.38 8 5.88V32.12C8 32.62 8.2 33.1 8.55 33.45C8.9 33.8 9.38 34 9.88 34H26.12C27.16 34 28 33.16 28 32.12V12.7101C27.2776 12.4951 26.604 12.1666 26 11.7453V32H10V6H14V4H22V6H23C23 4.83947 23.2824 3.74491 23.7823 2.78128Z\"/><path d=\"M18.59 11.77C18.31 11.32 17.71 11.17 17.25 11.44C16.78 11.71 16.61 12.3 16.86 12.77L19.36 17.11L13.29 16.11L18.58 26.7C18.84 27.17 19.43 27.36 19.91 27.12C20.39 26.88 20.6 26.3 20.37 25.81L16.84 18.73L23.22 19.79L18.59 11.77Z\"/>',\n    solid: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M26.12 4H24V3C24 2.45 23.55 2 23 2H13C12.45 2 12 2.45 12 3V4H9.88C9.38 4 8.9 4.2 8.55 4.55C8.2 4.9 8 5.38 8 5.88V32.12C8 32.62 8.2 33.1 8.55 33.45C8.9 33.8 9.38 34 9.88 34H26.12C27.16 34 28 33.16 28 32.12V5.88C28 4.84 27.16 4 26.12 4ZM23.19 19.99L17.2 18.99L20.56 25.73C20.83 26.32 20.59 27.01 20 27.31C19.83 27.39 19.65 27.44 19.47 27.44C19.03 27.44 18.62 27.2 18.41 26.8L13.12 16.21L12.94 15.86L13.33 15.92L18.98 16.85L16.69 12.87C16.39 12.3 16.59 11.6 17.15 11.27C17.33 11.16 17.54 11.11 17.75 11.11C18.16 11.11 18.54 11.32 18.77 11.67L23.4 19.69L23.61 20.06L23.19 19.99Z\"/>',\n    solidAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M23.9487 2.68446C23.8158 2.28783 23.4399 2 23 2H13C12.45 2 12 2.45 12 3V4H9.88C9.38 4 8.9 4.2 8.55 4.55C8.2 4.9 8 5.38 8 5.88V32.12C8 32.62 8.2 33.1 8.55 33.45C8.9 33.8 9.38 34 9.88 34H26.12C27.16 34 28 33.16 28 32.12V15.0367H22.3395C21.6287 15.0509 20.935 14.8354 20.3638 14.4308L23.4 19.69L23.61 20.06L17.2 18.99L20.56 25.73C20.83 26.32 20.59 27.01 20 27.31C19.83 27.39 19.65 27.44 19.47 27.44C19.03 27.44 18.62 27.2 18.41 26.8L13.12 16.21L12.94 15.86L13.33 15.92L18.98 16.85L16.69 12.87C16.39 12.3 16.59 11.6 17.15 11.27C17.33 11.16 17.54 11.11 17.75 11.11C18.16 11.11 18.54 11.32 18.77 11.67L19.0143 12.0932C18.9505 11.3807 19.1271 10.6553 19.5362 10.0387L23.9487 2.68446Z\"/>',\n    solidBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M23.8996 2.56456C23.3268 3.57944 23 4.75155 23 6C23 9.17102 25.1085 11.8496 28 12.7101V32.12C28 33.16 27.16 34 26.12 34H9.88C9.38 34 8.9 33.8 8.55 33.45C8.2 33.1 8 32.62 8 32.12V5.88C8 5.38 8.2 4.9 8.55 4.55C8.9 4.2 9.38 4 9.88 4H12V3C12 2.45 12.45 2 13 2H23C23.3942 2 23.737 2.23113 23.8996 2.56456ZM17.2 18.99L23.61 20.06L23.4 19.69L18.77 11.67C18.54 11.32 18.16 11.11 17.75 11.11C17.54 11.11 17.33 11.16 17.15 11.27C16.59 11.6 16.39 12.3 16.69 12.87L18.98 16.85L13.33 15.92L12.94 15.86L13.12 16.21L18.41 26.8C18.62 27.2 19.03 27.44 19.47 27.44C19.65 27.44 19.83 27.39 20 27.31C20.59 27.01 20.83 26.32 20.56 25.73L17.2 18.99Z\"/>'\n  })];\nexport { H as batteryIcon, L as batteryIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "H", "outline", "outlineAlerted", "outlineBadged", "solid", "solidAlerted", "solidBadged", "batteryIcon", "batteryIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/battery.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"battery\",H=[\"battery\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M26.12 4H24V3.58C24 2.71 23.29 2 22.42 2H13.58C12.71 2 12 2.71 12 3.58V4H9.88C9.38 4 8.9 4.2 8.55 4.55C8.2 4.9 8 5.38 8 5.88V32.12C8 32.62 8.2 33.1 8.55 33.45C8.9 33.8 9.38 34 9.88 34H26.12C27.16 34 28 33.16 28 32.12V5.88C28 4.84 27.16 4 26.12 4ZM26 32H10V6H14V4H22V6H26V32ZM17.25 11.44C17.71 11.17 18.31 11.32 18.59 11.77L23.22 19.79L16.84 18.73L20.37 25.81C20.6 26.3 20.39 26.88 19.91 27.12C19.43 27.36 18.84 27.17 18.58 26.7L13.29 16.11L19.36 17.11L16.86 12.77C16.61 12.3 16.78 11.71 17.25 11.44Z\"/>',outlineAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M23.8338 2.87593C23.5741 2.35747 23.0373 2 22.42 2H13.58C12.71 2 12 2.71 12 3.58V4H9.88C9.38 4 8.9 4.2 8.55 4.55C8.2 4.9 8 5.38 8 5.88V32.12C8 32.62 8.2 33.1 8.55 33.45C8.9 33.8 9.38 34 9.88 34H26.12C27.16 34 28 33.16 28 32.12V15.0367H26V32H10V6H14V4H22V5.9323L23.8338 2.87593Z\"/><path d=\"M19.1305 12.7062L18.59 11.77C18.31 11.32 17.71 11.17 17.25 11.44C16.78 11.71 16.61 12.3 16.86 12.77L19.36 17.11L13.29 16.11L18.58 26.7C18.84 27.17 19.43 27.36 19.91 27.12C20.39 26.88 20.6 26.3 20.37 25.81L16.84 18.73L23.22 19.79L19.889 14.0202C19.7107 13.8328 19.5528 13.6219 19.4206 13.3893C19.2959 13.1703 19.1994 12.941 19.1305 12.7062Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M23.7823 2.78128C23.5071 2.31447 22.999 2 22.42 2H13.58C12.71 2 12 2.71 12 3.58V4H9.88C9.38 4 8.9 4.2 8.55 4.55C8.2 4.9 8 5.38 8 5.88V32.12C8 32.62 8.2 33.1 8.55 33.45C8.9 33.8 9.38 34 9.88 34H26.12C27.16 34 28 33.16 28 32.12V12.7101C27.2776 12.4951 26.604 12.1666 26 11.7453V32H10V6H14V4H22V6H23C23 4.83947 23.2824 3.74491 23.7823 2.78128Z\"/><path d=\"M18.59 11.77C18.31 11.32 17.71 11.17 17.25 11.44C16.78 11.71 16.61 12.3 16.86 12.77L19.36 17.11L13.29 16.11L18.58 26.7C18.84 27.17 19.43 27.36 19.91 27.12C20.39 26.88 20.6 26.3 20.37 25.81L16.84 18.73L23.22 19.79L18.59 11.77Z\"/>',solid:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M26.12 4H24V3C24 2.45 23.55 2 23 2H13C12.45 2 12 2.45 12 3V4H9.88C9.38 4 8.9 4.2 8.55 4.55C8.2 4.9 8 5.38 8 5.88V32.12C8 32.62 8.2 33.1 8.55 33.45C8.9 33.8 9.38 34 9.88 34H26.12C27.16 34 28 33.16 28 32.12V5.88C28 4.84 27.16 4 26.12 4ZM23.19 19.99L17.2 18.99L20.56 25.73C20.83 26.32 20.59 27.01 20 27.31C19.83 27.39 19.65 27.44 19.47 27.44C19.03 27.44 18.62 27.2 18.41 26.8L13.12 16.21L12.94 15.86L13.33 15.92L18.98 16.85L16.69 12.87C16.39 12.3 16.59 11.6 17.15 11.27C17.33 11.16 17.54 11.11 17.75 11.11C18.16 11.11 18.54 11.32 18.77 11.67L23.4 19.69L23.61 20.06L23.19 19.99Z\"/>',solidAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M23.9487 2.68446C23.8158 2.28783 23.4399 2 23 2H13C12.45 2 12 2.45 12 3V4H9.88C9.38 4 8.9 4.2 8.55 4.55C8.2 4.9 8 5.38 8 5.88V32.12C8 32.62 8.2 33.1 8.55 33.45C8.9 33.8 9.38 34 9.88 34H26.12C27.16 34 28 33.16 28 32.12V15.0367H22.3395C21.6287 15.0509 20.935 14.8354 20.3638 14.4308L23.4 19.69L23.61 20.06L17.2 18.99L20.56 25.73C20.83 26.32 20.59 27.01 20 27.31C19.83 27.39 19.65 27.44 19.47 27.44C19.03 27.44 18.62 27.2 18.41 26.8L13.12 16.21L12.94 15.86L13.33 15.92L18.98 16.85L16.69 12.87C16.39 12.3 16.59 11.6 17.15 11.27C17.33 11.16 17.54 11.11 17.75 11.11C18.16 11.11 18.54 11.32 18.77 11.67L19.0143 12.0932C18.9505 11.3807 19.1271 10.6553 19.5362 10.0387L23.9487 2.68446Z\"/>',solidBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M23.8996 2.56456C23.3268 3.57944 23 4.75155 23 6C23 9.17102 25.1085 11.8496 28 12.7101V32.12C28 33.16 27.16 34 26.12 34H9.88C9.38 34 8.9 33.8 8.55 33.45C8.2 33.1 8 32.62 8 32.12V5.88C8 5.38 8.2 4.9 8.55 4.55C8.9 4.2 9.38 4 9.88 4H12V3C12 2.45 12.45 2 13 2H23C23.3942 2 23.737 2.23113 23.8996 2.56456ZM17.2 18.99L23.61 20.06L23.4 19.69L18.77 11.67C18.54 11.32 18.16 11.11 17.75 11.11C17.54 11.11 17.33 11.16 17.15 11.27C16.59 11.6 16.39 12.3 16.69 12.87L18.98 16.85L13.33 15.92L12.94 15.86L13.12 16.21L18.41 26.8C18.62 27.2 19.03 27.44 19.47 27.44C19.65 27.44 19.83 27.39 20 27.31C20.59 27.01 20.83 26.32 20.56 25.73L17.2 18.99Z\"/>'})];export{H as batteryIcon,L as batteryIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,SAAS;EAACC,CAAC,GAAC,CAAC,SAAS,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,yiBAAyiB;IAACC,cAAc,EAAC,q+BAAq+B;IAACC,aAAa,EAAC,6sBAA6sB;IAACC,KAAK,EAAC,onBAAonB;IAACC,YAAY,EAAC,qhCAAqhC;IAACC,WAAW,EAAC;EAAuyB,CAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,WAAW,EAACR,CAAC,IAAIS,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}