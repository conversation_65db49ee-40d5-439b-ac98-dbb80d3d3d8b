{"ast": null, "code": "import { renderIcon as a } from \"../icon.renderer.js\";\nconst o = \"mouse\",\n  V = [\"mouse\", a({\n    outline: '<path d=\"M18,34A10,10,0,0,1,8,24V12a10,10,0,0,1,20,0V24A10,10,0,0,1,18,34ZM18,4a8,8,0,0,0-8,8V24a8,8,0,0,0,16,0V12A8,8,0,0,0,18,4Z\"/><path d=\"M18,15a1,1,0,0,1-1-1V10a1,1,0,0,1,2,0v4A1,1,0,0,1,18,15Z\"/>',\n    solid: '<path d=\"M18,2A10,10,0,0,0,8,12V24a10,10,0,0,0,20,0V12A10,10,0,0,0,18,2Zm1.3,11.44a1.3,1.3,0,0,1-2.6,0V10a1.3,1.3,0,0,1,2.6,0Z\"/>'\n  })];\nexport { V as mouseIcon, o as mouseIconName };", "map": {"version": 3, "names": ["renderIcon", "a", "o", "V", "outline", "solid", "mouseIcon", "mouseIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/mouse.js"], "sourcesContent": ["import{renderIcon as a}from\"../icon.renderer.js\";const o=\"mouse\",V=[\"mouse\",a({outline:'<path d=\"M18,34A10,10,0,0,1,8,24V12a10,10,0,0,1,20,0V24A10,10,0,0,1,18,34ZM18,4a8,8,0,0,0-8,8V24a8,8,0,0,0,16,0V12A8,8,0,0,0,18,4Z\"/><path d=\"M18,15a1,1,0,0,1-1-1V10a1,1,0,0,1,2,0v4A1,1,0,0,1,18,15Z\"/>',solid:'<path d=\"M18,2A10,10,0,0,0,8,12V24a10,10,0,0,0,20,0V12A10,10,0,0,0,18,2Zm1.3,11.44a1.3,1.3,0,0,1-2.6,0V10a1.3,1.3,0,0,1,2.6,0Z\"/>'})];export{V as mouseIcon,o as mouseIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,OAAO;EAACC,CAAC,GAAC,CAAC,OAAO,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,2MAA2M;IAACC,KAAK,EAAC;EAAmI,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,SAAS,EAACJ,CAAC,IAAIK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}