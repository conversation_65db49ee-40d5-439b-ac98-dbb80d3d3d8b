{"ast": null, "code": "const t = [{\n  opacity: 0,\n  transform: \"translateY(-15rem)\"\n}, {\n  opacity: 1,\n  transform: \"translateY(0)\"\n}];\nexport { t as fadeAndSlideInKeyframes };", "map": {"version": 3, "names": ["t", "opacity", "transform", "fadeAndSlideInKeyframes"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/motion/animations/keyframes/fade-in-and-slide-down.js"], "sourcesContent": ["const t=[{opacity:0,transform:\"translateY(-15rem)\"},{opacity:1,transform:\"translateY(0)\"}];export{t as fadeAndSlideInKeyframes};\n"], "mappings": "AAAA,MAAMA,CAAC,GAAC,CAAC;EAACC,OAAO,EAAC,CAAC;EAACC,SAAS,EAAC;AAAoB,CAAC,EAAC;EAACD,OAAO,EAAC,CAAC;EAACC,SAAS,EAAC;AAAe,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}