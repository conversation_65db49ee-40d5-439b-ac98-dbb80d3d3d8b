{"ast": null, "code": "import { __decorate as o } from \"tslib\";\nimport { LitElement as r, html as t } from \"lit\";\nimport { state as e } from \"../decorators/property.js\";\nimport { createId as s } from \"../utils/identity.js\";\nimport { FirstFocusController as l } from \"../controllers/first-focus.controller.js\";\nimport { ClosableController as i } from \"../controllers/closable.controller.js\";\nimport { InlineFocusTrapController as n } from \"../controllers/inline-focus-trap.controller.js\";\nclass p extends r {\n  constructor() {\n    super();\n    this.firstFocusController = new l(this), this.closableController = new i(this), this.inlineFocusTrapController = new n(this), this.demoMode = !1, this.focusTrapId = s();\n  }\n  render() {\n    return t`<slot></slot>`;\n  }\n}\no([e({\n  type: Boolean,\n  reflect: !0\n})], p.prototype, \"demoMode\", void 0), o([e({\n  type: String\n})], p.prototype, \"focusTrapId\", void 0);\nexport { p as CdsBaseFocusTrap };", "map": {"version": 3, "names": ["__decorate", "o", "LitElement", "r", "html", "t", "state", "e", "createId", "s", "FirstFocusController", "l", "ClosableController", "i", "InlineFocusTrapController", "n", "p", "constructor", "firstFocusController", "closableController", "inlineFocusTrapController", "demoMode", "focusTrapId", "render", "type", "Boolean", "reflect", "prototype", "String", "CdsBaseFocusTrap"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/base/focus-trap.base.js"], "sourcesContent": ["import{__decorate as o}from\"tslib\";import{LitElement as r,html as t}from\"lit\";import{state as e}from\"../decorators/property.js\";import{createId as s}from\"../utils/identity.js\";import{FirstFocusController as l}from\"../controllers/first-focus.controller.js\";import{ClosableController as i}from\"../controllers/closable.controller.js\";import{InlineFocusTrapController as n}from\"../controllers/inline-focus-trap.controller.js\";class p extends r{constructor(){super();this.firstFocusController=new l(this),this.closableController=new i(this),this.inlineFocusTrapController=new n(this),this.demoMode=!1,this.focusTrapId=s()}render(){return t`<slot></slot>`}}o([e({type:Boolean,reflect:!0})],p.prototype,\"demoMode\",void 0),o([e({type:String})],p.prototype,\"focusTrapId\",void 0);export{p as CdsBaseFocusTrap};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,UAAU,IAAIC,CAAC,EAACC,IAAI,IAAIC,CAAC,QAAK,KAAK;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,oBAAoB,IAAIC,CAAC,QAAK,0CAA0C;AAAC,SAAOC,kBAAkB,IAAIC,CAAC,QAAK,uCAAuC;AAAC,SAAOC,yBAAyB,IAAIC,CAAC,QAAK,gDAAgD;AAAC,MAAMC,CAAC,SAASb,CAAC;EAACc,WAAWA,CAAA,EAAE;IAAC,KAAK,CAAC,CAAC;IAAC,IAAI,CAACC,oBAAoB,GAAC,IAAIP,CAAC,CAAC,IAAI,CAAC,EAAC,IAAI,CAACQ,kBAAkB,GAAC,IAAIN,CAAC,CAAC,IAAI,CAAC,EAAC,IAAI,CAACO,yBAAyB,GAAC,IAAIL,CAAC,CAAC,IAAI,CAAC,EAAC,IAAI,CAACM,QAAQ,GAAC,CAAC,CAAC,EAAC,IAAI,CAACC,WAAW,GAACb,CAAC,CAAC,CAAC;EAAA;EAACc,MAAMA,CAAA,EAAE;IAAC,OAAOlB,CAAE,eAAc;EAAA;AAAC;AAACJ,CAAC,CAAC,CAACM,CAAC,CAAC;EAACiB,IAAI,EAACC,OAAO;EAACC,OAAO,EAAC,CAAC;AAAC,CAAC,CAAC,CAAC,EAACV,CAAC,CAACW,SAAS,EAAC,UAAU,EAAC,KAAK,CAAC,CAAC,EAAC1B,CAAC,CAAC,CAACM,CAAC,CAAC;EAACiB,IAAI,EAACI;AAAM,CAAC,CAAC,CAAC,EAACZ,CAAC,CAACW,SAAS,EAAC,aAAa,EAAC,KAAK,CAAC,CAAC;AAAC,SAAOX,CAAC,IAAIa,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}