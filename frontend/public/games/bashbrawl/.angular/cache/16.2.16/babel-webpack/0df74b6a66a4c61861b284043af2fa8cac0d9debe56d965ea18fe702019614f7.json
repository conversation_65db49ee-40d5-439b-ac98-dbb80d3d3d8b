{"ast": null, "code": "import { EventSubject as t } from \"../utils/event-subject.js\";\nimport { setupCDSGlobal as e } from \"../utils/global.js\";\nimport { LogService as s } from \"./log.service.js\";\nconst a = new t();\na.listener = document.addEventListener(\"CDS_STATE_UPDATE\", t => a.emit(t.detail));\nclass i {\n  static get state() {\n    return e(), window.CDS._state;\n  }\n  static getValue(t) {\n    return i.state[t];\n  }\n  static setValue(t, e) {\n    i.state[t] = e;\n  }\n  static log() {\n    s.log(JSON.stringify(i.state, null, 2));\n  }\n}\ni.stateUpdates = a.toEventObservable();\nexport { i as GlobalStateService };", "map": {"version": 3, "names": ["EventSubject", "t", "setupCDSGlobal", "e", "LogService", "s", "a", "listener", "document", "addEventListener", "emit", "detail", "i", "state", "window", "CDS", "_state", "getValue", "setValue", "log", "JSON", "stringify", "stateUpdates", "toEventObservable", "GlobalStateService"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/services/global.service.js"], "sourcesContent": ["import{EventSubject as t}from\"../utils/event-subject.js\";import{setupCDSGlobal as e}from\"../utils/global.js\";import{LogService as s}from\"./log.service.js\";const a=new t;a.listener=document.addEventListener(\"CDS_STATE_UPDATE\",(t=>a.emit(t.detail)));class i{static get state(){return e(),window.CDS._state}static getValue(t){return i.state[t]}static setValue(t,e){i.state[t]=e}static log(){s.log(JSON.stringify(i.state,null,2))}}i.stateUpdates=a.toEventObservable();export{i as GlobalStateService};\n"], "mappings": "AAAA,SAAOA,YAAY,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,kBAAkB;AAAC,MAAMC,CAAC,GAAC,IAAIL,CAAC,CAAD,CAAC;AAACK,CAAC,CAACC,QAAQ,GAACC,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAER,CAAC,IAAEK,CAAC,CAACI,IAAI,CAACT,CAAC,CAACU,MAAM,CAAE,CAAC;AAAC,MAAMC,CAAC;EAAC,WAAWC,KAAKA,CAAA,EAAE;IAAC,OAAOV,CAAC,CAAC,CAAC,EAACW,MAAM,CAACC,GAAG,CAACC,MAAM;EAAA;EAAC,OAAOC,QAAQA,CAAChB,CAAC,EAAC;IAAC,OAAOW,CAAC,CAACC,KAAK,CAACZ,CAAC,CAAC;EAAA;EAAC,OAAOiB,QAAQA,CAACjB,CAAC,EAACE,CAAC,EAAC;IAACS,CAAC,CAACC,KAAK,CAACZ,CAAC,CAAC,GAACE,CAAC;EAAA;EAAC,OAAOgB,GAAGA,CAAA,EAAE;IAACd,CAAC,CAACc,GAAG,CAACC,IAAI,CAACC,SAAS,CAACT,CAAC,CAACC,KAAK,EAAC,IAAI,EAAC,CAAC,CAAC,CAAC;EAAA;AAAC;AAACD,CAAC,CAACU,YAAY,GAAChB,CAAC,CAACiB,iBAAiB,CAAC,CAAC;AAAC,SAAOX,CAAC,IAAIY,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}