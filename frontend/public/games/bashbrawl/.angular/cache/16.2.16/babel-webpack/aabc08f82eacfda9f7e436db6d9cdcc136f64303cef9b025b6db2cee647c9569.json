{"ast": null, "code": "import { ClarityIcons as s } from \"../icon.service.js\";\nimport { alignBottomIcon as o } from \"../shapes/align-bottom.js\";\nimport { alignCenterIcon as r } from \"../shapes/align-center.js\";\nimport { alignLeftTextIcon as t } from \"../shapes/align-left-text.js\";\nimport { alignLeftIcon as p } from \"../shapes/align-left.js\";\nimport { alignMiddleIcon as m } from \"../shapes/align-middle.js\";\nimport { alignRightTextIcon as e } from \"../shapes/align-right-text.js\";\nimport { alignRightIcon as i } from \"../shapes/align-right.js\";\nimport { alignTopIcon as a } from \"../shapes/align-top.js\";\nimport { blockQuoteIcon as h } from \"../shapes/block-quote.js\";\nimport { boldIcon as f } from \"../shapes/bold.js\";\nimport { bulletListIcon as j } from \"../shapes/bullet-list.js\";\nimport { centerTextIcon as l } from \"../shapes/center-text.js\";\nimport { checkboxListIcon as n } from \"../shapes/checkbox-list.js\";\nimport { fontSizeIcon as g } from \"../shapes/font-size.js\";\nimport { highlighterIcon as c } from \"../shapes/highlighter.js\";\nimport { indentIcon as u } from \"../shapes/indent.js\";\nimport { italicIcon as d } from \"../shapes/italic.js\";\nimport { justifyTextIcon as x } from \"../shapes/justify-text.js\";\nimport { languageIcon as b } from \"../shapes/language.js\";\nimport { numberListIcon as k } from \"../shapes/number-list.js\";\nimport { outdentIcon as q } from \"../shapes/outdent.js\";\nimport { paintRollerIcon as v } from \"../shapes/paint-roller.js\";\nimport { strikethroughIcon as y } from \"../shapes/strikethrough.js\";\nimport { subscriptIcon as z } from \"../shapes/subscript.js\";\nimport { superscriptIcon as A } from \"../shapes/superscript.js\";\nimport { textColorIcon as I } from \"../shapes/text-color.js\";\nimport { textIcon as w } from \"../shapes/text.js\";\nimport { underlineIcon as B } from \"../shapes/underline.js\";\nconst C = [o, r, p, t, m, i, e, a, h, f, j, l, n, g, c, u, d, x, b, k, q, v, y, z, A, w, I, B],\n  D = [];\nfunction E() {\n  s.addIcons(...C), s.addAliases(...D);\n}\nexport { E as loadTextEditIconSet, D as textEditCollectionAliases, C as textEditCollectionIcons };", "map": {"version": 3, "names": ["ClarityIcons", "s", "alignBottomIcon", "o", "alignCenterIcon", "r", "alignLeftTextIcon", "t", "alignLeftIcon", "p", "alignMiddleIcon", "m", "alignRightTextIcon", "e", "alignRightIcon", "i", "alignTopIcon", "a", "blockQuoteIcon", "h", "boldIcon", "f", "bulletListIcon", "j", "centerTextIcon", "l", "checkboxListIcon", "n", "fontSizeIcon", "g", "highlighterIcon", "c", "indentIcon", "u", "italicIcon", "d", "justifyTextIcon", "x", "languageIcon", "b", "numberListIcon", "k", "outdentIcon", "q", "paintRollerIcon", "v", "strikethroughIcon", "y", "subscriptIcon", "z", "superscriptIcon", "A", "textColorIcon", "I", "textIcon", "w", "underlineIcon", "B", "C", "D", "E", "addIcons", "addAliases", "loadTextEditIconSet", "textEditCollectionAliases", "textEditCollectionIcons"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/collections/text-edit.js"], "sourcesContent": ["import{ClarityIcons as s}from\"../icon.service.js\";import{alignBottomIcon as o}from\"../shapes/align-bottom.js\";import{alignCenterIcon as r}from\"../shapes/align-center.js\";import{alignLeftTextIcon as t}from\"../shapes/align-left-text.js\";import{alignLeftIcon as p}from\"../shapes/align-left.js\";import{alignMiddleIcon as m}from\"../shapes/align-middle.js\";import{alignRightTextIcon as e}from\"../shapes/align-right-text.js\";import{alignRightIcon as i}from\"../shapes/align-right.js\";import{alignTopIcon as a}from\"../shapes/align-top.js\";import{blockQuoteIcon as h}from\"../shapes/block-quote.js\";import{boldIcon as f}from\"../shapes/bold.js\";import{bulletListIcon as j}from\"../shapes/bullet-list.js\";import{centerTextIcon as l}from\"../shapes/center-text.js\";import{checkboxListIcon as n}from\"../shapes/checkbox-list.js\";import{fontSizeIcon as g}from\"../shapes/font-size.js\";import{highlighterIcon as c}from\"../shapes/highlighter.js\";import{indentIcon as u}from\"../shapes/indent.js\";import{italicIcon as d}from\"../shapes/italic.js\";import{justifyTextIcon as x}from\"../shapes/justify-text.js\";import{languageIcon as b}from\"../shapes/language.js\";import{numberListIcon as k}from\"../shapes/number-list.js\";import{outdentIcon as q}from\"../shapes/outdent.js\";import{paintRollerIcon as v}from\"../shapes/paint-roller.js\";import{strikethroughIcon as y}from\"../shapes/strikethrough.js\";import{subscriptIcon as z}from\"../shapes/subscript.js\";import{superscriptIcon as A}from\"../shapes/superscript.js\";import{textColorIcon as I}from\"../shapes/text-color.js\";import{textIcon as w}from\"../shapes/text.js\";import{underlineIcon as B}from\"../shapes/underline.js\";const C=[o,r,p,t,m,i,e,a,h,f,j,l,n,g,c,u,d,x,b,k,q,v,y,z,A,w,I,B],D=[];function E(){s.addIcons(...C),s.addAliases(...D)}export{E as loadTextEditIconSet,D as textEditCollectionAliases,C as textEditCollectionIcons};\n"], "mappings": "AAAA,SAAOA,YAAY,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,iBAAiB,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,kBAAkB,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,wBAAwB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,wBAAwB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,iBAAiB,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,wBAAwB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,wBAAwB;AAAC,MAAMC,CAAC,GAAC,CAACvD,CAAC,EAACE,CAAC,EAACI,CAAC,EAACF,CAAC,EAACI,CAAC,EAACI,CAAC,EAACF,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACF,CAAC,EAACI,CAAC,CAAC;EAACE,CAAC,GAAC,EAAE;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC3D,CAAC,CAAC4D,QAAQ,CAAC,GAAGH,CAAC,CAAC,EAACzD,CAAC,CAAC6D,UAAU,CAAC,GAAGH,CAAC,CAAC;AAAA;AAAC,SAAOC,CAAC,IAAIG,mBAAmB,EAACJ,CAAC,IAAIK,yBAAyB,EAACN,CAAC,IAAIO,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}