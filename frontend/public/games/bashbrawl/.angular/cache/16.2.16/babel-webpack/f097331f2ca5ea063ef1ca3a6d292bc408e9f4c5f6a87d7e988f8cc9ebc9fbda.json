{"ast": null, "code": "import { renderIcon as L } from \"../icon.renderer.js\";\nconst C = \"half-star\",\n  r = [\"half-star\", L({\n    outline: '<path d=\"M33.95 16.1241C33.83 15.7643 33.52 15.5144 33.15 15.4544L23.31 13.9549L18.91 4.56829C18.74 4.21842 18.39 3.9885 18 3.9885C17.61 3.9885 17.26 4.20842 17.09 4.56829L12.68 13.9549L2.84996 15.4544C2.47996 15.5144 2.16996 15.7743 2.04996 16.1241C1.92996 16.474 2.01996 16.8739 2.27996 17.1438L9.42996 24.4711L7.73996 34.8274C7.67996 35.2073 7.83996 35.5871 8.14996 35.8071C8.45996 36.027 8.87996 36.047 9.20996 35.867L18 31.0088L26.79 35.867C26.94 35.947 27.11 35.987 27.27 35.987C27.47 35.987 27.67 35.927 27.85 35.8071C28.16 35.5871 28.32 35.2073 28.26 34.8274L26.57 24.4711L33.72 17.1438C33.98 16.8739 34.07 16.484 33.95 16.1241ZM18 28.8496C17.83 28.8496 17.67 28.8895 17.52 28.9695L10.05 33.098L11.49 24.2812C11.54 23.9713 11.44 23.6514 11.22 23.4215L5.07996 17.1338L13.52 15.8442C13.85 15.7943 14.13 15.5843 14.27 15.2844L18 7.3373V28.8496Z\"/>',\n    solid: '<path d=\"M33.95 14.1356C33.83 13.7758 33.52 13.5259 33.15 13.4659L23.31 11.9664L18.91 2.57979C18.74 2.22992 18.39 2 18 2C17.61 2 17.26 2.21992 17.09 2.57979L12.68 11.9664L2.84996 13.4659C2.47996 13.5259 2.16996 13.7858 2.04996 14.1356C1.92996 14.4855 2.01996 14.8854 2.27996 15.1553L9.42996 22.4826L7.73996 32.8389C7.67996 33.2188 7.83996 33.5986 8.14996 33.8186C8.45996 34.0385 8.87996 34.0585 9.20996 33.8785L18 29.0203L26.79 33.8785C26.94 33.9585 27.11 33.9985 27.27 33.9985C27.47 33.9985 27.67 33.9385 27.85 33.8186C28.16 33.5986 28.32 33.2188 28.26 32.8389L26.57 22.4826L33.72 15.1553C33.98 14.8854 34.07 14.4955 33.95 14.1356ZM24.79 21.443C24.57 21.6729 24.47 21.9928 24.52 22.3027L25.96 31.1195L18.49 26.991C18.34 26.911 18.17 26.8711 18.01 26.8711V5.3488L21.74 13.2959C21.88 13.5958 22.16 13.8058 22.49 13.8557L30.93 15.1453L24.79 21.433V21.443Z\"/>'\n  })];\nexport { r as halfStarIcon, C as halfStarIconName };", "map": {"version": 3, "names": ["renderIcon", "L", "C", "r", "outline", "solid", "halfStarIcon", "halfStarIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/half-star.js"], "sourcesContent": ["import{renderIcon as L}from\"../icon.renderer.js\";const C=\"half-star\",r=[\"half-star\",L({outline:'<path d=\"M33.95 16.1241C33.83 15.7643 33.52 15.5144 33.15 15.4544L23.31 13.9549L18.91 4.56829C18.74 4.21842 18.39 3.9885 18 3.9885C17.61 3.9885 17.26 4.20842 17.09 4.56829L12.68 13.9549L2.84996 15.4544C2.47996 15.5144 2.16996 15.7743 2.04996 16.1241C1.92996 16.474 2.01996 16.8739 2.27996 17.1438L9.42996 24.4711L7.73996 34.8274C7.67996 35.2073 7.83996 35.5871 8.14996 35.8071C8.45996 36.027 8.87996 36.047 9.20996 35.867L18 31.0088L26.79 35.867C26.94 35.947 27.11 35.987 27.27 35.987C27.47 35.987 27.67 35.927 27.85 35.8071C28.16 35.5871 28.32 35.2073 28.26 34.8274L26.57 24.4711L33.72 17.1438C33.98 16.8739 34.07 16.484 33.95 16.1241ZM18 28.8496C17.83 28.8496 17.67 28.8895 17.52 28.9695L10.05 33.098L11.49 24.2812C11.54 23.9713 11.44 23.6514 11.22 23.4215L5.07996 17.1338L13.52 15.8442C13.85 15.7943 14.13 15.5843 14.27 15.2844L18 7.3373V28.8496Z\"/>',solid:'<path d=\"M33.95 14.1356C33.83 13.7758 33.52 13.5259 33.15 13.4659L23.31 11.9664L18.91 2.57979C18.74 2.22992 18.39 2 18 2C17.61 2 17.26 2.21992 17.09 2.57979L12.68 11.9664L2.84996 13.4659C2.47996 13.5259 2.16996 13.7858 2.04996 14.1356C1.92996 14.4855 2.01996 14.8854 2.27996 15.1553L9.42996 22.4826L7.73996 32.8389C7.67996 33.2188 7.83996 33.5986 8.14996 33.8186C8.45996 34.0385 8.87996 34.0585 9.20996 33.8785L18 29.0203L26.79 33.8785C26.94 33.9585 27.11 33.9985 27.27 33.9985C27.47 33.9985 27.67 33.9385 27.85 33.8186C28.16 33.5986 28.32 33.2188 28.26 32.8389L26.57 22.4826L33.72 15.1553C33.98 14.8854 34.07 14.4955 33.95 14.1356ZM24.79 21.443C24.57 21.6729 24.47 21.9928 24.52 22.3027L25.96 31.1195L18.49 26.991C18.34 26.911 18.17 26.8711 18.01 26.8711V5.3488L21.74 13.2959C21.88 13.5958 22.16 13.8058 22.49 13.8557L30.93 15.1453L24.79 21.433V21.443Z\"/>'})];export{r as halfStarIcon,C as halfStarIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,WAAW;EAACC,CAAC,GAAC,CAAC,WAAW,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,s1BAAs1B;IAACC,KAAK,EAAC;EAA01B,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,YAAY,EAACJ,CAAC,IAAIK,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}