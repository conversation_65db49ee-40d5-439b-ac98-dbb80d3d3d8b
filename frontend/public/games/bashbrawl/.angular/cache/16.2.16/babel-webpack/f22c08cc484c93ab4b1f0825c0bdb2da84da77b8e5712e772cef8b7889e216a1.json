{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst V = \"library\",\n  L = [\"library\", C({\n    outline: '<path d=\"M31.8902 28.69L24.5328 13.09C24.3028 12.59 23.7031 12.38 23.2032 12.61L19.9844 14.13V8.01C19.9844 7.46 19.5345 7.01 18.9847 7.01H13.9865V5C13.9865 4.45 13.5366 4 12.9868 4H4.99965C4.44984 4 4 4.45 4 5V31C4 31.55 4.44984 32 4.99965 32H18.9947C19.5445 32 19.9944 31.55 19.9944 31V18.49L26.0922 31.42C26.2622 31.78 26.622 31.99 27.0019 31.99C27.1418 31.99 27.2918 31.96 27.4317 31.89L31.4303 30.01C31.6702 29.9 31.8502 29.69 31.9402 29.44C32.0301 29.19 32.0201 28.92 31.9002 28.68L31.8902 28.69ZM11.9972 8V30H5.9993V6H11.9972V8ZM17.9951 30H13.9965V9H17.9951V30ZM27.4717 29.67L20.974 15.88L23.1632 14.85L29.661 28.64L27.4717 29.67Z\"/>',\n    solid: '<path d=\"M11.0025 4H5.00036C4.45016 4 4 4.45 4 5V31C4 31.55 4.45016 32 5.00036 32H11.0025C11.5527 32 12.0029 31.55 12.0029 31V5C12.0029 4.45 11.5527 4 11.0025 4ZM31.9101 29.09L24.5474 13.49C24.3174 12.99 23.7171 12.78 23.217 13.01L19.9958 14.53V8C19.9958 7.45 19.5456 7 18.9954 7H13.9936V32H18.9954C19.5456 32 19.9958 31.55 19.9958 31V14.87L28.0787 32L31.4299 30.42C31.67 30.31 31.8501 30.1 31.9401 29.85C32.0301 29.6 32.0201 29.33 31.9001 29.09H31.9101Z\"/>'\n  })];\nexport { L as libraryIcon, V as libraryIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "V", "L", "outline", "solid", "libraryIcon", "libraryIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/library.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const V=\"library\",L=[\"library\",C({outline:'<path d=\"M31.8902 28.69L24.5328 13.09C24.3028 12.59 23.7031 12.38 23.2032 12.61L19.9844 14.13V8.01C19.9844 7.46 19.5345 7.01 18.9847 7.01H13.9865V5C13.9865 4.45 13.5366 4 12.9868 4H4.99965C4.44984 4 4 4.45 4 5V31C4 31.55 4.44984 32 4.99965 32H18.9947C19.5445 32 19.9944 31.55 19.9944 31V18.49L26.0922 31.42C26.2622 31.78 26.622 31.99 27.0019 31.99C27.1418 31.99 27.2918 31.96 27.4317 31.89L31.4303 30.01C31.6702 29.9 31.8502 29.69 31.9402 29.44C32.0301 29.19 32.0201 28.92 31.9002 28.68L31.8902 28.69ZM11.9972 8V30H5.9993V6H11.9972V8ZM17.9951 30H13.9965V9H17.9951V30ZM27.4717 29.67L20.974 15.88L23.1632 14.85L29.661 28.64L27.4717 29.67Z\"/>',solid:'<path d=\"M11.0025 4H5.00036C4.45016 4 4 4.45 4 5V31C4 31.55 4.45016 32 5.00036 32H11.0025C11.5527 32 12.0029 31.55 12.0029 31V5C12.0029 4.45 11.5527 4 11.0025 4ZM31.9101 29.09L24.5474 13.49C24.3174 12.99 23.7171 12.78 23.217 13.01L19.9958 14.53V8C19.9958 7.45 19.5456 7 18.9954 7H13.9936V32H18.9954C19.5456 32 19.9958 31.55 19.9958 31V14.87L28.0787 32L31.4299 30.42C31.67 30.31 31.8501 30.1 31.9401 29.85C32.0301 29.6 32.0201 29.33 31.9001 29.09H31.9101Z\"/>'})];export{L as libraryIcon,V as libraryIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,SAAS;EAACC,CAAC,GAAC,CAAC,SAAS,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,ioBAAioB;IAACC,KAAK,EAAC;EAA2c,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,WAAW,EAACJ,CAAC,IAAIK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}