{"ast": null, "code": "export default function _arity(n, fn) {\n  /* eslint-disable no-unused-vars */\n  switch (n) {\n    case 0:\n      return function () {\n        return fn.apply(this, arguments);\n      };\n    case 1:\n      return function (a0) {\n        return fn.apply(this, arguments);\n      };\n    case 2:\n      return function (a0, a1) {\n        return fn.apply(this, arguments);\n      };\n    case 3:\n      return function (a0, a1, a2) {\n        return fn.apply(this, arguments);\n      };\n    case 4:\n      return function (a0, a1, a2, a3) {\n        return fn.apply(this, arguments);\n      };\n    case 5:\n      return function (a0, a1, a2, a3, a4) {\n        return fn.apply(this, arguments);\n      };\n    case 6:\n      return function (a0, a1, a2, a3, a4, a5) {\n        return fn.apply(this, arguments);\n      };\n    case 7:\n      return function (a0, a1, a2, a3, a4, a5, a6) {\n        return fn.apply(this, arguments);\n      };\n    case 8:\n      return function (a0, a1, a2, a3, a4, a5, a6, a7) {\n        return fn.apply(this, arguments);\n      };\n    case 9:\n      return function (a0, a1, a2, a3, a4, a5, a6, a7, a8) {\n        return fn.apply(this, arguments);\n      };\n    case 10:\n      return function (a0, a1, a2, a3, a4, a5, a6, a7, a8, a9) {\n        return fn.apply(this, arguments);\n      };\n    default:\n      throw new Error('First argument to _arity must be a non-negative integer no greater than ten');\n  }\n}", "map": {"version": 3, "names": ["_arity", "n", "fn", "apply", "arguments", "a0", "a1", "a2", "a3", "a4", "a5", "a6", "a7", "a8", "a9", "Error"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_arity.js"], "sourcesContent": ["export default function _arity(n, fn) {\n  /* eslint-disable no-unused-vars */\n  switch (n) {\n    case 0:\n      return function () {\n        return fn.apply(this, arguments);\n      };\n\n    case 1:\n      return function (a0) {\n        return fn.apply(this, arguments);\n      };\n\n    case 2:\n      return function (a0, a1) {\n        return fn.apply(this, arguments);\n      };\n\n    case 3:\n      return function (a0, a1, a2) {\n        return fn.apply(this, arguments);\n      };\n\n    case 4:\n      return function (a0, a1, a2, a3) {\n        return fn.apply(this, arguments);\n      };\n\n    case 5:\n      return function (a0, a1, a2, a3, a4) {\n        return fn.apply(this, arguments);\n      };\n\n    case 6:\n      return function (a0, a1, a2, a3, a4, a5) {\n        return fn.apply(this, arguments);\n      };\n\n    case 7:\n      return function (a0, a1, a2, a3, a4, a5, a6) {\n        return fn.apply(this, arguments);\n      };\n\n    case 8:\n      return function (a0, a1, a2, a3, a4, a5, a6, a7) {\n        return fn.apply(this, arguments);\n      };\n\n    case 9:\n      return function (a0, a1, a2, a3, a4, a5, a6, a7, a8) {\n        return fn.apply(this, arguments);\n      };\n\n    case 10:\n      return function (a0, a1, a2, a3, a4, a5, a6, a7, a8, a9) {\n        return fn.apply(this, arguments);\n      };\n\n    default:\n      throw new Error('First argument to _arity must be a non-negative integer no greater than ten');\n  }\n}"], "mappings": "AAAA,eAAe,SAASA,MAAMA,CAACC,CAAC,EAAEC,EAAE,EAAE;EACpC;EACA,QAAQD,CAAC;IACP,KAAK,CAAC;MACJ,OAAO,YAAY;QACjB,OAAOC,EAAE,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAClC,CAAC;IAEH,KAAK,CAAC;MACJ,OAAO,UAAUC,EAAE,EAAE;QACnB,OAAOH,EAAE,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAClC,CAAC;IAEH,KAAK,CAAC;MACJ,OAAO,UAAUC,EAAE,EAAEC,EAAE,EAAE;QACvB,OAAOJ,EAAE,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAClC,CAAC;IAEH,KAAK,CAAC;MACJ,OAAO,UAAUC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;QAC3B,OAAOL,EAAE,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAClC,CAAC;IAEH,KAAK,CAAC;MACJ,OAAO,UAAUC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;QAC/B,OAAON,EAAE,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAClC,CAAC;IAEH,KAAK,CAAC;MACJ,OAAO,UAAUC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;QACnC,OAAOP,EAAE,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAClC,CAAC;IAEH,KAAK,CAAC;MACJ,OAAO,UAAUC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;QACvC,OAAOR,EAAE,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAClC,CAAC;IAEH,KAAK,CAAC;MACJ,OAAO,UAAUC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;QAC3C,OAAOT,EAAE,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAClC,CAAC;IAEH,KAAK,CAAC;MACJ,OAAO,UAAUC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;QAC/C,OAAOV,EAAE,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAClC,CAAC;IAEH,KAAK,CAAC;MACJ,OAAO,UAAUC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;QACnD,OAAOX,EAAE,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAClC,CAAC;IAEH,KAAK,EAAE;MACL,OAAO,UAAUC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;QACvD,OAAOZ,EAAE,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAClC,CAAC;IAEH;MACE,MAAM,IAAIW,KAAK,CAAC,6EAA6E,CAAC;EAClG;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}