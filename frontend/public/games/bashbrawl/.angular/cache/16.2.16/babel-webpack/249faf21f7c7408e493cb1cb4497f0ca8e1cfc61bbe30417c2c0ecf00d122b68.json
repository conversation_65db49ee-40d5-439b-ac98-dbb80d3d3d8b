{"ast": null, "code": "import _has from \"./_has.js\";\nvar toString = Object.prototype.toString;\nvar _isArguments = /*#__PURE__*/\nfunction () {\n  return toString.call(arguments) === '[object Arguments]' ? function _isArguments(x) {\n    return toString.call(x) === '[object Arguments]';\n  } : function _isArguments(x) {\n    return _has('callee', x);\n  };\n}();\nexport default _isArguments;", "map": {"version": 3, "names": ["_has", "toString", "Object", "prototype", "_isArguments", "call", "arguments", "x"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_isArguments.js"], "sourcesContent": ["import _has from \"./_has.js\";\nvar toString = Object.prototype.toString;\n\nvar _isArguments =\n/*#__PURE__*/\nfunction () {\n  return toString.call(arguments) === '[object Arguments]' ? function _isArguments(x) {\n    return toString.call(x) === '[object Arguments]';\n  } : function _isArguments(x) {\n    return _has('callee', x);\n  };\n}();\n\nexport default _isArguments;"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAC5B,IAAIC,QAAQ,GAAGC,MAAM,CAACC,SAAS,CAACF,QAAQ;AAExC,IAAIG,YAAY,GAChB;AACA,YAAY;EACV,OAAOH,QAAQ,CAACI,IAAI,CAACC,SAAS,CAAC,KAAK,oBAAoB,GAAG,SAASF,YAAYA,CAACG,CAAC,EAAE;IAClF,OAAON,QAAQ,CAACI,IAAI,CAACE,CAAC,CAAC,KAAK,oBAAoB;EAClD,CAAC,GAAG,SAASH,YAAYA,CAACG,CAAC,EAAE;IAC3B,OAAOP,IAAI,CAAC,QAAQ,EAAEO,CAAC,CAAC;EAC1B,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,eAAeH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}