{"ast": null, "code": "import { __decorate as t } from \"tslib\";\nimport { html as i } from \"lit\";\nimport { CdsBaseButton as e, I18nService as s, LogService as o, baseStyles as r, property as n, i18n as a, state as d } from \"@cds/core/internal\";\nimport p from \"./button-action.element.scss.js\";\nclass c extends e {\n  constructor() {\n    super(...arguments);\n    this.i18n = s.keys.actions, this.cdsButtonAction = !0;\n  }\n  render() {\n    return i`<div class=\"private-host\"><slot><cds-icon .shape=\"${this.shape ? this.shape : \"ellipsis-vertical\"}\" .size=\"${this.iconSize}\" ?solid=\"${this.pressed || this.expanded}\" inner-offset=\"${1}\"></cds-icon></slot></div>`;\n  }\n  updated(t) {\n    super.updated(t), this.ariaLabel || this.readonly || o.warn(\"A aria-label is required for interactive cds-button-action type\", this), t.has(\"readonly\") && (this.readonly && !this.hasAttribute(\"aria-label\") ? this.ariaHidden = \"true\" : this.ariaHidden = null);\n  }\n}\nc.styles = [r, p], t([n({\n  type: String\n})], c.prototype, \"shape\", void 0), t([n({\n  type: String,\n  reflect: !0\n})], c.prototype, \"action\", void 0), t([n({\n  type: String\n})], c.prototype, \"iconSize\", void 0), t([a()], c.prototype, \"i18n\", void 0), t([d({\n  type: Boolean,\n  reflect: !0,\n  attribute: \"cds-button-action\"\n})], c.prototype, \"cdsButtonAction\", void 0);\nexport { c as CdsButtonAction };", "map": {"version": 3, "names": ["__decorate", "t", "html", "i", "CdsBaseButton", "e", "I18nService", "s", "LogService", "o", "baseStyles", "r", "property", "n", "i18n", "a", "state", "d", "p", "c", "constructor", "arguments", "keys", "actions", "cdsButtonAction", "render", "shape", "iconSize", "pressed", "expanded", "updated", "aria<PERSON><PERSON><PERSON>", "readonly", "warn", "has", "hasAttribute", "ariaHidden", "styles", "type", "String", "prototype", "reflect", "Boolean", "attribute", "CdsButtonAction"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/button-action/button-action.element.js"], "sourcesContent": ["import{__decorate as t}from\"tslib\";import{html as i}from\"lit\";import{CdsBaseButton as e,I18nService as s,LogService as o,baseStyles as r,property as n,i18n as a,state as d}from\"@cds/core/internal\";import p from\"./button-action.element.scss.js\";class c extends e{constructor(){super(...arguments);this.i18n=s.keys.actions,this.cdsButtonAction=!0}render(){return i`<div class=\"private-host\"><slot><cds-icon .shape=\"${this.shape?this.shape:\"ellipsis-vertical\"}\" .size=\"${this.iconSize}\" ?solid=\"${this.pressed||this.expanded}\" inner-offset=\"${1}\"></cds-icon></slot></div>`}updated(t){super.updated(t),this.ariaLabel||this.readonly||o.warn(\"A aria-label is required for interactive cds-button-action type\",this),t.has(\"readonly\")&&(this.readonly&&!this.hasAttribute(\"aria-label\")?this.ariaHidden=\"true\":this.ariaHidden=null)}}c.styles=[r,p],t([n({type:String})],c.prototype,\"shape\",void 0),t([n({type:String,reflect:!0})],c.prototype,\"action\",void 0),t([n({type:String})],c.prototype,\"iconSize\",void 0),t([a()],c.prototype,\"i18n\",void 0),t([d({type:Boolean,reflect:!0,attribute:\"cds-button-action\"})],c.prototype,\"cdsButtonAction\",void 0);export{c as CdsButtonAction};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,IAAI,IAAIC,CAAC,QAAK,KAAK;AAAC,SAAOC,aAAa,IAAIC,CAAC,EAACC,WAAW,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,EAACC,IAAI,IAAIC,CAAC,EAACC,KAAK,IAAIC,CAAC,QAAK,oBAAoB;AAAC,OAAOC,CAAC,MAAK,iCAAiC;AAAC,MAAMC,CAAC,SAASd,CAAC;EAACe,WAAWA,CAAA,EAAE;IAAC,KAAK,CAAC,GAAGC,SAAS,CAAC;IAAC,IAAI,CAACP,IAAI,GAACP,CAAC,CAACe,IAAI,CAACC,OAAO,EAAC,IAAI,CAACC,eAAe,GAAC,CAAC,CAAC;EAAA;EAACC,MAAMA,CAAA,EAAE;IAAC,OAAOtB,CAAE,qDAAoD,IAAI,CAACuB,KAAK,GAAC,IAAI,CAACA,KAAK,GAAC,mBAAoB,YAAW,IAAI,CAACC,QAAS,aAAY,IAAI,CAACC,OAAO,IAAE,IAAI,CAACC,QAAS,mBAAkB,CAAE,4BAA2B;EAAA;EAACC,OAAOA,CAAC7B,CAAC,EAAC;IAAC,KAAK,CAAC6B,OAAO,CAAC7B,CAAC,CAAC,EAAC,IAAI,CAAC8B,SAAS,IAAE,IAAI,CAACC,QAAQ,IAAEvB,CAAC,CAACwB,IAAI,CAAC,iEAAiE,EAAC,IAAI,CAAC,EAAChC,CAAC,CAACiC,GAAG,CAAC,UAAU,CAAC,KAAG,IAAI,CAACF,QAAQ,IAAE,CAAC,IAAI,CAACG,YAAY,CAAC,YAAY,CAAC,GAAC,IAAI,CAACC,UAAU,GAAC,MAAM,GAAC,IAAI,CAACA,UAAU,GAAC,IAAI,CAAC;EAAA;AAAC;AAACjB,CAAC,CAACkB,MAAM,GAAC,CAAC1B,CAAC,EAACO,CAAC,CAAC,EAACjB,CAAC,CAAC,CAACY,CAAC,CAAC;EAACyB,IAAI,EAACC;AAAM,CAAC,CAAC,CAAC,EAACpB,CAAC,CAACqB,SAAS,EAAC,OAAO,EAAC,KAAK,CAAC,CAAC,EAACvC,CAAC,CAAC,CAACY,CAAC,CAAC;EAACyB,IAAI,EAACC,MAAM;EAACE,OAAO,EAAC,CAAC;AAAC,CAAC,CAAC,CAAC,EAACtB,CAAC,CAACqB,SAAS,EAAC,QAAQ,EAAC,KAAK,CAAC,CAAC,EAACvC,CAAC,CAAC,CAACY,CAAC,CAAC;EAACyB,IAAI,EAACC;AAAM,CAAC,CAAC,CAAC,EAACpB,CAAC,CAACqB,SAAS,EAAC,UAAU,EAAC,KAAK,CAAC,CAAC,EAACvC,CAAC,CAAC,CAACc,CAAC,CAAC,CAAC,CAAC,EAACI,CAAC,CAACqB,SAAS,EAAC,MAAM,EAAC,KAAK,CAAC,CAAC,EAACvC,CAAC,CAAC,CAACgB,CAAC,CAAC;EAACqB,IAAI,EAACI,OAAO;EAACD,OAAO,EAAC,CAAC,CAAC;EAACE,SAAS,EAAC;AAAmB,CAAC,CAAC,CAAC,EAACxB,CAAC,CAACqB,SAAS,EAAC,iBAAiB,EAAC,KAAK,CAAC,CAAC;AAAC,SAAOrB,CAAC,IAAIyB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}