{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"download-cloud\",\n  d = [\"download-cloud\", C({\n    outline: '<path d=\"M21.78 27.35L19.08 30.01V16.04C19.08 15.46 18.61 15 18.04 15C17.47 15 17 15.47 17 16.04V30.01L14.32 27.33C14.12 27.14 13.85 27.03 13.58 27.04C13.15 27.04 12.77 27.29 12.6 27.68C12.43 28.07 12.52 28.53 12.83 28.83L18.05 34.05L23.27 28.83C23.62 28.42 23.6 27.8 23.22 27.41C22.84 27.02 22.22 27 21.8 27.36L21.78 27.35ZM29.92 13.83C29.98 13.39 30 12.94 30 12.5C30 6.71 25.29 2 19.5 2C14.95 2 10.94 4.94 9.54 9.21C5.2 10.13 2 14.01 2 18.5C2 23.4 5.73 27.45 10.51 27.95C10.53 27.6 10.6 27.24 10.74 26.9C10.89 26.55 11.09 26.25 11.34 25.99C7.28 25.9 4 22.58 4 18.5C4 14.78 6.78 11.59 10.46 11.08L11.12 10.99L11.29 10.34C12.27 6.6 15.64 4 19.5 4C24.19 4 28 7.81 28 12.5C28 13.08 27.94 13.66 27.82 14.23L27.65 15.06L28.45 15.36C30.57 16.17 32 18.23 32 20.5C32 23.53 29.53 26 26.5 26H24.61C25.16 26.55 25.45 27.27 25.48 28H26.49C30.63 28 33.99 24.64 33.99 20.5C33.99 17.69 32.38 15.1 29.91 13.83H29.92Z\"/>',\n    outlineAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M23.8121 2.92206C22.4973 2.32979 21.0392 2 19.5055 2C14.954 2 10.9428 4.93541 9.54236 9.19875C5.201 10.1173 2 13.9913 2 18.4743C2 23.3666 5.73117 27.4103 10.5127 27.9095C10.5327 27.5601 10.6027 27.2006 10.7427 26.8612C10.8928 26.5117 11.0928 26.2122 11.3429 25.9526C7.28165 25.8627 4.00063 22.5479 4.00063 18.4743C4.00063 14.7601 6.78149 11.575 10.4626 11.0658L11.1229 10.976L11.2929 10.327C12.2732 6.59282 15.6443 3.99688 19.5055 3.99688C20.6635 3.99688 21.7678 4.22864 22.7745 4.64817L23.8121 2.92206Z\"/><path d=\"M27.6628 15.0163H31.6242C33.1044 16.4088 34 18.3789 34 20.4711C34 24.6047 30.639 27.9594 26.4977 27.9594H25.4873C25.4573 27.2306 25.1672 26.5117 24.6171 25.9626H26.5077C29.5386 25.9626 32.0094 23.4964 32.0094 20.4711C32.0094 18.2047 30.5789 16.1479 28.4583 15.3392L27.658 15.0396L27.6628 15.0163Z\"/><path d=\"M19.0853 29.9663L21.7862 27.3105L21.8062 27.3204C22.2263 26.961 22.8465 26.981 23.2266 27.3704C23.6068 27.7598 23.6268 28.3788 23.2767 28.7881L18.055 34L12.8334 28.7881C12.5233 28.4886 12.4333 28.0293 12.6033 27.6399C12.7734 27.2505 13.1535 27.0009 13.5836 27.0009C13.8537 26.991 14.1238 27.1008 14.3239 27.2905L17.0047 29.9663V16.0181C17.0047 15.449 17.4748 14.9797 18.045 14.9797C18.6152 14.9797 19.0853 15.439 19.0853 16.0181V29.9663Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M23.7344 2.88747C22.44 2.31696 21.0091 2 19.5055 2C14.954 2 10.9428 4.93541 9.54236 9.19875C5.201 10.1173 2 13.9913 2 18.4743C2 23.3666 5.73117 27.4103 10.5127 27.9095C10.5327 27.5601 10.6027 27.2006 10.7427 26.8612C10.8928 26.5117 11.0928 26.2122 11.3429 25.9526C7.28165 25.8627 4.00063 22.5479 4.00063 18.4743C4.00063 14.7601 6.78149 11.575 10.4626 11.0658L11.1229 10.976L11.2929 10.327C12.2732 6.59282 15.6443 3.99688 19.5055 3.99688C20.7932 3.99688 22.0145 4.28346 23.109 4.79613C23.2256 4.12192 23.439 3.48077 23.7344 2.88747Z\"/><path d=\"M28.0055 12.6926C28.6373 12.8806 29.3064 12.9819 29.9992 12.9828C29.988 13.2597 29.9662 13.5376 29.9287 13.8115H29.9187C32.3895 15.0796 34 17.6655 34 20.4711C34 24.6047 30.639 27.9594 26.4977 27.9594H25.4873C25.4573 27.2306 25.1672 26.5117 24.6171 25.9626H26.5077C29.5386 25.9626 32.0094 23.4964 32.0094 20.4711C32.0094 18.2047 30.5789 16.1479 28.4583 15.3392L27.658 15.0396L27.8281 14.2109C27.9337 13.7103 27.9928 13.2019 28.0055 12.6926Z\"/><path d=\"M19.0853 29.9663L21.7862 27.3105L21.8062 27.3204C22.2263 26.961 22.8465 26.981 23.2266 27.3704C23.6068 27.7598 23.6268 28.3788 23.2767 28.7881L18.055 34L12.8334 28.7881C12.5233 28.4886 12.4333 28.0293 12.6033 27.6399C12.7734 27.2505 13.1535 27.0009 13.5836 27.0009C13.8537 26.991 14.1238 27.1008 14.3239 27.2905L17.0047 29.9663V16.0181C17.0047 15.449 17.4748 14.9797 18.045 14.9797C18.6152 14.9797 19.0853 15.439 19.0853 16.0181V29.9663Z\"/>'\n  })];\nexport { d as downloadCloudIcon, L as downloadCloudIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "d", "outline", "outlineAlerted", "outlineBadged", "downloadCloudIcon", "downloadCloudIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/download-cloud.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"download-cloud\",d=[\"download-cloud\",C({outline:'<path d=\"M21.78 27.35L19.08 30.01V16.04C19.08 15.46 18.61 15 18.04 15C17.47 15 17 15.47 17 16.04V30.01L14.32 27.33C14.12 27.14 13.85 27.03 13.58 27.04C13.15 27.04 12.77 27.29 12.6 27.68C12.43 28.07 12.52 28.53 12.83 28.83L18.05 34.05L23.27 28.83C23.62 28.42 23.6 27.8 23.22 27.41C22.84 27.02 22.22 27 21.8 27.36L21.78 27.35ZM29.92 13.83C29.98 13.39 30 12.94 30 12.5C30 6.71 25.29 2 19.5 2C14.95 2 10.94 4.94 9.54 9.21C5.2 10.13 2 14.01 2 18.5C2 23.4 5.73 27.45 10.51 27.95C10.53 27.6 10.6 27.24 10.74 26.9C10.89 26.55 11.09 26.25 11.34 25.99C7.28 25.9 4 22.58 4 18.5C4 14.78 6.78 11.59 10.46 11.08L11.12 10.99L11.29 10.34C12.27 6.6 15.64 4 19.5 4C24.19 4 28 7.81 28 12.5C28 13.08 27.94 13.66 27.82 14.23L27.65 15.06L28.45 15.36C30.57 16.17 32 18.23 32 20.5C32 23.53 29.53 26 26.5 26H24.61C25.16 26.55 25.45 27.27 25.48 28H26.49C30.63 28 33.99 24.64 33.99 20.5C33.99 17.69 32.38 15.1 29.91 13.83H29.92Z\"/>',outlineAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M23.8121 2.92206C22.4973 2.32979 21.0392 2 19.5055 2C14.954 2 10.9428 4.93541 9.54236 9.19875C5.201 10.1173 2 13.9913 2 18.4743C2 23.3666 5.73117 27.4103 10.5127 27.9095C10.5327 27.5601 10.6027 27.2006 10.7427 26.8612C10.8928 26.5117 11.0928 26.2122 11.3429 25.9526C7.28165 25.8627 4.00063 22.5479 4.00063 18.4743C4.00063 14.7601 6.78149 11.575 10.4626 11.0658L11.1229 10.976L11.2929 10.327C12.2732 6.59282 15.6443 3.99688 19.5055 3.99688C20.6635 3.99688 21.7678 4.22864 22.7745 4.64817L23.8121 2.92206Z\"/><path d=\"M27.6628 15.0163H31.6242C33.1044 16.4088 34 18.3789 34 20.4711C34 24.6047 30.639 27.9594 26.4977 27.9594H25.4873C25.4573 27.2306 25.1672 26.5117 24.6171 25.9626H26.5077C29.5386 25.9626 32.0094 23.4964 32.0094 20.4711C32.0094 18.2047 30.5789 16.1479 28.4583 15.3392L27.658 15.0396L27.6628 15.0163Z\"/><path d=\"M19.0853 29.9663L21.7862 27.3105L21.8062 27.3204C22.2263 26.961 22.8465 26.981 23.2266 27.3704C23.6068 27.7598 23.6268 28.3788 23.2767 28.7881L18.055 34L12.8334 28.7881C12.5233 28.4886 12.4333 28.0293 12.6033 27.6399C12.7734 27.2505 13.1535 27.0009 13.5836 27.0009C13.8537 26.991 14.1238 27.1008 14.3239 27.2905L17.0047 29.9663V16.0181C17.0047 15.449 17.4748 14.9797 18.045 14.9797C18.6152 14.9797 19.0853 15.439 19.0853 16.0181V29.9663Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M23.7344 2.88747C22.44 2.31696 21.0091 2 19.5055 2C14.954 2 10.9428 4.93541 9.54236 9.19875C5.201 10.1173 2 13.9913 2 18.4743C2 23.3666 5.73117 27.4103 10.5127 27.9095C10.5327 27.5601 10.6027 27.2006 10.7427 26.8612C10.8928 26.5117 11.0928 26.2122 11.3429 25.9526C7.28165 25.8627 4.00063 22.5479 4.00063 18.4743C4.00063 14.7601 6.78149 11.575 10.4626 11.0658L11.1229 10.976L11.2929 10.327C12.2732 6.59282 15.6443 3.99688 19.5055 3.99688C20.7932 3.99688 22.0145 4.28346 23.109 4.79613C23.2256 4.12192 23.439 3.48077 23.7344 2.88747Z\"/><path d=\"M28.0055 12.6926C28.6373 12.8806 29.3064 12.9819 29.9992 12.9828C29.988 13.2597 29.9662 13.5376 29.9287 13.8115H29.9187C32.3895 15.0796 34 17.6655 34 20.4711C34 24.6047 30.639 27.9594 26.4977 27.9594H25.4873C25.4573 27.2306 25.1672 26.5117 24.6171 25.9626H26.5077C29.5386 25.9626 32.0094 23.4964 32.0094 20.4711C32.0094 18.2047 30.5789 16.1479 28.4583 15.3392L27.658 15.0396L27.8281 14.2109C27.9337 13.7103 27.9928 13.2019 28.0055 12.6926Z\"/><path d=\"M19.0853 29.9663L21.7862 27.3105L21.8062 27.3204C22.2263 26.961 22.8465 26.981 23.2266 27.3704C23.6068 27.7598 23.6268 28.3788 23.2767 28.7881L18.055 34L12.8334 28.7881C12.5233 28.4886 12.4333 28.0293 12.6033 27.6399C12.7734 27.2505 13.1535 27.0009 13.5836 27.0009C13.8537 26.991 14.1238 27.1008 14.3239 27.2905L17.0047 29.9663V16.0181C17.0047 15.449 17.4748 14.9797 18.045 14.9797C18.6152 14.9797 19.0853 15.439 19.0853 16.0181V29.9663Z\"/>'})];export{d as downloadCloudIcon,L as downloadCloudIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,gBAAgB;EAACC,CAAC,GAAC,CAAC,gBAAgB,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,04BAA04B;IAACC,cAAc,EAAC,6lDAA6lD;IAACC,aAAa,EAAC;EAAmiD,CAAC,CAAC,CAAC;AAAC,SAAOH,CAAC,IAAII,iBAAiB,EAACL,CAAC,IAAIM,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}