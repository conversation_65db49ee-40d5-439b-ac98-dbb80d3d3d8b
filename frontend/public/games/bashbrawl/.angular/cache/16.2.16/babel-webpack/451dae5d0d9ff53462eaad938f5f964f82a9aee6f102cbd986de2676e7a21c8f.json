{"ast": null, "code": "const i = \"cds-tree-item-expand\",\n  t = [{\n    target: \".item-children\",\n    animation: [{\n      opacity: 0,\n      height: \"0\"\n    }, {\n      opacity: 1,\n      height: \"from:item-children\"\n    }],\n    options: {\n      duration: \"--animation-duration\",\n      easing: \"--animation-easing\",\n      fill: \"forwards\"\n    }\n  }];\nexport { t as AnimationTreeItemExpandConfig, i as AnimationTreeItemExpandName };", "map": {"version": 3, "names": ["i", "t", "target", "animation", "opacity", "height", "options", "duration", "easing", "fill", "AnimationTreeItemExpandConfig", "AnimationTreeItemExpandName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/motion/animations/cds-tree-item-expand.js"], "sourcesContent": ["const i=\"cds-tree-item-expand\",t=[{target:\".item-children\",animation:[{opacity:0,height:\"0\"},{opacity:1,height:\"from:item-children\"}],options:{duration:\"--animation-duration\",easing:\"--animation-easing\",fill:\"forwards\"}}];export{t as AnimationTreeItemExpandConfig,i as AnimationTreeItemExpandName};\n"], "mappings": "AAAA,MAAMA,CAAC,GAAC,sBAAsB;EAACC,CAAC,GAAC,CAAC;IAACC,MAAM,EAAC,gBAAgB;IAACC,SAAS,EAAC,CAAC;MAACC,OAAO,EAAC,CAAC;MAACC,MAAM,EAAC;IAAG,CAAC,EAAC;MAACD,OAAO,EAAC,CAAC;MAACC,MAAM,EAAC;IAAoB,CAAC,CAAC;IAACC,OAAO,EAAC;MAACC,QAAQ,EAAC,sBAAsB;MAACC,MAAM,EAAC,oBAAoB;MAACC,IAAI,EAAC;IAAU;EAAC,CAAC,CAAC;AAAC,SAAOR,CAAC,IAAIS,6BAA6B,EAACV,CAAC,IAAIW,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}