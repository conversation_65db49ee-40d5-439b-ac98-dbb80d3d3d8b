{"ast": null, "code": "import { renderIcon as c } from \"../icon.renderer.js\";\nconst l = \"digital-signature\",\n  t = [\"digital-signature\", c({\n    outline: '<path d=\"M17,33.9c-3.9,0-7.9-1.5-10.5-4.5c-0.9,0.7-2,1.3-3.1,1.8c-0.5,0.2-1.1,0-1.3-0.5c-0.2-0.5,0-1.1,0.5-1.3c1-0.4,1.9-0.9,2.7-1.5c-1.2-1.9-1.9-4.3-1.9-7.1c0-5.3,2.5-8.1,4.8-8.1c1.5,0,3.2,1.2,3.2,4.7c0,4.7-1.1,8.2-3.5,10.8c2.2,2.6,5.6,3.9,9,3.9c0.6,0,1,0.4,1,1S17.6,33.9,17,33.9z M8.2,14.6c-1,0-2.8,1.8-2.8,6.1c0,2.2,0.5,4.1,1.4,5.6c1.8-2.1,2.7-5.1,2.7-9.1C9.5,15.7,9,14.6,8.2,14.6z\"/><path d=\"M33.3,4.8c-0.8-1.4-2.1-2.4-3.6-2.8c-0.5-0.1-1.1,0.2-1.2,0.7l-0.9,3.4l-1.6-2.8c-0.1-0.2-0.4-0.4-0.7-0.5c-0.3-0.1-0.6,0-0.8,0.2c-1.1,0.8-1.8,1.9-2.2,3.2l-4.2,15.4c-0.4,1.5-0.2,3.2,0.6,4.6c0.6,1.1,1.6,1.9,2.7,2.5l-1.1,4c-0.1,0.5,0.2,1.1,0.7,1.2c0.1,0,0.2,0,0.3,0c0.4,0,0.8-0.3,1-0.7l1.1-3.9c0.2,0,0.4,0,0.6,0c1,0,2-0.3,3-0.8c1.4-0.8,2.4-2.1,2.8-3.6l1.6-5.8c0,0,0,0,0,0l1.6-5.8c0.1-0.4,0-0.8-0.4-1.1c-0.3-0.2-0.8-0.3-1.1-0.1l-4,2.3l0.6-2.1l5.7-3.2c0.3-0.2,0.5-0.5,0.5-0.8C34.2,7.1,33.9,5.9,33.3,4.8z M24.2,6.8c0.1-0.5,0.3-0.9,0.6-1.3l1.9,3.4l-1.4,5.1l-2.1-3.7L24.2,6.8zM20.4,25.2c-0.5-0.9-0.7-2-0.4-3l2.5-9.2l2.1,3.7L23.2,22c0,0,0,0,0,0l-1.3,4.7C21.3,26.4,20.8,25.9,20.4,25.2z M27.7,24.3c-0.3,1-0.9,1.9-1.9,2.4c-0.6,0.4-1.3,0.5-2,0.5L25,23l3.7-2.1L27.7,24.3z M30.3,15.1l-0.8,3.1l-3.7,2.1l0.8-3.1L30.3,15.1zM28.6,9.6l0.1-0.4c0.1-0.1,0.1-0.3,0.1-0.4l1.2-4.5c0.6,0.3,1.1,0.9,1.5,1.5c0.3,0.6,0.5,1.2,0.5,1.8L28.6,9.6z\"/>'\n  })];\nexport { t as digitalSignatureIcon, l as digitalSignatureIconName };", "map": {"version": 3, "names": ["renderIcon", "c", "l", "t", "outline", "digitalSignatureIcon", "digitalSignatureIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/digital-signature.js"], "sourcesContent": ["import{renderIcon as c}from\"../icon.renderer.js\";const l=\"digital-signature\",t=[\"digital-signature\",c({outline:'<path d=\"M17,33.9c-3.9,0-7.9-1.5-10.5-4.5c-0.9,0.7-2,1.3-3.1,1.8c-0.5,0.2-1.1,0-1.3-0.5c-0.2-0.5,0-1.1,0.5-1.3c1-0.4,1.9-0.9,2.7-1.5c-1.2-1.9-1.9-4.3-1.9-7.1c0-5.3,2.5-8.1,4.8-8.1c1.5,0,3.2,1.2,3.2,4.7c0,4.7-1.1,8.2-3.5,10.8c2.2,2.6,5.6,3.9,9,3.9c0.6,0,1,0.4,1,1S17.6,33.9,17,33.9z M8.2,14.6c-1,0-2.8,1.8-2.8,6.1c0,2.2,0.5,4.1,1.4,5.6c1.8-2.1,2.7-5.1,2.7-9.1C9.5,15.7,9,14.6,8.2,14.6z\"/><path d=\"M33.3,4.8c-0.8-1.4-2.1-2.4-3.6-2.8c-0.5-0.1-1.1,0.2-1.2,0.7l-0.9,3.4l-1.6-2.8c-0.1-0.2-0.4-0.4-0.7-0.5c-0.3-0.1-0.6,0-0.8,0.2c-1.1,0.8-1.8,1.9-2.2,3.2l-4.2,15.4c-0.4,1.5-0.2,3.2,0.6,4.6c0.6,1.1,1.6,1.9,2.7,2.5l-1.1,4c-0.1,0.5,0.2,1.1,0.7,1.2c0.1,0,0.2,0,0.3,0c0.4,0,0.8-0.3,1-0.7l1.1-3.9c0.2,0,0.4,0,0.6,0c1,0,2-0.3,3-0.8c1.4-0.8,2.4-2.1,2.8-3.6l1.6-5.8c0,0,0,0,0,0l1.6-5.8c0.1-0.4,0-0.8-0.4-1.1c-0.3-0.2-0.8-0.3-1.1-0.1l-4,2.3l0.6-2.1l5.7-3.2c0.3-0.2,0.5-0.5,0.5-0.8C34.2,7.1,33.9,5.9,33.3,4.8z M24.2,6.8c0.1-0.5,0.3-0.9,0.6-1.3l1.9,3.4l-1.4,5.1l-2.1-3.7L24.2,6.8zM20.4,25.2c-0.5-0.9-0.7-2-0.4-3l2.5-9.2l2.1,3.7L23.2,22c0,0,0,0,0,0l-1.3,4.7C21.3,26.4,20.8,25.9,20.4,25.2z M27.7,24.3c-0.3,1-0.9,1.9-1.9,2.4c-0.6,0.4-1.3,0.5-2,0.5L25,23l3.7-2.1L27.7,24.3z M30.3,15.1l-0.8,3.1l-3.7,2.1l0.8-3.1L30.3,15.1zM28.6,9.6l0.1-0.4c0.1-0.1,0.1-0.3,0.1-0.4l1.2-4.5c0.6,0.3,1.1,0.9,1.5,1.5c0.3,0.6,0.5,1.2,0.5,1.8L28.6,9.6z\"/>'})];export{t as digitalSignatureIcon,l as digitalSignatureIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,mBAAmB;EAACC,CAAC,GAAC,CAAC,mBAAmB,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAA8xC,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,oBAAoB,EAACH,CAAC,IAAII,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}