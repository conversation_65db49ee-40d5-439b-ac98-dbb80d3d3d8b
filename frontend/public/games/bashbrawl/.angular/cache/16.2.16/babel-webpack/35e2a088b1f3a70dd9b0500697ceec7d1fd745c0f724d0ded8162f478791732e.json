{"ast": null, "code": "import { renderIcon as t } from \"../icon.renderer.js\";\nconst r = \"text-color\",\n  o = [\"text-color\", t({\n    outline: '<path d=\"M19.47,3.84a1.45,1.45,0,0,0-1.4-1H18a1.45,1.45,0,0,0-1.42,1L8.42,21.56a1.35,1.35,0,0,0-.14.59,1,1,0,0,0,1,1,1.11,1.11,0,0,0,1.08-.77l2.08-4.65h11l2.08,4.59a1.24,1.24,0,0,0,1.12.83,1.08,1.08,0,0,0,1.08-1.08,1.59,1.59,0,0,0-.14-.57ZM13.36,15.71,18,5.49l4.6,10.22Z\"/><rect x=\"4.06\" y=\"25\" width=\"28\" height=\"8\" rx=\"2\" ry=\"2\"/>'\n  })];\nexport { o as textColorIcon, r as textColorIconName };", "map": {"version": 3, "names": ["renderIcon", "t", "r", "o", "outline", "textColorIcon", "textColorIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/text-color.js"], "sourcesContent": ["import{renderIcon as t}from\"../icon.renderer.js\";const r=\"text-color\",o=[\"text-color\",t({outline:'<path d=\"M19.47,3.84a1.45,1.45,0,0,0-1.4-1H18a1.45,1.45,0,0,0-1.42,1L8.42,21.56a1.35,1.35,0,0,0-.14.59,1,1,0,0,0,1,1,1.11,1.11,0,0,0,1.08-.77l2.08-4.65h11l2.08,4.59a1.24,1.24,0,0,0,1.12.83,1.08,1.08,0,0,0,1.08-1.08,1.59,1.59,0,0,0-.14-.57ZM13.36,15.71,18,5.49l4.6,10.22Z\"/><rect x=\"4.06\" y=\"25\" width=\"28\" height=\"8\" rx=\"2\" ry=\"2\"/>'})];export{o as textColorIcon,r as textColorIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,YAAY;EAACC,CAAC,GAAC,CAAC,YAAY,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAA8U,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,aAAa,EAACH,CAAC,IAAII,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}