{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"cluster\",\n  V = [\"cluster\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M11.68 4H24.32C25.25 4 26 4.75 26 5.68V32H10V5.68C10 4.75 10.75 4 11.68 4ZM22 8H14V10H22V8ZM24 30H12V6H24V30ZM28 8H32.36C32.8 8 33.22 8.18 33.53 8.49C33.84 8.8 34.01 9.23 34 9.67V32H28V30H32V10H28V8ZM4 10H8V8H3.64C3.2 8 2.78 8.18 2.47 8.49C2.16 8.81 2 9.23 2 9.67V32H8V30H4V10ZM20 26C20 27.1 19.1 27.99 18 27.99C16.9 27.99 16 27.1 16 26C16 24.9 16.9 24.01 18 24.01C19.1 24.01 20 24.9 20 26Z\"/>',\n    outlineAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M32 15.0367H33.6637C33.7763 15.0389 33.8886 15.0354 34 15.0263V32H28V30H32V15.0367Z\"/><path d=\"M23.1594 4H11.68C10.75 4 10 4.75 10 5.68V32H26V15.0367H24V30H12V6H21.9594L23.1594 4Z\"/><path d=\"M20.7594 8H14V10H19.5594L20.7594 8Z\"/><path d=\"M16 26C16 24.9 16.9 24.01 18 24.01C19.1 24.01 20 24.9 20 26C20 27.1 19.1 27.99 18 27.99C16.9 27.99 16 27.1 16 26Z\"/><path d=\"M2 9.67C2 9.23 2.16 8.81 2.47 8.49C2.78 8.18 3.2 8 3.64 8H8V10H4V30H8V32H2V9.67Z\"/>',\n    outlineBadged: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M32 12.7101V30H28V32H34V11.7453C33.396 12.1666 32.7224 12.4951 32 12.7101Z\"/><path d=\"M23.2899 4H11.68C10.75 4 10 4.75 10 5.68V32H26V11.7453C25.1926 11.1821 24.5095 10.4531 24 9.60759V30H12V6H23C23 5.30503 23.1013 4.63371 23.2899 4Z\"/><path d=\"M14 8H22V10H14V8Z\"/><path d=\"M8 10H4V30H8V32H2V9.67C2 9.23 2.16 8.81 2.47 8.49C2.78 8.18 3.2 8 3.64 8H8V10Z\"/><path d=\"M18 27.99C19.1 27.99 20 27.1 20 26C20 24.9 19.1 24.01 18 24.01C16.9 24.01 16 24.9 16 26C16 27.1 16.9 27.99 18 27.99Z\"/>',\n    solid: '<path d=\"M24.327 4H11.683C10.7527 4 10.0025 4.75 10.0025 5.68V32H26.0075V5.68C26.0075 4.75 25.2573 4 24.327 4ZM18.005 28.09C16.8446 28.09 15.9043 27.15 15.9043 26C15.9043 24.85 16.8446 23.91 18.005 23.91C19.1654 23.91 20.1057 24.85 20.1057 26C20.1057 27.15 19.1654 28.09 18.005 28.09ZM22.1063 10.1H13.9037V7.9H22.1063V10.1ZM2.47015 8.49C2.16005 8.81 2 9.23 2 9.67V32H8.00188V8H3.64051C3.20038 8 2.78024 8.18 2.47015 8.49ZM33.5299 8.49C33.2198 8.18 32.7996 8 32.3595 8H27.9981V32H34V9.67C34 9.23 33.8399 8.81 33.5299 8.49Z\"/>',\n    solidAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M27.9981 15.0367H33.6736C33.7829 15.0389 33.8918 15.0356 34 15.0271V32H27.9981V15.0367Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M23.166 4H11.683C10.7527 4 10.0025 4.75 10.0025 5.68V32H26.0075V15.0367H22.3459C21.1637 15.0604 20.0289 14.4489 19.426 13.3893C18.8378 12.3556 18.8738 11.0944 19.502 10.1H13.9037V7.9H20.8253L23.166 4ZM15.9043 26C15.9043 27.15 16.8446 28.09 18.005 28.09C19.1654 28.09 20.1057 27.15 20.1057 26C20.1057 24.85 19.1654 23.91 18.005 23.91C16.8446 23.91 15.9043 24.85 15.9043 26Z\"/><path d=\"M2 9.67C2 9.23 2.16005 8.81 2.47015 8.49C2.78024 8.18 3.20038 8 3.64051 8H8.00188V32H2V9.67Z\"/>',\n    solidBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M34 11.7523C32.8677 12.5389 31.4921 13 30.0088 13C29.3099 13 28.635 12.8977 27.9981 12.7071V32H34V11.7523Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M23.2965 4C23.1079 4.63371 23.0066 5.30503 23.0066 6C23.0066 8.37875 24.1935 10.4804 26.0075 11.7453V32H10.0025V5.68C10.0025 4.75 10.7527 4 11.683 4H23.2965ZM15.9043 26C15.9043 27.15 16.8446 28.09 18.005 28.09C19.1654 28.09 20.1057 27.15 20.1057 26C20.1057 24.85 19.1654 23.91 18.005 23.91C16.8446 23.91 15.9043 24.85 15.9043 26ZM13.9037 10.1H22.1063V7.9H13.9037V10.1Z\"/><path d=\"M2 9.67C2 9.23 2.16005 8.81 2.47015 8.49C2.78024 8.18 3.20038 8 3.64051 8H8.00188V32H2V9.67Z\"/>'\n  })];\nexport { V as clusterIcon, H as clusterIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "V", "outline", "outlineAlerted", "outlineBadged", "solid", "solidAlerted", "solidBadged", "clusterIcon", "clusterIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/cluster.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"cluster\",V=[\"cluster\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M11.68 4H24.32C25.25 4 26 4.75 26 5.68V32H10V5.68C10 4.75 10.75 4 11.68 4ZM22 8H14V10H22V8ZM24 30H12V6H24V30ZM28 8H32.36C32.8 8 33.22 8.18 33.53 8.49C33.84 8.8 34.01 9.23 34 9.67V32H28V30H32V10H28V8ZM4 10H8V8H3.64C3.2 8 2.78 8.18 2.47 8.49C2.16 8.81 2 9.23 2 9.67V32H8V30H4V10ZM20 26C20 27.1 19.1 27.99 18 27.99C16.9 27.99 16 27.1 16 26C16 24.9 16.9 24.01 18 24.01C19.1 24.01 20 24.9 20 26Z\"/>',outlineAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M32 15.0367H33.6637C33.7763 15.0389 33.8886 15.0354 34 15.0263V32H28V30H32V15.0367Z\"/><path d=\"M23.1594 4H11.68C10.75 4 10 4.75 10 5.68V32H26V15.0367H24V30H12V6H21.9594L23.1594 4Z\"/><path d=\"M20.7594 8H14V10H19.5594L20.7594 8Z\"/><path d=\"M16 26C16 24.9 16.9 24.01 18 24.01C19.1 24.01 20 24.9 20 26C20 27.1 19.1 27.99 18 27.99C16.9 27.99 16 27.1 16 26Z\"/><path d=\"M2 9.67C2 9.23 2.16 8.81 2.47 8.49C2.78 8.18 3.2 8 3.64 8H8V10H4V30H8V32H2V9.67Z\"/>',outlineBadged:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M32 12.7101V30H28V32H34V11.7453C33.396 12.1666 32.7224 12.4951 32 12.7101Z\"/><path d=\"M23.2899 4H11.68C10.75 4 10 4.75 10 5.68V32H26V11.7453C25.1926 11.1821 24.5095 10.4531 24 9.60759V30H12V6H23C23 5.30503 23.1013 4.63371 23.2899 4Z\"/><path d=\"M14 8H22V10H14V8Z\"/><path d=\"M8 10H4V30H8V32H2V9.67C2 9.23 2.16 8.81 2.47 8.49C2.78 8.18 3.2 8 3.64 8H8V10Z\"/><path d=\"M18 27.99C19.1 27.99 20 27.1 20 26C20 24.9 19.1 24.01 18 24.01C16.9 24.01 16 24.9 16 26C16 27.1 16.9 27.99 18 27.99Z\"/>',solid:'<path d=\"M24.327 4H11.683C10.7527 4 10.0025 4.75 10.0025 5.68V32H26.0075V5.68C26.0075 4.75 25.2573 4 24.327 4ZM18.005 28.09C16.8446 28.09 15.9043 27.15 15.9043 26C15.9043 24.85 16.8446 23.91 18.005 23.91C19.1654 23.91 20.1057 24.85 20.1057 26C20.1057 27.15 19.1654 28.09 18.005 28.09ZM22.1063 10.1H13.9037V7.9H22.1063V10.1ZM2.47015 8.49C2.16005 8.81 2 9.23 2 9.67V32H8.00188V8H3.64051C3.20038 8 2.78024 8.18 2.47015 8.49ZM33.5299 8.49C33.2198 8.18 32.7996 8 32.3595 8H27.9981V32H34V9.67C34 9.23 33.8399 8.81 33.5299 8.49Z\"/>',solidAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M27.9981 15.0367H33.6736C33.7829 15.0389 33.8918 15.0356 34 15.0271V32H27.9981V15.0367Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M23.166 4H11.683C10.7527 4 10.0025 4.75 10.0025 5.68V32H26.0075V15.0367H22.3459C21.1637 15.0604 20.0289 14.4489 19.426 13.3893C18.8378 12.3556 18.8738 11.0944 19.502 10.1H13.9037V7.9H20.8253L23.166 4ZM15.9043 26C15.9043 27.15 16.8446 28.09 18.005 28.09C19.1654 28.09 20.1057 27.15 20.1057 26C20.1057 24.85 19.1654 23.91 18.005 23.91C16.8446 23.91 15.9043 24.85 15.9043 26Z\"/><path d=\"M2 9.67C2 9.23 2.16005 8.81 2.47015 8.49C2.78024 8.18 3.20038 8 3.64051 8H8.00188V32H2V9.67Z\"/>',solidBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M34 11.7523C32.8677 12.5389 31.4921 13 30.0088 13C29.3099 13 28.635 12.8977 27.9981 12.7071V32H34V11.7523Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M23.2965 4C23.1079 4.63371 23.0066 5.30503 23.0066 6C23.0066 8.37875 24.1935 10.4804 26.0075 11.7453V32H10.0025V5.68C10.0025 4.75 10.7527 4 11.683 4H23.2965ZM15.9043 26C15.9043 27.15 16.8446 28.09 18.005 28.09C19.1654 28.09 20.1057 27.15 20.1057 26C20.1057 24.85 19.1654 23.91 18.005 23.91C16.8446 23.91 15.9043 24.85 15.9043 26ZM13.9037 10.1H22.1063V7.9H13.9037V10.1Z\"/><path d=\"M2 9.67C2 9.23 2.16005 8.81 2.47015 8.49C2.78024 8.18 3.20038 8 3.64051 8H8.00188V32H2V9.67Z\"/>'})];export{V as clusterIcon,H as clusterIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,SAAS;EAACC,CAAC,GAAC,CAAC,SAAS,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,4bAA4b;IAACC,cAAc,EAAC,4yBAA4yB;IAACC,aAAa,EAAC,mpBAAmpB;IAACC,KAAK,EAAC,8gBAA8gB;IAACC,YAAY,EAAC,w9BAAw9B;IAACC,WAAW,EAAC;EAAkwB,CAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,WAAW,EAACR,CAAC,IAAIS,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}