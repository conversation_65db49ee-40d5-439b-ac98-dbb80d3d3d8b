{"ast": null, "code": "import { stopEvent as e } from \"./events.js\";\nfunction t(e) {\n  return e.matches(\"a[href],area[href],input:not([disabled]),button:not([disabled]),select:not([disabled]),textarea:not([disabled]),iframe,object,embed,*[tabindex],*[contenteditable=true],[role=button]:not([disabled])\");\n}\nfunction n(e) {\n  return e.matches(\"a[href],button:not([disabled]),input[type=checkbox],input[type=radio],object,embed,*[tabindex],[role=button]:not([disabled])\");\n}\nfunction o(e = document) {\n  return e.activeElement && e.activeElement.shadowRoot ? o(e.activeElement.shadowRoot) ?? e.activeElement : e.activeElement;\n}\nfunction a(e) {\n  e && !t(e) ? (e.setAttribute(\"tabindex\", \"-1\"), e.focus(), e.addEventListener(\"blur\", () => e.removeAttribute(\"tabindex\"), {\n    once: !0\n  })) : e?.focus();\n}\nfunction d(e, t) {\n  e.addEventListener(\"focusout\", n => {\n    !e.contains(n.relatedTarget) && document.hasFocus() && t();\n  });\n}\nfunction i(t, n) {\n  t.addEventListener(\"keyup\", t => {\n    \"Escape\" === t.code && (e(t), n());\n  });\n}\nfunction c(e) {\n  return !0 === e.cdsIgnoreFocus || e.hasAttribute(\"cds-ignore-focus\") || e.hasAttribute(\"_demo-mode\");\n}\nfunction r(e, t) {\n  e.forEach(e => e.tabIndex = -1), t.tabIndex = 0;\n}\nfunction u(e) {\n  e.forEach(e => e.tabIndex = -1), e[0].tabIndex = 0;\n}\nexport { a as focusElement, t as focusable, o as getActiveElement, c as ignoreFocus, u as initializeKeyListItems, i as onEscape, d as onFocusOut, r as setActiveKeyListItem, n as simpleFocusable };", "map": {"version": 3, "names": ["stopEvent", "e", "t", "matches", "n", "o", "document", "activeElement", "shadowRoot", "a", "setAttribute", "focus", "addEventListener", "removeAttribute", "once", "d", "contains", "relatedTarget", "hasFocus", "i", "code", "c", "cdsIgnoreFocus", "hasAttribute", "r", "for<PERSON>ach", "tabIndex", "u", "focusElement", "focusable", "getActiveElement", "ignoreFocus", "initializeKeyListItems", "onEscape", "onFocusOut", "setActiveKeyListItem", "simpleFocusable"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/focus.js"], "sourcesContent": ["import{stopEvent as e}from\"./events.js\";function t(e){return e.matches(\"a[href],area[href],input:not([disabled]),button:not([disabled]),select:not([disabled]),textarea:not([disabled]),iframe,object,embed,*[tabindex],*[contenteditable=true],[role=button]:not([disabled])\")}function n(e){return e.matches(\"a[href],button:not([disabled]),input[type=checkbox],input[type=radio],object,embed,*[tabindex],[role=button]:not([disabled])\")}function o(e=document){return e.activeElement&&e.activeElement.shadowRoot?o(e.activeElement.shadowRoot)??e.activeElement:e.activeElement}function a(e){e&&!t(e)?(e.setAttribute(\"tabindex\",\"-1\"),e.focus(),e.addEventListener(\"blur\",(()=>e.removeAttribute(\"tabindex\")),{once:!0})):e?.focus()}function d(e,t){e.addEventListener(\"focusout\",(n=>{!e.contains(n.relatedTarget)&&document.hasFocus()&&t()}))}function i(t,n){t.addEventListener(\"keyup\",(t=>{\"Escape\"===t.code&&(e(t),n())}))}function c(e){return!0===e.cdsIgnoreFocus||e.hasAttribute(\"cds-ignore-focus\")||e.hasAttribute(\"_demo-mode\")}function r(e,t){e.forEach((e=>e.tabIndex=-1)),t.tabIndex=0}function u(e){e.forEach((e=>e.tabIndex=-1)),e[0].tabIndex=0}export{a as focusElement,t as focusable,o as getActiveElement,c as ignoreFocus,u as initializeKeyListItems,i as onEscape,d as onFocusOut,r as setActiveKeyListItem,n as simpleFocusable};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,QAAK,aAAa;AAAC,SAASC,CAACA,CAACD,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACE,OAAO,CAAC,uMAAuM,CAAC;AAAA;AAAC,SAASC,CAACA,CAACH,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACE,OAAO,CAAC,8HAA8H,CAAC;AAAA;AAAC,SAASE,CAACA,CAACJ,CAAC,GAACK,QAAQ,EAAC;EAAC,OAAOL,CAAC,CAACM,aAAa,IAAEN,CAAC,CAACM,aAAa,CAACC,UAAU,GAACH,CAAC,CAACJ,CAAC,CAACM,aAAa,CAACC,UAAU,CAAC,IAAEP,CAAC,CAACM,aAAa,GAACN,CAAC,CAACM,aAAa;AAAA;AAAC,SAASE,CAACA,CAACR,CAAC,EAAC;EAACA,CAAC,IAAE,CAACC,CAAC,CAACD,CAAC,CAAC,IAAEA,CAAC,CAACS,YAAY,CAAC,UAAU,EAAC,IAAI,CAAC,EAACT,CAAC,CAACU,KAAK,CAAC,CAAC,EAACV,CAAC,CAACW,gBAAgB,CAAC,MAAM,EAAE,MAAIX,CAAC,CAACY,eAAe,CAAC,UAAU,CAAC,EAAE;IAACC,IAAI,EAAC,CAAC;EAAC,CAAC,CAAC,IAAEb,CAAC,EAAEU,KAAK,CAAC,CAAC;AAAA;AAAC,SAASI,CAACA,CAACd,CAAC,EAACC,CAAC,EAAC;EAACD,CAAC,CAACW,gBAAgB,CAAC,UAAU,EAAER,CAAC,IAAE;IAAC,CAACH,CAAC,CAACe,QAAQ,CAACZ,CAAC,CAACa,aAAa,CAAC,IAAEX,QAAQ,CAACY,QAAQ,CAAC,CAAC,IAAEhB,CAAC,CAAC,CAAC;EAAA,CAAE,CAAC;AAAA;AAAC,SAASiB,CAACA,CAACjB,CAAC,EAACE,CAAC,EAAC;EAACF,CAAC,CAACU,gBAAgB,CAAC,OAAO,EAAEV,CAAC,IAAE;IAAC,QAAQ,KAAGA,CAAC,CAACkB,IAAI,KAAGnB,CAAC,CAACC,CAAC,CAAC,EAACE,CAAC,CAAC,CAAC,CAAC;EAAA,CAAE,CAAC;AAAA;AAAC,SAASiB,CAACA,CAACpB,CAAC,EAAC;EAAC,OAAM,CAAC,CAAC,KAAGA,CAAC,CAACqB,cAAc,IAAErB,CAAC,CAACsB,YAAY,CAAC,kBAAkB,CAAC,IAAEtB,CAAC,CAACsB,YAAY,CAAC,YAAY,CAAC;AAAA;AAAC,SAASC,CAACA,CAACvB,CAAC,EAACC,CAAC,EAAC;EAACD,CAAC,CAACwB,OAAO,CAAExB,CAAC,IAAEA,CAAC,CAACyB,QAAQ,GAAC,CAAC,CAAE,CAAC,EAACxB,CAAC,CAACwB,QAAQ,GAAC,CAAC;AAAA;AAAC,SAASC,CAACA,CAAC1B,CAAC,EAAC;EAACA,CAAC,CAACwB,OAAO,CAAExB,CAAC,IAAEA,CAAC,CAACyB,QAAQ,GAAC,CAAC,CAAE,CAAC,EAACzB,CAAC,CAAC,CAAC,CAAC,CAACyB,QAAQ,GAAC,CAAC;AAAA;AAAC,SAAOjB,CAAC,IAAImB,YAAY,EAAC1B,CAAC,IAAI2B,SAAS,EAACxB,CAAC,IAAIyB,gBAAgB,EAACT,CAAC,IAAIU,WAAW,EAACJ,CAAC,IAAIK,sBAAsB,EAACb,CAAC,IAAIc,QAAQ,EAAClB,CAAC,IAAImB,UAAU,EAACV,CAAC,IAAIW,oBAAoB,EAAC/B,CAAC,IAAIgC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}