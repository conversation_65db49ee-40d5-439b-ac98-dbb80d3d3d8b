{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"objects\",\n  o = [\"objects\", C({\n    outline: '<path d=\"M4.39942 28L11.9976 6.06L15.4868 16.13C15.9867 16.05 16.4866 16 16.9964 16C17.1864 16 17.3764 16.02 17.5663 16.03L12.9474 2.67C12.8074 2.27 12.4275 2 11.9976 2C11.5677 2 11.1878 2.27 11.0479 2.67L2.04998 28.67C1.94 28.98 1.98999 29.31 2.17995 29.58C2.3699 29.85 2.66983 30 2.98975 30H7.82861C7.54868 29.36 7.32873 28.7 7.18876 28H4.38942H4.39942ZM16.9964 18C12.5875 18 8.99834 21.59 8.99834 26C8.99834 30.41 12.5875 34 16.9964 34C21.4054 34 24.9946 30.41 24.9946 26C24.9946 21.59 21.4054 18 16.9964 18ZM16.9964 32C13.6872 32 10.9979 29.31 10.9979 26C10.9979 22.69 13.6872 20 16.9964 20C20.3057 20 22.995 22.69 22.995 26C22.995 29.31 20.3057 32 16.9964 32ZM31.9929 12H21.9953C20.8955 12 19.9957 12.9 19.9957 14V16.46C20.7056 16.68 21.3654 16.99 21.9953 17.35V14H31.9929V24H26.7941C26.9241 24.65 26.9941 25.32 26.9941 26H31.9929C33.0926 26 33.9924 25.1 33.9924 24V14C33.9924 12.9 33.0926 12 31.9929 12Z\"/>',\n    solid: '<path d=\"M16.9964 16C17.1864 16 17.3764 16.02 17.5663 16.03L12.9474 2.67C12.8074 2.27 12.4275 2 11.9976 2C11.5677 2 11.1878 2.27 11.0479 2.67L2.04998 28.67C1.94 28.98 1.98999 29.31 2.17995 29.58C2.3699 29.85 2.66983 30 2.98975 30H7.82861C7.28874 28.77 6.98881 27.42 6.98881 26C6.98881 20.49 11.4777 16 16.9864 16H16.9964ZM16.9964 18C12.5875 18 8.99834 21.59 8.99834 26C8.99834 30.41 12.5875 34 16.9964 34C21.4054 34 24.9946 30.41 24.9946 26C24.9946 21.59 21.4054 18 16.9964 18ZM31.9929 12H21.9953C20.8955 12 19.9957 12.9 19.9957 14V16.46C24.0448 17.74 26.9941 21.53 26.9941 26H31.9929C33.0926 26 33.9924 25.1 33.9924 24V14C33.9924 12.9 33.0926 12 31.9929 12Z\"/>'\n  })];\nexport { o as objectsIcon, H as objectsIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "o", "outline", "solid", "objectsIcon", "objectsIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/objects.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"objects\",o=[\"objects\",C({outline:'<path d=\"M4.39942 28L11.9976 6.06L15.4868 16.13C15.9867 16.05 16.4866 16 16.9964 16C17.1864 16 17.3764 16.02 17.5663 16.03L12.9474 2.67C12.8074 2.27 12.4275 2 11.9976 2C11.5677 2 11.1878 2.27 11.0479 2.67L2.04998 28.67C1.94 28.98 1.98999 29.31 2.17995 29.58C2.3699 29.85 2.66983 30 2.98975 30H7.82861C7.54868 29.36 7.32873 28.7 7.18876 28H4.38942H4.39942ZM16.9964 18C12.5875 18 8.99834 21.59 8.99834 26C8.99834 30.41 12.5875 34 16.9964 34C21.4054 34 24.9946 30.41 24.9946 26C24.9946 21.59 21.4054 18 16.9964 18ZM16.9964 32C13.6872 32 10.9979 29.31 10.9979 26C10.9979 22.69 13.6872 20 16.9964 20C20.3057 20 22.995 22.69 22.995 26C22.995 29.31 20.3057 32 16.9964 32ZM31.9929 12H21.9953C20.8955 12 19.9957 12.9 19.9957 14V16.46C20.7056 16.68 21.3654 16.99 21.9953 17.35V14H31.9929V24H26.7941C26.9241 24.65 26.9941 25.32 26.9941 26H31.9929C33.0926 26 33.9924 25.1 33.9924 24V14C33.9924 12.9 33.0926 12 31.9929 12Z\"/>',solid:'<path d=\"M16.9964 16C17.1864 16 17.3764 16.02 17.5663 16.03L12.9474 2.67C12.8074 2.27 12.4275 2 11.9976 2C11.5677 2 11.1878 2.27 11.0479 2.67L2.04998 28.67C1.94 28.98 1.98999 29.31 2.17995 29.58C2.3699 29.85 2.66983 30 2.98975 30H7.82861C7.28874 28.77 6.98881 27.42 6.98881 26C6.98881 20.49 11.4777 16 16.9864 16H16.9964ZM16.9964 18C12.5875 18 8.99834 21.59 8.99834 26C8.99834 30.41 12.5875 34 16.9964 34C21.4054 34 24.9946 30.41 24.9946 26C24.9946 21.59 21.4054 18 16.9964 18ZM31.9929 12H21.9953C20.8955 12 19.9957 12.9 19.9957 14V16.46C24.0448 17.74 26.9941 21.53 26.9941 26H31.9929C33.0926 26 33.9924 25.1 33.9924 24V14C33.9924 12.9 33.0926 12 31.9929 12Z\"/>'})];export{o as objectsIcon,H as objectsIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,SAAS;EAACC,CAAC,GAAC,CAAC,SAAS,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,k5BAAk5B;IAACC,KAAK,EAAC;EAAupB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,WAAW,EAACJ,CAAC,IAAIK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}