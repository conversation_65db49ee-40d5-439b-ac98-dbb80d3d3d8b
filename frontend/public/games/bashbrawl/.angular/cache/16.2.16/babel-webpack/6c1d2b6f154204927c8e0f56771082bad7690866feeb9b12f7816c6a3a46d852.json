{"ast": null, "code": "import { isNil as n } from \"./identity.js\";\nfunction o(o = window) {\n  return !n(o);\n}\nfunction t() {\n  return void 0 !== globalThis?.process?.env?.JEST_WORKER_ID;\n}\nfunction e() {\n  return !!t() || \"test\" === globalThis?.process?.env?.NODE_ENV && navigator?.userAgent?.includes(\"jsdom\");\n}\nexport { o as isBrowser, t as isJestTest, e as isJsdomTest };", "map": {"version": 3, "names": ["isNil", "n", "o", "window", "t", "globalThis", "process", "env", "JEST_WORKER_ID", "e", "NODE_ENV", "navigator", "userAgent", "includes", "<PERSON><PERSON><PERSON><PERSON>", "isJestTest", "isJsdomTest"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/environment.js"], "sourcesContent": ["import{isNil as n}from\"./identity.js\";function o(o=window){return!n(o)}function t(){return void 0!==globalThis?.process?.env?.JEST_WORKER_ID}function e(){return!!t()||\"test\"===globalThis?.process?.env?.NODE_ENV&&navigator?.userAgent?.includes(\"jsdom\")}export{o as isBrowser,t as isJestTest,e as isJsdomTest};\n"], "mappings": "AAAA,SAAOA,KAAK,IAAIC,CAAC,QAAK,eAAe;AAAC,SAASC,CAACA,CAACA,CAAC,GAACC,MAAM,EAAC;EAAC,OAAM,CAACF,CAAC,CAACC,CAAC,CAAC;AAAA;AAAC,SAASE,CAACA,CAAA,EAAE;EAAC,OAAO,KAAK,CAAC,KAAGC,UAAU,EAAEC,OAAO,EAAEC,GAAG,EAAEC,cAAc;AAAA;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,OAAM,CAAC,CAACL,CAAC,CAAC,CAAC,IAAE,MAAM,KAAGC,UAAU,EAAEC,OAAO,EAAEC,GAAG,EAAEG,QAAQ,IAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,CAAC,OAAO,CAAC;AAAA;AAAC,SAAOX,CAAC,IAAIY,SAAS,EAACV,CAAC,IAAIW,UAAU,EAACN,CAAC,IAAIO,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}