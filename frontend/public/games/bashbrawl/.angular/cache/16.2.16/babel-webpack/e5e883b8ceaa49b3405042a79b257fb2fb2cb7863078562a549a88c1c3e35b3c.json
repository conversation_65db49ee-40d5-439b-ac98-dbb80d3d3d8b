{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"snowflake\",\n  V = [\"snowflake\", C({\n    outline: '<path d=\"M31.4603 24.6442L28.3249 22.833L30.1922 22.3327C30.7214 22.1926 31.041 21.6423 30.9012 21.1119C30.7614 20.5816 30.2122 20.2614 29.6729 20.4015L25.8784 21.4221L19.9769 18.01L25.9383 14.5679L29.633 15.5585C29.7229 15.5785 29.8027 15.5885 29.8926 15.5885C30.332 15.5885 30.7414 15.2983 30.8612 14.848C31.001 14.3177 30.6915 13.7674 30.1522 13.6273L28.3848 13.157L31.4603 11.3859C31.9397 11.1057 32.0994 10.4953 31.8298 10.015C31.5502 9.53471 30.9411 9.37461 30.4618 9.64478L27.3263 11.4559L27.8256 9.59475C27.9654 9.06442 27.6558 8.51407 27.1166 8.37398C26.5874 8.2339 26.0382 8.54409 25.8884 9.08443L24.8699 12.8868L18.9684 16.2989V9.40463L21.6745 6.69293C22.0639 6.30269 22.0639 5.6723 21.6745 5.28205C21.285 4.89181 20.656 4.89181 20.2565 5.28205L18.9684 6.57286V3.00063C18.9684 2.45028 18.519 2 17.9698 2C17.4206 2 16.9713 2.45028 16.9713 3.00063V6.62289L15.6033 5.26204C15.2138 4.87179 14.5847 4.87179 14.1853 5.26204C13.7859 5.65228 13.7959 6.28268 14.1853 6.67292L16.9613 9.45466V16.2789L10.9999 12.8368L10.0113 9.13446C9.87153 8.60413 9.32233 8.28393 8.78311 8.42402C8.25387 8.5641 7.93433 9.11445 8.07413 9.64478L8.54345 11.4159L5.46789 9.63477C4.98859 9.3546 4.37947 9.5247 4.09987 10.005C3.82027 10.4853 3.99003 11.0957 4.46934 11.3759L7.60481 13.187L5.73751 13.6873C5.20827 13.8274 4.88873 14.3777 5.02853 14.9081C5.14836 15.3583 5.54778 15.6485 5.99713 15.6485C6.087 15.6485 6.16688 15.6385 6.25675 15.6185L10.0513 14.5979L15.9528 18.01L9.99136 21.4522L6.2967 20.4615C5.76746 20.3215 5.21826 20.6316 5.06847 21.172C4.91869 21.7123 5.23823 22.2527 5.77745 22.3927L7.54489 22.863L4.46934 24.6341C3.99003 24.9143 3.83026 25.5247 4.09987 26.005C4.2896 26.3252 4.61912 26.5053 4.96862 26.5053C5.13837 26.5053 5.30813 26.4653 5.46789 26.3752L8.60337 24.5641L8.10409 26.4253C7.96429 26.9556 8.27384 27.5059 8.81306 27.646C8.90293 27.666 8.98282 27.676 9.07269 27.676C9.51205 27.676 9.92146 27.3859 10.0413 26.9356L11.0598 23.1332L16.9613 19.7211V26.6154L14.2552 29.3271C13.8658 29.7173 13.8658 30.3477 14.2552 30.738C14.6446 31.1282 15.2737 31.1282 15.6732 30.738L16.9613 29.4472V32.9994C16.9613 33.5497 17.4106 34 17.9599 34C18.5091 34 18.9584 33.5497 18.9584 32.9994V29.3771L20.3264 30.738C20.5261 30.9381 20.7758 31.0281 21.0354 31.0281C21.295 31.0281 21.5447 30.9281 21.7444 30.738C22.1338 30.3477 22.1338 29.7173 21.7444 29.3271L18.9684 26.5453V19.7211L24.9298 23.1632L25.9184 26.8655C26.0382 27.3158 26.4376 27.606 26.887 27.606C26.9768 27.606 27.0567 27.596 27.1466 27.576C27.6758 27.4359 27.9954 26.8856 27.8556 26.3552L27.3862 24.5841L30.4618 26.3652C30.6216 26.4553 30.7913 26.4953 30.9611 26.4953C31.3106 26.4953 31.6401 26.3152 31.8298 25.995C32.1094 25.5147 31.9397 24.9043 31.4603 24.6241V24.6442Z\"/>'\n  })];\nexport { V as snowflakeIcon, L as snowflakeIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "V", "outline", "snowflakeIcon", "snowflakeIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/snowflake.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"snowflake\",V=[\"snowflake\",C({outline:'<path d=\"M31.4603 24.6442L28.3249 22.833L30.1922 22.3327C30.7214 22.1926 31.041 21.6423 30.9012 21.1119C30.7614 20.5816 30.2122 20.2614 29.6729 20.4015L25.8784 21.4221L19.9769 18.01L25.9383 14.5679L29.633 15.5585C29.7229 15.5785 29.8027 15.5885 29.8926 15.5885C30.332 15.5885 30.7414 15.2983 30.8612 14.848C31.001 14.3177 30.6915 13.7674 30.1522 13.6273L28.3848 13.157L31.4603 11.3859C31.9397 11.1057 32.0994 10.4953 31.8298 10.015C31.5502 9.53471 30.9411 9.37461 30.4618 9.64478L27.3263 11.4559L27.8256 9.59475C27.9654 9.06442 27.6558 8.51407 27.1166 8.37398C26.5874 8.2339 26.0382 8.54409 25.8884 9.08443L24.8699 12.8868L18.9684 16.2989V9.40463L21.6745 6.69293C22.0639 6.30269 22.0639 5.6723 21.6745 5.28205C21.285 4.89181 20.656 4.89181 20.2565 5.28205L18.9684 6.57286V3.00063C18.9684 2.45028 18.519 2 17.9698 2C17.4206 2 16.9713 2.45028 16.9713 3.00063V6.62289L15.6033 5.26204C15.2138 4.87179 14.5847 4.87179 14.1853 5.26204C13.7859 5.65228 13.7959 6.28268 14.1853 6.67292L16.9613 9.45466V16.2789L10.9999 12.8368L10.0113 9.13446C9.87153 8.60413 9.32233 8.28393 8.78311 8.42402C8.25387 8.5641 7.93433 9.11445 8.07413 9.64478L8.54345 11.4159L5.46789 9.63477C4.98859 9.3546 4.37947 9.5247 4.09987 10.005C3.82027 10.4853 3.99003 11.0957 4.46934 11.3759L7.60481 13.187L5.73751 13.6873C5.20827 13.8274 4.88873 14.3777 5.02853 14.9081C5.14836 15.3583 5.54778 15.6485 5.99713 15.6485C6.087 15.6485 6.16688 15.6385 6.25675 15.6185L10.0513 14.5979L15.9528 18.01L9.99136 21.4522L6.2967 20.4615C5.76746 20.3215 5.21826 20.6316 5.06847 21.172C4.91869 21.7123 5.23823 22.2527 5.77745 22.3927L7.54489 22.863L4.46934 24.6341C3.99003 24.9143 3.83026 25.5247 4.09987 26.005C4.2896 26.3252 4.61912 26.5053 4.96862 26.5053C5.13837 26.5053 5.30813 26.4653 5.46789 26.3752L8.60337 24.5641L8.10409 26.4253C7.96429 26.9556 8.27384 27.5059 8.81306 27.646C8.90293 27.666 8.98282 27.676 9.07269 27.676C9.51205 27.676 9.92146 27.3859 10.0413 26.9356L11.0598 23.1332L16.9613 19.7211V26.6154L14.2552 29.3271C13.8658 29.7173 13.8658 30.3477 14.2552 30.738C14.6446 31.1282 15.2737 31.1282 15.6732 30.738L16.9613 29.4472V32.9994C16.9613 33.5497 17.4106 34 17.9599 34C18.5091 34 18.9584 33.5497 18.9584 32.9994V29.3771L20.3264 30.738C20.5261 30.9381 20.7758 31.0281 21.0354 31.0281C21.295 31.0281 21.5447 30.9281 21.7444 30.738C22.1338 30.3477 22.1338 29.7173 21.7444 29.3271L18.9684 26.5453V19.7211L24.9298 23.1632L25.9184 26.8655C26.0382 27.3158 26.4376 27.606 26.887 27.606C26.9768 27.606 27.0567 27.596 27.1466 27.576C27.6758 27.4359 27.9954 26.8856 27.8556 26.3552L27.3862 24.5841L30.4618 26.3652C30.6216 26.4553 30.7913 26.4953 30.9611 26.4953C31.3106 26.4953 31.6401 26.3152 31.8298 25.995C32.1094 25.5147 31.9397 24.9043 31.4603 24.6241V24.6442Z\"/>'})];export{V as snowflakeIcon,L as snowflakeIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,WAAW;EAACC,CAAC,GAAC,CAAC,WAAW,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAwqF,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,aAAa,EAACH,CAAC,IAAII,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}