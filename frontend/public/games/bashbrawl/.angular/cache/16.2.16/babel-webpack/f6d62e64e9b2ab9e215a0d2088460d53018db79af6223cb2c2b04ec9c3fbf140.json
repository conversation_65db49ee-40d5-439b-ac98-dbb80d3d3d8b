{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { isSafari as o, isWindows as i } from \"../utils/browser.js\";\nimport { onChildListMutation as t } from \"../utils/events.js\";\nimport { getFlattenedFocusableItems as r } from \"../utils/traversal.js\";\nfunction e(o = {\n  update: \"slot\"\n}) {\n  return i => {\n    i.addInitializer(i => {\n      i.ariaGridController || (i.ariaGridController = new s(i, o));\n    });\n  };\n}\nclass s {\n  constructor(o, i = {\n    update: \"slot\"\n  }) {\n    this.host = o, this.config = i, this.observers = [], o.addController(this);\n  }\n  hostConnected() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.host.updateComplete, _this.intializeColumnSort(), _this.update(), \"slot\" === _this.config.update ? _this.host.shadowRoot.addEventListener(\"slotchange\", () => _this.host.updateComplete.then(() => _this.update())) : _this.observers.push(t(_this.host, () => _this.host.updateComplete.then(() => _this.update())));\n    })();\n  }\n  hostDisconnected() {\n    this.observers.forEach(o => o?.disconnect());\n  }\n  update() {\n    this.grid = {\n      grid: this.host.grid ? this.host.grid : this.host,\n      columnRowGroup: this.host.columnRowGroup,\n      columnRow: this.host.columnRow,\n      columns: this.host.columns,\n      rowGroup: this.host.rowGroup,\n      rows: this.host.rows,\n      cells: this.host.cells,\n      footerRowGroup: this.host.footerRowGroup,\n      footerRow: this.host.footerRow,\n      footerCells: this.host.footerCells,\n      placeholderCell: this.host.placeholderCell\n    }, this.initializeGrid(), this.intializeColumns(), this.initializeRows(), this.initializeCells(), this.initializePlaceholder(), this.intializeFooter();\n  }\n  intializeColumnSort() {\n    this.host.addEventListener(\"sortChange\", o => {\n      const i = o.composedPath().find(o => \"columnheader\" === o.role);\n      i && (i.ariaSort = o.detail);\n    });\n  }\n  initializeGrid() {\n    const o = Math.max(this.grid.rows?.length, 1),\n      i = this.grid.footerRow ? 1 : 0;\n    this.grid.grid.role = \"grid\", this.grid.grid.ariaRowCount = \"\" + (1 + o + i), this.grid.grid.ariaColCount = \"\" + this.grid.columns.length;\n  }\n  intializeColumns() {\n    this.grid.columnRowGroup.role = \"rowgroup\", this.grid.columnRow.role = \"row\", this.grid.columnRow.ariaRowIndex = \"1\", this.grid.columns.forEach((o, i) => {\n      o.role = \"columnheader\", o.ariaColIndex = \"\" + (i + 1), o.ariaSort = \"none\", this.patchInvalidScreenReaderBehavior(o);\n    });\n  }\n  initializeRows() {\n    this.grid.rows?.forEach((o, i) => {\n      o.role = \"row\", o.ariaRowIndex = \"\" + (i + 2);\n    });\n  }\n  initializeCells() {\n    const o = this.grid.columns.length;\n    this.grid.cells?.forEach((i, t) => {\n      i.role || (i.role = \"gridcell\"), i.ariaColIndex = \"\" + (t % o + 1);\n    });\n  }\n  initializePlaceholder() {\n    this.grid.placeholderCell && (this.grid.placeholderCell.ariaColSpan = this.grid.grid.ariaColCount);\n  }\n  intializeFooter() {\n    this.grid.footerRowGroup && this.grid.footerRow && (this.grid.footerRowGroup.role = \"rowgroup\", this.grid.footerRow.role = \"row\", this.grid.footerRow.ariaRowIndex = \"\" + (this.grid.rows.length + 2), this.grid.footerCells?.forEach(o => o.role = \"gridcell\"), 1 === this.grid.footerCells?.length && (this.grid.footerCells[0].ariaColSpan = this.grid.grid.ariaColCount));\n  }\n  patchInvalidScreenReaderBehavior(t) {\n    if (o() || i()) {\n      const o = r(t).filter(o => !o.readonly),\n        i = t.ariaLabel ? t.ariaLabel : t?.textContent?.trim();\n      (o.length || t.resizable) && i.length && (t.ariaLabel = i, t.setAttribute(\"scope\", \"col\"));\n    }\n  }\n}\nexport { s as AriaGridController, e as ariaGrid };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "o", "isWindows", "i", "onChildListMutation", "t", "getFlattenedFocusableItems", "r", "e", "update", "addInitializer", "ariaGridController", "s", "constructor", "host", "config", "observers", "addController", "hostConnected", "_this", "_asyncToGenerator", "updateComplete", "intializeColumnSort", "shadowRoot", "addEventListener", "then", "push", "hostDisconnected", "for<PERSON>ach", "disconnect", "grid", "columnRowGroup", "columnRow", "columns", "rowGroup", "rows", "cells", "footerRowGroup", "footerRow", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder<PERSON><PERSON>", "initializeGrid", "intializeColumns", "initializeRows", "initializeCells", "initializePlaceholder", "intial<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON>", "find", "role", "ariaSort", "detail", "Math", "max", "length", "ariaRowCount", "ariaColCount", "ariaRowIndex", "ariaColIndex", "patchInvalidScreenReaderBehavior", "ariaColSpan", "filter", "readonly", "aria<PERSON><PERSON><PERSON>", "textContent", "trim", "resizable", "setAttribute", "AriaGridController", "ariaGrid"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/aria-grid.controller.js"], "sourcesContent": ["import{is<PERSON><PERSON><PERSON> as o,isWindows as i}from\"../utils/browser.js\";import{onChildListMutation as t}from\"../utils/events.js\";import{getFlattenedFocusableItems as r}from\"../utils/traversal.js\";function e(o={update:\"slot\"}){return i=>{i.addInitializer((i=>{i.ariaGridController||(i.ariaGridController=new s(i,o))}))}}class s{constructor(o,i={update:\"slot\"}){this.host=o,this.config=i,this.observers=[],o.addController(this)}async hostConnected(){await this.host.updateComplete,this.intializeColumnSort(),this.update(),\"slot\"===this.config.update?this.host.shadowRoot.addEventListener(\"slotchange\",(()=>this.host.updateComplete.then((()=>this.update())))):this.observers.push(t(this.host,(()=>this.host.updateComplete.then((()=>this.update())))))}hostDisconnected(){this.observers.forEach((o=>o?.disconnect()))}update(){this.grid={grid:this.host.grid?this.host.grid:this.host,columnRowGroup:this.host.columnRowGroup,columnRow:this.host.columnRow,columns:this.host.columns,rowGroup:this.host.rowGroup,rows:this.host.rows,cells:this.host.cells,footerRowGroup:this.host.footerRowGroup,footerRow:this.host.footerRow,footerCells:this.host.footerCells,placeholderCell:this.host.placeholderCell},this.initializeGrid(),this.intializeColumns(),this.initializeRows(),this.initializeCells(),this.initializePlaceholder(),this.intializeFooter()}intializeColumnSort(){this.host.addEventListener(\"sortChange\",(o=>{const i=o.composedPath().find((o=>\"columnheader\"===o.role));i&&(i.ariaSort=o.detail)}))}initializeGrid(){const o=Math.max(this.grid.rows?.length,1),i=this.grid.footerRow?1:0;this.grid.grid.role=\"grid\",this.grid.grid.ariaRowCount=\"\"+(1+o+i),this.grid.grid.ariaColCount=\"\"+this.grid.columns.length}intializeColumns(){this.grid.columnRowGroup.role=\"rowgroup\",this.grid.columnRow.role=\"row\",this.grid.columnRow.ariaRowIndex=\"1\",this.grid.columns.forEach(((o,i)=>{o.role=\"columnheader\",o.ariaColIndex=\"\"+(i+1),o.ariaSort=\"none\",this.patchInvalidScreenReaderBehavior(o)}))}initializeRows(){this.grid.rows?.forEach(((o,i)=>{o.role=\"row\",o.ariaRowIndex=\"\"+(i+2)}))}initializeCells(){const o=this.grid.columns.length;this.grid.cells?.forEach(((i,t)=>{i.role||(i.role=\"gridcell\"),i.ariaColIndex=\"\"+(t%o+1)}))}initializePlaceholder(){this.grid.placeholderCell&&(this.grid.placeholderCell.ariaColSpan=this.grid.grid.ariaColCount)}intializeFooter(){this.grid.footerRowGroup&&this.grid.footerRow&&(this.grid.footerRowGroup.role=\"rowgroup\",this.grid.footerRow.role=\"row\",this.grid.footerRow.ariaRowIndex=\"\"+(this.grid.rows.length+2),this.grid.footerCells?.forEach((o=>o.role=\"gridcell\")),1===this.grid.footerCells?.length&&(this.grid.footerCells[0].ariaColSpan=this.grid.grid.ariaColCount))}patchInvalidScreenReaderBehavior(t){if(o()||i()){const o=r(t).filter((o=>!o.readonly)),i=t.ariaLabel?t.ariaLabel:t?.textContent?.trim();(o.length||t.resizable)&&i.length&&(t.ariaLabel=i,t.setAttribute(\"scope\",\"col\"))}}}export{s as AriaGridController,e as ariaGrid};\n"], "mappings": ";AAAA,SAAOA,QAAQ,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,0BAA0B,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAASC,CAACA,CAACP,CAAC,GAAC;EAACQ,MAAM,EAAC;AAAM,CAAC,EAAC;EAAC,OAAON,CAAC,IAAE;IAACA,CAAC,CAACO,cAAc,CAAEP,CAAC,IAAE;MAACA,CAAC,CAACQ,kBAAkB,KAAGR,CAAC,CAACQ,kBAAkB,GAAC,IAAIC,CAAC,CAACT,CAAC,EAACF,CAAC,CAAC,CAAC;IAAA,CAAE,CAAC;EAAA,CAAC;AAAA;AAAC,MAAMW,CAAC;EAACC,WAAWA,CAACZ,CAAC,EAACE,CAAC,GAAC;IAACM,MAAM,EAAC;EAAM,CAAC,EAAC;IAAC,IAAI,CAACK,IAAI,GAACb,CAAC,EAAC,IAAI,CAACc,MAAM,GAACZ,CAAC,EAAC,IAAI,CAACa,SAAS,GAAC,EAAE,EAACf,CAAC,CAACgB,aAAa,CAAC,IAAI,CAAC;EAAA;EAAOC,aAAaA,CAAA,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAAC,MAAMD,KAAI,CAACL,IAAI,CAACO,cAAc,EAACF,KAAI,CAACG,mBAAmB,CAAC,CAAC,EAACH,KAAI,CAACV,MAAM,CAAC,CAAC,EAAC,MAAM,KAAGU,KAAI,CAACJ,MAAM,CAACN,MAAM,GAACU,KAAI,CAACL,IAAI,CAACS,UAAU,CAACC,gBAAgB,CAAC,YAAY,EAAE,MAAIL,KAAI,CAACL,IAAI,CAACO,cAAc,CAACI,IAAI,CAAE,MAAIN,KAAI,CAACV,MAAM,CAAC,CAAE,CAAE,CAAC,GAACU,KAAI,CAACH,SAAS,CAACU,IAAI,CAACrB,CAAC,CAACc,KAAI,CAACL,IAAI,EAAE,MAAIK,KAAI,CAACL,IAAI,CAACO,cAAc,CAACI,IAAI,CAAE,MAAIN,KAAI,CAACV,MAAM,CAAC,CAAE,CAAE,CAAC,CAAC;IAAA;EAAA;EAACkB,gBAAgBA,CAAA,EAAE;IAAC,IAAI,CAACX,SAAS,CAACY,OAAO,CAAE3B,CAAC,IAAEA,CAAC,EAAE4B,UAAU,CAAC,CAAE,CAAC;EAAA;EAACpB,MAAMA,CAAA,EAAE;IAAC,IAAI,CAACqB,IAAI,GAAC;MAACA,IAAI,EAAC,IAAI,CAAChB,IAAI,CAACgB,IAAI,GAAC,IAAI,CAAChB,IAAI,CAACgB,IAAI,GAAC,IAAI,CAAChB,IAAI;MAACiB,cAAc,EAAC,IAAI,CAACjB,IAAI,CAACiB,cAAc;MAACC,SAAS,EAAC,IAAI,CAAClB,IAAI,CAACkB,SAAS;MAACC,OAAO,EAAC,IAAI,CAACnB,IAAI,CAACmB,OAAO;MAACC,QAAQ,EAAC,IAAI,CAACpB,IAAI,CAACoB,QAAQ;MAACC,IAAI,EAAC,IAAI,CAACrB,IAAI,CAACqB,IAAI;MAACC,KAAK,EAAC,IAAI,CAACtB,IAAI,CAACsB,KAAK;MAACC,cAAc,EAAC,IAAI,CAACvB,IAAI,CAACuB,cAAc;MAACC,SAAS,EAAC,IAAI,CAACxB,IAAI,CAACwB,SAAS;MAACC,WAAW,EAAC,IAAI,CAACzB,IAAI,CAACyB,WAAW;MAACC,eAAe,EAAC,IAAI,CAAC1B,IAAI,CAAC0B;IAAe,CAAC,EAAC,IAAI,CAACC,cAAc,CAAC,CAAC,EAAC,IAAI,CAACC,gBAAgB,CAAC,CAAC,EAAC,IAAI,CAACC,cAAc,CAAC,CAAC,EAAC,IAAI,CAACC,eAAe,CAAC,CAAC,EAAC,IAAI,CAACC,qBAAqB,CAAC,CAAC,EAAC,IAAI,CAACC,eAAe,CAAC,CAAC;EAAA;EAACxB,mBAAmBA,CAAA,EAAE;IAAC,IAAI,CAACR,IAAI,CAACU,gBAAgB,CAAC,YAAY,EAAEvB,CAAC,IAAE;MAAC,MAAME,CAAC,GAACF,CAAC,CAAC8C,YAAY,CAAC,CAAC,CAACC,IAAI,CAAE/C,CAAC,IAAE,cAAc,KAAGA,CAAC,CAACgD,IAAK,CAAC;MAAC9C,CAAC,KAAGA,CAAC,CAAC+C,QAAQ,GAACjD,CAAC,CAACkD,MAAM,CAAC;IAAA,CAAE,CAAC;EAAA;EAACV,cAAcA,CAAA,EAAE;IAAC,MAAMxC,CAAC,GAACmD,IAAI,CAACC,GAAG,CAAC,IAAI,CAACvB,IAAI,CAACK,IAAI,EAAEmB,MAAM,EAAC,CAAC,CAAC;MAACnD,CAAC,GAAC,IAAI,CAAC2B,IAAI,CAACQ,SAAS,GAAC,CAAC,GAAC,CAAC;IAAC,IAAI,CAACR,IAAI,CAACA,IAAI,CAACmB,IAAI,GAAC,MAAM,EAAC,IAAI,CAACnB,IAAI,CAACA,IAAI,CAACyB,YAAY,GAAC,EAAE,IAAE,CAAC,GAACtD,CAAC,GAACE,CAAC,CAAC,EAAC,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAAC0B,YAAY,GAAC,EAAE,GAAC,IAAI,CAAC1B,IAAI,CAACG,OAAO,CAACqB,MAAM;EAAA;EAACZ,gBAAgBA,CAAA,EAAE;IAAC,IAAI,CAACZ,IAAI,CAACC,cAAc,CAACkB,IAAI,GAAC,UAAU,EAAC,IAAI,CAACnB,IAAI,CAACE,SAAS,CAACiB,IAAI,GAAC,KAAK,EAAC,IAAI,CAACnB,IAAI,CAACE,SAAS,CAACyB,YAAY,GAAC,GAAG,EAAC,IAAI,CAAC3B,IAAI,CAACG,OAAO,CAACL,OAAO,CAAE,CAAC3B,CAAC,EAACE,CAAC,KAAG;MAACF,CAAC,CAACgD,IAAI,GAAC,cAAc,EAAChD,CAAC,CAACyD,YAAY,GAAC,EAAE,IAAEvD,CAAC,GAAC,CAAC,CAAC,EAACF,CAAC,CAACiD,QAAQ,GAAC,MAAM,EAAC,IAAI,CAACS,gCAAgC,CAAC1D,CAAC,CAAC;IAAA,CAAE,CAAC;EAAA;EAAC0C,cAAcA,CAAA,EAAE;IAAC,IAAI,CAACb,IAAI,CAACK,IAAI,EAAEP,OAAO,CAAE,CAAC3B,CAAC,EAACE,CAAC,KAAG;MAACF,CAAC,CAACgD,IAAI,GAAC,KAAK,EAAChD,CAAC,CAACwD,YAAY,GAAC,EAAE,IAAEtD,CAAC,GAAC,CAAC,CAAC;IAAA,CAAE,CAAC;EAAA;EAACyC,eAAeA,CAAA,EAAE;IAAC,MAAM3C,CAAC,GAAC,IAAI,CAAC6B,IAAI,CAACG,OAAO,CAACqB,MAAM;IAAC,IAAI,CAACxB,IAAI,CAACM,KAAK,EAAER,OAAO,CAAE,CAACzB,CAAC,EAACE,CAAC,KAAG;MAACF,CAAC,CAAC8C,IAAI,KAAG9C,CAAC,CAAC8C,IAAI,GAAC,UAAU,CAAC,EAAC9C,CAAC,CAACuD,YAAY,GAAC,EAAE,IAAErD,CAAC,GAACJ,CAAC,GAAC,CAAC,CAAC;IAAA,CAAE,CAAC;EAAA;EAAC4C,qBAAqBA,CAAA,EAAE;IAAC,IAAI,CAACf,IAAI,CAACU,eAAe,KAAG,IAAI,CAACV,IAAI,CAACU,eAAe,CAACoB,WAAW,GAAC,IAAI,CAAC9B,IAAI,CAACA,IAAI,CAAC0B,YAAY,CAAC;EAAA;EAACV,eAAeA,CAAA,EAAE;IAAC,IAAI,CAAChB,IAAI,CAACO,cAAc,IAAE,IAAI,CAACP,IAAI,CAACQ,SAAS,KAAG,IAAI,CAACR,IAAI,CAACO,cAAc,CAACY,IAAI,GAAC,UAAU,EAAC,IAAI,CAACnB,IAAI,CAACQ,SAAS,CAACW,IAAI,GAAC,KAAK,EAAC,IAAI,CAACnB,IAAI,CAACQ,SAAS,CAACmB,YAAY,GAAC,EAAE,IAAE,IAAI,CAAC3B,IAAI,CAACK,IAAI,CAACmB,MAAM,GAAC,CAAC,CAAC,EAAC,IAAI,CAACxB,IAAI,CAACS,WAAW,EAAEX,OAAO,CAAE3B,CAAC,IAAEA,CAAC,CAACgD,IAAI,GAAC,UAAW,CAAC,EAAC,CAAC,KAAG,IAAI,CAACnB,IAAI,CAACS,WAAW,EAAEe,MAAM,KAAG,IAAI,CAACxB,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC,CAACqB,WAAW,GAAC,IAAI,CAAC9B,IAAI,CAACA,IAAI,CAAC0B,YAAY,CAAC,CAAC;EAAA;EAACG,gCAAgCA,CAACtD,CAAC,EAAC;IAAC,IAAGJ,CAAC,CAAC,CAAC,IAAEE,CAAC,CAAC,CAAC,EAAC;MAAC,MAAMF,CAAC,GAACM,CAAC,CAACF,CAAC,CAAC,CAACwD,MAAM,CAAE5D,CAAC,IAAE,CAACA,CAAC,CAAC6D,QAAS,CAAC;QAAC3D,CAAC,GAACE,CAAC,CAAC0D,SAAS,GAAC1D,CAAC,CAAC0D,SAAS,GAAC1D,CAAC,EAAE2D,WAAW,EAAEC,IAAI,CAAC,CAAC;MAAC,CAAChE,CAAC,CAACqD,MAAM,IAAEjD,CAAC,CAAC6D,SAAS,KAAG/D,CAAC,CAACmD,MAAM,KAAGjD,CAAC,CAAC0D,SAAS,GAAC5D,CAAC,EAACE,CAAC,CAAC8D,YAAY,CAAC,OAAO,EAAC,KAAK,CAAC,CAAC;IAAA;EAAC;AAAC;AAAC,SAAOvD,CAAC,IAAIwD,kBAAkB,EAAC5D,CAAC,IAAI6D,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}