{"ast": null, "code": "var a = {\n  \"@@functional/placeholder\": !0\n};\nexport { a as default };", "map": {"version": 3, "names": ["a", "default"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/__.js"], "sourcesContent": ["var a={\"@@functional/placeholder\":!0};export{a as default};\n"], "mappings": "AAAA,IAAIA,CAAC,GAAC;EAAC,0BAA0B,EAAC,CAAC;AAAC,CAAC;AAAC,SAAOA,CAAC,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}