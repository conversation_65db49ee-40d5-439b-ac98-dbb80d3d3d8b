{"ast": null, "code": "import { KeyNavigationCode as r } from \"../utils/keycodes.js\";\nimport { getFlattenedDOMTree as o } from \"../utils/traversal.js\";\nfunction t(t, e, n) {\n  const i = t.find(r => 0 === r.tabIndex),\n    l = e.find(r => o(r).find(r => r === i)),\n    w = Array.from(o(l)).filter(r => !!t.find(o => o === r)),\n    f = e.length - 1,\n    a = w.length - 1,\n    {\n      code: d,\n      ctrlKey: A,\n      dir: c\n    } = n;\n  let g = w.indexOf(i),\n    h = e.indexOf(l);\n  const s = \"rtl\" === c ? r.ArrowRight : r.Arrow<PERSON>eft,\n    p = \"rtl\" === c ? r.ArrowLeft : r.ArrowRight;\n  return d === r.ArrowUp && 0 !== h ? h -= 1 : d === r.ArrowDown && h < f ? h += 1 : d === s && 0 !== g ? g -= 1 : d === p && g < a ? g += 1 : d === r.End ? (g = a, A && (h = f)) : d === r.Home ? (g = 0, A && (h = 0)) : d === r.PageUp ? h = h - 4 > 0 ? h - 4 : 0 : d === r.PageDown && (h = h + 4 < f ? h + 4 : f), {\n    x: g,\n    y: h\n  };\n}\nfunction e(o, t, e) {\n  const {\n    code: n,\n    layout: i,\n    loop: l,\n    dir: w\n  } = e;\n  let f = t.indexOf(o);\n  const a = f,\n    d = \"rtl\" === w ? r.ArrowRight : r.ArrowLeft,\n    A = \"rtl\" === w ? r.ArrowLeft : r.ArrowRight,\n    c = t.length - 1;\n  return \"horizontal\" !== i && n === r.ArrowUp && 0 !== f ? f -= 1 : \"horizontal\" !== i && n === r.ArrowUp && 0 === f && l ? f = c : \"horizontal\" !== i && n === r.ArrowDown && f < c ? f += 1 : \"horizontal\" !== i && n === r.ArrowDown && f === c && l ? f = 0 : \"vertical\" !== i && n === d && 0 !== f ? f -= 1 : \"vertical\" !== i && n === A && f < c ? f += 1 : n === r.End ? f = c : n === r.Home ? f = 0 : n === r.PageUp ? f = f - 4 > 0 ? f - 4 : 0 : n === r.PageDown && (f = f + 4 < c ? f + 4 : c), {\n    next: f,\n    previous: a\n  };\n}\nexport { t as getNextKeyGridItem, e as getNextKeyListItem };", "map": {"version": 3, "names": ["KeyNavigationCode", "r", "getFlattenedDOMTree", "o", "t", "e", "n", "i", "find", "tabIndex", "l", "w", "Array", "from", "filter", "f", "length", "a", "code", "d", "ctrl<PERSON>ey", "A", "dir", "c", "g", "indexOf", "h", "s", "ArrowRight", "ArrowLeft", "p", "ArrowUp", "ArrowDown", "End", "Home", "PageUp", "PageDown", "x", "y", "layout", "loop", "next", "previous", "getNextKeyGridItem", "getNextKeyListItem"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/key-navigation.utils.js"], "sourcesContent": ["import{KeyNavigationCode as r}from\"../utils/keycodes.js\";import{getFlattenedDOMTree as o}from\"../utils/traversal.js\";function t(t,e,n){const i=t.find((r=>0===r.tabIndex)),l=e.find((r=>o(r).find((r=>r===i)))),w=Array.from(o(l)).filter((r=>!!t.find((o=>o===r)))),f=e.length-1,a=w.length-1,{code:d,ctrlKey:A,dir:c}=n;let g=w.indexOf(i),h=e.indexOf(l);const s=\"rtl\"===c?r.ArrowRight:r.ArrowLeft,p=\"rtl\"===c?r.ArrowLeft:r.ArrowRight;return d===r.ArrowUp&&0!==h?h-=1:d===r.ArrowDown&&h<f?h+=1:d===s&&0!==g?g-=1:d===p&&g<a?g+=1:d===r.End?(g=a,A&&(h=f)):d===r.Home?(g=0,A&&(h=0)):d===r.PageUp?h=h-4>0?h-4:0:d===r.PageDown&&(h=h+4<f?h+4:f),{x:g,y:h}}function e(o,t,e){const{code:n,layout:i,loop:l,dir:w}=e;let f=t.indexOf(o);const a=f,d=\"rtl\"===w?r.ArrowRight:r.ArrowLeft,A=\"rtl\"===w?r.ArrowLeft:r.ArrowRight,c=t.length-1;return\"horizontal\"!==i&&n===r.ArrowUp&&0!==f?f-=1:\"horizontal\"!==i&&n===r.ArrowUp&&0===f&&l?f=c:\"horizontal\"!==i&&n===r.ArrowDown&&f<c?f+=1:\"horizontal\"!==i&&n===r.ArrowDown&&f===c&&l?f=0:\"vertical\"!==i&&n===d&&0!==f?f-=1:\"vertical\"!==i&&n===A&&f<c?f+=1:n===r.End?f=c:n===r.Home?f=0:n===r.PageUp?f=f-4>0?f-4:0:n===r.PageDown&&(f=f+4<c?f+4:c),{next:f,previous:a}}export{t as getNextKeyGridItem,e as getNextKeyListItem};\n"], "mappings": "AAAA,SAAOA,iBAAiB,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAASC,CAACA,CAACA,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,MAAMC,CAAC,GAACH,CAAC,CAACI,IAAI,CAAEP,CAAC,IAAE,CAAC,KAAGA,CAAC,CAACQ,QAAS,CAAC;IAACC,CAAC,GAACL,CAAC,CAACG,IAAI,CAAEP,CAAC,IAAEE,CAAC,CAACF,CAAC,CAAC,CAACO,IAAI,CAAEP,CAAC,IAAEA,CAAC,KAAGM,CAAE,CAAE,CAAC;IAACI,CAAC,GAACC,KAAK,CAACC,IAAI,CAACV,CAAC,CAACO,CAAC,CAAC,CAAC,CAACI,MAAM,CAAEb,CAAC,IAAE,CAAC,CAACG,CAAC,CAACI,IAAI,CAAEL,CAAC,IAAEA,CAAC,KAAGF,CAAE,CAAE,CAAC;IAACc,CAAC,GAACV,CAAC,CAACW,MAAM,GAAC,CAAC;IAACC,CAAC,GAACN,CAAC,CAACK,MAAM,GAAC,CAAC;IAAC;MAACE,IAAI,EAACC,CAAC;MAACC,OAAO,EAACC,CAAC;MAACC,GAAG,EAACC;IAAC,CAAC,GAACjB,CAAC;EAAC,IAAIkB,CAAC,GAACb,CAAC,CAACc,OAAO,CAAClB,CAAC,CAAC;IAACmB,CAAC,GAACrB,CAAC,CAACoB,OAAO,CAACf,CAAC,CAAC;EAAC,MAAMiB,CAAC,GAAC,KAAK,KAAGJ,CAAC,GAACtB,CAAC,CAAC2B,UAAU,GAAC3B,CAAC,CAAC4B,SAAS;IAACC,CAAC,GAAC,KAAK,KAAGP,CAAC,GAACtB,CAAC,CAAC4B,SAAS,GAAC5B,CAAC,CAAC2B,UAAU;EAAC,OAAOT,CAAC,KAAGlB,CAAC,CAAC8B,OAAO,IAAE,CAAC,KAAGL,CAAC,GAACA,CAAC,IAAE,CAAC,GAACP,CAAC,KAAGlB,CAAC,CAAC+B,SAAS,IAAEN,CAAC,GAACX,CAAC,GAACW,CAAC,IAAE,CAAC,GAACP,CAAC,KAAGQ,CAAC,IAAE,CAAC,KAAGH,CAAC,GAACA,CAAC,IAAE,CAAC,GAACL,CAAC,KAAGW,CAAC,IAAEN,CAAC,GAACP,CAAC,GAACO,CAAC,IAAE,CAAC,GAACL,CAAC,KAAGlB,CAAC,CAACgC,GAAG,IAAET,CAAC,GAACP,CAAC,EAACI,CAAC,KAAGK,CAAC,GAACX,CAAC,CAAC,IAAEI,CAAC,KAAGlB,CAAC,CAACiC,IAAI,IAAEV,CAAC,GAAC,CAAC,EAACH,CAAC,KAAGK,CAAC,GAAC,CAAC,CAAC,IAAEP,CAAC,KAAGlB,CAAC,CAACkC,MAAM,GAACT,CAAC,GAACA,CAAC,GAAC,CAAC,GAAC,CAAC,GAACA,CAAC,GAAC,CAAC,GAAC,CAAC,GAACP,CAAC,KAAGlB,CAAC,CAACmC,QAAQ,KAAGV,CAAC,GAACA,CAAC,GAAC,CAAC,GAACX,CAAC,GAACW,CAAC,GAAC,CAAC,GAACX,CAAC,CAAC,EAAC;IAACsB,CAAC,EAACb,CAAC;IAACc,CAAC,EAACZ;EAAC,CAAC;AAAA;AAAC,SAASrB,CAACA,CAACF,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,MAAK;IAACa,IAAI,EAACZ,CAAC;IAACiC,MAAM,EAAChC,CAAC;IAACiC,IAAI,EAAC9B,CAAC;IAACY,GAAG,EAACX;EAAC,CAAC,GAACN,CAAC;EAAC,IAAIU,CAAC,GAACX,CAAC,CAACqB,OAAO,CAACtB,CAAC,CAAC;EAAC,MAAMc,CAAC,GAACF,CAAC;IAACI,CAAC,GAAC,KAAK,KAAGR,CAAC,GAACV,CAAC,CAAC2B,UAAU,GAAC3B,CAAC,CAAC4B,SAAS;IAACR,CAAC,GAAC,KAAK,KAAGV,CAAC,GAACV,CAAC,CAAC4B,SAAS,GAAC5B,CAAC,CAAC2B,UAAU;IAACL,CAAC,GAACnB,CAAC,CAACY,MAAM,GAAC,CAAC;EAAC,OAAM,YAAY,KAAGT,CAAC,IAAED,CAAC,KAAGL,CAAC,CAAC8B,OAAO,IAAE,CAAC,KAAGhB,CAAC,GAACA,CAAC,IAAE,CAAC,GAAC,YAAY,KAAGR,CAAC,IAAED,CAAC,KAAGL,CAAC,CAAC8B,OAAO,IAAE,CAAC,KAAGhB,CAAC,IAAEL,CAAC,GAACK,CAAC,GAACQ,CAAC,GAAC,YAAY,KAAGhB,CAAC,IAAED,CAAC,KAAGL,CAAC,CAAC+B,SAAS,IAAEjB,CAAC,GAACQ,CAAC,GAACR,CAAC,IAAE,CAAC,GAAC,YAAY,KAAGR,CAAC,IAAED,CAAC,KAAGL,CAAC,CAAC+B,SAAS,IAAEjB,CAAC,KAAGQ,CAAC,IAAEb,CAAC,GAACK,CAAC,GAAC,CAAC,GAAC,UAAU,KAAGR,CAAC,IAAED,CAAC,KAAGa,CAAC,IAAE,CAAC,KAAGJ,CAAC,GAACA,CAAC,IAAE,CAAC,GAAC,UAAU,KAAGR,CAAC,IAAED,CAAC,KAAGe,CAAC,IAAEN,CAAC,GAACQ,CAAC,GAACR,CAAC,IAAE,CAAC,GAACT,CAAC,KAAGL,CAAC,CAACgC,GAAG,GAAClB,CAAC,GAACQ,CAAC,GAACjB,CAAC,KAAGL,CAAC,CAACiC,IAAI,GAACnB,CAAC,GAAC,CAAC,GAACT,CAAC,KAAGL,CAAC,CAACkC,MAAM,GAACpB,CAAC,GAACA,CAAC,GAAC,CAAC,GAAC,CAAC,GAACA,CAAC,GAAC,CAAC,GAAC,CAAC,GAACT,CAAC,KAAGL,CAAC,CAACmC,QAAQ,KAAGrB,CAAC,GAACA,CAAC,GAAC,CAAC,GAACQ,CAAC,GAACR,CAAC,GAAC,CAAC,GAACQ,CAAC,CAAC,EAAC;IAACkB,IAAI,EAAC1B,CAAC;IAAC2B,QAAQ,EAACzB;EAAC,CAAC;AAAA;AAAC,SAAOb,CAAC,IAAIuC,kBAAkB,EAACtC,CAAC,IAAIuC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}