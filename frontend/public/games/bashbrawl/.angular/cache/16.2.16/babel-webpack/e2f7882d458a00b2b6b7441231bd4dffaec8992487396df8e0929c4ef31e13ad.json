{"ast": null, "code": "let e = !1,\n  t = !1;\nfunction o(e, t, o) {\n  Object.defineProperty(e, o, {\n    configurable: !0,\n    enumerable: !0,\n    get: function () {\n      return this.hasAttribute(t) ? this.getAttribute(t) : null;\n    },\n    set: function (e) {\n      null !== e ? this.setAttribute(t, e) : this.removeAttribute(t);\n    }\n  });\n}\ne || Element.prototype.hasOwnProperty(\"role\") || (o(Element.prototype, \"role\", \"role\"), e = !0), t || Element.prototype.hasOwnProperty(\"ariaLabel\") || (t = !0, [\"ActiveDescendant\", \"Atomic\", \"AutoComplete\", \"Busy\", \"Checked\", \"ColCount\", \"ColIndex\", \"ColSpan\", \"Controls\", \"Current\", \"DescribedBy\", \"Details\", \"Disabled\", \"ErrorMessage\", \"Expanded\", \"FlowTo\", \"HasPopup\", \"Hidden\", \"Invalid\", \"KeyShortcuts\", \"Label\", \"LabelledBy\", \"Level\", \"Live\", \"Modal\", \"MultiLine\", \"MultiSelectable\", \"Orientation\", \"Owns\", \"Placeholder\", \"PosInSet\", \"Pressed\", \"ReadOnly\", \"Relevant\", \"Required\", \"RoleDescription\", \"RowCount\", \"RowIndex\", \"RowSpan\", \"Selected\", \"SetSize\", \"Sort\", \"ValueMax\", \"ValueMin\", \"ValueNow\", \"ValueText\"].forEach(e => o(Element.prototype, \"aria-\" + e.toLowerCase(), \"aria\" + e)));\nexport { o as reflect };", "map": {"version": 3, "names": ["e", "t", "o", "Object", "defineProperty", "configurable", "enumerable", "get", "hasAttribute", "getAttribute", "set", "setAttribute", "removeAttribute", "Element", "prototype", "hasOwnProperty", "for<PERSON>ach", "toLowerCase", "reflect"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/polyfills/aria-reflect.js"], "sourcesContent": ["let e=!1,t=!1;function o(e,t,o){Object.defineProperty(e,o,{configurable:!0,enumerable:!0,get:function(){return this.hasAttribute(t)?this.getAttribute(t):null},set:function(e){null!==e?this.setAttribute(t,e):this.removeAttribute(t)}})}e||Element.prototype.hasOwnProperty(\"role\")||(o(Element.prototype,\"role\",\"role\"),e=!0),t||Element.prototype.hasOwnProperty(\"ariaLabel\")||(t=!0,[\"ActiveDescendant\",\"Atomic\",\"AutoComplete\",\"Busy\",\"Checked\",\"ColCount\",\"ColIndex\",\"ColSpan\",\"Controls\",\"Current\",\"DescribedBy\",\"Details\",\"Disabled\",\"ErrorMessage\",\"Expanded\",\"FlowTo\",\"HasPopup\",\"Hidden\",\"Invalid\",\"KeyShortcuts\",\"Label\",\"LabelledBy\",\"Level\",\"Live\",\"Modal\",\"MultiLine\",\"MultiSelectable\",\"Orientation\",\"Owns\",\"Placeholder\",\"PosInSet\",\"Pressed\",\"ReadOnly\",\"Relevant\",\"Required\",\"RoleDescription\",\"RowCount\",\"RowIndex\",\"RowSpan\",\"Selected\",\"SetSize\",\"Sort\",\"ValueMax\",\"ValueMin\",\"ValueNow\",\"ValueText\"].forEach((e=>o(Element.prototype,\"aria-\"+e.toLowerCase(),\"aria\"+e))));export{o as reflect};\n"], "mappings": "AAAA,IAAIA,CAAC,GAAC,CAAC,CAAC;EAACC,CAAC,GAAC,CAAC,CAAC;AAAC,SAASC,CAACA,CAACF,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAACC,MAAM,CAACC,cAAc,CAACJ,CAAC,EAACE,CAAC,EAAC;IAACG,YAAY,EAAC,CAAC,CAAC;IAACC,UAAU,EAAC,CAAC,CAAC;IAACC,GAAG,EAAC,SAAAA,CAAA,EAAU;MAAC,OAAO,IAAI,CAACC,YAAY,CAACP,CAAC,CAAC,GAAC,IAAI,CAACQ,YAAY,CAACR,CAAC,CAAC,GAAC,IAAI;IAAA,CAAC;IAACS,GAAG,EAAC,SAAAA,CAASV,CAAC,EAAC;MAAC,IAAI,KAAGA,CAAC,GAAC,IAAI,CAACW,YAAY,CAACV,CAAC,EAACD,CAAC,CAAC,GAAC,IAAI,CAACY,eAAe,CAACX,CAAC,CAAC;IAAA;EAAC,CAAC,CAAC;AAAA;AAACD,CAAC,IAAEa,OAAO,CAACC,SAAS,CAACC,cAAc,CAAC,MAAM,CAAC,KAAGb,CAAC,CAACW,OAAO,CAACC,SAAS,EAAC,MAAM,EAAC,MAAM,CAAC,EAACd,CAAC,GAAC,CAAC,CAAC,CAAC,EAACC,CAAC,IAAEY,OAAO,CAACC,SAAS,CAACC,cAAc,CAAC,WAAW,CAAC,KAAGd,CAAC,GAAC,CAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,QAAQ,EAAC,cAAc,EAAC,MAAM,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,EAAC,SAAS,EAAC,UAAU,EAAC,SAAS,EAAC,aAAa,EAAC,SAAS,EAAC,UAAU,EAAC,cAAc,EAAC,UAAU,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,SAAS,EAAC,cAAc,EAAC,OAAO,EAAC,YAAY,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,WAAW,EAAC,iBAAiB,EAAC,aAAa,EAAC,MAAM,EAAC,aAAa,EAAC,UAAU,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,EAAC,UAAU,EAAC,iBAAiB,EAAC,UAAU,EAAC,UAAU,EAAC,SAAS,EAAC,UAAU,EAAC,SAAS,EAAC,MAAM,EAAC,UAAU,EAAC,UAAU,EAAC,UAAU,EAAC,WAAW,CAAC,CAACe,OAAO,CAAEhB,CAAC,IAAEE,CAAC,CAACW,OAAO,CAACC,SAAS,EAAC,OAAO,GAACd,CAAC,CAACiB,WAAW,CAAC,CAAC,EAAC,MAAM,GAACjB,CAAC,CAAE,CAAC,CAAC;AAAC,SAAOE,CAAC,IAAIgB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}