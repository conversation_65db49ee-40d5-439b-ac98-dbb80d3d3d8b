{"ast": null, "code": "import { deepClone as t } from \"../utils/identity.js\";\nconst r = new Map([[\"arrow-left\", \"ArrowLeft\"], [\"arrow-right\", \"ArrowRight\"], [\"arrow-up\", \"ArrowUp\"], [\"arrow-down\", \"ArrowDown\"], [\"tab\", \"Tab\"], [\"enter\", \"Enter\"], [\"escape\", \"Escape\"], [\"space\", \" \"], [\"home\", \"Home\"], [\"end\", \"End\"]]);\nclass e {\n  static get keycodes() {\n    return t(r);\n  }\n  static add(t, e) {\n    r.set(t, e);\n  }\n  static has(t) {\n    return r.has(t);\n  }\n  static getCode(t, r = this.keycodes) {\n    return o(t, r);\n  }\n}\nfunction o(t, r) {\n  return r.get(t) || \"\";\n}\nexport { e as KeyCodeService, o as getKeycodeFromRegistry };", "map": {"version": 3, "names": ["deepClone", "t", "r", "Map", "e", "keycodes", "add", "set", "has", "getCode", "o", "get", "KeyCodeService", "getKeycodeFromRegistry"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/services/keycodes.service.js"], "sourcesContent": ["import{deepClone as t}from\"../utils/identity.js\";const r=new Map([[\"arrow-left\",\"ArrowLeft\"],[\"arrow-right\",\"ArrowRight\"],[\"arrow-up\",\"ArrowUp\"],[\"arrow-down\",\"ArrowDown\"],[\"tab\",\"Tab\"],[\"enter\",\"Enter\"],[\"escape\",\"Escape\"],[\"space\",\" \"],[\"home\",\"Home\"],[\"end\",\"End\"]]);class e{static get keycodes(){return t(r)}static add(t,e){r.set(t,e)}static has(t){return r.has(t)}static getCode(t,r=this.keycodes){return o(t,r)}}function o(t,r){return r.get(t)||\"\"}export{e as KeyCodeService,o as getKeycodeFromRegistry};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,QAAK,sBAAsB;AAAC,MAAMC,CAAC,GAAC,IAAIC,GAAG,CAAC,CAAC,CAAC,YAAY,EAAC,WAAW,CAAC,EAAC,CAAC,aAAa,EAAC,YAAY,CAAC,EAAC,CAAC,UAAU,EAAC,SAAS,CAAC,EAAC,CAAC,YAAY,EAAC,WAAW,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,OAAO,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,CAAC,CAAC,CAAC;AAAC,MAAMC,CAAC;EAAC,WAAWC,QAAQA,CAAA,EAAE;IAAC,OAAOJ,CAAC,CAACC,CAAC,CAAC;EAAA;EAAC,OAAOI,GAAGA,CAACL,CAAC,EAACG,CAAC,EAAC;IAACF,CAAC,CAACK,GAAG,CAACN,CAAC,EAACG,CAAC,CAAC;EAAA;EAAC,OAAOI,GAAGA,CAACP,CAAC,EAAC;IAAC,OAAOC,CAAC,CAACM,GAAG,CAACP,CAAC,CAAC;EAAA;EAAC,OAAOQ,OAAOA,CAACR,CAAC,EAACC,CAAC,GAAC,IAAI,CAACG,QAAQ,EAAC;IAAC,OAAOK,CAAC,CAACT,CAAC,EAACC,CAAC,CAAC;EAAA;AAAC;AAAC,SAASQ,CAACA,CAACT,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACS,GAAG,CAACV,CAAC,CAAC,IAAE,EAAE;AAAA;AAAC,SAAOG,CAAC,IAAIQ,cAAc,EAACF,CAAC,IAAIG,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}