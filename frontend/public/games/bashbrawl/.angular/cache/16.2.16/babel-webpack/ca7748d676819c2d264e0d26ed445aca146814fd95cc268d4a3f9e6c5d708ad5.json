{"ast": null, "code": "import { renderIcon as a } from \"../icon.renderer.js\";\nconst l = \"detail-collapse\",\n  e = [\"detail-collapse\", a({\n    outline: '<g><path d=\"M32 18H18.35l2.89-3a1 1 0 10-1.44-1.39l-4.51 4.67-.68.71.68.71 4.51 4.67a1 1 0 00.72.31 1.05 1.05 0 00.7-.28 1 1 0 000-1.42l-2.89-3H32a1 1 0 000-2zM7 32a1 1 0 01-1-1V5a1 1 0 012 0v26a1 1 0 01-1 1zM11 32a1 1 0 01-1-1V5a1 1 0 012 0v26a1 1 0 01-1 1z\"/></g>'\n  })];\nexport { e as detailCollapseIcon, l as detailCollapseIconName };", "map": {"version": 3, "names": ["renderIcon", "a", "l", "e", "outline", "detailCollapseIcon", "detailCollapseIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/detail-collapse.js"], "sourcesContent": ["import{renderIcon as a}from\"../icon.renderer.js\";const l=\"detail-collapse\",e=[\"detail-collapse\",a({outline:'<g><path d=\"M32 18H18.35l2.89-3a1 1 0 10-1.44-1.39l-4.51 4.67-.68.71.68.71 4.51 4.67a1 1 0 00.72.31 1.05 1.05 0 00.7-.28 1 1 0 000-1.42l-2.89-3H32a1 1 0 000-2zM7 32a1 1 0 01-1-1V5a1 1 0 012 0v26a1 1 0 01-1 1zM11 32a1 1 0 01-1-1V5a1 1 0 012 0v26a1 1 0 01-1 1z\"/></g>'})];export{e as detailCollapseIcon,l as detailCollapseIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,iBAAiB;EAACC,CAAC,GAAC,CAAC,iBAAiB,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAA2Q,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,kBAAkB,EAACH,CAAC,IAAII,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}