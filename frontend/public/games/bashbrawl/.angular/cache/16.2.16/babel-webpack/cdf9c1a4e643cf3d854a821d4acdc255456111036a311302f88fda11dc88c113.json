{"ast": null, "code": "function t() {\n  return t => t.addInitializer(t => new s(t));\n}\nclass s {\n  constructor(t) {\n    this.host = t, this.host.addController(this);\n  }\n  hostUpdated() {\n    null !== this.host.selected && void 0 !== this.host.selected && (this.host.ariaSelected = this.host.selected ? \"true\" : \"false\");\n  }\n}\nexport { s as AriaSelectedController, t as ariaSelected };", "map": {"version": 3, "names": ["t", "addInitializer", "s", "constructor", "host", "addController", "hostUpdated", "selected", "ariaSelected", "AriaSelectedController"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/aria-selected.controller.js"], "sourcesContent": ["function t(){return t=>t.addInitializer((t=>new s(t)))}class s{constructor(t){this.host=t,this.host.addController(this)}hostUpdated(){null!==this.host.selected&&void 0!==this.host.selected&&(this.host.ariaSelected=this.host.selected?\"true\":\"false\")}}export{s as AriaSelectedController,t as ariaSelected};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAOA,CAAC,IAAEA,CAAC,CAACC,cAAc,CAAED,CAAC,IAAE,IAAIE,CAAC,CAACF,CAAC,CAAE,CAAC;AAAA;AAAC,MAAME,CAAC;EAACC,WAAWA,CAACH,CAAC,EAAC;IAAC,IAAI,CAACI,IAAI,GAACJ,CAAC,EAAC,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;EAAA;EAACC,WAAWA,CAAA,EAAE;IAAC,IAAI,KAAG,IAAI,CAACF,IAAI,CAACG,QAAQ,IAAE,KAAK,CAAC,KAAG,IAAI,CAACH,IAAI,CAACG,QAAQ,KAAG,IAAI,CAACH,IAAI,CAACI,YAAY,GAAC,IAAI,CAACJ,IAAI,CAACG,QAAQ,GAAC,MAAM,GAAC,OAAO,CAAC;EAAA;AAAC;AAAC,SAAOL,CAAC,IAAIO,sBAAsB,EAACT,CAAC,IAAIQ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}