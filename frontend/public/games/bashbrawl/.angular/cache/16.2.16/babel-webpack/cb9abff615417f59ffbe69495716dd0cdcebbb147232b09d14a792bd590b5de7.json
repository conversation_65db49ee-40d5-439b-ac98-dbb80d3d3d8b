{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nvar _a, _b, _c, _d;\nconst DEV_MODE = true;\nconst ENABLE_EXTRA_SECURITY_HOOKS = true;\nconst ENABLE_SHADYDOM_NOPATCH = true;\nconst NODE_MODE = false;\n// Use window for browser builds because IE11 doesn't have globalThis.\nconst global = NODE_MODE ? globalThis : window;\n/**\n * Useful for visualizing and logging insights into what the Lit template system is doing.\n *\n * Compiled out of prod mode builds.\n */\nconst debugLogEvent = DEV_MODE ? event => {\n  const shouldEmit = global.emitLitDebugLogEvents;\n  if (!shouldEmit) {\n    return;\n  }\n  global.dispatchEvent(new CustomEvent('lit-debug', {\n    detail: event\n  }));\n} : undefined;\n// Used for connecting beginRender and endRender events when there are nested\n// renders when errors are thrown preventing an endRender event from being\n// called.\nlet debugLogRenderId = 0;\nlet issueWarning;\nif (DEV_MODE) {\n  (_a = global.litIssuedWarnings) !== null && _a !== void 0 ? _a : global.litIssuedWarnings = new Set();\n  // Issue a warning, if we haven't already.\n  issueWarning = (code, warning) => {\n    warning += code ? ` See https://lit.dev/msg/${code} for more information.` : '';\n    if (!global.litIssuedWarnings.has(warning)) {\n      console.warn(warning);\n      global.litIssuedWarnings.add(warning);\n    }\n  };\n  issueWarning('dev-mode', `Lit is in dev mode. Not recommended for production!`);\n}\nconst wrap = ENABLE_SHADYDOM_NOPATCH && ((_b = global.ShadyDOM) === null || _b === void 0 ? void 0 : _b.inUse) && ((_c = global.ShadyDOM) === null || _c === void 0 ? void 0 : _c.noPatch) === true ? global.ShadyDOM.wrap : node => node;\nconst trustedTypes = global.trustedTypes;\n/**\n * Our TrustedTypePolicy for HTML which is declared using the html template\n * tag function.\n *\n * That HTML is a developer-authored constant, and is parsed with innerHTML\n * before any untrusted expressions have been mixed in. Therefor it is\n * considered safe by construction.\n */\nconst policy = trustedTypes ? trustedTypes.createPolicy('lit-html', {\n  createHTML: s => s\n}) : undefined;\nconst identityFunction = value => value;\nconst noopSanitizer = (_node, _name, _type) => identityFunction;\n/** Sets the global sanitizer factory. */\nconst setSanitizer = newSanitizer => {\n  if (!ENABLE_EXTRA_SECURITY_HOOKS) {\n    return;\n  }\n  if (sanitizerFactoryInternal !== noopSanitizer) {\n    throw new Error(`Attempted to overwrite existing lit-html security policy.` + ` setSanitizeDOMValueFactory should be called at most once.`);\n  }\n  sanitizerFactoryInternal = newSanitizer;\n};\n/**\n * Only used in internal tests, not a part of the public API.\n */\nconst _testOnlyClearSanitizerFactoryDoNotCallOrElse = () => {\n  sanitizerFactoryInternal = noopSanitizer;\n};\nconst createSanitizer = (node, name, type) => {\n  return sanitizerFactoryInternal(node, name, type);\n};\n// Added to an attribute name to mark the attribute as bound so we can find\n// it easily.\nconst boundAttributeSuffix = '$lit$';\n// This marker is used in many syntactic positions in HTML, so it must be\n// a valid element name and attribute name. We don't support dynamic names (yet)\n// but this at least ensures that the parse tree is closer to the template\n// intention.\nconst marker = `lit$${String(Math.random()).slice(9)}$`;\n// String used to tell if a comment is a marker comment\nconst markerMatch = '?' + marker;\n// Text used to insert a comment marker node. We use processing instruction\n// syntax because it's slightly smaller, but parses as a comment node.\nconst nodeMarker = `<${markerMatch}>`;\nconst d = NODE_MODE && global.document === undefined ? {\n  createTreeWalker() {\n    return {};\n  }\n} : document;\n// Creates a dynamic marker. We never have to search for these in the DOM.\nconst createMarker = () => d.createComment('');\nconst isPrimitive = value => value === null || typeof value != 'object' && typeof value != 'function';\nconst isArray = Array.isArray;\nconst isIterable = value => isArray(value) ||\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntypeof (value === null || value === void 0 ? void 0 : value[Symbol.iterator]) === 'function';\nconst SPACE_CHAR = `[ \\t\\n\\f\\r]`;\nconst ATTR_VALUE_CHAR = `[^ \\t\\n\\f\\r\"'\\`<>=]`;\nconst NAME_CHAR = `[^\\\\s\"'>=/]`;\n// These regexes represent the five parsing states that we care about in the\n// Template's HTML scanner. They match the *end* of the state they're named\n// after.\n// Depending on the match, we transition to a new state. If there's no match,\n// we stay in the same state.\n// Note that the regexes are stateful. We utilize lastIndex and sync it\n// across the multiple regexes used. In addition to the five regexes below\n// we also dynamically create a regex to find the matching end tags for raw\n// text elements.\n/**\n * End of text is: `<` followed by:\n *   (comment start) or (tag) or (dynamic tag binding)\n */\nconst textEndRegex = /<(?:(!--|\\/[^a-zA-Z])|(\\/?[a-zA-Z][^>\\s]*)|(\\/?$))/g;\nconst COMMENT_START = 1;\nconst TAG_NAME = 2;\nconst DYNAMIC_TAG_NAME = 3;\nconst commentEndRegex = /-->/g;\n/**\n * Comments not started with <!--, like </{, can be ended by a single `>`\n */\nconst comment2EndRegex = />/g;\n/**\n * The tagEnd regex matches the end of the \"inside an opening\" tag syntax\n * position. It either matches a `>`, an attribute-like sequence, or the end\n * of the string after a space (attribute-name position ending).\n *\n * See attributes in the HTML spec:\n * https://www.w3.org/TR/html5/syntax.html#elements-attributes\n *\n * \" \\t\\n\\f\\r\" are HTML space characters:\n * https://infra.spec.whatwg.org/#ascii-whitespace\n *\n * So an attribute is:\n *  * The name: any character except a whitespace character, (\"), ('), \">\",\n *    \"=\", or \"/\". Note: this is different from the HTML spec which also excludes control characters.\n *  * Followed by zero or more space characters\n *  * Followed by \"=\"\n *  * Followed by zero or more space characters\n *  * Followed by:\n *    * Any character except space, ('), (\"), \"<\", \">\", \"=\", (`), or\n *    * (\") then any non-(\"), or\n *    * (') then any non-(')\n */\nconst tagEndRegex = new RegExp(`>|${SPACE_CHAR}(?:(${NAME_CHAR}+)(${SPACE_CHAR}*=${SPACE_CHAR}*(?:${ATTR_VALUE_CHAR}|(\"|')|))|$)`, 'g');\nconst ENTIRE_MATCH = 0;\nconst ATTRIBUTE_NAME = 1;\nconst SPACES_AND_EQUALS = 2;\nconst QUOTE_CHAR = 3;\nconst singleQuoteAttrEndRegex = /'/g;\nconst doubleQuoteAttrEndRegex = /\"/g;\n/**\n * Matches the raw text elements.\n *\n * Comments are not parsed within raw text elements, so we need to search their\n * text content for marker strings.\n */\nconst rawTextElement = /^(?:script|style|textarea|title)$/i;\n/** TemplateResult types */\nconst HTML_RESULT = 1;\nconst SVG_RESULT = 2;\n// TemplatePart types\n// IMPORTANT: these must match the values in PartType\nconst ATTRIBUTE_PART = 1;\nconst CHILD_PART = 2;\nconst PROPERTY_PART = 3;\nconst BOOLEAN_ATTRIBUTE_PART = 4;\nconst EVENT_PART = 5;\nconst ELEMENT_PART = 6;\nconst COMMENT_PART = 7;\n/**\n * Generates a template literal tag function that returns a TemplateResult with\n * the given result type.\n */\nconst tag = type => (strings, ...values) => {\n  // Warn against templates octal escape sequences\n  // We do this here rather than in render so that the warning is closer to the\n  // template definition.\n  if (DEV_MODE && strings.some(s => s === undefined)) {\n    console.warn('Some template strings are undefined.\\n' + 'This is probably caused by illegal octal escape sequences.');\n  }\n  return {\n    // This property needs to remain unminified.\n    ['_$litType$']: type,\n    strings,\n    values\n  };\n};\n/**\n * Interprets a template literal as an HTML template that can efficiently\n * render to and update a container.\n *\n * ```ts\n * const header = (title: string) => html`<h1>${title}</h1>`;\n * ```\n *\n * The `html` tag returns a description of the DOM to render as a value. It is\n * lazy, meaning no work is done until the template is rendered. When rendering,\n * if a template comes from the same expression as a previously rendered result,\n * it's efficiently updated instead of replaced.\n */\nexport const html = tag(HTML_RESULT);\n/**\n * Interprets a template literal as an SVG fragment that can efficiently\n * render to and update a container.\n *\n * ```ts\n * const rect = svg`<rect width=\"10\" height=\"10\"></rect>`;\n *\n * const myImage = html`\n *   <svg viewBox=\"0 0 10 10\" xmlns=\"http://www.w3.org/2000/svg\">\n *     ${rect}\n *   </svg>`;\n * ```\n *\n * The `svg` *tag function* should only be used for SVG fragments, or elements\n * that would be contained **inside** an `<svg>` HTML element. A common error is\n * placing an `<svg>` *element* in a template tagged with the `svg` tag\n * function. The `<svg>` element is an HTML element and should be used within a\n * template tagged with the {@linkcode html} tag function.\n *\n * In LitElement usage, it's invalid to return an SVG fragment from the\n * `render()` method, as the SVG fragment will be contained within the element's\n * shadow root and thus cannot be used within an `<svg>` HTML element.\n */\nexport const svg = tag(SVG_RESULT);\n/**\n * A sentinel value that signals that a value was handled by a directive and\n * should not be written to the DOM.\n */\nexport const noChange = Symbol.for('lit-noChange');\n/**\n * A sentinel value that signals a ChildPart to fully clear its content.\n *\n * ```ts\n * const button = html`${\n *  user.isAdmin\n *    ? html`<button>DELETE</button>`\n *    : nothing\n * }`;\n * ```\n *\n * Prefer using `nothing` over other falsy values as it provides a consistent\n * behavior between various expression binding contexts.\n *\n * In child expressions, `undefined`, `null`, `''`, and `nothing` all behave the\n * same and render no nodes. In attribute expressions, `nothing` _removes_ the\n * attribute, while `undefined` and `null` will render an empty string. In\n * property expressions `nothing` becomes `undefined`.\n */\nexport const nothing = Symbol.for('lit-nothing');\n/**\n * The cache of prepared templates, keyed by the tagged TemplateStringsArray\n * and _not_ accounting for the specific template tag used. This means that\n * template tags cannot be dynamic - the must statically be one of html, svg,\n * or attr. This restriction simplifies the cache lookup, which is on the hot\n * path for rendering.\n */\nconst templateCache = new WeakMap();\nconst walker = d.createTreeWalker(d, 129 /* NodeFilter.SHOW_{ELEMENT|COMMENT} */, null, false);\nlet sanitizerFactoryInternal = noopSanitizer;\nfunction trustFromTemplateString(tsa, stringFromTSA) {\n  // A security check to prevent spoofing of Lit template results.\n  // In the future, we may be able to replace this with Array.isTemplateObject,\n  // though we might need to make that check inside of the html and svg\n  // functions, because precompiled templates don't come in as\n  // TemplateStringArray objects.\n  if (!Array.isArray(tsa) || !tsa.hasOwnProperty('raw')) {\n    let message = 'invalid template strings array';\n    if (DEV_MODE) {\n      message = `\n          Internal Error: expected template strings to be an array\n          with a 'raw' field. Faking a template strings array by\n          calling html or svg like an ordinary function is effectively\n          the same as calling unsafeHtml and can lead to major security\n          issues, e.g. opening your code up to XSS attacks.\n          If you're using the html or svg tagged template functions normally\n          and still seeing this error, please file a bug at\n          https://github.com/lit/lit/issues/new?template=bug_report.md\n          and include information about your build tooling, if any.\n        `.trim().replace(/\\n */g, '\\n');\n    }\n    throw new Error(message);\n  }\n  return policy !== undefined ? policy.createHTML(stringFromTSA) : stringFromTSA;\n}\n/**\n * Returns an HTML string for the given TemplateStringsArray and result type\n * (HTML or SVG), along with the case-sensitive bound attribute names in\n * template order. The HTML contains comment markers denoting the `ChildPart`s\n * and suffixes on bound attributes denoting the `AttributeParts`.\n *\n * @param strings template strings array\n * @param type HTML or SVG\n * @return Array containing `[html, attrNames]` (array returned for terseness,\n *     to avoid object fields since this code is shared with non-minified SSR\n *     code)\n */\nconst getTemplateHtml = (strings, type) => {\n  // Insert makers into the template HTML to represent the position of\n  // bindings. The following code scans the template strings to determine the\n  // syntactic position of the bindings. They can be in text position, where\n  // we insert an HTML comment, attribute value position, where we insert a\n  // sentinel string and re-write the attribute name, or inside a tag where\n  // we insert the sentinel string.\n  const l = strings.length - 1;\n  // Stores the case-sensitive bound attribute names in the order of their\n  // parts. ElementParts are also reflected in this array as undefined\n  // rather than a string, to disambiguate from attribute bindings.\n  const attrNames = [];\n  let html = type === SVG_RESULT ? '<svg>' : '';\n  // When we're inside a raw text tag (not it's text content), the regex\n  // will still be tagRegex so we can find attributes, but will switch to\n  // this regex when the tag ends.\n  let rawTextEndRegex;\n  // The current parsing state, represented as a reference to one of the\n  // regexes\n  let regex = textEndRegex;\n  for (let i = 0; i < l; i++) {\n    const s = strings[i];\n    // The index of the end of the last attribute name. When this is\n    // positive at end of a string, it means we're in an attribute value\n    // position and need to rewrite the attribute name.\n    // We also use a special value of -2 to indicate that we encountered\n    // the end of a string in attribute name position.\n    let attrNameEndIndex = -1;\n    let attrName;\n    let lastIndex = 0;\n    let match;\n    // The conditions in this loop handle the current parse state, and the\n    // assignments to the `regex` variable are the state transitions.\n    while (lastIndex < s.length) {\n      // Make sure we start searching from where we previously left off\n      regex.lastIndex = lastIndex;\n      match = regex.exec(s);\n      if (match === null) {\n        break;\n      }\n      lastIndex = regex.lastIndex;\n      if (regex === textEndRegex) {\n        if (match[COMMENT_START] === '!--') {\n          regex = commentEndRegex;\n        } else if (match[COMMENT_START] !== undefined) {\n          // We started a weird comment, like </{\n          regex = comment2EndRegex;\n        } else if (match[TAG_NAME] !== undefined) {\n          if (rawTextElement.test(match[TAG_NAME])) {\n            // Record if we encounter a raw-text element. We'll switch to\n            // this regex at the end of the tag.\n            rawTextEndRegex = new RegExp(`</${match[TAG_NAME]}`, 'g');\n          }\n          regex = tagEndRegex;\n        } else if (match[DYNAMIC_TAG_NAME] !== undefined) {\n          if (DEV_MODE) {\n            throw new Error('Bindings in tag names are not supported. Please use static templates instead. ' + 'See https://lit.dev/docs/templates/expressions/#static-expressions');\n          }\n          regex = tagEndRegex;\n        }\n      } else if (regex === tagEndRegex) {\n        if (match[ENTIRE_MATCH] === '>') {\n          // End of a tag. If we had started a raw-text element, use that\n          // regex\n          regex = rawTextEndRegex !== null && rawTextEndRegex !== void 0 ? rawTextEndRegex : textEndRegex;\n          // We may be ending an unquoted attribute value, so make sure we\n          // clear any pending attrNameEndIndex\n          attrNameEndIndex = -1;\n        } else if (match[ATTRIBUTE_NAME] === undefined) {\n          // Attribute name position\n          attrNameEndIndex = -2;\n        } else {\n          attrNameEndIndex = regex.lastIndex - match[SPACES_AND_EQUALS].length;\n          attrName = match[ATTRIBUTE_NAME];\n          regex = match[QUOTE_CHAR] === undefined ? tagEndRegex : match[QUOTE_CHAR] === '\"' ? doubleQuoteAttrEndRegex : singleQuoteAttrEndRegex;\n        }\n      } else if (regex === doubleQuoteAttrEndRegex || regex === singleQuoteAttrEndRegex) {\n        regex = tagEndRegex;\n      } else if (regex === commentEndRegex || regex === comment2EndRegex) {\n        regex = textEndRegex;\n      } else {\n        // Not one of the five state regexes, so it must be the dynamically\n        // created raw text regex and we're at the close of that element.\n        regex = tagEndRegex;\n        rawTextEndRegex = undefined;\n      }\n    }\n    if (DEV_MODE) {\n      // If we have a attrNameEndIndex, which indicates that we should\n      // rewrite the attribute name, assert that we're in a valid attribute\n      // position - either in a tag, or a quoted attribute value.\n      console.assert(attrNameEndIndex === -1 || regex === tagEndRegex || regex === singleQuoteAttrEndRegex || regex === doubleQuoteAttrEndRegex, 'unexpected parse state B');\n    }\n    // We have four cases:\n    //  1. We're in text position, and not in a raw text element\n    //     (regex === textEndRegex): insert a comment marker.\n    //  2. We have a non-negative attrNameEndIndex which means we need to\n    //     rewrite the attribute name to add a bound attribute suffix.\n    //  3. We're at the non-first binding in a multi-binding attribute, use a\n    //     plain marker.\n    //  4. We're somewhere else inside the tag. If we're in attribute name\n    //     position (attrNameEndIndex === -2), add a sequential suffix to\n    //     generate a unique attribute name.\n    // Detect a binding next to self-closing tag end and insert a space to\n    // separate the marker from the tag end:\n    const end = regex === tagEndRegex && strings[i + 1].startsWith('/>') ? ' ' : '';\n    html += regex === textEndRegex ? s + nodeMarker : attrNameEndIndex >= 0 ? (attrNames.push(attrName), s.slice(0, attrNameEndIndex) + boundAttributeSuffix + s.slice(attrNameEndIndex)) + marker + end : s + marker + (attrNameEndIndex === -2 ? (attrNames.push(undefined), i) : end);\n  }\n  const htmlResult = html + (strings[l] || '<?>') + (type === SVG_RESULT ? '</svg>' : '');\n  // Returned as an array for terseness\n  return [trustFromTemplateString(strings, htmlResult), attrNames];\n};\nclass Template {\n  constructor(\n  // This property needs to remain unminified.\n  {\n    strings,\n    ['_$litType$']: type\n  }, options) {\n    this.parts = [];\n    let node;\n    let nodeIndex = 0;\n    let attrNameIndex = 0;\n    const partCount = strings.length - 1;\n    const parts = this.parts;\n    // Create template element\n    const [html, attrNames] = getTemplateHtml(strings, type);\n    this.el = Template.createElement(html, options);\n    walker.currentNode = this.el.content;\n    // Reparent SVG nodes into template root\n    if (type === SVG_RESULT) {\n      const content = this.el.content;\n      const svgElement = content.firstChild;\n      svgElement.remove();\n      content.append(...svgElement.childNodes);\n    }\n    // Walk the template to find binding markers and create TemplateParts\n    while ((node = walker.nextNode()) !== null && parts.length < partCount) {\n      if (node.nodeType === 1) {\n        if (DEV_MODE) {\n          const tag = node.localName;\n          // Warn if `textarea` includes an expression and throw if `template`\n          // does since these are not supported. We do this by checking\n          // innerHTML for anything that looks like a marker. This catches\n          // cases like bindings in textarea there markers turn into text nodes.\n          if (/^(?:textarea|template)$/i.test(tag) && node.innerHTML.includes(marker)) {\n            const m = `Expressions are not supported inside \\`${tag}\\` ` + `elements. See https://lit.dev/msg/expression-in-${tag} for more ` + `information.`;\n            if (tag === 'template') {\n              throw new Error(m);\n            } else issueWarning('', m);\n          }\n        }\n        // TODO (justinfagnani): for attempted dynamic tag names, we don't\n        // increment the bindingIndex, and it'll be off by 1 in the element\n        // and off by two after it.\n        if (node.hasAttributes()) {\n          // We defer removing bound attributes because on IE we might not be\n          // iterating attributes in their template order, and would sometimes\n          // remove an attribute that we still need to create a part for.\n          const attrsToRemove = [];\n          for (const name of node.getAttributeNames()) {\n            // `name` is the name of the attribute we're iterating over, but not\n            // _necessarily_ the name of the attribute we will create a part\n            // for. They can be different in browsers that don't iterate on\n            // attributes in source order. In that case the attrNames array\n            // contains the attribute name we'll process next. We only need the\n            // attribute name here to know if we should process a bound attribute\n            // on this element.\n            if (name.endsWith(boundAttributeSuffix) || name.startsWith(marker)) {\n              const realName = attrNames[attrNameIndex++];\n              attrsToRemove.push(name);\n              if (realName !== undefined) {\n                // Lowercase for case-sensitive SVG attributes like viewBox\n                const value = node.getAttribute(realName.toLowerCase() + boundAttributeSuffix);\n                const statics = value.split(marker);\n                const m = /([.?@])?(.*)/.exec(realName);\n                parts.push({\n                  type: ATTRIBUTE_PART,\n                  index: nodeIndex,\n                  name: m[2],\n                  strings: statics,\n                  ctor: m[1] === '.' ? PropertyPart : m[1] === '?' ? BooleanAttributePart : m[1] === '@' ? EventPart : AttributePart\n                });\n              } else {\n                parts.push({\n                  type: ELEMENT_PART,\n                  index: nodeIndex\n                });\n              }\n            }\n          }\n          for (const name of attrsToRemove) {\n            node.removeAttribute(name);\n          }\n        }\n        // TODO (justinfagnani): benchmark the regex against testing for each\n        // of the 3 raw text element names.\n        if (rawTextElement.test(node.tagName)) {\n          // For raw text elements we need to split the text content on\n          // markers, create a Text node for each segment, and create\n          // a TemplatePart for each marker.\n          const strings = node.textContent.split(marker);\n          const lastIndex = strings.length - 1;\n          if (lastIndex > 0) {\n            node.textContent = trustedTypes ? trustedTypes.emptyScript : '';\n            // Generate a new text node for each literal section\n            // These nodes are also used as the markers for node parts\n            // We can't use empty text nodes as markers because they're\n            // normalized when cloning in IE (could simplify when\n            // IE is no longer supported)\n            for (let i = 0; i < lastIndex; i++) {\n              node.append(strings[i], createMarker());\n              // Walk past the marker node we just added\n              walker.nextNode();\n              parts.push({\n                type: CHILD_PART,\n                index: ++nodeIndex\n              });\n            }\n            // Note because this marker is added after the walker's current\n            // node, it will be walked to in the outer loop (and ignored), so\n            // we don't need to adjust nodeIndex here\n            node.append(strings[lastIndex], createMarker());\n          }\n        }\n      } else if (node.nodeType === 8) {\n        const data = node.data;\n        if (data === markerMatch) {\n          parts.push({\n            type: CHILD_PART,\n            index: nodeIndex\n          });\n        } else {\n          let i = -1;\n          while ((i = node.data.indexOf(marker, i + 1)) !== -1) {\n            // Comment node has a binding marker inside, make an inactive part\n            // The binding won't work, but subsequent bindings will\n            parts.push({\n              type: COMMENT_PART,\n              index: nodeIndex\n            });\n            // Move to the end of the match\n            i += marker.length - 1;\n          }\n        }\n      }\n      nodeIndex++;\n    }\n    // We could set walker.currentNode to another node here to prevent a memory\n    // leak, but every time we prepare a template, we immediately render it\n    // and re-use the walker in new TemplateInstance._clone().\n    debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n      kind: 'template prep',\n      template: this,\n      clonableTemplate: this.el,\n      parts: this.parts,\n      strings\n    });\n  }\n  // Overridden via `litHtmlPolyfillSupport` to provide platform support.\n  /** @nocollapse */\n  static createElement(html, _options) {\n    const el = d.createElement('template');\n    el.innerHTML = html;\n    return el;\n  }\n}\nfunction resolveDirective(part, value, parent = part, attributeIndex) {\n  var _a, _b, _c;\n  var _d;\n  // Bail early if the value is explicitly noChange. Note, this means any\n  // nested directive is still attached and is not run.\n  if (value === noChange) {\n    return value;\n  }\n  let currentDirective = attributeIndex !== undefined ? (_a = parent.__directives) === null || _a === void 0 ? void 0 : _a[attributeIndex] : parent.__directive;\n  const nextDirectiveConstructor = isPrimitive(value) ? undefined :\n  // This property needs to remain unminified.\n  value['_$litDirective$'];\n  if ((currentDirective === null || currentDirective === void 0 ? void 0 : currentDirective.constructor) !== nextDirectiveConstructor) {\n    // This property needs to remain unminified.\n    (_b = currentDirective === null || currentDirective === void 0 ? void 0 : currentDirective['_$notifyDirectiveConnectionChanged']) === null || _b === void 0 ? void 0 : _b.call(currentDirective, false);\n    if (nextDirectiveConstructor === undefined) {\n      currentDirective = undefined;\n    } else {\n      currentDirective = new nextDirectiveConstructor(part);\n      currentDirective._$initialize(part, parent, attributeIndex);\n    }\n    if (attributeIndex !== undefined) {\n      ((_c = (_d = parent).__directives) !== null && _c !== void 0 ? _c : _d.__directives = [])[attributeIndex] = currentDirective;\n    } else {\n      parent.__directive = currentDirective;\n    }\n  }\n  if (currentDirective !== undefined) {\n    value = resolveDirective(part, currentDirective._$resolve(part, value.values), currentDirective, attributeIndex);\n  }\n  return value;\n}\n/**\n * An updateable instance of a Template. Holds references to the Parts used to\n * update the template instance.\n */\nclass TemplateInstance {\n  constructor(template, parent) {\n    this._$parts = [];\n    /** @internal */\n    this._$disconnectableChildren = undefined;\n    this._$template = template;\n    this._$parent = parent;\n  }\n  // Called by ChildPart parentNode getter\n  get parentNode() {\n    return this._$parent.parentNode;\n  }\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n  // This method is separate from the constructor because we need to return a\n  // DocumentFragment and we don't want to hold onto it with an instance field.\n  _clone(options) {\n    var _a;\n    const {\n      el: {\n        content\n      },\n      parts: parts\n    } = this._$template;\n    const fragment = ((_a = options === null || options === void 0 ? void 0 : options.creationScope) !== null && _a !== void 0 ? _a : d).importNode(content, true);\n    walker.currentNode = fragment;\n    let node = walker.nextNode();\n    let nodeIndex = 0;\n    let partIndex = 0;\n    let templatePart = parts[0];\n    while (templatePart !== undefined) {\n      if (nodeIndex === templatePart.index) {\n        let part;\n        if (templatePart.type === CHILD_PART) {\n          part = new ChildPart(node, node.nextSibling, this, options);\n        } else if (templatePart.type === ATTRIBUTE_PART) {\n          part = new templatePart.ctor(node, templatePart.name, templatePart.strings, this, options);\n        } else if (templatePart.type === ELEMENT_PART) {\n          part = new ElementPart(node, this, options);\n        }\n        this._$parts.push(part);\n        templatePart = parts[++partIndex];\n      }\n      if (nodeIndex !== (templatePart === null || templatePart === void 0 ? void 0 : templatePart.index)) {\n        node = walker.nextNode();\n        nodeIndex++;\n      }\n    }\n    // We need to set the currentNode away from the cloned tree so that we\n    // don't hold onto the tree even if the tree is detached and should be\n    // freed.\n    walker.currentNode = d;\n    return fragment;\n  }\n  _update(values) {\n    let i = 0;\n    for (const part of this._$parts) {\n      if (part !== undefined) {\n        debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n          kind: 'set part',\n          part,\n          value: values[i],\n          valueIndex: i,\n          values,\n          templateInstance: this\n        });\n        if (part.strings !== undefined) {\n          part._$setValue(values, part, i);\n          // The number of values the part consumes is part.strings.length - 1\n          // since values are in between template spans. We increment i by 1\n          // later in the loop, so increment it by part.strings.length - 2 here\n          i += part.strings.length - 2;\n        } else {\n          part._$setValue(values[i]);\n        }\n      }\n      i++;\n    }\n  }\n}\nclass ChildPart {\n  constructor(startNode, endNode, parent, options) {\n    var _a;\n    this.type = CHILD_PART;\n    this._$committedValue = nothing;\n    // The following fields will be patched onto ChildParts when required by\n    // AsyncDirective\n    /** @internal */\n    this._$disconnectableChildren = undefined;\n    this._$startNode = startNode;\n    this._$endNode = endNode;\n    this._$parent = parent;\n    this.options = options;\n    // Note __isConnected is only ever accessed on RootParts (i.e. when there is\n    // no _$parent); the value on a non-root-part is \"don't care\", but checking\n    // for parent would be more code\n    this.__isConnected = (_a = options === null || options === void 0 ? void 0 : options.isConnected) !== null && _a !== void 0 ? _a : true;\n    if (ENABLE_EXTRA_SECURITY_HOOKS) {\n      // Explicitly initialize for consistent class shape.\n      this._textSanitizer = undefined;\n    }\n  }\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    var _a, _b;\n    // ChildParts that are not at the root should always be created with a\n    // parent; only RootChildNode's won't, so they return the local isConnected\n    // state\n    return (_b = (_a = this._$parent) === null || _a === void 0 ? void 0 : _a._$isConnected) !== null && _b !== void 0 ? _b : this.__isConnected;\n  }\n  /**\n   * The parent node into which the part renders its content.\n   *\n   * A ChildPart's content consists of a range of adjacent child nodes of\n   * `.parentNode`, possibly bordered by 'marker nodes' (`.startNode` and\n   * `.endNode`).\n   *\n   * - If both `.startNode` and `.endNode` are non-null, then the part's content\n   * consists of all siblings between `.startNode` and `.endNode`, exclusively.\n   *\n   * - If `.startNode` is non-null but `.endNode` is null, then the part's\n   * content consists of all siblings following `.startNode`, up to and\n   * including the last child of `.parentNode`. If `.endNode` is non-null, then\n   * `.startNode` will always be non-null.\n   *\n   * - If both `.endNode` and `.startNode` are null, then the part's content\n   * consists of all child nodes of `.parentNode`.\n   */\n  get parentNode() {\n    let parentNode = wrap(this._$startNode).parentNode;\n    const parent = this._$parent;\n    if (parent !== undefined && (parentNode === null || parentNode === void 0 ? void 0 : parentNode.nodeType) === 11 /* Node.DOCUMENT_FRAGMENT */) {\n      // If the parentNode is a DocumentFragment, it may be because the DOM is\n      // still in the cloned fragment during initial render; if so, get the real\n      // parentNode the part will be committed into by asking the parent.\n      parentNode = parent.parentNode;\n    }\n    return parentNode;\n  }\n  /**\n   * The part's leading marker node, if any. See `.parentNode` for more\n   * information.\n   */\n  get startNode() {\n    return this._$startNode;\n  }\n  /**\n   * The part's trailing marker node, if any. See `.parentNode` for more\n   * information.\n   */\n  get endNode() {\n    return this._$endNode;\n  }\n  _$setValue(value, directiveParent = this) {\n    var _a;\n    if (DEV_MODE && this.parentNode === null) {\n      throw new Error(`This \\`ChildPart\\` has no \\`parentNode\\` and therefore cannot accept a value. This likely means the element containing the part was manipulated in an unsupported way outside of Lit's control such that the part's marker nodes were ejected from DOM. For example, setting the element's \\`innerHTML\\` or \\`textContent\\` can do this.`);\n    }\n    value = resolveDirective(this, value, directiveParent);\n    if (isPrimitive(value)) {\n      // Non-rendering child values. It's important that these do not render\n      // empty text nodes to avoid issues with preventing default <slot>\n      // fallback content.\n      if (value === nothing || value == null || value === '') {\n        if (this._$committedValue !== nothing) {\n          debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n            kind: 'commit nothing to child',\n            start: this._$startNode,\n            end: this._$endNode,\n            parent: this._$parent,\n            options: this.options\n          });\n          this._$clear();\n        }\n        this._$committedValue = nothing;\n      } else if (value !== this._$committedValue && value !== noChange) {\n        this._commitText(value);\n      }\n      // This property needs to remain unminified.\n    } else if (value['_$litType$'] !== undefined) {\n      this._commitTemplateResult(value);\n    } else if (value.nodeType !== undefined) {\n      if (DEV_MODE && ((_a = this.options) === null || _a === void 0 ? void 0 : _a.host) === value) {\n        this._commitText(`[probable mistake: rendered a template's host in itself ` + `(commonly caused by writing \\${this} in a template]`);\n        console.warn(`Attempted to render the template host`, value, `inside itself. This is almost always a mistake, and in dev mode `, `we render some warning text. In production however, we'll `, `render it, which will usually result in an error, and sometimes `, `in the element disappearing from the DOM.`);\n        return;\n      }\n      this._commitNode(value);\n    } else if (isIterable(value)) {\n      this._commitIterable(value);\n    } else {\n      // Fallback, will render the string representation\n      this._commitText(value);\n    }\n  }\n  _insert(node) {\n    return wrap(wrap(this._$startNode).parentNode).insertBefore(node, this._$endNode);\n  }\n  _commitNode(value) {\n    var _a;\n    if (this._$committedValue !== value) {\n      this._$clear();\n      if (ENABLE_EXTRA_SECURITY_HOOKS && sanitizerFactoryInternal !== noopSanitizer) {\n        const parentNodeName = (_a = this._$startNode.parentNode) === null || _a === void 0 ? void 0 : _a.nodeName;\n        if (parentNodeName === 'STYLE' || parentNodeName === 'SCRIPT') {\n          let message = 'Forbidden';\n          if (DEV_MODE) {\n            if (parentNodeName === 'STYLE') {\n              message = `Lit does not support binding inside style nodes. ` + `This is a security risk, as style injection attacks can ` + `exfiltrate data and spoof UIs. ` + `Consider instead using css\\`...\\` literals ` + `to compose styles, and make do dynamic styling with ` + `css custom properties, ::parts, <slot>s, ` + `and by mutating the DOM rather than stylesheets.`;\n            } else {\n              message = `Lit does not support binding inside script nodes. ` + `This is a security risk, as it could allow arbitrary ` + `code execution.`;\n            }\n          }\n          throw new Error(message);\n        }\n      }\n      debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n        kind: 'commit node',\n        start: this._$startNode,\n        parent: this._$parent,\n        value: value,\n        options: this.options\n      });\n      this._$committedValue = this._insert(value);\n    }\n  }\n  _commitText(value) {\n    // If the committed value is a primitive it means we called _commitText on\n    // the previous render, and we know that this._$startNode.nextSibling is a\n    // Text node. We can now just replace the text content (.data) of the node.\n    if (this._$committedValue !== nothing && isPrimitive(this._$committedValue)) {\n      const node = wrap(this._$startNode).nextSibling;\n      if (ENABLE_EXTRA_SECURITY_HOOKS) {\n        if (this._textSanitizer === undefined) {\n          this._textSanitizer = createSanitizer(node, 'data', 'property');\n        }\n        value = this._textSanitizer(value);\n      }\n      debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n        kind: 'commit text',\n        node,\n        value,\n        options: this.options\n      });\n      node.data = value;\n    } else {\n      if (ENABLE_EXTRA_SECURITY_HOOKS) {\n        const textNode = d.createTextNode('');\n        this._commitNode(textNode);\n        // When setting text content, for security purposes it matters a lot\n        // what the parent is. For example, <style> and <script> need to be\n        // handled with care, while <span> does not. So first we need to put a\n        // text node into the document, then we can sanitize its content.\n        if (this._textSanitizer === undefined) {\n          this._textSanitizer = createSanitizer(textNode, 'data', 'property');\n        }\n        value = this._textSanitizer(value);\n        debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n          kind: 'commit text',\n          node: textNode,\n          value,\n          options: this.options\n        });\n        textNode.data = value;\n      } else {\n        this._commitNode(d.createTextNode(value));\n        debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n          kind: 'commit text',\n          node: wrap(this._$startNode).nextSibling,\n          value,\n          options: this.options\n        });\n      }\n    }\n    this._$committedValue = value;\n  }\n  _commitTemplateResult(result) {\n    var _a;\n    // This property needs to remain unminified.\n    const {\n      values,\n      ['_$litType$']: type\n    } = result;\n    // If $litType$ is a number, result is a plain TemplateResult and we get\n    // the template from the template cache. If not, result is a\n    // CompiledTemplateResult and _$litType$ is a CompiledTemplate and we need\n    // to create the <template> element the first time we see it.\n    const template = typeof type === 'number' ? this._$getTemplate(result) : (type.el === undefined && (type.el = Template.createElement(trustFromTemplateString(type.h, type.h[0]), this.options)), type);\n    if (((_a = this._$committedValue) === null || _a === void 0 ? void 0 : _a._$template) === template) {\n      debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n        kind: 'template updating',\n        template,\n        instance: this._$committedValue,\n        parts: this._$committedValue._$parts,\n        options: this.options,\n        values\n      });\n      this._$committedValue._update(values);\n    } else {\n      const instance = new TemplateInstance(template, this);\n      const fragment = instance._clone(this.options);\n      debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n        kind: 'template instantiated',\n        template,\n        instance,\n        parts: instance._$parts,\n        options: this.options,\n        fragment,\n        values\n      });\n      instance._update(values);\n      debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n        kind: 'template instantiated and updated',\n        template,\n        instance,\n        parts: instance._$parts,\n        options: this.options,\n        fragment,\n        values\n      });\n      this._commitNode(fragment);\n      this._$committedValue = instance;\n    }\n  }\n  // Overridden via `litHtmlPolyfillSupport` to provide platform support.\n  /** @internal */\n  _$getTemplate(result) {\n    let template = templateCache.get(result.strings);\n    if (template === undefined) {\n      templateCache.set(result.strings, template = new Template(result));\n    }\n    return template;\n  }\n  _commitIterable(value) {\n    // For an Iterable, we create a new InstancePart per item, then set its\n    // value to the item. This is a little bit of overhead for every item in\n    // an Iterable, but it lets us recurse easily and efficiently update Arrays\n    // of TemplateResults that will be commonly returned from expressions like:\n    // array.map((i) => html`${i}`), by reusing existing TemplateInstances.\n    // If value is an array, then the previous render was of an\n    // iterable and value will contain the ChildParts from the previous\n    // render. If value is not an array, clear this part and make a new\n    // array for ChildParts.\n    if (!isArray(this._$committedValue)) {\n      this._$committedValue = [];\n      this._$clear();\n    }\n    // Lets us keep track of how many items we stamped so we can clear leftover\n    // items from a previous render\n    const itemParts = this._$committedValue;\n    let partIndex = 0;\n    let itemPart;\n    for (const item of value) {\n      if (partIndex === itemParts.length) {\n        // If no existing part, create a new one\n        // TODO (justinfagnani): test perf impact of always creating two parts\n        // instead of sharing parts between nodes\n        // https://github.com/lit/lit/issues/1266\n        itemParts.push(itemPart = new ChildPart(this._insert(createMarker()), this._insert(createMarker()), this, this.options));\n      } else {\n        // Reuse an existing part\n        itemPart = itemParts[partIndex];\n      }\n      itemPart._$setValue(item);\n      partIndex++;\n    }\n    if (partIndex < itemParts.length) {\n      // itemParts always have end nodes\n      this._$clear(itemPart && wrap(itemPart._$endNode).nextSibling, partIndex);\n      // Truncate the parts array so _value reflects the current state\n      itemParts.length = partIndex;\n    }\n  }\n  /**\n   * Removes the nodes contained within this Part from the DOM.\n   *\n   * @param start Start node to clear from, for clearing a subset of the part's\n   *     DOM (used when truncating iterables)\n   * @param from  When `start` is specified, the index within the iterable from\n   *     which ChildParts are being removed, used for disconnecting directives in\n   *     those Parts.\n   *\n   * @internal\n   */\n  _$clear(start = wrap(this._$startNode).nextSibling, from) {\n    var _a;\n    (_a = this._$notifyConnectionChanged) === null || _a === void 0 ? void 0 : _a.call(this, false, true, from);\n    while (start && start !== this._$endNode) {\n      const n = wrap(start).nextSibling;\n      wrap(start).remove();\n      start = n;\n    }\n  }\n  /**\n   * Implementation of RootPart's `isConnected`. Note that this metod\n   * should only be called on `RootPart`s (the `ChildPart` returned from a\n   * top-level `render()` call). It has no effect on non-root ChildParts.\n   * @param isConnected Whether to set\n   * @internal\n   */\n  setConnected(isConnected) {\n    var _a;\n    if (this._$parent === undefined) {\n      this.__isConnected = isConnected;\n      (_a = this._$notifyConnectionChanged) === null || _a === void 0 ? void 0 : _a.call(this, isConnected);\n    } else if (DEV_MODE) {\n      throw new Error('part.setConnected() may only be called on a ' + 'RootPart returned from render().');\n    }\n  }\n}\nclass AttributePart {\n  constructor(element, name, strings, parent, options) {\n    this.type = ATTRIBUTE_PART;\n    /** @internal */\n    this._$committedValue = nothing;\n    /** @internal */\n    this._$disconnectableChildren = undefined;\n    this.element = element;\n    this.name = name;\n    this._$parent = parent;\n    this.options = options;\n    if (strings.length > 2 || strings[0] !== '' || strings[1] !== '') {\n      this._$committedValue = new Array(strings.length - 1).fill(new String());\n      this.strings = strings;\n    } else {\n      this._$committedValue = nothing;\n    }\n    if (ENABLE_EXTRA_SECURITY_HOOKS) {\n      this._sanitizer = undefined;\n    }\n  }\n  get tagName() {\n    return this.element.tagName;\n  }\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n  /**\n   * Sets the value of this part by resolving the value from possibly multiple\n   * values and static strings and committing it to the DOM.\n   * If this part is single-valued, `this._strings` will be undefined, and the\n   * method will be called with a single value argument. If this part is\n   * multi-value, `this._strings` will be defined, and the method is called\n   * with the value array of the part's owning TemplateInstance, and an offset\n   * into the value array from which the values should be read.\n   * This method is overloaded this way to eliminate short-lived array slices\n   * of the template instance values, and allow a fast-path for single-valued\n   * parts.\n   *\n   * @param value The part value, or an array of values for multi-valued parts\n   * @param valueIndex the index to start reading values from. `undefined` for\n   *   single-valued parts\n   * @param noCommit causes the part to not commit its value to the DOM. Used\n   *   in hydration to prime attribute parts with their first-rendered value,\n   *   but not set the attribute, and in SSR to no-op the DOM operation and\n   *   capture the value for serialization.\n   *\n   * @internal\n   */\n  _$setValue(value, directiveParent = this, valueIndex, noCommit) {\n    const strings = this.strings;\n    // Whether any of the values has changed, for dirty-checking\n    let change = false;\n    if (strings === undefined) {\n      // Single-value binding case\n      value = resolveDirective(this, value, directiveParent, 0);\n      change = !isPrimitive(value) || value !== this._$committedValue && value !== noChange;\n      if (change) {\n        this._$committedValue = value;\n      }\n    } else {\n      // Interpolation case\n      const values = value;\n      value = strings[0];\n      let i, v;\n      for (i = 0; i < strings.length - 1; i++) {\n        v = resolveDirective(this, values[valueIndex + i], directiveParent, i);\n        if (v === noChange) {\n          // If the user-provided value is `noChange`, use the previous value\n          v = this._$committedValue[i];\n        }\n        change || (change = !isPrimitive(v) || v !== this._$committedValue[i]);\n        if (v === nothing) {\n          value = nothing;\n        } else if (value !== nothing) {\n          value += (v !== null && v !== void 0 ? v : '') + strings[i + 1];\n        }\n        // We always record each value, even if one is `nothing`, for future\n        // change detection.\n        this._$committedValue[i] = v;\n      }\n    }\n    if (change && !noCommit) {\n      this._commitValue(value);\n    }\n  }\n  /** @internal */\n  _commitValue(value) {\n    if (value === nothing) {\n      wrap(this.element).removeAttribute(this.name);\n    } else {\n      if (ENABLE_EXTRA_SECURITY_HOOKS) {\n        if (this._sanitizer === undefined) {\n          this._sanitizer = sanitizerFactoryInternal(this.element, this.name, 'attribute');\n        }\n        value = this._sanitizer(value !== null && value !== void 0 ? value : '');\n      }\n      debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n        kind: 'commit attribute',\n        element: this.element,\n        name: this.name,\n        value,\n        options: this.options\n      });\n      wrap(this.element).setAttribute(this.name, value !== null && value !== void 0 ? value : '');\n    }\n  }\n}\nclass PropertyPart extends AttributePart {\n  constructor() {\n    super(...arguments);\n    this.type = PROPERTY_PART;\n  }\n  /** @internal */\n  _commitValue(value) {\n    if (ENABLE_EXTRA_SECURITY_HOOKS) {\n      if (this._sanitizer === undefined) {\n        this._sanitizer = sanitizerFactoryInternal(this.element, this.name, 'property');\n      }\n      value = this._sanitizer(value);\n    }\n    debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n      kind: 'commit property',\n      element: this.element,\n      name: this.name,\n      value,\n      options: this.options\n    });\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    this.element[this.name] = value === nothing ? undefined : value;\n  }\n}\n// Temporary workaround for https://crbug.com/993268\n// Currently, any attribute starting with \"on\" is considered to be a\n// TrustedScript source. Such boolean attributes must be set to the equivalent\n// trusted emptyScript value.\nconst emptyStringForBooleanAttribute = trustedTypes ? trustedTypes.emptyScript : '';\nclass BooleanAttributePart extends AttributePart {\n  constructor() {\n    super(...arguments);\n    this.type = BOOLEAN_ATTRIBUTE_PART;\n  }\n  /** @internal */\n  _commitValue(value) {\n    debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n      kind: 'commit boolean attribute',\n      element: this.element,\n      name: this.name,\n      value: !!(value && value !== nothing),\n      options: this.options\n    });\n    if (value && value !== nothing) {\n      wrap(this.element).setAttribute(this.name, emptyStringForBooleanAttribute);\n    } else {\n      wrap(this.element).removeAttribute(this.name);\n    }\n  }\n}\nclass EventPart extends AttributePart {\n  constructor(element, name, strings, parent, options) {\n    super(element, name, strings, parent, options);\n    this.type = EVENT_PART;\n    if (DEV_MODE && this.strings !== undefined) {\n      throw new Error(`A \\`<${element.localName}>\\` has a \\`@${name}=...\\` listener with ` + 'invalid content. Event listeners in templates must have exactly ' + 'one expression and no surrounding text.');\n    }\n  }\n  // EventPart does not use the base _$setValue/_resolveValue implementation\n  // since the dirty checking is more complex\n  /** @internal */\n  _$setValue(newListener, directiveParent = this) {\n    var _a;\n    newListener = (_a = resolveDirective(this, newListener, directiveParent, 0)) !== null && _a !== void 0 ? _a : nothing;\n    if (newListener === noChange) {\n      return;\n    }\n    const oldListener = this._$committedValue;\n    // If the new value is nothing or any options change we have to remove the\n    // part as a listener.\n    const shouldRemoveListener = newListener === nothing && oldListener !== nothing || newListener.capture !== oldListener.capture || newListener.once !== oldListener.once || newListener.passive !== oldListener.passive;\n    // If the new value is not nothing and we removed the listener, we have\n    // to add the part as a listener.\n    const shouldAddListener = newListener !== nothing && (oldListener === nothing || shouldRemoveListener);\n    debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n      kind: 'commit event listener',\n      element: this.element,\n      name: this.name,\n      value: newListener,\n      options: this.options,\n      removeListener: shouldRemoveListener,\n      addListener: shouldAddListener,\n      oldListener\n    });\n    if (shouldRemoveListener) {\n      this.element.removeEventListener(this.name, this, oldListener);\n    }\n    if (shouldAddListener) {\n      // Beware: IE11 and Chrome 41 don't like using the listener as the\n      // options object. Figure out how to deal w/ this in IE11 - maybe\n      // patch addEventListener?\n      this.element.addEventListener(this.name, this, newListener);\n    }\n    this._$committedValue = newListener;\n  }\n  handleEvent(event) {\n    var _a, _b;\n    if (typeof this._$committedValue === 'function') {\n      this._$committedValue.call((_b = (_a = this.options) === null || _a === void 0 ? void 0 : _a.host) !== null && _b !== void 0 ? _b : this.element, event);\n    } else {\n      this._$committedValue.handleEvent(event);\n    }\n  }\n}\nclass ElementPart {\n  constructor(element, parent, options) {\n    this.element = element;\n    this.type = ELEMENT_PART;\n    /** @internal */\n    this._$disconnectableChildren = undefined;\n    this._$parent = parent;\n    this.options = options;\n  }\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n  _$setValue(value) {\n    debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n      kind: 'commit to element binding',\n      element: this.element,\n      value,\n      options: this.options\n    });\n    resolveDirective(this, value);\n  }\n}\n/**\n * END USERS SHOULD NOT RELY ON THIS OBJECT.\n *\n * Private exports for use by other Lit packages, not intended for use by\n * external users.\n *\n * We currently do not make a mangled rollup build of the lit-ssr code. In order\n * to keep a number of (otherwise private) top-level exports  mangled in the\n * client side code, we export a _$LH object containing those members (or\n * helper methods for accessing private fields of those members), and then\n * re-export them for use in lit-ssr. This keeps lit-ssr agnostic to whether the\n * client-side code is being used in `dev` mode or `prod` mode.\n *\n * This has a unique name, to disambiguate it from private exports in\n * lit-element, which re-exports all of lit-html.\n *\n * @private\n */\nexport const _$LH = {\n  // Used in lit-ssr\n  _boundAttributeSuffix: boundAttributeSuffix,\n  _marker: marker,\n  _markerMatch: markerMatch,\n  _HTML_RESULT: HTML_RESULT,\n  _getTemplateHtml: getTemplateHtml,\n  // Used in tests and private-ssr-support\n  _TemplateInstance: TemplateInstance,\n  _isIterable: isIterable,\n  _resolveDirective: resolveDirective,\n  _ChildPart: ChildPart,\n  _AttributePart: AttributePart,\n  _BooleanAttributePart: BooleanAttributePart,\n  _EventPart: EventPart,\n  _PropertyPart: PropertyPart,\n  _ElementPart: ElementPart\n};\n// Apply polyfills if available\nconst polyfillSupport = DEV_MODE ? global.litHtmlPolyfillSupportDevMode : global.litHtmlPolyfillSupport;\npolyfillSupport === null || polyfillSupport === void 0 ? void 0 : polyfillSupport(Template, ChildPart);\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for lit-html usage.\n((_d = global.litHtmlVersions) !== null && _d !== void 0 ? _d : global.litHtmlVersions = []).push('2.8.0');\nif (DEV_MODE && global.litHtmlVersions.length > 1) {\n  issueWarning('multiple-versions', `Multiple versions of Lit loaded. ` + `Loading multiple versions is not recommended.`);\n}\n/**\n * Renders a value, usually a lit-html TemplateResult, to the container.\n *\n * This example renders the text \"Hello, Zoe!\" inside a paragraph tag, appending\n * it to the container `document.body`.\n *\n * ```js\n * import {html, render} from 'lit';\n *\n * const name = \"Zoe\";\n * render(html`<p>Hello, ${name}!</p>`, document.body);\n * ```\n *\n * @param value Any [renderable\n *   value](https://lit.dev/docs/templates/expressions/#child-expressions),\n *   typically a {@linkcode TemplateResult} created by evaluating a template tag\n *   like {@linkcode html} or {@linkcode svg}.\n * @param container A DOM container to render to. The first render will append\n *   the rendered value to the container, and subsequent renders will\n *   efficiently update the rendered value if the same result type was\n *   previously rendered there.\n * @param options See {@linkcode RenderOptions} for options documentation.\n * @see\n * {@link https://lit.dev/docs/libraries/standalone-templates/#rendering-lit-html-templates| Rendering Lit HTML Templates}\n */\nexport const render = (value, container, options) => {\n  var _a, _b;\n  if (DEV_MODE && container == null) {\n    // Give a clearer error message than\n    //     Uncaught TypeError: Cannot read properties of null (reading\n    //     '_$litPart$')\n    // which reads like an internal Lit error.\n    throw new TypeError(`The container to render into may not be ${container}`);\n  }\n  const renderId = DEV_MODE ? debugLogRenderId++ : 0;\n  const partOwnerNode = (_a = options === null || options === void 0 ? void 0 : options.renderBefore) !== null && _a !== void 0 ? _a : container;\n  // This property needs to remain unminified.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let part = partOwnerNode['_$litPart$'];\n  debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n    kind: 'begin render',\n    id: renderId,\n    value,\n    container,\n    options,\n    part\n  });\n  if (part === undefined) {\n    const endNode = (_b = options === null || options === void 0 ? void 0 : options.renderBefore) !== null && _b !== void 0 ? _b : null;\n    // This property needs to remain unminified.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    partOwnerNode['_$litPart$'] = part = new ChildPart(container.insertBefore(createMarker(), endNode), endNode, undefined, options !== null && options !== void 0 ? options : {});\n  }\n  part._$setValue(value);\n  debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n    kind: 'end render',\n    id: renderId,\n    value,\n    container,\n    options,\n    part\n  });\n  return part;\n};\nif (ENABLE_EXTRA_SECURITY_HOOKS) {\n  render.setSanitizer = setSanitizer;\n  render.createSanitizer = createSanitizer;\n  if (DEV_MODE) {\n    render._testOnlyClearSanitizerFactoryDoNotCallOrElse = _testOnlyClearSanitizerFactoryDoNotCallOrElse;\n  }\n}", "map": {"version": 3, "names": ["_a", "_b", "_c", "_d", "DEV_MODE", "ENABLE_EXTRA_SECURITY_HOOKS", "ENABLE_SHADYDOM_NOPATCH", "NODE_MODE", "global", "globalThis", "window", "debugLogEvent", "event", "shouldEmit", "emitLitDebugLogEvents", "dispatchEvent", "CustomEvent", "detail", "undefined", "debugLogRenderId", "issueWarning", "litIssuedWarnings", "Set", "code", "warning", "has", "console", "warn", "add", "wrap", "ShadyDOM", "inUse", "noPatch", "node", "trustedTypes", "policy", "createPolicy", "createHTML", "s", "identityFunction", "value", "noopSanitizer", "_node", "_name", "_type", "setSanitizer", "newSanitizer", "sanitizerFactoryInternal", "Error", "_testOnlyClearSanitizerFactoryDoNotCallOrElse", "createSanitizer", "name", "type", "boundAttributeSuffix", "marker", "String", "Math", "random", "slice", "markerMatch", "node<PERSON>ark<PERSON>", "d", "document", "createTreeWalker", "createMarker", "createComment", "isPrimitive", "isArray", "Array", "isIterable", "Symbol", "iterator", "SPACE_CHAR", "ATTR_VALUE_CHAR", "NAME_CHAR", "textEndRegex", "COMMENT_START", "TAG_NAME", "DYNAMIC_TAG_NAME", "commentEndRegex", "comment2EndRegex", "tagEndRegex", "RegExp", "ENTIRE_MATCH", "ATTRIBUTE_NAME", "SPACES_AND_EQUALS", "QUOTE_CHAR", "singleQuoteAttrEndRegex", "doubleQuoteAttrEndRegex", "rawTextElement", "HTML_RESULT", "SVG_RESULT", "ATTRIBUTE_PART", "CHILD_PART", "PROPERTY_PART", "BOOLEAN_ATTRIBUTE_PART", "EVENT_PART", "ELEMENT_PART", "COMMENT_PART", "tag", "strings", "values", "some", "html", "svg", "noChange", "for", "nothing", "templateCache", "WeakMap", "walker", "trustFromTemplateString", "tsa", "stringFromTSA", "hasOwnProperty", "message", "trim", "replace", "getTemplateHtml", "l", "length", "attrNames", "rawTextEndRegex", "regex", "i", "attrNameEndIndex", "attrName", "lastIndex", "match", "exec", "test", "assert", "end", "startsWith", "push", "htmlResult", "Template", "constructor", "options", "parts", "nodeIndex", "attrNameIndex", "partCount", "el", "createElement", "currentNode", "content", "svgElement", "<PERSON><PERSON><PERSON><PERSON>", "remove", "append", "childNodes", "nextNode", "nodeType", "localName", "innerHTML", "includes", "m", "hasAttributes", "attrsToRemove", "getAttributeNames", "endsWith", "realName", "getAttribute", "toLowerCase", "statics", "split", "index", "ctor", "PropertyPart", "BooleanAttributePart", "EventPart", "AttributePart", "removeAttribute", "tagName", "textContent", "emptyScript", "data", "indexOf", "kind", "template", "clonableTemplate", "_options", "resolveDirective", "part", "parent", "attributeIndex", "currentDirective", "__directives", "__directive", "nextDirectiveConstructor", "call", "_$initialize", "_$resolve", "TemplateInstance", "_$parts", "_$disconnectableChildren", "_$template", "_$parent", "parentNode", "_$isConnected", "_clone", "fragment", "creationScope", "importNode", "partIndex", "templatePart", "<PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "ElementPart", "_update", "valueIndex", "templateInstance", "_$setValue", "startNode", "endNode", "_$committedValue", "_$startNode", "_$endNode", "__isConnected", "isConnected", "_textSanitizer", "directiveParent", "start", "_$clear", "_commitText", "_commitTemplateResult", "host", "_commitNode", "_commitIterable", "_insert", "insertBefore", "parentNodeName", "nodeName", "textNode", "createTextNode", "result", "_$getTemplate", "h", "instance", "get", "set", "itemParts", "itemPart", "item", "from", "_$notifyConnectionChanged", "n", "setConnected", "element", "fill", "_sanitizer", "noCommit", "change", "v", "_commitValue", "setAttribute", "arguments", "emptyStringForBooleanAttribute", "newListener", "old<PERSON><PERSON><PERSON>", "shouldRemoveListener", "capture", "once", "passive", "shouldAddListener", "removeListener", "addListener", "removeEventListener", "addEventListener", "handleEvent", "_$LH", "_boundAttributeSuffix", "_marker", "_markerMatch", "_HTML_RESULT", "_getTemplateHtml", "_TemplateInstance", "_isIterable", "_resolveDirective", "_<PERSON><PERSON><PERSON>", "_AttributePart", "_BooleanAttributePart", "_EventPart", "_PropertyPart", "_ElementPart", "polyfillSupport", "litHtmlPolyfillSupportDevMode", "litHtmlPolyfillSupport", "litHtmlVersions", "render", "container", "TypeError", "renderId", "partOwnerNode", "renderBefore", "id"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/lit-html/development/lit-html.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nvar _a, _b, _c, _d;\nconst DEV_MODE = true;\nconst ENABLE_EXTRA_SECURITY_HOOKS = true;\nconst ENABLE_SHADYDOM_NOPATCH = true;\nconst NODE_MODE = false;\n// Use window for browser builds because IE11 doesn't have globalThis.\nconst global = NODE_MODE ? globalThis : window;\n/**\n * Useful for visualizing and logging insights into what the Lit template system is doing.\n *\n * Compiled out of prod mode builds.\n */\nconst debugLogEvent = DEV_MODE\n    ? (event) => {\n        const shouldEmit = global\n            .emitLitDebugLogEvents;\n        if (!shouldEmit) {\n            return;\n        }\n        global.dispatchEvent(new CustomEvent('lit-debug', {\n            detail: event,\n        }));\n    }\n    : undefined;\n// Used for connecting beginRender and endRender events when there are nested\n// renders when errors are thrown preventing an endRender event from being\n// called.\nlet debugLogRenderId = 0;\nlet issueWarning;\nif (DEV_MODE) {\n    (_a = global.litIssuedWarnings) !== null && _a !== void 0 ? _a : (global.litIssuedWarnings = new Set());\n    // Issue a warning, if we haven't already.\n    issueWarning = (code, warning) => {\n        warning += code\n            ? ` See https://lit.dev/msg/${code} for more information.`\n            : '';\n        if (!global.litIssuedWarnings.has(warning)) {\n            console.warn(warning);\n            global.litIssuedWarnings.add(warning);\n        }\n    };\n    issueWarning('dev-mode', `Lit is in dev mode. Not recommended for production!`);\n}\nconst wrap = ENABLE_SHADYDOM_NOPATCH &&\n    ((_b = global.ShadyDOM) === null || _b === void 0 ? void 0 : _b.inUse) &&\n    ((_c = global.ShadyDOM) === null || _c === void 0 ? void 0 : _c.noPatch) === true\n    ? global.ShadyDOM.wrap\n    : (node) => node;\nconst trustedTypes = global.trustedTypes;\n/**\n * Our TrustedTypePolicy for HTML which is declared using the html template\n * tag function.\n *\n * That HTML is a developer-authored constant, and is parsed with innerHTML\n * before any untrusted expressions have been mixed in. Therefor it is\n * considered safe by construction.\n */\nconst policy = trustedTypes\n    ? trustedTypes.createPolicy('lit-html', {\n        createHTML: (s) => s,\n    })\n    : undefined;\nconst identityFunction = (value) => value;\nconst noopSanitizer = (_node, _name, _type) => identityFunction;\n/** Sets the global sanitizer factory. */\nconst setSanitizer = (newSanitizer) => {\n    if (!ENABLE_EXTRA_SECURITY_HOOKS) {\n        return;\n    }\n    if (sanitizerFactoryInternal !== noopSanitizer) {\n        throw new Error(`Attempted to overwrite existing lit-html security policy.` +\n            ` setSanitizeDOMValueFactory should be called at most once.`);\n    }\n    sanitizerFactoryInternal = newSanitizer;\n};\n/**\n * Only used in internal tests, not a part of the public API.\n */\nconst _testOnlyClearSanitizerFactoryDoNotCallOrElse = () => {\n    sanitizerFactoryInternal = noopSanitizer;\n};\nconst createSanitizer = (node, name, type) => {\n    return sanitizerFactoryInternal(node, name, type);\n};\n// Added to an attribute name to mark the attribute as bound so we can find\n// it easily.\nconst boundAttributeSuffix = '$lit$';\n// This marker is used in many syntactic positions in HTML, so it must be\n// a valid element name and attribute name. We don't support dynamic names (yet)\n// but this at least ensures that the parse tree is closer to the template\n// intention.\nconst marker = `lit$${String(Math.random()).slice(9)}$`;\n// String used to tell if a comment is a marker comment\nconst markerMatch = '?' + marker;\n// Text used to insert a comment marker node. We use processing instruction\n// syntax because it's slightly smaller, but parses as a comment node.\nconst nodeMarker = `<${markerMatch}>`;\nconst d = NODE_MODE && global.document === undefined\n    ? {\n        createTreeWalker() {\n            return {};\n        },\n    }\n    : document;\n// Creates a dynamic marker. We never have to search for these in the DOM.\nconst createMarker = () => d.createComment('');\nconst isPrimitive = (value) => value === null || (typeof value != 'object' && typeof value != 'function');\nconst isArray = Array.isArray;\nconst isIterable = (value) => isArray(value) ||\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    typeof (value === null || value === void 0 ? void 0 : value[Symbol.iterator]) === 'function';\nconst SPACE_CHAR = `[ \\t\\n\\f\\r]`;\nconst ATTR_VALUE_CHAR = `[^ \\t\\n\\f\\r\"'\\`<>=]`;\nconst NAME_CHAR = `[^\\\\s\"'>=/]`;\n// These regexes represent the five parsing states that we care about in the\n// Template's HTML scanner. They match the *end* of the state they're named\n// after.\n// Depending on the match, we transition to a new state. If there's no match,\n// we stay in the same state.\n// Note that the regexes are stateful. We utilize lastIndex and sync it\n// across the multiple regexes used. In addition to the five regexes below\n// we also dynamically create a regex to find the matching end tags for raw\n// text elements.\n/**\n * End of text is: `<` followed by:\n *   (comment start) or (tag) or (dynamic tag binding)\n */\nconst textEndRegex = /<(?:(!--|\\/[^a-zA-Z])|(\\/?[a-zA-Z][^>\\s]*)|(\\/?$))/g;\nconst COMMENT_START = 1;\nconst TAG_NAME = 2;\nconst DYNAMIC_TAG_NAME = 3;\nconst commentEndRegex = /-->/g;\n/**\n * Comments not started with <!--, like </{, can be ended by a single `>`\n */\nconst comment2EndRegex = />/g;\n/**\n * The tagEnd regex matches the end of the \"inside an opening\" tag syntax\n * position. It either matches a `>`, an attribute-like sequence, or the end\n * of the string after a space (attribute-name position ending).\n *\n * See attributes in the HTML spec:\n * https://www.w3.org/TR/html5/syntax.html#elements-attributes\n *\n * \" \\t\\n\\f\\r\" are HTML space characters:\n * https://infra.spec.whatwg.org/#ascii-whitespace\n *\n * So an attribute is:\n *  * The name: any character except a whitespace character, (\"), ('), \">\",\n *    \"=\", or \"/\". Note: this is different from the HTML spec which also excludes control characters.\n *  * Followed by zero or more space characters\n *  * Followed by \"=\"\n *  * Followed by zero or more space characters\n *  * Followed by:\n *    * Any character except space, ('), (\"), \"<\", \">\", \"=\", (`), or\n *    * (\") then any non-(\"), or\n *    * (') then any non-(')\n */\nconst tagEndRegex = new RegExp(`>|${SPACE_CHAR}(?:(${NAME_CHAR}+)(${SPACE_CHAR}*=${SPACE_CHAR}*(?:${ATTR_VALUE_CHAR}|(\"|')|))|$)`, 'g');\nconst ENTIRE_MATCH = 0;\nconst ATTRIBUTE_NAME = 1;\nconst SPACES_AND_EQUALS = 2;\nconst QUOTE_CHAR = 3;\nconst singleQuoteAttrEndRegex = /'/g;\nconst doubleQuoteAttrEndRegex = /\"/g;\n/**\n * Matches the raw text elements.\n *\n * Comments are not parsed within raw text elements, so we need to search their\n * text content for marker strings.\n */\nconst rawTextElement = /^(?:script|style|textarea|title)$/i;\n/** TemplateResult types */\nconst HTML_RESULT = 1;\nconst SVG_RESULT = 2;\n// TemplatePart types\n// IMPORTANT: these must match the values in PartType\nconst ATTRIBUTE_PART = 1;\nconst CHILD_PART = 2;\nconst PROPERTY_PART = 3;\nconst BOOLEAN_ATTRIBUTE_PART = 4;\nconst EVENT_PART = 5;\nconst ELEMENT_PART = 6;\nconst COMMENT_PART = 7;\n/**\n * Generates a template literal tag function that returns a TemplateResult with\n * the given result type.\n */\nconst tag = (type) => (strings, ...values) => {\n    // Warn against templates octal escape sequences\n    // We do this here rather than in render so that the warning is closer to the\n    // template definition.\n    if (DEV_MODE && strings.some((s) => s === undefined)) {\n        console.warn('Some template strings are undefined.\\n' +\n            'This is probably caused by illegal octal escape sequences.');\n    }\n    return {\n        // This property needs to remain unminified.\n        ['_$litType$']: type,\n        strings,\n        values,\n    };\n};\n/**\n * Interprets a template literal as an HTML template that can efficiently\n * render to and update a container.\n *\n * ```ts\n * const header = (title: string) => html`<h1>${title}</h1>`;\n * ```\n *\n * The `html` tag returns a description of the DOM to render as a value. It is\n * lazy, meaning no work is done until the template is rendered. When rendering,\n * if a template comes from the same expression as a previously rendered result,\n * it's efficiently updated instead of replaced.\n */\nexport const html = tag(HTML_RESULT);\n/**\n * Interprets a template literal as an SVG fragment that can efficiently\n * render to and update a container.\n *\n * ```ts\n * const rect = svg`<rect width=\"10\" height=\"10\"></rect>`;\n *\n * const myImage = html`\n *   <svg viewBox=\"0 0 10 10\" xmlns=\"http://www.w3.org/2000/svg\">\n *     ${rect}\n *   </svg>`;\n * ```\n *\n * The `svg` *tag function* should only be used for SVG fragments, or elements\n * that would be contained **inside** an `<svg>` HTML element. A common error is\n * placing an `<svg>` *element* in a template tagged with the `svg` tag\n * function. The `<svg>` element is an HTML element and should be used within a\n * template tagged with the {@linkcode html} tag function.\n *\n * In LitElement usage, it's invalid to return an SVG fragment from the\n * `render()` method, as the SVG fragment will be contained within the element's\n * shadow root and thus cannot be used within an `<svg>` HTML element.\n */\nexport const svg = tag(SVG_RESULT);\n/**\n * A sentinel value that signals that a value was handled by a directive and\n * should not be written to the DOM.\n */\nexport const noChange = Symbol.for('lit-noChange');\n/**\n * A sentinel value that signals a ChildPart to fully clear its content.\n *\n * ```ts\n * const button = html`${\n *  user.isAdmin\n *    ? html`<button>DELETE</button>`\n *    : nothing\n * }`;\n * ```\n *\n * Prefer using `nothing` over other falsy values as it provides a consistent\n * behavior between various expression binding contexts.\n *\n * In child expressions, `undefined`, `null`, `''`, and `nothing` all behave the\n * same and render no nodes. In attribute expressions, `nothing` _removes_ the\n * attribute, while `undefined` and `null` will render an empty string. In\n * property expressions `nothing` becomes `undefined`.\n */\nexport const nothing = Symbol.for('lit-nothing');\n/**\n * The cache of prepared templates, keyed by the tagged TemplateStringsArray\n * and _not_ accounting for the specific template tag used. This means that\n * template tags cannot be dynamic - the must statically be one of html, svg,\n * or attr. This restriction simplifies the cache lookup, which is on the hot\n * path for rendering.\n */\nconst templateCache = new WeakMap();\nconst walker = d.createTreeWalker(d, 129 /* NodeFilter.SHOW_{ELEMENT|COMMENT} */, null, false);\nlet sanitizerFactoryInternal = noopSanitizer;\nfunction trustFromTemplateString(tsa, stringFromTSA) {\n    // A security check to prevent spoofing of Lit template results.\n    // In the future, we may be able to replace this with Array.isTemplateObject,\n    // though we might need to make that check inside of the html and svg\n    // functions, because precompiled templates don't come in as\n    // TemplateStringArray objects.\n    if (!Array.isArray(tsa) || !tsa.hasOwnProperty('raw')) {\n        let message = 'invalid template strings array';\n        if (DEV_MODE) {\n            message = `\n          Internal Error: expected template strings to be an array\n          with a 'raw' field. Faking a template strings array by\n          calling html or svg like an ordinary function is effectively\n          the same as calling unsafeHtml and can lead to major security\n          issues, e.g. opening your code up to XSS attacks.\n          If you're using the html or svg tagged template functions normally\n          and still seeing this error, please file a bug at\n          https://github.com/lit/lit/issues/new?template=bug_report.md\n          and include information about your build tooling, if any.\n        `\n                .trim()\n                .replace(/\\n */g, '\\n');\n        }\n        throw new Error(message);\n    }\n    return policy !== undefined\n        ? policy.createHTML(stringFromTSA)\n        : stringFromTSA;\n}\n/**\n * Returns an HTML string for the given TemplateStringsArray and result type\n * (HTML or SVG), along with the case-sensitive bound attribute names in\n * template order. The HTML contains comment markers denoting the `ChildPart`s\n * and suffixes on bound attributes denoting the `AttributeParts`.\n *\n * @param strings template strings array\n * @param type HTML or SVG\n * @return Array containing `[html, attrNames]` (array returned for terseness,\n *     to avoid object fields since this code is shared with non-minified SSR\n *     code)\n */\nconst getTemplateHtml = (strings, type) => {\n    // Insert makers into the template HTML to represent the position of\n    // bindings. The following code scans the template strings to determine the\n    // syntactic position of the bindings. They can be in text position, where\n    // we insert an HTML comment, attribute value position, where we insert a\n    // sentinel string and re-write the attribute name, or inside a tag where\n    // we insert the sentinel string.\n    const l = strings.length - 1;\n    // Stores the case-sensitive bound attribute names in the order of their\n    // parts. ElementParts are also reflected in this array as undefined\n    // rather than a string, to disambiguate from attribute bindings.\n    const attrNames = [];\n    let html = type === SVG_RESULT ? '<svg>' : '';\n    // When we're inside a raw text tag (not it's text content), the regex\n    // will still be tagRegex so we can find attributes, but will switch to\n    // this regex when the tag ends.\n    let rawTextEndRegex;\n    // The current parsing state, represented as a reference to one of the\n    // regexes\n    let regex = textEndRegex;\n    for (let i = 0; i < l; i++) {\n        const s = strings[i];\n        // The index of the end of the last attribute name. When this is\n        // positive at end of a string, it means we're in an attribute value\n        // position and need to rewrite the attribute name.\n        // We also use a special value of -2 to indicate that we encountered\n        // the end of a string in attribute name position.\n        let attrNameEndIndex = -1;\n        let attrName;\n        let lastIndex = 0;\n        let match;\n        // The conditions in this loop handle the current parse state, and the\n        // assignments to the `regex` variable are the state transitions.\n        while (lastIndex < s.length) {\n            // Make sure we start searching from where we previously left off\n            regex.lastIndex = lastIndex;\n            match = regex.exec(s);\n            if (match === null) {\n                break;\n            }\n            lastIndex = regex.lastIndex;\n            if (regex === textEndRegex) {\n                if (match[COMMENT_START] === '!--') {\n                    regex = commentEndRegex;\n                }\n                else if (match[COMMENT_START] !== undefined) {\n                    // We started a weird comment, like </{\n                    regex = comment2EndRegex;\n                }\n                else if (match[TAG_NAME] !== undefined) {\n                    if (rawTextElement.test(match[TAG_NAME])) {\n                        // Record if we encounter a raw-text element. We'll switch to\n                        // this regex at the end of the tag.\n                        rawTextEndRegex = new RegExp(`</${match[TAG_NAME]}`, 'g');\n                    }\n                    regex = tagEndRegex;\n                }\n                else if (match[DYNAMIC_TAG_NAME] !== undefined) {\n                    if (DEV_MODE) {\n                        throw new Error('Bindings in tag names are not supported. Please use static templates instead. ' +\n                            'See https://lit.dev/docs/templates/expressions/#static-expressions');\n                    }\n                    regex = tagEndRegex;\n                }\n            }\n            else if (regex === tagEndRegex) {\n                if (match[ENTIRE_MATCH] === '>') {\n                    // End of a tag. If we had started a raw-text element, use that\n                    // regex\n                    regex = rawTextEndRegex !== null && rawTextEndRegex !== void 0 ? rawTextEndRegex : textEndRegex;\n                    // We may be ending an unquoted attribute value, so make sure we\n                    // clear any pending attrNameEndIndex\n                    attrNameEndIndex = -1;\n                }\n                else if (match[ATTRIBUTE_NAME] === undefined) {\n                    // Attribute name position\n                    attrNameEndIndex = -2;\n                }\n                else {\n                    attrNameEndIndex = regex.lastIndex - match[SPACES_AND_EQUALS].length;\n                    attrName = match[ATTRIBUTE_NAME];\n                    regex =\n                        match[QUOTE_CHAR] === undefined\n                            ? tagEndRegex\n                            : match[QUOTE_CHAR] === '\"'\n                                ? doubleQuoteAttrEndRegex\n                                : singleQuoteAttrEndRegex;\n                }\n            }\n            else if (regex === doubleQuoteAttrEndRegex ||\n                regex === singleQuoteAttrEndRegex) {\n                regex = tagEndRegex;\n            }\n            else if (regex === commentEndRegex || regex === comment2EndRegex) {\n                regex = textEndRegex;\n            }\n            else {\n                // Not one of the five state regexes, so it must be the dynamically\n                // created raw text regex and we're at the close of that element.\n                regex = tagEndRegex;\n                rawTextEndRegex = undefined;\n            }\n        }\n        if (DEV_MODE) {\n            // If we have a attrNameEndIndex, which indicates that we should\n            // rewrite the attribute name, assert that we're in a valid attribute\n            // position - either in a tag, or a quoted attribute value.\n            console.assert(attrNameEndIndex === -1 ||\n                regex === tagEndRegex ||\n                regex === singleQuoteAttrEndRegex ||\n                regex === doubleQuoteAttrEndRegex, 'unexpected parse state B');\n        }\n        // We have four cases:\n        //  1. We're in text position, and not in a raw text element\n        //     (regex === textEndRegex): insert a comment marker.\n        //  2. We have a non-negative attrNameEndIndex which means we need to\n        //     rewrite the attribute name to add a bound attribute suffix.\n        //  3. We're at the non-first binding in a multi-binding attribute, use a\n        //     plain marker.\n        //  4. We're somewhere else inside the tag. If we're in attribute name\n        //     position (attrNameEndIndex === -2), add a sequential suffix to\n        //     generate a unique attribute name.\n        // Detect a binding next to self-closing tag end and insert a space to\n        // separate the marker from the tag end:\n        const end = regex === tagEndRegex && strings[i + 1].startsWith('/>') ? ' ' : '';\n        html +=\n            regex === textEndRegex\n                ? s + nodeMarker\n                : attrNameEndIndex >= 0\n                    ? (attrNames.push(attrName),\n                        s.slice(0, attrNameEndIndex) +\n                            boundAttributeSuffix +\n                            s.slice(attrNameEndIndex)) +\n                        marker +\n                        end\n                    : s +\n                        marker +\n                        (attrNameEndIndex === -2 ? (attrNames.push(undefined), i) : end);\n    }\n    const htmlResult = html + (strings[l] || '<?>') + (type === SVG_RESULT ? '</svg>' : '');\n    // Returned as an array for terseness\n    return [trustFromTemplateString(strings, htmlResult), attrNames];\n};\nclass Template {\n    constructor(\n    // This property needs to remain unminified.\n    { strings, ['_$litType$']: type }, options) {\n        this.parts = [];\n        let node;\n        let nodeIndex = 0;\n        let attrNameIndex = 0;\n        const partCount = strings.length - 1;\n        const parts = this.parts;\n        // Create template element\n        const [html, attrNames] = getTemplateHtml(strings, type);\n        this.el = Template.createElement(html, options);\n        walker.currentNode = this.el.content;\n        // Reparent SVG nodes into template root\n        if (type === SVG_RESULT) {\n            const content = this.el.content;\n            const svgElement = content.firstChild;\n            svgElement.remove();\n            content.append(...svgElement.childNodes);\n        }\n        // Walk the template to find binding markers and create TemplateParts\n        while ((node = walker.nextNode()) !== null && parts.length < partCount) {\n            if (node.nodeType === 1) {\n                if (DEV_MODE) {\n                    const tag = node.localName;\n                    // Warn if `textarea` includes an expression and throw if `template`\n                    // does since these are not supported. We do this by checking\n                    // innerHTML for anything that looks like a marker. This catches\n                    // cases like bindings in textarea there markers turn into text nodes.\n                    if (/^(?:textarea|template)$/i.test(tag) &&\n                        node.innerHTML.includes(marker)) {\n                        const m = `Expressions are not supported inside \\`${tag}\\` ` +\n                            `elements. See https://lit.dev/msg/expression-in-${tag} for more ` +\n                            `information.`;\n                        if (tag === 'template') {\n                            throw new Error(m);\n                        }\n                        else\n                            issueWarning('', m);\n                    }\n                }\n                // TODO (justinfagnani): for attempted dynamic tag names, we don't\n                // increment the bindingIndex, and it'll be off by 1 in the element\n                // and off by two after it.\n                if (node.hasAttributes()) {\n                    // We defer removing bound attributes because on IE we might not be\n                    // iterating attributes in their template order, and would sometimes\n                    // remove an attribute that we still need to create a part for.\n                    const attrsToRemove = [];\n                    for (const name of node.getAttributeNames()) {\n                        // `name` is the name of the attribute we're iterating over, but not\n                        // _necessarily_ the name of the attribute we will create a part\n                        // for. They can be different in browsers that don't iterate on\n                        // attributes in source order. In that case the attrNames array\n                        // contains the attribute name we'll process next. We only need the\n                        // attribute name here to know if we should process a bound attribute\n                        // on this element.\n                        if (name.endsWith(boundAttributeSuffix) ||\n                            name.startsWith(marker)) {\n                            const realName = attrNames[attrNameIndex++];\n                            attrsToRemove.push(name);\n                            if (realName !== undefined) {\n                                // Lowercase for case-sensitive SVG attributes like viewBox\n                                const value = node.getAttribute(realName.toLowerCase() + boundAttributeSuffix);\n                                const statics = value.split(marker);\n                                const m = /([.?@])?(.*)/.exec(realName);\n                                parts.push({\n                                    type: ATTRIBUTE_PART,\n                                    index: nodeIndex,\n                                    name: m[2],\n                                    strings: statics,\n                                    ctor: m[1] === '.'\n                                        ? PropertyPart\n                                        : m[1] === '?'\n                                            ? BooleanAttributePart\n                                            : m[1] === '@'\n                                                ? EventPart\n                                                : AttributePart,\n                                });\n                            }\n                            else {\n                                parts.push({\n                                    type: ELEMENT_PART,\n                                    index: nodeIndex,\n                                });\n                            }\n                        }\n                    }\n                    for (const name of attrsToRemove) {\n                        node.removeAttribute(name);\n                    }\n                }\n                // TODO (justinfagnani): benchmark the regex against testing for each\n                // of the 3 raw text element names.\n                if (rawTextElement.test(node.tagName)) {\n                    // For raw text elements we need to split the text content on\n                    // markers, create a Text node for each segment, and create\n                    // a TemplatePart for each marker.\n                    const strings = node.textContent.split(marker);\n                    const lastIndex = strings.length - 1;\n                    if (lastIndex > 0) {\n                        node.textContent = trustedTypes\n                            ? trustedTypes.emptyScript\n                            : '';\n                        // Generate a new text node for each literal section\n                        // These nodes are also used as the markers for node parts\n                        // We can't use empty text nodes as markers because they're\n                        // normalized when cloning in IE (could simplify when\n                        // IE is no longer supported)\n                        for (let i = 0; i < lastIndex; i++) {\n                            node.append(strings[i], createMarker());\n                            // Walk past the marker node we just added\n                            walker.nextNode();\n                            parts.push({ type: CHILD_PART, index: ++nodeIndex });\n                        }\n                        // Note because this marker is added after the walker's current\n                        // node, it will be walked to in the outer loop (and ignored), so\n                        // we don't need to adjust nodeIndex here\n                        node.append(strings[lastIndex], createMarker());\n                    }\n                }\n            }\n            else if (node.nodeType === 8) {\n                const data = node.data;\n                if (data === markerMatch) {\n                    parts.push({ type: CHILD_PART, index: nodeIndex });\n                }\n                else {\n                    let i = -1;\n                    while ((i = node.data.indexOf(marker, i + 1)) !== -1) {\n                        // Comment node has a binding marker inside, make an inactive part\n                        // The binding won't work, but subsequent bindings will\n                        parts.push({ type: COMMENT_PART, index: nodeIndex });\n                        // Move to the end of the match\n                        i += marker.length - 1;\n                    }\n                }\n            }\n            nodeIndex++;\n        }\n        // We could set walker.currentNode to another node here to prevent a memory\n        // leak, but every time we prepare a template, we immediately render it\n        // and re-use the walker in new TemplateInstance._clone().\n        debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n            kind: 'template prep',\n            template: this,\n            clonableTemplate: this.el,\n            parts: this.parts,\n            strings,\n        });\n    }\n    // Overridden via `litHtmlPolyfillSupport` to provide platform support.\n    /** @nocollapse */\n    static createElement(html, _options) {\n        const el = d.createElement('template');\n        el.innerHTML = html;\n        return el;\n    }\n}\nfunction resolveDirective(part, value, parent = part, attributeIndex) {\n    var _a, _b, _c;\n    var _d;\n    // Bail early if the value is explicitly noChange. Note, this means any\n    // nested directive is still attached and is not run.\n    if (value === noChange) {\n        return value;\n    }\n    let currentDirective = attributeIndex !== undefined\n        ? (_a = parent.__directives) === null || _a === void 0 ? void 0 : _a[attributeIndex]\n        : parent.__directive;\n    const nextDirectiveConstructor = isPrimitive(value)\n        ? undefined\n        : // This property needs to remain unminified.\n            value['_$litDirective$'];\n    if ((currentDirective === null || currentDirective === void 0 ? void 0 : currentDirective.constructor) !== nextDirectiveConstructor) {\n        // This property needs to remain unminified.\n        (_b = currentDirective === null || currentDirective === void 0 ? void 0 : currentDirective['_$notifyDirectiveConnectionChanged']) === null || _b === void 0 ? void 0 : _b.call(currentDirective, false);\n        if (nextDirectiveConstructor === undefined) {\n            currentDirective = undefined;\n        }\n        else {\n            currentDirective = new nextDirectiveConstructor(part);\n            currentDirective._$initialize(part, parent, attributeIndex);\n        }\n        if (attributeIndex !== undefined) {\n            ((_c = (_d = parent).__directives) !== null && _c !== void 0 ? _c : (_d.__directives = []))[attributeIndex] =\n                currentDirective;\n        }\n        else {\n            parent.__directive = currentDirective;\n        }\n    }\n    if (currentDirective !== undefined) {\n        value = resolveDirective(part, currentDirective._$resolve(part, value.values), currentDirective, attributeIndex);\n    }\n    return value;\n}\n/**\n * An updateable instance of a Template. Holds references to the Parts used to\n * update the template instance.\n */\nclass TemplateInstance {\n    constructor(template, parent) {\n        this._$parts = [];\n        /** @internal */\n        this._$disconnectableChildren = undefined;\n        this._$template = template;\n        this._$parent = parent;\n    }\n    // Called by ChildPart parentNode getter\n    get parentNode() {\n        return this._$parent.parentNode;\n    }\n    // See comment in Disconnectable interface for why this is a getter\n    get _$isConnected() {\n        return this._$parent._$isConnected;\n    }\n    // This method is separate from the constructor because we need to return a\n    // DocumentFragment and we don't want to hold onto it with an instance field.\n    _clone(options) {\n        var _a;\n        const { el: { content }, parts: parts, } = this._$template;\n        const fragment = ((_a = options === null || options === void 0 ? void 0 : options.creationScope) !== null && _a !== void 0 ? _a : d).importNode(content, true);\n        walker.currentNode = fragment;\n        let node = walker.nextNode();\n        let nodeIndex = 0;\n        let partIndex = 0;\n        let templatePart = parts[0];\n        while (templatePart !== undefined) {\n            if (nodeIndex === templatePart.index) {\n                let part;\n                if (templatePart.type === CHILD_PART) {\n                    part = new ChildPart(node, node.nextSibling, this, options);\n                }\n                else if (templatePart.type === ATTRIBUTE_PART) {\n                    part = new templatePart.ctor(node, templatePart.name, templatePart.strings, this, options);\n                }\n                else if (templatePart.type === ELEMENT_PART) {\n                    part = new ElementPart(node, this, options);\n                }\n                this._$parts.push(part);\n                templatePart = parts[++partIndex];\n            }\n            if (nodeIndex !== (templatePart === null || templatePart === void 0 ? void 0 : templatePart.index)) {\n                node = walker.nextNode();\n                nodeIndex++;\n            }\n        }\n        // We need to set the currentNode away from the cloned tree so that we\n        // don't hold onto the tree even if the tree is detached and should be\n        // freed.\n        walker.currentNode = d;\n        return fragment;\n    }\n    _update(values) {\n        let i = 0;\n        for (const part of this._$parts) {\n            if (part !== undefined) {\n                debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                    kind: 'set part',\n                    part,\n                    value: values[i],\n                    valueIndex: i,\n                    values,\n                    templateInstance: this,\n                });\n                if (part.strings !== undefined) {\n                    part._$setValue(values, part, i);\n                    // The number of values the part consumes is part.strings.length - 1\n                    // since values are in between template spans. We increment i by 1\n                    // later in the loop, so increment it by part.strings.length - 2 here\n                    i += part.strings.length - 2;\n                }\n                else {\n                    part._$setValue(values[i]);\n                }\n            }\n            i++;\n        }\n    }\n}\nclass ChildPart {\n    constructor(startNode, endNode, parent, options) {\n        var _a;\n        this.type = CHILD_PART;\n        this._$committedValue = nothing;\n        // The following fields will be patched onto ChildParts when required by\n        // AsyncDirective\n        /** @internal */\n        this._$disconnectableChildren = undefined;\n        this._$startNode = startNode;\n        this._$endNode = endNode;\n        this._$parent = parent;\n        this.options = options;\n        // Note __isConnected is only ever accessed on RootParts (i.e. when there is\n        // no _$parent); the value on a non-root-part is \"don't care\", but checking\n        // for parent would be more code\n        this.__isConnected = (_a = options === null || options === void 0 ? void 0 : options.isConnected) !== null && _a !== void 0 ? _a : true;\n        if (ENABLE_EXTRA_SECURITY_HOOKS) {\n            // Explicitly initialize for consistent class shape.\n            this._textSanitizer = undefined;\n        }\n    }\n    // See comment in Disconnectable interface for why this is a getter\n    get _$isConnected() {\n        var _a, _b;\n        // ChildParts that are not at the root should always be created with a\n        // parent; only RootChildNode's won't, so they return the local isConnected\n        // state\n        return (_b = (_a = this._$parent) === null || _a === void 0 ? void 0 : _a._$isConnected) !== null && _b !== void 0 ? _b : this.__isConnected;\n    }\n    /**\n     * The parent node into which the part renders its content.\n     *\n     * A ChildPart's content consists of a range of adjacent child nodes of\n     * `.parentNode`, possibly bordered by 'marker nodes' (`.startNode` and\n     * `.endNode`).\n     *\n     * - If both `.startNode` and `.endNode` are non-null, then the part's content\n     * consists of all siblings between `.startNode` and `.endNode`, exclusively.\n     *\n     * - If `.startNode` is non-null but `.endNode` is null, then the part's\n     * content consists of all siblings following `.startNode`, up to and\n     * including the last child of `.parentNode`. If `.endNode` is non-null, then\n     * `.startNode` will always be non-null.\n     *\n     * - If both `.endNode` and `.startNode` are null, then the part's content\n     * consists of all child nodes of `.parentNode`.\n     */\n    get parentNode() {\n        let parentNode = wrap(this._$startNode).parentNode;\n        const parent = this._$parent;\n        if (parent !== undefined &&\n            (parentNode === null || parentNode === void 0 ? void 0 : parentNode.nodeType) === 11 /* Node.DOCUMENT_FRAGMENT */) {\n            // If the parentNode is a DocumentFragment, it may be because the DOM is\n            // still in the cloned fragment during initial render; if so, get the real\n            // parentNode the part will be committed into by asking the parent.\n            parentNode = parent.parentNode;\n        }\n        return parentNode;\n    }\n    /**\n     * The part's leading marker node, if any. See `.parentNode` for more\n     * information.\n     */\n    get startNode() {\n        return this._$startNode;\n    }\n    /**\n     * The part's trailing marker node, if any. See `.parentNode` for more\n     * information.\n     */\n    get endNode() {\n        return this._$endNode;\n    }\n    _$setValue(value, directiveParent = this) {\n        var _a;\n        if (DEV_MODE && this.parentNode === null) {\n            throw new Error(`This \\`ChildPart\\` has no \\`parentNode\\` and therefore cannot accept a value. This likely means the element containing the part was manipulated in an unsupported way outside of Lit's control such that the part's marker nodes were ejected from DOM. For example, setting the element's \\`innerHTML\\` or \\`textContent\\` can do this.`);\n        }\n        value = resolveDirective(this, value, directiveParent);\n        if (isPrimitive(value)) {\n            // Non-rendering child values. It's important that these do not render\n            // empty text nodes to avoid issues with preventing default <slot>\n            // fallback content.\n            if (value === nothing || value == null || value === '') {\n                if (this._$committedValue !== nothing) {\n                    debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                        kind: 'commit nothing to child',\n                        start: this._$startNode,\n                        end: this._$endNode,\n                        parent: this._$parent,\n                        options: this.options,\n                    });\n                    this._$clear();\n                }\n                this._$committedValue = nothing;\n            }\n            else if (value !== this._$committedValue && value !== noChange) {\n                this._commitText(value);\n            }\n            // This property needs to remain unminified.\n        }\n        else if (value['_$litType$'] !== undefined) {\n            this._commitTemplateResult(value);\n        }\n        else if (value.nodeType !== undefined) {\n            if (DEV_MODE && ((_a = this.options) === null || _a === void 0 ? void 0 : _a.host) === value) {\n                this._commitText(`[probable mistake: rendered a template's host in itself ` +\n                    `(commonly caused by writing \\${this} in a template]`);\n                console.warn(`Attempted to render the template host`, value, `inside itself. This is almost always a mistake, and in dev mode `, `we render some warning text. In production however, we'll `, `render it, which will usually result in an error, and sometimes `, `in the element disappearing from the DOM.`);\n                return;\n            }\n            this._commitNode(value);\n        }\n        else if (isIterable(value)) {\n            this._commitIterable(value);\n        }\n        else {\n            // Fallback, will render the string representation\n            this._commitText(value);\n        }\n    }\n    _insert(node) {\n        return wrap(wrap(this._$startNode).parentNode).insertBefore(node, this._$endNode);\n    }\n    _commitNode(value) {\n        var _a;\n        if (this._$committedValue !== value) {\n            this._$clear();\n            if (ENABLE_EXTRA_SECURITY_HOOKS &&\n                sanitizerFactoryInternal !== noopSanitizer) {\n                const parentNodeName = (_a = this._$startNode.parentNode) === null || _a === void 0 ? void 0 : _a.nodeName;\n                if (parentNodeName === 'STYLE' || parentNodeName === 'SCRIPT') {\n                    let message = 'Forbidden';\n                    if (DEV_MODE) {\n                        if (parentNodeName === 'STYLE') {\n                            message =\n                                `Lit does not support binding inside style nodes. ` +\n                                    `This is a security risk, as style injection attacks can ` +\n                                    `exfiltrate data and spoof UIs. ` +\n                                    `Consider instead using css\\`...\\` literals ` +\n                                    `to compose styles, and make do dynamic styling with ` +\n                                    `css custom properties, ::parts, <slot>s, ` +\n                                    `and by mutating the DOM rather than stylesheets.`;\n                        }\n                        else {\n                            message =\n                                `Lit does not support binding inside script nodes. ` +\n                                    `This is a security risk, as it could allow arbitrary ` +\n                                    `code execution.`;\n                        }\n                    }\n                    throw new Error(message);\n                }\n            }\n            debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                kind: 'commit node',\n                start: this._$startNode,\n                parent: this._$parent,\n                value: value,\n                options: this.options,\n            });\n            this._$committedValue = this._insert(value);\n        }\n    }\n    _commitText(value) {\n        // If the committed value is a primitive it means we called _commitText on\n        // the previous render, and we know that this._$startNode.nextSibling is a\n        // Text node. We can now just replace the text content (.data) of the node.\n        if (this._$committedValue !== nothing &&\n            isPrimitive(this._$committedValue)) {\n            const node = wrap(this._$startNode).nextSibling;\n            if (ENABLE_EXTRA_SECURITY_HOOKS) {\n                if (this._textSanitizer === undefined) {\n                    this._textSanitizer = createSanitizer(node, 'data', 'property');\n                }\n                value = this._textSanitizer(value);\n            }\n            debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                kind: 'commit text',\n                node,\n                value,\n                options: this.options,\n            });\n            node.data = value;\n        }\n        else {\n            if (ENABLE_EXTRA_SECURITY_HOOKS) {\n                const textNode = d.createTextNode('');\n                this._commitNode(textNode);\n                // When setting text content, for security purposes it matters a lot\n                // what the parent is. For example, <style> and <script> need to be\n                // handled with care, while <span> does not. So first we need to put a\n                // text node into the document, then we can sanitize its content.\n                if (this._textSanitizer === undefined) {\n                    this._textSanitizer = createSanitizer(textNode, 'data', 'property');\n                }\n                value = this._textSanitizer(value);\n                debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                    kind: 'commit text',\n                    node: textNode,\n                    value,\n                    options: this.options,\n                });\n                textNode.data = value;\n            }\n            else {\n                this._commitNode(d.createTextNode(value));\n                debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                    kind: 'commit text',\n                    node: wrap(this._$startNode).nextSibling,\n                    value,\n                    options: this.options,\n                });\n            }\n        }\n        this._$committedValue = value;\n    }\n    _commitTemplateResult(result) {\n        var _a;\n        // This property needs to remain unminified.\n        const { values, ['_$litType$']: type } = result;\n        // If $litType$ is a number, result is a plain TemplateResult and we get\n        // the template from the template cache. If not, result is a\n        // CompiledTemplateResult and _$litType$ is a CompiledTemplate and we need\n        // to create the <template> element the first time we see it.\n        const template = typeof type === 'number'\n            ? this._$getTemplate(result)\n            : (type.el === undefined &&\n                (type.el = Template.createElement(trustFromTemplateString(type.h, type.h[0]), this.options)),\n                type);\n        if (((_a = this._$committedValue) === null || _a === void 0 ? void 0 : _a._$template) === template) {\n            debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                kind: 'template updating',\n                template,\n                instance: this._$committedValue,\n                parts: this._$committedValue._$parts,\n                options: this.options,\n                values,\n            });\n            this._$committedValue._update(values);\n        }\n        else {\n            const instance = new TemplateInstance(template, this);\n            const fragment = instance._clone(this.options);\n            debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                kind: 'template instantiated',\n                template,\n                instance,\n                parts: instance._$parts,\n                options: this.options,\n                fragment,\n                values,\n            });\n            instance._update(values);\n            debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                kind: 'template instantiated and updated',\n                template,\n                instance,\n                parts: instance._$parts,\n                options: this.options,\n                fragment,\n                values,\n            });\n            this._commitNode(fragment);\n            this._$committedValue = instance;\n        }\n    }\n    // Overridden via `litHtmlPolyfillSupport` to provide platform support.\n    /** @internal */\n    _$getTemplate(result) {\n        let template = templateCache.get(result.strings);\n        if (template === undefined) {\n            templateCache.set(result.strings, (template = new Template(result)));\n        }\n        return template;\n    }\n    _commitIterable(value) {\n        // For an Iterable, we create a new InstancePart per item, then set its\n        // value to the item. This is a little bit of overhead for every item in\n        // an Iterable, but it lets us recurse easily and efficiently update Arrays\n        // of TemplateResults that will be commonly returned from expressions like:\n        // array.map((i) => html`${i}`), by reusing existing TemplateInstances.\n        // If value is an array, then the previous render was of an\n        // iterable and value will contain the ChildParts from the previous\n        // render. If value is not an array, clear this part and make a new\n        // array for ChildParts.\n        if (!isArray(this._$committedValue)) {\n            this._$committedValue = [];\n            this._$clear();\n        }\n        // Lets us keep track of how many items we stamped so we can clear leftover\n        // items from a previous render\n        const itemParts = this._$committedValue;\n        let partIndex = 0;\n        let itemPart;\n        for (const item of value) {\n            if (partIndex === itemParts.length) {\n                // If no existing part, create a new one\n                // TODO (justinfagnani): test perf impact of always creating two parts\n                // instead of sharing parts between nodes\n                // https://github.com/lit/lit/issues/1266\n                itemParts.push((itemPart = new ChildPart(this._insert(createMarker()), this._insert(createMarker()), this, this.options)));\n            }\n            else {\n                // Reuse an existing part\n                itemPart = itemParts[partIndex];\n            }\n            itemPart._$setValue(item);\n            partIndex++;\n        }\n        if (partIndex < itemParts.length) {\n            // itemParts always have end nodes\n            this._$clear(itemPart && wrap(itemPart._$endNode).nextSibling, partIndex);\n            // Truncate the parts array so _value reflects the current state\n            itemParts.length = partIndex;\n        }\n    }\n    /**\n     * Removes the nodes contained within this Part from the DOM.\n     *\n     * @param start Start node to clear from, for clearing a subset of the part's\n     *     DOM (used when truncating iterables)\n     * @param from  When `start` is specified, the index within the iterable from\n     *     which ChildParts are being removed, used for disconnecting directives in\n     *     those Parts.\n     *\n     * @internal\n     */\n    _$clear(start = wrap(this._$startNode).nextSibling, from) {\n        var _a;\n        (_a = this._$notifyConnectionChanged) === null || _a === void 0 ? void 0 : _a.call(this, false, true, from);\n        while (start && start !== this._$endNode) {\n            const n = wrap(start).nextSibling;\n            wrap(start).remove();\n            start = n;\n        }\n    }\n    /**\n     * Implementation of RootPart's `isConnected`. Note that this metod\n     * should only be called on `RootPart`s (the `ChildPart` returned from a\n     * top-level `render()` call). It has no effect on non-root ChildParts.\n     * @param isConnected Whether to set\n     * @internal\n     */\n    setConnected(isConnected) {\n        var _a;\n        if (this._$parent === undefined) {\n            this.__isConnected = isConnected;\n            (_a = this._$notifyConnectionChanged) === null || _a === void 0 ? void 0 : _a.call(this, isConnected);\n        }\n        else if (DEV_MODE) {\n            throw new Error('part.setConnected() may only be called on a ' +\n                'RootPart returned from render().');\n        }\n    }\n}\nclass AttributePart {\n    constructor(element, name, strings, parent, options) {\n        this.type = ATTRIBUTE_PART;\n        /** @internal */\n        this._$committedValue = nothing;\n        /** @internal */\n        this._$disconnectableChildren = undefined;\n        this.element = element;\n        this.name = name;\n        this._$parent = parent;\n        this.options = options;\n        if (strings.length > 2 || strings[0] !== '' || strings[1] !== '') {\n            this._$committedValue = new Array(strings.length - 1).fill(new String());\n            this.strings = strings;\n        }\n        else {\n            this._$committedValue = nothing;\n        }\n        if (ENABLE_EXTRA_SECURITY_HOOKS) {\n            this._sanitizer = undefined;\n        }\n    }\n    get tagName() {\n        return this.element.tagName;\n    }\n    // See comment in Disconnectable interface for why this is a getter\n    get _$isConnected() {\n        return this._$parent._$isConnected;\n    }\n    /**\n     * Sets the value of this part by resolving the value from possibly multiple\n     * values and static strings and committing it to the DOM.\n     * If this part is single-valued, `this._strings` will be undefined, and the\n     * method will be called with a single value argument. If this part is\n     * multi-value, `this._strings` will be defined, and the method is called\n     * with the value array of the part's owning TemplateInstance, and an offset\n     * into the value array from which the values should be read.\n     * This method is overloaded this way to eliminate short-lived array slices\n     * of the template instance values, and allow a fast-path for single-valued\n     * parts.\n     *\n     * @param value The part value, or an array of values for multi-valued parts\n     * @param valueIndex the index to start reading values from. `undefined` for\n     *   single-valued parts\n     * @param noCommit causes the part to not commit its value to the DOM. Used\n     *   in hydration to prime attribute parts with their first-rendered value,\n     *   but not set the attribute, and in SSR to no-op the DOM operation and\n     *   capture the value for serialization.\n     *\n     * @internal\n     */\n    _$setValue(value, directiveParent = this, valueIndex, noCommit) {\n        const strings = this.strings;\n        // Whether any of the values has changed, for dirty-checking\n        let change = false;\n        if (strings === undefined) {\n            // Single-value binding case\n            value = resolveDirective(this, value, directiveParent, 0);\n            change =\n                !isPrimitive(value) ||\n                    (value !== this._$committedValue && value !== noChange);\n            if (change) {\n                this._$committedValue = value;\n            }\n        }\n        else {\n            // Interpolation case\n            const values = value;\n            value = strings[0];\n            let i, v;\n            for (i = 0; i < strings.length - 1; i++) {\n                v = resolveDirective(this, values[valueIndex + i], directiveParent, i);\n                if (v === noChange) {\n                    // If the user-provided value is `noChange`, use the previous value\n                    v = this._$committedValue[i];\n                }\n                change || (change = !isPrimitive(v) || v !== this._$committedValue[i]);\n                if (v === nothing) {\n                    value = nothing;\n                }\n                else if (value !== nothing) {\n                    value += (v !== null && v !== void 0 ? v : '') + strings[i + 1];\n                }\n                // We always record each value, even if one is `nothing`, for future\n                // change detection.\n                this._$committedValue[i] = v;\n            }\n        }\n        if (change && !noCommit) {\n            this._commitValue(value);\n        }\n    }\n    /** @internal */\n    _commitValue(value) {\n        if (value === nothing) {\n            wrap(this.element).removeAttribute(this.name);\n        }\n        else {\n            if (ENABLE_EXTRA_SECURITY_HOOKS) {\n                if (this._sanitizer === undefined) {\n                    this._sanitizer = sanitizerFactoryInternal(this.element, this.name, 'attribute');\n                }\n                value = this._sanitizer(value !== null && value !== void 0 ? value : '');\n            }\n            debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n                kind: 'commit attribute',\n                element: this.element,\n                name: this.name,\n                value,\n                options: this.options,\n            });\n            wrap(this.element).setAttribute(this.name, (value !== null && value !== void 0 ? value : ''));\n        }\n    }\n}\nclass PropertyPart extends AttributePart {\n    constructor() {\n        super(...arguments);\n        this.type = PROPERTY_PART;\n    }\n    /** @internal */\n    _commitValue(value) {\n        if (ENABLE_EXTRA_SECURITY_HOOKS) {\n            if (this._sanitizer === undefined) {\n                this._sanitizer = sanitizerFactoryInternal(this.element, this.name, 'property');\n            }\n            value = this._sanitizer(value);\n        }\n        debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n            kind: 'commit property',\n            element: this.element,\n            name: this.name,\n            value,\n            options: this.options,\n        });\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        this.element[this.name] = value === nothing ? undefined : value;\n    }\n}\n// Temporary workaround for https://crbug.com/993268\n// Currently, any attribute starting with \"on\" is considered to be a\n// TrustedScript source. Such boolean attributes must be set to the equivalent\n// trusted emptyScript value.\nconst emptyStringForBooleanAttribute = trustedTypes\n    ? trustedTypes.emptyScript\n    : '';\nclass BooleanAttributePart extends AttributePart {\n    constructor() {\n        super(...arguments);\n        this.type = BOOLEAN_ATTRIBUTE_PART;\n    }\n    /** @internal */\n    _commitValue(value) {\n        debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n            kind: 'commit boolean attribute',\n            element: this.element,\n            name: this.name,\n            value: !!(value && value !== nothing),\n            options: this.options,\n        });\n        if (value && value !== nothing) {\n            wrap(this.element).setAttribute(this.name, emptyStringForBooleanAttribute);\n        }\n        else {\n            wrap(this.element).removeAttribute(this.name);\n        }\n    }\n}\nclass EventPart extends AttributePart {\n    constructor(element, name, strings, parent, options) {\n        super(element, name, strings, parent, options);\n        this.type = EVENT_PART;\n        if (DEV_MODE && this.strings !== undefined) {\n            throw new Error(`A \\`<${element.localName}>\\` has a \\`@${name}=...\\` listener with ` +\n                'invalid content. Event listeners in templates must have exactly ' +\n                'one expression and no surrounding text.');\n        }\n    }\n    // EventPart does not use the base _$setValue/_resolveValue implementation\n    // since the dirty checking is more complex\n    /** @internal */\n    _$setValue(newListener, directiveParent = this) {\n        var _a;\n        newListener =\n            (_a = resolveDirective(this, newListener, directiveParent, 0)) !== null && _a !== void 0 ? _a : nothing;\n        if (newListener === noChange) {\n            return;\n        }\n        const oldListener = this._$committedValue;\n        // If the new value is nothing or any options change we have to remove the\n        // part as a listener.\n        const shouldRemoveListener = (newListener === nothing && oldListener !== nothing) ||\n            newListener.capture !==\n                oldListener.capture ||\n            newListener.once !==\n                oldListener.once ||\n            newListener.passive !==\n                oldListener.passive;\n        // If the new value is not nothing and we removed the listener, we have\n        // to add the part as a listener.\n        const shouldAddListener = newListener !== nothing &&\n            (oldListener === nothing || shouldRemoveListener);\n        debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n            kind: 'commit event listener',\n            element: this.element,\n            name: this.name,\n            value: newListener,\n            options: this.options,\n            removeListener: shouldRemoveListener,\n            addListener: shouldAddListener,\n            oldListener,\n        });\n        if (shouldRemoveListener) {\n            this.element.removeEventListener(this.name, this, oldListener);\n        }\n        if (shouldAddListener) {\n            // Beware: IE11 and Chrome 41 don't like using the listener as the\n            // options object. Figure out how to deal w/ this in IE11 - maybe\n            // patch addEventListener?\n            this.element.addEventListener(this.name, this, newListener);\n        }\n        this._$committedValue = newListener;\n    }\n    handleEvent(event) {\n        var _a, _b;\n        if (typeof this._$committedValue === 'function') {\n            this._$committedValue.call((_b = (_a = this.options) === null || _a === void 0 ? void 0 : _a.host) !== null && _b !== void 0 ? _b : this.element, event);\n        }\n        else {\n            this._$committedValue.handleEvent(event);\n        }\n    }\n}\nclass ElementPart {\n    constructor(element, parent, options) {\n        this.element = element;\n        this.type = ELEMENT_PART;\n        /** @internal */\n        this._$disconnectableChildren = undefined;\n        this._$parent = parent;\n        this.options = options;\n    }\n    // See comment in Disconnectable interface for why this is a getter\n    get _$isConnected() {\n        return this._$parent._$isConnected;\n    }\n    _$setValue(value) {\n        debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n            kind: 'commit to element binding',\n            element: this.element,\n            value,\n            options: this.options,\n        });\n        resolveDirective(this, value);\n    }\n}\n/**\n * END USERS SHOULD NOT RELY ON THIS OBJECT.\n *\n * Private exports for use by other Lit packages, not intended for use by\n * external users.\n *\n * We currently do not make a mangled rollup build of the lit-ssr code. In order\n * to keep a number of (otherwise private) top-level exports  mangled in the\n * client side code, we export a _$LH object containing those members (or\n * helper methods for accessing private fields of those members), and then\n * re-export them for use in lit-ssr. This keeps lit-ssr agnostic to whether the\n * client-side code is being used in `dev` mode or `prod` mode.\n *\n * This has a unique name, to disambiguate it from private exports in\n * lit-element, which re-exports all of lit-html.\n *\n * @private\n */\nexport const _$LH = {\n    // Used in lit-ssr\n    _boundAttributeSuffix: boundAttributeSuffix,\n    _marker: marker,\n    _markerMatch: markerMatch,\n    _HTML_RESULT: HTML_RESULT,\n    _getTemplateHtml: getTemplateHtml,\n    // Used in tests and private-ssr-support\n    _TemplateInstance: TemplateInstance,\n    _isIterable: isIterable,\n    _resolveDirective: resolveDirective,\n    _ChildPart: ChildPart,\n    _AttributePart: AttributePart,\n    _BooleanAttributePart: BooleanAttributePart,\n    _EventPart: EventPart,\n    _PropertyPart: PropertyPart,\n    _ElementPart: ElementPart,\n};\n// Apply polyfills if available\nconst polyfillSupport = DEV_MODE\n    ? global.litHtmlPolyfillSupportDevMode\n    : global.litHtmlPolyfillSupport;\npolyfillSupport === null || polyfillSupport === void 0 ? void 0 : polyfillSupport(Template, ChildPart);\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for lit-html usage.\n((_d = global.litHtmlVersions) !== null && _d !== void 0 ? _d : (global.litHtmlVersions = [])).push('2.8.0');\nif (DEV_MODE && global.litHtmlVersions.length > 1) {\n    issueWarning('multiple-versions', `Multiple versions of Lit loaded. ` +\n        `Loading multiple versions is not recommended.`);\n}\n/**\n * Renders a value, usually a lit-html TemplateResult, to the container.\n *\n * This example renders the text \"Hello, Zoe!\" inside a paragraph tag, appending\n * it to the container `document.body`.\n *\n * ```js\n * import {html, render} from 'lit';\n *\n * const name = \"Zoe\";\n * render(html`<p>Hello, ${name}!</p>`, document.body);\n * ```\n *\n * @param value Any [renderable\n *   value](https://lit.dev/docs/templates/expressions/#child-expressions),\n *   typically a {@linkcode TemplateResult} created by evaluating a template tag\n *   like {@linkcode html} or {@linkcode svg}.\n * @param container A DOM container to render to. The first render will append\n *   the rendered value to the container, and subsequent renders will\n *   efficiently update the rendered value if the same result type was\n *   previously rendered there.\n * @param options See {@linkcode RenderOptions} for options documentation.\n * @see\n * {@link https://lit.dev/docs/libraries/standalone-templates/#rendering-lit-html-templates| Rendering Lit HTML Templates}\n */\nexport const render = (value, container, options) => {\n    var _a, _b;\n    if (DEV_MODE && container == null) {\n        // Give a clearer error message than\n        //     Uncaught TypeError: Cannot read properties of null (reading\n        //     '_$litPart$')\n        // which reads like an internal Lit error.\n        throw new TypeError(`The container to render into may not be ${container}`);\n    }\n    const renderId = DEV_MODE ? debugLogRenderId++ : 0;\n    const partOwnerNode = (_a = options === null || options === void 0 ? void 0 : options.renderBefore) !== null && _a !== void 0 ? _a : container;\n    // This property needs to remain unminified.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let part = partOwnerNode['_$litPart$'];\n    debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n        kind: 'begin render',\n        id: renderId,\n        value,\n        container,\n        options,\n        part,\n    });\n    if (part === undefined) {\n        const endNode = (_b = options === null || options === void 0 ? void 0 : options.renderBefore) !== null && _b !== void 0 ? _b : null;\n        // This property needs to remain unminified.\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        partOwnerNode['_$litPart$'] = part = new ChildPart(container.insertBefore(createMarker(), endNode), endNode, undefined, options !== null && options !== void 0 ? options : {});\n    }\n    part._$setValue(value);\n    debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n        kind: 'end render',\n        id: renderId,\n        value,\n        container,\n        options,\n        part,\n    });\n    return part;\n};\nif (ENABLE_EXTRA_SECURITY_HOOKS) {\n    render.setSanitizer = setSanitizer;\n    render.createSanitizer = createSanitizer;\n    if (DEV_MODE) {\n        render._testOnlyClearSanitizerFactoryDoNotCallOrElse =\n            _testOnlyClearSanitizerFactoryDoNotCallOrElse;\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,IAAIA,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;AAClB,MAAMC,QAAQ,GAAG,IAAI;AACrB,MAAMC,2BAA2B,GAAG,IAAI;AACxC,MAAMC,uBAAuB,GAAG,IAAI;AACpC,MAAMC,SAAS,GAAG,KAAK;AACvB;AACA,MAAMC,MAAM,GAAGD,SAAS,GAAGE,UAAU,GAAGC,MAAM;AAC9C;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAGP,QAAQ,GACvBQ,KAAK,IAAK;EACT,MAAMC,UAAU,GAAGL,MAAM,CACpBM,qBAAqB;EAC1B,IAAI,CAACD,UAAU,EAAE;IACb;EACJ;EACAL,MAAM,CAACO,aAAa,CAAC,IAAIC,WAAW,CAAC,WAAW,EAAE;IAC9CC,MAAM,EAAEL;EACZ,CAAC,CAAC,CAAC;AACP,CAAC,GACCM,SAAS;AACf;AACA;AACA;AACA,IAAIC,gBAAgB,GAAG,CAAC;AACxB,IAAIC,YAAY;AAChB,IAAIhB,QAAQ,EAAE;EACV,CAACJ,EAAE,GAAGQ,MAAM,CAACa,iBAAiB,MAAM,IAAI,IAAIrB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAIQ,MAAM,CAACa,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAE;EACvG;EACAF,YAAY,GAAGA,CAACG,IAAI,EAAEC,OAAO,KAAK;IAC9BA,OAAO,IAAID,IAAI,GACR,4BAA2BA,IAAK,wBAAuB,GACxD,EAAE;IACR,IAAI,CAACf,MAAM,CAACa,iBAAiB,CAACI,GAAG,CAACD,OAAO,CAAC,EAAE;MACxCE,OAAO,CAACC,IAAI,CAACH,OAAO,CAAC;MACrBhB,MAAM,CAACa,iBAAiB,CAACO,GAAG,CAACJ,OAAO,CAAC;IACzC;EACJ,CAAC;EACDJ,YAAY,CAAC,UAAU,EAAG,qDAAoD,CAAC;AACnF;AACA,MAAMS,IAAI,GAAGvB,uBAAuB,KAC/B,CAACL,EAAE,GAAGO,MAAM,CAACsB,QAAQ,MAAM,IAAI,IAAI7B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC8B,KAAK,CAAC,IACtE,CAAC,CAAC7B,EAAE,GAAGM,MAAM,CAACsB,QAAQ,MAAM,IAAI,IAAI5B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC8B,OAAO,MAAM,IAAI,GAC/ExB,MAAM,CAACsB,QAAQ,CAACD,IAAI,GACnBI,IAAI,IAAKA,IAAI;AACpB,MAAMC,YAAY,GAAG1B,MAAM,CAAC0B,YAAY;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAGD,YAAY,GACrBA,YAAY,CAACE,YAAY,CAAC,UAAU,EAAE;EACpCC,UAAU,EAAGC,CAAC,IAAKA;AACvB,CAAC,CAAC,GACApB,SAAS;AACf,MAAMqB,gBAAgB,GAAIC,KAAK,IAAKA,KAAK;AACzC,MAAMC,aAAa,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,KAAKL,gBAAgB;AAC/D;AACA,MAAMM,YAAY,GAAIC,YAAY,IAAK;EACnC,IAAI,CAACzC,2BAA2B,EAAE;IAC9B;EACJ;EACA,IAAI0C,wBAAwB,KAAKN,aAAa,EAAE;IAC5C,MAAM,IAAIO,KAAK,CAAE,2DAA0D,GACtE,4DAA2D,CAAC;EACrE;EACAD,wBAAwB,GAAGD,YAAY;AAC3C,CAAC;AACD;AACA;AACA;AACA,MAAMG,6CAA6C,GAAGA,CAAA,KAAM;EACxDF,wBAAwB,GAAGN,aAAa;AAC5C,CAAC;AACD,MAAMS,eAAe,GAAGA,CAACjB,IAAI,EAAEkB,IAAI,EAAEC,IAAI,KAAK;EAC1C,OAAOL,wBAAwB,CAACd,IAAI,EAAEkB,IAAI,EAAEC,IAAI,CAAC;AACrD,CAAC;AACD;AACA;AACA,MAAMC,oBAAoB,GAAG,OAAO;AACpC;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAI,OAAMC,MAAM,CAACC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAE,GAAE;AACvD;AACA,MAAMC,WAAW,GAAG,GAAG,GAAGL,MAAM;AAChC;AACA;AACA,MAAMM,UAAU,GAAI,IAAGD,WAAY,GAAE;AACrC,MAAME,CAAC,GAAGtD,SAAS,IAAIC,MAAM,CAACsD,QAAQ,KAAK5C,SAAS,GAC9C;EACE6C,gBAAgBA,CAAA,EAAG;IACf,OAAO,CAAC,CAAC;EACb;AACJ,CAAC,GACCD,QAAQ;AACd;AACA,MAAME,YAAY,GAAGA,CAAA,KAAMH,CAAC,CAACI,aAAa,CAAC,EAAE,CAAC;AAC9C,MAAMC,WAAW,GAAI1B,KAAK,IAAKA,KAAK,KAAK,IAAI,IAAK,OAAOA,KAAK,IAAI,QAAQ,IAAI,OAAOA,KAAK,IAAI,UAAW;AACzG,MAAM2B,OAAO,GAAGC,KAAK,CAACD,OAAO;AAC7B,MAAME,UAAU,GAAI7B,KAAK,IAAK2B,OAAO,CAAC3B,KAAK,CAAC;AACxC;AACA,QAAQA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC8B,MAAM,CAACC,QAAQ,CAAC,CAAC,KAAK,UAAU;AAChG,MAAMC,UAAU,GAAI,aAAY;AAChC,MAAMC,eAAe,GAAI,qBAAoB;AAC7C,MAAMC,SAAS,GAAI,aAAY;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,qDAAqD;AAC1E,MAAMC,aAAa,GAAG,CAAC;AACvB,MAAMC,QAAQ,GAAG,CAAC;AAClB,MAAMC,gBAAgB,GAAG,CAAC;AAC1B,MAAMC,eAAe,GAAG,MAAM;AAC9B;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,IAAI;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAG,IAAIC,MAAM,CAAE,KAAIV,UAAW,OAAME,SAAU,MAAKF,UAAW,KAAIA,UAAW,OAAMC,eAAgB,cAAa,EAAE,GAAG,CAAC;AACvI,MAAMU,YAAY,GAAG,CAAC;AACtB,MAAMC,cAAc,GAAG,CAAC;AACxB,MAAMC,iBAAiB,GAAG,CAAC;AAC3B,MAAMC,UAAU,GAAG,CAAC;AACpB,MAAMC,uBAAuB,GAAG,IAAI;AACpC,MAAMC,uBAAuB,GAAG,IAAI;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAG,oCAAoC;AAC3D;AACA,MAAMC,WAAW,GAAG,CAAC;AACrB,MAAMC,UAAU,GAAG,CAAC;AACpB;AACA;AACA,MAAMC,cAAc,GAAG,CAAC;AACxB,MAAMC,UAAU,GAAG,CAAC;AACpB,MAAMC,aAAa,GAAG,CAAC;AACvB,MAAMC,sBAAsB,GAAG,CAAC;AAChC,MAAMC,UAAU,GAAG,CAAC;AACpB,MAAMC,YAAY,GAAG,CAAC;AACtB,MAAMC,YAAY,GAAG,CAAC;AACtB;AACA;AACA;AACA;AACA,MAAMC,GAAG,GAAI/C,IAAI,IAAK,CAACgD,OAAO,EAAE,GAAGC,MAAM,KAAK;EAC1C;EACA;EACA;EACA,IAAIjG,QAAQ,IAAIgG,OAAO,CAACE,IAAI,CAAEhE,CAAC,IAAKA,CAAC,KAAKpB,SAAS,CAAC,EAAE;IAClDQ,OAAO,CAACC,IAAI,CAAC,wCAAwC,GACjD,4DAA4D,CAAC;EACrE;EACA,OAAO;IACH;IACA,CAAC,YAAY,GAAGyB,IAAI;IACpBgD,OAAO;IACPC;EACJ,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,IAAI,GAAGJ,GAAG,CAACT,WAAW,CAAC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMc,GAAG,GAAGL,GAAG,CAACR,UAAU,CAAC;AAClC;AACA;AACA;AACA;AACA,OAAO,MAAMc,QAAQ,GAAGnC,MAAM,CAACoC,GAAG,CAAC,cAAc,CAAC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,OAAO,GAAGrC,MAAM,CAACoC,GAAG,CAAC,aAAa,CAAC;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,aAAa,GAAG,IAAIC,OAAO,CAAC,CAAC;AACnC,MAAMC,MAAM,GAAGjD,CAAC,CAACE,gBAAgB,CAACF,CAAC,EAAE,GAAG,CAAC,yCAAyC,IAAI,EAAE,KAAK,CAAC;AAC9F,IAAId,wBAAwB,GAAGN,aAAa;AAC5C,SAASsE,uBAAuBA,CAACC,GAAG,EAAEC,aAAa,EAAE;EACjD;EACA;EACA;EACA;EACA;EACA,IAAI,CAAC7C,KAAK,CAACD,OAAO,CAAC6C,GAAG,CAAC,IAAI,CAACA,GAAG,CAACE,cAAc,CAAC,KAAK,CAAC,EAAE;IACnD,IAAIC,OAAO,GAAG,gCAAgC;IAC9C,IAAI/G,QAAQ,EAAE;MACV+G,OAAO,GAAI;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,CACQC,IAAI,CAAC,CAAC,CACNC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;IAC/B;IACA,MAAM,IAAIrE,KAAK,CAACmE,OAAO,CAAC;EAC5B;EACA,OAAOhF,MAAM,KAAKjB,SAAS,GACrBiB,MAAM,CAACE,UAAU,CAAC4E,aAAa,CAAC,GAChCA,aAAa;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,eAAe,GAAGA,CAAClB,OAAO,EAAEhD,IAAI,KAAK;EACvC;EACA;EACA;EACA;EACA;EACA;EACA,MAAMmE,CAAC,GAAGnB,OAAO,CAACoB,MAAM,GAAG,CAAC;EAC5B;EACA;EACA;EACA,MAAMC,SAAS,GAAG,EAAE;EACpB,IAAIlB,IAAI,GAAGnD,IAAI,KAAKuC,UAAU,GAAG,OAAO,GAAG,EAAE;EAC7C;EACA;EACA;EACA,IAAI+B,eAAe;EACnB;EACA;EACA,IAAIC,KAAK,GAAGhD,YAAY;EACxB,KAAK,IAAIiD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,EAAEK,CAAC,EAAE,EAAE;IACxB,MAAMtF,CAAC,GAAG8D,OAAO,CAACwB,CAAC,CAAC;IACpB;IACA;IACA;IACA;IACA;IACA,IAAIC,gBAAgB,GAAG,CAAC,CAAC;IACzB,IAAIC,QAAQ;IACZ,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,KAAK;IACT;IACA;IACA,OAAOD,SAAS,GAAGzF,CAAC,CAACkF,MAAM,EAAE;MACzB;MACAG,KAAK,CAACI,SAAS,GAAGA,SAAS;MAC3BC,KAAK,GAAGL,KAAK,CAACM,IAAI,CAAC3F,CAAC,CAAC;MACrB,IAAI0F,KAAK,KAAK,IAAI,EAAE;QAChB;MACJ;MACAD,SAAS,GAAGJ,KAAK,CAACI,SAAS;MAC3B,IAAIJ,KAAK,KAAKhD,YAAY,EAAE;QACxB,IAAIqD,KAAK,CAACpD,aAAa,CAAC,KAAK,KAAK,EAAE;UAChC+C,KAAK,GAAG5C,eAAe;QAC3B,CAAC,MACI,IAAIiD,KAAK,CAACpD,aAAa,CAAC,KAAK1D,SAAS,EAAE;UACzC;UACAyG,KAAK,GAAG3C,gBAAgB;QAC5B,CAAC,MACI,IAAIgD,KAAK,CAACnD,QAAQ,CAAC,KAAK3D,SAAS,EAAE;UACpC,IAAIuE,cAAc,CAACyC,IAAI,CAACF,KAAK,CAACnD,QAAQ,CAAC,CAAC,EAAE;YACtC;YACA;YACA6C,eAAe,GAAG,IAAIxC,MAAM,CAAE,KAAI8C,KAAK,CAACnD,QAAQ,CAAE,EAAC,EAAE,GAAG,CAAC;UAC7D;UACA8C,KAAK,GAAG1C,WAAW;QACvB,CAAC,MACI,IAAI+C,KAAK,CAAClD,gBAAgB,CAAC,KAAK5D,SAAS,EAAE;UAC5C,IAAId,QAAQ,EAAE;YACV,MAAM,IAAI4C,KAAK,CAAC,gFAAgF,GAC5F,oEAAoE,CAAC;UAC7E;UACA2E,KAAK,GAAG1C,WAAW;QACvB;MACJ,CAAC,MACI,IAAI0C,KAAK,KAAK1C,WAAW,EAAE;QAC5B,IAAI+C,KAAK,CAAC7C,YAAY,CAAC,KAAK,GAAG,EAAE;UAC7B;UACA;UACAwC,KAAK,GAAGD,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAG/C,YAAY;UAC/F;UACA;UACAkD,gBAAgB,GAAG,CAAC,CAAC;QACzB,CAAC,MACI,IAAIG,KAAK,CAAC5C,cAAc,CAAC,KAAKlE,SAAS,EAAE;UAC1C;UACA2G,gBAAgB,GAAG,CAAC,CAAC;QACzB,CAAC,MACI;UACDA,gBAAgB,GAAGF,KAAK,CAACI,SAAS,GAAGC,KAAK,CAAC3C,iBAAiB,CAAC,CAACmC,MAAM;UACpEM,QAAQ,GAAGE,KAAK,CAAC5C,cAAc,CAAC;UAChCuC,KAAK,GACDK,KAAK,CAAC1C,UAAU,CAAC,KAAKpE,SAAS,GACzB+D,WAAW,GACX+C,KAAK,CAAC1C,UAAU,CAAC,KAAK,GAAG,GACrBE,uBAAuB,GACvBD,uBAAuB;QACzC;MACJ,CAAC,MACI,IAAIoC,KAAK,KAAKnC,uBAAuB,IACtCmC,KAAK,KAAKpC,uBAAuB,EAAE;QACnCoC,KAAK,GAAG1C,WAAW;MACvB,CAAC,MACI,IAAI0C,KAAK,KAAK5C,eAAe,IAAI4C,KAAK,KAAK3C,gBAAgB,EAAE;QAC9D2C,KAAK,GAAGhD,YAAY;MACxB,CAAC,MACI;QACD;QACA;QACAgD,KAAK,GAAG1C,WAAW;QACnByC,eAAe,GAAGxG,SAAS;MAC/B;IACJ;IACA,IAAId,QAAQ,EAAE;MACV;MACA;MACA;MACAsB,OAAO,CAACyG,MAAM,CAACN,gBAAgB,KAAK,CAAC,CAAC,IAClCF,KAAK,KAAK1C,WAAW,IACrB0C,KAAK,KAAKpC,uBAAuB,IACjCoC,KAAK,KAAKnC,uBAAuB,EAAE,0BAA0B,CAAC;IACtE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM4C,GAAG,GAAGT,KAAK,KAAK1C,WAAW,IAAImB,OAAO,CAACwB,CAAC,GAAG,CAAC,CAAC,CAACS,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;IAC/E9B,IAAI,IACAoB,KAAK,KAAKhD,YAAY,GAChBrC,CAAC,GAAGsB,UAAU,GACdiE,gBAAgB,IAAI,CAAC,GACjB,CAACJ,SAAS,CAACa,IAAI,CAACR,QAAQ,CAAC,EACvBxF,CAAC,CAACoB,KAAK,CAAC,CAAC,EAAEmE,gBAAgB,CAAC,GACxBxE,oBAAoB,GACpBf,CAAC,CAACoB,KAAK,CAACmE,gBAAgB,CAAC,IAC7BvE,MAAM,GACN8E,GAAG,GACL9F,CAAC,GACCgB,MAAM,IACLuE,gBAAgB,KAAK,CAAC,CAAC,IAAIJ,SAAS,CAACa,IAAI,CAACpH,SAAS,CAAC,EAAE0G,CAAC,IAAIQ,GAAG,CAAC;EACpF;EACA,MAAMG,UAAU,GAAGhC,IAAI,IAAIH,OAAO,CAACmB,CAAC,CAAC,IAAI,KAAK,CAAC,IAAInE,IAAI,KAAKuC,UAAU,GAAG,QAAQ,GAAG,EAAE,CAAC;EACvF;EACA,OAAO,CAACoB,uBAAuB,CAACX,OAAO,EAAEmC,UAAU,CAAC,EAAEd,SAAS,CAAC;AACpE,CAAC;AACD,MAAMe,QAAQ,CAAC;EACXC,WAAWA;EACX;EACA;IAAErC,OAAO;IAAE,CAAC,YAAY,GAAGhD;EAAK,CAAC,EAAEsF,OAAO,EAAE;IACxC,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI1G,IAAI;IACR,IAAI2G,SAAS,GAAG,CAAC;IACjB,IAAIC,aAAa,GAAG,CAAC;IACrB,MAAMC,SAAS,GAAG1C,OAAO,CAACoB,MAAM,GAAG,CAAC;IACpC,MAAMmB,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB;IACA,MAAM,CAACpC,IAAI,EAAEkB,SAAS,CAAC,GAAGH,eAAe,CAAClB,OAAO,EAAEhD,IAAI,CAAC;IACxD,IAAI,CAAC2F,EAAE,GAAGP,QAAQ,CAACQ,aAAa,CAACzC,IAAI,EAAEmC,OAAO,CAAC;IAC/C5B,MAAM,CAACmC,WAAW,GAAG,IAAI,CAACF,EAAE,CAACG,OAAO;IACpC;IACA,IAAI9F,IAAI,KAAKuC,UAAU,EAAE;MACrB,MAAMuD,OAAO,GAAG,IAAI,CAACH,EAAE,CAACG,OAAO;MAC/B,MAAMC,UAAU,GAAGD,OAAO,CAACE,UAAU;MACrCD,UAAU,CAACE,MAAM,CAAC,CAAC;MACnBH,OAAO,CAACI,MAAM,CAAC,GAAGH,UAAU,CAACI,UAAU,CAAC;IAC5C;IACA;IACA,OAAO,CAACtH,IAAI,GAAG6E,MAAM,CAAC0C,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAIb,KAAK,CAACnB,MAAM,GAAGsB,SAAS,EAAE;MACpE,IAAI7G,IAAI,CAACwH,QAAQ,KAAK,CAAC,EAAE;QACrB,IAAIrJ,QAAQ,EAAE;UACV,MAAM+F,GAAG,GAAGlE,IAAI,CAACyH,SAAS;UAC1B;UACA;UACA;UACA;UACA,IAAI,0BAA0B,CAACxB,IAAI,CAAC/B,GAAG,CAAC,IACpClE,IAAI,CAAC0H,SAAS,CAACC,QAAQ,CAACtG,MAAM,CAAC,EAAE;YACjC,MAAMuG,CAAC,GAAI,0CAAyC1D,GAAI,KAAI,GACvD,mDAAkDA,GAAI,YAAW,GACjE,cAAa;YAClB,IAAIA,GAAG,KAAK,UAAU,EAAE;cACpB,MAAM,IAAInD,KAAK,CAAC6G,CAAC,CAAC;YACtB,CAAC,MAEGzI,YAAY,CAAC,EAAE,EAAEyI,CAAC,CAAC;UAC3B;QACJ;QACA;QACA;QACA;QACA,IAAI5H,IAAI,CAAC6H,aAAa,CAAC,CAAC,EAAE;UACtB;UACA;UACA;UACA,MAAMC,aAAa,GAAG,EAAE;UACxB,KAAK,MAAM5G,IAAI,IAAIlB,IAAI,CAAC+H,iBAAiB,CAAC,CAAC,EAAE;YACzC;YACA;YACA;YACA;YACA;YACA;YACA;YACA,IAAI7G,IAAI,CAAC8G,QAAQ,CAAC5G,oBAAoB,CAAC,IACnCF,IAAI,CAACkF,UAAU,CAAC/E,MAAM,CAAC,EAAE;cACzB,MAAM4G,QAAQ,GAAGzC,SAAS,CAACoB,aAAa,EAAE,CAAC;cAC3CkB,aAAa,CAACzB,IAAI,CAACnF,IAAI,CAAC;cACxB,IAAI+G,QAAQ,KAAKhJ,SAAS,EAAE;gBACxB;gBACA,MAAMsB,KAAK,GAAGP,IAAI,CAACkI,YAAY,CAACD,QAAQ,CAACE,WAAW,CAAC,CAAC,GAAG/G,oBAAoB,CAAC;gBAC9E,MAAMgH,OAAO,GAAG7H,KAAK,CAAC8H,KAAK,CAAChH,MAAM,CAAC;gBACnC,MAAMuG,CAAC,GAAG,cAAc,CAAC5B,IAAI,CAACiC,QAAQ,CAAC;gBACvCvB,KAAK,CAACL,IAAI,CAAC;kBACPlF,IAAI,EAAEwC,cAAc;kBACpB2E,KAAK,EAAE3B,SAAS;kBAChBzF,IAAI,EAAE0G,CAAC,CAAC,CAAC,CAAC;kBACVzD,OAAO,EAAEiE,OAAO;kBAChBG,IAAI,EAAEX,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GACZY,YAAY,GACZZ,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GACRa,oBAAoB,GACpBb,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GACRc,SAAS,GACTC;gBAClB,CAAC,CAAC;cACN,CAAC,MACI;gBACDjC,KAAK,CAACL,IAAI,CAAC;kBACPlF,IAAI,EAAE6C,YAAY;kBAClBsE,KAAK,EAAE3B;gBACX,CAAC,CAAC;cACN;YACJ;UACJ;UACA,KAAK,MAAMzF,IAAI,IAAI4G,aAAa,EAAE;YAC9B9H,IAAI,CAAC4I,eAAe,CAAC1H,IAAI,CAAC;UAC9B;QACJ;QACA;QACA;QACA,IAAIsC,cAAc,CAACyC,IAAI,CAACjG,IAAI,CAAC6I,OAAO,CAAC,EAAE;UACnC;UACA;UACA;UACA,MAAM1E,OAAO,GAAGnE,IAAI,CAAC8I,WAAW,CAACT,KAAK,CAAChH,MAAM,CAAC;UAC9C,MAAMyE,SAAS,GAAG3B,OAAO,CAACoB,MAAM,GAAG,CAAC;UACpC,IAAIO,SAAS,GAAG,CAAC,EAAE;YACf9F,IAAI,CAAC8I,WAAW,GAAG7I,YAAY,GACzBA,YAAY,CAAC8I,WAAW,GACxB,EAAE;YACR;YACA;YACA;YACA;YACA;YACA,KAAK,IAAIpD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,SAAS,EAAEH,CAAC,EAAE,EAAE;cAChC3F,IAAI,CAACqH,MAAM,CAAClD,OAAO,CAACwB,CAAC,CAAC,EAAE5D,YAAY,CAAC,CAAC,CAAC;cACvC;cACA8C,MAAM,CAAC0C,QAAQ,CAAC,CAAC;cACjBb,KAAK,CAACL,IAAI,CAAC;gBAAElF,IAAI,EAAEyC,UAAU;gBAAE0E,KAAK,EAAE,EAAE3B;cAAU,CAAC,CAAC;YACxD;YACA;YACA;YACA;YACA3G,IAAI,CAACqH,MAAM,CAAClD,OAAO,CAAC2B,SAAS,CAAC,EAAE/D,YAAY,CAAC,CAAC,CAAC;UACnD;QACJ;MACJ,CAAC,MACI,IAAI/B,IAAI,CAACwH,QAAQ,KAAK,CAAC,EAAE;QAC1B,MAAMwB,IAAI,GAAGhJ,IAAI,CAACgJ,IAAI;QACtB,IAAIA,IAAI,KAAKtH,WAAW,EAAE;UACtBgF,KAAK,CAACL,IAAI,CAAC;YAAElF,IAAI,EAAEyC,UAAU;YAAE0E,KAAK,EAAE3B;UAAU,CAAC,CAAC;QACtD,CAAC,MACI;UACD,IAAIhB,CAAC,GAAG,CAAC,CAAC;UACV,OAAO,CAACA,CAAC,GAAG3F,IAAI,CAACgJ,IAAI,CAACC,OAAO,CAAC5H,MAAM,EAAEsE,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE;YAClD;YACA;YACAe,KAAK,CAACL,IAAI,CAAC;cAAElF,IAAI,EAAE8C,YAAY;cAAEqE,KAAK,EAAE3B;YAAU,CAAC,CAAC;YACpD;YACAhB,CAAC,IAAItE,MAAM,CAACkE,MAAM,GAAG,CAAC;UAC1B;QACJ;MACJ;MACAoB,SAAS,EAAE;IACf;IACA;IACA;IACA;IACAjI,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;MACxEwK,IAAI,EAAE,eAAe;MACrBC,QAAQ,EAAE,IAAI;MACdC,gBAAgB,EAAE,IAAI,CAACtC,EAAE;MACzBJ,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBvC;IACJ,CAAC,CAAC;EACN;EACA;EACA;EACA,OAAO4C,aAAaA,CAACzC,IAAI,EAAE+E,QAAQ,EAAE;IACjC,MAAMvC,EAAE,GAAGlF,CAAC,CAACmF,aAAa,CAAC,UAAU,CAAC;IACtCD,EAAE,CAACY,SAAS,GAAGpD,IAAI;IACnB,OAAOwC,EAAE;EACb;AACJ;AACA,SAASwC,gBAAgBA,CAACC,IAAI,EAAEhJ,KAAK,EAAEiJ,MAAM,GAAGD,IAAI,EAAEE,cAAc,EAAE;EAClE,IAAI1L,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACd,IAAIC,EAAE;EACN;EACA;EACA,IAAIqC,KAAK,KAAKiE,QAAQ,EAAE;IACpB,OAAOjE,KAAK;EAChB;EACA,IAAImJ,gBAAgB,GAAGD,cAAc,KAAKxK,SAAS,GAC7C,CAAClB,EAAE,GAAGyL,MAAM,CAACG,YAAY,MAAM,IAAI,IAAI5L,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0L,cAAc,CAAC,GAClFD,MAAM,CAACI,WAAW;EACxB,MAAMC,wBAAwB,GAAG5H,WAAW,CAAC1B,KAAK,CAAC,GAC7CtB,SAAS;EACT;EACEsB,KAAK,CAAC,iBAAiB,CAAC;EAChC,IAAI,CAACmJ,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAAClD,WAAW,MAAMqD,wBAAwB,EAAE;IACjI;IACA,CAAC7L,EAAE,GAAG0L,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAAC,oCAAoC,CAAC,MAAM,IAAI,IAAI1L,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC8L,IAAI,CAACJ,gBAAgB,EAAE,KAAK,CAAC;IACvM,IAAIG,wBAAwB,KAAK5K,SAAS,EAAE;MACxCyK,gBAAgB,GAAGzK,SAAS;IAChC,CAAC,MACI;MACDyK,gBAAgB,GAAG,IAAIG,wBAAwB,CAACN,IAAI,CAAC;MACrDG,gBAAgB,CAACK,YAAY,CAACR,IAAI,EAAEC,MAAM,EAAEC,cAAc,CAAC;IAC/D;IACA,IAAIA,cAAc,KAAKxK,SAAS,EAAE;MAC9B,CAAC,CAAChB,EAAE,GAAG,CAACC,EAAE,GAAGsL,MAAM,EAAEG,YAAY,MAAM,IAAI,IAAI1L,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAIC,EAAE,CAACyL,YAAY,GAAG,EAAG,EAAEF,cAAc,CAAC,GACvGC,gBAAgB;IACxB,CAAC,MACI;MACDF,MAAM,CAACI,WAAW,GAAGF,gBAAgB;IACzC;EACJ;EACA,IAAIA,gBAAgB,KAAKzK,SAAS,EAAE;IAChCsB,KAAK,GAAG+I,gBAAgB,CAACC,IAAI,EAAEG,gBAAgB,CAACM,SAAS,CAACT,IAAI,EAAEhJ,KAAK,CAAC6D,MAAM,CAAC,EAAEsF,gBAAgB,EAAED,cAAc,CAAC;EACpH;EACA,OAAOlJ,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA,MAAM0J,gBAAgB,CAAC;EACnBzD,WAAWA,CAAC2C,QAAQ,EAAEK,MAAM,EAAE;IAC1B,IAAI,CAACU,OAAO,GAAG,EAAE;IACjB;IACA,IAAI,CAACC,wBAAwB,GAAGlL,SAAS;IACzC,IAAI,CAACmL,UAAU,GAAGjB,QAAQ;IAC1B,IAAI,CAACkB,QAAQ,GAAGb,MAAM;EAC1B;EACA;EACA,IAAIc,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACD,QAAQ,CAACC,UAAU;EACnC;EACA;EACA,IAAIC,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACF,QAAQ,CAACE,aAAa;EACtC;EACA;EACA;EACAC,MAAMA,CAAC/D,OAAO,EAAE;IACZ,IAAI1I,EAAE;IACN,MAAM;MAAE+I,EAAE,EAAE;QAAEG;MAAQ,CAAC;MAAEP,KAAK,EAAEA;IAAO,CAAC,GAAG,IAAI,CAAC0D,UAAU;IAC1D,MAAMK,QAAQ,GAAG,CAAC,CAAC1M,EAAE,GAAG0I,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACiE,aAAa,MAAM,IAAI,IAAI3M,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG6D,CAAC,EAAE+I,UAAU,CAAC1D,OAAO,EAAE,IAAI,CAAC;IAC9JpC,MAAM,CAACmC,WAAW,GAAGyD,QAAQ;IAC7B,IAAIzK,IAAI,GAAG6E,MAAM,CAAC0C,QAAQ,CAAC,CAAC;IAC5B,IAAIZ,SAAS,GAAG,CAAC;IACjB,IAAIiE,SAAS,GAAG,CAAC;IACjB,IAAIC,YAAY,GAAGnE,KAAK,CAAC,CAAC,CAAC;IAC3B,OAAOmE,YAAY,KAAK5L,SAAS,EAAE;MAC/B,IAAI0H,SAAS,KAAKkE,YAAY,CAACvC,KAAK,EAAE;QAClC,IAAIiB,IAAI;QACR,IAAIsB,YAAY,CAAC1J,IAAI,KAAKyC,UAAU,EAAE;UAClC2F,IAAI,GAAG,IAAIuB,SAAS,CAAC9K,IAAI,EAAEA,IAAI,CAAC+K,WAAW,EAAE,IAAI,EAAEtE,OAAO,CAAC;QAC/D,CAAC,MACI,IAAIoE,YAAY,CAAC1J,IAAI,KAAKwC,cAAc,EAAE;UAC3C4F,IAAI,GAAG,IAAIsB,YAAY,CAACtC,IAAI,CAACvI,IAAI,EAAE6K,YAAY,CAAC3J,IAAI,EAAE2J,YAAY,CAAC1G,OAAO,EAAE,IAAI,EAAEsC,OAAO,CAAC;QAC9F,CAAC,MACI,IAAIoE,YAAY,CAAC1J,IAAI,KAAK6C,YAAY,EAAE;UACzCuF,IAAI,GAAG,IAAIyB,WAAW,CAAChL,IAAI,EAAE,IAAI,EAAEyG,OAAO,CAAC;QAC/C;QACA,IAAI,CAACyD,OAAO,CAAC7D,IAAI,CAACkD,IAAI,CAAC;QACvBsB,YAAY,GAAGnE,KAAK,CAAC,EAAEkE,SAAS,CAAC;MACrC;MACA,IAAIjE,SAAS,MAAMkE,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACvC,KAAK,CAAC,EAAE;QAChGtI,IAAI,GAAG6E,MAAM,CAAC0C,QAAQ,CAAC,CAAC;QACxBZ,SAAS,EAAE;MACf;IACJ;IACA;IACA;IACA;IACA9B,MAAM,CAACmC,WAAW,GAAGpF,CAAC;IACtB,OAAO6I,QAAQ;EACnB;EACAQ,OAAOA,CAAC7G,MAAM,EAAE;IACZ,IAAIuB,CAAC,GAAG,CAAC;IACT,KAAK,MAAM4D,IAAI,IAAI,IAAI,CAACW,OAAO,EAAE;MAC7B,IAAIX,IAAI,KAAKtK,SAAS,EAAE;QACpBP,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;UACxEwK,IAAI,EAAE,UAAU;UAChBK,IAAI;UACJhJ,KAAK,EAAE6D,MAAM,CAACuB,CAAC,CAAC;UAChBuF,UAAU,EAAEvF,CAAC;UACbvB,MAAM;UACN+G,gBAAgB,EAAE;QACtB,CAAC,CAAC;QACF,IAAI5B,IAAI,CAACpF,OAAO,KAAKlF,SAAS,EAAE;UAC5BsK,IAAI,CAAC6B,UAAU,CAAChH,MAAM,EAAEmF,IAAI,EAAE5D,CAAC,CAAC;UAChC;UACA;UACA;UACAA,CAAC,IAAI4D,IAAI,CAACpF,OAAO,CAACoB,MAAM,GAAG,CAAC;QAChC,CAAC,MACI;UACDgE,IAAI,CAAC6B,UAAU,CAAChH,MAAM,CAACuB,CAAC,CAAC,CAAC;QAC9B;MACJ;MACAA,CAAC,EAAE;IACP;EACJ;AACJ;AACA,MAAMmF,SAAS,CAAC;EACZtE,WAAWA,CAAC6E,SAAS,EAAEC,OAAO,EAAE9B,MAAM,EAAE/C,OAAO,EAAE;IAC7C,IAAI1I,EAAE;IACN,IAAI,CAACoD,IAAI,GAAGyC,UAAU;IACtB,IAAI,CAAC2H,gBAAgB,GAAG7G,OAAO;IAC/B;IACA;IACA;IACA,IAAI,CAACyF,wBAAwB,GAAGlL,SAAS;IACzC,IAAI,CAACuM,WAAW,GAAGH,SAAS;IAC5B,IAAI,CAACI,SAAS,GAAGH,OAAO;IACxB,IAAI,CAACjB,QAAQ,GAAGb,MAAM;IACtB,IAAI,CAAC/C,OAAO,GAAGA,OAAO;IACtB;IACA;IACA;IACA,IAAI,CAACiF,aAAa,GAAG,CAAC3N,EAAE,GAAG0I,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACkF,WAAW,MAAM,IAAI,IAAI5N,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;IACvI,IAAIK,2BAA2B,EAAE;MAC7B;MACA,IAAI,CAACwN,cAAc,GAAG3M,SAAS;IACnC;EACJ;EACA;EACA,IAAIsL,aAAaA,CAAA,EAAG;IAChB,IAAIxM,EAAE,EAAEC,EAAE;IACV;IACA;IACA;IACA,OAAO,CAACA,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAACsM,QAAQ,MAAM,IAAI,IAAItM,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwM,aAAa,MAAM,IAAI,IAAIvM,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAAC0N,aAAa;EAChJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIpB,UAAUA,CAAA,EAAG;IACb,IAAIA,UAAU,GAAG1K,IAAI,CAAC,IAAI,CAAC4L,WAAW,CAAC,CAAClB,UAAU;IAClD,MAAMd,MAAM,GAAG,IAAI,CAACa,QAAQ;IAC5B,IAAIb,MAAM,KAAKvK,SAAS,IACpB,CAACqL,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC9C,QAAQ,MAAM,EAAE,CAAC,8BAA8B;MACnH;MACA;MACA;MACA8C,UAAU,GAAGd,MAAM,CAACc,UAAU;IAClC;IACA,OAAOA,UAAU;EACrB;EACA;AACJ;AACA;AACA;EACI,IAAIe,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACG,WAAW;EAC3B;EACA;AACJ;AACA;AACA;EACI,IAAIF,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACG,SAAS;EACzB;EACAL,UAAUA,CAAC7K,KAAK,EAAEsL,eAAe,GAAG,IAAI,EAAE;IACtC,IAAI9N,EAAE;IACN,IAAII,QAAQ,IAAI,IAAI,CAACmM,UAAU,KAAK,IAAI,EAAE;MACtC,MAAM,IAAIvJ,KAAK,CAAE,0UAAyU,CAAC;IAC/V;IACAR,KAAK,GAAG+I,gBAAgB,CAAC,IAAI,EAAE/I,KAAK,EAAEsL,eAAe,CAAC;IACtD,IAAI5J,WAAW,CAAC1B,KAAK,CAAC,EAAE;MACpB;MACA;MACA;MACA,IAAIA,KAAK,KAAKmE,OAAO,IAAInE,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAK,EAAE,EAAE;QACpD,IAAI,IAAI,CAACgL,gBAAgB,KAAK7G,OAAO,EAAE;UACnChG,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;YACxEwK,IAAI,EAAE,yBAAyB;YAC/B4C,KAAK,EAAE,IAAI,CAACN,WAAW;YACvBrF,GAAG,EAAE,IAAI,CAACsF,SAAS;YACnBjC,MAAM,EAAE,IAAI,CAACa,QAAQ;YACrB5D,OAAO,EAAE,IAAI,CAACA;UAClB,CAAC,CAAC;UACF,IAAI,CAACsF,OAAO,CAAC,CAAC;QAClB;QACA,IAAI,CAACR,gBAAgB,GAAG7G,OAAO;MACnC,CAAC,MACI,IAAInE,KAAK,KAAK,IAAI,CAACgL,gBAAgB,IAAIhL,KAAK,KAAKiE,QAAQ,EAAE;QAC5D,IAAI,CAACwH,WAAW,CAACzL,KAAK,CAAC;MAC3B;MACA;IACJ,CAAC,MACI,IAAIA,KAAK,CAAC,YAAY,CAAC,KAAKtB,SAAS,EAAE;MACxC,IAAI,CAACgN,qBAAqB,CAAC1L,KAAK,CAAC;IACrC,CAAC,MACI,IAAIA,KAAK,CAACiH,QAAQ,KAAKvI,SAAS,EAAE;MACnC,IAAId,QAAQ,IAAI,CAAC,CAACJ,EAAE,GAAG,IAAI,CAAC0I,OAAO,MAAM,IAAI,IAAI1I,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmO,IAAI,MAAM3L,KAAK,EAAE;QAC1F,IAAI,CAACyL,WAAW,CAAE,0DAAyD,GACtE,qDAAoD,CAAC;QAC1DvM,OAAO,CAACC,IAAI,CAAE,uCAAsC,EAAEa,KAAK,EAAG,kEAAiE,EAAG,4DAA2D,EAAG,kEAAiE,EAAG,2CAA0C,CAAC;QAC/S;MACJ;MACA,IAAI,CAAC4L,WAAW,CAAC5L,KAAK,CAAC;IAC3B,CAAC,MACI,IAAI6B,UAAU,CAAC7B,KAAK,CAAC,EAAE;MACxB,IAAI,CAAC6L,eAAe,CAAC7L,KAAK,CAAC;IAC/B,CAAC,MACI;MACD;MACA,IAAI,CAACyL,WAAW,CAACzL,KAAK,CAAC;IAC3B;EACJ;EACA8L,OAAOA,CAACrM,IAAI,EAAE;IACV,OAAOJ,IAAI,CAACA,IAAI,CAAC,IAAI,CAAC4L,WAAW,CAAC,CAAClB,UAAU,CAAC,CAACgC,YAAY,CAACtM,IAAI,EAAE,IAAI,CAACyL,SAAS,CAAC;EACrF;EACAU,WAAWA,CAAC5L,KAAK,EAAE;IACf,IAAIxC,EAAE;IACN,IAAI,IAAI,CAACwN,gBAAgB,KAAKhL,KAAK,EAAE;MACjC,IAAI,CAACwL,OAAO,CAAC,CAAC;MACd,IAAI3N,2BAA2B,IAC3B0C,wBAAwB,KAAKN,aAAa,EAAE;QAC5C,MAAM+L,cAAc,GAAG,CAACxO,EAAE,GAAG,IAAI,CAACyN,WAAW,CAAClB,UAAU,MAAM,IAAI,IAAIvM,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyO,QAAQ;QAC1G,IAAID,cAAc,KAAK,OAAO,IAAIA,cAAc,KAAK,QAAQ,EAAE;UAC3D,IAAIrH,OAAO,GAAG,WAAW;UACzB,IAAI/G,QAAQ,EAAE;YACV,IAAIoO,cAAc,KAAK,OAAO,EAAE;cAC5BrH,OAAO,GACF,mDAAkD,GAC9C,0DAAyD,GACzD,iCAAgC,GAChC,6CAA4C,GAC5C,sDAAqD,GACrD,2CAA0C,GAC1C,kDAAiD;YAC9D,CAAC,MACI;cACDA,OAAO,GACF,oDAAmD,GAC/C,uDAAsD,GACtD,iBAAgB;YAC7B;UACJ;UACA,MAAM,IAAInE,KAAK,CAACmE,OAAO,CAAC;QAC5B;MACJ;MACAxG,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;QACxEwK,IAAI,EAAE,aAAa;QACnB4C,KAAK,EAAE,IAAI,CAACN,WAAW;QACvBhC,MAAM,EAAE,IAAI,CAACa,QAAQ;QACrB9J,KAAK,EAAEA,KAAK;QACZkG,OAAO,EAAE,IAAI,CAACA;MAClB,CAAC,CAAC;MACF,IAAI,CAAC8E,gBAAgB,GAAG,IAAI,CAACc,OAAO,CAAC9L,KAAK,CAAC;IAC/C;EACJ;EACAyL,WAAWA,CAACzL,KAAK,EAAE;IACf;IACA;IACA;IACA,IAAI,IAAI,CAACgL,gBAAgB,KAAK7G,OAAO,IACjCzC,WAAW,CAAC,IAAI,CAACsJ,gBAAgB,CAAC,EAAE;MACpC,MAAMvL,IAAI,GAAGJ,IAAI,CAAC,IAAI,CAAC4L,WAAW,CAAC,CAACT,WAAW;MAC/C,IAAI3M,2BAA2B,EAAE;QAC7B,IAAI,IAAI,CAACwN,cAAc,KAAK3M,SAAS,EAAE;UACnC,IAAI,CAAC2M,cAAc,GAAG3K,eAAe,CAACjB,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC;QACnE;QACAO,KAAK,GAAG,IAAI,CAACqL,cAAc,CAACrL,KAAK,CAAC;MACtC;MACA7B,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;QACxEwK,IAAI,EAAE,aAAa;QACnBlJ,IAAI;QACJO,KAAK;QACLkG,OAAO,EAAE,IAAI,CAACA;MAClB,CAAC,CAAC;MACFzG,IAAI,CAACgJ,IAAI,GAAGzI,KAAK;IACrB,CAAC,MACI;MACD,IAAInC,2BAA2B,EAAE;QAC7B,MAAMqO,QAAQ,GAAG7K,CAAC,CAAC8K,cAAc,CAAC,EAAE,CAAC;QACrC,IAAI,CAACP,WAAW,CAACM,QAAQ,CAAC;QAC1B;QACA;QACA;QACA;QACA,IAAI,IAAI,CAACb,cAAc,KAAK3M,SAAS,EAAE;UACnC,IAAI,CAAC2M,cAAc,GAAG3K,eAAe,CAACwL,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC;QACvE;QACAlM,KAAK,GAAG,IAAI,CAACqL,cAAc,CAACrL,KAAK,CAAC;QAClC7B,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;UACxEwK,IAAI,EAAE,aAAa;UACnBlJ,IAAI,EAAEyM,QAAQ;UACdlM,KAAK;UACLkG,OAAO,EAAE,IAAI,CAACA;QAClB,CAAC,CAAC;QACFgG,QAAQ,CAACzD,IAAI,GAAGzI,KAAK;MACzB,CAAC,MACI;QACD,IAAI,CAAC4L,WAAW,CAACvK,CAAC,CAAC8K,cAAc,CAACnM,KAAK,CAAC,CAAC;QACzC7B,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;UACxEwK,IAAI,EAAE,aAAa;UACnBlJ,IAAI,EAAEJ,IAAI,CAAC,IAAI,CAAC4L,WAAW,CAAC,CAACT,WAAW;UACxCxK,KAAK;UACLkG,OAAO,EAAE,IAAI,CAACA;QAClB,CAAC,CAAC;MACN;IACJ;IACA,IAAI,CAAC8E,gBAAgB,GAAGhL,KAAK;EACjC;EACA0L,qBAAqBA,CAACU,MAAM,EAAE;IAC1B,IAAI5O,EAAE;IACN;IACA,MAAM;MAAEqG,MAAM;MAAE,CAAC,YAAY,GAAGjD;IAAK,CAAC,GAAGwL,MAAM;IAC/C;IACA;IACA;IACA;IACA,MAAMxD,QAAQ,GAAG,OAAOhI,IAAI,KAAK,QAAQ,GACnC,IAAI,CAACyL,aAAa,CAACD,MAAM,CAAC,IACzBxL,IAAI,CAAC2F,EAAE,KAAK7H,SAAS,KACnBkC,IAAI,CAAC2F,EAAE,GAAGP,QAAQ,CAACQ,aAAa,CAACjC,uBAAuB,CAAC3D,IAAI,CAAC0L,CAAC,EAAE1L,IAAI,CAAC0L,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACpG,OAAO,CAAC,CAAC,EAC5FtF,IAAI,CAAC;IACb,IAAI,CAAC,CAACpD,EAAE,GAAG,IAAI,CAACwN,gBAAgB,MAAM,IAAI,IAAIxN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqM,UAAU,MAAMjB,QAAQ,EAAE;MAChGzK,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;QACxEwK,IAAI,EAAE,mBAAmB;QACzBC,QAAQ;QACR2D,QAAQ,EAAE,IAAI,CAACvB,gBAAgB;QAC/B7E,KAAK,EAAE,IAAI,CAAC6E,gBAAgB,CAACrB,OAAO;QACpCzD,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBrC;MACJ,CAAC,CAAC;MACF,IAAI,CAACmH,gBAAgB,CAACN,OAAO,CAAC7G,MAAM,CAAC;IACzC,CAAC,MACI;MACD,MAAM0I,QAAQ,GAAG,IAAI7C,gBAAgB,CAACd,QAAQ,EAAE,IAAI,CAAC;MACrD,MAAMsB,QAAQ,GAAGqC,QAAQ,CAACtC,MAAM,CAAC,IAAI,CAAC/D,OAAO,CAAC;MAC9C/H,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;QACxEwK,IAAI,EAAE,uBAAuB;QAC7BC,QAAQ;QACR2D,QAAQ;QACRpG,KAAK,EAAEoG,QAAQ,CAAC5C,OAAO;QACvBzD,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBgE,QAAQ;QACRrG;MACJ,CAAC,CAAC;MACF0I,QAAQ,CAAC7B,OAAO,CAAC7G,MAAM,CAAC;MACxB1F,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;QACxEwK,IAAI,EAAE,mCAAmC;QACzCC,QAAQ;QACR2D,QAAQ;QACRpG,KAAK,EAAEoG,QAAQ,CAAC5C,OAAO;QACvBzD,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBgE,QAAQ;QACRrG;MACJ,CAAC,CAAC;MACF,IAAI,CAAC+H,WAAW,CAAC1B,QAAQ,CAAC;MAC1B,IAAI,CAACc,gBAAgB,GAAGuB,QAAQ;IACpC;EACJ;EACA;EACA;EACAF,aAAaA,CAACD,MAAM,EAAE;IAClB,IAAIxD,QAAQ,GAAGxE,aAAa,CAACoI,GAAG,CAACJ,MAAM,CAACxI,OAAO,CAAC;IAChD,IAAIgF,QAAQ,KAAKlK,SAAS,EAAE;MACxB0F,aAAa,CAACqI,GAAG,CAACL,MAAM,CAACxI,OAAO,EAAGgF,QAAQ,GAAG,IAAI5C,QAAQ,CAACoG,MAAM,CAAE,CAAC;IACxE;IACA,OAAOxD,QAAQ;EACnB;EACAiD,eAAeA,CAAC7L,KAAK,EAAE;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC2B,OAAO,CAAC,IAAI,CAACqJ,gBAAgB,CAAC,EAAE;MACjC,IAAI,CAACA,gBAAgB,GAAG,EAAE;MAC1B,IAAI,CAACQ,OAAO,CAAC,CAAC;IAClB;IACA;IACA;IACA,MAAMkB,SAAS,GAAG,IAAI,CAAC1B,gBAAgB;IACvC,IAAIX,SAAS,GAAG,CAAC;IACjB,IAAIsC,QAAQ;IACZ,KAAK,MAAMC,IAAI,IAAI5M,KAAK,EAAE;MACtB,IAAIqK,SAAS,KAAKqC,SAAS,CAAC1H,MAAM,EAAE;QAChC;QACA;QACA;QACA;QACA0H,SAAS,CAAC5G,IAAI,CAAE6G,QAAQ,GAAG,IAAIpC,SAAS,CAAC,IAAI,CAACuB,OAAO,CAACtK,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,CAACsK,OAAO,CAACtK,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC0E,OAAO,CAAE,CAAC;MAC9H,CAAC,MACI;QACD;QACAyG,QAAQ,GAAGD,SAAS,CAACrC,SAAS,CAAC;MACnC;MACAsC,QAAQ,CAAC9B,UAAU,CAAC+B,IAAI,CAAC;MACzBvC,SAAS,EAAE;IACf;IACA,IAAIA,SAAS,GAAGqC,SAAS,CAAC1H,MAAM,EAAE;MAC9B;MACA,IAAI,CAACwG,OAAO,CAACmB,QAAQ,IAAItN,IAAI,CAACsN,QAAQ,CAACzB,SAAS,CAAC,CAACV,WAAW,EAAEH,SAAS,CAAC;MACzE;MACAqC,SAAS,CAAC1H,MAAM,GAAGqF,SAAS;IAChC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACImB,OAAOA,CAACD,KAAK,GAAGlM,IAAI,CAAC,IAAI,CAAC4L,WAAW,CAAC,CAACT,WAAW,EAAEqC,IAAI,EAAE;IACtD,IAAIrP,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAACsP,yBAAyB,MAAM,IAAI,IAAItP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+L,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAEsD,IAAI,CAAC;IAC3G,OAAOtB,KAAK,IAAIA,KAAK,KAAK,IAAI,CAACL,SAAS,EAAE;MACtC,MAAM6B,CAAC,GAAG1N,IAAI,CAACkM,KAAK,CAAC,CAACf,WAAW;MACjCnL,IAAI,CAACkM,KAAK,CAAC,CAAC1E,MAAM,CAAC,CAAC;MACpB0E,KAAK,GAAGwB,CAAC;IACb;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,YAAYA,CAAC5B,WAAW,EAAE;IACtB,IAAI5N,EAAE;IACN,IAAI,IAAI,CAACsM,QAAQ,KAAKpL,SAAS,EAAE;MAC7B,IAAI,CAACyM,aAAa,GAAGC,WAAW;MAChC,CAAC5N,EAAE,GAAG,IAAI,CAACsP,yBAAyB,MAAM,IAAI,IAAItP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+L,IAAI,CAAC,IAAI,EAAE6B,WAAW,CAAC;IACzG,CAAC,MACI,IAAIxN,QAAQ,EAAE;MACf,MAAM,IAAI4C,KAAK,CAAC,8CAA8C,GAC1D,kCAAkC,CAAC;IAC3C;EACJ;AACJ;AACA,MAAM4H,aAAa,CAAC;EAChBnC,WAAWA,CAACgH,OAAO,EAAEtM,IAAI,EAAEiD,OAAO,EAAEqF,MAAM,EAAE/C,OAAO,EAAE;IACjD,IAAI,CAACtF,IAAI,GAAGwC,cAAc;IAC1B;IACA,IAAI,CAAC4H,gBAAgB,GAAG7G,OAAO;IAC/B;IACA,IAAI,CAACyF,wBAAwB,GAAGlL,SAAS;IACzC,IAAI,CAACuO,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACtM,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACmJ,QAAQ,GAAGb,MAAM;IACtB,IAAI,CAAC/C,OAAO,GAAGA,OAAO;IACtB,IAAItC,OAAO,CAACoB,MAAM,GAAG,CAAC,IAAIpB,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,IAAIA,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;MAC9D,IAAI,CAACoH,gBAAgB,GAAG,IAAIpJ,KAAK,CAACgC,OAAO,CAACoB,MAAM,GAAG,CAAC,CAAC,CAACkI,IAAI,CAAC,IAAInM,MAAM,CAAC,CAAC,CAAC;MACxE,IAAI,CAAC6C,OAAO,GAAGA,OAAO;IAC1B,CAAC,MACI;MACD,IAAI,CAACoH,gBAAgB,GAAG7G,OAAO;IACnC;IACA,IAAItG,2BAA2B,EAAE;MAC7B,IAAI,CAACsP,UAAU,GAAGzO,SAAS;IAC/B;EACJ;EACA,IAAI4J,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC2E,OAAO,CAAC3E,OAAO;EAC/B;EACA;EACA,IAAI0B,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACF,QAAQ,CAACE,aAAa;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIa,UAAUA,CAAC7K,KAAK,EAAEsL,eAAe,GAAG,IAAI,EAAEX,UAAU,EAAEyC,QAAQ,EAAE;IAC5D,MAAMxJ,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B;IACA,IAAIyJ,MAAM,GAAG,KAAK;IAClB,IAAIzJ,OAAO,KAAKlF,SAAS,EAAE;MACvB;MACAsB,KAAK,GAAG+I,gBAAgB,CAAC,IAAI,EAAE/I,KAAK,EAAEsL,eAAe,EAAE,CAAC,CAAC;MACzD+B,MAAM,GACF,CAAC3L,WAAW,CAAC1B,KAAK,CAAC,IACdA,KAAK,KAAK,IAAI,CAACgL,gBAAgB,IAAIhL,KAAK,KAAKiE,QAAS;MAC/D,IAAIoJ,MAAM,EAAE;QACR,IAAI,CAACrC,gBAAgB,GAAGhL,KAAK;MACjC;IACJ,CAAC,MACI;MACD;MACA,MAAM6D,MAAM,GAAG7D,KAAK;MACpBA,KAAK,GAAG4D,OAAO,CAAC,CAAC,CAAC;MAClB,IAAIwB,CAAC,EAAEkI,CAAC;MACR,KAAKlI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,OAAO,CAACoB,MAAM,GAAG,CAAC,EAAEI,CAAC,EAAE,EAAE;QACrCkI,CAAC,GAAGvE,gBAAgB,CAAC,IAAI,EAAElF,MAAM,CAAC8G,UAAU,GAAGvF,CAAC,CAAC,EAAEkG,eAAe,EAAElG,CAAC,CAAC;QACtE,IAAIkI,CAAC,KAAKrJ,QAAQ,EAAE;UAChB;UACAqJ,CAAC,GAAG,IAAI,CAACtC,gBAAgB,CAAC5F,CAAC,CAAC;QAChC;QACAiI,MAAM,KAAKA,MAAM,GAAG,CAAC3L,WAAW,CAAC4L,CAAC,CAAC,IAAIA,CAAC,KAAK,IAAI,CAACtC,gBAAgB,CAAC5F,CAAC,CAAC,CAAC;QACtE,IAAIkI,CAAC,KAAKnJ,OAAO,EAAE;UACfnE,KAAK,GAAGmE,OAAO;QACnB,CAAC,MACI,IAAInE,KAAK,KAAKmE,OAAO,EAAE;UACxBnE,KAAK,IAAI,CAACsN,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAGA,CAAC,GAAG,EAAE,IAAI1J,OAAO,CAACwB,CAAC,GAAG,CAAC,CAAC;QACnE;QACA;QACA;QACA,IAAI,CAAC4F,gBAAgB,CAAC5F,CAAC,CAAC,GAAGkI,CAAC;MAChC;IACJ;IACA,IAAID,MAAM,IAAI,CAACD,QAAQ,EAAE;MACrB,IAAI,CAACG,YAAY,CAACvN,KAAK,CAAC;IAC5B;EACJ;EACA;EACAuN,YAAYA,CAACvN,KAAK,EAAE;IAChB,IAAIA,KAAK,KAAKmE,OAAO,EAAE;MACnB9E,IAAI,CAAC,IAAI,CAAC4N,OAAO,CAAC,CAAC5E,eAAe,CAAC,IAAI,CAAC1H,IAAI,CAAC;IACjD,CAAC,MACI;MACD,IAAI9C,2BAA2B,EAAE;QAC7B,IAAI,IAAI,CAACsP,UAAU,KAAKzO,SAAS,EAAE;UAC/B,IAAI,CAACyO,UAAU,GAAG5M,wBAAwB,CAAC,IAAI,CAAC0M,OAAO,EAAE,IAAI,CAACtM,IAAI,EAAE,WAAW,CAAC;QACpF;QACAX,KAAK,GAAG,IAAI,CAACmN,UAAU,CAACnN,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE,CAAC;MAC5E;MACA7B,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;QACxEwK,IAAI,EAAE,kBAAkB;QACxBsE,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBtM,IAAI,EAAE,IAAI,CAACA,IAAI;QACfX,KAAK;QACLkG,OAAO,EAAE,IAAI,CAACA;MAClB,CAAC,CAAC;MACF7G,IAAI,CAAC,IAAI,CAAC4N,OAAO,CAAC,CAACO,YAAY,CAAC,IAAI,CAAC7M,IAAI,EAAGX,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAG,CAAC;IACjG;EACJ;AACJ;AACA,MAAMiI,YAAY,SAASG,aAAa,CAAC;EACrCnC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGwH,SAAS,CAAC;IACnB,IAAI,CAAC7M,IAAI,GAAG0C,aAAa;EAC7B;EACA;EACAiK,YAAYA,CAACvN,KAAK,EAAE;IAChB,IAAInC,2BAA2B,EAAE;MAC7B,IAAI,IAAI,CAACsP,UAAU,KAAKzO,SAAS,EAAE;QAC/B,IAAI,CAACyO,UAAU,GAAG5M,wBAAwB,CAAC,IAAI,CAAC0M,OAAO,EAAE,IAAI,CAACtM,IAAI,EAAE,UAAU,CAAC;MACnF;MACAX,KAAK,GAAG,IAAI,CAACmN,UAAU,CAACnN,KAAK,CAAC;IAClC;IACA7B,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;MACxEwK,IAAI,EAAE,iBAAiB;MACvBsE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBtM,IAAI,EAAE,IAAI,CAACA,IAAI;MACfX,KAAK;MACLkG,OAAO,EAAE,IAAI,CAACA;IAClB,CAAC,CAAC;IACF;IACA,IAAI,CAAC+G,OAAO,CAAC,IAAI,CAACtM,IAAI,CAAC,GAAGX,KAAK,KAAKmE,OAAO,GAAGzF,SAAS,GAAGsB,KAAK;EACnE;AACJ;AACA;AACA;AACA;AACA;AACA,MAAM0N,8BAA8B,GAAGhO,YAAY,GAC7CA,YAAY,CAAC8I,WAAW,GACxB,EAAE;AACR,MAAMN,oBAAoB,SAASE,aAAa,CAAC;EAC7CnC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGwH,SAAS,CAAC;IACnB,IAAI,CAAC7M,IAAI,GAAG2C,sBAAsB;EACtC;EACA;EACAgK,YAAYA,CAACvN,KAAK,EAAE;IAChB7B,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;MACxEwK,IAAI,EAAE,0BAA0B;MAChCsE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBtM,IAAI,EAAE,IAAI,CAACA,IAAI;MACfX,KAAK,EAAE,CAAC,EAAEA,KAAK,IAAIA,KAAK,KAAKmE,OAAO,CAAC;MACrC+B,OAAO,EAAE,IAAI,CAACA;IAClB,CAAC,CAAC;IACF,IAAIlG,KAAK,IAAIA,KAAK,KAAKmE,OAAO,EAAE;MAC5B9E,IAAI,CAAC,IAAI,CAAC4N,OAAO,CAAC,CAACO,YAAY,CAAC,IAAI,CAAC7M,IAAI,EAAE+M,8BAA8B,CAAC;IAC9E,CAAC,MACI;MACDrO,IAAI,CAAC,IAAI,CAAC4N,OAAO,CAAC,CAAC5E,eAAe,CAAC,IAAI,CAAC1H,IAAI,CAAC;IACjD;EACJ;AACJ;AACA,MAAMwH,SAAS,SAASC,aAAa,CAAC;EAClCnC,WAAWA,CAACgH,OAAO,EAAEtM,IAAI,EAAEiD,OAAO,EAAEqF,MAAM,EAAE/C,OAAO,EAAE;IACjD,KAAK,CAAC+G,OAAO,EAAEtM,IAAI,EAAEiD,OAAO,EAAEqF,MAAM,EAAE/C,OAAO,CAAC;IAC9C,IAAI,CAACtF,IAAI,GAAG4C,UAAU;IACtB,IAAI5F,QAAQ,IAAI,IAAI,CAACgG,OAAO,KAAKlF,SAAS,EAAE;MACxC,MAAM,IAAI8B,KAAK,CAAE,QAAOyM,OAAO,CAAC/F,SAAU,gBAAevG,IAAK,uBAAsB,GAChF,kEAAkE,GAClE,yCAAyC,CAAC;IAClD;EACJ;EACA;EACA;EACA;EACAkK,UAAUA,CAAC8C,WAAW,EAAErC,eAAe,GAAG,IAAI,EAAE;IAC5C,IAAI9N,EAAE;IACNmQ,WAAW,GACP,CAACnQ,EAAE,GAAGuL,gBAAgB,CAAC,IAAI,EAAE4E,WAAW,EAAErC,eAAe,EAAE,CAAC,CAAC,MAAM,IAAI,IAAI9N,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG2G,OAAO;IAC3G,IAAIwJ,WAAW,KAAK1J,QAAQ,EAAE;MAC1B;IACJ;IACA,MAAM2J,WAAW,GAAG,IAAI,CAAC5C,gBAAgB;IACzC;IACA;IACA,MAAM6C,oBAAoB,GAAIF,WAAW,KAAKxJ,OAAO,IAAIyJ,WAAW,KAAKzJ,OAAO,IAC5EwJ,WAAW,CAACG,OAAO,KACfF,WAAW,CAACE,OAAO,IACvBH,WAAW,CAACI,IAAI,KACZH,WAAW,CAACG,IAAI,IACpBJ,WAAW,CAACK,OAAO,KACfJ,WAAW,CAACI,OAAO;IAC3B;IACA;IACA,MAAMC,iBAAiB,GAAGN,WAAW,KAAKxJ,OAAO,KAC5CyJ,WAAW,KAAKzJ,OAAO,IAAI0J,oBAAoB,CAAC;IACrD1P,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;MACxEwK,IAAI,EAAE,uBAAuB;MAC7BsE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBtM,IAAI,EAAE,IAAI,CAACA,IAAI;MACfX,KAAK,EAAE2N,WAAW;MAClBzH,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBgI,cAAc,EAAEL,oBAAoB;MACpCM,WAAW,EAAEF,iBAAiB;MAC9BL;IACJ,CAAC,CAAC;IACF,IAAIC,oBAAoB,EAAE;MACtB,IAAI,CAACZ,OAAO,CAACmB,mBAAmB,CAAC,IAAI,CAACzN,IAAI,EAAE,IAAI,EAAEiN,WAAW,CAAC;IAClE;IACA,IAAIK,iBAAiB,EAAE;MACnB;MACA;MACA;MACA,IAAI,CAAChB,OAAO,CAACoB,gBAAgB,CAAC,IAAI,CAAC1N,IAAI,EAAE,IAAI,EAAEgN,WAAW,CAAC;IAC/D;IACA,IAAI,CAAC3C,gBAAgB,GAAG2C,WAAW;EACvC;EACAW,WAAWA,CAAClQ,KAAK,EAAE;IACf,IAAIZ,EAAE,EAAEC,EAAE;IACV,IAAI,OAAO,IAAI,CAACuN,gBAAgB,KAAK,UAAU,EAAE;MAC7C,IAAI,CAACA,gBAAgB,CAACzB,IAAI,CAAC,CAAC9L,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAAC0I,OAAO,MAAM,IAAI,IAAI1I,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmO,IAAI,MAAM,IAAI,IAAIlO,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAACwP,OAAO,EAAE7O,KAAK,CAAC;IAC5J,CAAC,MACI;MACD,IAAI,CAAC4M,gBAAgB,CAACsD,WAAW,CAAClQ,KAAK,CAAC;IAC5C;EACJ;AACJ;AACA,MAAMqM,WAAW,CAAC;EACdxE,WAAWA,CAACgH,OAAO,EAAEhE,MAAM,EAAE/C,OAAO,EAAE;IAClC,IAAI,CAAC+G,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACrM,IAAI,GAAG6C,YAAY;IACxB;IACA,IAAI,CAACmG,wBAAwB,GAAGlL,SAAS;IACzC,IAAI,CAACoL,QAAQ,GAAGb,MAAM;IACtB,IAAI,CAAC/C,OAAO,GAAGA,OAAO;EAC1B;EACA;EACA,IAAI8D,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACF,QAAQ,CAACE,aAAa;EACtC;EACAa,UAAUA,CAAC7K,KAAK,EAAE;IACd7B,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;MACxEwK,IAAI,EAAE,2BAA2B;MACjCsE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBjN,KAAK;MACLkG,OAAO,EAAE,IAAI,CAACA;IAClB,CAAC,CAAC;IACF6C,gBAAgB,CAAC,IAAI,EAAE/I,KAAK,CAAC;EACjC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMuO,IAAI,GAAG;EAChB;EACAC,qBAAqB,EAAE3N,oBAAoB;EAC3C4N,OAAO,EAAE3N,MAAM;EACf4N,YAAY,EAAEvN,WAAW;EACzBwN,YAAY,EAAEzL,WAAW;EACzB0L,gBAAgB,EAAE9J,eAAe;EACjC;EACA+J,iBAAiB,EAAEnF,gBAAgB;EACnCoF,WAAW,EAAEjN,UAAU;EACvBkN,iBAAiB,EAAEhG,gBAAgB;EACnCiG,UAAU,EAAEzE,SAAS;EACrB0E,cAAc,EAAE7G,aAAa;EAC7B8G,qBAAqB,EAAEhH,oBAAoB;EAC3CiH,UAAU,EAAEhH,SAAS;EACrBiH,aAAa,EAAEnH,YAAY;EAC3BoH,YAAY,EAAE5E;AAClB,CAAC;AACD;AACA,MAAM6E,eAAe,GAAG1R,QAAQ,GAC1BI,MAAM,CAACuR,6BAA6B,GACpCvR,MAAM,CAACwR,sBAAsB;AACnCF,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACtJ,QAAQ,EAAEuE,SAAS,CAAC;AACtG;AACA;AACA,CAAC,CAAC5M,EAAE,GAAGK,MAAM,CAACyR,eAAe,MAAM,IAAI,IAAI9R,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAIK,MAAM,CAACyR,eAAe,GAAG,EAAG,EAAE3J,IAAI,CAAC,OAAO,CAAC;AAC5G,IAAIlI,QAAQ,IAAII,MAAM,CAACyR,eAAe,CAACzK,MAAM,GAAG,CAAC,EAAE;EAC/CpG,YAAY,CAAC,mBAAmB,EAAG,mCAAkC,GAChE,+CAA8C,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM8Q,MAAM,GAAGA,CAAC1P,KAAK,EAAE2P,SAAS,EAAEzJ,OAAO,KAAK;EACjD,IAAI1I,EAAE,EAAEC,EAAE;EACV,IAAIG,QAAQ,IAAI+R,SAAS,IAAI,IAAI,EAAE;IAC/B;IACA;IACA;IACA;IACA,MAAM,IAAIC,SAAS,CAAE,2CAA0CD,SAAU,EAAC,CAAC;EAC/E;EACA,MAAME,QAAQ,GAAGjS,QAAQ,GAAGe,gBAAgB,EAAE,GAAG,CAAC;EAClD,MAAMmR,aAAa,GAAG,CAACtS,EAAE,GAAG0I,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC6J,YAAY,MAAM,IAAI,IAAIvS,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGmS,SAAS;EAC9I;EACA;EACA,IAAI3G,IAAI,GAAG8G,aAAa,CAAC,YAAY,CAAC;EACtC3R,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;IACxEwK,IAAI,EAAE,cAAc;IACpBqH,EAAE,EAAEH,QAAQ;IACZ7P,KAAK;IACL2P,SAAS;IACTzJ,OAAO;IACP8C;EACJ,CAAC,CAAC;EACF,IAAIA,IAAI,KAAKtK,SAAS,EAAE;IACpB,MAAMqM,OAAO,GAAG,CAACtN,EAAE,GAAGyI,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC6J,YAAY,MAAM,IAAI,IAAItS,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;IACnI;IACA;IACAqS,aAAa,CAAC,YAAY,CAAC,GAAG9G,IAAI,GAAG,IAAIuB,SAAS,CAACoF,SAAS,CAAC5D,YAAY,CAACvK,YAAY,CAAC,CAAC,EAAEuJ,OAAO,CAAC,EAAEA,OAAO,EAAErM,SAAS,EAAEwH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAG,CAAC,CAAC,CAAC;EAClL;EACA8C,IAAI,CAAC6B,UAAU,CAAC7K,KAAK,CAAC;EACtB7B,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;IACxEwK,IAAI,EAAE,YAAY;IAClBqH,EAAE,EAAEH,QAAQ;IACZ7P,KAAK;IACL2P,SAAS;IACTzJ,OAAO;IACP8C;EACJ,CAAC,CAAC;EACF,OAAOA,IAAI;AACf,CAAC;AACD,IAAInL,2BAA2B,EAAE;EAC7B6R,MAAM,CAACrP,YAAY,GAAGA,YAAY;EAClCqP,MAAM,CAAChP,eAAe,GAAGA,eAAe;EACxC,IAAI9C,QAAQ,EAAE;IACV8R,MAAM,CAACjP,6CAA6C,GAChDA,6CAA6C;EACrD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}