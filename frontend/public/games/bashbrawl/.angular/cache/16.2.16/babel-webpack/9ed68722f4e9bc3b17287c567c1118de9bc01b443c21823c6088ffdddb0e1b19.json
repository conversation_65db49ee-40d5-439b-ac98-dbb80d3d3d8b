{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { listenForAttributeChange as t } from \"../utils/events.js\";\nimport { getActiveElement as s } from \"../utils/focus.js\";\nfunction e(t = {\n  focus: !0\n}) {\n  return s => s.addInitializer(s => new r(s, t));\n}\nclass r {\n  constructor(t, e = {\n    focus: !0\n  }) {\n    this.host = t, this.config = e, this.activeElement = s(), this.host.addController(this);\n  }\n  get current() {\n    return this.host.trigger ? this._current : this.activeElement;\n  }\n  get prev() {\n    return this._prev;\n  }\n  hostConnected() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.observer = t(_this.host, \"hidden\", () => {\n        const t = s();\n        !_this.host.hidden && t ? _this.activeElement = t : _this.focusCurrent();\n      });\n    })();\n  }\n  hostUpdate() {\n    this._current !== this.host.trigger && (this._prev = this._current, this._current = this.host.trigger ? this.host.trigger : this.activeElement);\n  }\n  hostDisconnected() {\n    this.focusCurrent(), this.observer?.disconnect();\n  }\n  focusCurrent() {\n    this.config.focus && this.current?.focus();\n  }\n}\nexport { r as TriggerController, e as triggerable };", "map": {"version": 3, "names": ["listenForAttributeChange", "t", "getActiveElement", "s", "e", "focus", "addInitializer", "r", "constructor", "host", "config", "activeElement", "addController", "current", "trigger", "_current", "prev", "_prev", "hostConnected", "_this", "_asyncToGenerator", "observer", "hidden", "focusCurrent", "hostUpdate", "hostDisconnected", "disconnect", "TriggerController", "triggerable"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/trigger.controller.js"], "sourcesContent": ["import{listenForAttributeChange as t}from\"../utils/events.js\";import{getActiveElement as s}from\"../utils/focus.js\";function e(t={focus:!0}){return s=>s.addInitializer((s=>new r(s,t)))}class r{constructor(t,e={focus:!0}){this.host=t,this.config=e,this.activeElement=s(),this.host.addController(this)}get current(){return this.host.trigger?this._current:this.activeElement}get prev(){return this._prev}async hostConnected(){this.observer=t(this.host,\"hidden\",(()=>{const t=s();!this.host.hidden&&t?this.activeElement=t:this.focusCurrent()}))}hostUpdate(){this._current!==this.host.trigger&&(this._prev=this._current,this._current=this.host.trigger?this.host.trigger:this.activeElement)}hostDisconnected(){this.focusCurrent(),this.observer?.disconnect()}focusCurrent(){this.config.focus&&this.current?.focus()}}export{r as TriggerController,e as triggerable};\n"], "mappings": ";AAAA,SAAOA,wBAAwB,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAASC,CAACA,CAACH,CAAC,GAAC;EAACI,KAAK,EAAC,CAAC;AAAC,CAAC,EAAC;EAAC,OAAOF,CAAC,IAAEA,CAAC,CAACG,cAAc,CAAEH,CAAC,IAAE,IAAII,CAAC,CAACJ,CAAC,EAACF,CAAC,CAAE,CAAC;AAAA;AAAC,MAAMM,CAAC;EAACC,WAAWA,CAACP,CAAC,EAACG,CAAC,GAAC;IAACC,KAAK,EAAC,CAAC;EAAC,CAAC,EAAC;IAAC,IAAI,CAACI,IAAI,GAACR,CAAC,EAAC,IAAI,CAACS,MAAM,GAACN,CAAC,EAAC,IAAI,CAACO,aAAa,GAACR,CAAC,CAAC,CAAC,EAAC,IAAI,CAACM,IAAI,CAACG,aAAa,CAAC,IAAI,CAAC;EAAA;EAAC,IAAIC,OAAOA,CAAA,EAAE;IAAC,OAAO,IAAI,CAACJ,IAAI,CAACK,OAAO,GAAC,IAAI,CAACC,QAAQ,GAAC,IAAI,CAACJ,aAAa;EAAA;EAAC,IAAIK,IAAIA,CAAA,EAAE;IAAC,OAAO,IAAI,CAACC,KAAK;EAAA;EAAOC,aAAaA,CAAA,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAACD,KAAI,CAACE,QAAQ,GAACpB,CAAC,CAACkB,KAAI,CAACV,IAAI,EAAC,QAAQ,EAAE,MAAI;QAAC,MAAMR,CAAC,GAACE,CAAC,CAAC,CAAC;QAAC,CAACgB,KAAI,CAACV,IAAI,CAACa,MAAM,IAAErB,CAAC,GAACkB,KAAI,CAACR,aAAa,GAACV,CAAC,GAACkB,KAAI,CAACI,YAAY,CAAC,CAAC;MAAA,CAAE,CAAC;IAAA;EAAA;EAACC,UAAUA,CAAA,EAAE;IAAC,IAAI,CAACT,QAAQ,KAAG,IAAI,CAACN,IAAI,CAACK,OAAO,KAAG,IAAI,CAACG,KAAK,GAAC,IAAI,CAACF,QAAQ,EAAC,IAAI,CAACA,QAAQ,GAAC,IAAI,CAACN,IAAI,CAACK,OAAO,GAAC,IAAI,CAACL,IAAI,CAACK,OAAO,GAAC,IAAI,CAACH,aAAa,CAAC;EAAA;EAACc,gBAAgBA,CAAA,EAAE;IAAC,IAAI,CAACF,YAAY,CAAC,CAAC,EAAC,IAAI,CAACF,QAAQ,EAAEK,UAAU,CAAC,CAAC;EAAA;EAACH,YAAYA,CAAA,EAAE;IAAC,IAAI,CAACb,MAAM,CAACL,KAAK,IAAE,IAAI,CAACQ,OAAO,EAAER,KAAK,CAAC,CAAC;EAAA;AAAC;AAAC,SAAOE,CAAC,IAAIoB,iBAAiB,EAACvB,CAAC,IAAIwB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}