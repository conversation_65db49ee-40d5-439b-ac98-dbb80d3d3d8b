{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"uninstall\",\n  H = [\"uninstall\", C({\n    outline: '<path d=\"M12.2529 27.71C12.453 27.91 12.7031 28 12.9632 28C13.2233 28 13.4734 27.9 13.6735 27.71L17.965 23.42L22.2565 27.71C22.4566 27.91 22.7067 28 22.9668 28C23.2269 28 23.477 27.9 23.677 27.71C24.0672 27.32 24.0672 26.69 23.677 26.3L19.3855 22.01L23.677 17.72C24.0672 17.33 24.0672 16.7 23.677 16.31C23.2869 15.92 22.6567 15.92 22.2665 16.31L17.975 20.6L13.6835 16.31C13.2933 15.92 12.6631 15.92 12.273 16.31C11.8828 16.7 11.8828 17.33 12.273 17.72L16.5645 22.01L12.273 26.3C11.8828 26.69 11.8828 27.32 12.273 27.71H12.2529ZM31.3998 10.56C31.0096 10.18 30.4795 9.98002 29.9393 10H26.498C25.9478 10 25.4977 10.45 25.4977 11C25.4977 11.55 25.9478 12 26.498 12H30.0193V32H5.99071V12H9.36192C9.91211 12 10.3623 11.55 10.3623 11C10.3623 10.45 9.92212 10 9.36192 10H6.07074C5.53055 9.98002 5.00036 10.18 4.61022 10.56C4.22008 10.94 4 11.46 4 12V32C4 32.54 4.22008 33.07 4.61022 33.44C5.00036 33.82 5.53055 34.02 6.07074 34H29.9293C30.4695 34.02 30.9996 33.82 31.3898 33.44C31.7799 33.06 32 32.54 32 32V12C32 11.46 31.7799 10.93 31.3898 10.56H31.3998Z\"/>',\n    outlineAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M30.0193 15.0367H32V32C32 32.54 31.7799 33.06 31.3898 33.44C30.9996 33.82 30.4695 34.02 29.9293 34H6.07074C5.53055 34.02 5.00036 33.82 4.61022 33.44C4.22008 33.07 4 32.54 4 32V12C4 11.46 4.22008 10.94 4.61022 10.56C5.00036 10.18 5.53055 9.98002 6.07074 10H9.36192C9.92212 10 10.3623 10.45 10.3623 11C10.3623 11.55 9.91211 12 9.36192 12H5.99071V32H30.0193V15.0367Z\"/><path d=\"M12.9632 28C12.7031 28 12.453 27.91 12.2529 27.71H12.273C11.8828 27.32 11.8828 26.69 12.273 26.3L16.5645 22.01L12.273 17.72C11.8828 17.33 11.8828 16.7 12.273 16.31C12.6631 15.92 13.2933 15.92 13.6835 16.31L17.975 20.6L22.2665 16.31C22.6567 15.92 23.2869 15.92 23.677 16.31C24.0672 16.7 24.0672 17.33 23.677 17.72L19.3855 22.01L23.677 26.3C24.0672 26.69 24.0672 27.32 23.677 27.71C23.477 27.9 23.2269 28 22.9668 28C22.7067 28 22.4566 27.91 22.2565 27.71L17.965 23.42L13.6735 27.71C13.4734 27.9 13.2233 28 12.9632 28Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M30.0193 13V32H5.99071V12H9.36192C9.91211 12 10.3623 11.55 10.3623 11C10.3623 10.45 9.92212 10 9.36192 10H6.07074C5.53055 9.98002 5.00036 10.18 4.61022 10.56C4.22008 10.94 4 11.46 4 12V32C4 32.54 4.22008 33.07 4.61022 33.44C5.00036 33.82 5.53055 34.02 6.07074 34H29.9293C30.4695 34.02 30.9996 33.82 31.3898 33.44C31.7799 33.06 32 32.54 32 32V12.7131C31.372 12.8989 30.7073 12.999 30.0193 13Z\"/><path d=\"M12.9632 28C12.7031 28 12.453 27.91 12.2529 27.71H12.273C11.8828 27.32 11.8828 26.69 12.273 26.3L16.5645 22.01L12.273 17.72C11.8828 17.33 11.8828 16.7 12.273 16.31C12.6631 15.92 13.2933 15.92 13.6835 16.31L17.975 20.6L22.2665 16.31C22.6567 15.92 23.2869 15.92 23.677 16.31C24.0672 16.7 24.0672 17.33 23.677 17.72L19.3855 22.01L23.677 26.3C24.0672 26.69 24.0672 27.32 23.677 27.71C23.477 27.9 23.2269 28 22.9668 28C22.7067 28 22.4566 27.91 22.2565 27.71L17.965 23.42L13.6735 27.71C13.4734 27.9 13.2233 28 12.9632 28Z\"/>'\n  })];\nexport { H as uninstallIcon, L as uninstallIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "H", "outline", "outlineAlerted", "outlineBadged", "uninstallIcon", "uninstallIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/uninstall.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"uninstall\",H=[\"uninstall\",C({outline:'<path d=\"M12.2529 27.71C12.453 27.91 12.7031 28 12.9632 28C13.2233 28 13.4734 27.9 13.6735 27.71L17.965 23.42L22.2565 27.71C22.4566 27.91 22.7067 28 22.9668 28C23.2269 28 23.477 27.9 23.677 27.71C24.0672 27.32 24.0672 26.69 23.677 26.3L19.3855 22.01L23.677 17.72C24.0672 17.33 24.0672 16.7 23.677 16.31C23.2869 15.92 22.6567 15.92 22.2665 16.31L17.975 20.6L13.6835 16.31C13.2933 15.92 12.6631 15.92 12.273 16.31C11.8828 16.7 11.8828 17.33 12.273 17.72L16.5645 22.01L12.273 26.3C11.8828 26.69 11.8828 27.32 12.273 27.71H12.2529ZM31.3998 10.56C31.0096 10.18 30.4795 9.98002 29.9393 10H26.498C25.9478 10 25.4977 10.45 25.4977 11C25.4977 11.55 25.9478 12 26.498 12H30.0193V32H5.99071V12H9.36192C9.91211 12 10.3623 11.55 10.3623 11C10.3623 10.45 9.92212 10 9.36192 10H6.07074C5.53055 9.98002 5.00036 10.18 4.61022 10.56C4.22008 10.94 4 11.46 4 12V32C4 32.54 4.22008 33.07 4.61022 33.44C5.00036 33.82 5.53055 34.02 6.07074 34H29.9293C30.4695 34.02 30.9996 33.82 31.3898 33.44C31.7799 33.06 32 32.54 32 32V12C32 11.46 31.7799 10.93 31.3898 10.56H31.3998Z\"/>',outlineAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M30.0193 15.0367H32V32C32 32.54 31.7799 33.06 31.3898 33.44C30.9996 33.82 30.4695 34.02 29.9293 34H6.07074C5.53055 34.02 5.00036 33.82 4.61022 33.44C4.22008 33.07 4 32.54 4 32V12C4 11.46 4.22008 10.94 4.61022 10.56C5.00036 10.18 5.53055 9.98002 6.07074 10H9.36192C9.92212 10 10.3623 10.45 10.3623 11C10.3623 11.55 9.91211 12 9.36192 12H5.99071V32H30.0193V15.0367Z\"/><path d=\"M12.9632 28C12.7031 28 12.453 27.91 12.2529 27.71H12.273C11.8828 27.32 11.8828 26.69 12.273 26.3L16.5645 22.01L12.273 17.72C11.8828 17.33 11.8828 16.7 12.273 16.31C12.6631 15.92 13.2933 15.92 13.6835 16.31L17.975 20.6L22.2665 16.31C22.6567 15.92 23.2869 15.92 23.677 16.31C24.0672 16.7 24.0672 17.33 23.677 17.72L19.3855 22.01L23.677 26.3C24.0672 26.69 24.0672 27.32 23.677 27.71C23.477 27.9 23.2269 28 22.9668 28C22.7067 28 22.4566 27.91 22.2565 27.71L17.965 23.42L13.6735 27.71C13.4734 27.9 13.2233 28 12.9632 28Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M30.0193 13V32H5.99071V12H9.36192C9.91211 12 10.3623 11.55 10.3623 11C10.3623 10.45 9.92212 10 9.36192 10H6.07074C5.53055 9.98002 5.00036 10.18 4.61022 10.56C4.22008 10.94 4 11.46 4 12V32C4 32.54 4.22008 33.07 4.61022 33.44C5.00036 33.82 5.53055 34.02 6.07074 34H29.9293C30.4695 34.02 30.9996 33.82 31.3898 33.44C31.7799 33.06 32 32.54 32 32V12.7131C31.372 12.8989 30.7073 12.999 30.0193 13Z\"/><path d=\"M12.9632 28C12.7031 28 12.453 27.91 12.2529 27.71H12.273C11.8828 27.32 11.8828 26.69 12.273 26.3L16.5645 22.01L12.273 17.72C11.8828 17.33 11.8828 16.7 12.273 16.31C12.6631 15.92 13.2933 15.92 13.6835 16.31L17.975 20.6L22.2665 16.31C22.6567 15.92 23.2869 15.92 23.677 16.31C24.0672 16.7 24.0672 17.33 23.677 17.72L19.3855 22.01L23.677 26.3C24.0672 26.69 24.0672 27.32 23.677 27.71C23.477 27.9 23.2269 28 22.9668 28C22.7067 28 22.4566 27.91 22.2565 27.71L17.965 23.42L13.6735 27.71C13.4734 27.9 13.2233 28 12.9632 28Z\"/>'})];export{H as uninstallIcon,L as uninstallIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,WAAW;EAACC,CAAC,GAAC,CAAC,WAAW,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,4hCAA4hC;IAACC,cAAc,EAAC,2uCAA2uC;IAACC,aAAa,EAAC;EAAkiC,CAAC,CAAC,CAAC;AAAC,SAAOH,CAAC,IAAII,aAAa,EAACL,CAAC,IAAIM,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}