{"ast": null, "code": "import { renderIcon as H } from \"../icon.renderer.js\";\nconst V = \"tick-chart\",\n  C = [\"tick-chart\", H({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 5H4C2.895 5 2 5.895 2 7V29C2 30.105 2.895 31 4 31H32C33.105 31 34 30.105 34 29V7C34 5.895 33.105 5 32 5ZM4 29V7H32V29H4ZM8 25H6V26.991L27.723 26.99C28.483 26.93 28.903 26.06 28.463 25.43C28.293 25.18 28.023 25.02 27.723 25H25V22H23V25H20V22H18V25H15V22H13V25H10V22H8V25Z\"/>',\n    outlineAlerted: '<path d=\"M26.8962 1.61105L21.2145 11.0807C20.9449 11.4632 20.9205 11.9597 21.1512 12.3653C21.3819 12.7709 21.8286 13.0168 22.3071 13.0015H33.6805C34.1589 13.0168 34.6057 12.7709 34.8364 12.3653C35.0671 11.9597 35.0426 11.4632 34.7731 11.0807L29.0914 1.61105C28.8634 1.23397 28.4455 1.0022 27.9938 1.0022C27.542 1.0022 27.1242 1.23397 26.8962 1.61105Z\"/><path d=\"M22.5305 5H4C2.895 5 2 5.895 2 7V29C2 30.105 2.895 31 4 31H32C33.105 31 34 30.105 34 29V14.9905C33.886 15.0001 33.7712 15.0038 33.6559 15.0015H32V29H4V7H21.3305L22.5305 5Z\"/><path d=\"M6 25H8V22H10V25H13V22H15V25H18V22H20V25H23V22H25V25H27.723C28.023 25.02 28.293 25.18 28.463 25.43C28.903 26.06 28.483 26.93 27.723 26.99L6 26.991V25Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M32 12.7101V29H4V7H23.0709C23.0242 6.6734 23 6.33952 23 6C23 5.66048 23.0242 5.3266 23.0709 5H4C2.895 5 2 5.895 2 7V29C2 30.105 2.895 31 4 31H32C33.105 31 34 30.105 34 29V11.7453C33.396 12.1666 32.7224 12.4951 32 12.7101Z\"/><path d=\"M6 25H8V22H10V25H13V22H15V25H18V22H20V25H23V22H25V25H27.723C28.023 25.02 28.293 25.18 28.463 25.43C28.903 26.06 28.483 26.93 27.723 26.99L6 26.991V25Z\"/>',\n    solid: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M34 29V7C34 5.896 33.105 5 32 5H4C2.896 5 2 5.896 2 7V29C2 30.105 2.896 31 4 31H32C33.105 31 34 30.105 34 29ZM27.723 26.99L6 26.991V25H8V22H10V25H13V22H15V25H18V22H20V25H23V22H25V25H27.723C28.023 25.02 28.293 25.18 28.463 25.43C28.903 26.06 28.483 26.93 27.723 26.99Z\"/>',\n    solidAlerted: '<path d=\"M26.8962 1.61105L21.2145 11.0807C20.9449 11.4632 20.9205 11.9597 21.1512 12.3653C21.3819 12.7709 21.8286 13.0168 22.3071 13.0015H33.6805C34.1589 13.0168 34.6057 12.7709 34.8364 12.3653C35.0671 11.9597 35.0426 11.4632 34.7731 11.0807L29.0914 1.61105C28.8634 1.23397 28.4455 1.0022 27.9938 1.0022C27.542 1.0022 27.1242 1.23397 26.8962 1.61105Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M22.5305 5L19.5283 10.0036C18.8625 11.0071 18.8125 12.2991 19.4127 13.3542C20.0154 14.4137 21.1499 15.0252 22.3317 15.0015H33.6559C33.7712 15.0038 33.886 15.0001 34 14.9905V29C34 30.105 33.105 31 32 31H4C2.896 31 2 30.105 2 29V7C2 5.896 2.896 5 4 5H22.5305ZM6 26.991L27.723 26.99C28.483 26.93 28.903 26.06 28.463 25.43C28.293 25.18 28.023 25.02 27.723 25H25V22H23V25H20V22H18V25H15V22H13V25H10V22H8V25H6V26.991Z\"/>',\n    solidBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M34 11.7453C32.8662 12.5362 31.4872 13 30 13C26.134 13 23 9.86599 23 6C23 5.66048 23.0242 5.3266 23.0709 5H4C2.896 5 2 5.896 2 7V29C2 30.105 2.896 31 4 31H32C33.105 31 34 30.105 34 29V11.7453ZM6 26.991L27.723 26.99C28.483 26.93 28.903 26.06 28.463 25.43C28.293 25.18 28.023 25.02 27.723 25H25V22H23V25H20V22H18V25H15V22H13V25H10V22H8V25H6V26.991Z\"/>'\n  })];\nexport { C as tickChartIcon, V as tickChartIconName };", "map": {"version": 3, "names": ["renderIcon", "H", "V", "C", "outline", "outlineAlerted", "outlineBadged", "solid", "solidAlerted", "solidBadged", "tickChartIcon", "tickChartIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/tick-chart.js"], "sourcesContent": ["import{renderIcon as H}from\"../icon.renderer.js\";const V=\"tick-chart\",C=[\"tick-chart\",H({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 5H4C2.895 5 2 5.895 2 7V29C2 30.105 2.895 31 4 31H32C33.105 31 34 30.105 34 29V7C34 5.895 33.105 5 32 5ZM4 29V7H32V29H4ZM8 25H6V26.991L27.723 26.99C28.483 26.93 28.903 26.06 28.463 25.43C28.293 25.18 28.023 25.02 27.723 25H25V22H23V25H20V22H18V25H15V22H13V25H10V22H8V25Z\"/>',outlineAlerted:'<path d=\"M26.8962 1.61105L21.2145 11.0807C20.9449 11.4632 20.9205 11.9597 21.1512 12.3653C21.3819 12.7709 21.8286 13.0168 22.3071 13.0015H33.6805C34.1589 13.0168 34.6057 12.7709 34.8364 12.3653C35.0671 11.9597 35.0426 11.4632 34.7731 11.0807L29.0914 1.61105C28.8634 1.23397 28.4455 1.0022 27.9938 1.0022C27.542 1.0022 27.1242 1.23397 26.8962 1.61105Z\"/><path d=\"M22.5305 5H4C2.895 5 2 5.895 2 7V29C2 30.105 2.895 31 4 31H32C33.105 31 34 30.105 34 29V14.9905C33.886 15.0001 33.7712 15.0038 33.6559 15.0015H32V29H4V7H21.3305L22.5305 5Z\"/><path d=\"M6 25H8V22H10V25H13V22H15V25H18V22H20V25H23V22H25V25H27.723C28.023 25.02 28.293 25.18 28.463 25.43C28.903 26.06 28.483 26.93 27.723 26.99L6 26.991V25Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M32 12.7101V29H4V7H23.0709C23.0242 6.6734 23 6.33952 23 6C23 5.66048 23.0242 5.3266 23.0709 5H4C2.895 5 2 5.895 2 7V29C2 30.105 2.895 31 4 31H32C33.105 31 34 30.105 34 29V11.7453C33.396 12.1666 32.7224 12.4951 32 12.7101Z\"/><path d=\"M6 25H8V22H10V25H13V22H15V25H18V22H20V25H23V22H25V25H27.723C28.023 25.02 28.293 25.18 28.463 25.43C28.903 26.06 28.483 26.93 27.723 26.99L6 26.991V25Z\"/>',solid:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M34 29V7C34 5.896 33.105 5 32 5H4C2.896 5 2 5.896 2 7V29C2 30.105 2.896 31 4 31H32C33.105 31 34 30.105 34 29ZM27.723 26.99L6 26.991V25H8V22H10V25H13V22H15V25H18V22H20V25H23V22H25V25H27.723C28.023 25.02 28.293 25.18 28.463 25.43C28.903 26.06 28.483 26.93 27.723 26.99Z\"/>',solidAlerted:'<path d=\"M26.8962 1.61105L21.2145 11.0807C20.9449 11.4632 20.9205 11.9597 21.1512 12.3653C21.3819 12.7709 21.8286 13.0168 22.3071 13.0015H33.6805C34.1589 13.0168 34.6057 12.7709 34.8364 12.3653C35.0671 11.9597 35.0426 11.4632 34.7731 11.0807L29.0914 1.61105C28.8634 1.23397 28.4455 1.0022 27.9938 1.0022C27.542 1.0022 27.1242 1.23397 26.8962 1.61105Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M22.5305 5L19.5283 10.0036C18.8625 11.0071 18.8125 12.2991 19.4127 13.3542C20.0154 14.4137 21.1499 15.0252 22.3317 15.0015H33.6559C33.7712 15.0038 33.886 15.0001 34 14.9905V29C34 30.105 33.105 31 32 31H4C2.896 31 2 30.105 2 29V7C2 5.896 2.896 5 4 5H22.5305ZM6 26.991L27.723 26.99C28.483 26.93 28.903 26.06 28.463 25.43C28.293 25.18 28.023 25.02 27.723 25H25V22H23V25H20V22H18V25H15V22H13V25H10V22H8V25H6V26.991Z\"/>',solidBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M34 11.7453C32.8662 12.5362 31.4872 13 30 13C26.134 13 23 9.86599 23 6C23 5.66048 23.0242 5.3266 23.0709 5H4C2.896 5 2 5.896 2 7V29C2 30.105 2.896 31 4 31H32C33.105 31 34 30.105 34 29V11.7453ZM6 26.991L27.723 26.99C28.483 26.93 28.903 26.06 28.463 25.43C28.293 25.18 28.023 25.02 27.723 25H25V22H23V25H20V22H18V25H15V22H13V25H10V22H8V25H6V26.991Z\"/>'})];export{C as tickChartIcon,V as tickChartIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,YAAY;EAACC,CAAC,GAAC,CAAC,YAAY,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,wUAAwU;IAACC,cAAc,EAAC,4rBAA4rB;IAACC,aAAa,EAAC,2gBAA2gB;IAACC,KAAK,EAAC,iUAAiU;IAACC,YAAY,EAAC,kzBAAkzB;IAACC,WAAW,EAAC;EAA8gB,CAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,aAAa,EAACR,CAAC,IAAIS,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}