{"ast": null, "code": "export * from \"@lit/reactive-element/decorators/query.js\";", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/lit/decorators/query.js"], "sourcesContent": ["export*from\"@lit/reactive-element/decorators/query.js\";\n"], "mappings": "AAAA,cAAW,2CAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}