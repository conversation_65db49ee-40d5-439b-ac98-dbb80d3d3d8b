{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"boat\",\n  V = [\"boat\", C({\n    outline: '<path d=\"M17.9056 20H27.8844C28.2636 20 28.6029 19.79 28.7725 19.46C28.9422 19.13 28.9122 18.73 28.6927 18.42L18.7139 4.41998C18.4645 4.06998 18.0154 3.91998 17.5963 4.04998C17.1872 4.17998 16.9078 4.56998 16.9078 4.99998V19C16.9078 19.55 17.3568 20 17.9056 20ZM18.9035 8.11998L25.9485 18H18.9035V8.11998ZM7.92688 20H13.9141C14.463 20 14.912 19.55 14.912 19V8.99998C14.912 8.54998 14.6126 8.15998 14.1836 8.03998C13.7545 7.91998 13.2955 8.09998 13.0659 8.48998L7.07869 18.49C6.88909 18.8 6.88909 19.18 7.06871 19.5C7.24832 19.81 7.57762 20.01 7.93686 20.01L7.92688 20ZM12.9163 12.61V18H9.69312L12.9163 12.61ZM6.929 26.27V25C6.929 24.4 7.42794 24 8.02667 24H32.8738L30.4789 26.13C31.2672 26.2 31.9957 26.45 32.6443 26.84L34.2708 25.4L34.4704 25.2C35.1689 24.4 35.0691 23.1 34.2708 22.4C33.8717 22.2 33.4725 22 32.9736 22H8.02667C6.33028 22 5.03304 23.3 5.03304 25V27.32C5.10289 27.27 5.17274 27.24 5.23261 27.19C5.75151 26.77 6.33028 26.47 6.929 26.27ZM29.9799 28.09C28.8823 27.99 27.7846 28.39 26.8865 29.19C25.7889 30.29 23.9927 30.29 22.7952 29.19C21.7974 28.49 20.6997 28.09 19.5022 28.09C18.3048 27.99 17.1073 28.39 16.2093 29.19C15.6105 29.69 14.912 29.99 14.1137 29.99C13.3154 29.99 12.6169 29.69 12.0182 29.19C11.0203 28.39 9.82284 27.99 8.62539 27.99C7.42794 27.99 6.23049 28.39 5.23261 29.19C4.63389 29.69 3.7358 29.99 2.9375 29.99V31.99C4.23474 32.09 5.53198 31.69 6.52985 30.79C7.12858 30.29 8.02667 29.99 8.82497 29.99C9.52348 29.99 10.3218 30.29 10.9205 30.79C12.7167 32.39 15.5107 32.39 17.4067 30.79C18.0054 30.29 18.7039 29.99 19.5022 29.99C20.2008 29.99 20.8993 30.29 21.498 30.79C23.394 32.39 26.0882 32.39 27.9842 30.79C28.4831 30.29 29.2814 29.99 29.9799 29.99C30.6785 29.99 31.377 30.29 31.8759 30.79C32.774 31.49 33.7719 31.89 34.8695 31.99V29.99C33.8717 29.99 33.6721 29.59 33.1731 29.19C32.2751 28.49 31.1774 28.09 29.9799 28.09Z\"/>',\n    solid: '<path d=\"M17.9056 20H27.8844C28.2636 20 28.6029 19.79 28.7725 19.46C28.9422 19.13 28.9122 18.73 28.6927 18.42L18.7139 4.41998C18.4645 4.06998 18.0154 3.91998 17.5963 4.04998C17.1872 4.17998 16.9078 4.56998 16.9078 4.99998V19C16.9078 19.55 17.3568 20 17.9056 20ZM7.92688 20H13.9141C14.463 20 14.912 19.55 14.912 19V8.99998C14.912 8.54998 14.6126 8.15998 14.1836 8.03998C13.7545 7.91998 13.2955 8.09998 13.0659 8.48998L7.07869 18.49C6.88909 18.8 6.88909 19.18 7.06871 19.5C7.24832 19.81 7.57762 20.01 7.93686 20.01L7.92688 20ZM6.929 26.27V25C6.929 24.4 7.42794 24 8.02667 24H32.8738L30.4789 26.13C31.2672 26.2 31.9957 26.45 32.6443 26.84L34.2708 25.4L34.4704 25.2C35.1689 24.4 35.0691 23.1 34.2708 22.4C33.8717 22.2 33.4725 22 32.9736 22H8.02667C6.33028 22 5.03304 23.3 5.03304 25V27.32C5.10289 27.27 5.17274 27.24 5.23261 27.19C5.75151 26.77 6.33028 26.47 6.929 26.27ZM29.9799 28.09C28.8823 27.99 27.7846 28.39 26.8865 29.19C25.7889 30.29 23.9927 30.29 22.7952 29.19C21.7974 28.49 20.6997 28.09 19.5022 28.09C18.3048 27.99 17.1073 28.39 16.2093 29.19C15.6105 29.69 14.912 29.99 14.1137 29.99C13.3154 29.99 12.6169 29.69 12.0182 29.19C11.0203 28.39 9.82284 27.99 8.62539 27.99C7.42794 27.99 6.23049 28.39 5.23261 29.19C4.63389 29.69 3.7358 29.99 2.9375 29.99V31.99C4.23474 32.09 5.53198 31.69 6.52985 30.79C7.12858 30.29 8.02667 29.99 8.82497 29.99C9.52348 29.99 10.3218 30.29 10.9205 30.79C12.7167 32.39 15.5107 32.39 17.4067 30.79C18.0054 30.29 18.7039 29.99 19.5022 29.99C20.2008 29.99 20.8993 30.29 21.498 30.79C23.394 32.39 26.0882 32.39 27.9842 30.79C28.4831 30.29 29.2814 29.99 29.9799 29.99C30.6785 29.99 31.377 30.29 31.8759 30.79C32.774 31.49 33.7719 31.89 34.8695 31.99V29.99C33.8717 29.99 33.6721 29.59 33.1731 29.19C32.2751 28.49 31.1774 28.09 29.9799 28.09Z\"/>'\n  })];\nexport { V as boatIcon, L as boatIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "V", "outline", "solid", "boatIcon", "boatIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/boat.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"boat\",V=[\"boat\",C({outline:'<path d=\"M17.9056 20H27.8844C28.2636 20 28.6029 19.79 28.7725 19.46C28.9422 19.13 28.9122 18.73 28.6927 18.42L18.7139 4.41998C18.4645 4.06998 18.0154 3.91998 17.5963 4.04998C17.1872 4.17998 16.9078 4.56998 16.9078 4.99998V19C16.9078 19.55 17.3568 20 17.9056 20ZM18.9035 8.11998L25.9485 18H18.9035V8.11998ZM7.92688 20H13.9141C14.463 20 14.912 19.55 14.912 19V8.99998C14.912 8.54998 14.6126 8.15998 14.1836 8.03998C13.7545 7.91998 13.2955 8.09998 13.0659 8.48998L7.07869 18.49C6.88909 18.8 6.88909 19.18 7.06871 19.5C7.24832 19.81 7.57762 20.01 7.93686 20.01L7.92688 20ZM12.9163 12.61V18H9.69312L12.9163 12.61ZM6.929 26.27V25C6.929 24.4 7.42794 24 8.02667 24H32.8738L30.4789 26.13C31.2672 26.2 31.9957 26.45 32.6443 26.84L34.2708 25.4L34.4704 25.2C35.1689 24.4 35.0691 23.1 34.2708 22.4C33.8717 22.2 33.4725 22 32.9736 22H8.02667C6.33028 22 5.03304 23.3 5.03304 25V27.32C5.10289 27.27 5.17274 27.24 5.23261 27.19C5.75151 26.77 6.33028 26.47 6.929 26.27ZM29.9799 28.09C28.8823 27.99 27.7846 28.39 26.8865 29.19C25.7889 30.29 23.9927 30.29 22.7952 29.19C21.7974 28.49 20.6997 28.09 19.5022 28.09C18.3048 27.99 17.1073 28.39 16.2093 29.19C15.6105 29.69 14.912 29.99 14.1137 29.99C13.3154 29.99 12.6169 29.69 12.0182 29.19C11.0203 28.39 9.82284 27.99 8.62539 27.99C7.42794 27.99 6.23049 28.39 5.23261 29.19C4.63389 29.69 3.7358 29.99 2.9375 29.99V31.99C4.23474 32.09 5.53198 31.69 6.52985 30.79C7.12858 30.29 8.02667 29.99 8.82497 29.99C9.52348 29.99 10.3218 30.29 10.9205 30.79C12.7167 32.39 15.5107 32.39 17.4067 30.79C18.0054 30.29 18.7039 29.99 19.5022 29.99C20.2008 29.99 20.8993 30.29 21.498 30.79C23.394 32.39 26.0882 32.39 27.9842 30.79C28.4831 30.29 29.2814 29.99 29.9799 29.99C30.6785 29.99 31.377 30.29 31.8759 30.79C32.774 31.49 33.7719 31.89 34.8695 31.99V29.99C33.8717 29.99 33.6721 29.59 33.1731 29.19C32.2751 28.49 31.1774 28.09 29.9799 28.09Z\"/>',solid:'<path d=\"M17.9056 20H27.8844C28.2636 20 28.6029 19.79 28.7725 19.46C28.9422 19.13 28.9122 18.73 28.6927 18.42L18.7139 4.41998C18.4645 4.06998 18.0154 3.91998 17.5963 4.04998C17.1872 4.17998 16.9078 4.56998 16.9078 4.99998V19C16.9078 19.55 17.3568 20 17.9056 20ZM7.92688 20H13.9141C14.463 20 14.912 19.55 14.912 19V8.99998C14.912 8.54998 14.6126 8.15998 14.1836 8.03998C13.7545 7.91998 13.2955 8.09998 13.0659 8.48998L7.07869 18.49C6.88909 18.8 6.88909 19.18 7.06871 19.5C7.24832 19.81 7.57762 20.01 7.93686 20.01L7.92688 20ZM6.929 26.27V25C6.929 24.4 7.42794 24 8.02667 24H32.8738L30.4789 26.13C31.2672 26.2 31.9957 26.45 32.6443 26.84L34.2708 25.4L34.4704 25.2C35.1689 24.4 35.0691 23.1 34.2708 22.4C33.8717 22.2 33.4725 22 32.9736 22H8.02667C6.33028 22 5.03304 23.3 5.03304 25V27.32C5.10289 27.27 5.17274 27.24 5.23261 27.19C5.75151 26.77 6.33028 26.47 6.929 26.27ZM29.9799 28.09C28.8823 27.99 27.7846 28.39 26.8865 29.19C25.7889 30.29 23.9927 30.29 22.7952 29.19C21.7974 28.49 20.6997 28.09 19.5022 28.09C18.3048 27.99 17.1073 28.39 16.2093 29.19C15.6105 29.69 14.912 29.99 14.1137 29.99C13.3154 29.99 12.6169 29.69 12.0182 29.19C11.0203 28.39 9.82284 27.99 8.62539 27.99C7.42794 27.99 6.23049 28.39 5.23261 29.19C4.63389 29.69 3.7358 29.99 2.9375 29.99V31.99C4.23474 32.09 5.53198 31.69 6.52985 30.79C7.12858 30.29 8.02667 29.99 8.82497 29.99C9.52348 29.99 10.3218 30.29 10.9205 30.79C12.7167 32.39 15.5107 32.39 17.4067 30.79C18.0054 30.29 18.7039 29.99 19.5022 29.99C20.2008 29.99 20.8993 30.29 21.498 30.79C23.394 32.39 26.0882 32.39 27.9842 30.79C28.4831 30.29 29.2814 29.99 29.9799 29.99C30.6785 29.99 31.377 30.29 31.8759 30.79C32.774 31.49 33.7719 31.89 34.8695 31.99V29.99C33.8717 29.99 33.6721 29.59 33.1731 29.19C32.2751 28.49 31.1774 28.09 29.9799 28.09Z\"/>'})];export{V as boatIcon,L as boatIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,MAAM;EAACC,CAAC,GAAC,CAAC,MAAM,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,k0DAAk0D;IAACC,KAAK,EAAC;EAA8uD,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,QAAQ,EAACJ,CAAC,IAAIK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}