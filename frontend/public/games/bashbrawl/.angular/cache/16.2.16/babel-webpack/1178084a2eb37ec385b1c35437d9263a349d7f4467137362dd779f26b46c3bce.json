{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nfunction t(t = {\n  skipFirst: !1\n}) {\n  return e => e.addInitializer(e => new s(e, t));\n}\nclass s {\n  constructor(t, s = {\n    skipFirst: !1\n  }) {\n    this.host = t, this.skipFirst = !1, this.host.addController(this), this.skipFirst = !!s.skipFirst, this.resizeElement = s.element ? s.element : this.host;\n  }\n  hostConnected() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.host.updateComplete, _this.observer = new ResizeObserver(t => {\n        window.requestAnimationFrame(() => {\n          _this.skipFirst ? _this.skipFirst = !1 : (_this.host.dispatchEvent(new CustomEvent(\"cdsResizeChange\", {\n            detail: t[0].contentRect\n          })), _this.host.requestUpdate());\n        });\n      }), _this.observer.observe(_this.resizeElement);\n    })();\n  }\n  hostDisconnected() {\n    this.observer?.disconnect();\n  }\n}\nexport { s as ResponsiveController, t as responsive };", "map": {"version": 3, "names": ["t", "<PERSON><PERSON><PERSON><PERSON>", "e", "addInitializer", "s", "constructor", "host", "addController", "resizeElement", "element", "hostConnected", "_this", "_asyncToGenerator", "updateComplete", "observer", "ResizeObserver", "window", "requestAnimationFrame", "dispatchEvent", "CustomEvent", "detail", "contentRect", "requestUpdate", "observe", "hostDisconnected", "disconnect", "ResponsiveController", "responsive"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/responsive.controller.js"], "sourcesContent": ["function t(t={skipFirst:!1}){return e=>e.addInitializer((e=>new s(e,t)))}class s{constructor(t,s={skipFirst:!1}){this.host=t,this.skipFirst=!1,this.host.addController(this),this.skipFirst=!!s.skipFirst,this.resizeElement=s.element?s.element:this.host}async hostConnected(){await this.host.updateComplete,this.observer=new ResizeObserver((t=>{window.requestAnimationFrame((()=>{this.skipFirst?this.skipFirst=!1:(this.host.dispatchEvent(new CustomEvent(\"cdsResizeChange\",{detail:t[0].contentRect})),this.host.requestUpdate())}))})),this.observer.observe(this.resizeElement)}hostDisconnected(){this.observer?.disconnect()}}export{s as ResponsiveController,t as responsive};\n"], "mappings": ";AAAA,SAASA,CAACA,CAACA,CAAC,GAAC;EAACC,SAAS,EAAC,CAAC;AAAC,CAAC,EAAC;EAAC,OAAOC,CAAC,IAAEA,CAAC,CAACC,cAAc,CAAED,CAAC,IAAE,IAAIE,CAAC,CAACF,CAAC,EAACF,CAAC,CAAE,CAAC;AAAA;AAAC,MAAMI,CAAC;EAACC,WAAWA,CAACL,CAAC,EAACI,CAAC,GAAC;IAACH,SAAS,EAAC,CAAC;EAAC,CAAC,EAAC;IAAC,IAAI,CAACK,IAAI,GAACN,CAAC,EAAC,IAAI,CAACC,SAAS,GAAC,CAAC,CAAC,EAAC,IAAI,CAACK,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC,EAAC,IAAI,CAACN,SAAS,GAAC,CAAC,CAACG,CAAC,CAACH,SAAS,EAAC,IAAI,CAACO,aAAa,GAACJ,CAAC,CAACK,OAAO,GAACL,CAAC,CAACK,OAAO,GAAC,IAAI,CAACH,IAAI;EAAA;EAAOI,aAAaA,CAAA,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAAC,MAAMD,KAAI,CAACL,IAAI,CAACO,cAAc,EAACF,KAAI,CAACG,QAAQ,GAAC,IAAIC,cAAc,CAAEf,CAAC,IAAE;QAACgB,MAAM,CAACC,qBAAqB,CAAE,MAAI;UAACN,KAAI,CAACV,SAAS,GAACU,KAAI,CAACV,SAAS,GAAC,CAAC,CAAC,IAAEU,KAAI,CAACL,IAAI,CAACY,aAAa,CAAC,IAAIC,WAAW,CAAC,iBAAiB,EAAC;YAACC,MAAM,EAACpB,CAAC,CAAC,CAAC,CAAC,CAACqB;UAAW,CAAC,CAAC,CAAC,EAACV,KAAI,CAACL,IAAI,CAACgB,aAAa,CAAC,CAAC,CAAC;QAAA,CAAE,CAAC;MAAA,CAAE,CAAC,EAACX,KAAI,CAACG,QAAQ,CAACS,OAAO,CAACZ,KAAI,CAACH,aAAa,CAAC;IAAA;EAAA;EAACgB,gBAAgBA,CAAA,EAAE;IAAC,IAAI,CAACV,QAAQ,EAAEW,UAAU,CAAC,CAAC;EAAA;AAAC;AAAC,SAAOrB,CAAC,IAAIsB,oBAAoB,EAAC1B,CAAC,IAAI2B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}