{"ast": null, "code": "\"use strict\";\n\n/**\n * Zone.js flags to disable problematic patches\n */\n// 禁用导致 \"Cannot set property once\" 错误的补丁\nwindow.__Zone_disable_addEventListener = true; // 禁用addEventListener补丁\nwindow.__Zone_disable_on_property = true; // 禁用onProperty补丁\nwindow.__zone_symbol__UNPATCHED_EVENTS = ['scroll', 'mousemove', 'click', 'load']; // 禁用特定事件补丁\nwindow.__Zone_enable_cross_context_check = true; // iframe环境兼容性", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}