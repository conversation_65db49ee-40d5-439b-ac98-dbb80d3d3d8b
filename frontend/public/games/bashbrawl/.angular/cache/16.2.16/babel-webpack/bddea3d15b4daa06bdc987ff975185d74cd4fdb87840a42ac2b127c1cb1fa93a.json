{"ast": null, "code": "import { Positions as t } from \"./interfaces.js\";\nimport { transformSpacedStringToArray as e, removePrefix as o, capitalizeFirstLetter as n } from \"../utils/string.js\";\nimport { setAttributes as i, getWindowDimensions as r } from \"../utils/dom.js\";\nimport { isNil as s } from \"../utils/identity.js\";\nimport { updateElementStyles as p, getCssPropertyValue as f } from \"../utils/css.js\";\nimport { sumAndSubtract as u } from \"../utils/math.js\";\nimport { getEnumValueFromStringKey as c } from \"../utils/enum.js\";\nfunction a(t, e, o, n, i) {\n  const r = {\n      mainAxisOffset: 0,\n      crossAxisOffset: 0\n    },\n    p = o && \"default\" === o.type;\n  return s(t) ? p && (r.mainAxisOffset = -10) : r.mainAxisOffset = t, s(e) ? p && \"end\" === n && \"start\" === i && (r.crossAxisOffset = -10) : r.crossAxisOffset = e, r;\n}\nfunction l(t) {\n  t.removeAttribute(\"responsive\");\n  const e = a(t.mainAxisOffset, t.crossAxisOffset, t.pointer, t.pointerAlign, t.anchorAlign),\n    o = O(t.orientation, t.anchorRect, t.anchorAlign, t.pointer, t.pointerAlign, t.contentWrapper, e.mainAxisOffset, e.crossAxisOffset);\n  if (!1 === o) i(t, [\"responsive\", \"\"], [\"_position-at\", !1]), p(t.hostWrapper, [\"position\", \"\"], [\"top\", \"\"], [\"left\", \"\"]), p(t.pointerWrapper, [\"visibility\", \"hidden\"]);else {\n    if (t.pointer) {\n      const e = o.pointer;\n      p(t.pointerWrapper, [\"visibility\", \"visible\"]), i(t, [\"_pointer-type\", t.pointer.type || !1]), i(t, [\"_position-at\", e.location]);\n    } else p(t.pointerWrapper, [\"visibility\", \"hidden\"]), i(t, [\"_position-at\", !1], [\"_pointer-type\", !1]);\n    const e = o.popup.top + \"px\",\n      n = o.popup.left + \"px\";\n    p(t.hostWrapper, [\"position\", \"absolute\"], [\"top\", e], [\"left\", n]);\n  }\n}\nfunction h(i) {\n  const r = [],\n    s = [];\n  for (const p of e(i)) {\n    if (\"none\" === p) return [[], 0];\n    if (p.indexOf(\"only:\") > -1) return [[c(t, o(p, \"only:\"), n)], 0];\n    if (p.indexOf(\"not:\") > -1) s.push(c(t, o(p, \"not:\"), n));else {\n      const e = c(t, p, n);\n      s.push(e), r.push(e);\n    }\n  }\n  return [r, u(0, [t.All], s)];\n}\nfunction m(t) {\n  switch (t) {\n    case \"mid\":\n      return [1, 0, 2];\n    case \"end\":\n      return [2, 1, 0];\n    default:\n      return [0, 1, 2];\n  }\n}\nfunction d(e, o, n) {\n  if (!1 === o) return !1;\n  const i = o[t[e]?.toLowerCase()];\n  if (!i) return !1;\n  const [r, s, p] = i;\n  if (!1 === r && !1 === s && !1 === p) return !1;\n  for (const t of m(n)) if (i[t]) return Object.assign({}, i[t]);\n  return !1;\n}\nfunction g(e) {\n  const o = [t.Bottom, t.Right, t.Left, t.Top, t.Responsive];\n  for (const t of o) if (e >= t) return [t, e - t];\n  return [0, 0];\n}\nfunction b(e, o) {\n  if (e.length < 1) {\n    const [t, n] = g(o);\n    return [t, e, n];\n  }\n  return [e[0], e.slice(1), e[0] !== t.Responsive ? o : 0];\n}\nfunction w(t) {\n  switch (t.toLowerCase()) {\n    case \"top\":\n      return \"popup-bottom\";\n    case \"bottom\":\n      return \"popup-top\";\n    case \"left\":\n      return \"popup-right\";\n    default:\n      return \"popup-left\";\n  }\n}\nfunction x(t, e) {\n  let o;\n  return o = \"popup-bottom\" === t || \"popup-top\" === t ? {\n    start: \"pointer-left\",\n    mid: \"pointer-center\",\n    end: \"pointer-right\"\n  } : {\n    start: \"pointer-top\",\n    mid: \"pointer-mid\",\n    end: \"pointer-bottom\"\n  }, o[e];\n}\nfunction A(e, o, n) {\n  let [i, r] = o,\n    s = null;\n  for (; null === s;) {\n    const [o, p, f] = b(i, r),\n      u = d(o, e, n);\n    switch (!0) {\n      case !1 !== u:\n        s = Object.assign({}, u, {\n          pointer: {\n            location: w(t[o])\n          }\n        });\n        break;\n      case !1 === u && 0 === f:\n        s = !1;\n        break;\n      case !1 === u:\n        i = p, r = f;\n    }\n  }\n  return s;\n}\nfunction O(t, e, o, n, i, s, p, u) {\n  if (!e) return !1;\n  const c = n ? n.offsetHeight : 0,\n    a = r();\n  if (a.width <= parseInt(f(\"--cds-global-layout-width-xs-static\"), 10)) return !1;\n  const l = A(y(e, c, i, s.getBoundingClientRect(), a, p, u), h(t), o);\n  if (!1 === l) return !1;\n  const m = l.pointer.location,\n    d = m + \" \" + x(m, i);\n  return Object.assign({}, l, {\n    pointer: {\n      size: c,\n      location: d\n    }\n  });\n}\nfunction y(t, e, o, n, i, r, s) {\n  return {\n    top: j(\"top\", o, t, n, e, i, r, s),\n    right: j(\"right\", o, t, n, e, i, r, s),\n    bottom: j(\"bottom\", o, t, n, e, i, r, s),\n    left: j(\"left\", o, t, n, e, i, r, s)\n  };\n}\nfunction j(t, e, o, n, i, r, s, p) {\n  let f;\n  switch (t) {\n    case \"top\":\n      return f = L(o.top, i, n.height, s, 0), !1 !== f && C(o.left, o.width, n.width, p, 0, r.width, e).map(t => !1 !== t && {\n        popup: {\n          top: f,\n          left: t\n        }\n      });\n    case \"bottom\":\n      return f = L(o.bottom, 0, n.height, s, r.height), !1 !== f && C(o.left, o.width, n.width, p, 0, r.width, e).map(t => !1 !== t && {\n        popup: {\n          top: f,\n          left: t\n        }\n      });\n    case \"left\":\n      return f = L(o.left, i, n.width, s, 0), !1 !== f && C(o.top, o.height, n.height, p, 0, r.height, e).map(t => !1 !== t && {\n        popup: {\n          top: t,\n          left: f\n        }\n      });\n    case \"right\":\n      return f = L(o.right, 0, n.width, s, r.width), !1 !== f && C(o.top, o.height, n.height, p, 0, r.height, e).map(t => !1 !== t && {\n        popup: {\n          top: t,\n          left: f\n        }\n      });\n  }\n}\nfunction v(t, e, o, n, i) {\n  return 0 === i ? u(t, [], [e, o, n]) : u(t, [e, o, n], []);\n}\nfunction W(t, e, o) {\n  return 0 === o ? t > o && t : t < o && e;\n}\nfunction R(t, e, o, n, i, r = 0) {\n  switch (t) {\n    case \"mid\":\n      return e + o * n - .5 * r + i;\n    case \"end\":\n      return e + o * n - i;\n    case \"start\":\n      return e + o * n + i;\n  }\n}\nfunction _(t, e, o, n) {\n  const [i, r] = n;\n  switch (t) {\n    case \"mid\":\n      return e > i && e + o < r && e;\n    case \"end\":\n      {\n        const t = e - o;\n        return t > i && t;\n      }\n    case \"start\":\n      return e + o < r && e;\n  }\n}\nfunction C(t, e, o, n, i, r, s) {\n  return [0, .5, 1].map(p => _(s, R(s, t, p, e, n, o), o, [i, r]));\n}\nfunction L(t, e, o, n, i = 0) {\n  return W(v(t, e, o, n, i), t + n, i);\n}\nexport { d as checkNextPosition, A as getBestPositionForPreferences, m as getCrossAxisOrderOfPreference, R as getCrossAxisPosition, v as getMainAxisPosition, L as getMainAxisPositionOrViolation, g as getNextDefaultPosition, b as getNextPosition, h as getOrientationTuple, x as getPointerAlignment, w as getPointerPosition, a as getPopupOffsetOrDefaultOffsets, O as getPopupPosition, j as getPositionConfig, C as getPositionOrViolationFromCrossAxis, y as getPositions, l as setPopupPosition, _ as testCrossAxisPosition, W as testMainAxisPosition };", "map": {"version": 3, "names": ["Positions", "t", "transformSpacedStringToArray", "e", "removePrefix", "o", "capitalizeFirstLetter", "n", "setAttributes", "i", "getWindowDimensions", "r", "isNil", "s", "updateElementStyles", "p", "getCssPropertyValue", "f", "sumAndSubtract", "u", "getEnumValueFromStringKey", "c", "a", "mainAxisOffset", "crossAxisOffset", "type", "l", "removeAttribute", "pointer", "pointerAlign", "anchorAlign", "O", "orientation", "anchorRect", "contentWrapper", "hostWrapper", "pointerWrapper", "location", "popup", "top", "left", "h", "indexOf", "push", "All", "m", "d", "toLowerCase", "Object", "assign", "g", "Bottom", "Right", "Left", "Top", "Responsive", "b", "length", "slice", "w", "x", "start", "mid", "end", "A", "offsetHeight", "width", "parseInt", "y", "getBoundingClientRect", "size", "j", "right", "bottom", "L", "height", "C", "map", "v", "W", "R", "_", "checkNextPosition", "getBestPositionForPreferences", "getCrossAxisOrderOfPreference", "getCrossAxisPosition", "getMainAxisPosition", "getMainAxisPositionOrViolation", "getNextDefaultPosition", "getNextPosition", "getOrientationTuple", "getPointerAlignment", "getPointerPosition", "getPopupOffsetOrDefaultOffsets", "getPopupPosition", "getPositionConfig", "getPositionOrViolationFromCrossAxis", "getPositions", "setPopupPosition", "testCrossAxisPosition", "testMainAxisPosition"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/positioning/utils.js"], "sourcesContent": ["import{Positions as t}from\"./interfaces.js\";import{transformSpacedStringToArray as e,removePrefix as o,capitalizeFirstLetter as n}from\"../utils/string.js\";import{setAttributes as i,getWindowDimensions as r}from\"../utils/dom.js\";import{isNil as s}from\"../utils/identity.js\";import{updateElementStyles as p,getCssPropertyValue as f}from\"../utils/css.js\";import{sumAndSubtract as u}from\"../utils/math.js\";import{getEnumValueFromStringKey as c}from\"../utils/enum.js\";function a(t,e,o,n,i){const r={mainAxisOffset:0,crossAxisOffset:0},p=o&&\"default\"===o.type;return s(t)?p&&(r.mainAxisOffset=-10):r.mainAxisOffset=t,s(e)?p&&\"end\"===n&&\"start\"===i&&(r.crossAxisOffset=-10):r.crossAxisOffset=e,r}function l(t){t.removeAttribute(\"responsive\");const e=a(t.mainAxisOffset,t.crossAxisOffset,t.pointer,t.pointerAlign,t.anchorAlign),o=O(t.orientation,t.anchorRect,t.anchorAlign,t.pointer,t.pointerAlign,t.contentWrapper,e.mainAxisOffset,e.crossAxisOffset);if(!1===o)i(t,[\"responsive\",\"\"],[\"_position-at\",!1]),p(t.hostWrapper,[\"position\",\"\"],[\"top\",\"\"],[\"left\",\"\"]),p(t.pointerWrapper,[\"visibility\",\"hidden\"]);else{if(t.pointer){const e=o.pointer;p(t.pointerWrapper,[\"visibility\",\"visible\"]),i(t,[\"_pointer-type\",t.pointer.type||!1]),i(t,[\"_position-at\",e.location])}else p(t.pointerWrapper,[\"visibility\",\"hidden\"]),i(t,[\"_position-at\",!1],[\"_pointer-type\",!1]);const e=o.popup.top+\"px\",n=o.popup.left+\"px\";p(t.hostWrapper,[\"position\",\"absolute\"],[\"top\",e],[\"left\",n])}}function h(i){const r=[],s=[];for(const p of e(i)){if(\"none\"===p)return[[],0];if(p.indexOf(\"only:\")>-1)return[[c(t,o(p,\"only:\"),n)],0];if(p.indexOf(\"not:\")>-1)s.push(c(t,o(p,\"not:\"),n));else{const e=c(t,p,n);s.push(e),r.push(e)}}return[r,u(0,[t.All],s)]}function m(t){switch(t){case\"mid\":return[1,0,2];case\"end\":return[2,1,0];default:return[0,1,2]}}function d(e,o,n){if(!1===o)return!1;const i=o[t[e]?.toLowerCase()];if(!i)return!1;const[r,s,p]=i;if(!1===r&&!1===s&&!1===p)return!1;for(const t of m(n))if(i[t])return Object.assign({},i[t]);return!1}function g(e){const o=[t.Bottom,t.Right,t.Left,t.Top,t.Responsive];for(const t of o)if(e>=t)return[t,e-t];return[0,0]}function b(e,o){if(e.length<1){const[t,n]=g(o);return[t,e,n]}return[e[0],e.slice(1),e[0]!==t.Responsive?o:0]}function w(t){switch(t.toLowerCase()){case\"top\":return\"popup-bottom\";case\"bottom\":return\"popup-top\";case\"left\":return\"popup-right\";default:return\"popup-left\"}}function x(t,e){let o;return o=\"popup-bottom\"===t||\"popup-top\"===t?{start:\"pointer-left\",mid:\"pointer-center\",end:\"pointer-right\"}:{start:\"pointer-top\",mid:\"pointer-mid\",end:\"pointer-bottom\"},o[e]}function A(e,o,n){let[i,r]=o,s=null;for(;null===s;){const[o,p,f]=b(i,r),u=d(o,e,n);switch(!0){case!1!==u:s=Object.assign({},u,{pointer:{location:w(t[o])}});break;case!1===u&&0===f:s=!1;break;case!1===u:i=p,r=f}}return s}function O(t,e,o,n,i,s,p,u){if(!e)return!1;const c=n?n.offsetHeight:0,a=r();if(a.width<=parseInt(f(\"--cds-global-layout-width-xs-static\"),10))return!1;const l=A(y(e,c,i,s.getBoundingClientRect(),a,p,u),h(t),o);if(!1===l)return!1;const m=l.pointer.location,d=m+\" \"+x(m,i);return Object.assign({},l,{pointer:{size:c,location:d}})}function y(t,e,o,n,i,r,s){return{top:j(\"top\",o,t,n,e,i,r,s),right:j(\"right\",o,t,n,e,i,r,s),bottom:j(\"bottom\",o,t,n,e,i,r,s),left:j(\"left\",o,t,n,e,i,r,s)}}function j(t,e,o,n,i,r,s,p){let f;switch(t){case\"top\":return f=L(o.top,i,n.height,s,0),!1!==f&&C(o.left,o.width,n.width,p,0,r.width,e).map((t=>!1!==t&&{popup:{top:f,left:t}}));case\"bottom\":return f=L(o.bottom,0,n.height,s,r.height),!1!==f&&C(o.left,o.width,n.width,p,0,r.width,e).map((t=>!1!==t&&{popup:{top:f,left:t}}));case\"left\":return f=L(o.left,i,n.width,s,0),!1!==f&&C(o.top,o.height,n.height,p,0,r.height,e).map((t=>!1!==t&&{popup:{top:t,left:f}}));case\"right\":return f=L(o.right,0,n.width,s,r.width),!1!==f&&C(o.top,o.height,n.height,p,0,r.height,e).map((t=>!1!==t&&{popup:{top:t,left:f}}))}}function v(t,e,o,n,i){return 0===i?u(t,[],[e,o,n]):u(t,[e,o,n],[])}function W(t,e,o){return 0===o?t>o&&t:t<o&&e}function R(t,e,o,n,i,r=0){switch(t){case\"mid\":return e+o*n-.5*r+i;case\"end\":return e+o*n-i;case\"start\":return e+o*n+i}}function _(t,e,o,n){const[i,r]=n;switch(t){case\"mid\":return e>i&&e+o<r&&e;case\"end\":{const t=e-o;return t>i&&t}case\"start\":return e+o<r&&e}}function C(t,e,o,n,i,r,s){return[0,.5,1].map((p=>_(s,R(s,t,p,e,n,o),o,[i,r])))}function L(t,e,o,n,i=0){return W(v(t,e,o,n,i),t+n,i)}export{d as checkNextPosition,A as getBestPositionForPreferences,m as getCrossAxisOrderOfPreference,R as getCrossAxisPosition,v as getMainAxisPosition,L as getMainAxisPositionOrViolation,g as getNextDefaultPosition,b as getNextPosition,h as getOrientationTuple,x as getPointerAlignment,w as getPointerPosition,a as getPopupOffsetOrDefaultOffsets,O as getPopupPosition,j as getPositionConfig,C as getPositionOrViolationFromCrossAxis,y as getPositions,l as setPopupPosition,_ as testCrossAxisPosition,W as testMainAxisPosition};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,QAAK,iBAAiB;AAAC,SAAOC,4BAA4B,IAAIC,CAAC,EAACC,YAAY,IAAIC,CAAC,EAACC,qBAAqB,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,aAAa,IAAIC,CAAC,EAACC,mBAAmB,IAAIC,CAAC,QAAK,iBAAiB;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,EAACC,mBAAmB,IAAIC,CAAC,QAAK,iBAAiB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAAOC,yBAAyB,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAASC,CAACA,CAACrB,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAAC;EAAC,MAAME,CAAC,GAAC;MAACY,cAAc,EAAC,CAAC;MAACC,eAAe,EAAC;IAAC,CAAC;IAACT,CAAC,GAACV,CAAC,IAAE,SAAS,KAAGA,CAAC,CAACoB,IAAI;EAAC,OAAOZ,CAAC,CAACZ,CAAC,CAAC,GAACc,CAAC,KAAGJ,CAAC,CAACY,cAAc,GAAC,CAAC,EAAE,CAAC,GAACZ,CAAC,CAACY,cAAc,GAACtB,CAAC,EAACY,CAAC,CAACV,CAAC,CAAC,GAACY,CAAC,IAAE,KAAK,KAAGR,CAAC,IAAE,OAAO,KAAGE,CAAC,KAAGE,CAAC,CAACa,eAAe,GAAC,CAAC,EAAE,CAAC,GAACb,CAAC,CAACa,eAAe,GAACrB,CAAC,EAACQ,CAAC;AAAA;AAAC,SAASe,CAACA,CAACzB,CAAC,EAAC;EAACA,CAAC,CAAC0B,eAAe,CAAC,YAAY,CAAC;EAAC,MAAMxB,CAAC,GAACmB,CAAC,CAACrB,CAAC,CAACsB,cAAc,EAACtB,CAAC,CAACuB,eAAe,EAACvB,CAAC,CAAC2B,OAAO,EAAC3B,CAAC,CAAC4B,YAAY,EAAC5B,CAAC,CAAC6B,WAAW,CAAC;IAACzB,CAAC,GAAC0B,CAAC,CAAC9B,CAAC,CAAC+B,WAAW,EAAC/B,CAAC,CAACgC,UAAU,EAAChC,CAAC,CAAC6B,WAAW,EAAC7B,CAAC,CAAC2B,OAAO,EAAC3B,CAAC,CAAC4B,YAAY,EAAC5B,CAAC,CAACiC,cAAc,EAAC/B,CAAC,CAACoB,cAAc,EAACpB,CAAC,CAACqB,eAAe,CAAC;EAAC,IAAG,CAAC,CAAC,KAAGnB,CAAC,EAACI,CAAC,CAACR,CAAC,EAAC,CAAC,YAAY,EAAC,EAAE,CAAC,EAAC,CAAC,cAAc,EAAC,CAAC,CAAC,CAAC,CAAC,EAACc,CAAC,CAACd,CAAC,CAACkC,WAAW,EAAC,CAAC,UAAU,EAAC,EAAE,CAAC,EAAC,CAAC,KAAK,EAAC,EAAE,CAAC,EAAC,CAAC,MAAM,EAAC,EAAE,CAAC,CAAC,EAACpB,CAAC,CAACd,CAAC,CAACmC,cAAc,EAAC,CAAC,YAAY,EAAC,QAAQ,CAAC,CAAC,CAAC,KAAI;IAAC,IAAGnC,CAAC,CAAC2B,OAAO,EAAC;MAAC,MAAMzB,CAAC,GAACE,CAAC,CAACuB,OAAO;MAACb,CAAC,CAACd,CAAC,CAACmC,cAAc,EAAC,CAAC,YAAY,EAAC,SAAS,CAAC,CAAC,EAAC3B,CAAC,CAACR,CAAC,EAAC,CAAC,eAAe,EAACA,CAAC,CAAC2B,OAAO,CAACH,IAAI,IAAE,CAAC,CAAC,CAAC,CAAC,EAAChB,CAAC,CAACR,CAAC,EAAC,CAAC,cAAc,EAACE,CAAC,CAACkC,QAAQ,CAAC,CAAC;IAAA,CAAC,MAAKtB,CAAC,CAACd,CAAC,CAACmC,cAAc,EAAC,CAAC,YAAY,EAAC,QAAQ,CAAC,CAAC,EAAC3B,CAAC,CAACR,CAAC,EAAC,CAAC,cAAc,EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,eAAe,EAAC,CAAC,CAAC,CAAC,CAAC;IAAC,MAAME,CAAC,GAACE,CAAC,CAACiC,KAAK,CAACC,GAAG,GAAC,IAAI;MAAChC,CAAC,GAACF,CAAC,CAACiC,KAAK,CAACE,IAAI,GAAC,IAAI;IAACzB,CAAC,CAACd,CAAC,CAACkC,WAAW,EAAC,CAAC,UAAU,EAAC,UAAU,CAAC,EAAC,CAAC,KAAK,EAAChC,CAAC,CAAC,EAAC,CAAC,MAAM,EAACI,CAAC,CAAC,CAAC;EAAA;AAAC;AAAC,SAASkC,CAACA,CAAChC,CAAC,EAAC;EAAC,MAAME,CAAC,GAAC,EAAE;IAACE,CAAC,GAAC,EAAE;EAAC,KAAI,MAAME,CAAC,IAAIZ,CAAC,CAACM,CAAC,CAAC,EAAC;IAAC,IAAG,MAAM,KAAGM,CAAC,EAAC,OAAM,CAAC,EAAE,EAAC,CAAC,CAAC;IAAC,IAAGA,CAAC,CAAC2B,OAAO,CAAC,OAAO,CAAC,GAAC,CAAC,CAAC,EAAC,OAAM,CAAC,CAACrB,CAAC,CAACpB,CAAC,EAACI,CAAC,CAACU,CAAC,EAAC,OAAO,CAAC,EAACR,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;IAAC,IAAGQ,CAAC,CAAC2B,OAAO,CAAC,MAAM,CAAC,GAAC,CAAC,CAAC,EAAC7B,CAAC,CAAC8B,IAAI,CAACtB,CAAC,CAACpB,CAAC,EAACI,CAAC,CAACU,CAAC,EAAC,MAAM,CAAC,EAACR,CAAC,CAAC,CAAC,CAAC,KAAI;MAAC,MAAMJ,CAAC,GAACkB,CAAC,CAACpB,CAAC,EAACc,CAAC,EAACR,CAAC,CAAC;MAACM,CAAC,CAAC8B,IAAI,CAACxC,CAAC,CAAC,EAACQ,CAAC,CAACgC,IAAI,CAACxC,CAAC,CAAC;IAAA;EAAC;EAAC,OAAM,CAACQ,CAAC,EAACQ,CAAC,CAAC,CAAC,EAAC,CAAClB,CAAC,CAAC2C,GAAG,CAAC,EAAC/B,CAAC,CAAC,CAAC;AAAA;AAAC,SAASgC,CAACA,CAAC5C,CAAC,EAAC;EAAC,QAAOA,CAAC;IAAE,KAAI,KAAK;MAAC,OAAM,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;IAAC,KAAI,KAAK;MAAC,OAAM,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;IAAC;MAAQ,OAAM,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;EAAA;AAAC;AAAC,SAAS6C,CAACA,CAAC3C,CAAC,EAACE,CAAC,EAACE,CAAC,EAAC;EAAC,IAAG,CAAC,CAAC,KAAGF,CAAC,EAAC,OAAM,CAAC,CAAC;EAAC,MAAMI,CAAC,GAACJ,CAAC,CAACJ,CAAC,CAACE,CAAC,CAAC,EAAE4C,WAAW,CAAC,CAAC,CAAC;EAAC,IAAG,CAACtC,CAAC,EAAC,OAAM,CAAC,CAAC;EAAC,MAAK,CAACE,CAAC,EAACE,CAAC,EAACE,CAAC,CAAC,GAACN,CAAC;EAAC,IAAG,CAAC,CAAC,KAAGE,CAAC,IAAE,CAAC,CAAC,KAAGE,CAAC,IAAE,CAAC,CAAC,KAAGE,CAAC,EAAC,OAAM,CAAC,CAAC;EAAC,KAAI,MAAMd,CAAC,IAAI4C,CAAC,CAACtC,CAAC,CAAC,EAAC,IAAGE,CAAC,CAACR,CAAC,CAAC,EAAC,OAAO+C,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAACxC,CAAC,CAACR,CAAC,CAAC,CAAC;EAAC,OAAM,CAAC,CAAC;AAAA;AAAC,SAASiD,CAACA,CAAC/C,CAAC,EAAC;EAAC,MAAME,CAAC,GAAC,CAACJ,CAAC,CAACkD,MAAM,EAAClD,CAAC,CAACmD,KAAK,EAACnD,CAAC,CAACoD,IAAI,EAACpD,CAAC,CAACqD,GAAG,EAACrD,CAAC,CAACsD,UAAU,CAAC;EAAC,KAAI,MAAMtD,CAAC,IAAII,CAAC,EAAC,IAAGF,CAAC,IAAEF,CAAC,EAAC,OAAM,CAACA,CAAC,EAACE,CAAC,GAACF,CAAC,CAAC;EAAC,OAAM,CAAC,CAAC,EAAC,CAAC,CAAC;AAAA;AAAC,SAASuD,CAACA,CAACrD,CAAC,EAACE,CAAC,EAAC;EAAC,IAAGF,CAAC,CAACsD,MAAM,GAAC,CAAC,EAAC;IAAC,MAAK,CAACxD,CAAC,EAACM,CAAC,CAAC,GAAC2C,CAAC,CAAC7C,CAAC,CAAC;IAAC,OAAM,CAACJ,CAAC,EAACE,CAAC,EAACI,CAAC,CAAC;EAAA;EAAC,OAAM,CAACJ,CAAC,CAAC,CAAC,CAAC,EAACA,CAAC,CAACuD,KAAK,CAAC,CAAC,CAAC,EAACvD,CAAC,CAAC,CAAC,CAAC,KAAGF,CAAC,CAACsD,UAAU,GAAClD,CAAC,GAAC,CAAC,CAAC;AAAA;AAAC,SAASsD,CAACA,CAAC1D,CAAC,EAAC;EAAC,QAAOA,CAAC,CAAC8C,WAAW,CAAC,CAAC;IAAE,KAAI,KAAK;MAAC,OAAM,cAAc;IAAC,KAAI,QAAQ;MAAC,OAAM,WAAW;IAAC,KAAI,MAAM;MAAC,OAAM,aAAa;IAAC;MAAQ,OAAM,YAAY;EAAA;AAAC;AAAC,SAASa,CAACA,CAAC3D,CAAC,EAACE,CAAC,EAAC;EAAC,IAAIE,CAAC;EAAC,OAAOA,CAAC,GAAC,cAAc,KAAGJ,CAAC,IAAE,WAAW,KAAGA,CAAC,GAAC;IAAC4D,KAAK,EAAC,cAAc;IAACC,GAAG,EAAC,gBAAgB;IAACC,GAAG,EAAC;EAAe,CAAC,GAAC;IAACF,KAAK,EAAC,aAAa;IAACC,GAAG,EAAC,aAAa;IAACC,GAAG,EAAC;EAAgB,CAAC,EAAC1D,CAAC,CAACF,CAAC,CAAC;AAAA;AAAC,SAAS6D,CAACA,CAAC7D,CAAC,EAACE,CAAC,EAACE,CAAC,EAAC;EAAC,IAAG,CAACE,CAAC,EAACE,CAAC,CAAC,GAACN,CAAC;IAACQ,CAAC,GAAC,IAAI;EAAC,OAAK,IAAI,KAAGA,CAAC,GAAE;IAAC,MAAK,CAACR,CAAC,EAACU,CAAC,EAACE,CAAC,CAAC,GAACuC,CAAC,CAAC/C,CAAC,EAACE,CAAC,CAAC;MAACQ,CAAC,GAAC2B,CAAC,CAACzC,CAAC,EAACF,CAAC,EAACI,CAAC,CAAC;IAAC,QAAO,CAAC,CAAC;MAAE,KAAI,CAAC,CAAC,KAAGY,CAAC;QAACN,CAAC,GAACmC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAC9B,CAAC,EAAC;UAACS,OAAO,EAAC;YAACS,QAAQ,EAACsB,CAAC,CAAC1D,CAAC,CAACI,CAAC,CAAC;UAAC;QAAC,CAAC,CAAC;QAAC;MAAM,KAAI,CAAC,CAAC,KAAGc,CAAC,IAAE,CAAC,KAAGF,CAAC;QAACJ,CAAC,GAAC,CAAC,CAAC;QAAC;MAAM,KAAI,CAAC,CAAC,KAAGM,CAAC;QAACV,CAAC,GAACM,CAAC,EAACJ,CAAC,GAACM,CAAC;IAAA;EAAC;EAAC,OAAOJ,CAAC;AAAA;AAAC,SAASkB,CAACA,CAAC9B,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACE,CAAC,EAACI,CAAC,EAAC;EAAC,IAAG,CAAChB,CAAC,EAAC,OAAM,CAAC,CAAC;EAAC,MAAMkB,CAAC,GAACd,CAAC,GAACA,CAAC,CAAC0D,YAAY,GAAC,CAAC;IAAC3C,CAAC,GAACX,CAAC,CAAC,CAAC;EAAC,IAAGW,CAAC,CAAC4C,KAAK,IAAEC,QAAQ,CAAClD,CAAC,CAAC,qCAAqC,CAAC,EAAC,EAAE,CAAC,EAAC,OAAM,CAAC,CAAC;EAAC,MAAMS,CAAC,GAACsC,CAAC,CAACI,CAAC,CAACjE,CAAC,EAACkB,CAAC,EAACZ,CAAC,EAACI,CAAC,CAACwD,qBAAqB,CAAC,CAAC,EAAC/C,CAAC,EAACP,CAAC,EAACI,CAAC,CAAC,EAACsB,CAAC,CAACxC,CAAC,CAAC,EAACI,CAAC,CAAC;EAAC,IAAG,CAAC,CAAC,KAAGqB,CAAC,EAAC,OAAM,CAAC,CAAC;EAAC,MAAMmB,CAAC,GAACnB,CAAC,CAACE,OAAO,CAACS,QAAQ;IAACS,CAAC,GAACD,CAAC,GAAC,GAAG,GAACe,CAAC,CAACf,CAAC,EAACpC,CAAC,CAAC;EAAC,OAAOuC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAACvB,CAAC,EAAC;IAACE,OAAO,EAAC;MAAC0C,IAAI,EAACjD,CAAC;MAACgB,QAAQ,EAACS;IAAC;EAAC,CAAC,CAAC;AAAA;AAAC,SAASsB,CAACA,CAACnE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAAC;EAAC,OAAM;IAAC0B,GAAG,EAACgC,CAAC,CAAC,KAAK,EAAClE,CAAC,EAACJ,CAAC,EAACM,CAAC,EAACJ,CAAC,EAACM,CAAC,EAACE,CAAC,EAACE,CAAC,CAAC;IAAC2D,KAAK,EAACD,CAAC,CAAC,OAAO,EAAClE,CAAC,EAACJ,CAAC,EAACM,CAAC,EAACJ,CAAC,EAACM,CAAC,EAACE,CAAC,EAACE,CAAC,CAAC;IAAC4D,MAAM,EAACF,CAAC,CAAC,QAAQ,EAAClE,CAAC,EAACJ,CAAC,EAACM,CAAC,EAACJ,CAAC,EAACM,CAAC,EAACE,CAAC,EAACE,CAAC,CAAC;IAAC2B,IAAI,EAAC+B,CAAC,CAAC,MAAM,EAAClE,CAAC,EAACJ,CAAC,EAACM,CAAC,EAACJ,CAAC,EAACM,CAAC,EAACE,CAAC,EAACE,CAAC;EAAC,CAAC;AAAA;AAAC,SAAS0D,CAACA,CAACtE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAAC;EAAC,IAAIE,CAAC;EAAC,QAAOhB,CAAC;IAAE,KAAI,KAAK;MAAC,OAAOgB,CAAC,GAACyD,CAAC,CAACrE,CAAC,CAACkC,GAAG,EAAC9B,CAAC,EAACF,CAAC,CAACoE,MAAM,EAAC9D,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,KAAGI,CAAC,IAAE2D,CAAC,CAACvE,CAAC,CAACmC,IAAI,EAACnC,CAAC,CAAC6D,KAAK,EAAC3D,CAAC,CAAC2D,KAAK,EAACnD,CAAC,EAAC,CAAC,EAACJ,CAAC,CAACuD,KAAK,EAAC/D,CAAC,CAAC,CAAC0E,GAAG,CAAE5E,CAAC,IAAE,CAAC,CAAC,KAAGA,CAAC,IAAE;QAACqC,KAAK,EAAC;UAACC,GAAG,EAACtB,CAAC;UAACuB,IAAI,EAACvC;QAAC;MAAC,CAAE,CAAC;IAAC,KAAI,QAAQ;MAAC,OAAOgB,CAAC,GAACyD,CAAC,CAACrE,CAAC,CAACoE,MAAM,EAAC,CAAC,EAAClE,CAAC,CAACoE,MAAM,EAAC9D,CAAC,EAACF,CAAC,CAACgE,MAAM,CAAC,EAAC,CAAC,CAAC,KAAG1D,CAAC,IAAE2D,CAAC,CAACvE,CAAC,CAACmC,IAAI,EAACnC,CAAC,CAAC6D,KAAK,EAAC3D,CAAC,CAAC2D,KAAK,EAACnD,CAAC,EAAC,CAAC,EAACJ,CAAC,CAACuD,KAAK,EAAC/D,CAAC,CAAC,CAAC0E,GAAG,CAAE5E,CAAC,IAAE,CAAC,CAAC,KAAGA,CAAC,IAAE;QAACqC,KAAK,EAAC;UAACC,GAAG,EAACtB,CAAC;UAACuB,IAAI,EAACvC;QAAC;MAAC,CAAE,CAAC;IAAC,KAAI,MAAM;MAAC,OAAOgB,CAAC,GAACyD,CAAC,CAACrE,CAAC,CAACmC,IAAI,EAAC/B,CAAC,EAACF,CAAC,CAAC2D,KAAK,EAACrD,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,KAAGI,CAAC,IAAE2D,CAAC,CAACvE,CAAC,CAACkC,GAAG,EAAClC,CAAC,CAACsE,MAAM,EAACpE,CAAC,CAACoE,MAAM,EAAC5D,CAAC,EAAC,CAAC,EAACJ,CAAC,CAACgE,MAAM,EAACxE,CAAC,CAAC,CAAC0E,GAAG,CAAE5E,CAAC,IAAE,CAAC,CAAC,KAAGA,CAAC,IAAE;QAACqC,KAAK,EAAC;UAACC,GAAG,EAACtC,CAAC;UAACuC,IAAI,EAACvB;QAAC;MAAC,CAAE,CAAC;IAAC,KAAI,OAAO;MAAC,OAAOA,CAAC,GAACyD,CAAC,CAACrE,CAAC,CAACmE,KAAK,EAAC,CAAC,EAACjE,CAAC,CAAC2D,KAAK,EAACrD,CAAC,EAACF,CAAC,CAACuD,KAAK,CAAC,EAAC,CAAC,CAAC,KAAGjD,CAAC,IAAE2D,CAAC,CAACvE,CAAC,CAACkC,GAAG,EAAClC,CAAC,CAACsE,MAAM,EAACpE,CAAC,CAACoE,MAAM,EAAC5D,CAAC,EAAC,CAAC,EAACJ,CAAC,CAACgE,MAAM,EAACxE,CAAC,CAAC,CAAC0E,GAAG,CAAE5E,CAAC,IAAE,CAAC,CAAC,KAAGA,CAAC,IAAE;QAACqC,KAAK,EAAC;UAACC,GAAG,EAACtC,CAAC;UAACuC,IAAI,EAACvB;QAAC;MAAC,CAAE,CAAC;EAAA;AAAC;AAAC,SAAS6D,CAACA,CAAC7E,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAAC;EAAC,OAAO,CAAC,KAAGA,CAAC,GAACU,CAAC,CAAClB,CAAC,EAAC,EAAE,EAAC,CAACE,CAAC,EAACE,CAAC,EAACE,CAAC,CAAC,CAAC,GAACY,CAAC,CAAClB,CAAC,EAAC,CAACE,CAAC,EAACE,CAAC,EAACE,CAAC,CAAC,EAAC,EAAE,CAAC;AAAA;AAAC,SAASwE,CAACA,CAAC9E,CAAC,EAACE,CAAC,EAACE,CAAC,EAAC;EAAC,OAAO,CAAC,KAAGA,CAAC,GAACJ,CAAC,GAACI,CAAC,IAAEJ,CAAC,GAACA,CAAC,GAACI,CAAC,IAAEF,CAAC;AAAA;AAAC,SAAS6E,CAACA,CAAC/E,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,GAAC,CAAC,EAAC;EAAC,QAAOV,CAAC;IAAE,KAAI,KAAK;MAAC,OAAOE,CAAC,GAACE,CAAC,GAACE,CAAC,GAAC,EAAE,GAACI,CAAC,GAACF,CAAC;IAAC,KAAI,KAAK;MAAC,OAAON,CAAC,GAACE,CAAC,GAACE,CAAC,GAACE,CAAC;IAAC,KAAI,OAAO;MAAC,OAAON,CAAC,GAACE,CAAC,GAACE,CAAC,GAACE,CAAC;EAAA;AAAC;AAAC,SAASwE,CAACA,CAAChF,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAAC;EAAC,MAAK,CAACE,CAAC,EAACE,CAAC,CAAC,GAACJ,CAAC;EAAC,QAAON,CAAC;IAAE,KAAI,KAAK;MAAC,OAAOE,CAAC,GAACM,CAAC,IAAEN,CAAC,GAACE,CAAC,GAACM,CAAC,IAAER,CAAC;IAAC,KAAI,KAAK;MAAC;QAAC,MAAMF,CAAC,GAACE,CAAC,GAACE,CAAC;QAAC,OAAOJ,CAAC,GAACQ,CAAC,IAAER,CAAC;MAAA;IAAC,KAAI,OAAO;MAAC,OAAOE,CAAC,GAACE,CAAC,GAACM,CAAC,IAAER,CAAC;EAAA;AAAC;AAAC,SAASyE,CAACA,CAAC3E,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAAC;EAAC,OAAM,CAAC,CAAC,EAAC,EAAE,EAAC,CAAC,CAAC,CAACgE,GAAG,CAAE9D,CAAC,IAAEkE,CAAC,CAACpE,CAAC,EAACmE,CAAC,CAACnE,CAAC,EAACZ,CAAC,EAACc,CAAC,EAACZ,CAAC,EAACI,CAAC,EAACF,CAAC,CAAC,EAACA,CAAC,EAAC,CAACI,CAAC,EAACE,CAAC,CAAC,CAAE,CAAC;AAAA;AAAC,SAAS+D,CAACA,CAACzE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,GAAC,CAAC,EAAC;EAAC,OAAOsE,CAAC,CAACD,CAAC,CAAC7E,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,CAAC,EAACR,CAAC,GAACM,CAAC,EAACE,CAAC,CAAC;AAAA;AAAC,SAAOqC,CAAC,IAAIoC,iBAAiB,EAAClB,CAAC,IAAImB,6BAA6B,EAACtC,CAAC,IAAIuC,6BAA6B,EAACJ,CAAC,IAAIK,oBAAoB,EAACP,CAAC,IAAIQ,mBAAmB,EAACZ,CAAC,IAAIa,8BAA8B,EAACrC,CAAC,IAAIsC,sBAAsB,EAAChC,CAAC,IAAIiC,eAAe,EAAChD,CAAC,IAAIiD,mBAAmB,EAAC9B,CAAC,IAAI+B,mBAAmB,EAAChC,CAAC,IAAIiC,kBAAkB,EAACtE,CAAC,IAAIuE,8BAA8B,EAAC9D,CAAC,IAAI+D,gBAAgB,EAACvB,CAAC,IAAIwB,iBAAiB,EAACnB,CAAC,IAAIoB,mCAAmC,EAAC5B,CAAC,IAAI6B,YAAY,EAACvE,CAAC,IAAIwE,gBAAgB,EAACjB,CAAC,IAAIkB,qBAAqB,EAACpB,CAAC,IAAIqB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}