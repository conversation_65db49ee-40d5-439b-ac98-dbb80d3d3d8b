{"ast": null, "code": "import { renderIcon as H } from \"../icon.renderer.js\";\nconst a = \"connect\",\n  o = [\"connect\", H({\n    outline: '<path d=\"M34,17H28.23A6.25,6.25,0,0,0,22,12H14.15a6.25,6.25,0,0,0-6.21,5H2v2H7.93a6.22,6.22,0,0,0,6.22,5H22a6.22,6.22,0,0,0,6.22-5H34ZM17.08,22H14.15a4.17,4.17,0,0,1-4.31-4,4.17,4.17,0,0,1,4.31-4h2.94ZM22,22H19V14h3a4.17,4.17,0,0,1,4.31,4A4.17,4.17,0,0,1,22,22Z\"/>',\n    solid: '<path d=\"M17,12H14.15a6.25,6.25,0,0,0-6.21,5H2v2H7.93a6.22,6.22,0,0,0,6.22,5H17Z\"/><path d=\"M28.23,17A6.25,6.25,0,0,0,22,12H19V24h3a6.22,6.22,0,0,0,6.22-5H34V17Z\"/>'\n  })];\nexport { o as connectIcon, a as connectIconName };", "map": {"version": 3, "names": ["renderIcon", "H", "a", "o", "outline", "solid", "connectIcon", "connectIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/connect.js"], "sourcesContent": ["import{renderIcon as H}from\"../icon.renderer.js\";const a=\"connect\",o=[\"connect\",H({outline:'<path d=\"M34,17H28.23A6.25,6.25,0,0,0,22,12H14.15a6.25,6.25,0,0,0-6.21,5H2v2H7.93a6.22,6.22,0,0,0,6.22,5H22a6.22,6.22,0,0,0,6.22-5H34ZM17.08,22H14.15a4.17,4.17,0,0,1-4.31-4,4.17,4.17,0,0,1,4.31-4h2.94ZM22,22H19V14h3a4.17,4.17,0,0,1,4.31,4A4.17,4.17,0,0,1,22,22Z\"/>',solid:'<path d=\"M17,12H14.15a6.25,6.25,0,0,0-6.21,5H2v2H7.93a6.22,6.22,0,0,0,6.22,5H17Z\"/><path d=\"M28.23,17A6.25,6.25,0,0,0,22,12H19V24h3a6.22,6.22,0,0,0,6.22-5H34V17Z\"/>'})];export{o as connectIcon,a as connectIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,SAAS;EAACC,CAAC,GAAC,CAAC,SAAS,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,0QAA0Q;IAACC,KAAK,EAAC;EAAsK,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,WAAW,EAACJ,CAAC,IAAIK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}