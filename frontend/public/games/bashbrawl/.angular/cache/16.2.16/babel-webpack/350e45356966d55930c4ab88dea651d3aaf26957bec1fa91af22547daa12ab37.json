{"ast": null, "code": "import { getCssPropertyValue as r } from \"../utils/css.js\";\nfunction t(t) {\n  return r(\"direction\", t);\n}\nexport { t as getElementLanguageDirection };", "map": {"version": 3, "names": ["getCssPropertyValue", "r", "t", "getElementLanguageDirection"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/i18n/utils.js"], "sourcesContent": ["import{getCssPropertyValue as r}from\"../utils/css.js\";function t(t){return r(\"direction\",t)}export{t as getElementLanguageDirection};\n"], "mappings": "AAAA,SAAOA,mBAAmB,IAAIC,CAAC,QAAK,iBAAiB;AAAC,SAASC,CAACA,CAACA,CAAC,EAAC;EAAC,OAAOD,CAAC,CAAC,WAAW,EAACC,CAAC,CAAC;AAAA;AAAC,SAAOA,CAAC,IAAIC,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}