{"ast": null, "code": "import equals from \"../equals.js\";\nexport default function _indexOf(list, a, idx) {\n  var inf, item; // Array.prototype.indexOf doesn't exist below IE9\n\n  if (typeof list.indexOf === 'function') {\n    switch (typeof a) {\n      case 'number':\n        if (a === 0) {\n          // manually crawl the list to distinguish between +0 and -0\n          inf = 1 / a;\n          while (idx < list.length) {\n            item = list[idx];\n            if (item === 0 && 1 / item === inf) {\n              return idx;\n            }\n            idx += 1;\n          }\n          return -1;\n        } else if (a !== a) {\n          // NaN\n          while (idx < list.length) {\n            item = list[idx];\n            if (typeof item === 'number' && item !== item) {\n              return idx;\n            }\n            idx += 1;\n          }\n          return -1;\n        } // non-zero numbers can utilise Set\n\n        return list.indexOf(a, idx);\n      // all these types can utilise Set\n\n      case 'string':\n      case 'boolean':\n      case 'function':\n      case 'undefined':\n        return list.indexOf(a, idx);\n      case 'object':\n        if (a === null) {\n          // null can utilise Set\n          return list.indexOf(a, idx);\n        }\n    }\n  } // anything else not covered above, defer to R.equals\n\n  while (idx < list.length) {\n    if (equals(list[idx], a)) {\n      return idx;\n    }\n    idx += 1;\n  }\n  return -1;\n}", "map": {"version": 3, "names": ["equals", "_indexOf", "list", "a", "idx", "inf", "item", "indexOf", "length"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_indexOf.js"], "sourcesContent": ["import equals from \"../equals.js\";\nexport default function _indexOf(list, a, idx) {\n  var inf, item; // Array.prototype.indexOf doesn't exist below IE9\n\n  if (typeof list.indexOf === 'function') {\n    switch (typeof a) {\n      case 'number':\n        if (a === 0) {\n          // manually crawl the list to distinguish between +0 and -0\n          inf = 1 / a;\n\n          while (idx < list.length) {\n            item = list[idx];\n\n            if (item === 0 && 1 / item === inf) {\n              return idx;\n            }\n\n            idx += 1;\n          }\n\n          return -1;\n        } else if (a !== a) {\n          // NaN\n          while (idx < list.length) {\n            item = list[idx];\n\n            if (typeof item === 'number' && item !== item) {\n              return idx;\n            }\n\n            idx += 1;\n          }\n\n          return -1;\n        } // non-zero numbers can utilise Set\n\n\n        return list.indexOf(a, idx);\n      // all these types can utilise Set\n\n      case 'string':\n      case 'boolean':\n      case 'function':\n      case 'undefined':\n        return list.indexOf(a, idx);\n\n      case 'object':\n        if (a === null) {\n          // null can utilise Set\n          return list.indexOf(a, idx);\n        }\n\n    }\n  } // anything else not covered above, defer to R.equals\n\n\n  while (idx < list.length) {\n    if (equals(list[idx], a)) {\n      return idx;\n    }\n\n    idx += 1;\n  }\n\n  return -1;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,eAAe,SAASC,QAAQA,CAACC,IAAI,EAAEC,CAAC,EAAEC,GAAG,EAAE;EAC7C,IAAIC,GAAG,EAAEC,IAAI,CAAC,CAAC;;EAEf,IAAI,OAAOJ,IAAI,CAACK,OAAO,KAAK,UAAU,EAAE;IACtC,QAAQ,OAAOJ,CAAC;MACd,KAAK,QAAQ;QACX,IAAIA,CAAC,KAAK,CAAC,EAAE;UACX;UACAE,GAAG,GAAG,CAAC,GAAGF,CAAC;UAEX,OAAOC,GAAG,GAAGF,IAAI,CAACM,MAAM,EAAE;YACxBF,IAAI,GAAGJ,IAAI,CAACE,GAAG,CAAC;YAEhB,IAAIE,IAAI,KAAK,CAAC,IAAI,CAAC,GAAGA,IAAI,KAAKD,GAAG,EAAE;cAClC,OAAOD,GAAG;YACZ;YAEAA,GAAG,IAAI,CAAC;UACV;UAEA,OAAO,CAAC,CAAC;QACX,CAAC,MAAM,IAAID,CAAC,KAAKA,CAAC,EAAE;UAClB;UACA,OAAOC,GAAG,GAAGF,IAAI,CAACM,MAAM,EAAE;YACxBF,IAAI,GAAGJ,IAAI,CAACE,GAAG,CAAC;YAEhB,IAAI,OAAOE,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAKA,IAAI,EAAE;cAC7C,OAAOF,GAAG;YACZ;YAEAA,GAAG,IAAI,CAAC;UACV;UAEA,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;;QAGF,OAAOF,IAAI,CAACK,OAAO,CAACJ,CAAC,EAAEC,GAAG,CAAC;MAC7B;;MAEA,KAAK,QAAQ;MACb,KAAK,SAAS;MACd,KAAK,UAAU;MACf,KAAK,WAAW;QACd,OAAOF,IAAI,CAACK,OAAO,CAACJ,CAAC,EAAEC,GAAG,CAAC;MAE7B,KAAK,QAAQ;QACX,IAAID,CAAC,KAAK,IAAI,EAAE;UACd;UACA,OAAOD,IAAI,CAACK,OAAO,CAACJ,CAAC,EAAEC,GAAG,CAAC;QAC7B;IAEJ;EACF,CAAC,CAAC;;EAGF,OAAOA,GAAG,GAAGF,IAAI,CAACM,MAAM,EAAE;IACxB,IAAIR,MAAM,CAACE,IAAI,CAACE,GAAG,CAAC,EAAED,CAAC,CAAC,EAAE;MACxB,OAAOC,GAAG;IACZ;IAEAA,GAAG,IAAI,CAAC;EACV;EAEA,OAAO,CAAC,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}