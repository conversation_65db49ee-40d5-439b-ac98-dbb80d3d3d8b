{"ast": null, "code": "import { ClarityIcons as i } from \"../icon.service.js\";\nimport { arrowMiniIcon as s } from \"../shapes/arrow-mini.js\";\nimport { calendarMiniIcon as r } from \"../shapes/calendar-mini.js\";\nimport { checkCircleMiniIcon as m } from \"../shapes/check-circle-mini.js\";\nimport { checkMiniIcon as o } from \"../shapes/check-mini.js\";\nimport { errorMiniIcon as e } from \"../shapes/error-mini.js\";\nimport { eventMiniIcon as n } from \"../shapes/event-mini.js\";\nimport { filterGridCircleMiniIcon as p } from \"../shapes/filter-grid-circle-mini.js\";\nimport { filterGridMiniIcon as t } from \"../shapes/filter-grid-mini.js\";\nimport { infoCircleMiniIcon as a, infoCircleMiniIconName as c } from \"../shapes/info-circle-mini.js\";\nimport { timesMiniIcon as f, timesMiniIconName as h } from \"../shapes/times-mini.js\";\nimport { warningMiniIcon as j } from \"../shapes/warning-mini.js\";\nconst l = [s, r, m, o, e, n, t, p, a, f, j],\n  d = [[h, [\"close-mini\"]], [c, [\"info-mini\"]]];\nfunction g() {\n  i.addIcons(...l), i.addAliases(...d);\n}\nexport { g as loadMiniIconSet, d as miniCollectionAliases, l as miniCollectionIcons };", "map": {"version": 3, "names": ["ClarityIcons", "i", "arrowMiniIcon", "s", "calendarMiniIcon", "r", "checkCircleMiniIcon", "m", "checkMiniIcon", "o", "errorMiniIcon", "e", "eventMiniIcon", "n", "filterGridCircleMiniIcon", "p", "filterGridMiniIcon", "t", "infoCircleMiniIcon", "a", "infoCircleMiniIconName", "c", "timesMiniIcon", "f", "timesMiniIconName", "h", "warningMiniIcon", "j", "l", "d", "g", "addIcons", "addAliases", "loadMiniIconSet", "miniCollectionAliases", "miniCollectionIcons"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/collections/mini.js"], "sourcesContent": ["import{ClarityIcons as i}from\"../icon.service.js\";import{arrowMiniIcon as s}from\"../shapes/arrow-mini.js\";import{calendarMiniIcon as r}from\"../shapes/calendar-mini.js\";import{checkCircleMiniIcon as m}from\"../shapes/check-circle-mini.js\";import{checkMiniIcon as o}from\"../shapes/check-mini.js\";import{errorMiniIcon as e}from\"../shapes/error-mini.js\";import{eventMiniIcon as n}from\"../shapes/event-mini.js\";import{filterGridCircleMiniIcon as p}from\"../shapes/filter-grid-circle-mini.js\";import{filterGridMiniIcon as t}from\"../shapes/filter-grid-mini.js\";import{infoCircleMiniIcon as a,infoCircleMiniIconName as c}from\"../shapes/info-circle-mini.js\";import{timesMiniIcon as f,timesMiniIconName as h}from\"../shapes/times-mini.js\";import{warningMiniIcon as j}from\"../shapes/warning-mini.js\";const l=[s,r,m,o,e,n,t,p,a,f,j],d=[[h,[\"close-mini\"]],[c,[\"info-mini\"]]];function g(){i.addIcons(...l),i.addAliases(...d)}export{g as loadMiniIconSet,d as miniCollectionAliases,l as miniCollectionIcons};\n"], "mappings": "AAAA,SAAOA,YAAY,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,gCAAgC;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,wBAAwB,IAAIC,CAAC,QAAK,sCAAsC;AAAC,SAAOC,kBAAkB,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,kBAAkB,IAAIC,CAAC,EAACC,sBAAsB,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,aAAa,IAAIC,CAAC,EAACC,iBAAiB,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,MAAMC,CAAC,GAAC,CAACzB,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACF,CAAC,EAACI,CAAC,EAACI,CAAC,EAACI,CAAC,CAAC;EAACE,CAAC,GAAC,CAAC,CAACJ,CAAC,EAAC,CAAC,YAAY,CAAC,CAAC,EAAC,CAACJ,CAAC,EAAC,CAAC,WAAW,CAAC,CAAC,CAAC;AAAC,SAASS,CAACA,CAAA,EAAE;EAAC7B,CAAC,CAAC8B,QAAQ,CAAC,GAAGH,CAAC,CAAC,EAAC3B,CAAC,CAAC+B,UAAU,CAAC,GAAGH,CAAC,CAAC;AAAA;AAAC,SAAOC,CAAC,IAAIG,eAAe,EAACJ,CAAC,IAAIK,qBAAqB,EAACN,CAAC,IAAIO,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}