{"ast": null, "code": "import _createReduce from \"./_createReduce.js\";\nimport _xArrayReduce from \"./_xArrayReduce.js\";\nimport bind from \"../bind.js\";\nfunction _xIterableReduce(xf, acc, iter) {\n  var step = iter.next();\n  while (!step.done) {\n    acc = xf['@@transducer/step'](acc, step.value);\n    if (acc && acc['@@transducer/reduced']) {\n      acc = acc['@@transducer/value'];\n      break;\n    }\n    step = iter.next();\n  }\n  return xf['@@transducer/result'](acc);\n}\nfunction _xMethodReduce(xf, acc, obj, methodName) {\n  return xf['@@transducer/result'](obj[methodName](bind(xf['@@transducer/step'], xf), acc));\n}\nvar _xReduce = /*#__PURE__*/\n_createReduce(_xArrayReduce, _xMethodReduce, _xIterableReduce);\nexport default _xReduce;", "map": {"version": 3, "names": ["_createReduce", "_xArrayReduce", "bind", "_xIterableReduce", "xf", "acc", "iter", "step", "next", "done", "value", "_xMethodReduce", "obj", "methodName", "_xReduce"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_xReduce.js"], "sourcesContent": ["import _createReduce from \"./_createReduce.js\";\nimport _xArrayReduce from \"./_xArrayReduce.js\";\nimport bind from \"../bind.js\";\n\nfunction _xIterableReduce(xf, acc, iter) {\n  var step = iter.next();\n\n  while (!step.done) {\n    acc = xf['@@transducer/step'](acc, step.value);\n\n    if (acc && acc['@@transducer/reduced']) {\n      acc = acc['@@transducer/value'];\n      break;\n    }\n\n    step = iter.next();\n  }\n\n  return xf['@@transducer/result'](acc);\n}\n\nfunction _xMethodReduce(xf, acc, obj, methodName) {\n  return xf['@@transducer/result'](obj[methodName](bind(xf['@@transducer/step'], xf), acc));\n}\n\nvar _xReduce =\n/*#__PURE__*/\n_createReduce(_xArrayReduce, _xMethodReduce, _xIterableReduce);\n\nexport default _xReduce;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,IAAI,MAAM,YAAY;AAE7B,SAASC,gBAAgBA,CAACC,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACvC,IAAIC,IAAI,GAAGD,IAAI,CAACE,IAAI,CAAC,CAAC;EAEtB,OAAO,CAACD,IAAI,CAACE,IAAI,EAAE;IACjBJ,GAAG,GAAGD,EAAE,CAAC,mBAAmB,CAAC,CAACC,GAAG,EAAEE,IAAI,CAACG,KAAK,CAAC;IAE9C,IAAIL,GAAG,IAAIA,GAAG,CAAC,sBAAsB,CAAC,EAAE;MACtCA,GAAG,GAAGA,GAAG,CAAC,oBAAoB,CAAC;MAC/B;IACF;IAEAE,IAAI,GAAGD,IAAI,CAACE,IAAI,CAAC,CAAC;EACpB;EAEA,OAAOJ,EAAE,CAAC,qBAAqB,CAAC,CAACC,GAAG,CAAC;AACvC;AAEA,SAASM,cAAcA,CAACP,EAAE,EAAEC,GAAG,EAAEO,GAAG,EAAEC,UAAU,EAAE;EAChD,OAAOT,EAAE,CAAC,qBAAqB,CAAC,CAACQ,GAAG,CAACC,UAAU,CAAC,CAACX,IAAI,CAACE,EAAE,CAAC,mBAAmB,CAAC,EAAEA,EAAE,CAAC,EAAEC,GAAG,CAAC,CAAC;AAC3F;AAEA,IAAIS,QAAQ,GACZ;AACAd,aAAa,CAACC,aAAa,EAAEU,cAAc,EAAER,gBAAgB,CAAC;AAE9D,eAAeW,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}