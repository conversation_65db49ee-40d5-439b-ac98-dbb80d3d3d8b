{"ast": null, "code": "import _includes from \"./internal/_includes.js\";\nimport _curry2 from \"./internal/_curry2.js\";\n/**\n * Returns `true` if the specified value is equal, in [`<PERSON>.equals`](#equals)\n * terms, to at least one element of the given list; `false` otherwise.\n * Also works with strings.\n *\n * @func\n * @memberOf R\n * @since v0.26.0\n * @category List\n * @sig a -> [a] -> Boolean\n * @param {Object} a The item to compare against.\n * @param {Array} list The array to consider.\n * @return {Boolean} `true` if an equivalent item is in the list, `false` otherwise.\n * @see R.any\n * @example\n *\n *      R.includes(3, [1, 2, 3]); //=> true\n *      R.includes(4, [1, 2, 3]); //=> false\n *      R.includes({ name: '<PERSON>' }, [{ name: '<PERSON>' }]); //=> true\n *      R.includes([42], [[42]]); //=> true\n *      R.includes('ba', 'banana'); //=>true\n */\n\nvar includes = /*#__PURE__*/\n_curry2(_includes);\nexport default includes;", "map": {"version": 3, "names": ["_includes", "_curry2", "includes"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/includes.js"], "sourcesContent": ["import _includes from \"./internal/_includes.js\";\nimport _curry2 from \"./internal/_curry2.js\";\n/**\n * Returns `true` if the specified value is equal, in [`<PERSON>.equals`](#equals)\n * terms, to at least one element of the given list; `false` otherwise.\n * Also works with strings.\n *\n * @func\n * @memberOf R\n * @since v0.26.0\n * @category List\n * @sig a -> [a] -> Boolean\n * @param {Object} a The item to compare against.\n * @param {Array} list The array to consider.\n * @return {Boolean} `true` if an equivalent item is in the list, `false` otherwise.\n * @see R.any\n * @example\n *\n *      R.includes(3, [1, 2, 3]); //=> true\n *      R.includes(4, [1, 2, 3]); //=> false\n *      R.includes({ name: '<PERSON>' }, [{ name: '<PERSON>' }]); //=> true\n *      R.includes([42], [[42]]); //=> true\n *      R.includes('ba', 'banana'); //=>true\n */\n\nvar includes =\n/*#__PURE__*/\n_curry2(_includes);\n\nexport default includes;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,OAAO,MAAM,uBAAuB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,QAAQ,GACZ;AACAD,OAAO,CAACD,SAAS,CAAC;AAElB,eAAeE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}