{"ast": null, "code": "export default {\n  init: function () {\n    return this.xf['@@transducer/init']();\n  },\n  result: function (result) {\n    return this.xf['@@transducer/result'](result);\n  }\n};", "map": {"version": 3, "names": ["init", "xf", "result"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_xfBase.js"], "sourcesContent": ["export default {\n  init: function () {\n    return this.xf['@@transducer/init']();\n  },\n  result: function (result) {\n    return this.xf['@@transducer/result'](result);\n  }\n};"], "mappings": "AAAA,eAAe;EACbA,IAAI,EAAE,SAAAA,CAAA,EAAY;IAChB,OAAO,IAAI,CAACC,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAAC;EACvC,CAAC;EACDC,MAAM,EAAE,SAAAA,CAAUA,MAAM,EAAE;IACxB,OAAO,IAAI,CAACD,EAAE,CAAC,qBAAqB,CAAC,CAACC,MAAM,CAAC;EAC/C;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}