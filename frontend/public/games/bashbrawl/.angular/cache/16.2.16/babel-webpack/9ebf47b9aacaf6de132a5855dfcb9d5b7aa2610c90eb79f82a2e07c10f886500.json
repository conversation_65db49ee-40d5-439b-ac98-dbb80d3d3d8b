{"ast": null, "code": "import e from \"ramda/es/curryN\";\nimport { isBrowser as t } from \"./environment.js\";\nimport { elementExists as r, existsInWindow as o } from \"./exists.js\";\nimport { setupCDSGlobal as s } from \"./global.js\";\nimport { isStorybook as m } from \"./framework.js\";\nimport { LogService as i } from \"../services/log.service.js\";\nconst n = e(3, (e, t, o) => {\n  r(e) && !m() ? i.warn(e + \" has already been registered\") : (o.define(e, t), s(), window && !Object.keys(window.CDS._state.elementRegistry).some(t => t === e) && (window.CDS._state.elementRegistry = {\n    ...window.CDS._state.elementRegistry,\n    [e]: {}\n  }));\n});\nfunction w(e, r) {\n  t() && o([\"customElements\"]) && n(e, r, window.customElements);\n}\nexport { w as registerElementSafely };", "map": {"version": 3, "names": ["e", "<PERSON><PERSON><PERSON><PERSON>", "t", "elementExists", "r", "existsInWindow", "o", "setupCDSGlobal", "s", "isStorybook", "m", "LogService", "i", "n", "warn", "define", "window", "Object", "keys", "CDS", "_state", "elementRegistry", "some", "w", "customElements", "registerElementSafely"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/registration.js"], "sourcesContent": ["import e from\"ramda/es/curryN\";import{isBrowser as t}from\"./environment.js\";import{elementExists as r,existsInWindow as o}from\"./exists.js\";import{setupCDSGlobal as s}from\"./global.js\";import{isStorybook as m}from\"./framework.js\";import{LogService as i}from\"../services/log.service.js\";const n=e(3,((e,t,o)=>{r(e)&&!m()?i.warn(e+\" has already been registered\"):(o.define(e,t),s(),window&&!Object.keys(window.CDS._state.elementRegistry).some((t=>t===e))&&(window.CDS._state.elementRegistry={...window.CDS._state.elementRegistry,[e]:{}}))}));function w(e,r){t()&&o([\"customElements\"])&&n(e,r,window.customElements)}export{w as registerElementSafely};\n"], "mappings": "AAAA,OAAOA,CAAC,MAAK,iBAAiB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAAOC,aAAa,IAAIC,CAAC,EAACC,cAAc,IAAIC,CAAC,QAAK,aAAa;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,aAAa;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,4BAA4B;AAAC,MAAMC,CAAC,GAACb,CAAC,CAAC,CAAC,EAAE,CAACA,CAAC,EAACE,CAAC,EAACI,CAAC,KAAG;EAACF,CAAC,CAACJ,CAAC,CAAC,IAAE,CAACU,CAAC,CAAC,CAAC,GAACE,CAAC,CAACE,IAAI,CAACd,CAAC,GAAC,8BAA8B,CAAC,IAAEM,CAAC,CAACS,MAAM,CAACf,CAAC,EAACE,CAAC,CAAC,EAACM,CAAC,CAAC,CAAC,EAACQ,MAAM,IAAE,CAACC,MAAM,CAACC,IAAI,CAACF,MAAM,CAACG,GAAG,CAACC,MAAM,CAACC,eAAe,CAAC,CAACC,IAAI,CAAEpB,CAAC,IAAEA,CAAC,KAAGF,CAAE,CAAC,KAAGgB,MAAM,CAACG,GAAG,CAACC,MAAM,CAACC,eAAe,GAAC;IAAC,GAAGL,MAAM,CAACG,GAAG,CAACC,MAAM,CAACC,eAAe;IAAC,CAACrB,CAAC,GAAE,CAAC;EAAC,CAAC,CAAC,CAAC;AAAA,CAAE,CAAC;AAAC,SAASuB,CAACA,CAACvB,CAAC,EAACI,CAAC,EAAC;EAACF,CAAC,CAAC,CAAC,IAAEI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,IAAEO,CAAC,CAACb,CAAC,EAACI,CAAC,EAACY,MAAM,CAACQ,cAAc,CAAC;AAAA;AAAC,SAAOD,CAAC,IAAIE,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}