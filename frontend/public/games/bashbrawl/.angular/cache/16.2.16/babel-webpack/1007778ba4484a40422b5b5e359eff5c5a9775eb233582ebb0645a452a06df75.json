{"ast": null, "code": "import { arrayTail as t } from \"../utils/array.js\";\nimport { setAttributes as e } from \"../utils/dom.js\";\nimport { GlobalStateService as s } from \"./global.service.js\";\nconst r = \"cds-focus-trap\";\nclass a {\n  static getTrapElements() {\n    return [...s.state.focusTrapItems];\n  }\n  static setTrapElements(t) {\n    const r = document.querySelector(\"html\");\n    null !== r && e(r, [\"cds-focus-trap\", !!t.length && \"\"]), s.state.focusTrapItems = [...t];\n  }\n  static removeTrapElement(t) {\n    this.getTrapElements().length < 1 || this.setTrapElements([...this.getTrapElements().filter(e => e.focusTrapId !== t.focusTrapId)]);\n  }\n  static setCurrent(t) {\n    t?.focusTrapId && this.setTrapElements([...this.getTrapElements().filter(e => e.focusTrapId !== t.focusTrapId), t]);\n  }\n  static activatePreviousCurrent() {\n    this.setTrapElements([...this.getTrapElements()].slice(0, -1));\n  }\n  static getCurrent() {\n    return t(this.getTrapElements()) || null;\n  }\n}\nexport { r as CDS_FOCUS_TRAP_DOCUMENT_ATTR, a as FocusTrapTrackerService };", "map": {"version": 3, "names": ["arrayTail", "t", "setAttributes", "e", "GlobalStateService", "s", "r", "a", "getTrapElements", "state", "focusTrapItems", "setTrapElements", "document", "querySelector", "length", "removeTrapElement", "filter", "focusTrapId", "setCurrent", "activatePreviousCurrent", "slice", "get<PERSON>urrent", "CDS_FOCUS_TRAP_DOCUMENT_ATTR", "FocusTrapTrackerService"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/services/focus-trap-tracker.service.js"], "sourcesContent": ["import{arrayTail as t}from\"../utils/array.js\";import{setAttributes as e}from\"../utils/dom.js\";import{GlobalStateService as s}from\"./global.service.js\";const r=\"cds-focus-trap\";class a{static getTrapElements(){return[...s.state.focusTrapItems]}static setTrapElements(t){const r=document.querySelector(\"html\");null!==r&&e(r,[\"cds-focus-trap\",!!t.length&&\"\"]),s.state.focusTrapItems=[...t]}static removeTrapElement(t){this.getTrapElements().length<1||this.setTrapElements([...this.getTrapElements().filter((e=>e.focusTrapId!==t.focusTrapId))])}static setCurrent(t){t?.focusTrapId&&this.setTrapElements([...this.getTrapElements().filter((e=>e.focusTrapId!==t.focusTrapId)),t])}static activatePreviousCurrent(){this.setTrapElements([...this.getTrapElements()].slice(0,-1))}static getCurrent(){return t(this.getTrapElements())||null}}export{r as CDS_FOCUS_TRAP_DOCUMENT_ATTR,a as FocusTrapTrackerService};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,iBAAiB;AAAC,SAAOC,kBAAkB,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,gBAAgB;AAAC,MAAMC,CAAC;EAAC,OAAOC,eAAeA,CAAA,EAAE;IAAC,OAAM,CAAC,GAAGH,CAAC,CAACI,KAAK,CAACC,cAAc,CAAC;EAAA;EAAC,OAAOC,eAAeA,CAACV,CAAC,EAAC;IAAC,MAAMK,CAAC,GAACM,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAAC,IAAI,KAAGP,CAAC,IAAEH,CAAC,CAACG,CAAC,EAAC,CAAC,gBAAgB,EAAC,CAAC,CAACL,CAAC,CAACa,MAAM,IAAE,EAAE,CAAC,CAAC,EAACT,CAAC,CAACI,KAAK,CAACC,cAAc,GAAC,CAAC,GAAGT,CAAC,CAAC;EAAA;EAAC,OAAOc,iBAAiBA,CAACd,CAAC,EAAC;IAAC,IAAI,CAACO,eAAe,CAAC,CAAC,CAACM,MAAM,GAAC,CAAC,IAAE,IAAI,CAACH,eAAe,CAAC,CAAC,GAAG,IAAI,CAACH,eAAe,CAAC,CAAC,CAACQ,MAAM,CAAEb,CAAC,IAAEA,CAAC,CAACc,WAAW,KAAGhB,CAAC,CAACgB,WAAY,CAAC,CAAC,CAAC;EAAA;EAAC,OAAOC,UAAUA,CAACjB,CAAC,EAAC;IAACA,CAAC,EAAEgB,WAAW,IAAE,IAAI,CAACN,eAAe,CAAC,CAAC,GAAG,IAAI,CAACH,eAAe,CAAC,CAAC,CAACQ,MAAM,CAAEb,CAAC,IAAEA,CAAC,CAACc,WAAW,KAAGhB,CAAC,CAACgB,WAAY,CAAC,EAAChB,CAAC,CAAC,CAAC;EAAA;EAAC,OAAOkB,uBAAuBA,CAAA,EAAE;IAAC,IAAI,CAACR,eAAe,CAAC,CAAC,GAAG,IAAI,CAACH,eAAe,CAAC,CAAC,CAAC,CAACY,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC;EAAA;EAAC,OAAOC,UAAUA,CAAA,EAAE;IAAC,OAAOpB,CAAC,CAAC,IAAI,CAACO,eAAe,CAAC,CAAC,CAAC,IAAE,IAAI;EAAA;AAAC;AAAC,SAAOF,CAAC,IAAIgB,4BAA4B,EAACf,CAAC,IAAIgB,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}