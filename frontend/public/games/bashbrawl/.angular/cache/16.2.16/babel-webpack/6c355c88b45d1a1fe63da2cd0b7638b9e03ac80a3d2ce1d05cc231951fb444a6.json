{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"wallet\",\n  e = [\"wallet\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 15H33C33.55 15 34 15.45 34 16V24C34 24.55 33.55 25 33 25H32V31C32 31.55 31.55 32 31 32H8.13C7.04 32 6 31.58 5.22 30.82C4.45 30.06 4.01 29.02 4 27.93V7.36C4 7.24 4 7.12 4 7C4 5.34 5.35 4 7 4H30.58C31.13 4 31.58 4.45 31.58 5C31.58 5.55 31.13 6 30.58 6H7C6.51 6 6.09 6.34 6 6.82V7.18C6.09 7.66 6.51 8.01 7 8H31C31.55 8 32 8.45 32 9V15ZM25.51 20C25.51 20.83 24.84 21.5 24.01 21.5C23.18 21.5 22.51 20.83 22.51 20C22.51 19.17 23.18 18.5 24.01 18.5C24.84 18.5 25.51 19.17 25.51 20ZM30 30H8.13C6.97 30.01 6.02 29.09 6 27.93V9.88C6.32 9.97 6.66 10.01 7 10H30V15H23C20.24 15 18 17.24 18 20C18 22.76 20.24 25 23 25H30V30ZM23 23H32V17H23C21.34 17 20 18.34 20 20C20 21.66 21.34 23 23 23Z\"/>',\n    solid: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M33.0303 14H31.1493V9.00002C31.1493 8.44773 30.7151 8.00002 30.1796 8.00002H6.90885C6.37334 8.00002 5.93923 7.5523 5.93923 7.00002C5.93923 6.44773 6.37334 6.00002 6.90885 6.00002H29.7918C30.3273 6.00002 30.7614 5.5523 30.7614 5.00002C30.7614 4.44773 30.3273 4.00002 29.7918 4.00002H6.90885C6.15276 3.98394 5.42146 4.27837 4.87591 4.81851C4.33036 5.35865 4.01527 6.10023 4 6.88002V27.88C4.00256 28.9771 4.42826 30.0281 5.18317 30.801C5.93809 31.5739 6.96017 32.0053 8.02391 32H30.1796C30.7151 32 31.1493 31.5523 31.1493 31V26H33.0303C33.2777 26.0193 33.5218 25.9327 33.7052 25.7605C33.8887 25.5883 33.9953 25.3458 33.9999 25.09V15.09C34.0066 14.5196 33.5819 14.0422 33.0303 14ZM32.1189 24H23.7996C21.7305 23.924 20.112 22.134 20.1829 20C20.112 17.866 21.7305 16.076 23.7996 16H32.1189V24ZM25.8551 19.92C25.8551 20.7485 25.204 21.42 24.4007 21.42C23.5974 21.42 22.9463 20.7485 22.9463 19.92C22.9463 19.0916 23.5974 18.42 24.4007 18.42C25.204 18.42 25.8551 19.0916 25.8551 19.92Z\"/>'\n  })];\nexport { e as walletIcon, H as walletIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "e", "outline", "solid", "walletIcon", "walletIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/wallet.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"wallet\",e=[\"wallet\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 15H33C33.55 15 34 15.45 34 16V24C34 24.55 33.55 25 33 25H32V31C32 31.55 31.55 32 31 32H8.13C7.04 32 6 31.58 5.22 30.82C4.45 30.06 4.01 29.02 4 27.93V7.36C4 7.24 4 7.12 4 7C4 5.34 5.35 4 7 4H30.58C31.13 4 31.58 4.45 31.58 5C31.58 5.55 31.13 6 30.58 6H7C6.51 6 6.09 6.34 6 6.82V7.18C6.09 7.66 6.51 8.01 7 8H31C31.55 8 32 8.45 32 9V15ZM25.51 20C25.51 20.83 24.84 21.5 24.01 21.5C23.18 21.5 22.51 20.83 22.51 20C22.51 19.17 23.18 18.5 24.01 18.5C24.84 18.5 25.51 19.17 25.51 20ZM30 30H8.13C6.97 30.01 6.02 29.09 6 27.93V9.88C6.32 9.97 6.66 10.01 7 10H30V15H23C20.24 15 18 17.24 18 20C18 22.76 20.24 25 23 25H30V30ZM23 23H32V17H23C21.34 17 20 18.34 20 20C20 21.66 21.34 23 23 23Z\"/>',solid:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M33.0303 14H31.1493V9.00002C31.1493 8.44773 30.7151 8.00002 30.1796 8.00002H6.90885C6.37334 8.00002 5.93923 7.5523 5.93923 7.00002C5.93923 6.44773 6.37334 6.00002 6.90885 6.00002H29.7918C30.3273 6.00002 30.7614 5.5523 30.7614 5.00002C30.7614 4.44773 30.3273 4.00002 29.7918 4.00002H6.90885C6.15276 3.98394 5.42146 4.27837 4.87591 4.81851C4.33036 5.35865 4.01527 6.10023 4 6.88002V27.88C4.00256 28.9771 4.42826 30.0281 5.18317 30.801C5.93809 31.5739 6.96017 32.0053 8.02391 32H30.1796C30.7151 32 31.1493 31.5523 31.1493 31V26H33.0303C33.2777 26.0193 33.5218 25.9327 33.7052 25.7605C33.8887 25.5883 33.9953 25.3458 33.9999 25.09V15.09C34.0066 14.5196 33.5819 14.0422 33.0303 14ZM32.1189 24H23.7996C21.7305 23.924 20.112 22.134 20.1829 20C20.112 17.866 21.7305 16.076 23.7996 16H32.1189V24ZM25.8551 19.92C25.8551 20.7485 25.204 21.42 24.4007 21.42C23.5974 21.42 22.9463 20.7485 22.9463 19.92C22.9463 19.0916 23.5974 18.42 24.4007 18.42C25.204 18.42 25.8551 19.0916 25.8551 19.92Z\"/>'})];export{e as walletIcon,H as walletIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,4tBAA4tB;IAACC,KAAK,EAAC;EAAsgC,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,UAAU,EAACJ,CAAC,IAAIK,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}