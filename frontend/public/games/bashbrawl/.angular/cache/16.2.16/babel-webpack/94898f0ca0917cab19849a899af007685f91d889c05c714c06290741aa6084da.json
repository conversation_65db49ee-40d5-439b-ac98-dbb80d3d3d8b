{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"camera\",\n  e = [\"camera\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 8H24.7L23.64 5.28C23.3432 4.5107 22.6046 4.00244 21.78 4H14.22C13.3918 3.99831 12.6482 4.50729 12.35 5.28L11.3 8H4C2.89543 8 2 8.89543 2 10V30C2 31.1046 2.89543 32 4 32H32C33.1046 32 34 31.1046 34 30V10C34 8.89543 33.1046 8 32 8ZM9 19C9 23.9706 13.0294 28 18 28C22.9706 28 27 23.9706 27 19C27 14.0294 22.9706 10 18 10C13.0294 10 9 14.0294 9 19ZM8.57 12.03C9.01183 12.03 9.37 12.3882 9.37 12.83C9.37 13.2718 9.01183 13.63 8.57 13.63H6.17C5.72817 13.63 5.37 13.2718 5.37 12.83C5.37 12.3882 5.72817 12.03 6.17 12.03H8.57ZM15.58 24C13.6245 23.0995 12.363 21.1528 12.34 19C12.3245 16.8493 13.5508 14.8826 15.4888 13.95C17.4268 13.0173 19.7289 13.2861 21.4 14.64L20.54 16.02C19.3754 14.846 17.6085 14.5097 16.0941 15.1737C14.5796 15.8377 13.6299 17.3651 13.7043 19.0171C13.7787 20.669 14.862 22.1049 16.43 22.63L15.58 24ZM18 26.4C22.0869 26.4 25.4 23.0869 25.4 19C25.3945 14.9154 22.0846 11.6055 18 11.6C13.9131 11.6 10.6 14.9131 10.6 19C10.6 23.0869 13.9131 26.4 18 26.4ZM4 30H32V10H23.33L21.78 6H14.22L12.67 10H4V30Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 8H24.7L23.64 5.28C23.3432 4.5107 22.6046 4.00244 21.78 4H14.22C13.3918 3.99831 12.6482 4.50729 12.35 5.28L11.3 8H4C2.89543 8 2 8.89543 2 10V30C2 31.1046 2.89543 32 4 32H32C33.1046 32 34 31.1046 34 30V10C34 8.89543 33.1046 8 32 8ZM9 19C9 23.9706 13.0294 28 18 28C22.9706 28 27 23.9706 27 19C27 14.0294 22.9706 10 18 10C13.0294 10 9 14.0294 9 19ZM8.57 12.03C9.01183 12.03 9.37 12.3882 9.37 12.83C9.37 13.2718 9.01183 13.63 8.57 13.63H6.17C5.72817 13.63 5.37 13.2718 5.37 12.83C5.37 12.3882 5.72817 12.03 6.17 12.03H8.57ZM15.58 24C13.6245 23.0995 12.363 21.1528 12.34 19C12.3245 16.8493 13.5508 14.8826 15.4888 13.95C17.4268 13.0173 19.7289 13.2861 21.4 14.64L20.54 16.02C19.3754 14.846 17.6085 14.5097 16.0941 15.1737C14.5796 15.8377 13.6299 17.3651 13.7043 19.0171C13.7787 20.669 14.862 22.1049 16.43 22.63L15.58 24ZM18 26.4C22.0869 26.4 25.4 23.0869 25.4 19C25.3945 14.9154 22.0846 11.6055 18 11.6C13.9131 11.6 10.6 14.9131 10.6 19C10.6 23.0869 13.9131 26.4 18 26.4ZM4 30H32V10H23.33L21.78 6H14.22L12.67 10H4V30Z\"/>',\n    solid: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.7 8H32C33.1046 8 34 8.89543 34 10V30C34 31.1046 33.1046 32 32 32H4C2.89543 32 2 31.1046 2 30V10C2 8.89543 2.89543 8 4 8H11.3L12.35 5.28C12.6482 4.50729 13.3918 3.99831 14.22 4H21.78C22.6046 4.00244 23.3432 4.5107 23.64 5.28L24.7 8ZM15.22 25.47C12.7181 24.3166 11.1141 21.8149 11.11 19.06C11.1104 16.3371 12.6766 13.8571 15.1351 12.6866C17.5936 11.5161 20.5061 11.8637 22.62 13.58L21.53 15.34C20.036 14.0539 17.9258 13.7656 16.1417 14.6039C14.3575 15.4422 13.2324 17.2505 13.2687 19.2215C13.3051 21.1924 14.4961 22.9581 16.31 23.73L15.22 25.47ZM6.17 13.63C5.72817 13.63 5.37 13.2718 5.37 12.83C5.37 12.3882 5.72817 12.03 6.17 12.03H8.57C9.01183 12.03 9.37 12.3882 9.37 12.83C9.37 13.2718 9.01183 13.63 8.57 13.63H6.17ZM9 19C9 23.9706 13.0294 28 18 28C20.3869 28 22.6761 27.0518 24.364 25.364C26.0518 23.6761 27 21.387 27 19C27 14.0294 22.9706 10 18 10C13.0294 10 9 14.0294 9 19Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.7 8H32C33.1046 8 34 8.89543 34 10V30C34 31.1046 33.1046 32 32 32H4C2.89543 32 2 31.1046 2 30V10C2 8.89543 2.89543 8 4 8H11.3L12.35 5.28C12.6482 4.50729 13.3918 3.99831 14.22 4H21.78C22.6046 4.00244 23.3432 4.5107 23.64 5.28L24.7 8ZM15.22 25.47C12.7181 24.3166 11.1141 21.8149 11.11 19.06C11.1104 16.3371 12.6766 13.8571 15.1351 12.6866C17.5936 11.5161 20.5061 11.8637 22.62 13.58L21.53 15.34C20.036 14.0539 17.9258 13.7656 16.1417 14.6039C14.3575 15.4422 13.2324 17.2505 13.2687 19.2215C13.3051 21.1924 14.4961 22.9581 16.31 23.73L15.22 25.47ZM6.17 13.63C5.72817 13.63 5.37 13.2718 5.37 12.83C5.37 12.3882 5.72817 12.03 6.17 12.03H8.57C9.01183 12.03 9.37 12.3882 9.37 12.83C9.37 13.2718 9.01183 13.63 8.57 13.63H6.17ZM9 19C9 23.9706 13.0294 28 18 28C20.3869 28 22.6761 27.0518 24.364 25.364C26.0518 23.6761 27 21.387 27 19C27 14.0294 22.9706 10 18 10C13.0294 10 9 14.0294 9 19Z\"/>'\n  })];\nexport { e as cameraIcon, H as cameraIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "e", "outline", "solid", "cameraIcon", "cameraIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/camera.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"camera\",e=[\"camera\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 8H24.7L23.64 5.28C23.3432 4.5107 22.6046 4.00244 21.78 4H14.22C13.3918 3.99831 12.6482 4.50729 12.35 5.28L11.3 8H4C2.89543 8 2 8.89543 2 10V30C2 31.1046 2.89543 32 4 32H32C33.1046 32 34 31.1046 34 30V10C34 8.89543 33.1046 8 32 8ZM9 19C9 23.9706 13.0294 28 18 28C22.9706 28 27 23.9706 27 19C27 14.0294 22.9706 10 18 10C13.0294 10 9 14.0294 9 19ZM8.57 12.03C9.01183 12.03 9.37 12.3882 9.37 12.83C9.37 13.2718 9.01183 13.63 8.57 13.63H6.17C5.72817 13.63 5.37 13.2718 5.37 12.83C5.37 12.3882 5.72817 12.03 6.17 12.03H8.57ZM15.58 24C13.6245 23.0995 12.363 21.1528 12.34 19C12.3245 16.8493 13.5508 14.8826 15.4888 13.95C17.4268 13.0173 19.7289 13.2861 21.4 14.64L20.54 16.02C19.3754 14.846 17.6085 14.5097 16.0941 15.1737C14.5796 15.8377 13.6299 17.3651 13.7043 19.0171C13.7787 20.669 14.862 22.1049 16.43 22.63L15.58 24ZM18 26.4C22.0869 26.4 25.4 23.0869 25.4 19C25.3945 14.9154 22.0846 11.6055 18 11.6C13.9131 11.6 10.6 14.9131 10.6 19C10.6 23.0869 13.9131 26.4 18 26.4ZM4 30H32V10H23.33L21.78 6H14.22L12.67 10H4V30Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 8H24.7L23.64 5.28C23.3432 4.5107 22.6046 4.00244 21.78 4H14.22C13.3918 3.99831 12.6482 4.50729 12.35 5.28L11.3 8H4C2.89543 8 2 8.89543 2 10V30C2 31.1046 2.89543 32 4 32H32C33.1046 32 34 31.1046 34 30V10C34 8.89543 33.1046 8 32 8ZM9 19C9 23.9706 13.0294 28 18 28C22.9706 28 27 23.9706 27 19C27 14.0294 22.9706 10 18 10C13.0294 10 9 14.0294 9 19ZM8.57 12.03C9.01183 12.03 9.37 12.3882 9.37 12.83C9.37 13.2718 9.01183 13.63 8.57 13.63H6.17C5.72817 13.63 5.37 13.2718 5.37 12.83C5.37 12.3882 5.72817 12.03 6.17 12.03H8.57ZM15.58 24C13.6245 23.0995 12.363 21.1528 12.34 19C12.3245 16.8493 13.5508 14.8826 15.4888 13.95C17.4268 13.0173 19.7289 13.2861 21.4 14.64L20.54 16.02C19.3754 14.846 17.6085 14.5097 16.0941 15.1737C14.5796 15.8377 13.6299 17.3651 13.7043 19.0171C13.7787 20.669 14.862 22.1049 16.43 22.63L15.58 24ZM18 26.4C22.0869 26.4 25.4 23.0869 25.4 19C25.3945 14.9154 22.0846 11.6055 18 11.6C13.9131 11.6 10.6 14.9131 10.6 19C10.6 23.0869 13.9131 26.4 18 26.4ZM4 30H32V10H23.33L21.78 6H14.22L12.67 10H4V30Z\"/>',solid:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.7 8H32C33.1046 8 34 8.89543 34 10V30C34 31.1046 33.1046 32 32 32H4C2.89543 32 2 31.1046 2 30V10C2 8.89543 2.89543 8 4 8H11.3L12.35 5.28C12.6482 4.50729 13.3918 3.99831 14.22 4H21.78C22.6046 4.00244 23.3432 4.5107 23.64 5.28L24.7 8ZM15.22 25.47C12.7181 24.3166 11.1141 21.8149 11.11 19.06C11.1104 16.3371 12.6766 13.8571 15.1351 12.6866C17.5936 11.5161 20.5061 11.8637 22.62 13.58L21.53 15.34C20.036 14.0539 17.9258 13.7656 16.1417 14.6039C14.3575 15.4422 13.2324 17.2505 13.2687 19.2215C13.3051 21.1924 14.4961 22.9581 16.31 23.73L15.22 25.47ZM6.17 13.63C5.72817 13.63 5.37 13.2718 5.37 12.83C5.37 12.3882 5.72817 12.03 6.17 12.03H8.57C9.01183 12.03 9.37 12.3882 9.37 12.83C9.37 13.2718 9.01183 13.63 8.57 13.63H6.17ZM9 19C9 23.9706 13.0294 28 18 28C20.3869 28 22.6761 27.0518 24.364 25.364C26.0518 23.6761 27 21.387 27 19C27 14.0294 22.9706 10 18 10C13.0294 10 9 14.0294 9 19Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24.7 8H32C33.1046 8 34 8.89543 34 10V30C34 31.1046 33.1046 32 32 32H4C2.89543 32 2 31.1046 2 30V10C2 8.89543 2.89543 8 4 8H11.3L12.35 5.28C12.6482 4.50729 13.3918 3.99831 14.22 4H21.78C22.6046 4.00244 23.3432 4.5107 23.64 5.28L24.7 8ZM15.22 25.47C12.7181 24.3166 11.1141 21.8149 11.11 19.06C11.1104 16.3371 12.6766 13.8571 15.1351 12.6866C17.5936 11.5161 20.5061 11.8637 22.62 13.58L21.53 15.34C20.036 14.0539 17.9258 13.7656 16.1417 14.6039C14.3575 15.4422 13.2324 17.2505 13.2687 19.2215C13.3051 21.1924 14.4961 22.9581 16.31 23.73L15.22 25.47ZM6.17 13.63C5.72817 13.63 5.37 13.2718 5.37 12.83C5.37 12.3882 5.72817 12.03 6.17 12.03H8.57C9.01183 12.03 9.37 12.3882 9.37 12.83C9.37 13.2718 9.01183 13.63 8.57 13.63H6.17ZM9 19C9 23.9706 13.0294 28 18 28C20.3869 28 22.6761 27.0518 24.364 25.364C26.0518 23.6761 27 21.387 27 19C27 14.0294 22.9706 10 18 10C13.0294 10 9 14.0294 9 19Z\"/>'})];export{e as cameraIcon,H as cameraIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,0lEAA0lE;IAACC,KAAK,EAAC;EAA40D,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,UAAU,EAACJ,CAAC,IAAIK,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}