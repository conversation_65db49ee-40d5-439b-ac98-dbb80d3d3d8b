{"ast": null, "code": "/** Taken from https://github.com/CommandLineHeroes/clh-bash/tree/master/assets/cmds **/\nexport const pythonConfig = {\n  name: '<PERSON>',\n  cmds: [\n  // keywords\n  ['False'], ['class'], ['finally'], ['is'], ['return'], ['None'], ['continue'], ['for'], ['lambda'], ['try'], ['True'], ['def'], ['from'], ['nonlocal'], ['while'], ['and'], ['del'], ['global'], ['not'], ['with'], ['as'], ['elif'], ['if'], ['or'], ['yield'], ['assert'], ['else'], ['import'], ['pass'], ['break'], ['except'], ['in'], ['raise'],\n  // funtions\n  ['abs()'], ['delattr()'], ['hash()'], ['memoryview()'], ['set()'], ['all()'], ['dict()'], ['help()'], ['min()'], ['setattr()'], ['any()'], ['dir()'], ['hex()'], ['next()'], ['slice()'], ['ascii()'], ['divmod()'], ['id()'], ['object()'], ['sorted()'], ['bin()'], ['enumerate()'], ['input()'], ['oct()'], ['staticmethod()'], ['bool()'], ['eval()'], ['int()'], ['open()'], ['str()'], ['breakpoint()'], ['exec()'], ['isinstance()'], ['ord()'], ['sum()'], ['bytearray()'], ['filter()'], ['issubclass()'], ['pow()'], ['super()'], ['bytes()'], ['float()'], ['iter()'], ['print()'], ['tuple()'], ['callable()'], ['format()'], ['len()'], ['property()'], ['type()'], ['chr()'], ['frozenset()'], ['list()'], ['range()'], ['vars()'], ['classmethod()'], ['getattr()'], ['locals()'], ['repr()'], ['zip()'], ['compile()'], ['globals()'], ['map()'], ['reversed()'], ['__import__()'], ['complex()'], ['hasattr()'], ['max()'], ['round()'],\n  // functions without parens\n  ['abs'], ['delattr'], ['hash'], ['memoryview'], ['set'], ['all'], ['dict'], ['help'], ['min'], ['setattr'], ['any'], ['dir'], ['hex'], ['next'], ['slice'], ['ascii'], ['divmod'], ['id'], ['object'], ['sorted'], ['bin'], ['enumerate'], ['input'], ['oct'], ['staticmethod'], ['bool'], ['eval'], ['int'], ['open'], ['str'], ['breakpoint'], ['exec'], ['isinstance'], ['ord'], ['sum'], ['bytearray'], ['filter'], ['issubclass'], ['pow'], ['super'], ['bytes'], ['float'], ['iter'], ['print'], ['tuple'], ['callable'], ['format'], ['len'], ['property'], ['type'], ['chr'], ['frozenset'], ['list'], ['range'], ['vars'], ['classmethod'], ['getattr'], ['locals'], ['repr'], ['zip'], ['compile'], ['globals'], ['map'], ['reversed'], ['__import__'], ['complex'], ['hasattr'], ['max'], ['round']]\n};", "map": {"version": 3, "names": ["pythonConfig", "name", "cmds"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/src/app/terminals/bashbrawl/languages/python.ts"], "sourcesContent": ["/** Taken from https://github.com/CommandLineHeroes/clh-bash/tree/master/assets/cmds **/\n\n/**\n * This is a list of Python3+ keywords and built in functions\n *\n * See the README in this directory for more on how this list is assembled.\n *\n * There are duplicates, and that's okay.  But if you are removing items, be sure to look for multiple entries!\n */\nimport { LanguageConfig } from './language-config.interface';\nexport const pythonConfig: LanguageConfig = {\n  name: 'Python',\n  cmds: [\n    // keywords\n    ['False'],\n    ['class'],\n    ['finally'],\n    ['is'],\n    ['return'],\n    ['None'],\n    ['continue'],\n    ['for'],\n    ['lambda'],\n    ['try'],\n    ['True'],\n    ['def'],\n    ['from'],\n    ['nonlocal'],\n    ['while'],\n    ['and'],\n    ['del'],\n    ['global'],\n    ['not'],\n    ['with'],\n    ['as'],\n    ['elif'],\n    ['if'],\n    ['or'],\n    ['yield'],\n    ['assert'],\n    ['else'],\n    ['import'],\n    ['pass'],\n    ['break'],\n    ['except'],\n    ['in'],\n    ['raise'],\n    // funtions\n    ['abs()'],\n    ['delattr()'],\n    ['hash()'],\n    ['memoryview()'],\n    ['set()'],\n    ['all()'],\n    ['dict()'],\n    ['help()'],\n    ['min()'],\n    ['setattr()'],\n    ['any()'],\n    ['dir()'],\n    ['hex()'],\n    ['next()'],\n    ['slice()'],\n    ['ascii()'],\n    ['divmod()'],\n    ['id()'],\n    ['object()'],\n    ['sorted()'],\n    ['bin()'],\n    ['enumerate()'],\n    ['input()'],\n    ['oct()'],\n    ['staticmethod()'],\n    ['bool()'],\n    ['eval()'],\n    ['int()'],\n    ['open()'],\n    ['str()'],\n    ['breakpoint()'],\n    ['exec()'],\n    ['isinstance()'],\n    ['ord()'],\n    ['sum()'],\n    ['bytearray()'],\n    ['filter()'],\n    ['issubclass()'],\n    ['pow()'],\n    ['super()'],\n    ['bytes()'],\n    ['float()'],\n    ['iter()'],\n    ['print()'],\n    ['tuple()'],\n    ['callable()'],\n    ['format()'],\n    ['len()'],\n    ['property()'],\n    ['type()'],\n    ['chr()'],\n    ['frozenset()'],\n    ['list()'],\n    ['range()'],\n    ['vars()'],\n    ['classmethod()'],\n    ['getattr()'],\n    ['locals()'],\n    ['repr()'],\n    ['zip()'],\n    ['compile()'],\n    ['globals()'],\n    ['map()'],\n    ['reversed()'],\n    ['__import__()'],\n    ['complex()'],\n    ['hasattr()'],\n    ['max()'],\n    ['round()'],\n    // functions without parens\n    ['abs'],\n    ['delattr'],\n    ['hash'],\n    ['memoryview'],\n    ['set'],\n    ['all'],\n    ['dict'],\n    ['help'],\n    ['min'],\n    ['setattr'],\n    ['any'],\n    ['dir'],\n    ['hex'],\n    ['next'],\n    ['slice'],\n    ['ascii'],\n    ['divmod'],\n    ['id'],\n    ['object'],\n    ['sorted'],\n    ['bin'],\n    ['enumerate'],\n    ['input'],\n    ['oct'],\n    ['staticmethod'],\n    ['bool'],\n    ['eval'],\n    ['int'],\n    ['open'],\n    ['str'],\n    ['breakpoint'],\n    ['exec'],\n    ['isinstance'],\n    ['ord'],\n    ['sum'],\n    ['bytearray'],\n    ['filter'],\n    ['issubclass'],\n    ['pow'],\n    ['super'],\n    ['bytes'],\n    ['float'],\n    ['iter'],\n    ['print'],\n    ['tuple'],\n    ['callable'],\n    ['format'],\n    ['len'],\n    ['property'],\n    ['type'],\n    ['chr'],\n    ['frozenset'],\n    ['list'],\n    ['range'],\n    ['vars'],\n    ['classmethod'],\n    ['getattr'],\n    ['locals'],\n    ['repr'],\n    ['zip'],\n    ['compile'],\n    ['globals'],\n    ['map'],\n    ['reversed'],\n    ['__import__'],\n    ['complex'],\n    ['hasattr'],\n    ['max'],\n    ['round'],\n  ],\n};\n"], "mappings": "AAAA;AAUA,OAAO,MAAMA,YAAY,GAAmB;EAC1CC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE;EACJ;EACA,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,SAAS,CAAC,EACX,CAAC,IAAI,CAAC,EACN,CAAC,QAAQ,CAAC,EACV,CAAC,MAAM,CAAC,EACR,CAAC,UAAU,CAAC,EACZ,CAAC,KAAK,CAAC,EACP,CAAC,QAAQ,CAAC,EACV,CAAC,KAAK,CAAC,EACP,CAAC,MAAM,CAAC,EACR,CAAC,KAAK,CAAC,EACP,CAAC,MAAM,CAAC,EACR,CAAC,UAAU,CAAC,EACZ,CAAC,OAAO,CAAC,EACT,CAAC,KAAK,CAAC,EACP,CAAC,KAAK,CAAC,EACP,CAAC,QAAQ,CAAC,EACV,CAAC,KAAK,CAAC,EACP,CAAC,MAAM,CAAC,EACR,CAAC,IAAI,CAAC,EACN,CAAC,MAAM,CAAC,EACR,CAAC,IAAI,CAAC,EACN,CAAC,IAAI,CAAC,EACN,CAAC,OAAO,CAAC,EACT,CAAC,QAAQ,CAAC,EACV,CAAC,MAAM,CAAC,EACR,CAAC,QAAQ,CAAC,EACV,CAAC,MAAM,CAAC,EACR,CAAC,OAAO,CAAC,EACT,CAAC,QAAQ,CAAC,EACV,CAAC,IAAI,CAAC,EACN,CAAC,OAAO,CAAC;EACT;EACA,CAAC,OAAO,CAAC,EACT,CAAC,WAAW,CAAC,EACb,CAAC,QAAQ,CAAC,EACV,CAAC,cAAc,CAAC,EAChB,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,QAAQ,CAAC,EACV,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,WAAW,CAAC,EACb,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,QAAQ,CAAC,EACV,CAAC,SAAS,CAAC,EACX,CAAC,SAAS,CAAC,EACX,CAAC,UAAU,CAAC,EACZ,CAAC,MAAM,CAAC,EACR,CAAC,UAAU,CAAC,EACZ,CAAC,UAAU,CAAC,EACZ,CAAC,OAAO,CAAC,EACT,CAAC,aAAa,CAAC,EACf,CAAC,SAAS,CAAC,EACX,CAAC,OAAO,CAAC,EACT,CAAC,gBAAgB,CAAC,EAClB,CAAC,QAAQ,CAAC,EACV,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,cAAc,CAAC,EAChB,CAAC,QAAQ,CAAC,EACV,CAAC,cAAc,CAAC,EAChB,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,aAAa,CAAC,EACf,CAAC,UAAU,CAAC,EACZ,CAAC,cAAc,CAAC,EAChB,CAAC,OAAO,CAAC,EACT,CAAC,SAAS,CAAC,EACX,CAAC,SAAS,CAAC,EACX,CAAC,SAAS,CAAC,EACX,CAAC,QAAQ,CAAC,EACV,CAAC,SAAS,CAAC,EACX,CAAC,SAAS,CAAC,EACX,CAAC,YAAY,CAAC,EACd,CAAC,UAAU,CAAC,EACZ,CAAC,OAAO,CAAC,EACT,CAAC,YAAY,CAAC,EACd,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,aAAa,CAAC,EACf,CAAC,QAAQ,CAAC,EACV,CAAC,SAAS,CAAC,EACX,CAAC,QAAQ,CAAC,EACV,CAAC,eAAe,CAAC,EACjB,CAAC,WAAW,CAAC,EACb,CAAC,UAAU,CAAC,EACZ,CAAC,QAAQ,CAAC,EACV,CAAC,OAAO,CAAC,EACT,CAAC,WAAW,CAAC,EACb,CAAC,WAAW,CAAC,EACb,CAAC,OAAO,CAAC,EACT,CAAC,YAAY,CAAC,EACd,CAAC,cAAc,CAAC,EAChB,CAAC,WAAW,CAAC,EACb,CAAC,WAAW,CAAC,EACb,CAAC,OAAO,CAAC,EACT,CAAC,SAAS,CAAC;EACX;EACA,CAAC,KAAK,CAAC,EACP,CAAC,SAAS,CAAC,EACX,CAAC,MAAM,CAAC,EACR,CAAC,YAAY,CAAC,EACd,CAAC,KAAK,CAAC,EACP,CAAC,KAAK,CAAC,EACP,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,KAAK,CAAC,EACP,CAAC,SAAS,CAAC,EACX,CAAC,KAAK,CAAC,EACP,CAAC,KAAK,CAAC,EACP,CAAC,KAAK,CAAC,EACP,CAAC,MAAM,CAAC,EACR,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,QAAQ,CAAC,EACV,CAAC,IAAI,CAAC,EACN,CAAC,QAAQ,CAAC,EACV,CAAC,QAAQ,CAAC,EACV,CAAC,KAAK,CAAC,EACP,CAAC,WAAW,CAAC,EACb,CAAC,OAAO,CAAC,EACT,CAAC,KAAK,CAAC,EACP,CAAC,cAAc,CAAC,EAChB,CAAC,MAAM,CAAC,EACR,CAAC,MAAM,CAAC,EACR,CAAC,KAAK,CAAC,EACP,CAAC,MAAM,CAAC,EACR,CAAC,KAAK,CAAC,EACP,CAAC,YAAY,CAAC,EACd,CAAC,MAAM,CAAC,EACR,CAAC,YAAY,CAAC,EACd,CAAC,KAAK,CAAC,EACP,CAAC,KAAK,CAAC,EACP,CAAC,WAAW,CAAC,EACb,CAAC,QAAQ,CAAC,EACV,CAAC,YAAY,CAAC,EACd,CAAC,KAAK,CAAC,EACP,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,MAAM,CAAC,EACR,CAAC,OAAO,CAAC,EACT,CAAC,OAAO,CAAC,EACT,CAAC,UAAU,CAAC,EACZ,CAAC,QAAQ,CAAC,EACV,CAAC,KAAK,CAAC,EACP,CAAC,UAAU,CAAC,EACZ,CAAC,MAAM,CAAC,EACR,CAAC,KAAK,CAAC,EACP,CAAC,WAAW,CAAC,EACb,CAAC,MAAM,CAAC,EACR,CAAC,OAAO,CAAC,EACT,CAAC,MAAM,CAAC,EACR,CAAC,aAAa,CAAC,EACf,CAAC,SAAS,CAAC,EACX,CAAC,QAAQ,CAAC,EACV,CAAC,MAAM,CAAC,EACR,CAAC,KAAK,CAAC,EACP,CAAC,SAAS,CAAC,EACX,CAAC,SAAS,CAAC,EACX,CAAC,KAAK,CAAC,EACP,CAAC,UAAU,CAAC,EACZ,CAAC,YAAY,CAAC,EACd,CAAC,SAAS,CAAC,EACX,CAAC,SAAS,CAAC,EACX,CAAC,KAAK,CAAC,EACP,CAAC,OAAO,CAAC;CAEZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}