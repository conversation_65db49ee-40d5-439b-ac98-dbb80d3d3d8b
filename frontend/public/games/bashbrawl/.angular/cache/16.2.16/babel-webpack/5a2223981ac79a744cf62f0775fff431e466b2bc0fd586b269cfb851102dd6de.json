{"ast": null, "code": "import { renderIcon as L } from \"../icon.renderer.js\";\nconst C = \"won\",\n  H = [\"won\", L({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M33 18.0031H28.75L29.25 16.0027H33C33.5523 16.0027 34 15.5549 34 15.0024C34 14.45 33.5523 14.0022 33 14.0022H29.74L31.91 5.24028C31.9444 4.75635 31.6267 4.31748 31.1563 4.19924C30.6858 4.08099 30.1984 4.31746 30 4.76018L27.68 14.0022H21.31L19 4.76018C18.8885 4.31519 18.4886 4.00311 18.03 4.00311C17.5714 4.00311 17.1715 4.31519 17.06 4.76018L14.79 14.0022H8.42L6.13 4.76018C5.98087 4.24396 5.45061 3.93783 4.92912 4.06689C4.40763 4.19595 4.08127 4.71407 4.19 5.24028L6.36 14.0022H3C2.44772 14.0022 2 14.45 2 15.0024C2 15.5549 2.44772 16.0027 3 16.0027H6.85L7.35 18.0031H3C2.44772 18.0031 2 18.4509 2 19.0033C2 19.5557 2.44772 20.0036 3 20.0036H7.84L10.63 31.246C10.7415 31.691 11.1414 32.0031 11.6 32.0031C12.0586 32.0031 12.4585 31.691 12.57 31.246L15.36 20.0036H20.74L23.53 31.246C23.6415 31.691 24.0414 32.0031 24.5 32.0031C24.9586 32.0031 25.3585 31.691 25.47 31.246L28.25 20.0036H33C33.5523 20.0036 34 19.5557 34 19.0033C34 18.4509 33.5523 18.0031 33 18.0031ZM27.18 16.0027L26.68 18.0027H22.3L21.8 16.0027H27.18ZM19.25 14.0022L18 9.16115L16.85 14.0022H19.25ZM8.90997 16.0027H14.29L13.79 18.0027H9.40997L8.90997 16.0027ZM9.90997 20.0035L11.6 26.8451L13.3 20.0035H9.90997ZM15.85 18.0027L16.35 16.0027H19.74L20.24 18.0027H15.85ZM22.8 20.0035L24.49 26.8451L26.19 20.0035H22.8Z\"/>',\n    solid: '<path d=\"M23.6 20L22.78 23.1L21.97 20H23.6Z\"/><path d=\"M21.44 18H24.13L24.66 16H20.91L21.44 18Z\"/><path d=\"M14.1 20L13.29 23.1L12.47 20H14.1Z\"/><path d=\"M11.94 18H14.63L15.16 16H11.41L11.94 18Z\"/><path d=\"M17.22 18L17.74 16H18.32L18.85 18H17.22Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M18 2C9.16344 2 2 9.16344 2 18C2 26.8366 9.16344 34 18 34C26.8366 34 34 26.8366 34 18C34 13.7565 32.3143 9.68687 29.3137 6.68629C26.3131 3.68571 22.2435 2 18 2ZM29 20H26.19L24 28.32C23.8571 28.8713 23.3595 29.2563 22.79 29.2563C22.2205 29.2563 21.7229 28.8713 21.58 28.32L19.38 20H16.69L14.5 28.32C14.3571 28.8713 13.8595 29.2563 13.29 29.2563C12.7205 29.2563 12.2229 28.8713 12.08 28.32L9.88 20H7C6.44772 20 6 19.5523 6 19C6 18.4477 6.44772 18 7 18H9.35L8.82 16H7C6.44772 16 6 15.5523 6 15C6 14.4477 6.44772 14 7 14H8.3L7.3 10.32C7.12327 9.65174 7.52174 8.96673 8.19 8.79C8.85826 8.61327 9.54327 9.01174 9.72 9.68L10.88 14H15.68L16.82 9.68C16.9629 9.12868 17.4605 8.74369 18.03 8.74369C18.5995 8.74369 19.0971 9.12868 19.24 9.68L20.38 14H25.18L26.32 9.68C26.4967 9.01174 27.1817 8.61327 27.85 8.79C28.5183 8.96673 28.9167 9.65174 28.74 10.32L27.74 14H29C29.5523 14 30 14.4477 30 15C30 15.5523 29.5523 16 29 16H27.24L26.71 18H29C29.5523 18 30 18.4477 30 19C30 19.5523 29.5523 20 29 20Z\"/>'\n  })];\nexport { H as wonIcon, C as wonIconName };", "map": {"version": 3, "names": ["renderIcon", "L", "C", "H", "outline", "solid", "wonIcon", "wonIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/won.js"], "sourcesContent": ["import{renderIcon as L}from\"../icon.renderer.js\";const C=\"won\",H=[\"won\",L({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M33 18.0031H28.75L29.25 16.0027H33C33.5523 16.0027 34 15.5549 34 15.0024C34 14.45 33.5523 14.0022 33 14.0022H29.74L31.91 5.24028C31.9444 4.75635 31.6267 4.31748 31.1563 4.19924C30.6858 4.08099 30.1984 4.31746 30 4.76018L27.68 14.0022H21.31L19 4.76018C18.8885 4.31519 18.4886 4.00311 18.03 4.00311C17.5714 4.00311 17.1715 4.31519 17.06 4.76018L14.79 14.0022H8.42L6.13 4.76018C5.98087 4.24396 5.45061 3.93783 4.92912 4.06689C4.40763 4.19595 4.08127 4.71407 4.19 5.24028L6.36 14.0022H3C2.44772 14.0022 2 14.45 2 15.0024C2 15.5549 2.44772 16.0027 3 16.0027H6.85L7.35 18.0031H3C2.44772 18.0031 2 18.4509 2 19.0033C2 19.5557 2.44772 20.0036 3 20.0036H7.84L10.63 31.246C10.7415 31.691 11.1414 32.0031 11.6 32.0031C12.0586 32.0031 12.4585 31.691 12.57 31.246L15.36 20.0036H20.74L23.53 31.246C23.6415 31.691 24.0414 32.0031 24.5 32.0031C24.9586 32.0031 25.3585 31.691 25.47 31.246L28.25 20.0036H33C33.5523 20.0036 34 19.5557 34 19.0033C34 18.4509 33.5523 18.0031 33 18.0031ZM27.18 16.0027L26.68 18.0027H22.3L21.8 16.0027H27.18ZM19.25 14.0022L18 9.16115L16.85 14.0022H19.25ZM8.90997 16.0027H14.29L13.79 18.0027H9.40997L8.90997 16.0027ZM9.90997 20.0035L11.6 26.8451L13.3 20.0035H9.90997ZM15.85 18.0027L16.35 16.0027H19.74L20.24 18.0027H15.85ZM22.8 20.0035L24.49 26.8451L26.19 20.0035H22.8Z\"/>',solid:'<path d=\"M23.6 20L22.78 23.1L21.97 20H23.6Z\"/><path d=\"M21.44 18H24.13L24.66 16H20.91L21.44 18Z\"/><path d=\"M14.1 20L13.29 23.1L12.47 20H14.1Z\"/><path d=\"M11.94 18H14.63L15.16 16H11.41L11.94 18Z\"/><path d=\"M17.22 18L17.74 16H18.32L18.85 18H17.22Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M18 2C9.16344 2 2 9.16344 2 18C2 26.8366 9.16344 34 18 34C26.8366 34 34 26.8366 34 18C34 13.7565 32.3143 9.68687 29.3137 6.68629C26.3131 3.68571 22.2435 2 18 2ZM29 20H26.19L24 28.32C23.8571 28.8713 23.3595 29.2563 22.79 29.2563C22.2205 29.2563 21.7229 28.8713 21.58 28.32L19.38 20H16.69L14.5 28.32C14.3571 28.8713 13.8595 29.2563 13.29 29.2563C12.7205 29.2563 12.2229 28.8713 12.08 28.32L9.88 20H7C6.44772 20 6 19.5523 6 19C6 18.4477 6.44772 18 7 18H9.35L8.82 16H7C6.44772 16 6 15.5523 6 15C6 14.4477 6.44772 14 7 14H8.3L7.3 10.32C7.12327 9.65174 7.52174 8.96673 8.19 8.79C8.85826 8.61327 9.54327 9.01174 9.72 9.68L10.88 14H15.68L16.82 9.68C16.9629 9.12868 17.4605 8.74369 18.03 8.74369C18.5995 8.74369 19.0971 9.12868 19.24 9.68L20.38 14H25.18L26.32 9.68C26.4967 9.01174 27.1817 8.61327 27.85 8.79C28.5183 8.96673 28.9167 9.65174 28.74 10.32L27.74 14H29C29.5523 14 30 14.4477 30 15C30 15.5523 29.5523 16 29 16H27.24L26.71 18H29C29.5523 18 30 18.4477 30 19C30 19.5523 29.5523 20 29 20Z\"/>'})];export{H as wonIcon,C as wonIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,KAAK;EAACC,CAAC,GAAC,CAAC,KAAK,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,ozCAAozC;IAACC,KAAK,EAAC;EAAuwC,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,OAAO,EAACJ,CAAC,IAAIK,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}