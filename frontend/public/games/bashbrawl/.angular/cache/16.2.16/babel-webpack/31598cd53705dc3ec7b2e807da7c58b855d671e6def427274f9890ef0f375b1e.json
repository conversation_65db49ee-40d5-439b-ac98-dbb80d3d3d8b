{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst H = \"folder\",\n  e = [\"folder\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M16.42 10H30C31.1046 10 32 10.8954 32 12V30C32 31.1046 31.1046 32 30 32H6C4.89543 32 4 31.1046 4 30V8.00001C4 6.89544 4.89543 6.00001 6 6.00001H12.49C13.1301 5.99835 13.7323 6.30318 14.11 6.82001L16.42 10ZM6 30H30V12H15.91C15.5899 12.0008 15.2888 11.8484 15.1 11.59L12.49 8.00001H6V12H15.31C15.31 13.1046 14.4146 14 13.31 14H6V30Z\"/>',\n    outlineAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M19.5594 10H16.42L14.11 6.82001C13.7323 6.30318 13.1301 5.99835 12.49 6.00001H6C4.89543 6.00001 4 6.89544 4 8.00001V30C4 31.1046 4.89543 32 6 32H30C31.1046 32 32 31.1046 32 30V15.0367H30V30H6V14H13.31C14.4146 14 15.31 13.1046 15.31 12H6V8.00001H12.49L15.1 11.59C15.2888 11.8484 15.5899 12.0008 15.91 12H19.0073C18.9663 11.3177 19.1449 10.6284 19.5362 10.0387L19.5594 10Z\"/>',\n    outlineBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M32 12.7101C31.3663 12.8987 30.695 13 30 13V30H6V14H13.31C14.4146 14 15.31 13.1046 15.31 12H6V8.00001H12.49L15.1 11.59C15.2888 11.8484 15.5899 12.0008 15.91 12H26.3924C25.5469 11.4905 24.8179 10.8074 24.2547 10H16.42L14.11 6.82001C13.7323 6.30318 13.1301 5.99835 12.49 6.00001H6C4.89543 6.00001 4 6.89544 4 8.00001V30C4 31.1046 4.89543 32 6 32H30C31.1046 32 32 31.1046 32 30V12.7101Z\"/>',\n    solid: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M16.42 10H30C31.1046 10 32 10.8954 32 12V30C32 31.1046 31.1046 32 30 32H6C4.89543 32 4 31.1046 4 30V8.00001C4 6.89544 4.89543 6.00001 6 6.00001H12.49C13.1301 5.99835 13.7323 6.30318 14.11 6.82001L16.42 10ZM6 8.00001V12H15.21L12.49 8.00001H6Z\"/>',\n    solidAlerted: '<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M19.5594 10L19.5362 10.0387C18.8703 11.0423 18.8204 12.3342 19.4206 13.3893C20.0233 14.4489 21.1577 15.0604 22.3395 15.0367H32V30C32 31.1046 31.1046 32 30 32H6C4.89543 32 4 31.1046 4 30V8.00001C4 6.89544 4.89543 6.00001 6 6.00001H12.49C13.1301 5.99835 13.7323 6.30318 14.11 6.82001L16.42 10H19.5594ZM6 12V8.00001H12.49L15.21 12H6Z\"/>',\n    solidBadged: '<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 12.7101C31.3663 12.8987 30.695 13 30 13C27.6213 13 25.5196 11.8135 24.2547 10H16.42L14.11 6.82001C13.7323 6.30318 13.1301 5.99835 12.49 6.00001H6C4.89543 6.00001 4 6.89544 4 8.00001V30C4 31.1046 4.89543 32 6 32H30C31.1046 32 32 31.1046 32 30V12.7101ZM6 12V8.00001H12.49L15.21 12H6Z\"/>'\n  })];\nexport { e as folderIcon, H as folderIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "H", "e", "outline", "outlineAlerted", "outlineBadged", "solid", "solidAlerted", "solidBadged", "folderIcon", "folderIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/folder.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const H=\"folder\",e=[\"folder\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M16.42 10H30C31.1046 10 32 10.8954 32 12V30C32 31.1046 31.1046 32 30 32H6C4.89543 32 4 31.1046 4 30V8.00001C4 6.89544 4.89543 6.00001 6 6.00001H12.49C13.1301 5.99835 13.7323 6.30318 14.11 6.82001L16.42 10ZM6 30H30V12H15.91C15.5899 12.0008 15.2888 11.8484 15.1 11.59L12.49 8.00001H6V12H15.31C15.31 13.1046 14.4146 14 13.31 14H6V30Z\"/>',outlineAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path d=\"M19.5594 10H16.42L14.11 6.82001C13.7323 6.30318 13.1301 5.99835 12.49 6.00001H6C4.89543 6.00001 4 6.89544 4 8.00001V30C4 31.1046 4.89543 32 6 32H30C31.1046 32 32 31.1046 32 30V15.0367H30V30H6V14H13.31C14.4146 14 15.31 13.1046 15.31 12H6V8.00001H12.49L15.1 11.59C15.2888 11.8484 15.5899 12.0008 15.91 12H19.0073C18.9663 11.3177 19.1449 10.6284 19.5362 10.0387L19.5594 10Z\"/>',outlineBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path d=\"M32 12.7101C31.3663 12.8987 30.695 13 30 13V30H6V14H13.31C14.4146 14 15.31 13.1046 15.31 12H6V8.00001H12.49L15.1 11.59C15.2888 11.8484 15.5899 12.0008 15.91 12H26.3924C25.5469 11.4905 24.8179 10.8074 24.2547 10H16.42L14.11 6.82001C13.7323 6.30318 13.1301 5.99835 12.49 6.00001H6C4.89543 6.00001 4 6.89544 4 8.00001V30C4 31.1046 4.89543 32 6 32H30C31.1046 32 32 31.1046 32 30V12.7101Z\"/>',solid:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M16.42 10H30C31.1046 10 32 10.8954 32 12V30C32 31.1046 31.1046 32 30 32H6C4.89543 32 4 31.1046 4 30V8.00001C4 6.89544 4.89543 6.00001 6 6.00001H12.49C13.1301 5.99835 13.7323 6.30318 14.11 6.82001L16.42 10ZM6 8.00001V12H15.21L12.49 8.00001H6Z\"/>',solidAlerted:'<path d=\"M26.9039 1.64621L21.2222 11.1159C20.9526 11.4984 20.9281 11.9949 21.1588 12.4005C21.3896 12.806 21.8363 13.0519 22.3148 13.0367H33.6881C34.1666 13.0519 34.6134 12.806 34.8441 12.4005C35.0748 11.9949 35.0503 11.4984 34.7808 11.1159L29.0991 1.64621C28.8711 1.26913 28.4532 1.03735 28.0015 1.03735C27.5497 1.03735 27.1319 1.26913 26.9039 1.64621Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M19.5594 10L19.5362 10.0387C18.8703 11.0423 18.8204 12.3342 19.4206 13.3893C20.0233 14.4489 21.1577 15.0604 22.3395 15.0367H32V30C32 31.1046 31.1046 32 30 32H6C4.89543 32 4 31.1046 4 30V8.00001C4 6.89544 4.89543 6.00001 6 6.00001H12.49C13.1301 5.99835 13.7323 6.30318 14.11 6.82001L16.42 10H19.5594ZM6 12V8.00001H12.49L15.21 12H6Z\"/>',solidBadged:'<path d=\"M30 11C32.7614 11 35 8.76142 35 6C35 3.23858 32.7614 1 30 1C27.2386 1 25 3.23858 25 6C25 8.76142 27.2386 11 30 11Z\"/><path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M32 12.7101C31.3663 12.8987 30.695 13 30 13C27.6213 13 25.5196 11.8135 24.2547 10H16.42L14.11 6.82001C13.7323 6.30318 13.1301 5.99835 12.49 6.00001H6C4.89543 6.00001 4 6.89544 4 8.00001V30C4 31.1046 4.89543 32 6 32H30C31.1046 32 32 31.1046 32 30V12.7101ZM6 12V8.00001H12.49L15.21 12H6Z\"/>'})];export{e as folderIcon,H as folderIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,gYAAgY;IAACC,cAAc,EAAC,muBAAmuB;IAACC,aAAa,EAAC,2gBAA2gB;IAACC,KAAK,EAAC,uSAAuS;IAACC,YAAY,EAAC,muBAAmuB;IAACC,WAAW,EAAC;EAAid,CAAC,CAAC,CAAC;AAAC,SAAON,CAAC,IAAIO,UAAU,EAACR,CAAC,IAAIS,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}