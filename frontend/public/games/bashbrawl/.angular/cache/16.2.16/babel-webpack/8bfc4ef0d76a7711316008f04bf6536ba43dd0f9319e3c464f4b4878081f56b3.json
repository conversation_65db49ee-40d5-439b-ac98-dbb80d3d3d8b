{"ast": null, "code": "import { renderIcon as o } from \"../icon.renderer.js\";\nconst e = \"nodes\",\n  t = [\"nodes\", o({\n    outline: '<path d=\"M10.5,34.29,2,29.39V19.58l8.5-4.9,8.5,4.9v9.81ZM4,28.23,10.5,32,17,28.23V20.74L10.5,17,4,20.74Z\"/><path d=\"M25.5,34.29,17,29.39V19.58l8.5-4.9,8.5,4.9v9.81ZM19,28.23,25.5,32,32,28.23V20.74L25.5,17,19,20.74Z\"/><path d=\"M18,21.32l-8.5-4.9V6.61L18,1.71l8.5,4.9v9.81Zm-6.5-6.06L18,19l6.5-3.75V7.77L18,4,11.5,7.77Z\"/>'\n  })];\nexport { t as nodesIcon, e as nodesIconName };", "map": {"version": 3, "names": ["renderIcon", "o", "e", "t", "outline", "nodesIcon", "nodesIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/nodes.js"], "sourcesContent": ["import{renderIcon as o}from\"../icon.renderer.js\";const e=\"nodes\",t=[\"nodes\",o({outline:'<path d=\"M10.5,34.29,2,29.39V19.58l8.5-4.9,8.5,4.9v9.81ZM4,28.23,10.5,32,17,28.23V20.74L10.5,17,4,20.74Z\"/><path d=\"M25.5,34.29,17,29.39V19.58l8.5-4.9,8.5,4.9v9.81ZM19,28.23,25.5,32,32,28.23V20.74L25.5,17,19,20.74Z\"/><path d=\"M18,21.32l-8.5-4.9V6.61L18,1.71l8.5,4.9v9.81Zm-6.5-6.06L18,19l6.5-3.75V7.77L18,4,11.5,7.77Z\"/>'})];export{t as nodesIcon,e as nodesIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,OAAO;EAACC,CAAC,GAAC,CAAC,OAAO,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAkU,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,SAAS,EAACH,CAAC,IAAII,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}