{"ast": null, "code": "import { renderIcon as r } from \"../icon.renderer.js\";\nconst o = \"arrow-mini\",\n  i = [\"arrow-mini\", r({\n    outline: '<path d=\"M29.18,13.26,17.92,3,6.63,13.28a2,2,0,0,0-.55,2.33,2,2,0,0,0,3.19.68L16,10.13V30.29a2,2,0,0,0,1.35,2A2,2,0,0,0,20,30.38V10.28l6.57,6a2,2,0,0,0,1.35.52,2,2,0,0,0,1.72-1A2.08,2.08,0,0,0,29.18,13.26Z\"/>'\n  })];\nexport { i as arrowMiniIcon, o as arrowMiniIconName };", "map": {"version": 3, "names": ["renderIcon", "r", "o", "i", "outline", "arrowMiniIcon", "arrowMiniIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/arrow-mini.js"], "sourcesContent": ["import{renderIcon as r}from\"../icon.renderer.js\";const o=\"arrow-mini\",i=[\"arrow-mini\",r({outline:'<path d=\"M29.18,13.26,17.92,3,6.63,13.28a2,2,0,0,0-.55,2.33,2,2,0,0,0,3.19.68L16,10.13V30.29a2,2,0,0,0,1.35,2A2,2,0,0,0,20,30.38V10.28l6.57,6a2,2,0,0,0,1.35.52,2,2,0,0,0,1.72-1A2.08,2.08,0,0,0,29.18,13.26Z\"/>'})];export{i as arrowMiniIcon,o as arrowMiniIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,YAAY;EAACC,CAAC,GAAC,CAAC,YAAY,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAkN,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,aAAa,EAACH,CAAC,IAAII,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}