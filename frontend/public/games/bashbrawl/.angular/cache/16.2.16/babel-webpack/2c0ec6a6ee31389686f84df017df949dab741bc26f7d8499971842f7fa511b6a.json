{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nfunction t() {\n  return t => t.addInitializer(t => new o(t));\n}\nclass o {\n  constructor(t) {\n    this.host = t, this.host.addController(this);\n  }\n  get root() {\n    return this.host.shadowRoot ? this.host.shadowRoot : this.host;\n  }\n  hostConnected() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.host.updateComplete, _this.root.addEventListener(\"scroll\", () => _this.host.style.setProperty(\"--row-content-visibility\", \"visibile\"), {\n        once: !0,\n        capture: !0\n      });\n    })();\n  }\n}\nexport { o as ScrollableVisibilityController, t as scrollableVisibility };", "map": {"version": 3, "names": ["t", "addInitializer", "o", "constructor", "host", "addController", "root", "shadowRoot", "hostConnected", "_this", "_asyncToGenerator", "updateComplete", "addEventListener", "style", "setProperty", "once", "capture", "ScrollableVisibilityController", "scrollableVisibility"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/scrollable-list-visibility.controller.js"], "sourcesContent": ["function t(){return t=>t.addInitializer((t=>new o(t)))}class o{constructor(t){this.host=t,this.host.addController(this)}get root(){return this.host.shadowRoot?this.host.shadowRoot:this.host}async hostConnected(){await this.host.updateComplete,this.root.addEventListener(\"scroll\",(()=>this.host.style.setProperty(\"--row-content-visibility\",\"visibile\")),{once:!0,capture:!0})}}export{o as ScrollableVisibilityController,t as scrollableVisibility};\n"], "mappings": ";AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAOA,CAAC,IAAEA,CAAC,CAACC,cAAc,CAAED,CAAC,IAAE,IAAIE,CAAC,CAACF,CAAC,CAAE,CAAC;AAAA;AAAC,MAAME,CAAC;EAACC,WAAWA,CAACH,CAAC,EAAC;IAAC,IAAI,CAACI,IAAI,GAACJ,CAAC,EAAC,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;EAAA;EAAC,IAAIC,IAAIA,CAAA,EAAE;IAAC,OAAO,IAAI,CAACF,IAAI,CAACG,UAAU,GAAC,IAAI,CAACH,IAAI,CAACG,UAAU,GAAC,IAAI,CAACH,IAAI;EAAA;EAAOI,aAAaA,CAAA,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAAC,MAAMD,KAAI,CAACL,IAAI,CAACO,cAAc,EAACF,KAAI,CAACH,IAAI,CAACM,gBAAgB,CAAC,QAAQ,EAAE,MAAIH,KAAI,CAACL,IAAI,CAACS,KAAK,CAACC,WAAW,CAAC,0BAA0B,EAAC,UAAU,CAAC,EAAE;QAACC,IAAI,EAAC,CAAC,CAAC;QAACC,OAAO,EAAC,CAAC;MAAC,CAAC,CAAC;IAAA;EAAA;AAAC;AAAC,SAAOd,CAAC,IAAIe,8BAA8B,EAACjB,CAAC,IAAIkB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}