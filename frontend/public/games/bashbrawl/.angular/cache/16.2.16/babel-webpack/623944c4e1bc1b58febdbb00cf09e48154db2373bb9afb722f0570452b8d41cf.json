{"ast": null, "code": "import { onEscape as s, onFocusOut as t } from \"../utils/focus.js\";\nfunction o(s = {}) {\n  return t => {\n    t.addInitializer(t => {\n      t.closableController || (t.closableController = new e(t, s));\n    });\n  };\n}\nclass e {\n  constructor(s, t = {}) {\n    this.host = s, this.config = {\n      escape: !0,\n      focusout: !1,\n      ...t\n    }, this.host.addController(this);\n  }\n  hostConnected() {\n    this.config.escape && s(this.host, () => this.close(\"escape-keypress\")), this.config.focusout && (this.host.tabIndex = 0, t(this.host, () => this.close(\"focusout\")));\n  }\n  close(s) {\n    this.host.dispatchEvent(new CustomEvent(\"closeChange\", {\n      detail: s\n    }));\n  }\n}\nexport { e as ClosableController, o as closable };", "map": {"version": 3, "names": ["onEscape", "s", "onFocusOut", "t", "o", "addInitializer", "closableController", "e", "constructor", "host", "config", "escape", "focusout", "addController", "hostConnected", "close", "tabIndex", "dispatchEvent", "CustomEvent", "detail", "ClosableController", "closable"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/closable.controller.js"], "sourcesContent": ["import{onEscape as s,onFocusOut as t}from\"../utils/focus.js\";function o(s={}){return t=>{t.addInitializer((t=>{t.closableController||(t.closableController=new e(t,s))}))}}class e{constructor(s,t={}){this.host=s,this.config={escape:!0,focusout:!1,...t},this.host.addController(this)}hostConnected(){this.config.escape&&s(this.host,(()=>this.close(\"escape-keypress\"))),this.config.focusout&&(this.host.tabIndex=0,t(this.host,(()=>this.close(\"focusout\"))))}close(s){this.host.dispatchEvent(new CustomEvent(\"closeChange\",{detail:s}))}}export{e as ClosableController,o as closable};\n"], "mappings": "AAAA,SAAOA,QAAQ,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAASC,CAACA,CAACH,CAAC,GAAC,CAAC,CAAC,EAAC;EAAC,OAAOE,CAAC,IAAE;IAACA,CAAC,CAACE,cAAc,CAAEF,CAAC,IAAE;MAACA,CAAC,CAACG,kBAAkB,KAAGH,CAAC,CAACG,kBAAkB,GAAC,IAAIC,CAAC,CAACJ,CAAC,EAACF,CAAC,CAAC,CAAC;IAAA,CAAE,CAAC;EAAA,CAAC;AAAA;AAAC,MAAMM,CAAC;EAACC,WAAWA,CAACP,CAAC,EAACE,CAAC,GAAC,CAAC,CAAC,EAAC;IAAC,IAAI,CAACM,IAAI,GAACR,CAAC,EAAC,IAAI,CAACS,MAAM,GAAC;MAACC,MAAM,EAAC,CAAC,CAAC;MAACC,QAAQ,EAAC,CAAC,CAAC;MAAC,GAAGT;IAAC,CAAC,EAAC,IAAI,CAACM,IAAI,CAACI,aAAa,CAAC,IAAI,CAAC;EAAA;EAACC,aAAaA,CAAA,EAAE;IAAC,IAAI,CAACJ,MAAM,CAACC,MAAM,IAAEV,CAAC,CAAC,IAAI,CAACQ,IAAI,EAAE,MAAI,IAAI,CAACM,KAAK,CAAC,iBAAiB,CAAE,CAAC,EAAC,IAAI,CAACL,MAAM,CAACE,QAAQ,KAAG,IAAI,CAACH,IAAI,CAACO,QAAQ,GAAC,CAAC,EAACb,CAAC,CAAC,IAAI,CAACM,IAAI,EAAE,MAAI,IAAI,CAACM,KAAK,CAAC,UAAU,CAAE,CAAC,CAAC;EAAA;EAACA,KAAKA,CAACd,CAAC,EAAC;IAAC,IAAI,CAACQ,IAAI,CAACQ,aAAa,CAAC,IAAIC,WAAW,CAAC,aAAa,EAAC;MAACC,MAAM,EAAClB;IAAC,CAAC,CAAC,CAAC;EAAA;AAAC;AAAC,SAAOM,CAAC,IAAIa,kBAAkB,EAAChB,CAAC,IAAIiB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}