{"ast": null, "code": "import _curry1 from \"./internal/_curry1.js\";\n/**\n * Gives a single-word string description of the (native) type of a value,\n * returning such answers as 'Object', 'Number', 'Array', or 'Null'. Does not\n * attempt to distinguish user Object types any further, reporting them all as\n * 'Object'.\n *\n * @func\n * @memberOf R\n * @since v0.8.0\n * @category Type\n * @sig * -> String\n * @param {*} val The value to test\n * @return {String}\n * @example\n *\n *      R.type({}); //=> \"Object\"\n *      R.type(1); //=> \"Number\"\n *      R.type(false); //=> \"Boolean\"\n *      R.type('s'); //=> \"String\"\n *      R.type(null); //=> \"Null\"\n *      R.type([]); //=> \"Array\"\n *      R.type(/[A-z]/); //=> \"RegExp\"\n *      R.type(() => {}); //=> \"Function\"\n *      R.type(async () => {}); //=> \"AsyncFunction\"\n *      R.type(undefined); //=> \"Undefined\"\n */\n\nvar type = /*#__PURE__*/\n_curry1(function type(val) {\n  return val === null ? 'Null' : val === undefined ? 'Undefined' : Object.prototype.toString.call(val).slice(8, -1);\n});\nexport default type;", "map": {"version": 3, "names": ["_curry1", "type", "val", "undefined", "Object", "prototype", "toString", "call", "slice"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/type.js"], "sourcesContent": ["import _curry1 from \"./internal/_curry1.js\";\n/**\n * Gives a single-word string description of the (native) type of a value,\n * returning such answers as 'Object', 'Number', 'Array', or 'Null'. Does not\n * attempt to distinguish user Object types any further, reporting them all as\n * 'Object'.\n *\n * @func\n * @memberOf R\n * @since v0.8.0\n * @category Type\n * @sig * -> String\n * @param {*} val The value to test\n * @return {String}\n * @example\n *\n *      R.type({}); //=> \"Object\"\n *      R.type(1); //=> \"Number\"\n *      R.type(false); //=> \"Boolean\"\n *      R.type('s'); //=> \"String\"\n *      R.type(null); //=> \"Null\"\n *      R.type([]); //=> \"Array\"\n *      R.type(/[A-z]/); //=> \"RegExp\"\n *      R.type(() => {}); //=> \"Function\"\n *      R.type(async () => {}); //=> \"AsyncFunction\"\n *      R.type(undefined); //=> \"Undefined\"\n */\n\nvar type =\n/*#__PURE__*/\n_curry1(function type(val) {\n  return val === null ? 'Null' : val === undefined ? 'Undefined' : Object.prototype.toString.call(val).slice(8, -1);\n});\n\nexport default type;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,uBAAuB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,IAAI,GACR;AACAD,OAAO,CAAC,SAASC,IAAIA,CAACC,GAAG,EAAE;EACzB,OAAOA,GAAG,KAAK,IAAI,GAAG,MAAM,GAAGA,GAAG,KAAKC,SAAS,GAAG,WAAW,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,GAAG,CAAC,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnH,CAAC,CAAC;AAEF,eAAeP,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}