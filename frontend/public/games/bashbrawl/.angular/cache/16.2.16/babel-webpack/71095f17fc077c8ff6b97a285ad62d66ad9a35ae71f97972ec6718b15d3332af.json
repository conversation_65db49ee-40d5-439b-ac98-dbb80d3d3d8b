{"ast": null, "code": "function t() {\n  return t => t.addInitializer(t => new s(t));\n}\nclass s {\n  constructor(t) {\n    this.host = t, this.host.addController(this);\n  }\n  hostConnected() {\n    (this.host.hasAttribute(\"aria-controls\") || this.host.ariaControls) && (this.host.ariaHasPopup = \"true\", this.host.ariaExpanded = \"false\");\n  }\n}\nexport { s as AriaPopupTriggerController, t as ariaPopupTrigger };", "map": {"version": 3, "names": ["t", "addInitializer", "s", "constructor", "host", "addController", "hostConnected", "hasAttribute", "ariaControls", "aria<PERSON>as<PERSON><PERSON><PERSON>", "ariaExpanded", "AriaPopupTriggerController", "ariaPopupTrigger"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/controllers/aria-popup-trigger.controller.js"], "sourcesContent": ["function t(){return t=>t.addInitializer((t=>new s(t)))}class s{constructor(t){this.host=t,this.host.addController(this)}hostConnected(){(this.host.hasAttribute(\"aria-controls\")||this.host.ariaControls)&&(this.host.ariaHasPopup=\"true\",this.host.ariaExpanded=\"false\")}}export{s as AriaPopupTriggerController,t as ariaPopupTrigger};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAOA,CAAC,IAAEA,CAAC,CAACC,cAAc,CAAED,CAAC,IAAE,IAAIE,CAAC,CAACF,CAAC,CAAE,CAAC;AAAA;AAAC,MAAME,CAAC;EAACC,WAAWA,CAACH,CAAC,EAAC;IAAC,IAAI,CAACI,IAAI,GAACJ,CAAC,EAAC,IAAI,CAACI,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;EAAA;EAACC,aAAaA,CAAA,EAAE;IAAC,CAAC,IAAI,CAACF,IAAI,CAACG,YAAY,CAAC,eAAe,CAAC,IAAE,IAAI,CAACH,IAAI,CAACI,YAAY,MAAI,IAAI,CAACJ,IAAI,CAACK,YAAY,GAAC,MAAM,EAAC,IAAI,CAACL,IAAI,CAACM,YAAY,GAAC,OAAO,CAAC;EAAA;AAAC;AAAC,SAAOR,CAAC,IAAIS,0BAA0B,EAACX,CAAC,IAAIY,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}