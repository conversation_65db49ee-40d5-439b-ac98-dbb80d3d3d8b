{"ast": null, "code": "import { renderIcon as e } from \"../icon.renderer.js\";\nconst c = \"check\",\n  o = [\"check\", e({\n    outline: '<path d=\"M13.759 28.5L3.2909 17.8985C2.94988 17.4948 2.97281 16.8931 3.34353 16.5173C3.71424 16.1415 4.30784 16.1182 4.70604 16.4639L13.7389 25.6207L31.3931 7.74465C31.7913 7.39895 32.3849 7.4222 32.7556 7.798C33.1263 8.1738 33.1492 8.77554 32.8082 9.17921L13.759 28.5Z\"/>'\n  })];\nexport { o as checkIcon, c as checkIconName };", "map": {"version": 3, "names": ["renderIcon", "e", "c", "o", "outline", "checkIcon", "checkIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/check.js"], "sourcesContent": ["import{renderIcon as e}from\"../icon.renderer.js\";const c=\"check\",o=[\"check\",e({outline:'<path d=\"M13.759 28.5L3.2909 17.8985C2.94988 17.4948 2.97281 16.8931 3.34353 16.5173C3.71424 16.1415 4.30784 16.1182 4.70604 16.4639L13.7389 25.6207L31.3931 7.74465C31.7913 7.39895 32.3849 7.4222 32.7556 7.798C33.1263 8.1738 33.1492 8.77554 32.8082 9.17921L13.759 28.5Z\"/>'})];export{o as checkIcon,c as checkIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,OAAO;EAACC,CAAC,GAAC,CAAC,OAAO,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAkR,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,SAAS,EAACH,CAAC,IAAII,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}