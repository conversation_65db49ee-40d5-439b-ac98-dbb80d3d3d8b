{"ast": null, "code": "import { ClarityIcons as s } from \"../icon.service.js\";\nimport { cameraIcon as o } from \"../shapes/camera.js\";\nimport { fastForwardIcon as r } from \"../shapes/fast-forward.js\";\nimport { filmStripIcon as m } from \"../shapes/film-strip.js\";\nimport { headphonesIcon as p } from \"../shapes/headphones.js\";\nimport { imageGalleryIcon as e } from \"../shapes/image-gallery.js\";\nimport { microphoneMuteIcon as a } from \"../shapes/microphone-mute.js\";\nimport { microphoneIcon as i } from \"../shapes/microphone.js\";\nimport { musicNoteIcon as t } from \"../shapes/music-note.js\";\nimport { pauseIcon as f } from \"../shapes/pause.js\";\nimport { playIcon as h } from \"../shapes/play.js\";\nimport { powerIcon as j } from \"../shapes/power.js\";\nimport { replayAllIcon as l } from \"../shapes/replay-all.js\";\nimport { replayOneIcon as n } from \"../shapes/replay-one.js\";\nimport { rewindIcon as d } from \"../shapes/rewind.js\";\nimport { shuffleIcon as c } from \"../shapes/shuffle.js\";\nimport { stepForwardIcon as u } from \"../shapes/step-forward.js\";\nimport { stopIcon as v } from \"../shapes/stop.js\";\nimport { videoCameraIcon as w } from \"../shapes/video-camera.js\";\nimport { videoGalleryIcon as y } from \"../shapes/video-gallery.js\";\nimport { volumeDownIcon as g } from \"../shapes/volume-down.js\";\nimport { volumeMuteIcon as x } from \"../shapes/volume-mute.js\";\nimport { volumeUpIcon as A } from \"../shapes/volume-up.js\";\nconst I = [o, r, m, p, e, i, a, t, f, h, j, l, n, d, c, u, v, w, y, g, x, A],\n  b = [];\nfunction k() {\n  s.addIcons(...I), s.addAliases(...b);\n}\nexport { k as loadMediaIconSet, b as mediaCollectionAliases, I as mediaCollectionIcons };", "map": {"version": 3, "names": ["ClarityIcons", "s", "cameraIcon", "o", "fastForwardIcon", "r", "filmStripIcon", "m", "headphonesIcon", "p", "imageGalleryIcon", "e", "microphoneMuteIcon", "a", "microphoneIcon", "i", "musicNoteIcon", "t", "pauseIcon", "f", "playIcon", "h", "powerIcon", "j", "replayAllIcon", "l", "replayOneIcon", "n", "rewindIcon", "d", "shuffleIcon", "c", "stepForwardIcon", "u", "stopIcon", "v", "videoCameraIcon", "w", "videoGalleryIcon", "y", "volumeDownIcon", "g", "volumeMuteIcon", "x", "volumeUpIcon", "A", "I", "b", "k", "addIcons", "addAliases", "loadMediaIconSet", "mediaCollectionAliases", "mediaCollectionIcons"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/collections/media.js"], "sourcesContent": ["import{ClarityIcons as s}from\"../icon.service.js\";import{cameraIcon as o}from\"../shapes/camera.js\";import{fastForwardIcon as r}from\"../shapes/fast-forward.js\";import{filmStripIcon as m}from\"../shapes/film-strip.js\";import{headphonesIcon as p}from\"../shapes/headphones.js\";import{imageGalleryIcon as e}from\"../shapes/image-gallery.js\";import{microphoneMuteIcon as a}from\"../shapes/microphone-mute.js\";import{microphoneIcon as i}from\"../shapes/microphone.js\";import{musicNoteIcon as t}from\"../shapes/music-note.js\";import{pauseIcon as f}from\"../shapes/pause.js\";import{playIcon as h}from\"../shapes/play.js\";import{powerIcon as j}from\"../shapes/power.js\";import{replayAllIcon as l}from\"../shapes/replay-all.js\";import{replayOneIcon as n}from\"../shapes/replay-one.js\";import{rewindIcon as d}from\"../shapes/rewind.js\";import{shuffleIcon as c}from\"../shapes/shuffle.js\";import{stepForwardIcon as u}from\"../shapes/step-forward.js\";import{stopIcon as v}from\"../shapes/stop.js\";import{videoCameraIcon as w}from\"../shapes/video-camera.js\";import{videoGalleryIcon as y}from\"../shapes/video-gallery.js\";import{volumeDownIcon as g}from\"../shapes/volume-down.js\";import{volumeMuteIcon as x}from\"../shapes/volume-mute.js\";import{volumeUpIcon as A}from\"../shapes/volume-up.js\";const I=[o,r,m,p,e,i,a,t,f,h,j,l,n,d,c,u,v,w,y,g,x,A],b=[];function k(){s.addIcons(...I),s.addAliases(...b)}export{k as loadMediaIconSet,b as mediaCollectionAliases,I as mediaCollectionIcons};\n"], "mappings": "AAAA,SAAOA,YAAY,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,kBAAkB,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,wBAAwB;AAAC,MAAMC,CAAC,GAAC,CAAC3C,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACF,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,CAAC;EAACE,CAAC,GAAC,EAAE;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC/C,CAAC,CAACgD,QAAQ,CAAC,GAAGH,CAAC,CAAC,EAAC7C,CAAC,CAACiD,UAAU,CAAC,GAAGH,CAAC,CAAC;AAAA;AAAC,SAAOC,CAAC,IAAIG,gBAAgB,EAACJ,CAAC,IAAIK,sBAAsB,EAACN,CAAC,IAAIO,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}