{"ast": null, "code": "import o from \"ramda/es/curryN\";\nimport r from \"ramda/es/path\";\nimport t from \"./__.js\";\nconst m = o(2, (o, t) => void 0 !== r(o, t));\nfunction n(o, r) {\n  return r || (r = window && window.customElements), !r || !!r.get(o);\n}\nconst i = m(t, window);\nexport { n as elementExists, m as existsIn, i as existsInWindow };", "map": {"version": 3, "names": ["o", "r", "t", "m", "n", "window", "customElements", "get", "i", "elementExists", "existsIn", "existsInWindow"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/exists.js"], "sourcesContent": ["import o from\"ramda/es/curryN\";import r from\"ramda/es/path\";import t from\"./__.js\";const m=o(2,((o,t)=>void 0!==r(o,t)));function n(o,r){return r||(r=window&&window.customElements),!r||!!r.get(o)}const i=m(t,window);export{n as elementExists,m as existsIn,i as existsInWindow};\n"], "mappings": "AAAA,OAAOA,CAAC,MAAK,iBAAiB;AAAC,OAAOC,CAAC,MAAK,eAAe;AAAC,OAAOC,CAAC,MAAK,SAAS;AAAC,MAAMC,CAAC,GAACH,CAAC,CAAC,CAAC,EAAE,CAACA,CAAC,EAACE,CAAC,KAAG,KAAK,CAAC,KAAGD,CAAC,CAACD,CAAC,EAACE,CAAC,CAAE,CAAC;AAAC,SAASE,CAACA,CAACJ,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOA,CAAC,KAAGA,CAAC,GAACI,MAAM,IAAEA,MAAM,CAACC,cAAc,CAAC,EAAC,CAACL,CAAC,IAAE,CAAC,CAACA,CAAC,CAACM,GAAG,CAACP,CAAC,CAAC;AAAA;AAAC,MAAMQ,CAAC,GAACL,CAAC,CAACD,CAAC,EAACG,MAAM,CAAC;AAAC,SAAOD,CAAC,IAAIK,aAAa,EAACN,CAAC,IAAIO,QAAQ,EAACF,CAAC,IAAIG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}