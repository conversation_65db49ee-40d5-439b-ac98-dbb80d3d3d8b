{"ast": null, "code": "export default function _arrayFromIterator(iter) {\n  var list = [];\n  var next;\n  while (!(next = iter.next()).done) {\n    list.push(next.value);\n  }\n  return list;\n}", "map": {"version": 3, "names": ["_arrayFromIterator", "iter", "list", "next", "done", "push", "value"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_arrayFromIterator.js"], "sourcesContent": ["export default function _arrayFromIterator(iter) {\n  var list = [];\n  var next;\n\n  while (!(next = iter.next()).done) {\n    list.push(next.value);\n  }\n\n  return list;\n}"], "mappings": "AAAA,eAAe,SAASA,kBAAkBA,CAACC,IAAI,EAAE;EAC/C,IAAIC,IAAI,GAAG,EAAE;EACb,IAAIC,IAAI;EAER,OAAO,CAAC,CAACA,IAAI,GAAGF,IAAI,CAACE,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAE;IACjCF,IAAI,CAACG,IAAI,CAACF,IAAI,CAACG,KAAK,CAAC;EACvB;EAEA,OAAOJ,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}