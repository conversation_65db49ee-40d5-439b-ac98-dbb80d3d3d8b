{"ast": null, "code": "import _isArrayLike from \"./_isArrayLike.js\";\nvar symIterator = typeof Symbol !== 'undefined' ? Symbol.iterator : '@@iterator';\nexport default function _createReduce(arrayReduce, methodReduce, iterableReduce) {\n  return function _reduce(xf, acc, list) {\n    if (_isArrayLike(list)) {\n      return arrayReduce(xf, acc, list);\n    }\n    if (list == null) {\n      return acc;\n    }\n    if (typeof list['fantasy-land/reduce'] === 'function') {\n      return methodReduce(xf, acc, list, 'fantasy-land/reduce');\n    }\n    if (list[symIterator] != null) {\n      return iterableReduce(xf, acc, list[symIterator]());\n    }\n    if (typeof list.next === 'function') {\n      return iterableReduce(xf, acc, list);\n    }\n    if (typeof list.reduce === 'function') {\n      return methodReduce(xf, acc, list, 'reduce');\n    }\n    throw new TypeError('reduce: list must be array or iterable');\n  };\n}", "map": {"version": 3, "names": ["_isArrayLike", "symIterator", "Symbol", "iterator", "_createReduce", "arrayReduce", "methodReduce", "iterableReduce", "_reduce", "xf", "acc", "list", "next", "reduce", "TypeError"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_createReduce.js"], "sourcesContent": ["import _isArrayLike from \"./_isArrayLike.js\";\nvar symIterator = typeof Symbol !== 'undefined' ? Symbol.iterator : '@@iterator';\nexport default function _createReduce(arrayReduce, methodReduce, iterableReduce) {\n  return function _reduce(xf, acc, list) {\n    if (_isArrayLike(list)) {\n      return arrayReduce(xf, acc, list);\n    }\n\n    if (list == null) {\n      return acc;\n    }\n\n    if (typeof list['fantasy-land/reduce'] === 'function') {\n      return methodReduce(xf, acc, list, 'fantasy-land/reduce');\n    }\n\n    if (list[symIterator] != null) {\n      return iterableReduce(xf, acc, list[symIterator]());\n    }\n\n    if (typeof list.next === 'function') {\n      return iterableReduce(xf, acc, list);\n    }\n\n    if (typeof list.reduce === 'function') {\n      return methodReduce(xf, acc, list, 'reduce');\n    }\n\n    throw new TypeError('reduce: list must be array or iterable');\n  };\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,mBAAmB;AAC5C,IAAIC,WAAW,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACC,QAAQ,GAAG,YAAY;AAChF,eAAe,SAASC,aAAaA,CAACC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAE;EAC/E,OAAO,SAASC,OAAOA,CAACC,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAE;IACrC,IAAIX,YAAY,CAACW,IAAI,CAAC,EAAE;MACtB,OAAON,WAAW,CAACI,EAAE,EAAEC,GAAG,EAAEC,IAAI,CAAC;IACnC;IAEA,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChB,OAAOD,GAAG;IACZ;IAEA,IAAI,OAAOC,IAAI,CAAC,qBAAqB,CAAC,KAAK,UAAU,EAAE;MACrD,OAAOL,YAAY,CAACG,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAE,qBAAqB,CAAC;IAC3D;IAEA,IAAIA,IAAI,CAACV,WAAW,CAAC,IAAI,IAAI,EAAE;MAC7B,OAAOM,cAAc,CAACE,EAAE,EAAEC,GAAG,EAAEC,IAAI,CAACV,WAAW,CAAC,CAAC,CAAC,CAAC;IACrD;IAEA,IAAI,OAAOU,IAAI,CAACC,IAAI,KAAK,UAAU,EAAE;MACnC,OAAOL,cAAc,CAACE,EAAE,EAAEC,GAAG,EAAEC,IAAI,CAAC;IACtC;IAEA,IAAI,OAAOA,IAAI,CAACE,MAAM,KAAK,UAAU,EAAE;MACrC,OAAOP,YAAY,CAACG,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAE,QAAQ,CAAC;IAC9C;IAEA,MAAM,IAAIG,SAAS,CAAC,wCAAwC,CAAC;EAC/D,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}