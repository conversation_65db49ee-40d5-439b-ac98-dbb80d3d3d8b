{"ast": null, "code": "const t = [{\n  transformOrigin: \"top left\",\n  transform: \"rotate3d(0, 0, 1, 80deg)\",\n  offset: .2\n}, {\n  transformOrigin: \"top left\",\n  transform: \"rotate3d(0, 0, 1, 60deg)\",\n  offset: .4\n}, {\n  transformOrigin: \"top left\",\n  transform: \"rotate3d(0, 0, 1, 80deg)\",\n  offset: .6\n}, {\n  transformOrigin: \"top left\",\n  transform: \"rotate3d(0, 0, 1, 60deg)\",\n  opacity: 1,\n  offset: .8\n}, {\n  transformOrigin: \"top left\",\n  opacity: 0,\n  transform: \"translate3d(0, 700px, 0)\"\n}];\nexport { t as hingeKeyframes };", "map": {"version": 3, "names": ["t", "transform<PERSON><PERSON>in", "transform", "offset", "opacity", "hingeKeyframes"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/motion/animations/keyframes/hinge.js"], "sourcesContent": ["const t=[{transformOrigin:\"top left\",transform:\"rotate3d(0, 0, 1, 80deg)\",offset:.2},{transformOrigin:\"top left\",transform:\"rotate3d(0, 0, 1, 60deg)\",offset:.4},{transformOrigin:\"top left\",transform:\"rotate3d(0, 0, 1, 80deg)\",offset:.6},{transformOrigin:\"top left\",transform:\"rotate3d(0, 0, 1, 60deg)\",opacity:1,offset:.8},{transformOrigin:\"top left\",opacity:0,transform:\"translate3d(0, 700px, 0)\"}];export{t as hingeKeyframes};\n"], "mappings": "AAAA,MAAMA,CAAC,GAAC,CAAC;EAACC,eAAe,EAAC,UAAU;EAACC,SAAS,EAAC,0BAA0B;EAACC,MAAM,EAAC;AAAE,CAAC,EAAC;EAACF,eAAe,EAAC,UAAU;EAACC,SAAS,EAAC,0BAA0B;EAACC,MAAM,EAAC;AAAE,CAAC,EAAC;EAACF,eAAe,EAAC,UAAU;EAACC,SAAS,EAAC,0BAA0B;EAACC,MAAM,EAAC;AAAE,CAAC,EAAC;EAACF,eAAe,EAAC,UAAU;EAACC,SAAS,EAAC,0BAA0B;EAACE,OAAO,EAAC,CAAC;EAACD,MAAM,EAAC;AAAE,CAAC,EAAC;EAACF,eAAe,EAAC,UAAU;EAACG,OAAO,EAAC,CAAC;EAACF,SAAS,EAAC;AAA0B,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIK,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}