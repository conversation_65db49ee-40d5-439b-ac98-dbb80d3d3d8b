{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst e = \"dollar\",\n  l = [\"dollar\", C({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M26.4415 21.15C25.5276 19.4853 23.9831 18.2835 22.175 17.83C21.3357 17.5715 20.4838 17.3579 19.6229 17.19V8.12C21.3345 8.30848 22.9674 8.95694 24.3569 10C24.6179 10.2376 24.9826 10.3142 25.3135 10.2009C25.6444 10.0876 25.8914 9.80175 25.9613 9.45091C26.0312 9.10007 25.9135 8.73758 25.6525 8.5C23.9091 7.1216 21.8157 6.28834 19.6229 6.1V3C19.6229 2.44772 19.1868 2 18.6489 2C18.1109 2 17.6748 2.44772 17.6748 3V6C13.3888 6.1 11.0218 8.29 10.301 10.18C9.30811 12.693 10.226 15.5747 12.4732 17C14.0574 17.9914 15.8319 18.6191 17.6748 18.84V28C15.1318 27.8686 12.6957 26.9088 10.7199 25.26C10.4793 24.966 10.096 24.8395 9.73355 24.9343C9.37113 25.0291 9.0936 25.3286 9.0194 25.7049C8.94519 26.0812 9.08742 26.4679 9.38537 26.7C11.7153 28.7138 14.6315 29.8747 17.6748 30V33C17.6748 33.5523 18.1109 34 18.6489 34C19.1868 34 19.6229 33.5523 19.6229 33V30C22.3698 29.81 25.5356 28.91 26.7337 25.24C27.1715 23.8912 27.0663 22.4188 26.4415 21.15ZM13.5739 15.32C12.1045 14.4221 11.4891 12.5606 12.1225 10.93C12.2297 10.63 13.3207 8.12 17.6748 8V16.8C16.2217 16.608 14.8244 16.1037 13.5739 15.32ZM19.6229 27.96C22.6231 27.73 24.1816 26.7 24.883 24.56C25.1338 23.7311 25.0604 22.8344 24.6784 22.06C23.9961 20.8822 22.8728 20.0435 21.5711 19.74C20.8892 19.53 20.2463 19.37 19.6229 19.23V27.96Z\"/>',\n    solid: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 18C2 9.16344 9.16344 2 18 2C22.2435 2 26.3131 3.68571 29.3137 6.68629C32.3143 9.68687 34 13.7565 34 18C34 26.8366 26.8366 34 18 34C9.16344 34 2 26.8366 2 18ZM19.75 27.59C22.04 27.43 24.65 26.59 25.65 23.59C26.0211 22.4243 25.9169 21.1592 25.36 20.07C24.5718 18.6611 23.2364 17.6406 21.67 17.25C21 17.04 20.36 16.9 19.76 16.77V10.64C20.9318 10.8015 22.0452 11.2517 23 11.95C23.5166 12.3047 24.2159 12.2221 24.6357 11.7568C25.0554 11.2916 25.0658 10.5875 24.66 10.11C23.2444 9.00064 21.5475 8.30803 19.76 8.11V6.25C19.76 5.55964 19.2004 5 18.51 5C17.8196 5 17.26 5.55964 17.26 6.25V8.07C14.04 8.35 12.21 10.07 11.62 11.58C10.7641 13.7325 11.5676 16.1892 13.53 17.42C14.6613 18.1451 15.9319 18.6254 17.26 18.83V25.11C15.5934 24.8953 14.0187 24.2239 12.71 23.17C12.3831 22.8645 11.9181 22.7567 11.4901 22.887C11.0621 23.0174 10.7362 23.3662 10.6351 23.802C10.534 24.2379 10.6731 24.6945 11 25C12.7755 26.4721 14.9536 27.3747 17.25 27.59V29.59C17.25 30.2804 17.8096 30.84 18.5 30.84C19.1904 30.84 19.75 30.2804 19.75 29.59V27.59ZM20.92 19.64C20.52 19.52 20.13 19.42 19.75 19.34V25.1C21.75 24.9 22.82 24.2 23.28 22.8C23.4333 22.2735 23.3797 21.7083 23.13 21.22C22.637 20.4165 21.8398 19.8466 20.92 19.64ZM14.94 15.35C13.9357 14.7782 13.5085 13.5521 13.94 12.48C14.19 11.84 15.16 10.8 17.26 10.55V16.27C16.4347 16.1153 15.647 15.803 14.94 15.35Z\"/>'\n  })];\nexport { l as dollarIcon, e as dollarIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "e", "l", "outline", "solid", "dollarIcon", "dollarIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/dollar.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const e=\"dollar\",l=[\"dollar\",C({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M26.4415 21.15C25.5276 19.4853 23.9831 18.2835 22.175 17.83C21.3357 17.5715 20.4838 17.3579 19.6229 17.19V8.12C21.3345 8.30848 22.9674 8.95694 24.3569 10C24.6179 10.2376 24.9826 10.3142 25.3135 10.2009C25.6444 10.0876 25.8914 9.80175 25.9613 9.45091C26.0312 9.10007 25.9135 8.73758 25.6525 8.5C23.9091 7.1216 21.8157 6.28834 19.6229 6.1V3C19.6229 2.44772 19.1868 2 18.6489 2C18.1109 2 17.6748 2.44772 17.6748 3V6C13.3888 6.1 11.0218 8.29 10.301 10.18C9.30811 12.693 10.226 15.5747 12.4732 17C14.0574 17.9914 15.8319 18.6191 17.6748 18.84V28C15.1318 27.8686 12.6957 26.9088 10.7199 25.26C10.4793 24.966 10.096 24.8395 9.73355 24.9343C9.37113 25.0291 9.0936 25.3286 9.0194 25.7049C8.94519 26.0812 9.08742 26.4679 9.38537 26.7C11.7153 28.7138 14.6315 29.8747 17.6748 30V33C17.6748 33.5523 18.1109 34 18.6489 34C19.1868 34 19.6229 33.5523 19.6229 33V30C22.3698 29.81 25.5356 28.91 26.7337 25.24C27.1715 23.8912 27.0663 22.4188 26.4415 21.15ZM13.5739 15.32C12.1045 14.4221 11.4891 12.5606 12.1225 10.93C12.2297 10.63 13.3207 8.12 17.6748 8V16.8C16.2217 16.608 14.8244 16.1037 13.5739 15.32ZM19.6229 27.96C22.6231 27.73 24.1816 26.7 24.883 24.56C25.1338 23.7311 25.0604 22.8344 24.6784 22.06C23.9961 20.8822 22.8728 20.0435 21.5711 19.74C20.8892 19.53 20.2463 19.37 19.6229 19.23V27.96Z\"/>',solid:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M2 18C2 9.16344 9.16344 2 18 2C22.2435 2 26.3131 3.68571 29.3137 6.68629C32.3143 9.68687 34 13.7565 34 18C34 26.8366 26.8366 34 18 34C9.16344 34 2 26.8366 2 18ZM19.75 27.59C22.04 27.43 24.65 26.59 25.65 23.59C26.0211 22.4243 25.9169 21.1592 25.36 20.07C24.5718 18.6611 23.2364 17.6406 21.67 17.25C21 17.04 20.36 16.9 19.76 16.77V10.64C20.9318 10.8015 22.0452 11.2517 23 11.95C23.5166 12.3047 24.2159 12.2221 24.6357 11.7568C25.0554 11.2916 25.0658 10.5875 24.66 10.11C23.2444 9.00064 21.5475 8.30803 19.76 8.11V6.25C19.76 5.55964 19.2004 5 18.51 5C17.8196 5 17.26 5.55964 17.26 6.25V8.07C14.04 8.35 12.21 10.07 11.62 11.58C10.7641 13.7325 11.5676 16.1892 13.53 17.42C14.6613 18.1451 15.9319 18.6254 17.26 18.83V25.11C15.5934 24.8953 14.0187 24.2239 12.71 23.17C12.3831 22.8645 11.9181 22.7567 11.4901 22.887C11.0621 23.0174 10.7362 23.3662 10.6351 23.802C10.534 24.2379 10.6731 24.6945 11 25C12.7755 26.4721 14.9536 27.3747 17.25 27.59V29.59C17.25 30.2804 17.8096 30.84 18.5 30.84C19.1904 30.84 19.75 30.2804 19.75 29.59V27.59ZM20.92 19.64C20.52 19.52 20.13 19.42 19.75 19.34V25.1C21.75 24.9 22.82 24.2 23.28 22.8C23.4333 22.2735 23.3797 21.7083 23.13 21.22C22.637 20.4165 21.8398 19.8466 20.92 19.64ZM14.94 15.35C13.9357 14.7782 13.5085 13.5521 13.94 12.48C14.19 11.84 15.16 10.8 17.26 10.55V16.27C16.4347 16.1153 15.647 15.803 14.94 15.35Z\"/>'})];export{l as dollarIcon,e as dollarIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,szCAAszC;IAACC,KAAK,EAAC;EAAm3C,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,UAAU,EAACJ,CAAC,IAAIK,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}