{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nvar _a, _b, _c, _d;\nvar _e;\n/**\n * Use this module if you want to create your own base class extending\n * {@link ReactiveElement}.\n * @packageDocumentation\n */\nimport { getCompatibleStyle, adoptStyles } from './css-tag.js';\n// In the Node build, this import will be injected by Rollup:\n// import {HTMLElement, customElements} from '@lit-labs/ssr-dom-shim';\nexport * from './css-tag.js';\nconst NODE_MODE = false;\nconst global = NODE_MODE ? globalThis : window;\nif (NODE_MODE) {\n  (_a = global.customElements) !== null && _a !== void 0 ? _a : global.customElements = customElements;\n}\nconst DEV_MODE = true;\nlet requestUpdateThenable;\nlet issueWarning;\nconst trustedTypes = global.trustedTypes;\n// Temporary workaround for https://crbug.com/993268\n// Currently, any attribute starting with \"on\" is considered to be a\n// TrustedScript source. Such boolean attributes must be set to the equivalent\n// trusted emptyScript value.\nconst emptyStringForBooleanAttribute = trustedTypes ? trustedTypes.emptyScript : '';\nconst polyfillSupport = DEV_MODE ? global.reactiveElementPolyfillSupportDevMode : global.reactiveElementPolyfillSupport;\nif (DEV_MODE) {\n  // Ensure warnings are issued only 1x, even if multiple versions of Lit\n  // are loaded.\n  const issuedWarnings = (_b = global.litIssuedWarnings) !== null && _b !== void 0 ? _b : global.litIssuedWarnings = new Set();\n  // Issue a warning, if we haven't already.\n  issueWarning = (code, warning) => {\n    warning += ` See https://lit.dev/msg/${code} for more information.`;\n    if (!issuedWarnings.has(warning)) {\n      console.warn(warning);\n      issuedWarnings.add(warning);\n    }\n  };\n  issueWarning('dev-mode', `Lit is in dev mode. Not recommended for production!`);\n  // Issue polyfill support warning.\n  if (((_c = global.ShadyDOM) === null || _c === void 0 ? void 0 : _c.inUse) && polyfillSupport === undefined) {\n    issueWarning('polyfill-support-missing', `Shadow DOM is being polyfilled via \\`ShadyDOM\\` but ` + `the \\`polyfill-support\\` module has not been loaded.`);\n  }\n  requestUpdateThenable = name => ({\n    then: (onfulfilled, _onrejected) => {\n      issueWarning('request-update-promise', `The \\`requestUpdate\\` method should no longer return a Promise but ` + `does so on \\`${name}\\`. Use \\`updateComplete\\` instead.`);\n      if (onfulfilled !== undefined) {\n        onfulfilled(false);\n      }\n    }\n  });\n}\n/**\n * Useful for visualizing and logging insights into what the Lit template system is doing.\n *\n * Compiled out of prod mode builds.\n */\nconst debugLogEvent = DEV_MODE ? event => {\n  const shouldEmit = global.emitLitDebugLogEvents;\n  if (!shouldEmit) {\n    return;\n  }\n  global.dispatchEvent(new CustomEvent('lit-debug', {\n    detail: event\n  }));\n} : undefined;\n/*\n * When using Closure Compiler, JSCompiler_renameProperty(property, object) is\n * replaced at compile time by the munged name for object[property]. We cannot\n * alias this function, so we have to use a small shim that has the same\n * behavior when not compiling.\n */\n/*@__INLINE__*/\nconst JSCompiler_renameProperty = (prop, _obj) => prop;\nexport const defaultConverter = {\n  toAttribute(value, type) {\n    switch (type) {\n      case Boolean:\n        value = value ? emptyStringForBooleanAttribute : null;\n        break;\n      case Object:\n      case Array:\n        // if the value is `null` or `undefined` pass this through\n        // to allow removing/no change behavior.\n        value = value == null ? value : JSON.stringify(value);\n        break;\n    }\n    return value;\n  },\n  fromAttribute(value, type) {\n    let fromValue = value;\n    switch (type) {\n      case Boolean:\n        fromValue = value !== null;\n        break;\n      case Number:\n        fromValue = value === null ? null : Number(value);\n        break;\n      case Object:\n      case Array:\n        // Do *not* generate exception when invalid JSON is set as elements\n        // don't normally complain on being mis-configured.\n        // TODO(sorvell): Do generate exception in *dev mode*.\n        try {\n          // Assert to adhere to Bazel's \"must type assert JSON parse\" rule.\n          fromValue = JSON.parse(value);\n        } catch (e) {\n          fromValue = null;\n        }\n        break;\n    }\n    return fromValue;\n  }\n};\n/**\n * Change function that returns true if `value` is different from `oldValue`.\n * This method is used as the default for a property's `hasChanged` function.\n */\nexport const notEqual = (value, old) => {\n  // This ensures (old==NaN, value==NaN) always returns false\n  return old !== value && (old === old || value === value);\n};\nconst defaultPropertyDeclaration = {\n  attribute: true,\n  type: String,\n  converter: defaultConverter,\n  reflect: false,\n  hasChanged: notEqual\n};\n/**\n * The Closure JS Compiler doesn't currently have good support for static\n * property semantics where \"this\" is dynamic (e.g.\n * https://github.com/google/closure-compiler/issues/3177 and others) so we use\n * this hack to bypass any rewriting by the compiler.\n */\nconst finalized = 'finalized';\n/**\n * Base element class which manages element properties and attributes. When\n * properties change, the `update` method is asynchronously called. This method\n * should be supplied by subclassers to render updates as desired.\n * @noInheritDoc\n */\nexport class ReactiveElement\n// In the Node build, this `extends` clause will be substituted with\n// `(globalThis.HTMLElement ?? HTMLElement)`.\n//\n// This way, we will first prefer any global `HTMLElement` polyfill that the\n// user has assigned, and then fall back to the `HTMLElement` shim which has\n// been imported (see note at the top of this file about how this import is\n// generated by Rollup). Note that the `HTMLElement` variable has been\n// shadowed by this import, so it no longer refers to the global.\nextends HTMLElement {\n  constructor() {\n    super();\n    this.__instanceProperties = new Map();\n    /**\n     * True if there is a pending update as a result of calling `requestUpdate()`.\n     * Should only be read.\n     * @category updates\n     */\n    this.isUpdatePending = false;\n    /**\n     * Is set to `true` after the first update. The element code cannot assume\n     * that `renderRoot` exists before the element `hasUpdated`.\n     * @category updates\n     */\n    this.hasUpdated = false;\n    /**\n     * Name of currently reflecting property\n     */\n    this.__reflectingProperty = null;\n    this.__initialize();\n  }\n  /**\n   * Adds an initializer function to the class that is called during instance\n   * construction.\n   *\n   * This is useful for code that runs against a `ReactiveElement`\n   * subclass, such as a decorator, that needs to do work for each\n   * instance, such as setting up a `ReactiveController`.\n   *\n   * ```ts\n   * const myDecorator = (target: typeof ReactiveElement, key: string) => {\n   *   target.addInitializer((instance: ReactiveElement) => {\n   *     // This is run during construction of the element\n   *     new MyController(instance);\n   *   });\n   * }\n   * ```\n   *\n   * Decorating a field will then cause each instance to run an initializer\n   * that adds a controller:\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   @myDecorator foo;\n   * }\n   * ```\n   *\n   * Initializers are stored per-constructor. Adding an initializer to a\n   * subclass does not add it to a superclass. Since initializers are run in\n   * constructors, initializers will run in order of the class hierarchy,\n   * starting with superclasses and progressing to the instance's class.\n   *\n   * @nocollapse\n   */\n  static addInitializer(initializer) {\n    var _a;\n    this.finalize();\n    ((_a = this._initializers) !== null && _a !== void 0 ? _a : this._initializers = []).push(initializer);\n  }\n  /**\n   * Returns a list of attributes corresponding to the registered properties.\n   * @nocollapse\n   * @category attributes\n   */\n  static get observedAttributes() {\n    // note: piggy backing on this to ensure we're finalized.\n    this.finalize();\n    const attributes = [];\n    // Use forEach so this works even if for/of loops are compiled to for loops\n    // expecting arrays\n    this.elementProperties.forEach((v, p) => {\n      const attr = this.__attributeNameForProperty(p, v);\n      if (attr !== undefined) {\n        this.__attributeToPropertyMap.set(attr, p);\n        attributes.push(attr);\n      }\n    });\n    return attributes;\n  }\n  /**\n   * Creates a property accessor on the element prototype if one does not exist\n   * and stores a {@linkcode PropertyDeclaration} for the property with the\n   * given options. The property setter calls the property's `hasChanged`\n   * property option or uses a strict identity check to determine whether or not\n   * to request an update.\n   *\n   * This method may be overridden to customize properties; however,\n   * when doing so, it's important to call `super.createProperty` to ensure\n   * the property is setup correctly. This method calls\n   * `getPropertyDescriptor` internally to get a descriptor to install.\n   * To customize what properties do when they are get or set, override\n   * `getPropertyDescriptor`. To customize the options for a property,\n   * implement `createProperty` like this:\n   *\n   * ```ts\n   * static createProperty(name, options) {\n   *   options = Object.assign(options, {myOption: true});\n   *   super.createProperty(name, options);\n   * }\n   * ```\n   *\n   * @nocollapse\n   * @category properties\n   */\n  static createProperty(name, options = defaultPropertyDeclaration) {\n    var _a;\n    // if this is a state property, force the attribute to false.\n    if (options.state) {\n      // Cast as any since this is readonly.\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      options.attribute = false;\n    }\n    // Note, since this can be called by the `@property` decorator which\n    // is called before `finalize`, we ensure finalization has been kicked off.\n    this.finalize();\n    this.elementProperties.set(name, options);\n    // Do not generate an accessor if the prototype already has one, since\n    // it would be lost otherwise and that would never be the user's intention;\n    // Instead, we expect users to call `requestUpdate` themselves from\n    // user-defined accessors. Note that if the super has an accessor we will\n    // still overwrite it\n    if (!options.noAccessor && !this.prototype.hasOwnProperty(name)) {\n      const key = typeof name === 'symbol' ? Symbol() : `__${name}`;\n      const descriptor = this.getPropertyDescriptor(name, key, options);\n      if (descriptor !== undefined) {\n        Object.defineProperty(this.prototype, name, descriptor);\n        if (DEV_MODE) {\n          // If this class doesn't have its own set, create one and initialize\n          // with the values in the set from the nearest ancestor class, if any.\n          if (!this.hasOwnProperty('__reactivePropertyKeys')) {\n            this.__reactivePropertyKeys = new Set((_a = this.__reactivePropertyKeys) !== null && _a !== void 0 ? _a : []);\n          }\n          this.__reactivePropertyKeys.add(name);\n        }\n      }\n    }\n  }\n  /**\n   * Returns a property descriptor to be defined on the given named property.\n   * If no descriptor is returned, the property will not become an accessor.\n   * For example,\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   static getPropertyDescriptor(name, key, options) {\n   *     const defaultDescriptor =\n   *         super.getPropertyDescriptor(name, key, options);\n   *     const setter = defaultDescriptor.set;\n   *     return {\n   *       get: defaultDescriptor.get,\n   *       set(value) {\n   *         setter.call(this, value);\n   *         // custom action.\n   *       },\n   *       configurable: true,\n   *       enumerable: true\n   *     }\n   *   }\n   * }\n   * ```\n   *\n   * @nocollapse\n   * @category properties\n   */\n  static getPropertyDescriptor(name, key, options) {\n    return {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      get() {\n        return this[key];\n      },\n      set(value) {\n        const oldValue = this[name];\n        this[key] = value;\n        this.requestUpdate(name, oldValue, options);\n      },\n      configurable: true,\n      enumerable: true\n    };\n  }\n  /**\n   * Returns the property options associated with the given property.\n   * These options are defined with a `PropertyDeclaration` via the `properties`\n   * object or the `@property` decorator and are registered in\n   * `createProperty(...)`.\n   *\n   * Note, this method should be considered \"final\" and not overridden. To\n   * customize the options for a given property, override\n   * {@linkcode createProperty}.\n   *\n   * @nocollapse\n   * @final\n   * @category properties\n   */\n  static getPropertyOptions(name) {\n    return this.elementProperties.get(name) || defaultPropertyDeclaration;\n  }\n  /**\n   * Creates property accessors for registered properties, sets up element\n   * styling, and ensures any superclasses are also finalized. Returns true if\n   * the element was finalized.\n   * @nocollapse\n   */\n  static finalize() {\n    if (this.hasOwnProperty(finalized)) {\n      return false;\n    }\n    this[finalized] = true;\n    // finalize any superclasses\n    const superCtor = Object.getPrototypeOf(this);\n    superCtor.finalize();\n    // Create own set of initializers for this class if any exist on the\n    // superclass and copy them down. Note, for a small perf boost, avoid\n    // creating initializers unless needed.\n    if (superCtor._initializers !== undefined) {\n      this._initializers = [...superCtor._initializers];\n    }\n    this.elementProperties = new Map(superCtor.elementProperties);\n    // initialize Map populated in observedAttributes\n    this.__attributeToPropertyMap = new Map();\n    // make any properties\n    // Note, only process \"own\" properties since this element will inherit\n    // any properties defined on the superClass, and finalization ensures\n    // the entire prototype chain is finalized.\n    if (this.hasOwnProperty(JSCompiler_renameProperty('properties', this))) {\n      const props = this.properties;\n      // support symbols in properties (IE11 does not support this)\n      const propKeys = [...Object.getOwnPropertyNames(props), ...Object.getOwnPropertySymbols(props)];\n      // This for/of is ok because propKeys is an array\n      for (const p of propKeys) {\n        // note, use of `any` is due to TypeScript lack of support for symbol in\n        // index types\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        this.createProperty(p, props[p]);\n      }\n    }\n    this.elementStyles = this.finalizeStyles(this.styles);\n    // DEV mode warnings\n    if (DEV_MODE) {\n      const warnRemovedOrRenamed = (name, renamed = false) => {\n        if (this.prototype.hasOwnProperty(name)) {\n          issueWarning(renamed ? 'renamed-api' : 'removed-api', `\\`${name}\\` is implemented on class ${this.name}. It ` + `has been ${renamed ? 'renamed' : 'removed'} ` + `in this version of LitElement.`);\n        }\n      };\n      warnRemovedOrRenamed('initialize');\n      warnRemovedOrRenamed('requestUpdateInternal');\n      warnRemovedOrRenamed('_getUpdateComplete', true);\n    }\n    return true;\n  }\n  /**\n   * Takes the styles the user supplied via the `static styles` property and\n   * returns the array of styles to apply to the element.\n   * Override this method to integrate into a style management system.\n   *\n   * Styles are deduplicated preserving the _last_ instance in the list. This\n   * is a performance optimization to avoid duplicated styles that can occur\n   * especially when composing via subclassing. The last item is kept to try\n   * to preserve the cascade order with the assumption that it's most important\n   * that last added styles override previous styles.\n   *\n   * @nocollapse\n   * @category styles\n   */\n  static finalizeStyles(styles) {\n    const elementStyles = [];\n    if (Array.isArray(styles)) {\n      // Dedupe the flattened array in reverse order to preserve the last items.\n      // Casting to Array<unknown> works around TS error that\n      // appears to come from trying to flatten a type CSSResultArray.\n      const set = new Set(styles.flat(Infinity).reverse());\n      // Then preserve original order by adding the set items in reverse order.\n      for (const s of set) {\n        elementStyles.unshift(getCompatibleStyle(s));\n      }\n    } else if (styles !== undefined) {\n      elementStyles.push(getCompatibleStyle(styles));\n    }\n    return elementStyles;\n  }\n  /**\n   * Returns the property name for the given attribute `name`.\n   * @nocollapse\n   */\n  static __attributeNameForProperty(name, options) {\n    const attribute = options.attribute;\n    return attribute === false ? undefined : typeof attribute === 'string' ? attribute : typeof name === 'string' ? name.toLowerCase() : undefined;\n  }\n  /**\n   * Internal only override point for customizing work done when elements\n   * are constructed.\n   */\n  __initialize() {\n    var _a;\n    this.__updatePromise = new Promise(res => this.enableUpdating = res);\n    this._$changedProperties = new Map();\n    this.__saveInstanceProperties();\n    // ensures first update will be caught by an early access of\n    // `updateComplete`\n    this.requestUpdate();\n    (_a = this.constructor._initializers) === null || _a === void 0 ? void 0 : _a.forEach(i => i(this));\n  }\n  /**\n   * Registers a `ReactiveController` to participate in the element's reactive\n   * update cycle. The element automatically calls into any registered\n   * controllers during its lifecycle callbacks.\n   *\n   * If the element is connected when `addController()` is called, the\n   * controller's `hostConnected()` callback will be immediately called.\n   * @category controllers\n   */\n  addController(controller) {\n    var _a, _b;\n    ((_a = this.__controllers) !== null && _a !== void 0 ? _a : this.__controllers = []).push(controller);\n    // If a controller is added after the element has been connected,\n    // call hostConnected. Note, re-using existence of `renderRoot` here\n    // (which is set in connectedCallback) to avoid the need to track a\n    // first connected state.\n    if (this.renderRoot !== undefined && this.isConnected) {\n      (_b = controller.hostConnected) === null || _b === void 0 ? void 0 : _b.call(controller);\n    }\n  }\n  /**\n   * Removes a `ReactiveController` from the element.\n   * @category controllers\n   */\n  removeController(controller) {\n    var _a;\n    // Note, if the indexOf is -1, the >>> will flip the sign which makes the\n    // splice do nothing.\n    (_a = this.__controllers) === null || _a === void 0 ? void 0 : _a.splice(this.__controllers.indexOf(controller) >>> 0, 1);\n  }\n  /**\n   * Fixes any properties set on the instance before upgrade time.\n   * Otherwise these would shadow the accessor and break these properties.\n   * The properties are stored in a Map which is played back after the\n   * constructor runs. Note, on very old versions of Safari (<=9) or Chrome\n   * (<=41), properties created for native platform properties like (`id` or\n   * `name`) may not have default values set in the element constructor. On\n   * these browsers native properties appear on instances and therefore their\n   * default value will overwrite any element default (e.g. if the element sets\n   * this.id = 'id' in the constructor, the 'id' will become '' since this is\n   * the native platform default).\n   */\n  __saveInstanceProperties() {\n    // Use forEach so this works even if for/of loops are compiled to for loops\n    // expecting arrays\n    this.constructor.elementProperties.forEach((_v, p) => {\n      if (this.hasOwnProperty(p)) {\n        this.__instanceProperties.set(p, this[p]);\n        delete this[p];\n      }\n    });\n  }\n  /**\n   * Returns the node into which the element should render and by default\n   * creates and returns an open shadowRoot. Implement to customize where the\n   * element's DOM is rendered. For example, to render into the element's\n   * childNodes, return `this`.\n   *\n   * @return Returns a node into which to render.\n   * @category rendering\n   */\n  createRenderRoot() {\n    var _a;\n    const renderRoot = (_a = this.shadowRoot) !== null && _a !== void 0 ? _a : this.attachShadow(this.constructor.shadowRootOptions);\n    adoptStyles(renderRoot, this.constructor.elementStyles);\n    return renderRoot;\n  }\n  /**\n   * On first connection, creates the element's renderRoot, sets up\n   * element styling, and enables updating.\n   * @category lifecycle\n   */\n  connectedCallback() {\n    var _a;\n    // create renderRoot before first update.\n    if (this.renderRoot === undefined) {\n      this.renderRoot = this.createRenderRoot();\n    }\n    this.enableUpdating(true);\n    (_a = this.__controllers) === null || _a === void 0 ? void 0 : _a.forEach(c => {\n      var _a;\n      return (_a = c.hostConnected) === null || _a === void 0 ? void 0 : _a.call(c);\n    });\n  }\n  /**\n   * Note, this method should be considered final and not overridden. It is\n   * overridden on the element instance with a function that triggers the first\n   * update.\n   * @category updates\n   */\n  enableUpdating(_requestedUpdate) {}\n  /**\n   * Allows for `super.disconnectedCallback()` in extensions while\n   * reserving the possibility of making non-breaking feature additions\n   * when disconnecting at some point in the future.\n   * @category lifecycle\n   */\n  disconnectedCallback() {\n    var _a;\n    (_a = this.__controllers) === null || _a === void 0 ? void 0 : _a.forEach(c => {\n      var _a;\n      return (_a = c.hostDisconnected) === null || _a === void 0 ? void 0 : _a.call(c);\n    });\n  }\n  /**\n   * Synchronizes property values when attributes change.\n   *\n   * Specifically, when an attribute is set, the corresponding property is set.\n   * You should rarely need to implement this callback. If this method is\n   * overridden, `super.attributeChangedCallback(name, _old, value)` must be\n   * called.\n   *\n   * See [using the lifecycle callbacks](https://developer.mozilla.org/en-US/docs/Web/Web_Components/Using_custom_elements#using_the_lifecycle_callbacks)\n   * on MDN for more information about the `attributeChangedCallback`.\n   * @category attributes\n   */\n  attributeChangedCallback(name, _old, value) {\n    this._$attributeToProperty(name, value);\n  }\n  __propertyToAttribute(name, value, options = defaultPropertyDeclaration) {\n    var _a;\n    const attr = this.constructor.__attributeNameForProperty(name, options);\n    if (attr !== undefined && options.reflect === true) {\n      const converter = ((_a = options.converter) === null || _a === void 0 ? void 0 : _a.toAttribute) !== undefined ? options.converter : defaultConverter;\n      const attrValue = converter.toAttribute(value, options.type);\n      if (DEV_MODE && this.constructor.enabledWarnings.indexOf('migration') >= 0 && attrValue === undefined) {\n        issueWarning('undefined-attribute-value', `The attribute value for the ${name} property is ` + `undefined on element ${this.localName}. The attribute will be ` + `removed, but in the previous version of \\`ReactiveElement\\`, ` + `the attribute would not have changed.`);\n      }\n      // Track if the property is being reflected to avoid\n      // setting the property again via `attributeChangedCallback`. Note:\n      // 1. this takes advantage of the fact that the callback is synchronous.\n      // 2. will behave incorrectly if multiple attributes are in the reaction\n      // stack at time of calling. However, since we process attributes\n      // in `update` this should not be possible (or an extreme corner case\n      // that we'd like to discover).\n      // mark state reflecting\n      this.__reflectingProperty = name;\n      if (attrValue == null) {\n        this.removeAttribute(attr);\n      } else {\n        this.setAttribute(attr, attrValue);\n      }\n      // mark state not reflecting\n      this.__reflectingProperty = null;\n    }\n  }\n  /** @internal */\n  _$attributeToProperty(name, value) {\n    var _a;\n    const ctor = this.constructor;\n    // Note, hint this as an `AttributeMap` so closure clearly understands\n    // the type; it has issues with tracking types through statics\n    const propName = ctor.__attributeToPropertyMap.get(name);\n    // Use tracking info to avoid reflecting a property value to an attribute\n    // if it was just set because the attribute changed.\n    if (propName !== undefined && this.__reflectingProperty !== propName) {\n      const options = ctor.getPropertyOptions(propName);\n      const converter = typeof options.converter === 'function' ? {\n        fromAttribute: options.converter\n      } : ((_a = options.converter) === null || _a === void 0 ? void 0 : _a.fromAttribute) !== undefined ? options.converter : defaultConverter;\n      // mark state reflecting\n      this.__reflectingProperty = propName;\n      this[propName] = converter.fromAttribute(value, options.type\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      );\n      // mark state not reflecting\n      this.__reflectingProperty = null;\n    }\n  }\n  /**\n   * Requests an update which is processed asynchronously. This should be called\n   * when an element should update based on some state not triggered by setting\n   * a reactive property. In this case, pass no arguments. It should also be\n   * called when manually implementing a property setter. In this case, pass the\n   * property `name` and `oldValue` to ensure that any configured property\n   * options are honored.\n   *\n   * @param name name of requesting property\n   * @param oldValue old value of requesting property\n   * @param options property options to use instead of the previously\n   *     configured options\n   * @category updates\n   */\n  requestUpdate(name, oldValue, options) {\n    let shouldRequestUpdate = true;\n    // If we have a property key, perform property update steps.\n    if (name !== undefined) {\n      options = options || this.constructor.getPropertyOptions(name);\n      const hasChanged = options.hasChanged || notEqual;\n      if (hasChanged(this[name], oldValue)) {\n        if (!this._$changedProperties.has(name)) {\n          this._$changedProperties.set(name, oldValue);\n        }\n        // Add to reflecting properties set.\n        // Note, it's important that every change has a chance to add the\n        // property to `_reflectingProperties`. This ensures setting\n        // attribute + property reflects correctly.\n        if (options.reflect === true && this.__reflectingProperty !== name) {\n          if (this.__reflectingProperties === undefined) {\n            this.__reflectingProperties = new Map();\n          }\n          this.__reflectingProperties.set(name, options);\n        }\n      } else {\n        // Abort the request if the property should not be considered changed.\n        shouldRequestUpdate = false;\n      }\n    }\n    if (!this.isUpdatePending && shouldRequestUpdate) {\n      this.__updatePromise = this.__enqueueUpdate();\n    }\n    // Note, since this no longer returns a promise, in dev mode we return a\n    // thenable which warns if it's called.\n    return DEV_MODE ? requestUpdateThenable(this.localName) : undefined;\n  }\n  /**\n   * Sets up the element to asynchronously update.\n   */\n  __enqueueUpdate() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.isUpdatePending = true;\n      try {\n        // Ensure any previous update has resolved before updating.\n        // This `await` also ensures that property changes are batched.\n        yield _this.__updatePromise;\n      } catch (e) {\n        // Refire any previous errors async so they do not disrupt the update\n        // cycle. Errors are refired so developers have a chance to observe\n        // them, and this can be done by implementing\n        // `window.onunhandledrejection`.\n        Promise.reject(e);\n      }\n      const result = _this.scheduleUpdate();\n      // If `scheduleUpdate` returns a Promise, we await it. This is done to\n      // enable coordinating updates with a scheduler. Note, the result is\n      // checked to avoid delaying an additional microtask unless we need to.\n      if (result != null) {\n        yield result;\n      }\n      return !_this.isUpdatePending;\n    })();\n  }\n  /**\n   * Schedules an element update. You can override this method to change the\n   * timing of updates by returning a Promise. The update will await the\n   * returned Promise, and you should resolve the Promise to allow the update\n   * to proceed. If this method is overridden, `super.scheduleUpdate()`\n   * must be called.\n   *\n   * For instance, to schedule updates to occur just before the next frame:\n   *\n   * ```ts\n   * override protected async scheduleUpdate(): Promise<unknown> {\n   *   await new Promise((resolve) => requestAnimationFrame(() => resolve()));\n   *   super.scheduleUpdate();\n   * }\n   * ```\n   * @category updates\n   */\n  scheduleUpdate() {\n    return this.performUpdate();\n  }\n  /**\n   * Performs an element update. Note, if an exception is thrown during the\n   * update, `firstUpdated` and `updated` will not be called.\n   *\n   * Call `performUpdate()` to immediately process a pending update. This should\n   * generally not be needed, but it can be done in rare cases when you need to\n   * update synchronously.\n   *\n   * Note: To ensure `performUpdate()` synchronously completes a pending update,\n   * it should not be overridden. In LitElement 2.x it was suggested to override\n   * `performUpdate()` to also customizing update scheduling. Instead, you should now\n   * override `scheduleUpdate()`. For backwards compatibility with LitElement 2.x,\n   * scheduling updates via `performUpdate()` continues to work, but will make\n   * also calling `performUpdate()` to synchronously process updates difficult.\n   *\n   * @category updates\n   */\n  performUpdate() {\n    var _a, _b;\n    // Abort any update if one is not pending when this is called.\n    // This can happen if `performUpdate` is called early to \"flush\"\n    // the update.\n    if (!this.isUpdatePending) {\n      return;\n    }\n    debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({\n      kind: 'update'\n    });\n    // create renderRoot before first update.\n    if (!this.hasUpdated) {\n      // Produce warning if any class properties are shadowed by class fields\n      if (DEV_MODE) {\n        const shadowedProperties = [];\n        (_a = this.constructor.__reactivePropertyKeys) === null || _a === void 0 ? void 0 : _a.forEach(p => {\n          var _a;\n          if (this.hasOwnProperty(p) && !((_a = this.__instanceProperties) === null || _a === void 0 ? void 0 : _a.has(p))) {\n            shadowedProperties.push(p);\n          }\n        });\n        if (shadowedProperties.length) {\n          throw new Error(`The following properties on element ${this.localName} will not ` + `trigger updates as expected because they are set using class ` + `fields: ${shadowedProperties.join(', ')}. ` + `Native class fields and some compiled output will overwrite ` + `accessors used for detecting changes. See ` + `https://lit.dev/msg/class-field-shadowing ` + `for more information.`);\n        }\n      }\n    }\n    // Mixin instance properties once, if they exist.\n    if (this.__instanceProperties) {\n      // Use forEach so this works even if for/of loops are compiled to for loops\n      // expecting arrays\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      this.__instanceProperties.forEach((v, p) => this[p] = v);\n      this.__instanceProperties = undefined;\n    }\n    let shouldUpdate = false;\n    const changedProperties = this._$changedProperties;\n    try {\n      shouldUpdate = this.shouldUpdate(changedProperties);\n      if (shouldUpdate) {\n        this.willUpdate(changedProperties);\n        (_b = this.__controllers) === null || _b === void 0 ? void 0 : _b.forEach(c => {\n          var _a;\n          return (_a = c.hostUpdate) === null || _a === void 0 ? void 0 : _a.call(c);\n        });\n        this.update(changedProperties);\n      } else {\n        this.__markUpdated();\n      }\n    } catch (e) {\n      // Prevent `firstUpdated` and `updated` from running when there's an\n      // update exception.\n      shouldUpdate = false;\n      // Ensure element can accept additional updates after an exception.\n      this.__markUpdated();\n      throw e;\n    }\n    // The update is no longer considered pending and further updates are now allowed.\n    if (shouldUpdate) {\n      this._$didUpdate(changedProperties);\n    }\n  }\n  /**\n   * Invoked before `update()` to compute values needed during the update.\n   *\n   * Implement `willUpdate` to compute property values that depend on other\n   * properties and are used in the rest of the update process.\n   *\n   * ```ts\n   * willUpdate(changedProperties) {\n   *   // only need to check changed properties for an expensive computation.\n   *   if (changedProperties.has('firstName') || changedProperties.has('lastName')) {\n   *     this.sha = computeSHA(`${this.firstName} ${this.lastName}`);\n   *   }\n   * }\n   *\n   * render() {\n   *   return html`SHA: ${this.sha}`;\n   * }\n   * ```\n   *\n   * @category updates\n   */\n  willUpdate(_changedProperties) {}\n  // Note, this is an override point for polyfill-support.\n  // @internal\n  _$didUpdate(changedProperties) {\n    var _a;\n    (_a = this.__controllers) === null || _a === void 0 ? void 0 : _a.forEach(c => {\n      var _a;\n      return (_a = c.hostUpdated) === null || _a === void 0 ? void 0 : _a.call(c);\n    });\n    if (!this.hasUpdated) {\n      this.hasUpdated = true;\n      this.firstUpdated(changedProperties);\n    }\n    this.updated(changedProperties);\n    if (DEV_MODE && this.isUpdatePending && this.constructor.enabledWarnings.indexOf('change-in-update') >= 0) {\n      issueWarning('change-in-update', `Element ${this.localName} scheduled an update ` + `(generally because a property was set) ` + `after an update completed, causing a new update to be scheduled. ` + `This is inefficient and should be avoided unless the next update ` + `can only be scheduled as a side effect of the previous update.`);\n    }\n  }\n  __markUpdated() {\n    this._$changedProperties = new Map();\n    this.isUpdatePending = false;\n  }\n  /**\n   * Returns a Promise that resolves when the element has completed updating.\n   * The Promise value is a boolean that is `true` if the element completed the\n   * update without triggering another update. The Promise result is `false` if\n   * a property was set inside `updated()`. If the Promise is rejected, an\n   * exception was thrown during the update.\n   *\n   * To await additional asynchronous work, override the `getUpdateComplete`\n   * method. For example, it is sometimes useful to await a rendered element\n   * before fulfilling this Promise. To do this, first await\n   * `super.getUpdateComplete()`, then any subsequent state.\n   *\n   * @return A promise of a boolean that resolves to true if the update completed\n   *     without triggering another update.\n   * @category updates\n   */\n  get updateComplete() {\n    return this.getUpdateComplete();\n  }\n  /**\n   * Override point for the `updateComplete` promise.\n   *\n   * It is not safe to override the `updateComplete` getter directly due to a\n   * limitation in TypeScript which means it is not possible to call a\n   * superclass getter (e.g. `super.updateComplete.then(...)`) when the target\n   * language is ES5 (https://github.com/microsoft/TypeScript/issues/338).\n   * This method should be overridden instead. For example:\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   override async getUpdateComplete() {\n   *     const result = await super.getUpdateComplete();\n   *     await this._myChild.updateComplete;\n   *     return result;\n   *   }\n   * }\n   * ```\n   *\n   * @return A promise of a boolean that resolves to true if the update completed\n   *     without triggering another update.\n   * @category updates\n   */\n  getUpdateComplete() {\n    return this.__updatePromise;\n  }\n  /**\n   * Controls whether or not `update()` should be called when the element requests\n   * an update. By default, this method always returns `true`, but this can be\n   * customized to control when to update.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  shouldUpdate(_changedProperties) {\n    return true;\n  }\n  /**\n   * Updates the element. This method reflects property values to attributes.\n   * It can be overridden to render and keep updated element DOM.\n   * Setting properties inside this method will *not* trigger\n   * another update.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  update(_changedProperties) {\n    if (this.__reflectingProperties !== undefined) {\n      // Use forEach so this works even if for/of loops are compiled to for\n      // loops expecting arrays\n      this.__reflectingProperties.forEach((v, k) => this.__propertyToAttribute(k, this[k], v));\n      this.__reflectingProperties = undefined;\n    }\n    this.__markUpdated();\n  }\n  /**\n   * Invoked whenever the element is updated. Implement to perform\n   * post-updating tasks via DOM APIs, for example, focusing an element.\n   *\n   * Setting properties inside this method will trigger the element to update\n   * again after this update cycle completes.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  updated(_changedProperties) {}\n  /**\n   * Invoked when the element is first updated. Implement to perform one time\n   * work on the element after update.\n   *\n   * ```ts\n   * firstUpdated() {\n   *   this.renderRoot.getElementById('my-text-area').focus();\n   * }\n   * ```\n   *\n   * Setting properties inside this method will trigger the element to update\n   * again after this update cycle completes.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  firstUpdated(_changedProperties) {}\n}\n_e = finalized;\n/**\n * Marks class as having finished creating properties.\n */\nReactiveElement[_e] = true;\n/**\n * Memoized list of all element properties, including any superclass properties.\n * Created lazily on user subclasses when finalizing the class.\n * @nocollapse\n * @category properties\n */\nReactiveElement.elementProperties = new Map();\n/**\n * Memoized list of all element styles.\n * Created lazily on user subclasses when finalizing the class.\n * @nocollapse\n * @category styles\n */\nReactiveElement.elementStyles = [];\n/**\n * Options used when calling `attachShadow`. Set this property to customize\n * the options for the shadowRoot; for example, to create a closed\n * shadowRoot: `{mode: 'closed'}`.\n *\n * Note, these options are used in `createRenderRoot`. If this method\n * is customized, options should be respected if possible.\n * @nocollapse\n * @category rendering\n */\nReactiveElement.shadowRootOptions = {\n  mode: 'open'\n};\n// Apply polyfills if available\npolyfillSupport === null || polyfillSupport === void 0 ? void 0 : polyfillSupport({\n  ReactiveElement\n});\n// Dev mode warnings...\nif (DEV_MODE) {\n  // Default warning set.\n  ReactiveElement.enabledWarnings = ['change-in-update'];\n  const ensureOwnWarnings = function (ctor) {\n    if (!ctor.hasOwnProperty(JSCompiler_renameProperty('enabledWarnings', ctor))) {\n      ctor.enabledWarnings = ctor.enabledWarnings.slice();\n    }\n  };\n  ReactiveElement.enableWarning = function (warning) {\n    ensureOwnWarnings(this);\n    if (this.enabledWarnings.indexOf(warning) < 0) {\n      this.enabledWarnings.push(warning);\n    }\n  };\n  ReactiveElement.disableWarning = function (warning) {\n    ensureOwnWarnings(this);\n    const i = this.enabledWarnings.indexOf(warning);\n    if (i >= 0) {\n      this.enabledWarnings.splice(i, 1);\n    }\n  };\n}\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for ReactiveElement usage.\n((_d = global.reactiveElementVersions) !== null && _d !== void 0 ? _d : global.reactiveElementVersions = []).push('1.6.3');\nif (DEV_MODE && global.reactiveElementVersions.length > 1) {\n  issueWarning('multiple-versions', `Multiple versions of Lit loaded. Loading multiple versions ` + `is not recommended.`);\n}", "map": {"version": 3, "names": ["_a", "_b", "_c", "_d", "_e", "getCompatibleStyle", "adoptStyles", "NODE_MODE", "global", "globalThis", "window", "customElements", "DEV_MODE", "requestUpdateThenable", "issueWarning", "trustedTypes", "emptyStringForBooleanAttribute", "emptyScript", "polyfillSupport", "reactiveElementPolyfillSupportDevMode", "reactiveElementPolyfillSupport", "issuedWarnings", "litIssuedWarnings", "Set", "code", "warning", "has", "console", "warn", "add", "ShadyDOM", "inUse", "undefined", "name", "then", "onfulfilled", "_onrejected", "debugLogEvent", "event", "shouldEmit", "emitLitDebugLogEvents", "dispatchEvent", "CustomEvent", "detail", "JSCompiler_renameProperty", "prop", "_obj", "defaultConverter", "toAttribute", "value", "type", "Boolean", "Object", "Array", "JSON", "stringify", "fromAttribute", "fromValue", "Number", "parse", "e", "notEqual", "old", "defaultPropertyDeclaration", "attribute", "String", "converter", "reflect", "has<PERSON><PERSON>ed", "finalized", "ReactiveElement", "HTMLElement", "constructor", "__instanceProperties", "Map", "isUpdatePending", "hasUpdated", "__reflectingProperty", "__initialize", "addInitializer", "initializer", "finalize", "_initializers", "push", "observedAttributes", "attributes", "elementProperties", "for<PERSON>ach", "v", "p", "attr", "__attributeNameForProperty", "__attributeToPropertyMap", "set", "createProperty", "options", "state", "noAccessor", "prototype", "hasOwnProperty", "key", "Symbol", "descriptor", "getPropertyDescriptor", "defineProperty", "__reactivePropertyKeys", "get", "oldValue", "requestUpdate", "configurable", "enumerable", "getPropertyOptions", "superCtor", "getPrototypeOf", "props", "properties", "propKeys", "getOwnPropertyNames", "getOwnPropertySymbols", "elementStyles", "finalizeStyles", "styles", "warnRemovedOrRenamed", "renamed", "isArray", "flat", "Infinity", "reverse", "s", "unshift", "toLowerCase", "__updatePromise", "Promise", "res", "enableUpdating", "_$changedProperties", "__saveInstanceProperties", "i", "addController", "controller", "__controllers", "renderRoot", "isConnected", "hostConnected", "call", "removeController", "splice", "indexOf", "_v", "createRenderRoot", "shadowRoot", "attachShadow", "shadowRootOptions", "connectedCallback", "c", "_requestedUpdate", "disconnectedCallback", "hostDisconnected", "attributeChangedCallback", "_old", "_$attributeToProperty", "__propertyToAttribute", "attrValue", "enabledWarnings", "localName", "removeAttribute", "setAttribute", "ctor", "propName", "shouldRequestUpdate", "__reflectingProperties", "__enqueueUpdate", "_this", "_asyncToGenerator", "reject", "result", "scheduleUpdate", "performUpdate", "kind", "shadowedProperties", "length", "Error", "join", "shouldUpdate", "changedProperties", "willUpdate", "hostUpdate", "update", "__markUpdated", "_$didUpdate", "_changedProperties", "hostUpdated", "firstUpdated", "updated", "updateComplete", "getUpdateComplete", "k", "mode", "ensureOwnWarnings", "slice", "enableWarning", "disableWarning", "reactiveElementVersions"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@lit/reactive-element/development/reactive-element.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nvar _a, _b, _c, _d;\nvar _e;\n/**\n * Use this module if you want to create your own base class extending\n * {@link ReactiveElement}.\n * @packageDocumentation\n */\nimport { getCompatibleStyle, adoptStyles, } from './css-tag.js';\n// In the Node build, this import will be injected by Rollup:\n// import {HTMLElement, customElements} from '@lit-labs/ssr-dom-shim';\nexport * from './css-tag.js';\nconst NODE_MODE = false;\nconst global = NODE_MODE ? globalThis : window;\nif (NODE_MODE) {\n    (_a = global.customElements) !== null && _a !== void 0 ? _a : (global.customElements = customElements);\n}\nconst DEV_MODE = true;\nlet requestUpdateThenable;\nlet issueWarning;\nconst trustedTypes = global\n    .trustedTypes;\n// Temporary workaround for https://crbug.com/993268\n// Currently, any attribute starting with \"on\" is considered to be a\n// TrustedScript source. Such boolean attributes must be set to the equivalent\n// trusted emptyScript value.\nconst emptyStringForBooleanAttribute = trustedTypes\n    ? trustedTypes.emptyScript\n    : '';\nconst polyfillSupport = DEV_MODE\n    ? global.reactiveElementPolyfillSupportDevMode\n    : global.reactiveElementPolyfillSupport;\nif (DEV_MODE) {\n    // Ensure warnings are issued only 1x, even if multiple versions of Lit\n    // are loaded.\n    const issuedWarnings = ((_b = global.litIssuedWarnings) !== null && _b !== void 0 ? _b : (global.litIssuedWarnings = new Set()));\n    // Issue a warning, if we haven't already.\n    issueWarning = (code, warning) => {\n        warning += ` See https://lit.dev/msg/${code} for more information.`;\n        if (!issuedWarnings.has(warning)) {\n            console.warn(warning);\n            issuedWarnings.add(warning);\n        }\n    };\n    issueWarning('dev-mode', `Lit is in dev mode. Not recommended for production!`);\n    // Issue polyfill support warning.\n    if (((_c = global.ShadyDOM) === null || _c === void 0 ? void 0 : _c.inUse) && polyfillSupport === undefined) {\n        issueWarning('polyfill-support-missing', `Shadow DOM is being polyfilled via \\`ShadyDOM\\` but ` +\n            `the \\`polyfill-support\\` module has not been loaded.`);\n    }\n    requestUpdateThenable = (name) => ({\n        then: (onfulfilled, _onrejected) => {\n            issueWarning('request-update-promise', `The \\`requestUpdate\\` method should no longer return a Promise but ` +\n                `does so on \\`${name}\\`. Use \\`updateComplete\\` instead.`);\n            if (onfulfilled !== undefined) {\n                onfulfilled(false);\n            }\n        },\n    });\n}\n/**\n * Useful for visualizing and logging insights into what the Lit template system is doing.\n *\n * Compiled out of prod mode builds.\n */\nconst debugLogEvent = DEV_MODE\n    ? (event) => {\n        const shouldEmit = global\n            .emitLitDebugLogEvents;\n        if (!shouldEmit) {\n            return;\n        }\n        global.dispatchEvent(new CustomEvent('lit-debug', {\n            detail: event,\n        }));\n    }\n    : undefined;\n/*\n * When using Closure Compiler, JSCompiler_renameProperty(property, object) is\n * replaced at compile time by the munged name for object[property]. We cannot\n * alias this function, so we have to use a small shim that has the same\n * behavior when not compiling.\n */\n/*@__INLINE__*/\nconst JSCompiler_renameProperty = (prop, _obj) => prop;\nexport const defaultConverter = {\n    toAttribute(value, type) {\n        switch (type) {\n            case Boolean:\n                value = value ? emptyStringForBooleanAttribute : null;\n                break;\n            case Object:\n            case Array:\n                // if the value is `null` or `undefined` pass this through\n                // to allow removing/no change behavior.\n                value = value == null ? value : JSON.stringify(value);\n                break;\n        }\n        return value;\n    },\n    fromAttribute(value, type) {\n        let fromValue = value;\n        switch (type) {\n            case Boolean:\n                fromValue = value !== null;\n                break;\n            case Number:\n                fromValue = value === null ? null : Number(value);\n                break;\n            case Object:\n            case Array:\n                // Do *not* generate exception when invalid JSON is set as elements\n                // don't normally complain on being mis-configured.\n                // TODO(sorvell): Do generate exception in *dev mode*.\n                try {\n                    // Assert to adhere to Bazel's \"must type assert JSON parse\" rule.\n                    fromValue = JSON.parse(value);\n                }\n                catch (e) {\n                    fromValue = null;\n                }\n                break;\n        }\n        return fromValue;\n    },\n};\n/**\n * Change function that returns true if `value` is different from `oldValue`.\n * This method is used as the default for a property's `hasChanged` function.\n */\nexport const notEqual = (value, old) => {\n    // This ensures (old==NaN, value==NaN) always returns false\n    return old !== value && (old === old || value === value);\n};\nconst defaultPropertyDeclaration = {\n    attribute: true,\n    type: String,\n    converter: defaultConverter,\n    reflect: false,\n    hasChanged: notEqual,\n};\n/**\n * The Closure JS Compiler doesn't currently have good support for static\n * property semantics where \"this\" is dynamic (e.g.\n * https://github.com/google/closure-compiler/issues/3177 and others) so we use\n * this hack to bypass any rewriting by the compiler.\n */\nconst finalized = 'finalized';\n/**\n * Base element class which manages element properties and attributes. When\n * properties change, the `update` method is asynchronously called. This method\n * should be supplied by subclassers to render updates as desired.\n * @noInheritDoc\n */\nexport class ReactiveElement\n// In the Node build, this `extends` clause will be substituted with\n// `(globalThis.HTMLElement ?? HTMLElement)`.\n//\n// This way, we will first prefer any global `HTMLElement` polyfill that the\n// user has assigned, and then fall back to the `HTMLElement` shim which has\n// been imported (see note at the top of this file about how this import is\n// generated by Rollup). Note that the `HTMLElement` variable has been\n// shadowed by this import, so it no longer refers to the global.\n extends HTMLElement {\n    constructor() {\n        super();\n        this.__instanceProperties = new Map();\n        /**\n         * True if there is a pending update as a result of calling `requestUpdate()`.\n         * Should only be read.\n         * @category updates\n         */\n        this.isUpdatePending = false;\n        /**\n         * Is set to `true` after the first update. The element code cannot assume\n         * that `renderRoot` exists before the element `hasUpdated`.\n         * @category updates\n         */\n        this.hasUpdated = false;\n        /**\n         * Name of currently reflecting property\n         */\n        this.__reflectingProperty = null;\n        this.__initialize();\n    }\n    /**\n     * Adds an initializer function to the class that is called during instance\n     * construction.\n     *\n     * This is useful for code that runs against a `ReactiveElement`\n     * subclass, such as a decorator, that needs to do work for each\n     * instance, such as setting up a `ReactiveController`.\n     *\n     * ```ts\n     * const myDecorator = (target: typeof ReactiveElement, key: string) => {\n     *   target.addInitializer((instance: ReactiveElement) => {\n     *     // This is run during construction of the element\n     *     new MyController(instance);\n     *   });\n     * }\n     * ```\n     *\n     * Decorating a field will then cause each instance to run an initializer\n     * that adds a controller:\n     *\n     * ```ts\n     * class MyElement extends LitElement {\n     *   @myDecorator foo;\n     * }\n     * ```\n     *\n     * Initializers are stored per-constructor. Adding an initializer to a\n     * subclass does not add it to a superclass. Since initializers are run in\n     * constructors, initializers will run in order of the class hierarchy,\n     * starting with superclasses and progressing to the instance's class.\n     *\n     * @nocollapse\n     */\n    static addInitializer(initializer) {\n        var _a;\n        this.finalize();\n        ((_a = this._initializers) !== null && _a !== void 0 ? _a : (this._initializers = [])).push(initializer);\n    }\n    /**\n     * Returns a list of attributes corresponding to the registered properties.\n     * @nocollapse\n     * @category attributes\n     */\n    static get observedAttributes() {\n        // note: piggy backing on this to ensure we're finalized.\n        this.finalize();\n        const attributes = [];\n        // Use forEach so this works even if for/of loops are compiled to for loops\n        // expecting arrays\n        this.elementProperties.forEach((v, p) => {\n            const attr = this.__attributeNameForProperty(p, v);\n            if (attr !== undefined) {\n                this.__attributeToPropertyMap.set(attr, p);\n                attributes.push(attr);\n            }\n        });\n        return attributes;\n    }\n    /**\n     * Creates a property accessor on the element prototype if one does not exist\n     * and stores a {@linkcode PropertyDeclaration} for the property with the\n     * given options. The property setter calls the property's `hasChanged`\n     * property option or uses a strict identity check to determine whether or not\n     * to request an update.\n     *\n     * This method may be overridden to customize properties; however,\n     * when doing so, it's important to call `super.createProperty` to ensure\n     * the property is setup correctly. This method calls\n     * `getPropertyDescriptor` internally to get a descriptor to install.\n     * To customize what properties do when they are get or set, override\n     * `getPropertyDescriptor`. To customize the options for a property,\n     * implement `createProperty` like this:\n     *\n     * ```ts\n     * static createProperty(name, options) {\n     *   options = Object.assign(options, {myOption: true});\n     *   super.createProperty(name, options);\n     * }\n     * ```\n     *\n     * @nocollapse\n     * @category properties\n     */\n    static createProperty(name, options = defaultPropertyDeclaration) {\n        var _a;\n        // if this is a state property, force the attribute to false.\n        if (options.state) {\n            // Cast as any since this is readonly.\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            options.attribute = false;\n        }\n        // Note, since this can be called by the `@property` decorator which\n        // is called before `finalize`, we ensure finalization has been kicked off.\n        this.finalize();\n        this.elementProperties.set(name, options);\n        // Do not generate an accessor if the prototype already has one, since\n        // it would be lost otherwise and that would never be the user's intention;\n        // Instead, we expect users to call `requestUpdate` themselves from\n        // user-defined accessors. Note that if the super has an accessor we will\n        // still overwrite it\n        if (!options.noAccessor && !this.prototype.hasOwnProperty(name)) {\n            const key = typeof name === 'symbol' ? Symbol() : `__${name}`;\n            const descriptor = this.getPropertyDescriptor(name, key, options);\n            if (descriptor !== undefined) {\n                Object.defineProperty(this.prototype, name, descriptor);\n                if (DEV_MODE) {\n                    // If this class doesn't have its own set, create one and initialize\n                    // with the values in the set from the nearest ancestor class, if any.\n                    if (!this.hasOwnProperty('__reactivePropertyKeys')) {\n                        this.__reactivePropertyKeys = new Set((_a = this.__reactivePropertyKeys) !== null && _a !== void 0 ? _a : []);\n                    }\n                    this.__reactivePropertyKeys.add(name);\n                }\n            }\n        }\n    }\n    /**\n     * Returns a property descriptor to be defined on the given named property.\n     * If no descriptor is returned, the property will not become an accessor.\n     * For example,\n     *\n     * ```ts\n     * class MyElement extends LitElement {\n     *   static getPropertyDescriptor(name, key, options) {\n     *     const defaultDescriptor =\n     *         super.getPropertyDescriptor(name, key, options);\n     *     const setter = defaultDescriptor.set;\n     *     return {\n     *       get: defaultDescriptor.get,\n     *       set(value) {\n     *         setter.call(this, value);\n     *         // custom action.\n     *       },\n     *       configurable: true,\n     *       enumerable: true\n     *     }\n     *   }\n     * }\n     * ```\n     *\n     * @nocollapse\n     * @category properties\n     */\n    static getPropertyDescriptor(name, key, options) {\n        return {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            get() {\n                return this[key];\n            },\n            set(value) {\n                const oldValue = this[name];\n                this[key] = value;\n                this.requestUpdate(name, oldValue, options);\n            },\n            configurable: true,\n            enumerable: true,\n        };\n    }\n    /**\n     * Returns the property options associated with the given property.\n     * These options are defined with a `PropertyDeclaration` via the `properties`\n     * object or the `@property` decorator and are registered in\n     * `createProperty(...)`.\n     *\n     * Note, this method should be considered \"final\" and not overridden. To\n     * customize the options for a given property, override\n     * {@linkcode createProperty}.\n     *\n     * @nocollapse\n     * @final\n     * @category properties\n     */\n    static getPropertyOptions(name) {\n        return this.elementProperties.get(name) || defaultPropertyDeclaration;\n    }\n    /**\n     * Creates property accessors for registered properties, sets up element\n     * styling, and ensures any superclasses are also finalized. Returns true if\n     * the element was finalized.\n     * @nocollapse\n     */\n    static finalize() {\n        if (this.hasOwnProperty(finalized)) {\n            return false;\n        }\n        this[finalized] = true;\n        // finalize any superclasses\n        const superCtor = Object.getPrototypeOf(this);\n        superCtor.finalize();\n        // Create own set of initializers for this class if any exist on the\n        // superclass and copy them down. Note, for a small perf boost, avoid\n        // creating initializers unless needed.\n        if (superCtor._initializers !== undefined) {\n            this._initializers = [...superCtor._initializers];\n        }\n        this.elementProperties = new Map(superCtor.elementProperties);\n        // initialize Map populated in observedAttributes\n        this.__attributeToPropertyMap = new Map();\n        // make any properties\n        // Note, only process \"own\" properties since this element will inherit\n        // any properties defined on the superClass, and finalization ensures\n        // the entire prototype chain is finalized.\n        if (this.hasOwnProperty(JSCompiler_renameProperty('properties', this))) {\n            const props = this.properties;\n            // support symbols in properties (IE11 does not support this)\n            const propKeys = [\n                ...Object.getOwnPropertyNames(props),\n                ...Object.getOwnPropertySymbols(props),\n            ];\n            // This for/of is ok because propKeys is an array\n            for (const p of propKeys) {\n                // note, use of `any` is due to TypeScript lack of support for symbol in\n                // index types\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                this.createProperty(p, props[p]);\n            }\n        }\n        this.elementStyles = this.finalizeStyles(this.styles);\n        // DEV mode warnings\n        if (DEV_MODE) {\n            const warnRemovedOrRenamed = (name, renamed = false) => {\n                if (this.prototype.hasOwnProperty(name)) {\n                    issueWarning(renamed ? 'renamed-api' : 'removed-api', `\\`${name}\\` is implemented on class ${this.name}. It ` +\n                        `has been ${renamed ? 'renamed' : 'removed'} ` +\n                        `in this version of LitElement.`);\n                }\n            };\n            warnRemovedOrRenamed('initialize');\n            warnRemovedOrRenamed('requestUpdateInternal');\n            warnRemovedOrRenamed('_getUpdateComplete', true);\n        }\n        return true;\n    }\n    /**\n     * Takes the styles the user supplied via the `static styles` property and\n     * returns the array of styles to apply to the element.\n     * Override this method to integrate into a style management system.\n     *\n     * Styles are deduplicated preserving the _last_ instance in the list. This\n     * is a performance optimization to avoid duplicated styles that can occur\n     * especially when composing via subclassing. The last item is kept to try\n     * to preserve the cascade order with the assumption that it's most important\n     * that last added styles override previous styles.\n     *\n     * @nocollapse\n     * @category styles\n     */\n    static finalizeStyles(styles) {\n        const elementStyles = [];\n        if (Array.isArray(styles)) {\n            // Dedupe the flattened array in reverse order to preserve the last items.\n            // Casting to Array<unknown> works around TS error that\n            // appears to come from trying to flatten a type CSSResultArray.\n            const set = new Set(styles.flat(Infinity).reverse());\n            // Then preserve original order by adding the set items in reverse order.\n            for (const s of set) {\n                elementStyles.unshift(getCompatibleStyle(s));\n            }\n        }\n        else if (styles !== undefined) {\n            elementStyles.push(getCompatibleStyle(styles));\n        }\n        return elementStyles;\n    }\n    /**\n     * Returns the property name for the given attribute `name`.\n     * @nocollapse\n     */\n    static __attributeNameForProperty(name, options) {\n        const attribute = options.attribute;\n        return attribute === false\n            ? undefined\n            : typeof attribute === 'string'\n                ? attribute\n                : typeof name === 'string'\n                    ? name.toLowerCase()\n                    : undefined;\n    }\n    /**\n     * Internal only override point for customizing work done when elements\n     * are constructed.\n     */\n    __initialize() {\n        var _a;\n        this.__updatePromise = new Promise((res) => (this.enableUpdating = res));\n        this._$changedProperties = new Map();\n        this.__saveInstanceProperties();\n        // ensures first update will be caught by an early access of\n        // `updateComplete`\n        this.requestUpdate();\n        (_a = this.constructor._initializers) === null || _a === void 0 ? void 0 : _a.forEach((i) => i(this));\n    }\n    /**\n     * Registers a `ReactiveController` to participate in the element's reactive\n     * update cycle. The element automatically calls into any registered\n     * controllers during its lifecycle callbacks.\n     *\n     * If the element is connected when `addController()` is called, the\n     * controller's `hostConnected()` callback will be immediately called.\n     * @category controllers\n     */\n    addController(controller) {\n        var _a, _b;\n        ((_a = this.__controllers) !== null && _a !== void 0 ? _a : (this.__controllers = [])).push(controller);\n        // If a controller is added after the element has been connected,\n        // call hostConnected. Note, re-using existence of `renderRoot` here\n        // (which is set in connectedCallback) to avoid the need to track a\n        // first connected state.\n        if (this.renderRoot !== undefined && this.isConnected) {\n            (_b = controller.hostConnected) === null || _b === void 0 ? void 0 : _b.call(controller);\n        }\n    }\n    /**\n     * Removes a `ReactiveController` from the element.\n     * @category controllers\n     */\n    removeController(controller) {\n        var _a;\n        // Note, if the indexOf is -1, the >>> will flip the sign which makes the\n        // splice do nothing.\n        (_a = this.__controllers) === null || _a === void 0 ? void 0 : _a.splice(this.__controllers.indexOf(controller) >>> 0, 1);\n    }\n    /**\n     * Fixes any properties set on the instance before upgrade time.\n     * Otherwise these would shadow the accessor and break these properties.\n     * The properties are stored in a Map which is played back after the\n     * constructor runs. Note, on very old versions of Safari (<=9) or Chrome\n     * (<=41), properties created for native platform properties like (`id` or\n     * `name`) may not have default values set in the element constructor. On\n     * these browsers native properties appear on instances and therefore their\n     * default value will overwrite any element default (e.g. if the element sets\n     * this.id = 'id' in the constructor, the 'id' will become '' since this is\n     * the native platform default).\n     */\n    __saveInstanceProperties() {\n        // Use forEach so this works even if for/of loops are compiled to for loops\n        // expecting arrays\n        this.constructor.elementProperties.forEach((_v, p) => {\n            if (this.hasOwnProperty(p)) {\n                this.__instanceProperties.set(p, this[p]);\n                delete this[p];\n            }\n        });\n    }\n    /**\n     * Returns the node into which the element should render and by default\n     * creates and returns an open shadowRoot. Implement to customize where the\n     * element's DOM is rendered. For example, to render into the element's\n     * childNodes, return `this`.\n     *\n     * @return Returns a node into which to render.\n     * @category rendering\n     */\n    createRenderRoot() {\n        var _a;\n        const renderRoot = (_a = this.shadowRoot) !== null && _a !== void 0 ? _a : this.attachShadow(this.constructor.shadowRootOptions);\n        adoptStyles(renderRoot, this.constructor.elementStyles);\n        return renderRoot;\n    }\n    /**\n     * On first connection, creates the element's renderRoot, sets up\n     * element styling, and enables updating.\n     * @category lifecycle\n     */\n    connectedCallback() {\n        var _a;\n        // create renderRoot before first update.\n        if (this.renderRoot === undefined) {\n            this.renderRoot = this.createRenderRoot();\n        }\n        this.enableUpdating(true);\n        (_a = this.__controllers) === null || _a === void 0 ? void 0 : _a.forEach((c) => { var _a; return (_a = c.hostConnected) === null || _a === void 0 ? void 0 : _a.call(c); });\n    }\n    /**\n     * Note, this method should be considered final and not overridden. It is\n     * overridden on the element instance with a function that triggers the first\n     * update.\n     * @category updates\n     */\n    enableUpdating(_requestedUpdate) { }\n    /**\n     * Allows for `super.disconnectedCallback()` in extensions while\n     * reserving the possibility of making non-breaking feature additions\n     * when disconnecting at some point in the future.\n     * @category lifecycle\n     */\n    disconnectedCallback() {\n        var _a;\n        (_a = this.__controllers) === null || _a === void 0 ? void 0 : _a.forEach((c) => { var _a; return (_a = c.hostDisconnected) === null || _a === void 0 ? void 0 : _a.call(c); });\n    }\n    /**\n     * Synchronizes property values when attributes change.\n     *\n     * Specifically, when an attribute is set, the corresponding property is set.\n     * You should rarely need to implement this callback. If this method is\n     * overridden, `super.attributeChangedCallback(name, _old, value)` must be\n     * called.\n     *\n     * See [using the lifecycle callbacks](https://developer.mozilla.org/en-US/docs/Web/Web_Components/Using_custom_elements#using_the_lifecycle_callbacks)\n     * on MDN for more information about the `attributeChangedCallback`.\n     * @category attributes\n     */\n    attributeChangedCallback(name, _old, value) {\n        this._$attributeToProperty(name, value);\n    }\n    __propertyToAttribute(name, value, options = defaultPropertyDeclaration) {\n        var _a;\n        const attr = this.constructor.__attributeNameForProperty(name, options);\n        if (attr !== undefined && options.reflect === true) {\n            const converter = ((_a = options.converter) === null || _a === void 0 ? void 0 : _a.toAttribute) !==\n                undefined\n                ? options.converter\n                : defaultConverter;\n            const attrValue = converter.toAttribute(value, options.type);\n            if (DEV_MODE &&\n                this.constructor.enabledWarnings.indexOf('migration') >= 0 &&\n                attrValue === undefined) {\n                issueWarning('undefined-attribute-value', `The attribute value for the ${name} property is ` +\n                    `undefined on element ${this.localName}. The attribute will be ` +\n                    `removed, but in the previous version of \\`ReactiveElement\\`, ` +\n                    `the attribute would not have changed.`);\n            }\n            // Track if the property is being reflected to avoid\n            // setting the property again via `attributeChangedCallback`. Note:\n            // 1. this takes advantage of the fact that the callback is synchronous.\n            // 2. will behave incorrectly if multiple attributes are in the reaction\n            // stack at time of calling. However, since we process attributes\n            // in `update` this should not be possible (or an extreme corner case\n            // that we'd like to discover).\n            // mark state reflecting\n            this.__reflectingProperty = name;\n            if (attrValue == null) {\n                this.removeAttribute(attr);\n            }\n            else {\n                this.setAttribute(attr, attrValue);\n            }\n            // mark state not reflecting\n            this.__reflectingProperty = null;\n        }\n    }\n    /** @internal */\n    _$attributeToProperty(name, value) {\n        var _a;\n        const ctor = this.constructor;\n        // Note, hint this as an `AttributeMap` so closure clearly understands\n        // the type; it has issues with tracking types through statics\n        const propName = ctor.__attributeToPropertyMap.get(name);\n        // Use tracking info to avoid reflecting a property value to an attribute\n        // if it was just set because the attribute changed.\n        if (propName !== undefined && this.__reflectingProperty !== propName) {\n            const options = ctor.getPropertyOptions(propName);\n            const converter = typeof options.converter === 'function'\n                ? { fromAttribute: options.converter }\n                : ((_a = options.converter) === null || _a === void 0 ? void 0 : _a.fromAttribute) !== undefined\n                    ? options.converter\n                    : defaultConverter;\n            // mark state reflecting\n            this.__reflectingProperty = propName;\n            this[propName] = converter.fromAttribute(value, options.type\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            );\n            // mark state not reflecting\n            this.__reflectingProperty = null;\n        }\n    }\n    /**\n     * Requests an update which is processed asynchronously. This should be called\n     * when an element should update based on some state not triggered by setting\n     * a reactive property. In this case, pass no arguments. It should also be\n     * called when manually implementing a property setter. In this case, pass the\n     * property `name` and `oldValue` to ensure that any configured property\n     * options are honored.\n     *\n     * @param name name of requesting property\n     * @param oldValue old value of requesting property\n     * @param options property options to use instead of the previously\n     *     configured options\n     * @category updates\n     */\n    requestUpdate(name, oldValue, options) {\n        let shouldRequestUpdate = true;\n        // If we have a property key, perform property update steps.\n        if (name !== undefined) {\n            options =\n                options ||\n                    this.constructor.getPropertyOptions(name);\n            const hasChanged = options.hasChanged || notEqual;\n            if (hasChanged(this[name], oldValue)) {\n                if (!this._$changedProperties.has(name)) {\n                    this._$changedProperties.set(name, oldValue);\n                }\n                // Add to reflecting properties set.\n                // Note, it's important that every change has a chance to add the\n                // property to `_reflectingProperties`. This ensures setting\n                // attribute + property reflects correctly.\n                if (options.reflect === true && this.__reflectingProperty !== name) {\n                    if (this.__reflectingProperties === undefined) {\n                        this.__reflectingProperties = new Map();\n                    }\n                    this.__reflectingProperties.set(name, options);\n                }\n            }\n            else {\n                // Abort the request if the property should not be considered changed.\n                shouldRequestUpdate = false;\n            }\n        }\n        if (!this.isUpdatePending && shouldRequestUpdate) {\n            this.__updatePromise = this.__enqueueUpdate();\n        }\n        // Note, since this no longer returns a promise, in dev mode we return a\n        // thenable which warns if it's called.\n        return DEV_MODE\n            ? requestUpdateThenable(this.localName)\n            : undefined;\n    }\n    /**\n     * Sets up the element to asynchronously update.\n     */\n    async __enqueueUpdate() {\n        this.isUpdatePending = true;\n        try {\n            // Ensure any previous update has resolved before updating.\n            // This `await` also ensures that property changes are batched.\n            await this.__updatePromise;\n        }\n        catch (e) {\n            // Refire any previous errors async so they do not disrupt the update\n            // cycle. Errors are refired so developers have a chance to observe\n            // them, and this can be done by implementing\n            // `window.onunhandledrejection`.\n            Promise.reject(e);\n        }\n        const result = this.scheduleUpdate();\n        // If `scheduleUpdate` returns a Promise, we await it. This is done to\n        // enable coordinating updates with a scheduler. Note, the result is\n        // checked to avoid delaying an additional microtask unless we need to.\n        if (result != null) {\n            await result;\n        }\n        return !this.isUpdatePending;\n    }\n    /**\n     * Schedules an element update. You can override this method to change the\n     * timing of updates by returning a Promise. The update will await the\n     * returned Promise, and you should resolve the Promise to allow the update\n     * to proceed. If this method is overridden, `super.scheduleUpdate()`\n     * must be called.\n     *\n     * For instance, to schedule updates to occur just before the next frame:\n     *\n     * ```ts\n     * override protected async scheduleUpdate(): Promise<unknown> {\n     *   await new Promise((resolve) => requestAnimationFrame(() => resolve()));\n     *   super.scheduleUpdate();\n     * }\n     * ```\n     * @category updates\n     */\n    scheduleUpdate() {\n        return this.performUpdate();\n    }\n    /**\n     * Performs an element update. Note, if an exception is thrown during the\n     * update, `firstUpdated` and `updated` will not be called.\n     *\n     * Call `performUpdate()` to immediately process a pending update. This should\n     * generally not be needed, but it can be done in rare cases when you need to\n     * update synchronously.\n     *\n     * Note: To ensure `performUpdate()` synchronously completes a pending update,\n     * it should not be overridden. In LitElement 2.x it was suggested to override\n     * `performUpdate()` to also customizing update scheduling. Instead, you should now\n     * override `scheduleUpdate()`. For backwards compatibility with LitElement 2.x,\n     * scheduling updates via `performUpdate()` continues to work, but will make\n     * also calling `performUpdate()` to synchronously process updates difficult.\n     *\n     * @category updates\n     */\n    performUpdate() {\n        var _a, _b;\n        // Abort any update if one is not pending when this is called.\n        // This can happen if `performUpdate` is called early to \"flush\"\n        // the update.\n        if (!this.isUpdatePending) {\n            return;\n        }\n        debugLogEvent === null || debugLogEvent === void 0 ? void 0 : debugLogEvent({ kind: 'update' });\n        // create renderRoot before first update.\n        if (!this.hasUpdated) {\n            // Produce warning if any class properties are shadowed by class fields\n            if (DEV_MODE) {\n                const shadowedProperties = [];\n                (_a = this.constructor.__reactivePropertyKeys) === null || _a === void 0 ? void 0 : _a.forEach((p) => {\n                    var _a;\n                    if (this.hasOwnProperty(p) && !((_a = this.__instanceProperties) === null || _a === void 0 ? void 0 : _a.has(p))) {\n                        shadowedProperties.push(p);\n                    }\n                });\n                if (shadowedProperties.length) {\n                    throw new Error(`The following properties on element ${this.localName} will not ` +\n                        `trigger updates as expected because they are set using class ` +\n                        `fields: ${shadowedProperties.join(', ')}. ` +\n                        `Native class fields and some compiled output will overwrite ` +\n                        `accessors used for detecting changes. See ` +\n                        `https://lit.dev/msg/class-field-shadowing ` +\n                        `for more information.`);\n                }\n            }\n        }\n        // Mixin instance properties once, if they exist.\n        if (this.__instanceProperties) {\n            // Use forEach so this works even if for/of loops are compiled to for loops\n            // expecting arrays\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this.__instanceProperties.forEach((v, p) => (this[p] = v));\n            this.__instanceProperties = undefined;\n        }\n        let shouldUpdate = false;\n        const changedProperties = this._$changedProperties;\n        try {\n            shouldUpdate = this.shouldUpdate(changedProperties);\n            if (shouldUpdate) {\n                this.willUpdate(changedProperties);\n                (_b = this.__controllers) === null || _b === void 0 ? void 0 : _b.forEach((c) => { var _a; return (_a = c.hostUpdate) === null || _a === void 0 ? void 0 : _a.call(c); });\n                this.update(changedProperties);\n            }\n            else {\n                this.__markUpdated();\n            }\n        }\n        catch (e) {\n            // Prevent `firstUpdated` and `updated` from running when there's an\n            // update exception.\n            shouldUpdate = false;\n            // Ensure element can accept additional updates after an exception.\n            this.__markUpdated();\n            throw e;\n        }\n        // The update is no longer considered pending and further updates are now allowed.\n        if (shouldUpdate) {\n            this._$didUpdate(changedProperties);\n        }\n    }\n    /**\n     * Invoked before `update()` to compute values needed during the update.\n     *\n     * Implement `willUpdate` to compute property values that depend on other\n     * properties and are used in the rest of the update process.\n     *\n     * ```ts\n     * willUpdate(changedProperties) {\n     *   // only need to check changed properties for an expensive computation.\n     *   if (changedProperties.has('firstName') || changedProperties.has('lastName')) {\n     *     this.sha = computeSHA(`${this.firstName} ${this.lastName}`);\n     *   }\n     * }\n     *\n     * render() {\n     *   return html`SHA: ${this.sha}`;\n     * }\n     * ```\n     *\n     * @category updates\n     */\n    willUpdate(_changedProperties) { }\n    // Note, this is an override point for polyfill-support.\n    // @internal\n    _$didUpdate(changedProperties) {\n        var _a;\n        (_a = this.__controllers) === null || _a === void 0 ? void 0 : _a.forEach((c) => { var _a; return (_a = c.hostUpdated) === null || _a === void 0 ? void 0 : _a.call(c); });\n        if (!this.hasUpdated) {\n            this.hasUpdated = true;\n            this.firstUpdated(changedProperties);\n        }\n        this.updated(changedProperties);\n        if (DEV_MODE &&\n            this.isUpdatePending &&\n            this.constructor.enabledWarnings.indexOf('change-in-update') >= 0) {\n            issueWarning('change-in-update', `Element ${this.localName} scheduled an update ` +\n                `(generally because a property was set) ` +\n                `after an update completed, causing a new update to be scheduled. ` +\n                `This is inefficient and should be avoided unless the next update ` +\n                `can only be scheduled as a side effect of the previous update.`);\n        }\n    }\n    __markUpdated() {\n        this._$changedProperties = new Map();\n        this.isUpdatePending = false;\n    }\n    /**\n     * Returns a Promise that resolves when the element has completed updating.\n     * The Promise value is a boolean that is `true` if the element completed the\n     * update without triggering another update. The Promise result is `false` if\n     * a property was set inside `updated()`. If the Promise is rejected, an\n     * exception was thrown during the update.\n     *\n     * To await additional asynchronous work, override the `getUpdateComplete`\n     * method. For example, it is sometimes useful to await a rendered element\n     * before fulfilling this Promise. To do this, first await\n     * `super.getUpdateComplete()`, then any subsequent state.\n     *\n     * @return A promise of a boolean that resolves to true if the update completed\n     *     without triggering another update.\n     * @category updates\n     */\n    get updateComplete() {\n        return this.getUpdateComplete();\n    }\n    /**\n     * Override point for the `updateComplete` promise.\n     *\n     * It is not safe to override the `updateComplete` getter directly due to a\n     * limitation in TypeScript which means it is not possible to call a\n     * superclass getter (e.g. `super.updateComplete.then(...)`) when the target\n     * language is ES5 (https://github.com/microsoft/TypeScript/issues/338).\n     * This method should be overridden instead. For example:\n     *\n     * ```ts\n     * class MyElement extends LitElement {\n     *   override async getUpdateComplete() {\n     *     const result = await super.getUpdateComplete();\n     *     await this._myChild.updateComplete;\n     *     return result;\n     *   }\n     * }\n     * ```\n     *\n     * @return A promise of a boolean that resolves to true if the update completed\n     *     without triggering another update.\n     * @category updates\n     */\n    getUpdateComplete() {\n        return this.__updatePromise;\n    }\n    /**\n     * Controls whether or not `update()` should be called when the element requests\n     * an update. By default, this method always returns `true`, but this can be\n     * customized to control when to update.\n     *\n     * @param _changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    shouldUpdate(_changedProperties) {\n        return true;\n    }\n    /**\n     * Updates the element. This method reflects property values to attributes.\n     * It can be overridden to render and keep updated element DOM.\n     * Setting properties inside this method will *not* trigger\n     * another update.\n     *\n     * @param _changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    update(_changedProperties) {\n        if (this.__reflectingProperties !== undefined) {\n            // Use forEach so this works even if for/of loops are compiled to for\n            // loops expecting arrays\n            this.__reflectingProperties.forEach((v, k) => this.__propertyToAttribute(k, this[k], v));\n            this.__reflectingProperties = undefined;\n        }\n        this.__markUpdated();\n    }\n    /**\n     * Invoked whenever the element is updated. Implement to perform\n     * post-updating tasks via DOM APIs, for example, focusing an element.\n     *\n     * Setting properties inside this method will trigger the element to update\n     * again after this update cycle completes.\n     *\n     * @param _changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    updated(_changedProperties) { }\n    /**\n     * Invoked when the element is first updated. Implement to perform one time\n     * work on the element after update.\n     *\n     * ```ts\n     * firstUpdated() {\n     *   this.renderRoot.getElementById('my-text-area').focus();\n     * }\n     * ```\n     *\n     * Setting properties inside this method will trigger the element to update\n     * again after this update cycle completes.\n     *\n     * @param _changedProperties Map of changed properties with old values\n     * @category updates\n     */\n    firstUpdated(_changedProperties) { }\n}\n_e = finalized;\n/**\n * Marks class as having finished creating properties.\n */\nReactiveElement[_e] = true;\n/**\n * Memoized list of all element properties, including any superclass properties.\n * Created lazily on user subclasses when finalizing the class.\n * @nocollapse\n * @category properties\n */\nReactiveElement.elementProperties = new Map();\n/**\n * Memoized list of all element styles.\n * Created lazily on user subclasses when finalizing the class.\n * @nocollapse\n * @category styles\n */\nReactiveElement.elementStyles = [];\n/**\n * Options used when calling `attachShadow`. Set this property to customize\n * the options for the shadowRoot; for example, to create a closed\n * shadowRoot: `{mode: 'closed'}`.\n *\n * Note, these options are used in `createRenderRoot`. If this method\n * is customized, options should be respected if possible.\n * @nocollapse\n * @category rendering\n */\nReactiveElement.shadowRootOptions = { mode: 'open' };\n// Apply polyfills if available\npolyfillSupport === null || polyfillSupport === void 0 ? void 0 : polyfillSupport({ ReactiveElement });\n// Dev mode warnings...\nif (DEV_MODE) {\n    // Default warning set.\n    ReactiveElement.enabledWarnings = ['change-in-update'];\n    const ensureOwnWarnings = function (ctor) {\n        if (!ctor.hasOwnProperty(JSCompiler_renameProperty('enabledWarnings', ctor))) {\n            ctor.enabledWarnings = ctor.enabledWarnings.slice();\n        }\n    };\n    ReactiveElement.enableWarning = function (warning) {\n        ensureOwnWarnings(this);\n        if (this.enabledWarnings.indexOf(warning) < 0) {\n            this.enabledWarnings.push(warning);\n        }\n    };\n    ReactiveElement.disableWarning = function (warning) {\n        ensureOwnWarnings(this);\n        const i = this.enabledWarnings.indexOf(warning);\n        if (i >= 0) {\n            this.enabledWarnings.splice(i, 1);\n        }\n    };\n}\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for ReactiveElement usage.\n((_d = global.reactiveElementVersions) !== null && _d !== void 0 ? _d : (global.reactiveElementVersions = [])).push('1.6.3');\nif (DEV_MODE && global.reactiveElementVersions.length > 1) {\n    issueWarning('multiple-versions', `Multiple versions of Lit loaded. Loading multiple versions ` +\n        `is not recommended.`);\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA,IAAIA,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;AAClB,IAAIC,EAAE;AACN;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkB,EAAEC,WAAW,QAAS,cAAc;AAC/D;AACA;AACA,cAAc,cAAc;AAC5B,MAAMC,SAAS,GAAG,KAAK;AACvB,MAAMC,MAAM,GAAGD,SAAS,GAAGE,UAAU,GAAGC,MAAM;AAC9C,IAAIH,SAAS,EAAE;EACX,CAACP,EAAE,GAAGQ,MAAM,CAACG,cAAc,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAIQ,MAAM,CAACG,cAAc,GAAGA,cAAe;AAC1G;AACA,MAAMC,QAAQ,GAAG,IAAI;AACrB,IAAIC,qBAAqB;AACzB,IAAIC,YAAY;AAChB,MAAMC,YAAY,GAAGP,MAAM,CACtBO,YAAY;AACjB;AACA;AACA;AACA;AACA,MAAMC,8BAA8B,GAAGD,YAAY,GAC7CA,YAAY,CAACE,WAAW,GACxB,EAAE;AACR,MAAMC,eAAe,GAAGN,QAAQ,GAC1BJ,MAAM,CAACW,qCAAqC,GAC5CX,MAAM,CAACY,8BAA8B;AAC3C,IAAIR,QAAQ,EAAE;EACV;EACA;EACA,MAAMS,cAAc,GAAI,CAACpB,EAAE,GAAGO,MAAM,CAACc,iBAAiB,MAAM,IAAI,IAAIrB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAIO,MAAM,CAACc,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAG;EAChI;EACAT,YAAY,GAAGA,CAACU,IAAI,EAAEC,OAAO,KAAK;IAC9BA,OAAO,IAAK,4BAA2BD,IAAK,wBAAuB;IACnE,IAAI,CAACH,cAAc,CAACK,GAAG,CAACD,OAAO,CAAC,EAAE;MAC9BE,OAAO,CAACC,IAAI,CAACH,OAAO,CAAC;MACrBJ,cAAc,CAACQ,GAAG,CAACJ,OAAO,CAAC;IAC/B;EACJ,CAAC;EACDX,YAAY,CAAC,UAAU,EAAG,qDAAoD,CAAC;EAC/E;EACA,IAAI,CAAC,CAACZ,EAAE,GAAGM,MAAM,CAACsB,QAAQ,MAAM,IAAI,IAAI5B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6B,KAAK,KAAKb,eAAe,KAAKc,SAAS,EAAE;IACzGlB,YAAY,CAAC,0BAA0B,EAAG,sDAAqD,GAC1F,sDAAqD,CAAC;EAC/D;EACAD,qBAAqB,GAAIoB,IAAI,KAAM;IAC/BC,IAAI,EAAEA,CAACC,WAAW,EAAEC,WAAW,KAAK;MAChCtB,YAAY,CAAC,wBAAwB,EAAG,qEAAoE,GACvG,gBAAemB,IAAK,qCAAoC,CAAC;MAC9D,IAAIE,WAAW,KAAKH,SAAS,EAAE;QAC3BG,WAAW,CAAC,KAAK,CAAC;MACtB;IACJ;EACJ,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,aAAa,GAAGzB,QAAQ,GACvB0B,KAAK,IAAK;EACT,MAAMC,UAAU,GAAG/B,MAAM,CACpBgC,qBAAqB;EAC1B,IAAI,CAACD,UAAU,EAAE;IACb;EACJ;EACA/B,MAAM,CAACiC,aAAa,CAAC,IAAIC,WAAW,CAAC,WAAW,EAAE;IAC9CC,MAAM,EAAEL;EACZ,CAAC,CAAC,CAAC;AACP,CAAC,GACCN,SAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,yBAAyB,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAKD,IAAI;AACtD,OAAO,MAAME,gBAAgB,GAAG;EAC5BC,WAAWA,CAACC,KAAK,EAAEC,IAAI,EAAE;IACrB,QAAQA,IAAI;MACR,KAAKC,OAAO;QACRF,KAAK,GAAGA,KAAK,GAAGjC,8BAA8B,GAAG,IAAI;QACrD;MACJ,KAAKoC,MAAM;MACX,KAAKC,KAAK;QACN;QACA;QACAJ,KAAK,GAAGA,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGK,IAAI,CAACC,SAAS,CAACN,KAAK,CAAC;QACrD;IACR;IACA,OAAOA,KAAK;EAChB,CAAC;EACDO,aAAaA,CAACP,KAAK,EAAEC,IAAI,EAAE;IACvB,IAAIO,SAAS,GAAGR,KAAK;IACrB,QAAQC,IAAI;MACR,KAAKC,OAAO;QACRM,SAAS,GAAGR,KAAK,KAAK,IAAI;QAC1B;MACJ,KAAKS,MAAM;QACPD,SAAS,GAAGR,KAAK,KAAK,IAAI,GAAG,IAAI,GAAGS,MAAM,CAACT,KAAK,CAAC;QACjD;MACJ,KAAKG,MAAM;MACX,KAAKC,KAAK;QACN;QACA;QACA;QACA,IAAI;UACA;UACAI,SAAS,GAAGH,IAAI,CAACK,KAAK,CAACV,KAAK,CAAC;QACjC,CAAC,CACD,OAAOW,CAAC,EAAE;UACNH,SAAS,GAAG,IAAI;QACpB;QACA;IACR;IACA,OAAOA,SAAS;EACpB;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,OAAO,MAAMI,QAAQ,GAAGA,CAACZ,KAAK,EAAEa,GAAG,KAAK;EACpC;EACA,OAAOA,GAAG,KAAKb,KAAK,KAAKa,GAAG,KAAKA,GAAG,IAAIb,KAAK,KAAKA,KAAK,CAAC;AAC5D,CAAC;AACD,MAAMc,0BAA0B,GAAG;EAC/BC,SAAS,EAAE,IAAI;EACfd,IAAI,EAAEe,MAAM;EACZC,SAAS,EAAEnB,gBAAgB;EAC3BoB,OAAO,EAAE,KAAK;EACdC,UAAU,EAAEP;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,SAAS,GAAG,WAAW;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,QACSC,WAAW,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACrC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;AACR;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACC,YAAY,CAAC,CAAC;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,cAAcA,CAACC,WAAW,EAAE;IAC/B,IAAIhF,EAAE;IACN,IAAI,CAACiF,QAAQ,CAAC,CAAC;IACf,CAAC,CAACjF,EAAE,GAAG,IAAI,CAACkF,aAAa,MAAM,IAAI,IAAIlF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAI,IAAI,CAACkF,aAAa,GAAG,EAAG,EAAEC,IAAI,CAACH,WAAW,CAAC;EAC5G;EACA;AACJ;AACA;AACA;AACA;EACI,WAAWI,kBAAkBA,CAAA,EAAG;IAC5B;IACA,IAAI,CAACH,QAAQ,CAAC,CAAC;IACf,MAAMI,UAAU,GAAG,EAAE;IACrB;IACA;IACA,IAAI,CAACC,iBAAiB,CAACC,OAAO,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACrC,MAAMC,IAAI,GAAG,IAAI,CAACC,0BAA0B,CAACF,CAAC,EAAED,CAAC,CAAC;MAClD,IAAIE,IAAI,KAAK1D,SAAS,EAAE;QACpB,IAAI,CAAC4D,wBAAwB,CAACC,GAAG,CAACH,IAAI,EAAED,CAAC,CAAC;QAC1CJ,UAAU,CAACF,IAAI,CAACO,IAAI,CAAC;MACzB;IACJ,CAAC,CAAC;IACF,OAAOL,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOS,cAAcA,CAAC7D,IAAI,EAAE8D,OAAO,GAAGhC,0BAA0B,EAAE;IAC9D,IAAI/D,EAAE;IACN;IACA,IAAI+F,OAAO,CAACC,KAAK,EAAE;MACf;MACA;MACAD,OAAO,CAAC/B,SAAS,GAAG,KAAK;IAC7B;IACA;IACA;IACA,IAAI,CAACiB,QAAQ,CAAC,CAAC;IACf,IAAI,CAACK,iBAAiB,CAACO,GAAG,CAAC5D,IAAI,EAAE8D,OAAO,CAAC;IACzC;IACA;IACA;IACA;IACA;IACA,IAAI,CAACA,OAAO,CAACE,UAAU,IAAI,CAAC,IAAI,CAACC,SAAS,CAACC,cAAc,CAAClE,IAAI,CAAC,EAAE;MAC7D,MAAMmE,GAAG,GAAG,OAAOnE,IAAI,KAAK,QAAQ,GAAGoE,MAAM,CAAC,CAAC,GAAI,KAAIpE,IAAK,EAAC;MAC7D,MAAMqE,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAACtE,IAAI,EAAEmE,GAAG,EAAEL,OAAO,CAAC;MACjE,IAAIO,UAAU,KAAKtE,SAAS,EAAE;QAC1BoB,MAAM,CAACoD,cAAc,CAAC,IAAI,CAACN,SAAS,EAAEjE,IAAI,EAAEqE,UAAU,CAAC;QACvD,IAAI1F,QAAQ,EAAE;UACV;UACA;UACA,IAAI,CAAC,IAAI,CAACuF,cAAc,CAAC,wBAAwB,CAAC,EAAE;YAChD,IAAI,CAACM,sBAAsB,GAAG,IAAIlF,GAAG,CAAC,CAACvB,EAAE,GAAG,IAAI,CAACyG,sBAAsB,MAAM,IAAI,IAAIzG,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,CAAC;UACjH;UACA,IAAI,CAACyG,sBAAsB,CAAC5E,GAAG,CAACI,IAAI,CAAC;QACzC;MACJ;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOsE,qBAAqBA,CAACtE,IAAI,EAAEmE,GAAG,EAAEL,OAAO,EAAE;IAC7C,OAAO;MACH;MACAW,GAAGA,CAAA,EAAG;QACF,OAAO,IAAI,CAACN,GAAG,CAAC;MACpB,CAAC;MACDP,GAAGA,CAAC5C,KAAK,EAAE;QACP,MAAM0D,QAAQ,GAAG,IAAI,CAAC1E,IAAI,CAAC;QAC3B,IAAI,CAACmE,GAAG,CAAC,GAAGnD,KAAK;QACjB,IAAI,CAAC2D,aAAa,CAAC3E,IAAI,EAAE0E,QAAQ,EAAEZ,OAAO,CAAC;MAC/C,CAAC;MACDc,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE;IAChB,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,kBAAkBA,CAAC9E,IAAI,EAAE;IAC5B,OAAO,IAAI,CAACqD,iBAAiB,CAACoB,GAAG,CAACzE,IAAI,CAAC,IAAI8B,0BAA0B;EACzE;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,OAAOkB,QAAQA,CAAA,EAAG;IACd,IAAI,IAAI,CAACkB,cAAc,CAAC9B,SAAS,CAAC,EAAE;MAChC,OAAO,KAAK;IAChB;IACA,IAAI,CAACA,SAAS,CAAC,GAAG,IAAI;IACtB;IACA,MAAM2C,SAAS,GAAG5D,MAAM,CAAC6D,cAAc,CAAC,IAAI,CAAC;IAC7CD,SAAS,CAAC/B,QAAQ,CAAC,CAAC;IACpB;IACA;IACA;IACA,IAAI+B,SAAS,CAAC9B,aAAa,KAAKlD,SAAS,EAAE;MACvC,IAAI,CAACkD,aAAa,GAAG,CAAC,GAAG8B,SAAS,CAAC9B,aAAa,CAAC;IACrD;IACA,IAAI,CAACI,iBAAiB,GAAG,IAAIZ,GAAG,CAACsC,SAAS,CAAC1B,iBAAiB,CAAC;IAC7D;IACA,IAAI,CAACM,wBAAwB,GAAG,IAAIlB,GAAG,CAAC,CAAC;IACzC;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACyB,cAAc,CAACvD,yBAAyB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,EAAE;MACpE,MAAMsE,KAAK,GAAG,IAAI,CAACC,UAAU;MAC7B;MACA,MAAMC,QAAQ,GAAG,CACb,GAAGhE,MAAM,CAACiE,mBAAmB,CAACH,KAAK,CAAC,EACpC,GAAG9D,MAAM,CAACkE,qBAAqB,CAACJ,KAAK,CAAC,CACzC;MACD;MACA,KAAK,MAAMzB,CAAC,IAAI2B,QAAQ,EAAE;QACtB;QACA;QACA;QACA,IAAI,CAACtB,cAAc,CAACL,CAAC,EAAEyB,KAAK,CAACzB,CAAC,CAAC,CAAC;MACpC;IACJ;IACA,IAAI,CAAC8B,aAAa,GAAG,IAAI,CAACC,cAAc,CAAC,IAAI,CAACC,MAAM,CAAC;IACrD;IACA,IAAI7G,QAAQ,EAAE;MACV,MAAM8G,oBAAoB,GAAGA,CAACzF,IAAI,EAAE0F,OAAO,GAAG,KAAK,KAAK;QACpD,IAAI,IAAI,CAACzB,SAAS,CAACC,cAAc,CAAClE,IAAI,CAAC,EAAE;UACrCnB,YAAY,CAAC6G,OAAO,GAAG,aAAa,GAAG,aAAa,EAAG,KAAI1F,IAAK,8BAA6B,IAAI,CAACA,IAAK,OAAM,GACxG,YAAW0F,OAAO,GAAG,SAAS,GAAG,SAAU,GAAE,GAC7C,gCAA+B,CAAC;QACzC;MACJ,CAAC;MACDD,oBAAoB,CAAC,YAAY,CAAC;MAClCA,oBAAoB,CAAC,uBAAuB,CAAC;MAC7CA,oBAAoB,CAAC,oBAAoB,EAAE,IAAI,CAAC;IACpD;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOF,cAAcA,CAACC,MAAM,EAAE;IAC1B,MAAMF,aAAa,GAAG,EAAE;IACxB,IAAIlE,KAAK,CAACuE,OAAO,CAACH,MAAM,CAAC,EAAE;MACvB;MACA;MACA;MACA,MAAM5B,GAAG,GAAG,IAAItE,GAAG,CAACkG,MAAM,CAACI,IAAI,CAACC,QAAQ,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;MACpD;MACA,KAAK,MAAMC,CAAC,IAAInC,GAAG,EAAE;QACjB0B,aAAa,CAACU,OAAO,CAAC5H,kBAAkB,CAAC2H,CAAC,CAAC,CAAC;MAChD;IACJ,CAAC,MACI,IAAIP,MAAM,KAAKzF,SAAS,EAAE;MAC3BuF,aAAa,CAACpC,IAAI,CAAC9E,kBAAkB,CAACoH,MAAM,CAAC,CAAC;IAClD;IACA,OAAOF,aAAa;EACxB;EACA;AACJ;AACA;AACA;EACI,OAAO5B,0BAA0BA,CAAC1D,IAAI,EAAE8D,OAAO,EAAE;IAC7C,MAAM/B,SAAS,GAAG+B,OAAO,CAAC/B,SAAS;IACnC,OAAOA,SAAS,KAAK,KAAK,GACpBhC,SAAS,GACT,OAAOgC,SAAS,KAAK,QAAQ,GACzBA,SAAS,GACT,OAAO/B,IAAI,KAAK,QAAQ,GACpBA,IAAI,CAACiG,WAAW,CAAC,CAAC,GAClBlG,SAAS;EAC3B;EACA;AACJ;AACA;AACA;EACI8C,YAAYA,CAAA,EAAG;IACX,IAAI9E,EAAE;IACN,IAAI,CAACmI,eAAe,GAAG,IAAIC,OAAO,CAAEC,GAAG,IAAM,IAAI,CAACC,cAAc,GAAGD,GAAI,CAAC;IACxE,IAAI,CAACE,mBAAmB,GAAG,IAAI7D,GAAG,CAAC,CAAC;IACpC,IAAI,CAAC8D,wBAAwB,CAAC,CAAC;IAC/B;IACA;IACA,IAAI,CAAC5B,aAAa,CAAC,CAAC;IACpB,CAAC5G,EAAE,GAAG,IAAI,CAACwE,WAAW,CAACU,aAAa,MAAM,IAAI,IAAIlF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuF,OAAO,CAAEkD,CAAC,IAAKA,CAAC,CAAC,IAAI,CAAC,CAAC;EACzG;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,aAAaA,CAACC,UAAU,EAAE;IACtB,IAAI3I,EAAE,EAAEC,EAAE;IACV,CAAC,CAACD,EAAE,GAAG,IAAI,CAAC4I,aAAa,MAAM,IAAI,IAAI5I,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAI,IAAI,CAAC4I,aAAa,GAAG,EAAG,EAAEzD,IAAI,CAACwD,UAAU,CAAC;IACvG;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACE,UAAU,KAAK7G,SAAS,IAAI,IAAI,CAAC8G,WAAW,EAAE;MACnD,CAAC7I,EAAE,GAAG0I,UAAU,CAACI,aAAa,MAAM,IAAI,IAAI9I,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+I,IAAI,CAACL,UAAU,CAAC;IAC5F;EACJ;EACA;AACJ;AACA;AACA;EACIM,gBAAgBA,CAACN,UAAU,EAAE;IACzB,IAAI3I,EAAE;IACN;IACA;IACA,CAACA,EAAE,GAAG,IAAI,CAAC4I,aAAa,MAAM,IAAI,IAAI5I,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkJ,MAAM,CAAC,IAAI,CAACN,aAAa,CAACO,OAAO,CAACR,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;EAC7H;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIH,wBAAwBA,CAAA,EAAG;IACvB;IACA;IACA,IAAI,CAAChE,WAAW,CAACc,iBAAiB,CAACC,OAAO,CAAC,CAAC6D,EAAE,EAAE3D,CAAC,KAAK;MAClD,IAAI,IAAI,CAACU,cAAc,CAACV,CAAC,CAAC,EAAE;QACxB,IAAI,CAAChB,oBAAoB,CAACoB,GAAG,CAACJ,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC,CAAC;QACzC,OAAO,IAAI,CAACA,CAAC,CAAC;MAClB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI4D,gBAAgBA,CAAA,EAAG;IACf,IAAIrJ,EAAE;IACN,MAAM6I,UAAU,GAAG,CAAC7I,EAAE,GAAG,IAAI,CAACsJ,UAAU,MAAM,IAAI,IAAItJ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAACuJ,YAAY,CAAC,IAAI,CAAC/E,WAAW,CAACgF,iBAAiB,CAAC;IAChIlJ,WAAW,CAACuI,UAAU,EAAE,IAAI,CAACrE,WAAW,CAAC+C,aAAa,CAAC;IACvD,OAAOsB,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;EACIY,iBAAiBA,CAAA,EAAG;IAChB,IAAIzJ,EAAE;IACN;IACA,IAAI,IAAI,CAAC6I,UAAU,KAAK7G,SAAS,EAAE;MAC/B,IAAI,CAAC6G,UAAU,GAAG,IAAI,CAACQ,gBAAgB,CAAC,CAAC;IAC7C;IACA,IAAI,CAACf,cAAc,CAAC,IAAI,CAAC;IACzB,CAACtI,EAAE,GAAG,IAAI,CAAC4I,aAAa,MAAM,IAAI,IAAI5I,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuF,OAAO,CAAEmE,CAAC,IAAK;MAAE,IAAI1J,EAAE;MAAE,OAAO,CAACA,EAAE,GAAG0J,CAAC,CAACX,aAAa,MAAM,IAAI,IAAI/I,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgJ,IAAI,CAACU,CAAC,CAAC;IAAE,CAAC,CAAC;EAChL;EACA;AACJ;AACA;AACA;AACA;AACA;EACIpB,cAAcA,CAACqB,gBAAgB,EAAE,CAAE;EACnC;AACJ;AACA;AACA;AACA;AACA;EACIC,oBAAoBA,CAAA,EAAG;IACnB,IAAI5J,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAAC4I,aAAa,MAAM,IAAI,IAAI5I,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuF,OAAO,CAAEmE,CAAC,IAAK;MAAE,IAAI1J,EAAE;MAAE,OAAO,CAACA,EAAE,GAAG0J,CAAC,CAACG,gBAAgB,MAAM,IAAI,IAAI7J,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgJ,IAAI,CAACU,CAAC,CAAC;IAAE,CAAC,CAAC;EACnL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACII,wBAAwBA,CAAC7H,IAAI,EAAE8H,IAAI,EAAE9G,KAAK,EAAE;IACxC,IAAI,CAAC+G,qBAAqB,CAAC/H,IAAI,EAAEgB,KAAK,CAAC;EAC3C;EACAgH,qBAAqBA,CAAChI,IAAI,EAAEgB,KAAK,EAAE8C,OAAO,GAAGhC,0BAA0B,EAAE;IACrE,IAAI/D,EAAE;IACN,MAAM0F,IAAI,GAAG,IAAI,CAAClB,WAAW,CAACmB,0BAA0B,CAAC1D,IAAI,EAAE8D,OAAO,CAAC;IACvE,IAAIL,IAAI,KAAK1D,SAAS,IAAI+D,OAAO,CAAC5B,OAAO,KAAK,IAAI,EAAE;MAChD,MAAMD,SAAS,GAAG,CAAC,CAAClE,EAAE,GAAG+F,OAAO,CAAC7B,SAAS,MAAM,IAAI,IAAIlE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgD,WAAW,MAC3FhB,SAAS,GACP+D,OAAO,CAAC7B,SAAS,GACjBnB,gBAAgB;MACtB,MAAMmH,SAAS,GAAGhG,SAAS,CAAClB,WAAW,CAACC,KAAK,EAAE8C,OAAO,CAAC7C,IAAI,CAAC;MAC5D,IAAItC,QAAQ,IACR,IAAI,CAAC4D,WAAW,CAAC2F,eAAe,CAAChB,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAC1De,SAAS,KAAKlI,SAAS,EAAE;QACzBlB,YAAY,CAAC,2BAA2B,EAAG,+BAA8BmB,IAAK,eAAc,GACvF,wBAAuB,IAAI,CAACmI,SAAU,0BAAyB,GAC/D,+DAA8D,GAC9D,uCAAsC,CAAC;MAChD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACvF,oBAAoB,GAAG5C,IAAI;MAChC,IAAIiI,SAAS,IAAI,IAAI,EAAE;QACnB,IAAI,CAACG,eAAe,CAAC3E,IAAI,CAAC;MAC9B,CAAC,MACI;QACD,IAAI,CAAC4E,YAAY,CAAC5E,IAAI,EAAEwE,SAAS,CAAC;MACtC;MACA;MACA,IAAI,CAACrF,oBAAoB,GAAG,IAAI;IACpC;EACJ;EACA;EACAmF,qBAAqBA,CAAC/H,IAAI,EAAEgB,KAAK,EAAE;IAC/B,IAAIjD,EAAE;IACN,MAAMuK,IAAI,GAAG,IAAI,CAAC/F,WAAW;IAC7B;IACA;IACA,MAAMgG,QAAQ,GAAGD,IAAI,CAAC3E,wBAAwB,CAACc,GAAG,CAACzE,IAAI,CAAC;IACxD;IACA;IACA,IAAIuI,QAAQ,KAAKxI,SAAS,IAAI,IAAI,CAAC6C,oBAAoB,KAAK2F,QAAQ,EAAE;MAClE,MAAMzE,OAAO,GAAGwE,IAAI,CAACxD,kBAAkB,CAACyD,QAAQ,CAAC;MACjD,MAAMtG,SAAS,GAAG,OAAO6B,OAAO,CAAC7B,SAAS,KAAK,UAAU,GACnD;QAAEV,aAAa,EAAEuC,OAAO,CAAC7B;MAAU,CAAC,GACpC,CAAC,CAAClE,EAAE,GAAG+F,OAAO,CAAC7B,SAAS,MAAM,IAAI,IAAIlE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwD,aAAa,MAAMxB,SAAS,GAC1F+D,OAAO,CAAC7B,SAAS,GACjBnB,gBAAgB;MAC1B;MACA,IAAI,CAAC8B,oBAAoB,GAAG2F,QAAQ;MACpC,IAAI,CAACA,QAAQ,CAAC,GAAGtG,SAAS,CAACV,aAAa,CAACP,KAAK,EAAE8C,OAAO,CAAC7C;MACxD;MACA,CAAC;MACD;MACA,IAAI,CAAC2B,oBAAoB,GAAG,IAAI;IACpC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI+B,aAAaA,CAAC3E,IAAI,EAAE0E,QAAQ,EAAEZ,OAAO,EAAE;IACnC,IAAI0E,mBAAmB,GAAG,IAAI;IAC9B;IACA,IAAIxI,IAAI,KAAKD,SAAS,EAAE;MACpB+D,OAAO,GACHA,OAAO,IACH,IAAI,CAACvB,WAAW,CAACuC,kBAAkB,CAAC9E,IAAI,CAAC;MACjD,MAAMmC,UAAU,GAAG2B,OAAO,CAAC3B,UAAU,IAAIP,QAAQ;MACjD,IAAIO,UAAU,CAAC,IAAI,CAACnC,IAAI,CAAC,EAAE0E,QAAQ,CAAC,EAAE;QAClC,IAAI,CAAC,IAAI,CAAC4B,mBAAmB,CAAC7G,GAAG,CAACO,IAAI,CAAC,EAAE;UACrC,IAAI,CAACsG,mBAAmB,CAAC1C,GAAG,CAAC5D,IAAI,EAAE0E,QAAQ,CAAC;QAChD;QACA;QACA;QACA;QACA;QACA,IAAIZ,OAAO,CAAC5B,OAAO,KAAK,IAAI,IAAI,IAAI,CAACU,oBAAoB,KAAK5C,IAAI,EAAE;UAChE,IAAI,IAAI,CAACyI,sBAAsB,KAAK1I,SAAS,EAAE;YAC3C,IAAI,CAAC0I,sBAAsB,GAAG,IAAIhG,GAAG,CAAC,CAAC;UAC3C;UACA,IAAI,CAACgG,sBAAsB,CAAC7E,GAAG,CAAC5D,IAAI,EAAE8D,OAAO,CAAC;QAClD;MACJ,CAAC,MACI;QACD;QACA0E,mBAAmB,GAAG,KAAK;MAC/B;IACJ;IACA,IAAI,CAAC,IAAI,CAAC9F,eAAe,IAAI8F,mBAAmB,EAAE;MAC9C,IAAI,CAACtC,eAAe,GAAG,IAAI,CAACwC,eAAe,CAAC,CAAC;IACjD;IACA;IACA;IACA,OAAO/J,QAAQ,GACTC,qBAAqB,CAAC,IAAI,CAACuJ,SAAS,CAAC,GACrCpI,SAAS;EACnB;EACA;AACJ;AACA;EACU2I,eAAeA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACpBD,KAAI,CAACjG,eAAe,GAAG,IAAI;MAC3B,IAAI;QACA;QACA;QACA,MAAMiG,KAAI,CAACzC,eAAe;MAC9B,CAAC,CACD,OAAOvE,CAAC,EAAE;QACN;QACA;QACA;QACA;QACAwE,OAAO,CAAC0C,MAAM,CAAClH,CAAC,CAAC;MACrB;MACA,MAAMmH,MAAM,GAAGH,KAAI,CAACI,cAAc,CAAC,CAAC;MACpC;MACA;MACA;MACA,IAAID,MAAM,IAAI,IAAI,EAAE;QAChB,MAAMA,MAAM;MAChB;MACA,OAAO,CAACH,KAAI,CAACjG,eAAe;IAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIqG,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,aAAa,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,aAAaA,CAAA,EAAG;IACZ,IAAIjL,EAAE,EAAEC,EAAE;IACV;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC0E,eAAe,EAAE;MACvB;IACJ;IACAtC,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;MAAE6I,IAAI,EAAE;IAAS,CAAC,CAAC;IAC/F;IACA,IAAI,CAAC,IAAI,CAACtG,UAAU,EAAE;MAClB;MACA,IAAIhE,QAAQ,EAAE;QACV,MAAMuK,kBAAkB,GAAG,EAAE;QAC7B,CAACnL,EAAE,GAAG,IAAI,CAACwE,WAAW,CAACiC,sBAAsB,MAAM,IAAI,IAAIzG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuF,OAAO,CAAEE,CAAC,IAAK;UAClG,IAAIzF,EAAE;UACN,IAAI,IAAI,CAACmG,cAAc,CAACV,CAAC,CAAC,IAAI,EAAE,CAACzF,EAAE,GAAG,IAAI,CAACyE,oBAAoB,MAAM,IAAI,IAAIzE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0B,GAAG,CAAC+D,CAAC,CAAC,CAAC,EAAE;YAC9G0F,kBAAkB,CAAChG,IAAI,CAACM,CAAC,CAAC;UAC9B;QACJ,CAAC,CAAC;QACF,IAAI0F,kBAAkB,CAACC,MAAM,EAAE;UAC3B,MAAM,IAAIC,KAAK,CAAE,uCAAsC,IAAI,CAACjB,SAAU,YAAW,GAC5E,+DAA8D,GAC9D,WAAUe,kBAAkB,CAACG,IAAI,CAAC,IAAI,CAAE,IAAG,GAC3C,8DAA6D,GAC7D,4CAA2C,GAC3C,4CAA2C,GAC3C,uBAAsB,CAAC;QAChC;MACJ;IACJ;IACA;IACA,IAAI,IAAI,CAAC7G,oBAAoB,EAAE;MAC3B;MACA;MACA;MACA,IAAI,CAACA,oBAAoB,CAACc,OAAO,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAM,IAAI,CAACA,CAAC,CAAC,GAAGD,CAAE,CAAC;MAC1D,IAAI,CAACf,oBAAoB,GAAGzC,SAAS;IACzC;IACA,IAAIuJ,YAAY,GAAG,KAAK;IACxB,MAAMC,iBAAiB,GAAG,IAAI,CAACjD,mBAAmB;IAClD,IAAI;MACAgD,YAAY,GAAG,IAAI,CAACA,YAAY,CAACC,iBAAiB,CAAC;MACnD,IAAID,YAAY,EAAE;QACd,IAAI,CAACE,UAAU,CAACD,iBAAiB,CAAC;QAClC,CAACvL,EAAE,GAAG,IAAI,CAAC2I,aAAa,MAAM,IAAI,IAAI3I,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsF,OAAO,CAAEmE,CAAC,IAAK;UAAE,IAAI1J,EAAE;UAAE,OAAO,CAACA,EAAE,GAAG0J,CAAC,CAACgC,UAAU,MAAM,IAAI,IAAI1L,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgJ,IAAI,CAACU,CAAC,CAAC;QAAE,CAAC,CAAC;QACzK,IAAI,CAACiC,MAAM,CAACH,iBAAiB,CAAC;MAClC,CAAC,MACI;QACD,IAAI,CAACI,aAAa,CAAC,CAAC;MACxB;IACJ,CAAC,CACD,OAAOhI,CAAC,EAAE;MACN;MACA;MACA2H,YAAY,GAAG,KAAK;MACpB;MACA,IAAI,CAACK,aAAa,CAAC,CAAC;MACpB,MAAMhI,CAAC;IACX;IACA;IACA,IAAI2H,YAAY,EAAE;MACd,IAAI,CAACM,WAAW,CAACL,iBAAiB,CAAC;IACvC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,UAAUA,CAACK,kBAAkB,EAAE,CAAE;EACjC;EACA;EACAD,WAAWA,CAACL,iBAAiB,EAAE;IAC3B,IAAIxL,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAAC4I,aAAa,MAAM,IAAI,IAAI5I,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuF,OAAO,CAAEmE,CAAC,IAAK;MAAE,IAAI1J,EAAE;MAAE,OAAO,CAACA,EAAE,GAAG0J,CAAC,CAACqC,WAAW,MAAM,IAAI,IAAI/L,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgJ,IAAI,CAACU,CAAC,CAAC;IAAE,CAAC,CAAC;IAC1K,IAAI,CAAC,IAAI,CAAC9E,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAG,IAAI;MACtB,IAAI,CAACoH,YAAY,CAACR,iBAAiB,CAAC;IACxC;IACA,IAAI,CAACS,OAAO,CAACT,iBAAiB,CAAC;IAC/B,IAAI5K,QAAQ,IACR,IAAI,CAAC+D,eAAe,IACpB,IAAI,CAACH,WAAW,CAAC2F,eAAe,CAAChB,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE;MACnErI,YAAY,CAAC,kBAAkB,EAAG,WAAU,IAAI,CAACsJ,SAAU,uBAAsB,GAC5E,yCAAwC,GACxC,mEAAkE,GAClE,mEAAkE,GAClE,gEAA+D,CAAC;IACzE;EACJ;EACAwB,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACrD,mBAAmB,GAAG,IAAI7D,GAAG,CAAC,CAAC;IACpC,IAAI,CAACC,eAAe,GAAG,KAAK;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIuH,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACC,iBAAiB,CAAC,CAAC;EACnC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAChE,eAAe;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIoD,YAAYA,CAACO,kBAAkB,EAAE;IAC7B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIH,MAAMA,CAACG,kBAAkB,EAAE;IACvB,IAAI,IAAI,CAACpB,sBAAsB,KAAK1I,SAAS,EAAE;MAC3C;MACA;MACA,IAAI,CAAC0I,sBAAsB,CAACnF,OAAO,CAAC,CAACC,CAAC,EAAE4G,CAAC,KAAK,IAAI,CAACnC,qBAAqB,CAACmC,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC,EAAE5G,CAAC,CAAC,CAAC;MACxF,IAAI,CAACkF,sBAAsB,GAAG1I,SAAS;IAC3C;IACA,IAAI,CAAC4J,aAAa,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIK,OAAOA,CAACH,kBAAkB,EAAE,CAAE;EAC9B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,YAAYA,CAACF,kBAAkB,EAAE,CAAE;AACvC;AACA1L,EAAE,GAAGiE,SAAS;AACd;AACA;AACA;AACAC,eAAe,CAAClE,EAAE,CAAC,GAAG,IAAI;AAC1B;AACA;AACA;AACA;AACA;AACA;AACAkE,eAAe,CAACgB,iBAAiB,GAAG,IAAIZ,GAAG,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACAJ,eAAe,CAACiD,aAAa,GAAG,EAAE;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAjD,eAAe,CAACkF,iBAAiB,GAAG;EAAE6C,IAAI,EAAE;AAAO,CAAC;AACpD;AACAnL,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAC;EAAEoD;AAAgB,CAAC,CAAC;AACtG;AACA,IAAI1D,QAAQ,EAAE;EACV;EACA0D,eAAe,CAAC6F,eAAe,GAAG,CAAC,kBAAkB,CAAC;EACtD,MAAMmC,iBAAiB,GAAG,SAAAA,CAAU/B,IAAI,EAAE;IACtC,IAAI,CAACA,IAAI,CAACpE,cAAc,CAACvD,yBAAyB,CAAC,iBAAiB,EAAE2H,IAAI,CAAC,CAAC,EAAE;MAC1EA,IAAI,CAACJ,eAAe,GAAGI,IAAI,CAACJ,eAAe,CAACoC,KAAK,CAAC,CAAC;IACvD;EACJ,CAAC;EACDjI,eAAe,CAACkI,aAAa,GAAG,UAAU/K,OAAO,EAAE;IAC/C6K,iBAAiB,CAAC,IAAI,CAAC;IACvB,IAAI,IAAI,CAACnC,eAAe,CAAChB,OAAO,CAAC1H,OAAO,CAAC,GAAG,CAAC,EAAE;MAC3C,IAAI,CAAC0I,eAAe,CAAChF,IAAI,CAAC1D,OAAO,CAAC;IACtC;EACJ,CAAC;EACD6C,eAAe,CAACmI,cAAc,GAAG,UAAUhL,OAAO,EAAE;IAChD6K,iBAAiB,CAAC,IAAI,CAAC;IACvB,MAAM7D,CAAC,GAAG,IAAI,CAAC0B,eAAe,CAAChB,OAAO,CAAC1H,OAAO,CAAC;IAC/C,IAAIgH,CAAC,IAAI,CAAC,EAAE;MACR,IAAI,CAAC0B,eAAe,CAACjB,MAAM,CAACT,CAAC,EAAE,CAAC,CAAC;IACrC;EACJ,CAAC;AACL;AACA;AACA;AACA,CAAC,CAACtI,EAAE,GAAGK,MAAM,CAACkM,uBAAuB,MAAM,IAAI,IAAIvM,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAIK,MAAM,CAACkM,uBAAuB,GAAG,EAAG,EAAEvH,IAAI,CAAC,OAAO,CAAC;AAC5H,IAAIvE,QAAQ,IAAIJ,MAAM,CAACkM,uBAAuB,CAACtB,MAAM,GAAG,CAAC,EAAE;EACvDtK,YAAY,CAAC,mBAAmB,EAAG,6DAA4D,GAC1F,qBAAoB,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}