{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"home\",\n  V = [\"home\", C({\n    outline: '<path d=\"M18.7722 2.29L33.8282 17.29L33.8182 17.3C34.1593 17.6968 34.1363 18.2882 33.7656 18.6576C33.3948 19.0269 32.8012 19.0498 32.4029 18.71L18.0596 4.41L3.7162 18.7C3.31795 19.0398 2.72431 19.0169 2.35356 18.6476C1.98281 18.2782 1.95988 17.6868 2.30093 17.29L17.357 2.29C17.7485 1.90228 18.3807 1.90228 18.7722 2.29Z\"/><path d=\"M23.0783 31.9999H28.097V17.7599L30.1044 19.7599V31.9999C30.1044 33.1045 29.2056 33.9999 28.097 33.9999H21.0708V23.9999H15.0484V33.9999H8.02225C6.91355 33.9999 6.01478 33.1045 6.01478 31.9999V19.9999L8.02225 17.9999V31.9999H13.0409V21.9999H23.0783V31.9999Z\"/>',\n    solid: '<path d=\"M32.3503 19.7148C32.5394 19.9021 32.7954 20.0064 33.0617 20.0049C33.4644 20.0032 33.827 19.7611 33.9821 19.3901C34.1373 19.0192 34.055 18.5915 33.7731 18.3044L18.7432 3.30009C18.3524 2.91226 17.7212 2.91226 17.3304 3.30009L2.30051 18.3044C1.96006 18.7013 1.98295 19.2929 2.35305 19.6624C2.72316 20.0318 3.31577 20.0547 3.71332 19.7148L18.0318 5.4107L32.3503 19.7148Z\"/><path d=\"M6.00793 20.8351L18.0318 8.79164L30.0558 20.7651V33.0086C30.0558 34.1135 29.1586 35.0092 28.0518 35.0092H21.0378V25.0063H15.0259V35.0092H8.01192C6.90515 35.0092 6.00793 34.1135 6.00793 33.0086V20.8351Z\"/>'\n  })];\nexport { V as homeIcon, L as homeIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "V", "outline", "solid", "homeIcon", "homeIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/home.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"home\",V=[\"home\",C({outline:'<path d=\"M18.7722 2.29L33.8282 17.29L33.8182 17.3C34.1593 17.6968 34.1363 18.2882 33.7656 18.6576C33.3948 19.0269 32.8012 19.0498 32.4029 18.71L18.0596 4.41L3.7162 18.7C3.31795 19.0398 2.72431 19.0169 2.35356 18.6476C1.98281 18.2782 1.95988 17.6868 2.30093 17.29L17.357 2.29C17.7485 1.90228 18.3807 1.90228 18.7722 2.29Z\"/><path d=\"M23.0783 31.9999H28.097V17.7599L30.1044 19.7599V31.9999C30.1044 33.1045 29.2056 33.9999 28.097 33.9999H21.0708V23.9999H15.0484V33.9999H8.02225C6.91355 33.9999 6.01478 33.1045 6.01478 31.9999V19.9999L8.02225 17.9999V31.9999H13.0409V21.9999H23.0783V31.9999Z\"/>',solid:'<path d=\"M32.3503 19.7148C32.5394 19.9021 32.7954 20.0064 33.0617 20.0049C33.4644 20.0032 33.827 19.7611 33.9821 19.3901C34.1373 19.0192 34.055 18.5915 33.7731 18.3044L18.7432 3.30009C18.3524 2.91226 17.7212 2.91226 17.3304 3.30009L2.30051 18.3044C1.96006 18.7013 1.98295 19.2929 2.35305 19.6624C2.72316 20.0318 3.31577 20.0547 3.71332 19.7148L18.0318 5.4107L32.3503 19.7148Z\"/><path d=\"M6.00793 20.8351L18.0318 8.79164L30.0558 20.7651V33.0086C30.0558 34.1135 29.1586 35.0092 28.0518 35.0092H21.0378V25.0063H15.0259V35.0092H8.01192C6.90515 35.0092 6.00793 34.1135 6.00793 33.0086V20.8351Z\"/>'})];export{V as homeIcon,L as homeIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,MAAM;EAACC,CAAC,GAAC,CAAC,MAAM,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,glBAAglB;IAACC,KAAK,EAAC;EAAilB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,QAAQ,EAACJ,CAAC,IAAIK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}