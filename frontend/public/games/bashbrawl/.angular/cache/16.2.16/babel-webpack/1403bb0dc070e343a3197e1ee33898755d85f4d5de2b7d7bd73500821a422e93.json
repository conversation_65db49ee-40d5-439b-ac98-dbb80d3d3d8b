{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"fish\",\n  o = [\"fish\", C({\n    outline: '<path d=\"M10.8658 19.006C11.7198 19.006 12.4122 18.3304 12.4122 17.497C12.4122 16.6636 11.7198 15.9879 10.8658 15.9879C10.0118 15.9879 9.31945 16.6636 9.31945 17.497C9.31945 18.3304 10.0118 19.006 10.8658 19.006Z\"/><path d=\"M33.5355 9.23742C33.3788 9.14913 33.201 9.10264 33.02 9.10264C32.8391 9.10264 32.6613 9.14913 32.5046 9.23742C29.0304 11.2495 26.4119 15.0825 25.3913 16.7324L24.6387 18L23.6078 19.6298C21.7934 22.1348 18.3502 26.0282 14.5462 26.0282C9.75243 26.0282 5.52571 19.841 4.40202 18.0402C5.52571 16.2394 9.74212 10.0523 14.5462 10.0523C18.1956 10.0523 21.5254 13.6539 23.3913 16.159L23.68 15.6761C23.68 15.6761 24.0511 15.163 24.6387 14.4185C22.4429 11.6016 18.7935 8 14.4843 8C7.69061 8 2.4536 17.1247 2.23711 17.5171L2 18L2.27835 18.4829C2.49484 18.8752 7.73185 28 14.5255 28C19.7419 28 24.0305 22.6278 25.8655 19.9517C25.8243 20.0121 27.0408 18 27.0408 18L27.1129 17.8893C27.6702 16.9108 28.319 15.9847 29.051 15.1227C29.9258 14.0212 30.9137 13.0099 31.9994 12.1046V23.9155C30.5864 22.7362 29.3355 21.3836 28.2778 19.8913L28.1129 20.1529L27.082 21.7525C28.5264 23.7351 30.3502 25.4261 32.453 26.7324C32.6091 26.8228 32.787 26.8714 32.9685 26.8732C33.1483 26.8691 33.3248 26.8243 33.4839 26.7425C33.6413 26.6538 33.7718 26.5262 33.8623 26.3725C33.9528 26.2188 34.0001 26.0445 33.9994 25.8672V10.1127C34.0055 9.94021 33.966 9.76911 33.8848 9.61583C33.8035 9.46255 33.6832 9.33224 33.5355 9.23742Z\"/>'\n  })];\nexport { o as fishIcon, L as fishIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "o", "outline", "fishIcon", "fishIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/fish.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"fish\",o=[\"fish\",C({outline:'<path d=\"M10.8658 19.006C11.7198 19.006 12.4122 18.3304 12.4122 17.497C12.4122 16.6636 11.7198 15.9879 10.8658 15.9879C10.0118 15.9879 9.31945 16.6636 9.31945 17.497C9.31945 18.3304 10.0118 19.006 10.8658 19.006Z\"/><path d=\"M33.5355 9.23742C33.3788 9.14913 33.201 9.10264 33.02 9.10264C32.8391 9.10264 32.6613 9.14913 32.5046 9.23742C29.0304 11.2495 26.4119 15.0825 25.3913 16.7324L24.6387 18L23.6078 19.6298C21.7934 22.1348 18.3502 26.0282 14.5462 26.0282C9.75243 26.0282 5.52571 19.841 4.40202 18.0402C5.52571 16.2394 9.74212 10.0523 14.5462 10.0523C18.1956 10.0523 21.5254 13.6539 23.3913 16.159L23.68 15.6761C23.68 15.6761 24.0511 15.163 24.6387 14.4185C22.4429 11.6016 18.7935 8 14.4843 8C7.69061 8 2.4536 17.1247 2.23711 17.5171L2 18L2.27835 18.4829C2.49484 18.8752 7.73185 28 14.5255 28C19.7419 28 24.0305 22.6278 25.8655 19.9517C25.8243 20.0121 27.0408 18 27.0408 18L27.1129 17.8893C27.6702 16.9108 28.319 15.9847 29.051 15.1227C29.9258 14.0212 30.9137 13.0099 31.9994 12.1046V23.9155C30.5864 22.7362 29.3355 21.3836 28.2778 19.8913L28.1129 20.1529L27.082 21.7525C28.5264 23.7351 30.3502 25.4261 32.453 26.7324C32.6091 26.8228 32.787 26.8714 32.9685 26.8732C33.1483 26.8691 33.3248 26.8243 33.4839 26.7425C33.6413 26.6538 33.7718 26.5262 33.8623 26.3725C33.9528 26.2188 34.0001 26.0445 33.9994 25.8672V10.1127C34.0055 9.94021 33.966 9.76911 33.8848 9.61583C33.8035 9.46255 33.6832 9.33224 33.5355 9.23742Z\"/>'})];export{o as fishIcon,L as fishIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,MAAM;EAACC,CAAC,GAAC,CAAC,MAAM,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAA04C,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,QAAQ,EAACH,CAAC,IAAII,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}