{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst L = \"cloud-traffic\",\n  H = [\"cloud-traffic\", C({\n    outline: '<path d=\"M29 21.5C28.45 21.5 28 21.95 28 22.5C28 23.05 28.45 23.5 29 23.5H30C30.55 23.5 31 23.05 31 22.5C31 21.95 30.55 21.5 30 21.5H29ZM27 22.5C27 21.95 26.55 21.5 26 21.5H21.41L22.7 20.21C23.09 19.82 23.09 19.19 22.7 18.8C22.31 18.41 21.68 18.41 21.29 18.8L18.29 21.8L17.58 22.51L18.29 23.22L21.29 26.22C21.49 26.42 21.74 26.51 22 26.51C22.26 26.51 22.51 26.41 22.71 26.22C23.1 25.83 23.1 25.2 22.71 24.81L21.42 23.52H26.01C26.56 23.52 27.01 23.07 27.01 22.52L27 22.5ZM29.59 13.5H25C24.45 13.5 24 13.95 24 14.5C24 15.05 24.45 15.5 25 15.5H29.59L28.3 16.79C27.91 17.18 27.91 17.81 28.3 18.2C28.5 18.4 28.75 18.49 29.01 18.49C29.27 18.49 29.52 18.39 29.72 18.2L32.72 15.2L33.43 14.49L32.72 13.78L29.72 10.78C29.33 10.39 28.7 10.39 28.31 10.78C27.92 11.17 27.92 11.8 28.31 12.19L29.6 13.48L29.59 13.5ZM33 21.5C32.45 21.5 32 21.95 32 22.5C32 23.05 32.45 23.5 33 23.5C33.55 23.5 34 23.05 34 22.5C34 21.95 33.55 21.5 33 21.5ZM19 14.5C19 13.95 18.55 13.5 18 13.5C17.45 13.5 17 13.95 17 14.5C17 15.05 17.45 15.5 18 15.5C18.55 15.5 19 15.05 19 14.5ZM30 25.5H29.43C28.55 27.27 26.74 28.5 24.64 28.5H9.55C9.47 28.48 9.39 28.48 9.3 28.48H9.18C9.12 28.49 9.06 28.5 8.99 28.5C6.23 28.5 3.99 26.26 3.99 23.5C3.99 21.02 5.77 18.95 8.21 18.57C8.74 18.49 9.11 18 9.05 17.46C9.01 17.14 8.99 16.82 8.99 16.5C8.99 12.09 12.58 8.5 16.99 8.5C19.68 8.5 22.07 9.84 23.52 11.89C23.96 11.64 24.46 11.5 24.99 11.5H25.64C23.91 8.51 20.68 6.5 16.99 6.5C11.49 6.5 7 10.99 7 16.5C7 16.6 7 16.69 7 16.79C4.05 17.65 2 20.34 2 23.5C2 27.36 5.14 30.5 9 30.5C9.07 30.5 9.14 30.5 9.23 30.5C9.27 30.5 9.31 30.5 9.36 30.5H24.63C27.98 30.5 30.81 28.25 31.7 25.18C31.63 25.15 31.56 25.13 31.5 25.09C31.06 25.35 30.55 25.49 30 25.49V25.5ZM22 15.5C22.55 15.5 23 15.05 23 14.5C23 13.95 22.55 13.5 22 13.5H21C20.45 13.5 20 13.95 20 14.5C20 15.05 20.45 15.5 21 15.5H22Z\"/>'\n  })];\nexport { H as cloudTrafficIcon, L as cloudTrafficIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "L", "H", "outline", "cloudTrafficIcon", "cloudTrafficIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/cloud-traffic.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const L=\"cloud-traffic\",H=[\"cloud-traffic\",C({outline:'<path d=\"M29 21.5C28.45 21.5 28 21.95 28 22.5C28 23.05 28.45 23.5 29 23.5H30C30.55 23.5 31 23.05 31 22.5C31 21.95 30.55 21.5 30 21.5H29ZM27 22.5C27 21.95 26.55 21.5 26 21.5H21.41L22.7 20.21C23.09 19.82 23.09 19.19 22.7 18.8C22.31 18.41 21.68 18.41 21.29 18.8L18.29 21.8L17.58 22.51L18.29 23.22L21.29 26.22C21.49 26.42 21.74 26.51 22 26.51C22.26 26.51 22.51 26.41 22.71 26.22C23.1 25.83 23.1 25.2 22.71 24.81L21.42 23.52H26.01C26.56 23.52 27.01 23.07 27.01 22.52L27 22.5ZM29.59 13.5H25C24.45 13.5 24 13.95 24 14.5C24 15.05 24.45 15.5 25 15.5H29.59L28.3 16.79C27.91 17.18 27.91 17.81 28.3 18.2C28.5 18.4 28.75 18.49 29.01 18.49C29.27 18.49 29.52 18.39 29.72 18.2L32.72 15.2L33.43 14.49L32.72 13.78L29.72 10.78C29.33 10.39 28.7 10.39 28.31 10.78C27.92 11.17 27.92 11.8 28.31 12.19L29.6 13.48L29.59 13.5ZM33 21.5C32.45 21.5 32 21.95 32 22.5C32 23.05 32.45 23.5 33 23.5C33.55 23.5 34 23.05 34 22.5C34 21.95 33.55 21.5 33 21.5ZM19 14.5C19 13.95 18.55 13.5 18 13.5C17.45 13.5 17 13.95 17 14.5C17 15.05 17.45 15.5 18 15.5C18.55 15.5 19 15.05 19 14.5ZM30 25.5H29.43C28.55 27.27 26.74 28.5 24.64 28.5H9.55C9.47 28.48 9.39 28.48 9.3 28.48H9.18C9.12 28.49 9.06 28.5 8.99 28.5C6.23 28.5 3.99 26.26 3.99 23.5C3.99 21.02 5.77 18.95 8.21 18.57C8.74 18.49 9.11 18 9.05 17.46C9.01 17.14 8.99 16.82 8.99 16.5C8.99 12.09 12.58 8.5 16.99 8.5C19.68 8.5 22.07 9.84 23.52 11.89C23.96 11.64 24.46 11.5 24.99 11.5H25.64C23.91 8.51 20.68 6.5 16.99 6.5C11.49 6.5 7 10.99 7 16.5C7 16.6 7 16.69 7 16.79C4.05 17.65 2 20.34 2 23.5C2 27.36 5.14 30.5 9 30.5C9.07 30.5 9.14 30.5 9.23 30.5C9.27 30.5 9.31 30.5 9.36 30.5H24.63C27.98 30.5 30.81 28.25 31.7 25.18C31.63 25.15 31.56 25.13 31.5 25.09C31.06 25.35 30.55 25.49 30 25.49V25.5ZM22 15.5C22.55 15.5 23 15.05 23 14.5C23 13.95 22.55 13.5 22 13.5H21C20.45 13.5 20 13.95 20 14.5C20 15.05 20.45 15.5 21 15.5H22Z\"/>'})];export{H as cloudTrafficIcon,L as cloudTrafficIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,eAAe;EAACC,CAAC,GAAC,CAAC,eAAe,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC;EAAqyD,CAAC,CAAC,CAAC;AAAC,SAAOD,CAAC,IAAIE,gBAAgB,EAACH,CAAC,IAAII,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}