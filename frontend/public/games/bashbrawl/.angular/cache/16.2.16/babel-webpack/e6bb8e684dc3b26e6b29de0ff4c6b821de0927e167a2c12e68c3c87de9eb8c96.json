{"ast": null, "code": "import { renderIcon as C } from \"../icon.renderer.js\";\nconst V = \"shopping-bag\",\n  H = [\"shopping-bag\", C({\n    outline: '<path d=\"M25 12.0156V9.06094C25 6.55617 23.6658 4.24166 21.5 2.98928C19.3342 1.73689 16.6658 1.73689 14.5 2.98928C12.3342 4.24166 11 6.55617 11 9.06094V16.0719C11 16.625 11.4477 17.0735 12 17.0735C12.5523 17.0735 13 16.625 13 16.0719V14.0187H21V12.0156H13V9.06094C13 6.2952 15.2386 4.05312 18 4.05312C20.7614 4.05312 23 6.2952 23 9.06094V16.0218C23 16.575 23.4477 17.0234 24 17.0234C24.5523 17.0234 25 16.575 25 16.0218V14.0187H30V32.0469H6V14.0187H9V12.0156H4V32.137C4 33.1935 4.85514 34.05 5.91 34.05H30.09C31.1449 34.05 32 33.1935 32 32.137V12.0156H25Z\"/>',\n    solid: '<path d=\"M18 4.23385C15.2386 4.23385 13 6.48792 13 9.26846V12.0677H11V9.26846C11 6.75029 12.3342 4.4234 14.5 3.16432C16.6658 1.90523 19.3342 1.90523 21.5 3.16432C23.6658 4.4234 25 6.75029 25 9.26846V12.0677H23V9.26846C23 6.48792 20.7614 4.23385 18 4.23385Z\"/><path d=\"M25 15.1892V12.0677L32 12.0677V32.2062C32 33.3184 31.1046 34.22 30 34.22H6C4.89543 34.22 4 33.3184 4 32.2062V12.0677L11 12.0677V15.1892C11 15.7453 11.4477 16.1961 12 16.1961C12.5523 16.1961 13 15.7453 13 15.1892V12.0677H23V15.1892C23 15.7453 23.4477 16.1961 24 16.1961C24.5523 16.1961 25 15.7453 25 15.1892Z\"/>'\n  })];\nexport { H as shoppingBagIcon, V as shoppingBagIconName };", "map": {"version": 3, "names": ["renderIcon", "C", "V", "H", "outline", "solid", "shoppingBagIcon", "shoppingBagIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/shopping-bag.js"], "sourcesContent": ["import{renderIcon as C}from\"../icon.renderer.js\";const V=\"shopping-bag\",H=[\"shopping-bag\",C({outline:'<path d=\"M25 12.0156V9.06094C25 6.55617 23.6658 4.24166 21.5 2.98928C19.3342 1.73689 16.6658 1.73689 14.5 2.98928C12.3342 4.24166 11 6.55617 11 9.06094V16.0719C11 16.625 11.4477 17.0735 12 17.0735C12.5523 17.0735 13 16.625 13 16.0719V14.0187H21V12.0156H13V9.06094C13 6.2952 15.2386 4.05312 18 4.05312C20.7614 4.05312 23 6.2952 23 9.06094V16.0218C23 16.575 23.4477 17.0234 24 17.0234C24.5523 17.0234 25 16.575 25 16.0218V14.0187H30V32.0469H6V14.0187H9V12.0156H4V32.137C4 33.1935 4.85514 34.05 5.91 34.05H30.09C31.1449 34.05 32 33.1935 32 32.137V12.0156H25Z\"/>',solid:'<path d=\"M18 4.23385C15.2386 4.23385 13 6.48792 13 9.26846V12.0677H11V9.26846C11 6.75029 12.3342 4.4234 14.5 3.16432C16.6658 1.90523 19.3342 1.90523 21.5 3.16432C23.6658 4.4234 25 6.75029 25 9.26846V12.0677H23V9.26846C23 6.48792 20.7614 4.23385 18 4.23385Z\"/><path d=\"M25 15.1892V12.0677L32 12.0677V32.2062C32 33.3184 31.1046 34.22 30 34.22H6C4.89543 34.22 4 33.3184 4 32.2062V12.0677L11 12.0677V15.1892C11 15.7453 11.4477 16.1961 12 16.1961C12.5523 16.1961 13 15.7453 13 15.1892V12.0677H23V15.1892C23 15.7453 23.4477 16.1961 24 16.1961C24.5523 16.1961 25 15.7453 25 15.1892Z\"/>'})];export{H as shoppingBagIcon,V as shoppingBagIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,cAAc;EAACC,CAAC,GAAC,CAAC,cAAc,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,gjBAAgjB;IAACC,KAAK,EAAC;EAAokB,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,eAAe,EAACJ,CAAC,IAAIK,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}