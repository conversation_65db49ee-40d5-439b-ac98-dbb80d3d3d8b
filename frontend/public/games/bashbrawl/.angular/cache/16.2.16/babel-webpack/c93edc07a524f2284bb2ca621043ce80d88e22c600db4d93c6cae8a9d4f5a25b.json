{"ast": null, "code": "import { existsInWindow as o } from \"../utils/exists.js\";\nclass n {\n  static log(...o) {\n    t() && r() && console.log(...o);\n  }\n  static warn(...o) {\n    t() && r() && console.warn(...o);\n  }\n  static error(...o) {\n    t() && r() && console.error(...o);\n  }\n}\nfunction r() {\n  return !o([\"jasmine\"]);\n}\nfunction t() {\n  return !window.CDS.environment.production;\n}\nexport { n as LogService, t as notProductionEnvironment };", "map": {"version": 3, "names": ["existsInWindow", "o", "n", "log", "t", "r", "console", "warn", "error", "window", "CDS", "environment", "production", "LogService", "notProductionEnvironment"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/services/log.service.js"], "sourcesContent": ["import{existsInWindow as o}from\"../utils/exists.js\";class n{static log(...o){t()&&r()&&console.log(...o)}static warn(...o){t()&&r()&&console.warn(...o)}static error(...o){t()&&r()&&console.error(...o)}}function r(){return!o([\"jasmine\"])}function t(){return!window.CDS.environment.production}export{n as LogService,t as notProductionEnvironment};\n"], "mappings": "AAAA,SAAOA,cAAc,IAAIC,CAAC,QAAK,oBAAoB;AAAC,MAAMC,CAAC;EAAC,OAAOC,GAAGA,CAAC,GAAGF,CAAC,EAAC;IAACG,CAAC,CAAC,CAAC,IAAEC,CAAC,CAAC,CAAC,IAAEC,OAAO,CAACH,GAAG,CAAC,GAAGF,CAAC,CAAC;EAAA;EAAC,OAAOM,IAAIA,CAAC,GAAGN,CAAC,EAAC;IAACG,CAAC,CAAC,CAAC,IAAEC,CAAC,CAAC,CAAC,IAAEC,OAAO,CAACC,IAAI,CAAC,GAAGN,CAAC,CAAC;EAAA;EAAC,OAAOO,KAAKA,CAAC,GAAGP,CAAC,EAAC;IAACG,CAAC,CAAC,CAAC,IAAEC,CAAC,CAAC,CAAC,IAAEC,OAAO,CAACE,KAAK,CAAC,GAAGP,CAAC,CAAC;EAAA;AAAC;AAAC,SAASI,CAACA,CAAA,EAAE;EAAC,OAAM,CAACJ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AAAA;AAAC,SAASG,CAACA,CAAA,EAAE;EAAC,OAAM,CAACK,MAAM,CAACC,GAAG,CAACC,WAAW,CAACC,UAAU;AAAA;AAAC,SAAOV,CAAC,IAAIW,UAAU,EAACT,CAAC,IAAIU,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}