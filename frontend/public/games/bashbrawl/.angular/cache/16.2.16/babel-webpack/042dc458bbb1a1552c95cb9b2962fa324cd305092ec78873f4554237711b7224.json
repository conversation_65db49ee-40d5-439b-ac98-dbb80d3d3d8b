{"ast": null, "code": "import { isNilOrEmpty as t, isNumericString as n, getFromObjectPath as r } from \"./identity.js\";\nfunction e(t, n, ...r) {\n  return n.map(t => t(...r)).join(t).trim();\n}\nfunction u(t, ...n) {\n  return e(\" \", t, ...n);\n}\nfunction i(t, ...n) {\n  return e(\"\", t, ...n);\n}\nfunction c(t) {\n  return t.replace(/[A-Z]/g, t => \"-\" + t.toLowerCase());\n}\nfunction o(t) {\n  return t.split(\"-\").map((t, n) => n ? t.charAt(0).toUpperCase() + t.slice(1).toLowerCase() : t).join(\"\");\n}\nfunction f(t) {\n  return a(o(t));\n}\nfunction s(t) {\n  return l(Object.keys(t), t);\n}\nfunction p(t) {\n  return l(Object.keys(t).filter(t => t.startsWith(\"--\")), t);\n}\nfunction l(t, n) {\n  return t.reduce((t, r) => `${t}${n[r] ? `${r}:${n[r]};` : \"\"}`, \"\");\n}\nfunction a(t) {\n  return t.charAt(0).toUpperCase() + t.slice(1);\n}\nconst h = \"CSS Custom Properties\",\n  g = \"Default Properties\";\nfunction m(t) {\n  return k(t, \"s\");\n}\nfunction x(n, r, e = \"prefix\") {\n  return !t(r) && !t(n) && (\"prefix\" === e ? n.substr(0, r.length) : n.substr(-1 * r.length)) === r;\n}\nfunction b(t, n) {\n  return x(t, n, \"prefix\");\n}\nfunction j(t, n) {\n  return x(t, n, \"suffix\");\n}\nfunction C(n, r, e = \"prefix\") {\n  if (t(n)) return \"\";\n  if (t(r) || !x(n, r, e)) return n;\n  switch (e) {\n    case \"prefix\":\n      return n.substr(r.length);\n    case \"suffix\":\n      return n.substr(0, n.length - r.length);\n    default:\n      return n;\n  }\n}\nfunction $(t, n) {\n  return C(t, n, \"prefix\");\n}\nfunction d(t, n) {\n  return C(t, n, \"suffix\");\n}\nfunction w(t, n, r = \"\") {\n  const e = t.split(\" \"),\n    u = \"\" === r ? e.filter(t => t !== n) : e.map(t => t === n ? r : t);\n  return u.length > 0 ? u.join(\" \") : \"\";\n}\nfunction y(t) {\n  return t ? (t + \"\").trim().replace(/  +/g, \" \") : \"\";\n}\nfunction A(t) {\n  const n = y(t);\n  return \"\" === n ? [] : n.split(\" \");\n}\nfunction O(t, n, r) {\n  return r ? Object.assign(n, r(t)) : n;\n}\nfunction k(t, r) {\n  const e = t ? t.trim() : \"\";\n  if (\"\" === e || !e.endsWith(r)) return 0;\n  const u = e.slice(0, -1 * r.length);\n  return n(u) ? +u : 0;\n}\nfunction L(t) {\n  return t ? k(t.trim(), \"px\") : 0;\n}\nfunction P(t, n, e) {\n  return t.replace(/\\$\\{.+?\\}/g, t => {\n    const u = t.substr(2, t.length - 3).trim();\n    return r(u, n, e);\n  });\n}\nexport { c as camelCaseToKebabCase, a as capitalizeFirstLetter, O as convertStringPropertyToObjectConfig, h as cssGroup, m as getNumericValueFromCssSecondsStyleValue, P as interpolateNaively, b as isPrefixedBy, x as isPrefixedOrSuffixedBy, j as isSuffixedBy, o as kebabCaseToCamelCase, f as kebabCaseToPascalCase, L as pluckPixelValue, k as pluckValueFromStringUnit, g as propertiesGroup, $ as removePrefix, C as removePrefixOrSuffix, d as removeSuffix, w as replaceWord, p as setPropStyles, s as setStyles, A as transformSpacedStringToArray, u as transformToSpacedString, e as transformToString, i as transformToUnspacedString, y as trimExtraWhitespace };", "map": {"version": 3, "names": ["isNilOrEmpty", "t", "isNumericString", "n", "getFromObjectPath", "r", "e", "map", "join", "trim", "u", "i", "c", "replace", "toLowerCase", "o", "split", "char<PERSON>t", "toUpperCase", "slice", "f", "a", "s", "l", "Object", "keys", "p", "filter", "startsWith", "reduce", "h", "g", "m", "k", "x", "substr", "length", "b", "j", "C", "$", "d", "w", "y", "A", "O", "assign", "endsWith", "L", "P", "camelCaseToKebabCase", "capitalizeFirstLetter", "convertStringPropertyToObjectConfig", "cssGroup", "getNumericValueFromCssSecondsStyleValue", "interpolate<PERSON><PERSON><PERSON><PERSON>", "isPrefixedBy", "isPrefixedOrSuffixedBy", "isSuffixedBy", "kebabCaseToCamelCase", "kebabCaseToPascalCase", "pluckPixelValue", "pluckValueFromStringUnit", "propertiesGroup", "removePrefix", "removePrefixOrSuffix", "removeSuffix", "replace<PERSON><PERSON>", "setPropStyles", "setStyles", "transformSpacedStringToArray", "transformToSpacedString", "transformToString", "transformToUnspacedString", "trimExtraWhitespace"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/string.js"], "sourcesContent": ["import{isNilOrEmpty as t,isNumericString as n,getFromObjectPath as r}from\"./identity.js\";function e(t,n,...r){return n.map((t=>t(...r))).join(t).trim()}function u(t,...n){return e(\" \",t,...n)}function i(t,...n){return e(\"\",t,...n)}function c(t){return t.replace(/[A-Z]/g,(t=>\"-\"+t.toLowerCase()))}function o(t){return t.split(\"-\").map(((t,n)=>n?t.charAt(0).toUpperCase()+t.slice(1).toLowerCase():t)).join(\"\")}function f(t){return a(o(t))}function s(t){return l(Object.keys(t),t)}function p(t){return l(Object.keys(t).filter((t=>t.startsWith(\"--\"))),t)}function l(t,n){return t.reduce(((t,r)=>`${t}${n[r]?`${r}:${n[r]};`:\"\"}`),\"\")}function a(t){return t.charAt(0).toUpperCase()+t.slice(1)}const h=\"CSS Custom Properties\",g=\"Default Properties\";function m(t){return k(t,\"s\")}function x(n,r,e=\"prefix\"){return!t(r)&&!t(n)&&(\"prefix\"===e?n.substr(0,r.length):n.substr(-1*r.length))===r}function b(t,n){return x(t,n,\"prefix\")}function j(t,n){return x(t,n,\"suffix\")}function C(n,r,e=\"prefix\"){if(t(n))return\"\";if(t(r)||!x(n,r,e))return n;switch(e){case\"prefix\":return n.substr(r.length);case\"suffix\":return n.substr(0,n.length-r.length);default:return n}}function $(t,n){return C(t,n,\"prefix\")}function d(t,n){return C(t,n,\"suffix\")}function w(t,n,r=\"\"){const e=t.split(\" \"),u=\"\"===r?e.filter((t=>t!==n)):e.map((t=>t===n?r:t));return u.length>0?u.join(\" \"):\"\"}function y(t){return t?(t+\"\").trim().replace(/  +/g,\" \"):\"\"}function A(t){const n=y(t);return\"\"===n?[]:n.split(\" \")}function O(t,n,r){return r?Object.assign(n,r(t)):n}function k(t,r){const e=t?t.trim():\"\";if(\"\"===e||!e.endsWith(r))return 0;const u=e.slice(0,-1*r.length);return n(u)?+u:0}function L(t){return t?k(t.trim(),\"px\"):0}function P(t,n,e){return t.replace(/\\$\\{.+?\\}/g,(t=>{const u=t.substr(2,t.length-3).trim();return r(u,n,e)}))}export{c as camelCaseToKebabCase,a as capitalizeFirstLetter,O as convertStringPropertyToObjectConfig,h as cssGroup,m as getNumericValueFromCssSecondsStyleValue,P as interpolateNaively,b as isPrefixedBy,x as isPrefixedOrSuffixedBy,j as isSuffixedBy,o as kebabCaseToCamelCase,f as kebabCaseToPascalCase,L as pluckPixelValue,k as pluckValueFromStringUnit,g as propertiesGroup,$ as removePrefix,C as removePrefixOrSuffix,d as removeSuffix,w as replaceWord,p as setPropStyles,s as setStyles,A as transformSpacedStringToArray,u as transformToSpacedString,e as transformToString,i as transformToUnspacedString,y as trimExtraWhitespace};\n"], "mappings": "AAAA,SAAOA,YAAY,IAAIC,CAAC,EAACC,eAAe,IAAIC,CAAC,EAACC,iBAAiB,IAAIC,CAAC,QAAK,eAAe;AAAC,SAASC,CAACA,CAACL,CAAC,EAACE,CAAC,EAAC,GAAGE,CAAC,EAAC;EAAC,OAAOF,CAAC,CAACI,GAAG,CAAEN,CAAC,IAAEA,CAAC,CAAC,GAAGI,CAAC,CAAE,CAAC,CAACG,IAAI,CAACP,CAAC,CAAC,CAACQ,IAAI,CAAC,CAAC;AAAA;AAAC,SAASC,CAACA,CAACT,CAAC,EAAC,GAAGE,CAAC,EAAC;EAAC,OAAOG,CAAC,CAAC,GAAG,EAACL,CAAC,EAAC,GAAGE,CAAC,CAAC;AAAA;AAAC,SAASQ,CAACA,CAACV,CAAC,EAAC,GAAGE,CAAC,EAAC;EAAC,OAAOG,CAAC,CAAC,EAAE,EAACL,CAAC,EAAC,GAAGE,CAAC,CAAC;AAAA;AAAC,SAASS,CAACA,CAACX,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACY,OAAO,CAAC,QAAQ,EAAEZ,CAAC,IAAE,GAAG,GAACA,CAAC,CAACa,WAAW,CAAC,CAAE,CAAC;AAAA;AAAC,SAASC,CAACA,CAACd,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACe,KAAK,CAAC,GAAG,CAAC,CAACT,GAAG,CAAE,CAACN,CAAC,EAACE,CAAC,KAAGA,CAAC,GAACF,CAAC,CAACgB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAACjB,CAAC,CAACkB,KAAK,CAAC,CAAC,CAAC,CAACL,WAAW,CAAC,CAAC,GAACb,CAAE,CAAC,CAACO,IAAI,CAAC,EAAE,CAAC;AAAA;AAAC,SAASY,CAACA,CAACnB,CAAC,EAAC;EAAC,OAAOoB,CAAC,CAACN,CAAC,CAACd,CAAC,CAAC,CAAC;AAAA;AAAC,SAASqB,CAACA,CAACrB,CAAC,EAAC;EAAC,OAAOsB,CAAC,CAACC,MAAM,CAACC,IAAI,CAACxB,CAAC,CAAC,EAACA,CAAC,CAAC;AAAA;AAAC,SAASyB,CAACA,CAACzB,CAAC,EAAC;EAAC,OAAOsB,CAAC,CAACC,MAAM,CAACC,IAAI,CAACxB,CAAC,CAAC,CAAC0B,MAAM,CAAE1B,CAAC,IAAEA,CAAC,CAAC2B,UAAU,CAAC,IAAI,CAAE,CAAC,EAAC3B,CAAC,CAAC;AAAA;AAAC,SAASsB,CAACA,CAACtB,CAAC,EAACE,CAAC,EAAC;EAAC,OAAOF,CAAC,CAAC4B,MAAM,CAAE,CAAC5B,CAAC,EAACI,CAAC,KAAI,GAAEJ,CAAE,GAAEE,CAAC,CAACE,CAAC,CAAC,GAAE,GAAEA,CAAE,IAAGF,CAAC,CAACE,CAAC,CAAE,GAAE,GAAC,EAAG,EAAC,EAAE,EAAE,CAAC;AAAA;AAAC,SAASgB,CAACA,CAACpB,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACgB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAACjB,CAAC,CAACkB,KAAK,CAAC,CAAC,CAAC;AAAA;AAAC,MAAMW,CAAC,GAAC,uBAAuB;EAACC,CAAC,GAAC,oBAAoB;AAAC,SAASC,CAACA,CAAC/B,CAAC,EAAC;EAAC,OAAOgC,CAAC,CAAChC,CAAC,EAAC,GAAG,CAAC;AAAA;AAAC,SAASiC,CAACA,CAAC/B,CAAC,EAACE,CAAC,EAACC,CAAC,GAAC,QAAQ,EAAC;EAAC,OAAM,CAACL,CAAC,CAACI,CAAC,CAAC,IAAE,CAACJ,CAAC,CAACE,CAAC,CAAC,IAAE,CAAC,QAAQ,KAAGG,CAAC,GAACH,CAAC,CAACgC,MAAM,CAAC,CAAC,EAAC9B,CAAC,CAAC+B,MAAM,CAAC,GAACjC,CAAC,CAACgC,MAAM,CAAC,CAAC,CAAC,GAAC9B,CAAC,CAAC+B,MAAM,CAAC,MAAI/B,CAAC;AAAA;AAAC,SAASgC,CAACA,CAACpC,CAAC,EAACE,CAAC,EAAC;EAAC,OAAO+B,CAAC,CAACjC,CAAC,EAACE,CAAC,EAAC,QAAQ,CAAC;AAAA;AAAC,SAASmC,CAACA,CAACrC,CAAC,EAACE,CAAC,EAAC;EAAC,OAAO+B,CAAC,CAACjC,CAAC,EAACE,CAAC,EAAC,QAAQ,CAAC;AAAA;AAAC,SAASoC,CAACA,CAACpC,CAAC,EAACE,CAAC,EAACC,CAAC,GAAC,QAAQ,EAAC;EAAC,IAAGL,CAAC,CAACE,CAAC,CAAC,EAAC,OAAM,EAAE;EAAC,IAAGF,CAAC,CAACI,CAAC,CAAC,IAAE,CAAC6B,CAAC,CAAC/B,CAAC,EAACE,CAAC,EAACC,CAAC,CAAC,EAAC,OAAOH,CAAC;EAAC,QAAOG,CAAC;IAAE,KAAI,QAAQ;MAAC,OAAOH,CAAC,CAACgC,MAAM,CAAC9B,CAAC,CAAC+B,MAAM,CAAC;IAAC,KAAI,QAAQ;MAAC,OAAOjC,CAAC,CAACgC,MAAM,CAAC,CAAC,EAAChC,CAAC,CAACiC,MAAM,GAAC/B,CAAC,CAAC+B,MAAM,CAAC;IAAC;MAAQ,OAAOjC,CAAC;EAAA;AAAC;AAAC,SAASqC,CAACA,CAACvC,CAAC,EAACE,CAAC,EAAC;EAAC,OAAOoC,CAAC,CAACtC,CAAC,EAACE,CAAC,EAAC,QAAQ,CAAC;AAAA;AAAC,SAASsC,CAACA,CAACxC,CAAC,EAACE,CAAC,EAAC;EAAC,OAAOoC,CAAC,CAACtC,CAAC,EAACE,CAAC,EAAC,QAAQ,CAAC;AAAA;AAAC,SAASuC,CAACA,CAACzC,CAAC,EAACE,CAAC,EAACE,CAAC,GAAC,EAAE,EAAC;EAAC,MAAMC,CAAC,GAACL,CAAC,CAACe,KAAK,CAAC,GAAG,CAAC;IAACN,CAAC,GAAC,EAAE,KAAGL,CAAC,GAACC,CAAC,CAACqB,MAAM,CAAE1B,CAAC,IAAEA,CAAC,KAAGE,CAAE,CAAC,GAACG,CAAC,CAACC,GAAG,CAAEN,CAAC,IAAEA,CAAC,KAAGE,CAAC,GAACE,CAAC,GAACJ,CAAE,CAAC;EAAC,OAAOS,CAAC,CAAC0B,MAAM,GAAC,CAAC,GAAC1B,CAAC,CAACF,IAAI,CAAC,GAAG,CAAC,GAAC,EAAE;AAAA;AAAC,SAASmC,CAACA,CAAC1C,CAAC,EAAC;EAAC,OAAOA,CAAC,GAAC,CAACA,CAAC,GAAC,EAAE,EAAEQ,IAAI,CAAC,CAAC,CAACI,OAAO,CAAC,MAAM,EAAC,GAAG,CAAC,GAAC,EAAE;AAAA;AAAC,SAAS+B,CAACA,CAAC3C,CAAC,EAAC;EAAC,MAAME,CAAC,GAACwC,CAAC,CAAC1C,CAAC,CAAC;EAAC,OAAM,EAAE,KAAGE,CAAC,GAAC,EAAE,GAACA,CAAC,CAACa,KAAK,CAAC,GAAG,CAAC;AAAA;AAAC,SAAS6B,CAACA,CAAC5C,CAAC,EAACE,CAAC,EAACE,CAAC,EAAC;EAAC,OAAOA,CAAC,GAACmB,MAAM,CAACsB,MAAM,CAAC3C,CAAC,EAACE,CAAC,CAACJ,CAAC,CAAC,CAAC,GAACE,CAAC;AAAA;AAAC,SAAS8B,CAACA,CAAChC,CAAC,EAACI,CAAC,EAAC;EAAC,MAAMC,CAAC,GAACL,CAAC,GAACA,CAAC,CAACQ,IAAI,CAAC,CAAC,GAAC,EAAE;EAAC,IAAG,EAAE,KAAGH,CAAC,IAAE,CAACA,CAAC,CAACyC,QAAQ,CAAC1C,CAAC,CAAC,EAAC,OAAO,CAAC;EAAC,MAAMK,CAAC,GAACJ,CAAC,CAACa,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,GAACd,CAAC,CAAC+B,MAAM,CAAC;EAAC,OAAOjC,CAAC,CAACO,CAAC,CAAC,GAAC,CAACA,CAAC,GAAC,CAAC;AAAA;AAAC,SAASsC,CAACA,CAAC/C,CAAC,EAAC;EAAC,OAAOA,CAAC,GAACgC,CAAC,CAAChC,CAAC,CAACQ,IAAI,CAAC,CAAC,EAAC,IAAI,CAAC,GAAC,CAAC;AAAA;AAAC,SAASwC,CAACA,CAAChD,CAAC,EAACE,CAAC,EAACG,CAAC,EAAC;EAAC,OAAOL,CAAC,CAACY,OAAO,CAAC,YAAY,EAAEZ,CAAC,IAAE;IAAC,MAAMS,CAAC,GAACT,CAAC,CAACkC,MAAM,CAAC,CAAC,EAAClC,CAAC,CAACmC,MAAM,GAAC,CAAC,CAAC,CAAC3B,IAAI,CAAC,CAAC;IAAC,OAAOJ,CAAC,CAACK,CAAC,EAACP,CAAC,EAACG,CAAC,CAAC;EAAA,CAAE,CAAC;AAAA;AAAC,SAAOM,CAAC,IAAIsC,oBAAoB,EAAC7B,CAAC,IAAI8B,qBAAqB,EAACN,CAAC,IAAIO,mCAAmC,EAACtB,CAAC,IAAIuB,QAAQ,EAACrB,CAAC,IAAIsB,uCAAuC,EAACL,CAAC,IAAIM,kBAAkB,EAAClB,CAAC,IAAImB,YAAY,EAACtB,CAAC,IAAIuB,sBAAsB,EAACnB,CAAC,IAAIoB,YAAY,EAAC3C,CAAC,IAAI4C,oBAAoB,EAACvC,CAAC,IAAIwC,qBAAqB,EAACZ,CAAC,IAAIa,eAAe,EAAC5B,CAAC,IAAI6B,wBAAwB,EAAC/B,CAAC,IAAIgC,eAAe,EAACvB,CAAC,IAAIwB,YAAY,EAACzB,CAAC,IAAI0B,oBAAoB,EAACxB,CAAC,IAAIyB,YAAY,EAACxB,CAAC,IAAIyB,WAAW,EAACzC,CAAC,IAAI0C,aAAa,EAAC9C,CAAC,IAAI+C,SAAS,EAACzB,CAAC,IAAI0B,4BAA4B,EAAC5D,CAAC,IAAI6D,uBAAuB,EAACjE,CAAC,IAAIkE,iBAAiB,EAAC7D,CAAC,IAAI8D,yBAAyB,EAAC9B,CAAC,IAAI+B,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}