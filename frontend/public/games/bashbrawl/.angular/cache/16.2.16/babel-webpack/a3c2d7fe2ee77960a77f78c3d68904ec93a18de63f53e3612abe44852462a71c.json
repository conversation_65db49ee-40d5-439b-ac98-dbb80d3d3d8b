{"ast": null, "code": "import _arrayFromIterator from \"./_arrayFromIterator.js\";\nimport _includesWith from \"./_includesWith.js\";\nimport _functionName from \"./_functionName.js\";\nimport _has from \"./_has.js\";\nimport _objectIs from \"./_objectIs.js\";\nimport keys from \"../keys.js\";\nimport type from \"../type.js\";\n/**\n * private _uniqContentEquals function.\n * That function is checking equality of 2 iterator contents with 2 assumptions\n * - iterators lengths are the same\n * - iterators values are unique\n *\n * false-positive result will be returned for comparison of, e.g.\n * - [1,2,3] and [1,2,3,4]\n * - [1,1,1] and [1,2,3]\n * */\n\nfunction _uniqContentEquals(aIterator, bIterator, stackA, stackB) {\n  var a = _arrayFromIterator(aIterator);\n  var b = _arrayFromIterator(bIterator);\n  function eq(_a, _b) {\n    return _equals(_a, _b, stackA.slice(), stackB.slice());\n  } // if *a* array contains any element that is not included in *b*\n\n  return !_includesWith(function (b, aItem) {\n    return !_includesWith(eq, aItem, b);\n  }, b, a);\n}\nexport default function _equals(a, b, stackA, stackB) {\n  if (_objectIs(a, b)) {\n    return true;\n  }\n  var typeA = type(a);\n  if (typeA !== type(b)) {\n    return false;\n  }\n  if (typeof a['fantasy-land/equals'] === 'function' || typeof b['fantasy-land/equals'] === 'function') {\n    return typeof a['fantasy-land/equals'] === 'function' && a['fantasy-land/equals'](b) && typeof b['fantasy-land/equals'] === 'function' && b['fantasy-land/equals'](a);\n  }\n  if (typeof a.equals === 'function' || typeof b.equals === 'function') {\n    return typeof a.equals === 'function' && a.equals(b) && typeof b.equals === 'function' && b.equals(a);\n  }\n  switch (typeA) {\n    case 'Arguments':\n    case 'Array':\n    case 'Object':\n      if (typeof a.constructor === 'function' && _functionName(a.constructor) === 'Promise') {\n        return a === b;\n      }\n      break;\n    case 'Boolean':\n    case 'Number':\n    case 'String':\n      if (!(typeof a === typeof b && _objectIs(a.valueOf(), b.valueOf()))) {\n        return false;\n      }\n      break;\n    case 'Date':\n      if (!_objectIs(a.valueOf(), b.valueOf())) {\n        return false;\n      }\n      break;\n    case 'Error':\n      return a.name === b.name && a.message === b.message;\n    case 'RegExp':\n      if (!(a.source === b.source && a.global === b.global && a.ignoreCase === b.ignoreCase && a.multiline === b.multiline && a.sticky === b.sticky && a.unicode === b.unicode)) {\n        return false;\n      }\n      break;\n  }\n  var idx = stackA.length - 1;\n  while (idx >= 0) {\n    if (stackA[idx] === a) {\n      return stackB[idx] === b;\n    }\n    idx -= 1;\n  }\n  switch (typeA) {\n    case 'Map':\n      if (a.size !== b.size) {\n        return false;\n      }\n      return _uniqContentEquals(a.entries(), b.entries(), stackA.concat([a]), stackB.concat([b]));\n    case 'Set':\n      if (a.size !== b.size) {\n        return false;\n      }\n      return _uniqContentEquals(a.values(), b.values(), stackA.concat([a]), stackB.concat([b]));\n    case 'Arguments':\n    case 'Array':\n    case 'Object':\n    case 'Boolean':\n    case 'Number':\n    case 'String':\n    case 'Date':\n    case 'Error':\n    case 'RegExp':\n    case 'Int8Array':\n    case 'Uint8Array':\n    case 'Uint8ClampedArray':\n    case 'Int16Array':\n    case 'Uint16Array':\n    case 'Int32Array':\n    case 'Uint32Array':\n    case 'Float32Array':\n    case 'Float64Array':\n    case 'ArrayBuffer':\n      break;\n    default:\n      // Values of other types are only equal if identical.\n      return false;\n  }\n  var keysA = keys(a);\n  if (keysA.length !== keys(b).length) {\n    return false;\n  }\n  var extendedStackA = stackA.concat([a]);\n  var extendedStackB = stackB.concat([b]);\n  idx = keysA.length - 1;\n  while (idx >= 0) {\n    var key = keysA[idx];\n    if (!(_has(key, b) && _equals(b[key], a[key], extendedStackA, extendedStackB))) {\n      return false;\n    }\n    idx -= 1;\n  }\n  return true;\n}", "map": {"version": 3, "names": ["_arrayFromIterator", "_includesWith", "_functionName", "_has", "_objectIs", "keys", "type", "_uniqContentEquals", "aIterator", "bIterator", "stackA", "stackB", "a", "b", "eq", "_a", "_b", "_equals", "slice", "aItem", "typeA", "equals", "constructor", "valueOf", "name", "message", "source", "global", "ignoreCase", "multiline", "sticky", "unicode", "idx", "length", "size", "entries", "concat", "values", "keysA", "extendedStackA", "extendedStackB", "key"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_equals.js"], "sourcesContent": ["import _arrayFromIterator from \"./_arrayFromIterator.js\";\nimport _includesWith from \"./_includesWith.js\";\nimport _functionName from \"./_functionName.js\";\nimport _has from \"./_has.js\";\nimport _objectIs from \"./_objectIs.js\";\nimport keys from \"../keys.js\";\nimport type from \"../type.js\";\n/**\n * private _uniqContentEquals function.\n * That function is checking equality of 2 iterator contents with 2 assumptions\n * - iterators lengths are the same\n * - iterators values are unique\n *\n * false-positive result will be returned for comparison of, e.g.\n * - [1,2,3] and [1,2,3,4]\n * - [1,1,1] and [1,2,3]\n * */\n\nfunction _uniqContentEquals(aIterator, bIterator, stackA, stackB) {\n  var a = _arrayFromIterator(aIterator);\n\n  var b = _arrayFromIterator(bIterator);\n\n  function eq(_a, _b) {\n    return _equals(_a, _b, stackA.slice(), stackB.slice());\n  } // if *a* array contains any element that is not included in *b*\n\n\n  return !_includesWith(function (b, aItem) {\n    return !_includesWith(eq, aItem, b);\n  }, b, a);\n}\n\nexport default function _equals(a, b, stackA, stackB) {\n  if (_objectIs(a, b)) {\n    return true;\n  }\n\n  var typeA = type(a);\n\n  if (typeA !== type(b)) {\n    return false;\n  }\n\n  if (typeof a['fantasy-land/equals'] === 'function' || typeof b['fantasy-land/equals'] === 'function') {\n    return typeof a['fantasy-land/equals'] === 'function' && a['fantasy-land/equals'](b) && typeof b['fantasy-land/equals'] === 'function' && b['fantasy-land/equals'](a);\n  }\n\n  if (typeof a.equals === 'function' || typeof b.equals === 'function') {\n    return typeof a.equals === 'function' && a.equals(b) && typeof b.equals === 'function' && b.equals(a);\n  }\n\n  switch (typeA) {\n    case 'Arguments':\n    case 'Array':\n    case 'Object':\n      if (typeof a.constructor === 'function' && _functionName(a.constructor) === 'Promise') {\n        return a === b;\n      }\n\n      break;\n\n    case 'Boolean':\n    case 'Number':\n    case 'String':\n      if (!(typeof a === typeof b && _objectIs(a.valueOf(), b.valueOf()))) {\n        return false;\n      }\n\n      break;\n\n    case 'Date':\n      if (!_objectIs(a.valueOf(), b.valueOf())) {\n        return false;\n      }\n\n      break;\n\n    case 'Error':\n      return a.name === b.name && a.message === b.message;\n\n    case 'RegExp':\n      if (!(a.source === b.source && a.global === b.global && a.ignoreCase === b.ignoreCase && a.multiline === b.multiline && a.sticky === b.sticky && a.unicode === b.unicode)) {\n        return false;\n      }\n\n      break;\n  }\n\n  var idx = stackA.length - 1;\n\n  while (idx >= 0) {\n    if (stackA[idx] === a) {\n      return stackB[idx] === b;\n    }\n\n    idx -= 1;\n  }\n\n  switch (typeA) {\n    case 'Map':\n      if (a.size !== b.size) {\n        return false;\n      }\n\n      return _uniqContentEquals(a.entries(), b.entries(), stackA.concat([a]), stackB.concat([b]));\n\n    case 'Set':\n      if (a.size !== b.size) {\n        return false;\n      }\n\n      return _uniqContentEquals(a.values(), b.values(), stackA.concat([a]), stackB.concat([b]));\n\n    case 'Arguments':\n    case 'Array':\n    case 'Object':\n    case 'Boolean':\n    case 'Number':\n    case 'String':\n    case 'Date':\n    case 'Error':\n    case 'RegExp':\n    case 'Int8Array':\n    case 'Uint8Array':\n    case 'Uint8ClampedArray':\n    case 'Int16Array':\n    case 'Uint16Array':\n    case 'Int32Array':\n    case 'Uint32Array':\n    case 'Float32Array':\n    case 'Float64Array':\n    case 'ArrayBuffer':\n      break;\n\n    default:\n      // Values of other types are only equal if identical.\n      return false;\n  }\n\n  var keysA = keys(a);\n\n  if (keysA.length !== keys(b).length) {\n    return false;\n  }\n\n  var extendedStackA = stackA.concat([a]);\n  var extendedStackB = stackB.concat([b]);\n  idx = keysA.length - 1;\n\n  while (idx >= 0) {\n    var key = keysA[idx];\n\n    if (!(_has(key, b) && _equals(b[key], a[key], extendedStackA, extendedStackB))) {\n      return false;\n    }\n\n    idx -= 1;\n  }\n\n  return true;\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,IAAI,MAAM,YAAY;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,kBAAkBA,CAACC,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,MAAM,EAAE;EAChE,IAAIC,CAAC,GAAGZ,kBAAkB,CAACQ,SAAS,CAAC;EAErC,IAAIK,CAAC,GAAGb,kBAAkB,CAACS,SAAS,CAAC;EAErC,SAASK,EAAEA,CAACC,EAAE,EAAEC,EAAE,EAAE;IAClB,OAAOC,OAAO,CAACF,EAAE,EAAEC,EAAE,EAAEN,MAAM,CAACQ,KAAK,CAAC,CAAC,EAAEP,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC;EACxD,CAAC,CAAC;;EAGF,OAAO,CAACjB,aAAa,CAAC,UAAUY,CAAC,EAAEM,KAAK,EAAE;IACxC,OAAO,CAAClB,aAAa,CAACa,EAAE,EAAEK,KAAK,EAAEN,CAAC,CAAC;EACrC,CAAC,EAAEA,CAAC,EAAED,CAAC,CAAC;AACV;AAEA,eAAe,SAASK,OAAOA,CAACL,CAAC,EAAEC,CAAC,EAAEH,MAAM,EAAEC,MAAM,EAAE;EACpD,IAAIP,SAAS,CAACQ,CAAC,EAAEC,CAAC,CAAC,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,IAAIO,KAAK,GAAGd,IAAI,CAACM,CAAC,CAAC;EAEnB,IAAIQ,KAAK,KAAKd,IAAI,CAACO,CAAC,CAAC,EAAE;IACrB,OAAO,KAAK;EACd;EAEA,IAAI,OAAOD,CAAC,CAAC,qBAAqB,CAAC,KAAK,UAAU,IAAI,OAAOC,CAAC,CAAC,qBAAqB,CAAC,KAAK,UAAU,EAAE;IACpG,OAAO,OAAOD,CAAC,CAAC,qBAAqB,CAAC,KAAK,UAAU,IAAIA,CAAC,CAAC,qBAAqB,CAAC,CAACC,CAAC,CAAC,IAAI,OAAOA,CAAC,CAAC,qBAAqB,CAAC,KAAK,UAAU,IAAIA,CAAC,CAAC,qBAAqB,CAAC,CAACD,CAAC,CAAC;EACvK;EAEA,IAAI,OAAOA,CAAC,CAACS,MAAM,KAAK,UAAU,IAAI,OAAOR,CAAC,CAACQ,MAAM,KAAK,UAAU,EAAE;IACpE,OAAO,OAAOT,CAAC,CAACS,MAAM,KAAK,UAAU,IAAIT,CAAC,CAACS,MAAM,CAACR,CAAC,CAAC,IAAI,OAAOA,CAAC,CAACQ,MAAM,KAAK,UAAU,IAAIR,CAAC,CAACQ,MAAM,CAACT,CAAC,CAAC;EACvG;EAEA,QAAQQ,KAAK;IACX,KAAK,WAAW;IAChB,KAAK,OAAO;IACZ,KAAK,QAAQ;MACX,IAAI,OAAOR,CAAC,CAACU,WAAW,KAAK,UAAU,IAAIpB,aAAa,CAACU,CAAC,CAACU,WAAW,CAAC,KAAK,SAAS,EAAE;QACrF,OAAOV,CAAC,KAAKC,CAAC;MAChB;MAEA;IAEF,KAAK,SAAS;IACd,KAAK,QAAQ;IACb,KAAK,QAAQ;MACX,IAAI,EAAE,OAAOD,CAAC,KAAK,OAAOC,CAAC,IAAIT,SAAS,CAACQ,CAAC,CAACW,OAAO,CAAC,CAAC,EAAEV,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;QACnE,OAAO,KAAK;MACd;MAEA;IAEF,KAAK,MAAM;MACT,IAAI,CAACnB,SAAS,CAACQ,CAAC,CAACW,OAAO,CAAC,CAAC,EAAEV,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,EAAE;QACxC,OAAO,KAAK;MACd;MAEA;IAEF,KAAK,OAAO;MACV,OAAOX,CAAC,CAACY,IAAI,KAAKX,CAAC,CAACW,IAAI,IAAIZ,CAAC,CAACa,OAAO,KAAKZ,CAAC,CAACY,OAAO;IAErD,KAAK,QAAQ;MACX,IAAI,EAAEb,CAAC,CAACc,MAAM,KAAKb,CAAC,CAACa,MAAM,IAAId,CAAC,CAACe,MAAM,KAAKd,CAAC,CAACc,MAAM,IAAIf,CAAC,CAACgB,UAAU,KAAKf,CAAC,CAACe,UAAU,IAAIhB,CAAC,CAACiB,SAAS,KAAKhB,CAAC,CAACgB,SAAS,IAAIjB,CAAC,CAACkB,MAAM,KAAKjB,CAAC,CAACiB,MAAM,IAAIlB,CAAC,CAACmB,OAAO,KAAKlB,CAAC,CAACkB,OAAO,CAAC,EAAE;QACzK,OAAO,KAAK;MACd;MAEA;EACJ;EAEA,IAAIC,GAAG,GAAGtB,MAAM,CAACuB,MAAM,GAAG,CAAC;EAE3B,OAAOD,GAAG,IAAI,CAAC,EAAE;IACf,IAAItB,MAAM,CAACsB,GAAG,CAAC,KAAKpB,CAAC,EAAE;MACrB,OAAOD,MAAM,CAACqB,GAAG,CAAC,KAAKnB,CAAC;IAC1B;IAEAmB,GAAG,IAAI,CAAC;EACV;EAEA,QAAQZ,KAAK;IACX,KAAK,KAAK;MACR,IAAIR,CAAC,CAACsB,IAAI,KAAKrB,CAAC,CAACqB,IAAI,EAAE;QACrB,OAAO,KAAK;MACd;MAEA,OAAO3B,kBAAkB,CAACK,CAAC,CAACuB,OAAO,CAAC,CAAC,EAAEtB,CAAC,CAACsB,OAAO,CAAC,CAAC,EAAEzB,MAAM,CAAC0B,MAAM,CAAC,CAACxB,CAAC,CAAC,CAAC,EAAED,MAAM,CAACyB,MAAM,CAAC,CAACvB,CAAC,CAAC,CAAC,CAAC;IAE7F,KAAK,KAAK;MACR,IAAID,CAAC,CAACsB,IAAI,KAAKrB,CAAC,CAACqB,IAAI,EAAE;QACrB,OAAO,KAAK;MACd;MAEA,OAAO3B,kBAAkB,CAACK,CAAC,CAACyB,MAAM,CAAC,CAAC,EAAExB,CAAC,CAACwB,MAAM,CAAC,CAAC,EAAE3B,MAAM,CAAC0B,MAAM,CAAC,CAACxB,CAAC,CAAC,CAAC,EAAED,MAAM,CAACyB,MAAM,CAAC,CAACvB,CAAC,CAAC,CAAC,CAAC;IAE3F,KAAK,WAAW;IAChB,KAAK,OAAO;IACZ,KAAK,QAAQ;IACb,KAAK,SAAS;IACd,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,MAAM;IACX,KAAK,OAAO;IACZ,KAAK,QAAQ;IACb,KAAK,WAAW;IAChB,KAAK,YAAY;IACjB,KAAK,mBAAmB;IACxB,KAAK,YAAY;IACjB,KAAK,aAAa;IAClB,KAAK,YAAY;IACjB,KAAK,aAAa;IAClB,KAAK,cAAc;IACnB,KAAK,cAAc;IACnB,KAAK,aAAa;MAChB;IAEF;MACE;MACA,OAAO,KAAK;EAChB;EAEA,IAAIyB,KAAK,GAAGjC,IAAI,CAACO,CAAC,CAAC;EAEnB,IAAI0B,KAAK,CAACL,MAAM,KAAK5B,IAAI,CAACQ,CAAC,CAAC,CAACoB,MAAM,EAAE;IACnC,OAAO,KAAK;EACd;EAEA,IAAIM,cAAc,GAAG7B,MAAM,CAAC0B,MAAM,CAAC,CAACxB,CAAC,CAAC,CAAC;EACvC,IAAI4B,cAAc,GAAG7B,MAAM,CAACyB,MAAM,CAAC,CAACvB,CAAC,CAAC,CAAC;EACvCmB,GAAG,GAAGM,KAAK,CAACL,MAAM,GAAG,CAAC;EAEtB,OAAOD,GAAG,IAAI,CAAC,EAAE;IACf,IAAIS,GAAG,GAAGH,KAAK,CAACN,GAAG,CAAC;IAEpB,IAAI,EAAE7B,IAAI,CAACsC,GAAG,EAAE5B,CAAC,CAAC,IAAII,OAAO,CAACJ,CAAC,CAAC4B,GAAG,CAAC,EAAE7B,CAAC,CAAC6B,GAAG,CAAC,EAAEF,cAAc,EAAEC,cAAc,CAAC,CAAC,EAAE;MAC9E,OAAO,KAAK;IACd;IAEAR,GAAG,IAAI,CAAC;EACV;EAEA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}