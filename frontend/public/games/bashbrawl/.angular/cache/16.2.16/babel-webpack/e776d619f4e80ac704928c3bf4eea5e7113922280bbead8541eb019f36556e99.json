{"ast": null, "code": "import { ClarityIcons as s } from \"../icon.service.js\";\nimport { airplaneIcon as o, airplaneIconName as r } from \"../shapes/airplane.js\";\nimport { bicycleIcon as p } from \"../shapes/bicycle.js\";\nimport { boatIcon as m } from \"../shapes/boat.js\";\nimport { canoeIcon as a } from \"../shapes/canoe.js\";\nimport { campervanIcon as e } from \"../shapes/campervan.js\";\nimport { carIcon as t } from \"../shapes/car.js\";\nimport { caravanIcon as i, caravanIconName as f } from \"../shapes/caravan.js\";\nimport { castleIcon as h } from \"../shapes/castle.js\";\nimport { compassIcon as j } from \"../shapes/compass.js\";\nimport { ferryIcon as c } from \"../shapes/ferry.js\";\nimport { gymIcon as n } from \"../shapes/gym.js\";\nimport { hotelIcon as l } from \"../shapes/hotel.js\";\nimport { mapMarkerIcon as d } from \"../shapes/map-marker.js\";\nimport { mapIcon as k } from \"../shapes/map.js\";\nimport { noSmokingIcon as u } from \"../shapes/no-smoking.js\";\nimport { onHolidayIcon as y } from \"../shapes/on-holiday.js\";\nimport { palmTreeIcon as g } from \"../shapes/palm-tree.js\";\nimport { passportIcon as v } from \"../shapes/passport.js\";\nimport { planeTicketIcon as b } from \"../shapes/plane-ticket.js\";\nimport { poolIcon as x } from \"../shapes/pool.js\";\nimport { smokingIcon as A } from \"../shapes/smoking.js\";\nimport { suitcaseIcon as I } from \"../shapes/suitcase.js\";\nimport { suitcase2Icon as q } from \"../shapes/suitcase-2.js\";\nimport { tentIcon as w } from \"../shapes/tent.js\";\nimport { trailerIcon as z } from \"../shapes/trailer.js\";\nimport { trainIcon as B } from \"../shapes/train.js\";\nimport { truckIcon as C } from \"../shapes/truck.js\";\nconst D = [o, p, m, t, e, a, i, h, j, c, n, l, k, d, u, y, g, v, b, x, A, I, q, w, z, B, C],\n  E = [[r, [\"plane\"]], [f, [\"auto\"]]];\nfunction F() {\n  s.addIcons(...D), s.addAliases(...E);\n}\nexport { F as loadTravelIconSet, E as travelCollectionAliases, D as travelCollectionIcons };", "map": {"version": 3, "names": ["ClarityIcons", "s", "airplaneIcon", "o", "airplaneIconName", "r", "bicycleIcon", "p", "boatIcon", "m", "canoeIcon", "a", "campervanIcon", "e", "carIcon", "t", "caravanIcon", "i", "caravanIconName", "f", "castleIcon", "h", "compassIcon", "j", "ferryIcon", "c", "gymIcon", "n", "hotelIcon", "l", "mapMarkerIcon", "d", "mapIcon", "k", "noSmokingIcon", "u", "onHolidayIcon", "y", "palmTreeIcon", "g", "passportIcon", "v", "planeTicketIcon", "b", "poolIcon", "x", "smokingIcon", "A", "suitcaseIcon", "I", "suitcase2Icon", "q", "tentIcon", "w", "trailerIcon", "z", "trainIcon", "B", "truckIcon", "C", "D", "E", "F", "addIcons", "addAliases", "loadTravelIconSet", "travelCollectionAliases", "travelCollectionIcons"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/collections/travel.js"], "sourcesContent": ["import{ClarityIcons as s}from\"../icon.service.js\";import{airplaneIcon as o,airplaneIconName as r}from\"../shapes/airplane.js\";import{bicycleIcon as p}from\"../shapes/bicycle.js\";import{boatIcon as m}from\"../shapes/boat.js\";import{canoeIcon as a}from\"../shapes/canoe.js\";import{campervanIcon as e}from\"../shapes/campervan.js\";import{carIcon as t}from\"../shapes/car.js\";import{caravanIcon as i,caravanIconName as f}from\"../shapes/caravan.js\";import{castleIcon as h}from\"../shapes/castle.js\";import{compassIcon as j}from\"../shapes/compass.js\";import{ferryIcon as c}from\"../shapes/ferry.js\";import{gymIcon as n}from\"../shapes/gym.js\";import{hotelIcon as l}from\"../shapes/hotel.js\";import{mapMarkerIcon as d}from\"../shapes/map-marker.js\";import{mapIcon as k}from\"../shapes/map.js\";import{noSmokingIcon as u}from\"../shapes/no-smoking.js\";import{onHolidayIcon as y}from\"../shapes/on-holiday.js\";import{palmTreeIcon as g}from\"../shapes/palm-tree.js\";import{passportIcon as v}from\"../shapes/passport.js\";import{planeTicketIcon as b}from\"../shapes/plane-ticket.js\";import{poolIcon as x}from\"../shapes/pool.js\";import{smokingIcon as A}from\"../shapes/smoking.js\";import{suitcaseIcon as I}from\"../shapes/suitcase.js\";import{suitcase2Icon as q}from\"../shapes/suitcase-2.js\";import{tentIcon as w}from\"../shapes/tent.js\";import{trailerIcon as z}from\"../shapes/trailer.js\";import{trainIcon as B}from\"../shapes/train.js\";import{truckIcon as C}from\"../shapes/truck.js\";const D=[o,p,m,t,e,a,i,h,j,c,n,l,k,d,u,y,g,v,b,x,A,I,q,w,z,B,C],E=[[r,[\"plane\"]],[f,[\"auto\"]]];function F(){s.addIcons(...D),s.addAliases(...E)}export{F as loadTravelIconSet,E as travelCollectionAliases,D as travelCollectionIcons};\n"], "mappings": "AAAA,SAAOA,YAAY,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,YAAY,IAAIC,CAAC,EAACC,gBAAgB,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,wBAAwB;AAAC,SAAOC,OAAO,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAAOC,WAAW,IAAIC,CAAC,EAACC,eAAe,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,OAAO,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,OAAO,IAAIC,CAAC,QAAK,kBAAkB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,wBAAwB;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,2BAA2B;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,oBAAoB;AAAC,MAAMC,CAAC,GAAC,CAACzD,CAAC,EAACI,CAAC,EAACE,CAAC,EAACM,CAAC,EAACF,CAAC,EAACF,CAAC,EAACM,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACI,CAAC,EAACF,CAAC,EAACI,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,CAAC;EAACE,CAAC,GAAC,CAAC,CAACxD,CAAC,EAAC,CAAC,OAAO,CAAC,CAAC,EAAC,CAACc,CAAC,EAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AAAC,SAAS2C,CAACA,CAAA,EAAE;EAAC7D,CAAC,CAAC8D,QAAQ,CAAC,GAAGH,CAAC,CAAC,EAAC3D,CAAC,CAAC+D,UAAU,CAAC,GAAGH,CAAC,CAAC;AAAA;AAAC,SAAOC,CAAC,IAAIG,iBAAiB,EAACJ,CAAC,IAAIK,uBAAuB,EAACN,CAAC,IAAIO,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}