{"ast": null, "code": "import { renderIcon as e } from \"../icon.renderer.js\";\nconst o = \"mobile\",\n  t = [\"mobile\", e({\n    outline: '<path d=\"M25,4H11A2,2,0,0,0,9,6V30a2,2,0,0,0,2,2H25a2,2,0,0,0,2-2V6A2,2,0,0,0,25,4ZM11,6H25V24H11Zm0,24V26H25v4Z\"/><rect x=\"17\" y=\"27\" width=\"2\" height=\"2\"/>',\n    solid: '<path d=\"M25,4H11A2,2,0,0,0,9,6V30a2,2,0,0,0,2,2H25a2,2,0,0,0,2-2V6A2,2,0,0,0,25,4ZM19,30H17V28h2Zm-8-4V6H25V26Z\"/>'\n  })];\nexport { t as mobileIcon, o as mobileIconName };", "map": {"version": 3, "names": ["renderIcon", "e", "o", "t", "outline", "solid", "mobileIcon", "mobileIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/mobile.js"], "sourcesContent": ["import{renderIcon as e}from\"../icon.renderer.js\";const o=\"mobile\",t=[\"mobile\",e({outline:'<path d=\"M25,4H11A2,2,0,0,0,9,6V30a2,2,0,0,0,2,2H25a2,2,0,0,0,2-2V6A2,2,0,0,0,25,4ZM11,6H25V24H11Zm0,24V26H25v4Z\"/><rect x=\"17\" y=\"27\" width=\"2\" height=\"2\"/>',solid:'<path d=\"M25,4H11A2,2,0,0,0,9,6V30a2,2,0,0,0,2,2H25a2,2,0,0,0,2-2V6A2,2,0,0,0,25,4ZM19,30H17V28h2Zm-8-4V6H25V26Z\"/>'})];export{t as mobileIcon,o as mobileIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,QAAQ;EAACC,CAAC,GAAC,CAAC,QAAQ,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,+JAA+J;IAACC,KAAK,EAAC;EAAqH,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,UAAU,EAACJ,CAAC,IAAIK,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}