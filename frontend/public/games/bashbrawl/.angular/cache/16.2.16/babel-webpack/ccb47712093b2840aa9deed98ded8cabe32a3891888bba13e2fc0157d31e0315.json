{"ast": null, "code": "import { renderIcon as V } from \"../icon.renderer.js\";\nconst a = \"devices\",\n  H = [\"devices\", V({\n    outline: '<path d=\"M32,13H24a2,2,0,0,0-2,2V30a2,2,0,0,0,2,2h8a2,2,0,0,0,2-2V15A2,2,0,0,0,32,13Zm0,2V26H24V15ZM24,30V27.6h8V30Z\"/><path d=\"M20,22H4V6H28v5h2V6a2,2,0,0,0-2-2H4A2,2,0,0,0,2,6V22a2,2,0,0,0,2,2H20Z\"/><path d=\"M20,26H9a1,1,0,0,0,0,2H20Z\"/>',\n    solid: '<path d=\"M32,13H24a2,2,0,0,0-2,2V30a2,2,0,0,0,2,2h8a2,2,0,0,0,2-2V15A2,2,0,0,0,32,13Zm0,2V28H24V15Z\"/><path d=\"M28,4H4A2,2,0,0,0,2,6V22a2,2,0,0,0,2,2h8v2H9.32A1.2,1.2,0,0,0,8,27a1.2,1.2,0,0,0,1.32,1H19.92v-.37H20V22H4V6H28v5h2V6A2,2,0,0,0,28,4Z\"/>'\n  })];\nexport { H as devicesIcon, a as devicesIconName };", "map": {"version": 3, "names": ["renderIcon", "V", "a", "H", "outline", "solid", "devicesIcon", "devicesIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/devices.js"], "sourcesContent": ["import{renderIcon as V}from\"../icon.renderer.js\";const a=\"devices\",H=[\"devices\",V({outline:'<path d=\"M32,13H24a2,2,0,0,0-2,2V30a2,2,0,0,0,2,2h8a2,2,0,0,0,2-2V15A2,2,0,0,0,32,13Zm0,2V26H24V15ZM24,30V27.6h8V30Z\"/><path d=\"M20,22H4V6H28v5h2V6a2,2,0,0,0-2-2H4A2,2,0,0,0,2,6V22a2,2,0,0,0,2,2H20Z\"/><path d=\"M20,26H9a1,1,0,0,0,0,2H20Z\"/>',solid:'<path d=\"M32,13H24a2,2,0,0,0-2,2V30a2,2,0,0,0,2,2h8a2,2,0,0,0,2-2V15A2,2,0,0,0,32,13Zm0,2V28H24V15Z\"/><path d=\"M28,4H4A2,2,0,0,0,2,6V22a2,2,0,0,0,2,2h8v2H9.32A1.2,1.2,0,0,0,8,27a1.2,1.2,0,0,0,1.32,1H19.92v-.37H20V22H4V6H28v5h2V6A2,2,0,0,0,28,4Z\"/>'})];export{H as devicesIcon,a as devicesIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,SAAS;EAACC,CAAC,GAAC,CAAC,SAAS,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,iPAAiP;IAACC,KAAK,EAAC;EAAyP,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,WAAW,EAACJ,CAAC,IAAIK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}