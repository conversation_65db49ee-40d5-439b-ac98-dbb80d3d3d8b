{"ast": null, "code": "import { renderIcon as H } from \"../icon.renderer.js\";\nconst V = \"paste\",\n  d = [\"paste\", H({\n    outline: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24 22V6C24 4.89543 23.1046 4 22 4H6C4.89543 4 4 4.89543 4 6V22C4 23.1046 4.89543 24 6 24H22C23.1046 24 24 23.1046 24 22ZM6 6H22V22H6V6Z\"/><path d=\"M12 26H14V30H18V32H14C12.8954 32 12 31.1046 12 30V26Z\"/><path d=\"M20 30H26V32H20V30Z\"/><path d=\"M28 30H30V26H32V30C32 31.1046 31.1046 32 30 32H28V30Z\"/><path d=\"M30 18H32V24H30V18Z\"/><path d=\"M26 12H30C31.1046 12 32 12.8954 32 14V16H30V14H26V12Z\"/>',\n    solid: '<path d=\"M4 6C4 4.89543 4.89543 4 6 4H22C23.1046 4 24 4.89543 24 6V22C24 23.1046 23.1046 24 22 24H6C4.89543 24 4 23.1046 4 22V6Z\"/><path d=\"M12 26H14V30H18V32H14C12.8954 32 12 31.1046 12 30V26Z\"/><path d=\"M26 30H20V32H26V30Z\"/><path d=\"M26 12H30C31.1046 12 32 12.8954 32 14V16H30V14H26V12Z\"/><path d=\"M30 18H32V24H30V18Z\"/><path d=\"M28 30H30V26H32V30C32 31.1046 31.1046 32 30 32H28V30Z\"/>'\n  })];\nexport { d as pasteIcon, V as pasteIconName };", "map": {"version": 3, "names": ["renderIcon", "H", "V", "d", "outline", "solid", "pasteIcon", "pasteIconName"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/icon/shapes/paste.js"], "sourcesContent": ["import{renderIcon as H}from\"../icon.renderer.js\";const V=\"paste\",d=[\"paste\",H({outline:'<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M24 22V6C24 4.89543 23.1046 4 22 4H6C4.89543 4 4 4.89543 4 6V22C4 23.1046 4.89543 24 6 24H22C23.1046 24 24 23.1046 24 22ZM6 6H22V22H6V6Z\"/><path d=\"M12 26H14V30H18V32H14C12.8954 32 12 31.1046 12 30V26Z\"/><path d=\"M20 30H26V32H20V30Z\"/><path d=\"M28 30H30V26H32V30C32 31.1046 31.1046 32 30 32H28V30Z\"/><path d=\"M30 18H32V24H30V18Z\"/><path d=\"M26 12H30C31.1046 12 32 12.8954 32 14V16H30V14H26V12Z\"/>',solid:'<path d=\"M4 6C4 4.89543 4.89543 4 6 4H22C23.1046 4 24 4.89543 24 6V22C24 23.1046 23.1046 24 22 24H6C4.89543 24 4 23.1046 4 22V6Z\"/><path d=\"M12 26H14V30H18V32H14C12.8954 32 12 31.1046 12 30V26Z\"/><path d=\"M26 30H20V32H26V30Z\"/><path d=\"M26 12H30C31.1046 12 32 12.8954 32 14V16H30V14H26V12Z\"/><path d=\"M30 18H32V24H30V18Z\"/><path d=\"M28 30H30V26H32V30C32 31.1046 31.1046 32 30 32H28V30Z\"/>'})];export{d as pasteIcon,V as pasteIconName};\n"], "mappings": "AAAA,SAAOA,UAAU,IAAIC,CAAC,QAAK,qBAAqB;AAAC,MAAMC,CAAC,GAAC,OAAO;EAACC,CAAC,GAAC,CAAC,OAAO,EAACF,CAAC,CAAC;IAACG,OAAO,EAAC,+bAA+b;IAACC,KAAK,EAAC;EAAsY,CAAC,CAAC,CAAC;AAAC,SAAOF,CAAC,IAAIG,SAAS,EAACJ,CAAC,IAAIK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}