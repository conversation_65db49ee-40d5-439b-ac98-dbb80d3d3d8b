{"ast": null, "code": "import _awaitAsyncGenerator from \"/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@babel/runtime/helpers/esm/awaitAsyncGenerator.js\";\nimport _wrapAsyncGenerator from \"/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@babel/runtime/helpers/esm/wrapAsyncGenerator.js\";\nfunction n(n, t) {\n  return n.reduce((n, e) => (n[e[t]] = e, n), {});\n}\nfunction t(n) {\n  return n.length ? n[n.length - 1] : void 0;\n}\nfunction e(n) {\n  return n.length ? n[0] : void 0;\n}\nfunction r(n, t) {\n  const e = t.indexOf(n);\n  return -1 === e ? void 0 : t[Math.max(e - 1, 0)];\n}\nfunction o(n, t) {\n  const e = t.indexOf(n);\n  return -1 === e ? void 0 : t[Math.min(e + 1, t.length - 1)];\n}\nfunction i(n, t) {\n  const e = Array.from(t),\n    r = t.indexOf(n);\n  return r > -1 && e.splice(r, 1), e;\n}\nfunction u(n, t) {\n  return i(n, Array.from(t).reverse()).reverse();\n}\nfunction c(n, t) {\n  const e = [].concat(n);\n  return t.filter(n => !e.includes(n));\n}\nfunction f(n, t) {\n  return [...n].reduce((n, e, r) => {\n    const o = Math.floor(r / t);\n    return (n[o] || (n[o] = [])).push(e), n;\n  }, []);\n}\nfunction s(_x) {\n  return _s.apply(this, arguments);\n}\nfunction _s() {\n  _s = _wrapAsyncGenerator(function* (n, t = 100) {\n    const e = f(n, t);\n    for (let n = 0; n < e.length; n++) yield e[n], yield _awaitAsyncGenerator(new Promise(n => setTimeout(n, 0)));\n  });\n  return _s.apply(this, arguments);\n}\nexport { e as arrayHead, c as arrayRemoveAllInstances, i as arrayRemoveFirstInstance, u as arrayRemoveLastInstance, t as arrayTail, n as arrayToObject, s as asyncGroupArray, f as groupArray, o as nextInArray, r as previousInArray };", "map": {"version": 3, "names": ["n", "t", "reduce", "e", "length", "r", "indexOf", "Math", "max", "o", "min", "i", "Array", "from", "splice", "u", "reverse", "c", "concat", "filter", "includes", "f", "floor", "push", "s", "_x", "_s", "apply", "arguments", "_wrapAsyncGenerator", "_awaitAsyncGenerator", "Promise", "setTimeout", "arrayHead", "arrayRemoveAllInstances", "arrayRemoveFirstInstance", "arrayRemoveLastInstance", "arrayTail", "arrayToObject", "asyncGroupArray", "groupArray", "nextInArray", "previousInArray"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/internal/utils/array.js"], "sourcesContent": ["function n(n,t){return n.reduce(((n,e)=>(n[e[t]]=e,n)),{})}function t(n){return n.length?n[n.length-1]:void 0}function e(n){return n.length?n[0]:void 0}function r(n,t){const e=t.indexOf(n);return-1===e?void 0:t[Math.max(e-1,0)]}function o(n,t){const e=t.indexOf(n);return-1===e?void 0:t[Math.min(e+1,t.length-1)]}function i(n,t){const e=Array.from(t),r=t.indexOf(n);return r>-1&&e.splice(r,1),e}function u(n,t){return i(n,Array.from(t).reverse()).reverse()}function c(n,t){const e=[].concat(n);return t.filter((n=>!e.includes(n)))}function f(n,t){return[...n].reduce(((n,e,r)=>{const o=Math.floor(r/t);return(n[o]||(n[o]=[])).push(e),n}),[])}async function*s(n,t=100){const e=f(n,t);for(let n=0;n<e.length;n++)yield e[n],await new Promise((n=>setTimeout(n,0)))}export{e as arrayHead,c as arrayRemoveAllInstances,i as arrayRemoveFirstInstance,u as arrayRemoveLastInstance,t as arrayTail,n as arrayToObject,s as asyncGroupArray,f as groupArray,o as nextInArray,r as previousInArray};\n"], "mappings": ";;AAAA,SAASA,CAACA,CAACA,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOD,CAAC,CAACE,MAAM,CAAE,CAACF,CAAC,EAACG,CAAC,MAAIH,CAAC,CAACG,CAAC,CAACF,CAAC,CAAC,CAAC,GAACE,CAAC,EAACH,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA;AAAC,SAASC,CAACA,CAACD,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACI,MAAM,GAACJ,CAAC,CAACA,CAAC,CAACI,MAAM,GAAC,CAAC,CAAC,GAAC,KAAK,CAAC;AAAA;AAAC,SAASD,CAACA,CAACH,CAAC,EAAC;EAAC,OAAOA,CAAC,CAACI,MAAM,GAACJ,CAAC,CAAC,CAAC,CAAC,GAAC,KAAK,CAAC;AAAA;AAAC,SAASK,CAACA,CAACL,CAAC,EAACC,CAAC,EAAC;EAAC,MAAME,CAAC,GAACF,CAAC,CAACK,OAAO,CAACN,CAAC,CAAC;EAAC,OAAM,CAAC,CAAC,KAAGG,CAAC,GAAC,KAAK,CAAC,GAACF,CAAC,CAACM,IAAI,CAACC,GAAG,CAACL,CAAC,GAAC,CAAC,EAAC,CAAC,CAAC,CAAC;AAAA;AAAC,SAASM,CAACA,CAACT,CAAC,EAACC,CAAC,EAAC;EAAC,MAAME,CAAC,GAACF,CAAC,CAACK,OAAO,CAACN,CAAC,CAAC;EAAC,OAAM,CAAC,CAAC,KAAGG,CAAC,GAAC,KAAK,CAAC,GAACF,CAAC,CAACM,IAAI,CAACG,GAAG,CAACP,CAAC,GAAC,CAAC,EAACF,CAAC,CAACG,MAAM,GAAC,CAAC,CAAC,CAAC;AAAA;AAAC,SAASO,CAACA,CAACX,CAAC,EAACC,CAAC,EAAC;EAAC,MAAME,CAAC,GAACS,KAAK,CAACC,IAAI,CAACZ,CAAC,CAAC;IAACI,CAAC,GAACJ,CAAC,CAACK,OAAO,CAACN,CAAC,CAAC;EAAC,OAAOK,CAAC,GAAC,CAAC,CAAC,IAAEF,CAAC,CAACW,MAAM,CAACT,CAAC,EAAC,CAAC,CAAC,EAACF,CAAC;AAAA;AAAC,SAASY,CAACA,CAACf,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOU,CAAC,CAACX,CAAC,EAACY,KAAK,CAACC,IAAI,CAACZ,CAAC,CAAC,CAACe,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,CAAC,CAAC;AAAA;AAAC,SAASC,CAACA,CAACjB,CAAC,EAACC,CAAC,EAAC;EAAC,MAAME,CAAC,GAAC,EAAE,CAACe,MAAM,CAAClB,CAAC,CAAC;EAAC,OAAOC,CAAC,CAACkB,MAAM,CAAEnB,CAAC,IAAE,CAACG,CAAC,CAACiB,QAAQ,CAACpB,CAAC,CAAE,CAAC;AAAA;AAAC,SAASqB,CAACA,CAACrB,CAAC,EAACC,CAAC,EAAC;EAAC,OAAM,CAAC,GAAGD,CAAC,CAAC,CAACE,MAAM,CAAE,CAACF,CAAC,EAACG,CAAC,EAACE,CAAC,KAAG;IAAC,MAAMI,CAAC,GAACF,IAAI,CAACe,KAAK,CAACjB,CAAC,GAACJ,CAAC,CAAC;IAAC,OAAM,CAACD,CAAC,CAACS,CAAC,CAAC,KAAGT,CAAC,CAACS,CAAC,CAAC,GAAC,EAAE,CAAC,EAAEc,IAAI,CAACpB,CAAC,CAAC,EAACH,CAAC;EAAA,CAAC,EAAE,EAAE,CAAC;AAAA;AAAC,SAAewB,CAACA,CAAAC,EAAA;EAAA,OAAAC,EAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,GAAA;EAAAA,EAAA,GAAAG,mBAAA,CAAhB,WAAiB7B,CAAC,EAACC,CAAC,GAAC,GAAG,EAAC;IAAC,MAAME,CAAC,GAACkB,CAAC,CAACrB,CAAC,EAACC,CAAC,CAAC;IAAC,KAAI,IAAID,CAAC,GAAC,CAAC,EAACA,CAAC,GAACG,CAAC,CAACC,MAAM,EAACJ,CAAC,EAAE,EAAC,MAAMG,CAAC,CAACH,CAAC,CAAC,QAAA8B,oBAAA,CAAO,IAAIC,OAAO,CAAE/B,CAAC,IAAEgC,UAAU,CAAChC,CAAC,EAAC,CAAC,CAAE,CAAC;EAAA,CAAC;EAAA,OAAA0B,EAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAOzB,CAAC,IAAI8B,SAAS,EAAChB,CAAC,IAAIiB,uBAAuB,EAACvB,CAAC,IAAIwB,wBAAwB,EAACpB,CAAC,IAAIqB,uBAAuB,EAACnC,CAAC,IAAIoC,SAAS,EAACrC,CAAC,IAAIsC,aAAa,EAACd,CAAC,IAAIe,eAAe,EAAClB,CAAC,IAAImB,UAAU,EAAC/B,CAAC,IAAIgC,WAAW,EAACpC,CAAC,IAAIqC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}