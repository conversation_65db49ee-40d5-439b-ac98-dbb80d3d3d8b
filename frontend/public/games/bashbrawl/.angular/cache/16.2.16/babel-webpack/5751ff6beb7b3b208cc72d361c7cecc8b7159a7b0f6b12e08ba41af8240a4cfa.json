{"ast": null, "code": "export default function _isObject(x) {\n  return Object.prototype.toString.call(x) === '[object Object]';\n}", "map": {"version": 3, "names": ["_isObject", "x", "Object", "prototype", "toString", "call"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/ramda/es/internal/_isObject.js"], "sourcesContent": ["export default function _isObject(x) {\n  return Object.prototype.toString.call(x) === '[object Object]';\n}"], "mappings": "AAAA,eAAe,SAASA,SAASA,CAACC,CAAC,EAAE;EACnC,OAAOC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,CAAC,CAAC,KAAK,iBAAiB;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}