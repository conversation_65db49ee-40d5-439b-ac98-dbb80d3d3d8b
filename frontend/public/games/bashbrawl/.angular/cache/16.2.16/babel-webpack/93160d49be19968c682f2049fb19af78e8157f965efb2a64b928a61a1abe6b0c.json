{"ast": null, "code": "let t = !1;\nfunction e(t) {\n  if ((t = Math.trunc(t) || 0) < 0 && (t += this.length), !(t < 0 || t >= this.length)) return this[t];\n}\nif (!t) {\n  t = !0;\n  const r = Reflect.getPrototypeOf(Int8Array);\n  for (const t of [Array, String, r]) Object.defineProperty(t.prototype, \"at\", {\n    value: e,\n    writable: !0,\n    enumerable: !1,\n    configurable: !0\n  });\n}\nexport { e as at };", "map": {"version": 3, "names": ["t", "e", "Math", "trunc", "length", "r", "Reflect", "getPrototypeOf", "Int8Array", "Array", "String", "Object", "defineProperty", "prototype", "value", "writable", "enumerable", "configurable", "at"], "sources": ["/Users/<USER>/develop/game/QuickGames/frontend/public/games/bashbrawl/node_modules/@cds/core/polyfills/at.js"], "sourcesContent": ["let t=!1;function e(t){if((t=Math.trunc(t)||0)<0&&(t+=this.length),!(t<0||t>=this.length))return this[t]}if(!t){t=!0;const r=Reflect.getPrototypeOf(Int8Array);for(const t of[Array,String,r])Object.defineProperty(t.prototype,\"at\",{value:e,writable:!0,enumerable:!1,configurable:!0})}export{e as at};\n"], "mappings": "AAAA,IAAIA,CAAC,GAAC,CAAC,CAAC;AAAC,SAASC,CAACA,CAACD,CAAC,EAAC;EAAC,IAAG,CAACA,CAAC,GAACE,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC,IAAE,CAAC,IAAE,CAAC,KAAGA,CAAC,IAAE,IAAI,CAACI,MAAM,CAAC,EAAC,EAAEJ,CAAC,GAAC,CAAC,IAAEA,CAAC,IAAE,IAAI,CAACI,MAAM,CAAC,EAAC,OAAO,IAAI,CAACJ,CAAC,CAAC;AAAA;AAAC,IAAG,CAACA,CAAC,EAAC;EAACA,CAAC,GAAC,CAAC,CAAC;EAAC,MAAMK,CAAC,GAACC,OAAO,CAACC,cAAc,CAACC,SAAS,CAAC;EAAC,KAAI,MAAMR,CAAC,IAAG,CAACS,KAAK,EAACC,MAAM,EAACL,CAAC,CAAC,EAACM,MAAM,CAACC,cAAc,CAACZ,CAAC,CAACa,SAAS,EAAC,IAAI,EAAC;IAACC,KAAK,EAACb,CAAC;IAACc,QAAQ,EAAC,CAAC,CAAC;IAACC,UAAU,EAAC,CAAC,CAAC;IAACC,YAAY,EAAC,CAAC;EAAC,CAAC,CAAC;AAAA;AAAC,SAAOhB,CAAC,IAAIiB,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}