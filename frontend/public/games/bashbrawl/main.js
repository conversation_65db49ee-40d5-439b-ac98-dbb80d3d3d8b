"use strict";
(self["webpackChunk_jggoebel_bashbrawl"] = self["webpackChunk_jggoebel_bashbrawl"] || []).push([["main"],{

/***/ 1437:
/*!***************************************!*\
  !*** ./src/app/app-config.service.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppConfigService: () => (/* binding */ AppConfigService)
/* harmony export */ });
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rxjs */ 5342);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);




class AppConfigService {
  constructor(http) {
    this.http = http;
  }
  loadAppConfig() {
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_0__.lastValueFrom)(this.http.get('/config.json')).then(data => {
      this.appConfig = data;
    });
  }
  getLogo(logoPath) {
    const headers = new _angular_common_http__WEBPACK_IMPORTED_MODULE_1__.HttpHeaders();
    headers.set('Accept', '*');
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_0__.lastValueFrom)(this.http.get(logoPath, {
      headers,
      responseType: 'text'
    }));
  }
  getConfig() {
    return this.appConfig;
  }
  static {
    this.ɵfac = function AppConfigService_Factory(t) {
      return new (t || AppConfigService)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_1__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjectable"]({
      token: AppConfigService,
      factory: AppConfigService.ɵfac
    });
  }
}

/***/ }),

/***/ 4114:
/*!***************************************!*\
  !*** ./src/app/app-routing.module.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppRoutingModule: () => (/* binding */ AppRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _home_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./home.component */ 9974);
/* harmony import */ var _app_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./app.component */ 92);
/* harmony import */ var _config_config_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./config/config.component */ 3054);
/* harmony import */ var _leaderboard_leaderboard_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./leaderboard/leaderboard.component */ 4568);
/* harmony import */ var _scans_scans_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./scans/scans.component */ 9028);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 7580);








const routes = [{
  path: '',
  component: _app_component__WEBPACK_IMPORTED_MODULE_1__.AppComponent,
  children: [{
    path: 'config',
    component: _config_config_component__WEBPACK_IMPORTED_MODULE_2__.ConfigComponent
  }, {
    path: 'leaderboard',
    component: _leaderboard_leaderboard_component__WEBPACK_IMPORTED_MODULE_3__.LeaderboardComponent
  }, {
    path: 'scans',
    component: _scans_scans_component__WEBPACK_IMPORTED_MODULE_4__.ScansComponent
  }, {
    path: '',
    component: _home_component__WEBPACK_IMPORTED_MODULE_0__.HomeComponent
  }]
}];
class AppRoutingModule {
  static {
    this.ɵfac = function AppRoutingModule_Factory(t) {
      return new (t || AppRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineNgModule"]({
      type: AppRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_6__.RouterModule.forRoot(routes, {}), _angular_router__WEBPACK_IMPORTED_MODULE_6__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵsetNgModuleScope"](AppRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_6__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_6__.RouterModule]
  });
})();

/***/ }),

/***/ 92:
/*!**********************************!*\
  !*** ./src/app/app.component.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppComponent: () => (/* binding */ AppComponent)
/* harmony export */ });
/* harmony import */ var _cds_core_icon_register_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @cds/core/icon/register.js */ 3031);
/* harmony import */ var _cds_core_icon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @cds/core/icon */ 2018);
/* harmony import */ var _terminals_terminal_themes_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./terminals/terminal-themes/themes */ 1710);
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/environments/environment */ 5312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _app_config_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app-config.service */ 1437);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _clr_angular__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @clr/angular */ 6275);









function AppComponent_div_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "cds-icon", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2, " bashbrawl.com\n");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function AppComponent_div_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 6)(1, "a", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](2, "cds-icon", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](3, " on GitHub");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
}
function AppComponent_div_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 10)(1, "a", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2, "Impressum");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("href", ctx_r2.imprint, _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵsanitizeUrl"]);
  }
}
function AppComponent_div_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 12)(1, "a", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2, "Privacy Policy");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("href", ctx_r3.privacyPolicy, _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵsanitizeUrl"]);
  }
}
class AppComponent {
  constructor(config) {
    this.config = config;
    this.Config = this.config.getConfig();
    this.title = this.Config.title || 'BashBrawl';
    this.logo = this.Config.logo || '/assets/bashbrawl/bashbrawl_text.png';
    this.themes = _terminals_terminal_themes_themes__WEBPACK_IMPORTED_MODULE_1__.themes;
    this.imprint = '';
    this.privacyPolicy = '';
    this.badgeScanningMode = false;
    this.config.getLogo(this.logo).then(obj => {
      _cds_core_icon__WEBPACK_IMPORTED_MODULE_5__.ClarityIcons.addIcons(['logo', obj]);
    });
    if (this.Config.favicon) {
      const fi = document.querySelector('#favicon');
      fi.href = this.Config.favicon;
    }
    if (src_environments_environment__WEBPACK_IMPORTED_MODULE_2__.environment.imprint && src_environments_environment__WEBPACK_IMPORTED_MODULE_2__.environment.imprint != '') {
      this.imprint = src_environments_environment__WEBPACK_IMPORTED_MODULE_2__.environment.imprint;
    }
    if (src_environments_environment__WEBPACK_IMPORTED_MODULE_2__.environment.privacypolicy && src_environments_environment__WEBPACK_IMPORTED_MODULE_2__.environment.privacypolicy != '') {
      this.privacyPolicy = src_environments_environment__WEBPACK_IMPORTED_MODULE_2__.environment.privacypolicy;
    }
    this.badgeScanningMode = localStorage.getItem('badge_scanner') ? true : false;
    if (localStorage.getItem('disable_imprint') && localStorage.getItem('disable_imprint') != '') {
      this.imprint = '';
      this.privacyPolicy = '';
    }
  }
  static {
    this.ɵfac = function AppComponent_Factory(t) {
      return new (t || AppComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_app_config_service__WEBPACK_IMPORTED_MODULE_3__.AppConfigService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: AppComponent,
      selectors: [["app-root"]],
      decls: 8,
      vars: 4,
      consts: [[1, "main-container"], [1, "content-container"], [1, "content-area"], ["class", "github", 4, "ngIf"], ["class", "imprint", 4, "ngIf"], ["class", "privacy", 4, "ngIf"], [1, "github"], ["shape", "play", "solid", "true"], ["href", "https://github.com/jggoebel/bashbrawl"], ["shape", "star", "solid", "true"], [1, "imprint"], [3, "href"], [1, "privacy"]],
      template: function AppComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](3, "router-outlet");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](4, AppComponent_div_4_Template, 3, 0, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](5, AppComponent_div_5_Template, 4, 0, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](6, AppComponent_div_6_Template, 3, 1, "div", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](7, AppComponent_div_7_Template, 3, 1, "div", 5);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.badgeScanningMode);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", !ctx.badgeScanningMode);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.imprint);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.privacyPolicy);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterOutlet, _clr_angular__WEBPACK_IMPORTED_MODULE_8__.CdsIconCustomTag],
      styles: ["[_ngcontent-%COMP%]:root {\n  --clr-icon-costum: black;\n}\n\n.imprint[_ngcontent-%COMP%] {\n  position: absolute;\n  right: 20px;\n  bottom: 20px;\n}\n.imprint[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  color: white;\n}\n\n.privacy[_ngcontent-%COMP%] {\n  position: absolute;\n  right: 100px;\n  bottom: 20px;\n}\n.privacy[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  color: white;\n}\n\n.github[_ngcontent-%COMP%] {\n  background-color: #74e2cd;\n  position: absolute;\n  left: 20px;\n  bottom: 20px;\n  padding: 5px 15px;\n  border-radius: 20px;\n  font-weight: bolder;\n  font-size: 20px;\n  color: #01162a;\n  text-decoration: none;\n}\n.github[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  font-weight: bolder;\n  font-size: 20px;\n  color: #01162a;\n  text-decoration: none;\n}\n.github[_ngcontent-%COMP%]   cds-icon[_ngcontent-%COMP%] {\n  font-size: 24px;\n  font-weight: bold;\n  color: #01162a;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usd0JBQUE7QUFDRjs7QUFFQTtFQUNFLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7QUFDRjtBQUFFO0VBQ0UsWUFBQTtBQUVKOztBQUVBO0VBQ0Usa0JBQUE7RUFDQSxZQUFBO0VBQ0EsWUFBQTtBQUNGO0FBQUU7RUFDRSxZQUFBO0FBRUo7O0FBRUE7RUFDRSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsVUFBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EsY0FBQTtFQUNBLHFCQUFBO0FBQ0Y7QUFBRTtFQUNFLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLGNBQUE7RUFDQSxxQkFBQTtBQUVKO0FBQUU7RUFDRSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSxjQUFBO0FBRUoiLCJzb3VyY2VzQ29udGVudCI6WyI6cm9vdCB7XG4gIC0tY2xyLWljb24tY29zdHVtOiBibGFjaztcbn1cblxuLmltcHJpbnQge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHJpZ2h0OiAyMHB4O1xuICBib3R0b206IDIwcHg7XG4gIGEge1xuICAgIGNvbG9yOiB3aGl0ZTtcbiAgfVxufVxuXG4ucHJpdmFjeSB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgcmlnaHQ6IDEwMHB4O1xuICBib3R0b206IDIwcHg7XG4gIGEge1xuICAgIGNvbG9yOiB3aGl0ZTtcbiAgfVxufVxuXG4uZ2l0aHViIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogIzc0ZTJjZDtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICBsZWZ0OiAyMHB4O1xuICBib3R0b206IDIwcHg7XG4gIHBhZGRpbmc6IDVweCAxNXB4O1xuICBib3JkZXItcmFkaXVzOiAyMHB4O1xuICBmb250LXdlaWdodDogYm9sZGVyO1xuICBmb250LXNpemU6IDIwcHg7XG4gIGNvbG9yOiAjMDExNjJhO1xuICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gIGEge1xuICAgIGZvbnQtd2VpZ2h0OiBib2xkZXI7XG4gICAgZm9udC1zaXplOiAyMHB4O1xuICAgIGNvbG9yOiAjMDExNjJhO1xuICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgfVxuICBjZHMtaWNvbiB7XG4gICAgZm9udC1zaXplOiAyNHB4O1xuICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xuICAgIGNvbG9yOiAjMDExNjJhO1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 635:
/*!*******************************!*\
  !*** ./src/app/app.module.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppModule: () => (/* binding */ AppModule)
/* harmony export */ });
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @angular/platform-browser */ 436);
/* harmony import */ var _angular_platform_browser_animations__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @angular/platform-browser/animations */ 3835);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _app_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app-routing.module */ 4114);
/* harmony import */ var _app_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./app.component */ 92);
/* harmony import */ var _clr_angular__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @clr/angular */ 6275);
/* harmony import */ var _root_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./root.component */ 1469);
/* harmony import */ var _home_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./home.component */ 9974);
/* harmony import */ var _config_config_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./config/config.component */ 3054);
/* harmony import */ var _leaderboard_leaderboard_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./leaderboard/leaderboard.component */ 4568);
/* harmony import */ var _scans_scans_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./scans/scans.component */ 9028);
/* harmony import */ var _leaderboard_embedded_leaderboard_embedded_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./leaderboard/embedded/leaderboard-embedded.component */ 4288);
/* harmony import */ var _terminals_bashbrawl_bashbrawlterminal_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./terminals/bashbrawl/bashbrawlterminal.component */ 6386);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var _pipes_atob_pipe__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./pipes/atob.pipe */ 810);
/* harmony import */ var _pipes_uppercase_pipe__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./pipes/uppercase.pipe */ 9128);
/* harmony import */ var _services_settings_service__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./services/settings.service */ 875);
/* harmony import */ var _app_config_service__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./app-config.service */ 1437);
/* harmony import */ var angular_split__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! angular-split */ 5787);
/* harmony import */ var _services_api_service__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./services/api.service */ 3366);
/* harmony import */ var _terminals_bashbrawl_languages_language_command_service__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./terminals/bashbrawl/languages/language-command.service */ 3818);
/* harmony import */ var _services_score_service__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./services/score.service */ 516);
/* harmony import */ var _cds_core_icon_register_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @cds/core/icon/register.js */ 3031);
/* harmony import */ var _cds_core_icon__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @cds/core/icon */ 2018);
/* harmony import */ var _cds_core_icon__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @cds/core/icon */ 7007);
/* harmony import */ var _cds_core_icon__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @cds/core/icon */ 8775);
/* harmony import */ var _cds_core_icon__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @cds/core/icon */ 6205);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @angular/forms */ 4456);


























_cds_core_icon__WEBPACK_IMPORTED_MODULE_17__.ClarityIcons.addIcons(_cds_core_icon__WEBPACK_IMPORTED_MODULE_18__.terminalIcon, _cds_core_icon__WEBPACK_IMPORTED_MODULE_19__.starIcon, _cds_core_icon__WEBPACK_IMPORTED_MODULE_20__.playIcon);
const appInitializerFn = appConfig => {
  return () => {
    return appConfig.loadAppConfig();
  };
};
class AppModule {
  static {
    this.ɵfac = function AppModule_Factory(t) {
      return new (t || AppModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵdefineNgModule"]({
      type: AppModule,
      bootstrap: [_root_component__WEBPACK_IMPORTED_MODULE_2__.RootComponent]
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵdefineInjector"]({
      providers: [_app_component__WEBPACK_IMPORTED_MODULE_1__.AppComponent, _services_settings_service__WEBPACK_IMPORTED_MODULE_11__.SettingsService, _app_config_service__WEBPACK_IMPORTED_MODULE_12__.AppConfigService, _services_api_service__WEBPACK_IMPORTED_MODULE_13__.APIClientFactory, _terminals_bashbrawl_languages_language_command_service__WEBPACK_IMPORTED_MODULE_14__.LanguageCommandService, _services_score_service__WEBPACK_IMPORTED_MODULE_15__.ScoreService, {
        provide: _angular_core__WEBPACK_IMPORTED_MODULE_21__.APP_INITIALIZER,
        useFactory: appInitializerFn,
        multi: true,
        deps: [_app_config_service__WEBPACK_IMPORTED_MODULE_12__.AppConfigService]
      }],
      imports: [_angular_platform_browser__WEBPACK_IMPORTED_MODULE_22__.BrowserModule, _app_routing_module__WEBPACK_IMPORTED_MODULE_0__.AppRoutingModule, _clr_angular__WEBPACK_IMPORTED_MODULE_23__.ClarityModule, _angular_common_http__WEBPACK_IMPORTED_MODULE_24__.HttpClientModule, angular_split__WEBPACK_IMPORTED_MODULE_25__.AngularSplitModule, _angular_platform_browser_animations__WEBPACK_IMPORTED_MODULE_26__.BrowserAnimationsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_27__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_27__.ReactiveFormsModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_21__["ɵɵsetNgModuleScope"](AppModule, {
    declarations: [_app_component__WEBPACK_IMPORTED_MODULE_1__.AppComponent, _root_component__WEBPACK_IMPORTED_MODULE_2__.RootComponent, _home_component__WEBPACK_IMPORTED_MODULE_3__.HomeComponent, _config_config_component__WEBPACK_IMPORTED_MODULE_4__.ConfigComponent, _leaderboard_leaderboard_component__WEBPACK_IMPORTED_MODULE_5__.LeaderboardComponent, _scans_scans_component__WEBPACK_IMPORTED_MODULE_6__.ScansComponent, _leaderboard_embedded_leaderboard_embedded_component__WEBPACK_IMPORTED_MODULE_7__.EmbeddedLeaderboardComponent, _terminals_bashbrawl_bashbrawlterminal_component__WEBPACK_IMPORTED_MODULE_8__.BashbrawlterminalComponent, _pipes_atob_pipe__WEBPACK_IMPORTED_MODULE_9__.AtobPipe, _pipes_uppercase_pipe__WEBPACK_IMPORTED_MODULE_10__.UppercasePipe],
    imports: [_angular_platform_browser__WEBPACK_IMPORTED_MODULE_22__.BrowserModule, _app_routing_module__WEBPACK_IMPORTED_MODULE_0__.AppRoutingModule, _clr_angular__WEBPACK_IMPORTED_MODULE_23__.ClarityModule, _angular_common_http__WEBPACK_IMPORTED_MODULE_24__.HttpClientModule, angular_split__WEBPACK_IMPORTED_MODULE_25__.AngularSplitModule, _angular_platform_browser_animations__WEBPACK_IMPORTED_MODULE_26__.BrowserAnimationsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_27__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_27__.ReactiveFormsModule]
  });
})();

/***/ }),

/***/ 3054:
/*!********************************************!*\
  !*** ./src/app/config/config.component.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ConfigComponent: () => (/* binding */ ConfigComponent),
/* harmony export */   Cooldown: () => (/* binding */ Cooldown)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _services_score_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../services/score.service */ 516);
/* harmony import */ var _terminals_bashbrawl_languages_language_command_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../terminals/bashbrawl/languages/language-command.service */ 3818);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _clr_angular__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @clr/angular */ 6275);







function ConfigComponent_clr_control_error_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "clr-control-error");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, "Server required.");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function ConfigComponent_div_20_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div")(1, "label");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](2, "input", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const lang_r3 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("formControlName", lang_r3);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", lang_r3, " ");
  }
}
function ConfigComponent_ng_container_25_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, " Healthy! ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerEnd"]();
  }
}
function ConfigComponent_ng_container_25_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, " Not healthy (or local) ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerEnd"]();
  }
}
function ConfigComponent_ng_container_25_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](1, ConfigComponent_ng_container_25_ng_container_1_Template, 2, 0, "ng-container", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](2, ConfigComponent_ng_container_25_ng_container_2_Template, 2, 0, "ng-container", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx_r2.healthy);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx_r2.healthy);
  }
}
class Cooldown {}
class ConfigComponent {
  constructor(scoreService, languageCommandService) {
    this.scoreService = scoreService;
    this.languageCommandService = languageCommandService;
    this.scannedCode = false;
    this.code = '';
    this.scannerCode = '';
    this.healthy = false;
    this.healthCheckPerformed = false;
    this.languagesForm = new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormGroup({});
    this.settingsForm = new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormGroup({
      server: new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormControl(null, [_angular_forms__WEBPACK_IMPORTED_MODULE_3__.Validators.required]),
      badge_scanner: new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormControl(null, [_angular_forms__WEBPACK_IMPORTED_MODULE_3__.Validators.required]),
      disable_imprint: new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormControl(null, [_angular_forms__WEBPACK_IMPORTED_MODULE_3__.Validators.required])
    }, {
      validators: ({
        value: {
          new_password1: pw1,
          new_password1: pw2
        }
      }) => pw1 && pw1 == pw2 ? null : {
        passwordMismatch: true
      }
    });
    this.badgeScanningMode = localStorage.getItem('badge_scanner') ? true : false;
    this.disableImprint = localStorage.getItem('disable_imprint') ? true : false;
    this.settingsForm.setValue({
      server: localStorage.getItem('score_server'),
      badge_scanner: this.badgeScanningMode,
      disable_imprint: this.disableImprint
    });
    this.languageCommandService.getAllLanguageKeys().forEach(lang => {
      let enabled = true;
      const configEnabled = localStorage.getItem('enabled_' + lang);
      if (configEnabled && configEnabled == 'false') {
        enabled = false;
      }
      this.languagesForm.addControl(lang, new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormControl(enabled));
    });
    // in normal mode display the
  }

  onScan(code) {
    console.log('Scanned: ' + code);
    this.code = code;
    // TODO Display scanned code
    this.scannedCode = false;
  }
  healthcheck() {
    this.scoreService.healthz().subscribe({
      next: () => {
        this.healthy = true;
        this.healthCheckPerformed = true;
      },
      error: () => {
        this.healthy = false;
        this.healthCheckPerformed = true;
      }
    });
  }
  storeSettings() {
    const server = this.settingsForm.controls['server'].value;
    localStorage.setItem('score_server', server);
    const badge_scanning_mode = this.settingsForm.controls['badge_scanner'].value;
    const disableImprint = this.settingsForm.controls['disable_imprint'].value;
    if (badge_scanning_mode == true) {
      localStorage.setItem('badge_scanner', 'true');
    } else {
      localStorage.removeItem('badge_scanner');
    }
    if (disableImprint == true) {
      localStorage.setItem('disable_imprint', 'true');
    } else {
      localStorage.removeItem('disable_imprint');
    }
    this.languageCommandService.getAllLanguageKeys().forEach(lang => {
      localStorage.setItem('enabled_' + lang, this.languagesForm.value[lang]);
    });
    window.location.reload();
  }
  keyEvent(event) {
    window.clearTimeout(this.scannerTimeoutId);
    if (event.key === 'Enter' && this.scannerCode.length >= 2) {
      this.onScan(this.scannerCode);
      this.scannerCode = '';
    } else {
      if (!this.scannedCode) {
        this.scannerCode += event.key;
        // Reset scannerCode after 20ms to avoid normal keyboard inputs
        this.scannerTimeoutId = window.setTimeout(() => {
          this.scannerCode = '';
        }, 50);
      }
    }
  }
  static {
    this.ɵfac = function ConfigComponent_Factory(t) {
      return new (t || ConfigComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_services_score_service__WEBPACK_IMPORTED_MODULE_0__.ScoreService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_terminals_bashbrawl_languages_language_command_service__WEBPACK_IMPORTED_MODULE_1__.LanguageCommandService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: ConfigComponent,
      selectors: [["app-config"]],
      hostBindings: function ConfigComponent_HostBindings(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("keypress", function ConfigComponent_keypress_HostBindingHandler($event) {
            return ctx.keyEvent($event);
          }, false, _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresolveWindow"]);
        }
      },
      decls: 32,
      vars: 6,
      consts: [[1, "config"], ["clrForm", "", 3, "formGroup"], [1, "clr-col-md-4"], ["clrInput", "", "type", "text", "name", "server", "formControlName", "server", "required", "", 1, "clr-col-md-8"], [4, "clrIfError"], ["type", "checkbox", "clrCheckbox", "", "name", "badge_scanner", "formControlName", "badge_scanner", 1, "clr-col-md-8"], ["type", "checkbox", "clrCheckbox", "", "name", "disable_imprint", "formControlName", "disable_imprint", 1, "clr-col-md-8"], [3, "formGroup"], [4, "ngFor", "ngForOf"], ["type", "button", 1, "btn", "btn-outline", 3, "click"], [4, "ngIf"], ["type", "checkbox", 3, "formControlName"]],
      template: function ConfigComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0)(1, "form", 1)(2, "clr-input-container")(3, "label", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](4, "Server (or local)");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](5, "input", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](6, ConfigComponent_clr_control_error_6_Template, 2, 0, "clr-control-error", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](7, "clr-checkbox-container")(8, "label", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9, "Badge Scanner Enabled");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](10, "clr-checkbox-wrapper");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](11, "input", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](12, "clr-checkbox-container")(13, "label", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](14, "Disable Imprint");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](15, "clr-checkbox-wrapper");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](16, "input", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](17, "form", 7)(18, "h3");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](19, "Languages");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](20, ConfigComponent_div_20_Template, 4, 2, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](21, "button", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function ConfigComponent_Template_button_click_21_listener() {
            return ctx.storeSettings();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](22, " Save ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](23, "button", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function ConfigComponent_Template_button_click_23_listener() {
            return ctx.healthcheck();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](24, " Healthcheck\n");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](25, ConfigComponent_ng_container_25_Template, 3, 2, "ng-container", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](26, "div");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](27, " Scan any code to view the output:");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](28, "br");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](29, " Last Scan: ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](30, "code");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](31);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("formGroup", ctx.settingsForm);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("clrIfError", "required");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](11);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("formGroup", ctx.languagesForm);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx.languageCommandService.getAllLanguageKeys());
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.healthCheckPerformed);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](ctx.code);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _clr_angular__WEBPACK_IMPORTED_MODULE_5__.ClrLabel, _clr_angular__WEBPACK_IMPORTED_MODULE_5__.ClrControlError, _clr_angular__WEBPACK_IMPORTED_MODULE_5__.ClrIfError, _clr_angular__WEBPACK_IMPORTED_MODULE_5__.ClrForm, _clr_angular__WEBPACK_IMPORTED_MODULE_5__.ClrCheckbox, _clr_angular__WEBPACK_IMPORTED_MODULE_5__.ClrCheckboxContainer, _clr_angular__WEBPACK_IMPORTED_MODULE_5__.ClrCheckboxWrapper, _clr_angular__WEBPACK_IMPORTED_MODULE_5__.ClrInput, _clr_angular__WEBPACK_IMPORTED_MODULE_5__.ClrInputContainer, _angular_forms__WEBPACK_IMPORTED_MODULE_3__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_3__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.CheckboxControlValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_3__.FormControlName],
      styles: ["/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 9974:
/*!***********************************!*\
  !*** ./src/app/home.component.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Cooldown: () => (/* binding */ Cooldown),
/* harmony export */   HomeComponent: () => (/* binding */ HomeComponent)
/* harmony export */ });
/* harmony import */ var _angular_animations__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/animations */ 7172);
/* harmony import */ var _services_api_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./services/api.service */ 3366);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _terminals_bashbrawl_languages_language_command_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./terminals/bashbrawl/languages/language-command.service */ 3818);
/* harmony import */ var _services_score_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./services/score.service */ 516);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var angular_split__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! angular-split */ 5787);
/* harmony import */ var _leaderboard_embedded_leaderboard_embedded_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./leaderboard/embedded/leaderboard-embedded.component */ 4288);
/* harmony import */ var _terminals_bashbrawl_bashbrawlterminal_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./terminals/bashbrawl/bashbrawlterminal.component */ 6386);
/* harmony import */ var _pipes_uppercase_pipe__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pipes/uppercase.pipe */ 9128);










const _c0 = ["terminal"];
const _c1 = ["instructions"];
function HomeComponent_span_14_span_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, " \u00B7 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function HomeComponent_span_14_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "span", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipe"](2, "uppercase");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](3, HomeComponent_span_14_span_3_Template, 2, 0, "span", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const lang_r9 = ctx.$implicit;
    const i_r10 = ctx.index;
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"]("", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipeBind1"](2, 2, lang_r9), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", i_r10 !== ctx_r2.getBrawlLanguages().length - 1);
  }
}
function HomeComponent_div_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, " Type ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "i", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](3, "brawl play");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4, " to enter the arena. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function HomeComponent_div_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, " Press ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "i", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](3, "ENTER");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4, " to play. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function HomeComponent_div_17_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, " Scan your ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "i", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](3, "BADGE");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4, " to play. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function HomeComponent_div_18_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, " Please come back in ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "i", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4, " for another try! ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](ctx_r6.getFormattedTimeUntilDate(ctx_r6.cooldownTime));
  }
}
function HomeComponent_div_26_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, " Press ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "i", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](3, "ENTER");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4, " to continue. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function HomeComponent_div_27_Template(rf, ctx) {
  if (rf & 1) {
    const _r14 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("@terminalAnimation.done", function HomeComponent_div_27_Template_div_animation_terminalAnimation_done_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r14);
      const ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r13.resize());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](1, "app-bashbrawl-terminal", 23, 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("gameEnded", function HomeComponent_div_27_Template_app_bashbrawl_terminal_gameEnded_1_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r14);
      const ctx_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r15.gameEnded($event));
    })("gameStarted", function HomeComponent_div_27_Template_app_bashbrawl_terminal_gameStarted_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r14);
      const ctx_r16 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r16.setLargeTerminal());
    })("focusout", function HomeComponent_div_27_Template_app_bashbrawl_terminal_focusout_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r14);
      const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r17.focusTerminal());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("@terminalAnimation", ctx_r8.terminalState);
  }
}
class Cooldown {}
class HomeComponent {
  constructor(languageCommandService, scoreService) {
    this.languageCommandService = languageCommandService;
    this.scoreService = scoreService;
    this.terminalState = 'hidden';
    this.shrinkState = 'normal';
    this.leaderboardState = 'normal';
    this.badgeScanningMode = false;
    this.gameStarted = false;
    this.advancedLeaderboard = false;
    this.code = '';
    this.scannerCode = '';
    this.cooldown = false;
    this.cooldownTime = '';
    this.badgeScanningMode = localStorage.getItem('badge_scanner') ? true : false;
    // in normal mode display the
  }

  ngOnInit() {
    this.terminalState = this.badgeScanningMode ? 'hidden' : 'small';
  }
  dragEnd() {
    this.resizeTerminals();
  }
  resizeTerminals() {
    this.terms.resize();
  }
  getBrawlLanguages() {
    return this.languageCommandService.getLanguageNames().slice(0, 5);
  }
  setLargeTerminal() {
    this.shrinkState = 'shrunk';
    this.leaderboardState = 'hidden';
    this.terminalState = 'large';
    this.terms.resize();
  }
  setSmallTerminal() {
    this.shrinkState = 'normal';
    this.terminalState = 'small';
    this.leaderboardState = 'hidden';
  }
  setHiddenTerminal() {
    this.shrinkState = 'normal';
    this.terminalState = 'hidden';
    this.leaderboardState = 'normal';
  }
  resetToDefault() {
    this.code = '';
    this.gameStarted = false;
    this.winningScreen = false;
    if (this.terms) {
      this.terms.clearTerminal();
    }
    this.setHiddenTerminal();
  }
  gameEnded(gameFinished) {
    this.unfocusTerminal();
    if (gameFinished && gameFinished.success) {
      this.gameStarted = false;
      this.winningScreen = true;
      this.leaderboardState = 'normal';
      this.shrinkState = 'shrunk';
      this.terminalState = 'hidden';
      this.leaderboardWithLocalPlacement = gameFinished.leaderboardWithLocalPlacement;
      this.score = gameFinished.score;
    } else {
      this.resetToDefault();
    }
  }
  onScan(code) {
    this.code = code;
    this.cooldown = false;
    this.cooldownTime = '';
    this.scoreService.scan(code).subscribe({
      next: () => {
        this.onValidScan(code);
      },
      error: errorResponse => {
        const responseText = (0,_services_api_service__WEBPACK_IMPORTED_MODULE_0__.extractResponseContent)(errorResponse.error);
        this.onCooldown(responseText.cooldown);
      }
    });
  }
  onValidScan(code) {
    this.code = code;
    this.gameStarted = true;
    setTimeout(() => this.setSmallTerminal(), 100);
  }
  onCooldown(cooldownTimeEnd) {
    this.cooldown = true;
    this.cooldownTime = cooldownTimeEnd;
    this.gameStarted = false;
    this.code = '';
    setTimeout(() => this.resetCooldownTimer(), 5000);
  }
  resetCooldownTimer() {
    this.cooldown = false;
    this.cooldownTime = '';
  }
  resize() {
    if (this.terms) {
      this.terms.resize();
    }
  }
  focusTerminal() {
    if (this.terminalState != 'hidden') {
      setTimeout(() => this.terms?.focusTerminal(), 0);
    }
  }
  unfocusTerminal() {
    this.terms?.blurTerminal();
  }
  keyEvent(event) {
    console.log(event.key);
    window.clearTimeout(this.scannerTimeoutId);
    if (event.code === 'Space') {
      this.advancedLeaderboard = true;
      return;
    }
    if (event.key === 'Enter' && this.winningScreen) {
      this.resetToDefault();
      return;
    }
    if (event.key === 'Enter' && !this.badgeScanningMode) {
      this.gameStarted = true;
      this.setSmallTerminal();
    } else if (event.key === 'Enter' && this.scannerCode.length >= 2) {
      this.onScan(this.scannerCode);
      this.scannerCode = '';
    } else {
      if (!this.gameStarted) {
        this.scannerCode += event.key;
        // Reset scannerCode after 20ms to avoid normal keyboard inputs
        this.scannerTimeoutId = window.setTimeout(() => {
          this.scannerCode = '';
        }, 50);
      }
    }
    event.preventDefault();
  }
  keyUpEvent(event) {
    if (event.code === 'Space') {
      this.advancedLeaderboard = false;
    }
    event.preventDefault();
  }
  getFormattedTimeUntilDate(futureDate) {
    const now = new Date(); // Current date and time
    const future = new Date(futureDate); // Target future date and time
    let difference = future.getTime() - now.getTime(); // Difference in milliseconds
    if (difference < 0) {
      return '00:00'; // If the future date is in the past, return zero time
    }

    const minutes = Math.floor(difference / 60000); // Convert milliseconds to minutes
    difference %= 60000; // Get the remaining milliseconds after removing minutes
    const seconds = Math.floor(difference / 1000); // Convert remaining milliseconds to seconds
    const formattedMinutes = minutes.toString();
    const formattedSeconds = seconds.toString();
    return `${formattedMinutes} minutes and ${formattedSeconds} seconds`;
  }
  static {
    this.ɵfac = function HomeComponent_Factory(t) {
      return new (t || HomeComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_terminals_bashbrawl_languages_language_command_service__WEBPACK_IMPORTED_MODULE_1__.LanguageCommandService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_score_service__WEBPACK_IMPORTED_MODULE_2__.ScoreService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineComponent"]({
      type: HomeComponent,
      selectors: [["app-home"]],
      viewQuery: function HomeComponent_Query(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵviewQuery"](_c0, 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵviewQuery"](_c1, 5);
        }
        if (rf & 2) {
          let _t;
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵloadQuery"]()) && (ctx.terms = _t.first);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵloadQuery"]()) && (ctx.instructions = _t.first);
        }
      },
      hostBindings: function HomeComponent_HostBindings(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("keypress", function HomeComponent_keypress_HostBindingHandler($event) {
            return ctx.keyEvent($event);
          }, false, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresolveWindow"])("keyup", function HomeComponent_keyup_HostBindingHandler($event) {
            return ctx.keyUpEvent($event);
          }, false, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresolveWindow"]);
        }
      },
      decls: 28,
      vars: 16,
      consts: [[1, "split-container"], ["unit", "percent", "direction", "vertical", "draggable", "false", 1, "hide-split-gutter", 3, "dragEnd"], ["divider", ""], [1, "split-area-1", 3, "size"], [1, "brawl-instructions"], ["instructions", ""], [1, "brawl-instructions-guide", 2, "max-height", "70vh"], ["src", "/assets/bashbrawl/bashbrawl_text.png"], [1, "brawl-instructions-text"], [1, "text", "centered"], ["class", "brawl-language", 4, "ngFor", "ngForOf"], ["class", "text centered cta", 4, "ngIf"], ["class", "text centered cooldown-timer", 4, "ngIf"], [3, "onlyTop", "embedded", "advanced", "score", "leaderboardWithLocalPlacementInput", "showLocalScores"], [1, "centered", "leaderboard-space"], [1, "brawl-language", "brawl-color"], ["class", "terminal", "id", "terminal", 4, "ngIf"], [1, "brawl-language"], [4, "ngIf"], [1, "text", "centered", "cta"], [1, "text", "centered", "cooldown-timer"], [1, "brawl-color"], ["id", "terminal", 1, "terminal"], [3, "gameEnded", "gameStarted", "focusout"], ["terminal", ""]],
      template: function HomeComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 0)(1, "as-split", 1, 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("dragEnd", function HomeComponent_Template_as_split_dragEnd_1_listener() {
            return ctx.dragEnd();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "as-split-area", 3)(4, "div", 4, 5)(6, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](7, "img", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](8, "div", 8)(9, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](10, " Test your command line skills.");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](11, "br");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](12, " You have 60 seconds to enter commands from ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](13, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](14, HomeComponent_span_14_Template, 4, 4, "span", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](15, HomeComponent_div_15_Template, 5, 0, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](16, HomeComponent_div_16_Template, 5, 0, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](17, HomeComponent_div_17_Template, 5, 0, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](18, HomeComponent_div_18_Template, 5, 1, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](19, "div");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](20, "app-leaderboard-embdedded", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](21, "div", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](22, " Hold ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](23, "i", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](24, "SPACE");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](25, " for stats. ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](26, HomeComponent_div_26_Template, 5, 0, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](27, HomeComponent_div_27_Template, 3, 1, "div", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("size", 100);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("@shrinkAnimation", ctx.shrinkState);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", ctx.getBrawlLanguages());
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx.badgeScanningMode && ctx.gameStarted);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx.badgeScanningMode && !ctx.gameStarted);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.badgeScanningMode && !ctx.gameStarted && !ctx.cooldown);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.cooldown);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("@shrinkAnimation", ctx.leaderboardState);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("onlyTop", 5)("embedded", true)("advanced", ctx.advancedLeaderboard)("score", ctx.score)("leaderboardWithLocalPlacementInput", ctx.leaderboardWithLocalPlacement)("showLocalScores", ctx.winningScreen);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.winningScreen);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.gameStarted);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_7__.NgIf, angular_split__WEBPACK_IMPORTED_MODULE_8__.SplitComponent, angular_split__WEBPACK_IMPORTED_MODULE_8__.SplitAreaDirective, _leaderboard_embedded_leaderboard_embedded_component__WEBPACK_IMPORTED_MODULE_3__.EmbeddedLeaderboardComponent, _terminals_bashbrawl_bashbrawlterminal_component__WEBPACK_IMPORTED_MODULE_4__.BashbrawlterminalComponent, _pipes_uppercase_pipe__WEBPACK_IMPORTED_MODULE_5__.UppercasePipe],
      styles: ["#terminal-column[_ngcontent-%COMP%] {\n  right: 0;\n  bottom: 0;\n  height: 100%;\n  width: 100%;\n}\n\n.split-container[_ngcontent-%COMP%] {\n  display: flex;\n  height: 100%;\n}\n\n.hide-split-gutter[_ngcontent-%COMP%]     .as-split-gutter {\n  display: none !important;\n}\n\n.split-area-1[_ngcontent-%COMP%] {\n  overflow-y: hidden !important;\n  padding-right: 0.6rem;\n}\n\n.split-area-2[_ngcontent-%COMP%] {\n  overflow-y: hidden !important;\n  padding-left: 0.6rem;\n}\n\n.brawl-color[_ngcontent-%COMP%] {\n  color: #74e2cd;\n}\n\n.brawl-instructions[_ngcontent-%COMP%] {\n  background-color: #01162a;\n  color: white;\n  padding: 0px 0px 24px 0;\n  height: 100%;\n  display: flex;\n  flex-flow: column;\n  font-family: monospace;\n}\n.brawl-instructions[_ngcontent-%COMP%]   .brawl-instructions-guide[_ngcontent-%COMP%] {\n  max-height: 50vh;\n}\n.brawl-instructions[_ngcontent-%COMP%]   .brawl-instructions-guide[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  margin-top: 5vh;\n  margin-left: auto;\n  margin-right: auto;\n  max-height: 30vh;\n  display: block;\n}\n.brawl-instructions[_ngcontent-%COMP%]   .brawl-instructions-guide[_ngcontent-%COMP%]   .leaderboard[_ngcontent-%COMP%] {\n  padding-top: 2vh;\n}\n.brawl-instructions[_ngcontent-%COMP%]   .brawl-instructions-guide[_ngcontent-%COMP%]   .leaderboard-space[_ngcontent-%COMP%] {\n  width: max-content;\n  font-size: 12px;\n}\n.brawl-instructions[_ngcontent-%COMP%]   .brawl-instructions-guide[_ngcontent-%COMP%]   .leaderboard-space[_ngcontent-%COMP%]   .brawl-language[_ngcontent-%COMP%] {\n  font-size: 16px;\n}\n.brawl-instructions[_ngcontent-%COMP%]   .brawl-instructions-guide[_ngcontent-%COMP%]   div.centered[_ngcontent-%COMP%] {\n  margin-left: auto;\n  margin-right: auto;\n}\n.brawl-instructions[_ngcontent-%COMP%]   .brawl-instructions-guide[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 20px;\n}\n.brawl-instructions[_ngcontent-%COMP%]   .brawl-instructions-guide[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: white;\n}\n.brawl-instructions[_ngcontent-%COMP%]   .brawl-instructions-guide[_ngcontent-%COMP%]   .text.centered[_ngcontent-%COMP%] {\n  text-align: center;\n}\n.brawl-instructions[_ngcontent-%COMP%]   .brawl-instructions-guide[_ngcontent-%COMP%]   .brawl-language[_ngcontent-%COMP%] {\n  color: #74e2cd;\n  font-size: 20px;\n  font-weight: bold;\n}\n.brawl-instructions[_ngcontent-%COMP%]   .brawl-instructions-guide[_ngcontent-%COMP%]   .cta[_ngcontent-%COMP%] {\n  font-size: 24px;\n  animation: _ngcontent-%COMP%_blinker 2.5s ease-in-out infinite;\n}\n@keyframes _ngcontent-%COMP%_blinker {\n  50% {\n    opacity: 0;\n  }\n}\n.brawl-instructions[_ngcontent-%COMP%]   .terminal[_ngcontent-%COMP%] {\n  margin-left: auto;\n  margin-right: auto;\n  display: block;\n  margin-top: 5vh;\n  min-width: 500px;\n}\n.brawl-instructions[_ngcontent-%COMP%]   .cooldown-timer[_ngcontent-%COMP%] {\n  text-align: center;\n  font-size: 24px;\n}\n\n.split-container[_ngcontent-%COMP%] {\n  height: calc(100% + 2.4rem) !important;\n  margin: -1.2rem;\n  background-color: #01162a;\n}\n\n.split-area-1[_ngcontent-%COMP%] {\n  padding-right: 0 !important;\n}\n\n.split-area-2[_ngcontent-%COMP%] {\n  padding-left: 0 !important;\n  padding-bottom: 0 !important;\n}\n\n@media only screen and (max-width: 600px) {\n  .brawl-instructions[_ngcontent-%COMP%]   .terminal[_ngcontent-%COMP%] {\n    max-width: 100% !important;\n    min-width: 100% !important;\n    width: 100% !important;\n  }\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"],
      data: {
        animation: [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.trigger)('terminalAnimation', [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.state)('hidden', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.style)({
          width: '10vw',
          minWidth: '0',
          height: '10vh',
          opacity: 0
        })), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.state)('small', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.style)({
          width: '30vw',
          height: '30vh'
        })), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.state)('large', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.style)({
          width: '50vw',
          height: '50vh'
        })), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.transition)('small <=> large', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.animate)('1000ms ease-in-out')), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.transition)('small <=> hidden', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.animate)('1000ms ease-in-out'))]), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.trigger)('shrinkAnimation', [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.state)('normal', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.style)({
          opacity: 1,
          maxHeight: '35vh'
        })), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.state)('shrunk', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.style)({
          opacity: 0,
          maxHeight: '0vh'
        })), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.state)('hidden', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.style)({
          opacity: 0,
          maxHeight: '0vh'
        })), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.transition)('normal <=> shrunk', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.animate)('1000ms ease-in-out'))])]
      }
    });
  }
}

/***/ }),

/***/ 4288:
/*!************************************************************************!*\
  !*** ./src/app/leaderboard/embedded/leaderboard-embedded.component.ts ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Cooldown: () => (/* binding */ Cooldown),
/* harmony export */   EmbeddedLeaderboardComponent: () => (/* binding */ EmbeddedLeaderboardComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _services_score_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../services/score.service */ 516);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common */ 316);



function EmbeddedLeaderboardComponent_div_0_ng_container_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, "Commands");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](3, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](4, "Streak");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](6, "Avg. Length");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](7, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](8, "Strokes");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
}
function EmbeddedLeaderboardComponent_div_0_ng_container_10_tr_1_td_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const i_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2).index;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"]("", i_r5 + 1, ".");
  }
}
function EmbeddedLeaderboardComponent_div_0_ng_container_10_tr_1_td_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "\uD83E\uDD47");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function EmbeddedLeaderboardComponent_div_0_ng_container_10_tr_1_td_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "\uD83E\uDD48");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function EmbeddedLeaderboardComponent_div_0_ng_container_10_tr_1_td_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "\uD83E\uDD49");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function EmbeddedLeaderboardComponent_div_0_ng_container_10_tr_1_ng_container_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "a", 6);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const score_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2).$implicit;
    const ctx_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("href", ctx_r11.getUrl(score_r4.code), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](score_r4.name);
  }
}
function EmbeddedLeaderboardComponent_div_0_ng_container_10_tr_1_ng_container_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const score_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2).$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", score_r4.name, " ");
  }
}
function EmbeddedLeaderboardComponent_div_0_ng_container_10_tr_1_ng_container_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "td", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](3, "td", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "td", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpipe"](7, "number");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](8, "td", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpipe"](10, "number");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const score_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2).$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", score_r4.x.count, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", score_r4.x.maxStreak, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpipeBind2"](7, 4, score_r4.x.avgLength, "1.2-2"), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"]("", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpipeBind2"](10, 7, score_r4.x.speed, "1.2-2"), "/s");
  }
}
function EmbeddedLeaderboardComponent_div_0_ng_container_10_tr_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "tr");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](1, EmbeddedLeaderboardComponent_div_0_ng_container_10_tr_1_td_1_Template, 2, 1, "td", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, EmbeddedLeaderboardComponent_div_0_ng_container_10_tr_1_td_2_Template, 2, 0, "td", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](3, EmbeddedLeaderboardComponent_div_0_ng_container_10_tr_1_td_3_Template, 2, 0, "td", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](4, EmbeddedLeaderboardComponent_div_0_ng_container_10_tr_1_td_4_Template, 2, 0, "td", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](6, EmbeddedLeaderboardComponent_div_0_ng_container_10_tr_1_ng_container_6_Template, 3, 2, "ng-container", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](7, EmbeddedLeaderboardComponent_div_0_ng_container_10_tr_1_ng_container_7_Template, 2, 1, "ng-container", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](8, "td", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](10, EmbeddedLeaderboardComponent_div_0_ng_container_10_tr_1_ng_container_10_Template, 11, 10, "ng-container", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r18 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    const i_r5 = ctx_r18.index;
    const score_r4 = ctx_r18.$implicit;
    const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassProp"]("entry-row", true)("self", i_r5 === ctx_r6.getScoreIndex());
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", i_r5 >= 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", i_r5 === 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", i_r5 === 1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", i_r5 === 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", score_r4.code && !ctx_r6.embedded);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", !score_r4.code || ctx_r6.embedded);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", score_r4.score, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r6.advanced);
  }
}
function EmbeddedLeaderboardComponent_div_0_ng_container_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](1, EmbeddedLeaderboardComponent_div_0_ng_container_10_tr_1_Template, 11, 12, "tr", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const i_r5 = ctx.index;
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", i_r5 < ctx_r2.onlyTop);
  }
}
function EmbeddedLeaderboardComponent_div_0_ng_container_11_ng_container_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "td", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, "...");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](3, "td", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](4, "...");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "td", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](6, "...");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](7, "td", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](8, "...");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
}
function EmbeddedLeaderboardComponent_div_0_ng_container_11_ng_container_9_ng_container_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "td", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](3, "td", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "td", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpipe"](7, "number");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](8, "td", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpipe"](10, "number");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const score_r21 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", score_r21.x.count, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", score_r21.x.maxStreak, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpipeBind2"](7, 4, score_r21.x.avgLength, "1.2-2"), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"]("", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpipeBind2"](10, 7, score_r21.x.speed, "1.2-2"), "/s");
  }
}
function EmbeddedLeaderboardComponent_div_0_ng_container_11_ng_container_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "tr")(2, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](6, "td", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](8, EmbeddedLeaderboardComponent_div_0_ng_container_11_ng_container_9_ng_container_8_Template, 11, 10, "ng-container", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const score_r21 = ctx.$implicit;
    const i_r22 = ctx.index;
    const ctx_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassProp"]("entry-row", true)("self", i_r22 === ctx_r20.getLocalScoreIndex());
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", ctx_r20.leaderboardWithLocalPlacement.placement + i_r22 - ctx_r20.getLocalScoreIndex() + 1, ". ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", score_r21.name, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", score_r21.score, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r20.advanced);
  }
}
function EmbeddedLeaderboardComponent_div_0_ng_container_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "tr", 7)(2, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3, "...");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](5, "...");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](6, "td", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](7, "...");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](8, EmbeddedLeaderboardComponent_div_0_ng_container_11_ng_container_8_Template, 9, 0, "ng-container", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](9, EmbeddedLeaderboardComponent_div_0_ng_container_11_ng_container_9_Template, 9, 8, "ng-container", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r3.advanced);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngForOf", ctx_r3.getLocalScores());
  }
}
function EmbeddedLeaderboardComponent_div_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 1)(1, "table")(2, "tr")(3, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](4, "Rank");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](6, "Name");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](7, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](8, "Score");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](9, EmbeddedLeaderboardComponent_div_0_ng_container_9_Template, 9, 0, "ng-container", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](10, EmbeddedLeaderboardComponent_div_0_ng_container_10_Template, 2, 1, "ng-container", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](11, EmbeddedLeaderboardComponent_div_0_ng_container_11_Template, 10, 2, "ng-container", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r0.advanced);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngForOf", ctx_r0.getScores());
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r0.hasLocalScores());
  }
}
class Cooldown {}
class EmbeddedLeaderboardComponent {
  constructor(scoreService) {
    this.scoreService = scoreService;
    this.onlyTop = 10;
    this.embedded = false;
    this.advanced = false;
  }
  ngAfterViewInit() {
    this.scoreService.healthSubject.subscribe(ready => {
      if (!ready) {
        return;
      }
      this.scoreService.getLeaderboard('all').subscribe(l => {
        this.leaderboard = l;
      });
    });
  }
  ngOnChanges() {
    if (this.leaderboardWithLocalPlacementInput && this.score) {
      this.leaderboardWithLocalPlacement = structuredClone(this.leaderboardWithLocalPlacementInput);
      if (this.leaderboardWithLocalPlacement.placement < 10) {
        this.onlyTop = this.showLocalScores ? 10 : 5;
        this.leaderboardWithLocalPlacement.scores.push(this.score);
        this.leaderboardWithLocalPlacement.scores = this.leaderboardWithLocalPlacement.scores.sort((a, b) => b.score - a.score);
      } else {
        this.leaderboardWithLocalPlacement.localscores.push(this.score);
        this.leaderboardWithLocalPlacement.localscores = this.leaderboardWithLocalPlacement.localscores.sort((a, b) => b.score - a.score);
      }
      this.leaderboard = {
        scores: this.leaderboardWithLocalPlacement.scores,
        language: this.leaderboardWithLocalPlacement.language
      };
    }
  }
  getScores() {
    return this.leaderboard?.scores.slice(0, 10) ?? [];
  }
  hasLocalScores() {
    if (!this.showLocalScores) {
      return false;
    }
    return this.leaderboardWithLocalPlacement?.localscores.length > 0;
  }
  getLocalScores() {
    return this.hasLocalScores() ? this.leaderboardWithLocalPlacement.localscores : [];
  }
  getScoreIndex() {
    if (!this.leaderboardWithLocalPlacement || this.leaderboardWithLocalPlacement.placement >= 10) {
      return -1;
    }
    return this.leaderboardWithLocalPlacement.placement;
  }
  getLocalScoreIndex() {
    if (this.leaderboardWithLocalPlacement.placement < 10) {
      return -1;
    }
    const scoreIndex = this.leaderboardWithLocalPlacement.localscores.findIndex(localScore => this.score.name === localScore.name && this.score.score === localScore.score);
    return scoreIndex;
  }
  getCodeBase64(code) {
    return btoa(code);
  }
  getUrl(code) {
    return this.scoreService.getServer() + '/score/qrcode/' + this.getCodeBase64(code);
  }
  static {
    this.ɵfac = function EmbeddedLeaderboardComponent_Factory(t) {
      return new (t || EmbeddedLeaderboardComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_services_score_service__WEBPACK_IMPORTED_MODULE_0__.ScoreService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: EmbeddedLeaderboardComponent,
      selectors: [["app-leaderboard-embdedded"]],
      inputs: {
        onlyTop: "onlyTop",
        embedded: "embedded",
        advanced: "advanced",
        showLocalScores: "showLocalScores",
        score: "score",
        leaderboardWithLocalPlacementInput: "leaderboardWithLocalPlacementInput"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵNgOnChangesFeature"]],
      decls: 1,
      vars: 1,
      consts: [["class", "leaderboard", 4, "ngIf"], [1, "leaderboard"], [4, "ngIf"], [4, "ngFor", "ngForOf"], [3, "entry-row", "self", 4, "ngIf"], [1, "score"], [3, "href"], [1, "entry-row", "divider"]],
      template: function EmbeddedLeaderboardComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](0, EmbeddedLeaderboardComponent_div_0_Template, 12, 3, "div", 0);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.getScores().length > 0);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_2__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_2__.NgIf, _angular_common__WEBPACK_IMPORTED_MODULE_2__.DecimalPipe],
      styles: [".brawl-color[_ngcontent-%COMP%] {\n  color: #74e2cd;\n}\n\n.leaderboard[_ngcontent-%COMP%] {\n  color: white;\n  font-family: monospace;\n  padding-top: 20px;\n}\n.leaderboard[_ngcontent-%COMP%]   .score[_ngcontent-%COMP%] {\n  text-align: right;\n}\n.leaderboard[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\n  margin-left: auto;\n  margin-right: auto;\n}\n.leaderboard[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\n  padding: 0 10px;\n}\n.leaderboard[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:nth-child(even) {\n  background: #02213d;\n}\n.leaderboard[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:nth-child(odd) {\n  background: #01162a;\n}\n.leaderboard[_ngcontent-%COMP%]   .entry-row[_ngcontent-%COMP%] {\n  font-size: 2.5vh;\n  line-height: 2vh;\n}\n.leaderboard[_ngcontent-%COMP%]   .entry-row.divider[_ngcontent-%COMP%] {\n  line-height: 1.5vh;\n}\n.leaderboard[_ngcontent-%COMP%]   .entry-row[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\n  padding: 10px;\n}\n.leaderboard[_ngcontent-%COMP%]   .entry-row[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  color: #74e2cd;\n  font-weight: bold;\n}\n.leaderboard[_ngcontent-%COMP%]   .entry-row.self[_ngcontent-%COMP%] {\n  background-color: #74e2cd;\n  font-weight: bold;\n  color: #01162a;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbGVhZGVyYm9hcmQvZW1iZWRkZWQvbGVhZGVyYm9hcmQtZW1iZWRkZWQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxjQUFBO0FBQ0Y7O0FBRUE7RUFDRSxZQUFBO0VBQ0Esc0JBQUE7RUFDQSxpQkFBQTtBQUNGO0FBQ0U7RUFDRSxpQkFBQTtBQUNKO0FBRUU7RUFDRSxpQkFBQTtFQUNBLGtCQUFBO0FBQUo7QUFHRTtFQUNFLGVBQUE7QUFESjtBQUlFO0VBQ0UsbUJBQUE7QUFGSjtBQUlFO0VBQ0UsbUJBQUE7QUFGSjtBQUtFO0VBQ0UsZ0JBQUE7RUFDQSxnQkFBQTtBQUhKO0FBSUk7RUFDRSxrQkFBQTtBQUZOO0FBSUk7RUFDRSxhQUFBO0FBRk47QUFHTTtFQUNFLGNBQUE7RUFDQSxpQkFBQTtBQURSO0FBSUk7RUFDRSx5QkFBQTtFQUNBLGlCQUFBO0VBQ0EsY0FBQTtBQUZOIiwic291cmNlc0NvbnRlbnQiOlsiLmJyYXdsLWNvbG9yIHtcbiAgY29sb3I6ICM3NGUyY2Q7XG59XG5cbi5sZWFkZXJib2FyZCB7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgZm9udC1mYW1pbHk6IG1vbm9zcGFjZTtcbiAgcGFkZGluZy10b3A6IDIwcHg7XG5cbiAgLnNjb3JlIHtcbiAgICB0ZXh0LWFsaWduOiByaWdodDtcbiAgfVxuXG4gIHRhYmxlIHtcbiAgICBtYXJnaW4tbGVmdDogYXV0bztcbiAgICBtYXJnaW4tcmlnaHQ6IGF1dG87XG4gIH1cblxuICB0aCB7XG4gICAgcGFkZGluZzogMCAxMHB4O1xuICB9XG5cbiAgdHI6bnRoLWNoaWxkKGV2ZW4pIHtcbiAgICBiYWNrZ3JvdW5kOiAjMDIyMTNkO1xuICB9XG4gIHRyOm50aC1jaGlsZChvZGQpIHtcbiAgICBiYWNrZ3JvdW5kOiAjMDExNjJhO1xuICB9XG5cbiAgLmVudHJ5LXJvdyB7XG4gICAgZm9udC1zaXplOiAyLjV2aDtcbiAgICBsaW5lLWhlaWdodDogMnZoO1xuICAgICYuZGl2aWRlciB7XG4gICAgICBsaW5lLWhlaWdodDogMS41dmg7XG4gICAgfVxuICAgIHRkIHtcbiAgICAgIHBhZGRpbmc6IDEwcHg7XG4gICAgICBhIHtcbiAgICAgICAgY29sb3I6ICM3NGUyY2Q7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xuICAgICAgfVxuICAgIH1cbiAgICAmLnNlbGYge1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzc0ZTJjZDtcbiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xuICAgICAgY29sb3I6ICMwMTE2MmE7XG4gICAgfVxuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 4568:
/*!******************************************************!*\
  !*** ./src/app/leaderboard/leaderboard.component.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Cooldown: () => (/* binding */ Cooldown),
/* harmony export */   LeaderboardComponent: () => (/* binding */ LeaderboardComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _embedded_leaderboard_embedded_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./embedded/leaderboard-embedded.component */ 4288);


class Cooldown {}
class LeaderboardComponent {
  static {
    this.ɵfac = function LeaderboardComponent_Factory(t) {
      return new (t || LeaderboardComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: LeaderboardComponent,
      selectors: [["app-leaderboard"]],
      decls: 4,
      vars: 0,
      consts: [[1, "leaderboard"], [1, "score-container"], ["src", "/assets/bashbrawl/bashbrawl_text.png"]],
      template: function LeaderboardComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0)(1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](2, "img", 2)(3, "app-leaderboard-embdedded");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
        }
      },
      dependencies: [_embedded_leaderboard_embedded_component__WEBPACK_IMPORTED_MODULE_0__.EmbeddedLeaderboardComponent],
      styles: [".brawl-color[_ngcontent-%COMP%] {\n  color: #74e2cd;\n}\n\n.leaderboard[_ngcontent-%COMP%] {\n  height: calc(100% + 2.4rem) !important;\n  margin: -1.2rem;\n  background-color: #01162a;\n  color: white;\n  padding: 0px 0px 24px 0;\n  font-family: monospace;\n  display: flex;\n  flex-flow: column;\n}\n\n.score-container[_ngcontent-%COMP%] {\n  margin-left: auto;\n  margin-right: auto;\n  display: block;\n}\n.score-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  margin-top: 5vh;\n  margin-bottom: 5vh;\n  max-height: 30vh;\n  margin-left: auto;\n  margin-right: auto;\n  display: block;\n}\n.score-container[_ngcontent-%COMP%]   .score[_ngcontent-%COMP%] {\n  text-align: right;\n}\n.score-container[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:nth-child(even) {\n  background: #02213d;\n}\n.score-container[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:nth-child(odd) {\n  background: #01162a;\n}\n.score-container[_ngcontent-%COMP%]   .entry-row[_ngcontent-%COMP%] {\n  font-size: 24px;\n}\n.score-container[_ngcontent-%COMP%]   .entry-row[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\n  padding: 10px;\n}\n.score-container[_ngcontent-%COMP%]   .entry-row[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  color: #74e2cd;\n  font-weight: bold;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbGVhZGVyYm9hcmQvbGVhZGVyYm9hcmQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxjQUFBO0FBQ0Y7O0FBRUE7RUFDRSxzQ0FBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLFlBQUE7RUFDQSx1QkFBQTtFQUNBLHNCQUFBO0VBQ0EsYUFBQTtFQUNBLGlCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EsY0FBQTtBQUNGO0FBQ0U7RUFDRSxlQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0FBQ0o7QUFFRTtFQUNFLGlCQUFBO0FBQUo7QUFHRTtFQUNFLG1CQUFBO0FBREo7QUFHRTtFQUNFLG1CQUFBO0FBREo7QUFJRTtFQUNFLGVBQUE7QUFGSjtBQUdJO0VBQ0UsYUFBQTtBQUROO0FBRU07RUFDRSxjQUFBO0VBQ0EsaUJBQUE7QUFBUiIsInNvdXJjZXNDb250ZW50IjpbIi5icmF3bC1jb2xvciB7XG4gIGNvbG9yOiAjNzRlMmNkO1xufVxuXG4ubGVhZGVyYm9hcmQge1xuICBoZWlnaHQ6IGNhbGMoMTAwJSArIDIuNHJlbSkgIWltcG9ydGFudDtcbiAgbWFyZ2luOiAtMS4ycmVtO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMDExNjJhO1xuICBjb2xvcjogd2hpdGU7XG4gIHBhZGRpbmc6IDBweCAwcHggMjRweCAwO1xuICBmb250LWZhbWlseTogbW9ub3NwYWNlO1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWZsb3c6IGNvbHVtbjtcbn1cblxuLnNjb3JlLWNvbnRhaW5lciB7XG4gIG1hcmdpbi1sZWZ0OiBhdXRvO1xuICBtYXJnaW4tcmlnaHQ6IGF1dG87XG4gIGRpc3BsYXk6IGJsb2NrO1xuXG4gIGltZyB7XG4gICAgbWFyZ2luLXRvcDogNXZoO1xuICAgIG1hcmdpbi1ib3R0b206IDV2aDtcbiAgICBtYXgtaGVpZ2h0OiAzMHZoO1xuICAgIG1hcmdpbi1sZWZ0OiBhdXRvO1xuICAgIG1hcmdpbi1yaWdodDogYXV0bztcbiAgICBkaXNwbGF5OiBibG9jaztcbiAgfVxuXG4gIC5zY29yZSB7XG4gICAgdGV4dC1hbGlnbjogcmlnaHQ7XG4gIH1cblxuICB0cjpudGgtY2hpbGQoZXZlbikge1xuICAgIGJhY2tncm91bmQ6ICMwMjIxM2Q7XG4gIH1cbiAgdHI6bnRoLWNoaWxkKG9kZCkge1xuICAgIGJhY2tncm91bmQ6ICMwMTE2MmE7XG4gIH1cblxuICAuZW50cnktcm93IHtcbiAgICBmb250LXNpemU6IDI0cHg7XG4gICAgdGQge1xuICAgICAgcGFkZGluZzogMTBweDtcbiAgICAgIGEge1xuICAgICAgICBjb2xvcjogIzc0ZTJjZDtcbiAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 810:
/*!************************************!*\
  !*** ./src/app/pipes/atob.pipe.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AtobPipe: () => (/* binding */ AtobPipe)
/* harmony export */ });
/* harmony import */ var _unicode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../unicode */ 8801);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);


class AtobPipe {
  transform(value) {
    if (value) {
      return (0,_unicode__WEBPACK_IMPORTED_MODULE_0__.atou)(value);
    }
  }
  static {
    this.ɵfac = function AtobPipe_Factory(t) {
      return new (t || AtobPipe)();
    };
  }
  static {
    this.ɵpipe = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefinePipe"]({
      name: "atob",
      type: AtobPipe,
      pure: true
    });
  }
}

/***/ }),

/***/ 9128:
/*!*****************************************!*\
  !*** ./src/app/pipes/uppercase.pipe.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   UppercasePipe: () => (/* binding */ UppercasePipe)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 7580);

class UppercasePipe {
  transform(value) {
    if (value) {
      return value.toUpperCase();
    }
  }
  static {
    this.ɵfac = function UppercasePipe_Factory(t) {
      return new (t || UppercasePipe)();
    };
  }
  static {
    this.ɵpipe = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefinePipe"]({
      name: "uppercase",
      type: UppercasePipe,
      pure: true
    });
  }
}

/***/ }),

/***/ 1469:
/*!***********************************!*\
  !*** ./src/app/root.component.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   RootComponent: () => (/* binding */ RootComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/platform-browser */ 436);
/* harmony import */ var _app_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app.component */ 92);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 5072);




class RootComponent {
  constructor(titleService, appComponent) {
    this.titleService = titleService;
    this.appComponent = appComponent;
    this.titleService.setTitle(this.appComponent.title);
  }
  static {
    this.ɵfac = function RootComponent_Factory(t) {
      return new (t || RootComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_platform_browser__WEBPACK_IMPORTED_MODULE_2__.Title), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_app_component__WEBPACK_IMPORTED_MODULE_0__.AppComponent));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: RootComponent,
      selectors: [["app-root"]],
      decls: 1,
      vars: 0,
      template: function RootComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](0, "router-outlet");
        }
      },
      dependencies: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterOutlet],
      encapsulation: 2
    });
  }
}

/***/ }),

/***/ 9028:
/*!******************************************!*\
  !*** ./src/app/scans/scans.component.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ScansComponent: () => (/* binding */ ScansComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _services_score_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../services/score.service */ 516);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common */ 316);



function ScansComponent_div_0_ng_container_2_td_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const i_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]().index;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"]("", i_r3, ".");
  }
}
function ScansComponent_div_0_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "tr", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, ScansComponent_div_0_ng_container_2_td_2_Template, 2, 1, "td", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](3, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](6, "img", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](7, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const scan_r2 = ctx.$implicit;
    const i_r3 = ctx.index;
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", i_r3 >= 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("src", ctx_r1.getUrl(scan_r2), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](ctx_r1.getCodeBase64Decoded(scan_r2));
  }
}
function ScansComponent_div_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 1)(1, "table");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, ScansComponent_div_0_ng_container_2_Template, 9, 3, "ng-container", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngForOf", ctx_r0.getScans());
  }
}
class ScansComponent {
  constructor(scoreService) {
    this.scoreService = scoreService;
  }
  ngAfterViewInit() {
    this.scoreService.healthSubject.subscribe(ready => {
      if (!ready) {
        return;
      }
      this.scoreService.getScannedCodes().subscribe(l => {
        this.scans = l;
      });
    });
  }
  getCodeBase64Decoded(code) {
    return atob(code);
  }
  getScans() {
    return this.scans ?? [];
  }
  getUrl(code) {
    return this.scoreService.getServer() + '/score/qrcode/' + code;
  }
  static {
    this.ɵfac = function ScansComponent_Factory(t) {
      return new (t || ScansComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_services_score_service__WEBPACK_IMPORTED_MODULE_0__.ScoreService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: ScansComponent,
      selectors: [["app-scans"]],
      decls: 1,
      vars: 1,
      consts: [["class", "scores", 4, "ngIf"], [1, "scores"], [4, "ngFor", "ngForOf"], [1, "entry-row"], [4, "ngIf"], [3, "src"]],
      template: function ScansComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](0, ScansComponent_div_0_Template, 3, 1, "div", 0);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.getScans().length > 0);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_2__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_2__.NgIf],
      styles: [".brawl-color[_ngcontent-%COMP%] {\n  color: #74e2cd;\n}\n\n.scores[_ngcontent-%COMP%] {\n  height: calc(100% + 2.4rem) !important;\n  margin: -1.2rem;\n  background-color: #01162a;\n  color: white;\n  padding: 0px 0px 24px 0;\n  font-family: monospace;\n  display: flex;\n  flex-flow: column;\n}\n\ntd[_ngcontent-%COMP%] {\n  padding-bottom: 20px;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2NhbnMvc2NhbnMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxjQUFBO0FBQ0Y7O0FBRUE7RUFDRSxzQ0FBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLFlBQUE7RUFDQSx1QkFBQTtFQUNBLHNCQUFBO0VBQ0EsYUFBQTtFQUNBLGlCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxvQkFBQTtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLmJyYXdsLWNvbG9yIHtcbiAgY29sb3I6ICM3NGUyY2Q7XG59XG5cbi5zY29yZXMge1xuICBoZWlnaHQ6IGNhbGMoMTAwJSArIDIuNHJlbSkgIWltcG9ydGFudDtcbiAgbWFyZ2luOiAtMS4ycmVtO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMDExNjJhO1xuICBjb2xvcjogd2hpdGU7XG4gIHBhZGRpbmc6IDBweCAwcHggMjRweCAwO1xuICBmb250LWZhbWlseTogbW9ub3NwYWNlO1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWZsb3c6IGNvbHVtbjtcbn1cblxudGQge1xuICBwYWRkaW5nLWJvdHRvbTogMjBweDtcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */"]
    });
  }
}

/***/ }),

/***/ 3366:
/*!*****************************************!*\
  !*** ./src/app/services/api.service.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   APIClientFactory: () => (/* binding */ APIClientFactory),
/* harmony export */   extractResponseContent: () => (/* binding */ extractResponseContent)
/* harmony export */ });
/* harmony import */ var _unicode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../unicode */ 8801);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common/http */ 6443);



class APIClientFactory {
  constructor(http) {
    this.http = http;
  }
  scopedClient(endpoint, prefix) {
    const baseUrl = endpoint + prefix;
    return this.buildPRoxy(baseUrl);
  }
  buildPRoxy(baseUrl) {
    return new Proxy(this.http, {
      get(target, key) {
        const prop = target[key];
        return typeof prop === 'function' ? (path, ...args) => prop.call(target, baseUrl + path, ...args) : prop;
      }
    });
  }
  static {
    this.ɵfac = function APIClientFactory_Factory(t) {
      return new (t || APIClientFactory)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_2__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
      token: APIClientFactory,
      factory: APIClientFactory.ɵfac
    });
  }
}
const extractResponseContent = s => JSON.parse((0,_unicode__WEBPACK_IMPORTED_MODULE_0__.atou)(s.content));

/***/ }),

/***/ 516:
/*!*******************************************!*\
  !*** ./src/app/services/score.service.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ScoreService: () => (/* binding */ ScoreService)
/* harmony export */ });
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var _api_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api.service */ 3366);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs/operators */ 271);
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/environments/environment */ 5312);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rxjs */ 5797);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs */ 9452);
/* harmony import */ var _unicode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../unicode */ 8801);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/core */ 7580);








class ScoreService {
  constructor(gcf) {
    this.useLocal = true;
    this.healthSubject = new rxjs__WEBPACK_IMPORTED_MODULE_3__.BehaviorSubject(false);
    this.garg = gcf.scopedClient(this.getServer(), '/score');
    this.validateHealth();
  }
  getLeaderboard(language) {
    if (!language || language == '') {
      language = 'all';
    }
    if (this.useLocal) {
      // Receive from leaderboard local storage
      let l = {
        language: language,
        scores: []
      };
      const jsonLeaderboard = localStorage.getItem('leaderboard_' + language) ?? '';
      if (jsonLeaderboard && jsonLeaderboard != '') {
        l = JSON.parse(jsonLeaderboard);
      }
      l.scores = l.scores.sort((a, b) => b.score - a.score);
      l.scores = l.scores.splice(0, 10);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.of)(l);
    }
    return this.garg.get('/leaderboard/' + language).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(_api_service__WEBPACK_IMPORTED_MODULE_0__.extractResponseContent));
  }
  setScoreForLanguage(language, score) {
    if (!language || language == '') {
      language = 'all';
    }
    if (this.useLocal) {
      // Store in local leaderboard
      // Get leaderboard
      // add score
      // Store leaderboard
      let l = {
        language: language,
        scores: []
      };
      let jsonLeaderboard = localStorage.getItem('leaderboard_' + language) ?? '';
      if (jsonLeaderboard && jsonLeaderboard != '') {
        l = JSON.parse(jsonLeaderboard);
      }
      const localPlacementLeaderboard = this.findLocalScores(l, score);
      l.scores.push(score);
      // TODO calculate local placement
      jsonLeaderboard = JSON.stringify(l);
      localStorage.setItem('leaderboard_' + language, jsonLeaderboard);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.of)(localPlacementLeaderboard);
    }
    // Set headers for JSON
    const headers = new _angular_common_http__WEBPACK_IMPORTED_MODULE_6__.HttpHeaders({
      'Content-Type': 'application/json'
    });
    return this.garg.post('/add/' + language, score, {
      headers
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(_api_service__WEBPACK_IMPORTED_MODULE_0__.extractResponseContent));
  }
  scan(code) {
    if (this.useLocal) {
      // scanning in local environment does not make any sence.
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.of)(true);
    }
    code = (0,_unicode__WEBPACK_IMPORTED_MODULE_2__.utoa)(code);
    return this.garg.post('/scan/' + code, false).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(_api_service__WEBPACK_IMPORTED_MODULE_0__.extractResponseContent));
  }
  getScannedCodes() {
    if (this.useLocal) {
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.of)([]);
    }
    return this.garg.get('/scan').pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(_api_service__WEBPACK_IMPORTED_MODULE_0__.extractResponseContent));
  }
  validateHealth() {
    const server = this.getServer();
    if (!server || !server.startsWith('http')) {
      this.useLocal = true;
      this.healthSubject.next(true);
      return this.healthSubject.asObservable();
    }
    this.healthz().subscribe({
      next: () => {
        this.useLocal = false;
        this.healthSubject.next(true);
      },
      error: () => {
        this.useLocal = true;
        this.healthSubject.next(true);
        console.log('Server ' + server + ' seems unhealthy, defaulting to local storage');
      }
    });
    return this.healthSubject.asObservable();
  }
  healthz() {
    return this.garg.get('/healthz');
  }
  getServer() {
    return localStorage.getItem('score_server') ?? src_environments_environment__WEBPACK_IMPORTED_MODULE_1__.environment.server;
  }
  findLocalScores(leaderboard, newScore) {
    // Create a deep copy of the scores and add the new score for sorting and placement
    const scores = [...leaderboard.scores];
    // Sort the scores array by score in descending order
    scores.sort((a, b) => b.score - a.score);
    // Determine the placement (number of scores greater than the current score)
    const placement = scores.filter(s => s.score >= newScore.score).length;
    // Create an array to hold local scores
    const localScores = [];
    // Get two scores above the new score (if available)
    if (placement >= 10) {
      for (let i = placement - 1; i >= 0 && i >= placement - 2 && i >= 10; i--) {
        localScores.push(scores[i]);
      }
      for (let i = placement; i < scores.length && localScores.length < 4; i++) {
        localScores.push(scores[i]);
      }
    }
    // Return the updated leaderboard object with localScores and placement
    return {
      language: leaderboard.language,
      scores: scores,
      localscores: localScores,
      placement: placement
    };
  }
  static {
    this.ɵfac = function ScoreService_Factory(t) {
      return new (t || ScoreService)(_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵinject"](_api_service__WEBPACK_IMPORTED_MODULE_0__.APIClientFactory));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdefineInjectable"]({
      token: ScoreService,
      factory: ScoreService.ɵfac
    });
  }
}

/***/ }),

/***/ 875:
/*!**********************************************!*\
  !*** ./src/app/services/settings.service.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SettingsService: () => (/* binding */ SettingsService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs */ 819);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rxjs */ 4665);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs */ 9452);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs/operators */ 6301);
/* harmony import */ var _terminals_terminal_themes_themes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../terminals/terminal-themes/themes */ 1710);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _api_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api.service */ 3366);





class SettingsService {
  constructor(gcf) {
    this.gcf = gcf;
    this.subject = new rxjs__WEBPACK_IMPORTED_MODULE_2__.Subject();
    this.settings$ = (0,rxjs__WEBPACK_IMPORTED_MODULE_3__.concat)(this.fetch(), this.subject).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_4__.shareReplay)(1));
  }
  fetch() {
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)({
      terminal_theme: _terminals_terminal_themes_themes__WEBPACK_IMPORTED_MODULE_0__.themes[0].id,
      terminal_fontSize: 16,
      divider_position: 40
    });
  }
  static {
    this.ɵfac = function SettingsService_Factory(t) {
      return new (t || SettingsService)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵinject"](_api_service__WEBPACK_IMPORTED_MODULE_1__.APIClientFactory));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineInjectable"]({
      token: SettingsService,
      factory: SettingsService.ɵfac
    });
  }
}

/***/ }),

/***/ 6386:
/*!********************************************************************!*\
  !*** ./src/app/terminals/bashbrawl/bashbrawlterminal.component.ts ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BashbrawlterminalComponent: () => (/* binding */ BashbrawlterminalComponent),
/* harmony export */   GameFinished: () => (/* binding */ GameFinished),
/* harmony export */   Leaderboard: () => (/* binding */ Leaderboard),
/* harmony export */   LeaderboardWithLocalPlacement: () => (/* binding */ LeaderboardWithLocalPlacement),
/* harmony export */   Score: () => (/* binding */ Score)
/* harmony export */ });
/* harmony import */ var _Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 9204);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var xterm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! xterm */ 883);
/* harmony import */ var xterm__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(xterm__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var xterm_addon_fit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! xterm-addon-fit */ 7295);
/* harmony import */ var xterm_addon_fit__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(xterm_addon_fit__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _terminal_themes_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../terminal-themes/themes */ 1710);
/* harmony import */ var xterm_addon_canvas__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! xterm-addon-canvas */ 3165);
/* harmony import */ var xterm_addon_canvas__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(xterm_addon_canvas__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _keycodes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./keycodes */ 7518);
/* harmony import */ var _cds_core_internal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @cds/core/internal */ 4902);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rxjs */ 6196);
/* harmony import */ var _services_settings_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../services/settings.service */ 875);
/* harmony import */ var _languages_language_command_service__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./languages/language-command.service */ 3818);
/* harmony import */ var _services_score_service__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../services/score.service */ 516);













const _c0 = ["terminal"];
class Score {}
class Leaderboard {}
class LeaderboardWithLocalPlacement {}
class GameFinished {}
// eslint-disable-next-line no-control-regex
const stripAnsi = str => str.replace(/\x1b\[[0-9;]*m/g, '');
class BashbrawlterminalComponent {
  constructor(settingsService, languageCommandService, scoreService) {
    this.settingsService = settingsService;
    this.languageCommandService = languageCommandService;
    this.scoreService = scoreService;
    this.code = '';
    this.gameEnded = new _angular_core__WEBPACK_IMPORTED_MODULE_9__.EventEmitter();
    this.gameStarted = new _angular_core__WEBPACK_IMPORTED_MODULE_9__.EventEmitter();
    this.fitAddon = new xterm_addon_fit__WEBPACK_IMPORTED_MODULE_2__.FitAddon();
    this.firstTabChange = true;
    this.isVisible = false;
    this.command = '';
    this.commandFn = this.menuCommandsFn;
    this.inputFn = this.handleCommandWithNewline;
    this.cursorPosition = 0;
    this.DEFAULT_FONT_SIZE = 16;
    this.DEFAULT_TERMINAL_THEME = 'default';
    this.DEFAULT_TERMINAL_SYMBOL = '#';
    this.terminalSymbol = '#';
    this.input_blocked = true;
    this.interrupted = false;
    this.TERMINAL_CHAR_DELAY = 40;
    this.TERMINAL_WHITESPACE_DELAY = 2;
    this.FIRE_COMMANDS_BASE = 3; // commands threshold to be entered in FIRE_COMMANDS_TIMERANGE
    this.FIRE_COMMANDS_TIMERANGE = 7; // seconds
    // Game related
    this.DEFAULT_GAME_TIME = 60;
    this.gameRunning = false;
    this.commandsEntered = [];
    this.commandsEnteredAtTimepoint = [];
    this.gameTime = 0; // How many seconds are left to play
    this.streak = 0; // Current streak
    this.score = 0; // Current score
    this.highestStreak = 0; // Highest Streak achieved
    this.keysPressed = 0; // Total number of keys pressed, used to calculate strokes / second
    this.keysPressedInGame = 0; // Used to save the number at the end of the game to avoid numbers being counted entered in the name field
    this.totalCommandLength = 0; // Used to calculate avg Length of commands entered;
  }

  resize() {
    const newDimensions = this.fitAddon.proposeDimensions();
    if (this.isVisible && newDimensions) {
      this.fitAddon.fit();
    }
  }
  buildTerminal() {
    // Check if current browser is firefox by useragent and use "duck-typing" as a fallback.
    const regExp = /firefox|fxios/i;
    const isFirefox = regExp.test(navigator.userAgent.toLowerCase()) || 'InstallTrigger' in window;
    this.term = new xterm__WEBPACK_IMPORTED_MODULE_1__.Terminal({
      fontFamily: 'monospace',
      fontSize: this.DEFAULT_FONT_SIZE,
      letterSpacing: 1.1,
      cursorBlink: true
    });
    if (!isFirefox) {
      // The default renderer is the dom renderer
      // Use the more performant canvas renderer if the current browser is not Firefox
      this.term.loadAddon(new xterm_addon_canvas__WEBPACK_IMPORTED_MODULE_4__.CanvasAddon());
    }
    this.settingsService.settings$.subscribe(({
      terminal_theme = this.DEFAULT_TERMINAL_THEME
    }) => {
      this.setTerminalTheme(terminal_theme);
    });
    this.settingsService.settings$.subscribe(({
      terminal_fontSize = this.DEFAULT_FONT_SIZE
    }) => {
      this.setFontSize(terminal_fontSize);
    });
    this.term.loadAddon(this.fitAddon);
    this.term.open(this.terminalDiv.nativeElement);
    this.term.focus();
    this.resize();
    this.resetToDefaultShell();
    this.term.write(` ${this.terminalSymbol} `);
    this.term.onData(e => {
      this.keysPressed++;
      if (e === _keycodes__WEBPACK_IMPORTED_MODULE_5__.Keycodes.CTR_C) {
        //this.resetToDefaultShell();
        //this.interrupted = true;
        //if (this.gameRunning) {
        //  this.gameEnded.emit();
        //}
        return;
      }
      if (this.input_blocked) {
        return;
      }
      if (e === _keycodes__WEBPACK_IMPORTED_MODULE_5__.Keycodes.ENTER) {
        this.cursorPosition = 0;
        this.inputFn(this.command.trim());
        this.command = ''; // Reset command buffer
      } else if (e === _keycodes__WEBPACK_IMPORTED_MODULE_5__.Keycodes.BACKSPACE) {
        if (this.command.length > 0) {
          const beforeChar = this.command.slice(0, this.cursorPosition - 1);
          const afterChar = this.command.slice(this.cursorPosition);
          this.term.write('\b' + afterChar + ' \b' + '\b'.repeat(afterChar.length)); // Move cursor back, erase character, move cursor back again
          this.command = beforeChar + afterChar;
          // Remove last character from command buffer
          this.cursorPosition -= 1;
        }
      } else if (e === _keycodes__WEBPACK_IMPORTED_MODULE_5__.Keycodes.DELETE) {
        if (this.command.length > 0) {
          if (this.cursorPosition >= this.command.length) {
            //We are one position behind the command, we can not delete something
            return;
          }
          const beforeChar = this.command.slice(0, this.cursorPosition);
          const afterChar = this.command.slice(this.cursorPosition + 1);
          this.term.write(afterChar + ' \b' + '\b'.repeat(afterChar.length)); // Move cursor back, erase character, move cursor back again
          this.command = beforeChar + afterChar;
        }
      } else if (e === _keycodes__WEBPACK_IMPORTED_MODULE_5__.Keycodes.LEFT_ARROW) {
        if (this.cursorPosition > 0) {
          this.cursorPosition -= 1;
          this.term.write(_keycodes__WEBPACK_IMPORTED_MODULE_5__.Keycodes.LEFT_ARROW);
        }
      } else if (e === _keycodes__WEBPACK_IMPORTED_MODULE_5__.Keycodes.RIGHT_ARROW) {
        if (this.cursorPosition < this.command.length) {
          this.cursorPosition += 1;
          this.term.write(_keycodes__WEBPACK_IMPORTED_MODULE_5__.Keycodes.RIGHT_ARROW);
        }
      } else if (e === _keycodes__WEBPACK_IMPORTED_MODULE_5__.Keycodes.UP_ARROW) {
        // TODO implement some weird logic here
        console.log('UP');
      } else if (e === _keycodes__WEBPACK_IMPORTED_MODULE_5__.Keycodes.DOWN_ARROW) {
        // TODO implement some weird logic here
        console.log('DOWN');
      } else {
        const beforeChar = this.command.slice(0, this.cursorPosition);
        const afterChar = this.command.slice(this.cursorPosition);
        this.term.write(e + afterChar + '\b'.repeat(afterChar.length));
        this.cursorPosition += 1;
        //this.term.write(e); // Echo the typed character
        this.command = beforeChar + e + afterChar; // Add typed character to command buffer
      }
    });
  }

  helpGame() {
    var _this = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      _this.term.writeln('Start the game with one of the following option modes:');
      _this.term.writeln('\nUsage:');
      yield _this.writeMatrix([['brawl play', 'Play with all languages'], ['brawl play [language]', 'Play selected language'], ['brawl lang', 'View all available languages'], ['brawl top', 'View the leaderboard']]);
    })();
  }
  startGame(option) {
    var _this2 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (option && option != '') {
        yield _this2.selectGameOption(option);
        return;
      } else {
        yield _this2.helpGame();
      }
    })();
  }
  confirmBeginGame(language) {
    var _this3 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      _this3.gameStarted.emit();
      if (language == 'all') {
        yield _this3.writeDelayed('You have ' + _this3.DEFAULT_GAME_TIME + ' seconds to input commands from:', true);
        yield _this3.displayAvailableLanguages(false);
      } else {
        yield _this3.writeDelayed('You have ' + _this3.DEFAULT_GAME_TIME + ' seconds to input commands from ' + _this3.languageCommandService.getLanguageNameById(language), true);
      }
      _this3.gameLanguage = language;
      _this3.input_blocked = false;
      _this3.terminalSymbol = `Press Enter to start!`;
      _this3.commandFn = _this3.confirmBeginGameFn;
    })();
  }
  confirmBeginGameFn() {
    var _this4 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      _this4.input_blocked = true;
      yield _this4.beginGame();
    })();
  }
  beginGame() {
    var _this5 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      // prevent multiple games running
      if (_this5.gameRunning) {
        return;
      }
      // set game running flag
      _this5.gameRunning = true;
      // set language array here;
      _this5.score = 0;
      _this5.streak = 0;
      _this5.highestStreak = 0;
      _this5.keysPressed = 0;
      _this5.commandsEntered = [];
      _this5.commandsEnteredAtTimepoint = [];
      _this5.gameTime = _this5.DEFAULT_GAME_TIME;
      _this5.clearTerminal();
      yield _this5.writeScore();
      _this5.terminalSymbol = '>';
      yield _this5.moveToInputLine();
      yield _this5.writeDelayed('Prepare yourself ... ', false);
      yield (0,_cds_core_internal__WEBPACK_IMPORTED_MODULE_10__.sleep)(1000);
      _this5.term.write('3 ');
      yield (0,_cds_core_internal__WEBPACK_IMPORTED_MODULE_10__.sleep)(1000);
      _this5.term.write('2 ');
      yield (0,_cds_core_internal__WEBPACK_IMPORTED_MODULE_10__.sleep)(1000);
      _this5.term.write('1');
      yield (0,_cds_core_internal__WEBPACK_IMPORTED_MODULE_10__.sleep)(1000);
      if (_this5.interrupted) {
        _this5.term.write('\r\n');
        _this5.gameEnded.emit({
          success: false
        });
        return;
      }
      _this5.commandFn = _this5.gameCommand;
      _this5.input_blocked = false;
      if (_this5.interrupted) {
        _this5.gameEnded.emit({
          success: false
        });
        return;
      }
      yield _this5.moveToInputLine();
      _this5.term.write(` ${_this5.terminalSymbol} `);
      while (_this5.gameTime > 0) {
        if (_this5.interrupted) {
          return;
        }
        yield (0,_cds_core_internal__WEBPACK_IMPORTED_MODULE_10__.sleep)(1000);
        _this5.gameTime -= 1;
        yield _this5.writeScore();
      }
      _this5.gameRunning = false;
      _this5.input_blocked = true;
      _this5.command = '';
      _this5.cursorPosition = 0;
      _this5.keysPressedInGame = _this5.keysPressed;
      //this.inputFn = this.handleCommandWithNewline;
      _this5.commandFn = _this5.noop;
      yield _this5.saveScore();
    })();
  }
  writeScore() {
    var _this6 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (_this6.interrupted) {
        return;
      }
      // Save the current cursor position before making any changes
      _this6.term.write('\x1b[s');
      // Move to the first line of the viewport
      _this6.term.write('\x1b[1;1H'); // CSI H moves the cursor to the specified position (1;1 is top left)
      // Clear the first line
      _this6.term.write('\x1b[2K'); // CSI K clears part of the line. '2' clears the entire line.
      const strScore = '' + _this6.score;
      let scoreFormatted = strScore;
      if (strScore.length < 8) {
        scoreFormatted = ' '.repeat(8 - strScore.length) + strScore;
      }
      // Write the new scoreboard text
      _this6.term.write(' SCORE: ' + scoreFormatted);
      _this6.term.write(' TIME LEFT: ' + _this6.gameTime);
      _this6.term.write(' LANGUAGE: ' + _this6.languageCommandService.getLanguageNameById(_this6.gameLanguage));
      // write empty line below score line and clear the row
      _this6.term.write('\x1b[2;1H\x1b[2K');
      // Restore the previously saved cursor position
      _this6.term.write('\x1b[u');
    })();
  }
  saveScore() {
    var _this7 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      _this7.term.write('\r\n');
      yield _this7.writeDelayed('Time is up!');
      yield _this7.writeDelayed('Enter your name:', true);
      _this7.terminalSymbol = 'Name:';
      _this7.commandFn = _this7.enterNameForLeaderboard;
      _this7.input_blocked = false;
    })();
  }
  enterNameForLeaderboard(name, args) {
    var _this8 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      name = stripAnsi(name);
      args = stripAnsi(args);
      if (!name || name == '') {
        yield _this8.writeDelayed('Please enter your Name:');
        return;
      }
      let fullName = name;
      if (args) {
        fullName += ' ' + args;
      }
      if (fullName.length > 20) {
        yield _this8.writeDelayed('Maximum length is 20 chars: Enter again:');
        return;
      }
      const score = {
        name: fullName,
        score: _this8.score,
        code: _this8.code,
        x: {
          maxStreak: _this8.highestStreak,
          speed: _this8.keysPressedInGame / _this8.DEFAULT_GAME_TIME,
          count: _this8.commandsEntered.length,
          avgLength: _this8.totalCommandLength / _this8.commandsEntered.length
        }
      };
      const leaderboardWithLocalPlacement = yield (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.firstValueFrom)(_this8.scoreService.setScoreForLanguage(_this8.gameLanguage, score));
      if (leaderboardWithLocalPlacement.placement < 10 && leaderboardWithLocalPlacement.placement > 0) {
        yield _this8.writeDelayed(`TOP SCORE!`);
      } else if (leaderboardWithLocalPlacement.placement == 0) {
        yield _this8.writeDelayed(`🔥 HIGHSCORE🔥`);
      }
      // Add ANSI Escape Codes to format the name for the leaderboard so the current run shows in bold letters
      //score.name = '>>\x1b[1;31m ' + score.name + ' \x1b[0m<<'; // \x1b[1;31m makes the text bold (1) and red (31), \x1b[0m clears all effects
      _this8.resetToDefaultShell();
      _this8.input_blocked = true;
      _this8.gameEnded.emit({
        success: true,
        leaderboardWithLocalPlacement: leaderboardWithLocalPlacement,
        score: score
      });
      return;
      yield _this8.displayLeaderboard(_this8.gameLanguage, leaderboardWithLocalPlacement, score);
      _this8.terminalSymbol = `Press Enter to continue!`;
      _this8.commandFn = _this8.endGame;
    })();
  }
  endGame() {
    var _this9 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      _this9.input_blocked = true;
      _this9.resetToDefaultShell();
      _this9.gameEnded.emit();
    })();
  }
  displayLeaderboard(language, leaderboardWithLocalPlacement, score) {
    var _this0 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      if (!language || language == '') {
        language = 'all';
      }
      if (!score.score && (!leaderboardWithLocalPlacement || leaderboardWithLocalPlacement.language == '' || leaderboardWithLocalPlacement.scores.length == 0)) {
        _this0.term.writeln(`No Leaderboard for this language present.`);
        return;
      }
      const langName = _this0.languageCommandService.getLanguageNameById(language);
      _this0.term.writeln('-------------' + '-'.repeat(langName.length) + '-');
      _this0.term.writeln('LEADERBOARD (' + langName + ')');
      _this0.term.writeln('-------------' + '-'.repeat(langName.length) + '-');
      // If we show the leaderboard after a game add the score to the placements leaderboard and sort it so that the score is displayed at the correct position
      if (score && score.score && score.name != '') {
        if (leaderboardWithLocalPlacement.placement < 10) {
          leaderboardWithLocalPlacement.scores.push(score);
          leaderboardWithLocalPlacement.scores = leaderboardWithLocalPlacement.scores.sort((a, b) => b.score - a.score);
        } else {
          leaderboardWithLocalPlacement.localscores.push(score);
          leaderboardWithLocalPlacement.localscores = leaderboardWithLocalPlacement.localscores.sort((a, b) => b.score - a.score);
        }
      }
      let scores = [['', 'NAME', 'SCORE']]; // Table heading for scoreboard
      scores = scores.concat(leaderboardWithLocalPlacement.scores.slice(0, 10).map((score, index) => ['' + (index + 1) + '.', score.name, score.score.toString()]));
      if (leaderboardWithLocalPlacement && leaderboardWithLocalPlacement.placement >= 10) {
        scores.push(['...', '...', '...']);
        const scoreIndex = leaderboardWithLocalPlacement.localscores.findIndex(localScore => score.name === localScore.name && score.score === localScore.score);
        scores = scores.concat(leaderboardWithLocalPlacement.localscores.map((score, index) => ['' + (leaderboardWithLocalPlacement.placement + index - scoreIndex + 1) + '.', score.name, score.score.toString()]));
      }
      const longestScore = scores[1][2]?.length ?? 0; // First score entry has the highest score so it is the longest,
      scores.forEach((scoreEntry, index) => {
        const pad = Math.max(longestScore - scoreEntry[2].length, 0);
        scores[index][2] = ' '.repeat(pad) + scoreEntry[2];
      });
      yield _this0.writeMatrix(scores, false);
    })();
  }
  displayAvailableLanguages() {
    var _this1 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* (incluedAll = true) {
      let languages = _this1.languageCommandService.getEnabledLanguageKeys().sort((a, b) => {
        return a.toLowerCase() > b.toLowerCase() ? 1 : -1;
      });
      if (incluedAll) {
        languages = ['all'].concat(languages);
      }
      const matrix = _this1.convertToMatrix(languages, 7);
      yield _this1.writeMatrix(matrix, false);
      //await this.writeMatrix(this.convertToMatrix(newLang, 4), false);
    }).apply(this, arguments);
  }
  gameCommand(cmd) {
    var _this10 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const r = _this10.languageCommandService.find(cmd, _this10.gameLanguage);
      let score = {
        base: 0,
        fireMultiplier: 1,
        streak: 0,
        streakPoints: 0,
        total: 0
      };
      let outputString;
      const timePassed = _this10.DEFAULT_GAME_TIME - _this10.gameTime;
      const totalRows = _this10.term.rows; // Total number of rows in the terminal
      const commandsAreaEnd = totalRows - 2; // The last line before the fixed input line
      if (!cmd) {
        yield _this10.writeScore();
        _this10.term.write(`\x1b[${totalRows - 1};1H\x1b[2K`); // Optionally clear the input line
        yield _this10.moveToInputLine();
        return;
      }
      if (r.found && !_this10.commandsEntered.includes(r.cmd)) {
        _this10.commandsEntered.push(r.cmd);
        _this10.streak += 1;
        _this10.highestStreak = Math.max(_this10.highestStreak, _this10.streak);
        if (_this10.commandsEnteredAtTimepoint[timePassed]) {
          _this10.commandsEnteredAtTimepoint[timePassed] += 1;
        } else {
          _this10.commandsEnteredAtTimepoint[timePassed] = 1;
        }
        score = _this10.getCommandScore();
        _this10.score += score.total;
        _this10.totalCommandLength += cmd.length; // cmd is the command entered, we do not need r.cmd as this could also be an alias
        outputString = ' ✔ ' + r.cmd;
        if (_this10.gameLanguage == 'all' && r.lang.length < 5) {
          outputString += ' | (' + r.lang.join(', ') + ')';
        } else if (_this10.gameLanguage == 'all' && r.lang.length >= 5) {
          outputString += ' | (' + r.lang[Math.floor(Math.random() * r.lang.length)] + ' & ' + (r.lang.length - 1) + ' other languages)';
        }
        outputString += ' | + ' + score.total;
        if (score.fireMultiplier > 1) {
          outputString += ' 🔥x' + score.fireMultiplier;
        }
      } else if (r.found && _this10.commandsEntered.includes(r.cmd)) {
        _this10.commandsEnteredAtTimepoint = []; // Reset so the streak gets lost
        _this10.streak = 0;
        outputString = ' ✘ ' + cmd + ' | You already entered "' + r.cmd + '"';
      } else {
        _this10.commandsEnteredAtTimepoint = []; // Reset so the streak gets lost
        _this10.streak = 0;
        outputString = ' ✘ ' + cmd;
      }
      // Write new command in the line just above the fixed input area
      _this10.term.write(`\x1b[${commandsAreaEnd};1H`); // Moves cursor and clears the line
      _this10.term.writeln(outputString); // Write the new command
      //this.term.writeln(''); // Write the new command
      // Update the scoreboard or perform other updates
      yield _this10.writeScore();
      // Ensure the fixed input line is clean and the cursor is placed correctly
      _this10.term.write(`\x1b[${totalRows - 1};1H\x1b[2K`); // Optionally clear the input line
      yield _this10.moveToInputLine();
    })();
  }
  // Calculate score based on the average count of commands over the last 5 seconds.
  getCommandScore() {
    const result = {
      base: 128,
      fireMultiplier: 1,
      streak: 0,
      streakPoints: 0,
      total: 0
    };
    const timeSinceStart = this.DEFAULT_GAME_TIME - this.gameTime;
    const commandsInLastSeconds = this.commandsInLastSeconds(this.commandsEnteredAtTimepoint, timeSinceStart, this.FIRE_COMMANDS_TIMERANGE);
    const growthFactor = 2;
    if (commandsInLastSeconds >= this.FIRE_COMMANDS_BASE) {
      result.fireMultiplier = Math.pow(growthFactor, commandsInLastSeconds - this.FIRE_COMMANDS_BASE + 1);
    }
    result.streak = this.streak;
    result.streakPoints = result.base * (this.streak - 1);
    result.total = (result.base + result.streakPoints) * result.fireMultiplier;
    return result;
  }
  commandsInLastSeconds(commandsArray, currentTimeIndex, seconds) {
    let sum = 0;
    // Start from the current time index, go back up to 5 seconds if available
    for (let i = currentTimeIndex; i > currentTimeIndex - seconds && i >= 0; i--) {
      sum += commandsArray[i] ?? 0;
    }
    return sum;
  }
  moveToInputLine() {
    var _this11 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      // Calculate the position for the new command line, which should be one line above the current input line
      const inputLinePosition = _this11.term.rows + _this11.term.buffer.active.viewportY - 1; // Position of the input line
      // Move back to the input line position
      _this11.term.write(`\x1b[${inputLinePosition + 1};1H`);
      _this11.term.write('\x1b[2K'); // Clear the line again to ensure it's clean for new input
    })();
  }

  selectGameOption(input) {
    var _this12 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const args = input.split(' ');
      const command = args[0];
      const params = args.slice(1).join(' ');
      switch (command) {
        case 'help':
          yield _this12.helpGame();
          break;
        case 'play':
          yield _this12.selectLanguage(params);
          break;
        case 'lang':
        case 'languages':
          yield _this12.displayAvailableLanguages();
          break;
        case 'leaderboard':
        case 'top':
          {
            const leaderboard = yield (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.firstValueFrom)(_this12.scoreService.getLeaderboard(params));
            yield _this12.displayLeaderboard(params, leaderboard, {});
            break;
          }
        default:
          _this12.term.writeln('Invalid Option: ' + input);
          yield _this12.helpGame();
      }
    })();
  }
  selectLanguage(language) {
    var _this13 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      let languages = _this13.languageCommandService.getEnabledLanguageKeys();
      languages = languages.map(el => {
        return el.toLowerCase();
      });
      if (languages.includes(language.toLowerCase())) {
        yield _this13.confirmBeginGame(language.toLowerCase());
      } else if (language == '') {
        yield _this13.confirmBeginGame('all');
      } else {
        _this13.term.writeln('Invalid language. Available languages are: ');
        yield _this13.displayAvailableLanguages();
      }
    })();
  }
  menuCommandsFn(command, args) {
    var _this14 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      switch (command) {
        case '':
          break;
        case 'ls':
          yield _this14.writeMatrix([['\x1b[1;32mbrawl\x1b[0m']]);
          break;
        case 'echo':
          _this14.term.writeln(args);
          break;
        case 'clear':
          _this14.clearTerminal();
          break;
        case 'brawl':
          yield _this14.startGame(args);
          break;
        case 'exit':
          _this14.endGame();
          break;
        default:
          _this14.term.writeln(`Command not found: ${command}`);
      }
    })();
  }
  handleCommand(input) {
    var _this15 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const args = input.split(' ');
      const command = args[0];
      const params = args.slice(1).join(' ');
      _this15.input_blocked = true;
      _this15.interrupted = false;
      yield _this15.commandFn(command, params);
      _this15.term.write(` ${_this15.terminalSymbol} `);
      _this15.interrupted = false;
      _this15.input_blocked = false;
    })();
  }
  handleCommandWithNewline(input) {
    var _this16 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      _this16.term.write('\r\n');
      yield _this16.handleCommand(input);
    })();
  }
  resetToDefaultShell() {
    this.terminalSymbol = this.DEFAULT_TERMINAL_SYMBOL;
    if (!this.input_blocked) {
      // We are inside a shell. Abort
      this.term.write(`\r\n ${this.terminalSymbol} `);
    }
    this.command = '';
    this.cursorPosition = 0;
    this.input_blocked = false;
    this.inputFn = this.handleCommandWithNewline;
    this.commandFn = this.menuCommandsFn;
  }
  noop() {
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      return;
    })();
  }
  writeMatrix(_x) {
    var _this17 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* (text, writeDelayed = false) {
      const maxColumns = text.reduce((max, row) => Math.max(max, row.length), 0);
      // Calculate the longest string in each column
      const maxLengths = Array(maxColumns).fill(0);
      text.forEach(row => {
        row.forEach((item, index) => {
          if (index < maxColumns) {
            // Ensure we don't exceed the number of columns
            maxLengths[index] = Math.max(maxLengths[index], stripAnsi(item).length);
          }
        });
      });
      // Generate and write each row of the matrix
      for (const row of text) {
        let rowString = '';
        row.forEach((item, index) => {
          if (index < maxColumns) {
            // Ensure we don't exceed the number of columns
            const paddingLength = maxLengths[index] - stripAnsi(item).length;
            rowString += `${item}${' '.repeat(paddingLength + 2)}`; // 2 spaces as gutter
          }
        });
        // Write the formatted row to the terminal
        if (writeDelayed) {
          yield _this17.writeDelayed(' ' + rowString);
        } else {
          _this17.term.writeln(' ' + rowString);
        }
      }
    }).apply(this, arguments);
  }
  // Simply convert a flat list to a matrix with numColumns
  // for advanced matrixes view convertToMatrixWithTerminalWidth
  convertToMatrix(list, numColumns) {
    const matrix = [];
    for (let i = 0; i < list.length; i += numColumns) {
      matrix.push(list.slice(i, i + numColumns));
    }
    return matrix;
  }
  writeDelayed(_x2) {
    var _this18 = this;
    return (0,_Users_wangjunyao_develop_game_QuickGames_frontend_public_games_bashbrawl_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* (text, newline = true, offset = 0) {
      if (text.length == offset || _this18.interrupted) {
        if (newline && offset != 0) {
          _this18.term.write(`\r\n`);
        }
        return;
      }
      const chars = text.split('');
      _this18.term.write(chars[offset]);
      const nextIsWhitespace = chars.length > offset + 1 && chars[offset + 1].match(/\s/);
      yield (0,_cds_core_internal__WEBPACK_IMPORTED_MODULE_10__.sleep)(nextIsWhitespace ? _this18.TERMINAL_WHITESPACE_DELAY : _this18.TERMINAL_CHAR_DELAY);
      yield _this18.writeDelayed(text, newline, offset + 1);
    }).apply(this, arguments);
  }
  ngOnInit() {
    this.buildTerminal();
  }
  ngAfterViewInit() {
    // Options for the observer (which mutations to observe)
    const config = {
      attributes: true,
      childList: true,
      subtree: true
    };
    // Callback function to execute when mutations are observed
    const callback = mutationsList => {
      mutationsList.forEach(mutation => {
        // After the first start of the scenario, wait until the visible terminal element is added to the DOM.
        if (mutation.type === 'childList') {
          if (this.term && this.term.element && this.term.element.offsetParent && !this.isVisible) {
            this.isVisible = true;
            this.firstTabChange = false;
            this.resize();
          } else if (!(this.term && this.term.element && this.term.element.offsetParent) && this.isVisible) {
            this.isVisible = false;
          }
        } else if (mutation.type === 'attributes') {
          // Is triggered if aria-selected changes (on tab button) and terminal should be visible.
          // Should only be called after the first tab change.
          if (this.term && this.term.element && this.term.element.offsetParent && !this.isVisible && !this.firstTabChange) {
            this.isVisible = true;
            this.resize();
            // After the first switch between tabs, do not change the terminal's visibility before the (xterm) canvas element attributes are changed.
            // The terminal is now visible and the xterm.fit() function can be called without throwing errors.
          } else if (this.term && this.term.element && this.term.element.offsetParent && !this.isVisible && mutation.target.nodeName == 'CANVAS') {
            this.isVisible = true;
            this.firstTabChange = false;
            this.resize();
            // Is triggered if aria-selected changes (on tab button) and terminal should not be visible anymore.
          } else if (!(this.term && this.term.element && this.term.element.offsetParent) && this.isVisible) {
            this.isVisible = false;
          }
        }
      });
    };
    // Create an observer instance linked to the callback function
    this.mutationObserver = new MutationObserver(callback);
    this.mutationObserver.observe(this.terminalDiv.nativeElement.offsetParent, config);
  }
  setTerminalTheme(themeId) {
    if (!this.term) return;
    const theme = _terminal_themes_themes__WEBPACK_IMPORTED_MODULE_3__.themes.find(t => t.id === themeId) || _terminal_themes_themes__WEBPACK_IMPORTED_MODULE_3__.themes[0];
    this.term.options.theme = theme.styles;
  }
  setFontSize(size) {
    if (!this.term) return;
    this.term.options.fontSize = size ?? this.DEFAULT_FONT_SIZE;
    this.resize();
  }
  clearTerminal() {
    this.term.clear();
  }
  focusTerminal() {
    this.term.focus();
  }
  blurTerminal() {
    this.term.blur();
  }
  static {
    this.ɵfac = function BashbrawlterminalComponent_Factory(t) {
      return new (t || BashbrawlterminalComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](_services_settings_service__WEBPACK_IMPORTED_MODULE_6__.SettingsService), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](_languages_language_command_service__WEBPACK_IMPORTED_MODULE_7__.LanguageCommandService), _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdirectiveInject"](_services_score_service__WEBPACK_IMPORTED_MODULE_8__.ScoreService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdefineComponent"]({
      type: BashbrawlterminalComponent,
      selectors: [["app-bashbrawl-terminal"]],
      viewQuery: function BashbrawlterminalComponent_Query(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵviewQuery"](_c0, 7);
        }
        if (rf & 2) {
          let _t;
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵloadQuery"]()) && (ctx.terminalDiv = _t.first);
        }
      },
      hostBindings: function BashbrawlterminalComponent_HostBindings(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵlistener"]("resize", function BashbrawlterminalComponent_resize_HostBindingHandler() {
            return ctx.resize();
          }, false, _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵresolveWindow"]);
        }
      },
      inputs: {
        code: "code"
      },
      outputs: {
        gameEnded: "gameEnded",
        gameStarted: "gameStarted"
      },
      decls: 9,
      vars: 0,
      consts: [[1, "fake-window-header"], [1, "bubble", "red"], [1, "bubble", "orange"], [1, "bubble", "green"], [1, "title"], [1, "terminal-border"], [1, "terminal-div"], ["terminal", ""]],
      template: function BashbrawlterminalComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelement"](1, "div", 1)(2, "div", 2)(3, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](4, "div", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵtext"](5, "BashBrawl");
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementStart"](6, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelement"](7, "div", 6, 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵelementEnd"]();
        }
      },
      styles: [".terminal-border {\n  height: calc(100% - 25px);\n  width: 100%;\n  border: 1px solid rgb(224, 224, 224);\n  border-top: none;\n  border-radius: 0 0 25px 25px;\n  z-index: 2;\n}\n\n.terminal-div {\n  height: calc(100% - 15px);\n  width: 100%;\n}\n\n.fake-window-header {\n  border: 1px solid rgb(224, 224, 224);\n  border-radius: 25px 25px 0 0;\n  background-color: rgb(224, 224, 224);\n  border-bottom: none;\n  height: 25px;\n}\n.fake-window-header .bubble {\n  border-radius: 50px;\n  width: 15px;\n  height: 15px;\n  float: left;\n  margin-left: 5px;\n  margin-top: 3px;\n  position: absolute;\n}\n.fake-window-header .bubble.red {\n  background-color: rgba(255, 0, 0, 0.5);\n  margin-left: 20px;\n}\n.fake-window-header .bubble.orange {\n  background-color: rgba(255, 166, 0, 0.5);\n  margin-left: 40px;\n}\n.fake-window-header .bubble.green {\n  background-color: rgba(0, 128, 0, 0.5);\n  margin-left: 60px;\n}\n.fake-window-header div.title {\n  width: 100%;\n  color: black;\n  font-weight: bold;\n  text-align: center;\n}\n\n.xterm {\n  height: calc(100% - 25px);\n}\n\n.xterm-viewport {\n  overflow: hidden !important;\n}\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"],
      encapsulation: 2
    });
  }
}

/***/ }),

/***/ 7518:
/*!*************************************************!*\
  !*** ./src/app/terminals/bashbrawl/keycodes.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Keycodes: () => (/* binding */ Keycodes)
/* harmony export */ });
const Keycodes = {
  ENTER: '\r',
  SPACE: 32,
  ESCAPE: 27,
  CTRL: 17,
  CTR_C: '',
  BACKSPACE: '\x7F',
  DELETE: '[3~',
  LEFT_ARROW: '[D',
  RIGHT_ARROW: '[C',
  UP_ARROW: '[A',
  DOWN_ARROW: '[B'
};

/***/ }),

/***/ 9187:
/*!*******************************************************!*\
  !*** ./src/app/terminals/bashbrawl/languages/bash.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   bashConfig: () => (/* binding */ bashConfig)
/* harmony export */ });
/** Taken from https://github.com/CommandLineHeroes/clh-bash/tree/master/assets/cmds **/
const bashConfig = {
  name: 'bash',
  cmds: [[''], ['!'], ['.'], ['411toppm'], [':'], ['GET'], ['HEAD'], ['Mail'], ['ModemManager'], ['NetworkManager'], ['POST'], ['VBoxClient'], ['VBoxClient-all'], ['VBoxControl'], ['VBoxService'], ['VGAuthService'], ['WebKitWebDriver'], ['X'], ['Xephyr'], ['Xorg'], ['Xvnc'], ['Xwayland'], ['['], ['[['], [']]'], ['__HOSTNAME'], ['__SIZE'], ['__SLAVEURL'], ['__VOLNAME'], ['__expand_tilde_by_ref'], ['__get_cword_at_cursor_by_ref'], ['__grub_dir'], ['__grub_get_last_option'], ['__grub_get_options_from_help'], ['__grub_get_options_from_usage'], ['__grub_list_menuentries'], ['__grub_list_modules'], ['__grubcomp'], ['__load_completion'], ['__ltrim_colon_completions'], ['__parse_options'], ['__reassemble_comp_words_by_ref'], ['__vte_osc7'], ['__vte_prompt_command'], ['__vte_ps1'], ['__vte_urlencode'], ['_allowed_groups'], ['_allowed_users'], ['_apport-bug'], ['_apport-cli'], ['_apport-collect'], ['_apport-unpack'], ['_apport_parameterless'], ['_apport_symptoms'], ['_available_fcoe_interfaces'], ['_available_interfaces'], ['_cd'], ['_cd_devices'], ['_command'], ['_command_offset'], ['_complete_as_root'], ['_completion_loader'], ['_configured_interfaces'], ['_count_args'], ['_dkms'], ['_dog'], ['_dog_benchmark'], ['_dog_benchmark_io'], ['_dog_cluster'], ['_dog_cluster_alter-copy'], ['_dog_cluster_check'], ['_dog_cluster_format'], ['_dog_cluster_info'], ['_dog_cluster_recover'], ['_dog_cluster_reweight'], ['_dog_cluster_shutdown'], ['_dog_cluster_snapshot'], ['_dog_node'], ['_dog_node_format'], ['_dog_node_info'], ['_dog_node_kill'], ['_dog_node_list'], ['_dog_node_log'], ['_dog_node_md'], ['_dog_node_recovery'], ['_dog_node_stat'], ['_dog_node_vnodes'], ['_dog_upgrade'], ['_dog_upgrade_config-convert'], ['_dog_upgrade_epoch-convert'], ['_dog_upgrade_inode-convert'], ['_dog_upgrade_object-location'], ['_dog_vdi'], ['_dog_vdi_alter-copy'], ['_dog_vdi_backup'], ['_dog_vdi_check'], ['_dog_vdi_clone'], ['_dog_vdi_create'], ['_dog_vdi_delete'], ['_dog_vdi_getattr'], ['_dog_vdi_graph'], ['_dog_vdi_list'], ['_dog_vdi_lock'], ['_dog_vdi_object'], ['_dog_vdi_read'], ['_dog_vdi_resize'], ['_dog_vdi_restore'], ['_dog_vdi_rollback'], ['_dog_vdi_setattr'], ['_dog_vdi_snapshot'], ['_dog_vdi_track'], ['_dog_vdi_tree'], ['_dog_vdi_write'], ['_dvd_devices'], ['_expand'], ['_fcoeadm_options'], ['_fcoemon_options'], ['_filedir'], ['_filedir_xspec'], ['_filename_parts'], ['_fstypes'], ['_get_comp_words_by_ref'], ['_get_cword'], ['_get_first_arg'], ['_get_pword'], ['_gids'], ['_gluster_completion'], ['_gluster_does_match'], ['_gluster_form_list'], ['_gluster_goto_child'], ['_gluster_goto_end'], ['_gluster_handle_list'], ['_gluster_parse'], ['_gluster_pop'], ['_gluster_push'], ['_gluster_throw'], ['_grub_editenv'], ['_grub_install'], ['_grub_mkconfig'], ['_grub_mkfont'], ['_grub_mkimage'], ['_grub_mkpasswd_pbkdf2'], ['_grub_mkrescue'], ['_grub_probe'], ['_grub_script_check'], ['_grub_set_entry'], ['_grub_setup'], ['_have'], ['_included_ssh_config_files'], ['_init_completion'], ['_installed_modules'], ['_ip_addresses'], ['_kernel_versions'], ['_kernels'], ['_known_hosts'], ['_known_hosts_real'], ['_lldpad_options'], ['_lldptool_options'], ['_longopt'], ['_mac_addresses'], ['_minimal'], ['_module'], ['_module_avail'], ['_module_long_arg_list'], ['_module_not_yet_loaded'], ['_module_raw'], ['_module_savelist'], ['_modules'], ['_ncpus'], ['_parse_help'], ['_parse_usage'], ['_pci_ids'], ['_pgids'], ['_pids'], ['_pnames'], ['_quote_readline_by_ref'], ['_realcommand'], ['_rl_enabled'], ['_root_command'], ['_scl'], ['_service'], ['_services'], ['_shells'], ['_signals'], ['_split_longopt'], ['_subdirectories'], ['_sysvdirs'], ['_terms'], ['_tilde'], ['_uids'], ['_upvar'], ['_upvars'], ['_usb_ids'], ['_user_at_host'], ['_usergroup'], ['_userland'], ['_variables'], ['_xfunc'], ['_xinetd_services'], ['aa-enabled'], ['aa-exec'], ['aa-remove-unknown'], ['aa-status'], ['ab'], ['abrt-action-analyze-backtrace'], ['abrt-action-analyze-c'], ['abrt-action-analyze-ccpp-local'], ['abrt-action-analyze-core'], ['abrt-action-analyze-java'], ['abrt-action-analyze-oops'], ['abrt-action-analyze-python'], ['abrt-action-analyze-vmcore'], ['abrt-action-analyze-vulnerability'], ['abrt-action-analyze-xorg'], ['abrt-action-check-oops-for-alt-component'], ['abrt-action-check-oops-for-hw-error'], ['abrt-action-find-bodhi-update'], ['abrt-action-generate-backtrace'], ['abrt-action-generate-core-backtrace'], ['abrt-action-install-debuginfo'], ['abrt-action-list-dsos'], ['abrt-action-notify'], ['abrt-action-perform-ccpp-analysis'], ['abrt-action-save-package-data'], ['abrt-action-trim-files'], ['abrt-applet'], ['abrt-auto-reporting'], ['abrt-bodhi'], ['abrt-cli'], ['abrt-configuration'], ['abrt-dbus'], ['abrt-dump-journal-core'], ['abrt-dump-journal-oops'], ['abrt-dump-journal-xorg'], ['abrt-dump-oops'], ['abrt-dump-xorg'], ['abrt-handle-upload'], ['abrt-harvest-pstoreoops'], ['abrt-harvest-vmcore'], ['abrt-install-ccpp-hook'], ['abrt-merge-pstoreoops'], ['abrt-retrace-client'], ['abrt-server'], ['abrt-watch-log'], ['abrtd'], ['ac'], ['accept'], ['accessdb'], ['accton'], ['aconnect'], ['acpi'], ['acpi_available'], ['acpi_listen'], ['acpid'], ['adcli'], ['add-apt-repository'], ['add-shell'], ['add.modules'], ['addgnupghome'], ['addgroup'], ['addpart'], ['addr2line'], ['adduser'], ['adsl-start'], ['adsl-stop'], ['afs5log'], ['agetty'], ['akmods'], ['akmods-shutdown'], ['akmodsbuild'], ['alert'], ['alias'], ['alsa'], ['alsa-info'], ['alsa-info.sh'], ['alsabat'], ['alsabat-test'], ['alsactl'], ['alsaloop'], ['alsamixer'], ['alsatplg'], ['alsaucm'], ['alsaunmute'], ['alternatives'], ['amidi'], ['amixer'], ['amuFormat.sh'], ['anaconda'], ['anaconda-cleanup'], ['anaconda-disable-nm-ibft-plugin'], ['anacron'], ['analog'], ['animate'], ['animate-im6'], ['animate-im6.q16'], ['annocheck'], ['anytopnm'], ['apachectl'], ['apg'], ['apgbfm'], ['aplay'], ['aplaymidi'], ['apm_available'], ['apparmor_parser'], ['apparmor_status'], ['applycal'], ['applydeltarpm'], ['applygnupgdefaults'], ['apport-bug'], ['apport-cli'], ['apport-collect'], ['apport-unpack'], ['appres'], ['appstream-compose'], ['appstream-util'], ['appstreamcli'], ['apropos'], ['apt'], ['apt-add-repository'], ['apt-cache'], ['apt-cdrom'], ['apt-config'], ['apt-extracttemplates'], ['apt-ftparchive'], ['apt-get'], ['apt-key'], ['apt-mark'], ['apt-sortpkgs'], ['aptd'], ['aptdcon'], ['apturl'], ['apturl-gtk'], ['ar'], ['arch'], ['arecord'], ['arecordmidi'], ['arm2hpdl'], ['arp'], ['arpaname'], ['arpd'], ['arping'], ['as'], ['asciitopgm'], ['aseqdump'], ['aseqnet'], ['aserver'], ['aspell'], ['aspell-autobuildhash'], ['aspell-import'], ['at'], ['atd'], ['atktopbm'], ['atobm'], ['atq'], ['atrm'], ['atrun'], ['attr'], ['audit2allow'], ['audit2why'], ['auditctl'], ['auditd'], ['augenrules'], ['aulast'], ['aulastlog'], ['aureport'], ['ausearch'], ['ausyscall'], ['authconfig'], ['authselect'], ['auvirt'], ['avahi-autoipd'], ['avahi-browse'], ['avahi-browse-domains'], ['avahi-daemon'], ['avahi-publish'], ['avahi-publish-address'], ['avahi-publish-service'], ['avahi-resolve'], ['avahi-resolve-address'], ['avahi-resolve-host-name'], ['avahi-set-host-name'], ['avcstat'], ['average'], ['awk'], ['axfer'], ['b2sum'], ['b43-fwcutter'], ['badblocks'], ['baobab'], ['base32'], ['base64'], ['basename'], ['bash'], ['bashbug'], ['bashbug-64'], ['batch'], ['bc'], ['bcache-status'], ['bcache-super-show'], ['bccmd'], ['bdftopcf'], ['bdftruncate'], ['bg'], ['bind'], ['bioradtopgm'], ['biosdecode'], ['bitmap'], ['blivet-gui'], ['blivet-gui-daemon'], ['blkdeactivate'], ['blkdiscard'], ['blkid'], ['blkmapd'], ['blkzone'], ['blockdev'], ['bluemoon'], ['bluetooth-sendto'], ['bluetoothctl'], ['bluetoothd'], ['bmptopnm'], ['bmptoppm'], ['bmtoa'], ['boltctl'], ['bond2team'], ['bootctl'], ['brctl'], ['break'], ['bridge'], ['brltty'], ['brltty-atb'], ['brltty-config'], ['brltty-ctb'], ['brltty-ktb'], ['brltty-lsinc'], ['brltty-setup'], ['brltty-trtxt'], ['brltty-ttb'], ['brltty-tune'], ['broadwayd'], ['brotli'], ['browse'], ['brushtopbm'], ['bsd-from'], ['bsd-write'], ['btattach'], ['btmgmt'], ['btmon'], ['btrfs'], ['btrfs-convert'], ['btrfs-find-root'], ['btrfs-image'], ['btrfs-map-logical'], ['btrfs-select-super'], ['btrfsck'], ['btrfstune'], ['built-by'], ['builtin'], ['bumblebee-bugreport'], ['bumblebeed'], ['bunzip2'], ['busctl'], ['busybox'], ['bwrap'], ['bzcat'], ['bzcmp'], ['bzdiff'], ['bzegrep'], ['bzexe'], ['bzfgrep'], ['bzgrep'], ['bzip2'], ['bzip2recover'], ['bzless'], ['bzmore'], ['c++filt'], ['c89'], ['c99'], ['c_rehash'], ['ca-legacy'], ['cache_check'], ['cache_dump'], ['cache_metadata_size'], ['cache_repair'], ['cache_restore'], ['cache_writeback'], ['cairo-sphinx'], ['cal'], ['calendar'], ['calibrate_ppa'], ['caller'], ['canberra-boot'], ['canberra-gtk-play'], ['cancel'], ['cancel.cups'], ['capsh'], ['captoinfo'], ['case'], ['cat'], ['catchsegv'], ['catman'], ['cautious-launcher'], ['cb2ti3'], ['cbq'], ['cc'], ['cctiff'], ['ccttest'], ['ccxxmake'], ['cd'], ['cd-convert'], ['cd-create-profile'], ['cd-drive'], ['cd-fix-profile'], ['cd-iccdump'], ['cd-info'], ['cd-it8'], ['cd-paranoia'], ['cd-read'], ['cdda-player'], ['celtdec051'], ['celtenc051'], ['cfdisk'], ['cgdisk'], ['chacl'], ['chage'], ['chardet3'], ['chardetect3'], ['chartread'], ['chat'], ['chattr'], ['chcat'], ['chcon'], ['chcpu'], ['check-abi'], ['check-language-support'], ['checkisomd5'], ['checkmodule'], ['checkpolicy'], ['checksctp'], ['cheese'], ['chfn'], ['chgpasswd'], ['chgrp'], ['chkconfig'], ['chmem'], ['chmod'], ['chown'], ['chpasswd'], ['chrome-gnome-shell'], ['chronyc'], ['chronyd'], ['chroot'], ['chrt'], ['chsh'], ['chvt'], ['cifs.idmap'], ['cifs.upcall'], ['cifscreds'], ['cifsdd'], ['ciptool'], ['cisco-decrypt'], ['ckbcomp'], ['cksum'], ['clear'], ['clear_console'], ['clock'], ['clockdiff'], ['cmp'], ['cmuwmtopbm'], ['codepage'], ['col'], ['colcrt'], ['collink'], ['colormgr'], ['colprof'], ['colrm'], ['column'], ['colverify'], ['combinedeltarpm'], ['combinediff'], ['comm'], ['command'], ['command_not_found_handle'], ['compare'], ['compare-im6'], ['compare-im6.q16'], ['compgen'], ['complete'], ['compopt'], ['compose'], ['composite'], ['composite-im6'], ['composite-im6.q16'], ['conjure'], ['conjure-im6'], ['conjure-im6.q16'], ['consolehelper'], ['consoletype'], ['continue'], ['convert'], ['convert-im6'], ['convert-im6.q16'], ['convertquota'], ['coproc'], ['coredumpctl'], ['corelist'], ['coverage-3.7'], ['coverage3'], ['cp'], ['cpan'], ['cpan5.26-x86_64-linux-gnu'], ['cpgr'], ['cpio'], ['cpp'], ['cpp-8'], ['cppw'], ['cpustat'], ['cracklib-check'], ['cracklib-format'], ['cracklib-packer'], ['cracklib-unpacker'], ['crc32'], ['crda'], ['create-cracklib-dict'], ['createmodule.py'], ['createmodule.sh'], ['createrepo'], ['createrepo_c'], ['cron'], ['crond'], ['cronnext'], ['crontab'], ['cryptsetup'], ['csplit'], ['csslint-0.6'], ['cstool'], ['ctrlaltdel'], ['ctstat'], ['cups-browsed'], ['cups-calibrate'], ['cups-genppd.5.2'], ['cups-genppdupdate'], ['cupsaccept'], ['cupsaddsmb'], ['cupsctl'], ['cupsd'], ['cupsdisable'], ['cupsenable'], ['cupsfilter'], ['cupsreject'], ['cupstestdsc'], ['cupstestppd'], ['curl'], ['cut'], ['cvt'], ['cvtsudoers'], ['dash'], ['date'], ['dazzle-list-counters'], ['db_archive'], ['db_checkpoint'], ['db_deadlock'], ['db_dump'], ['db_dump185'], ['db_hotbackup'], ['db_load'], ['db_log_verify'], ['db_printlog'], ['db_recover'], ['db_replicate'], ['db_stat'], ['db_tuner'], ['db_upgrade'], ['db_verify'], ['dbus-binding-tool'], ['dbus-cleanup-sockets'], ['dbus-daemon'], ['dbus-launch'], ['dbus-monitor'], ['dbus-run-session'], ['dbus-send'], ['dbus-test-tool'], ['dbus-update-activation-environment'], ['dbus-uuidgen'], ['dbwrap_tool'], ['dbxtool'], ['dc'], ['dcbtool'], ['dconf'], ['dd'], ['ddns-confgen'], ['ddstdecode'], ['deallocvt'], ['deb-systemd-helper'], ['deb-systemd-invoke'], ['debconf'], ['debconf-apt-progress'], ['debconf-communicate'], ['debconf-copydb'], ['debconf-escape'], ['debconf-set-selections'], ['debconf-show'], ['debugfs'], ['declare'], ['dehtmldiff'], ['deja-dup'], ['delgroup'], ['delpart'], ['deluser'], ['delv'], ['depmod'], ['dequote'], ['desktop-file-edit'], ['desktop-file-install'], ['desktop-file-validate'], ['devdump'], ['devlink'], ['df'], ['dfu-tool'], ['dh_bash-completion'], ['dh_perl_openssl'], ['dhclient'], ['dhclient-script'], ['diff'], ['diff3'], ['diffstat'], ['dig'], ['dir'], ['dircolors'], ['dirmngr'], ['dirmngr-client'], ['dirname'], ['dirs'], ['dirsplit'], ['disown'], ['dispcal'], ['display'], ['display-im6'], ['display-im6.q16'], ['dispread'], ['dispwin'], ['distro'], ['dkms'], ['dm_dso_reg_tool'], ['dmesg'], ['dmevent_tool'], ['dmeventd'], ['dmfilemapd'], ['dmidecode'], ['dmraid'], ['dmraid.static'], ['dmsetup'], ['dmstats'], ['dnf'], ['dnf-3'], ['dnsdomainname'], ['dnsmasq'], ['dnssec-checkds'], ['dnssec-coverage'], ['dnssec-dsfromkey'], ['dnssec-importkey'], ['dnssec-keyfromlabel'], ['dnssec-keygen'], ['dnssec-keymgr'], ['dnssec-revoke'], ['dnssec-settime'], ['dnssec-signzone'], ['dnssec-verify'], ['do'], ['do-release-upgrade'], ['dog'], ['domainname'], ['done'], ['dos2unix'], ['dosfsck'], ['dosfslabel'], ['dotlockfile'], ['dpkg'], ['dpkg-deb'], ['dpkg-divert'], ['dpkg-maintscript-helper'], ['dpkg-preconfigure'], ['dpkg-query'], ['dpkg-reconfigure'], ['dpkg-split'], ['dpkg-statoverride'], ['dpkg-trigger'], ['dracut'], ['driverless'], ['du'], ['dump-acct'], ['dump-utmp'], ['dumpe2fs'], ['dumpiso'], ['dumpkeys'], ['dvcont'], ['dvipdf'], ['dwp'], ['dwz'], ['e2freefrag'], ['e2fsck'], ['e2image'], ['e2label'], ['e2mmpstatus'], ['e2undo'], ['e4crypt'], ['e4defrag'], ['eapol_test'], ['easy_install-3.7'], ['ebtables'], ['ebtables-legacy'], ['ebtables-restore'], ['ebtables-save'], ['echo'], ['ed'], ['edid-decode'], ['edit'], ['editdiff'], ['editor'], ['editres'], ['edquota'], ['efibootdump'], ['efibootmgr'], ['egrep'], ['eject'], ['elfedit'], ['elif'], ['else'], ['enable'], ['enc2xs'], ['enca'], ['encguess'], ['enchant'], ['enchant-2'], ['enchant-lsmod'], ['enchant-lsmod-2'], ['enconv'], ['env'], ['envml'], ['envsubst'], ['eog'], ['epiphany'], ['eps2eps'], ['eqn'], ['era_check'], ['era_dump'], ['era_invalidate'], ['era_restore'], ['esac'], ['esc-m'], ['escputil'], ['esmtp'], ['esmtp-wrapper'], ['espdiff'], ['espeak-ng'], ['ether-wake'], ['ethtool'], ['eu-addr2line'], ['eu-ar'], ['eu-elfcmp'], ['eu-elfcompress'], ['eu-elflint'], ['eu-findtextrel'], ['eu-make-debug-archive'], ['eu-nm'], ['eu-objdump'], ['eu-ranlib'], ['eu-readelf'], ['eu-size'], ['eu-stack'], ['eu-strings'], ['eu-strip'], ['eu-unstrip'], ['eutp'], ['eval'], ['evince'], ['evince-previewer'], ['evince-thumbnailer'], ['evmctl'], ['evolution'], ['ex'], ['exec'], ['exempi'], ['exit'], ['exiv2'], ['expand'], ['expiry'], ['export'], ['exportfs'], ['expr'], ['extlinux'], ['extracticc'], ['extractttag'], ['eyuvtoppm'], ['factor'], ['faillock'], ['faillog'], ['fakeCMY'], ['faked'], ['faked-sysv'], ['faked-tcp'], ['fakeread'], ['fakeroot'], ['fakeroot-sysv'], ['fakeroot-tcp'], ['fallocate'], ['false'], ['fatlabel'], ['fc'], ['fc-cache'], ['fc-cache-64'], ['fc-cat'], ['fc-conflist'], ['fc-list'], ['fc-match'], ['fc-pattern'], ['fc-query'], ['fc-scan'], ['fc-validate'], ['fcgistarter'], ['fcnsq'], ['fcoeadm'], ['fcoemon'], ['fcping'], ['fcrls'], ['fdformat'], ['fdisk'], ['fg'], ['fgconsole'], ['fgrep'], ['fi'], ['fiascotopnm'], ['file'], ['file-roller'], ['file2brl'], ['filefrag'], ['filterdiff'], ['fincore'], ['find'], ['findfs'], ['findmnt'], ['findsmb'], ['fips-finish-install'], ['fips-mode-setup'], ['fipscheck'], ['fipshmac'], ['fipvlan'], ['firefox'], ['firewall-cmd'], ['firewall-offline-cmd'], ['firewalld'], ['fitstopnm'], ['fix-info-dir'], ['fix-qdf'], ['fixcvsdiff'], ['fixfiles'], ['fixparts'], ['flatpak'], ['flatpak-bisect'], ['flatpak-coredumpctl'], ['flipdiff'], ['flock'], ['fmt'], ['fold'], ['fonttosfnt'], ['foo2ddst'], ['foo2ddst-wrapper'], ['foo2hbpl2'], ['foo2hbpl2-wrapper'], ['foo2hiperc'], ['foo2hiperc-wrapper'], ['foo2hp'], ['foo2hp2600-wrapper'], ['foo2lava'], ['foo2lava-wrapper'], ['foo2oak'], ['foo2oak-wrapper'], ['foo2qpdl'], ['foo2qpdl-wrapper'], ['foo2slx'], ['foo2slx-wrapper'], ['foo2xqx'], ['foo2xqx-wrapper'], ['foo2zjs'], ['foo2zjs-icc2ps'], ['foo2zjs-pstops'], ['foo2zjs-wrapper'], ['foomatic-addpjloptions'], ['foomatic-cleanupdrivers'], ['foomatic-combo-xml'], ['foomatic-compiledb'], ['foomatic-configure'], ['foomatic-datafile'], ['foomatic-extract-text'], ['foomatic-fix-xml'], ['foomatic-getpjloptions'], ['foomatic-kitload'], ['foomatic-nonumericalids'], ['foomatic-perl-data'], ['foomatic-ppd-options'], ['foomatic-ppd-to-xml'], ['foomatic-ppdfile'], ['foomatic-preferred-driver'], ['foomatic-printermap-to-gutenprint-xml'], ['foomatic-printjob'], ['foomatic-replaceoldprinterids'], ['foomatic-rip'], ['foomatic-searchprinter'], ['for'], ['fpaste'], ['fprintd-delete'], ['fprintd-enroll'], ['fprintd-list'], ['fprintd-verify'], ['free'], ['fribidi'], ['from'], ['fros'], ['fsadm'], ['fsck'], ['fsck.btrfs'], ['fsck.cramfs'], ['fsck.ext2'], ['fsck.ext3'], ['fsck.ext4'], ['fsck.fat'], ['fsck.hfs'], ['fsck.hfsplus'], ['fsck.minix'], ['fsck.msdos'], ['fsck.ntfs'], ['fsck.vfat'], ['fsck.xfs'], ['fsfreeze'], ['fstab-decode'], ['fstopgm'], ['fstrim'], ['ftp'], ['function'], ['funzip'], ['fuse2fs'], ['fuser'], ['fusermount'], ['fusermount-glusterfs'], ['fwupdmgr'], ['g13'], ['g13-syshelp'], ['g3topbm'], ['gamma4scanimage'], ['gapplication'], ['gatttool'], ['gawk'], ['gawklibpath_append'], ['gawklibpath_default'], ['gawklibpath_prepend'], ['gawkpath_append'], ['gawkpath_default'], ['gawkpath_prepend'], ['gcalccmd'], ['gcc'], ['gcc-ar'], ['gcc-nm'], ['gcc-ranlib'], ['gcm-calibrate'], ['gcm-import'], ['gcm-inspect'], ['gcm-picker'], ['gcm-viewer'], ['gconf-merge-tree'], ['gconftool-2'], ['gcore'], ['gcov'], ['gcov-dump'], ['gcov-tool'], ['gcr-viewer'], ['gdb'], ['gdb-add-index'], ['gdbserver'], ['gdbtui'], ['gdbus'], ['gdialog'], ['gdisk'], ['gdk-pixbuf-csource'], ['gdk-pixbuf-pixdata'], ['gdk-pixbuf-query-loaders-64'], ['gdk-pixbuf-thumbnailer'], ['gdm'], ['gdm-screenshot'], ['gdm3'], ['gdmflexiserver'], ['gedit'], ['gemtopbm'], ['gemtopnm'], ['gencat'], ['gendiff'], ['genhomedircon'], ['genhostid'], ['genisoimage'], ['genl'], ['genl-ctrl-list'], ['genrandom'], ['geoiplookup'], ['geoiplookup6'], ['geqn'], ['getcap'], ['getcifsacl'], ['getconf'], ['geteltorito'], ['getenforce'], ['getent'], ['getfacl'], ['getfattr'], ['gethostip'], ['getkeycodes'], ['getopt'], ['getopts'], ['getpcaps'], ['getsebool'], ['gettext'], ['gettext.sh'], ['gettextize'], ['getty'], ['getweb'], ['ghostscript'], ['giftopnm'], ['ginstall-info'], ['gio'], ['gio-launch-desktop'], ['gio-querymodules'], ['gio-querymodules-64'], ['gipddecode'], ['git'], ['git-receive-pack'], ['git-shell'], ['git-upload-archive'], ['git-upload-pack'], ['gjs'], ['gjs-console'], ['gkbd-keyboard-display'], ['glib-compile-schemas'], ['glreadtest'], ['gluster'], ['glusterfs'], ['glusterfsd'], ['glxgears'], ['glxinfo'], ['glxinfo64'], ['glxspheres64'], ['gmake'], ['gneqn'], ['gnome-abrt'], ['gnome-boxes'], ['gnome-calculator'], ['gnome-calendar'], ['gnome-characters'], ['gnome-clocks'], ['gnome-contacts'], ['gnome-control-center'], ['gnome-disk-image-mounter'], ['gnome-disks'], ['gnome-documents'], ['gnome-font-viewer'], ['gnome-help'], ['gnome-keyring'], ['gnome-keyring-3'], ['gnome-keyring-daemon'], ['gnome-language-selector'], ['gnome-logs'], ['gnome-mahjongg'], ['gnome-maps'], ['gnome-menus-blacklist'], ['gnome-mines'], ['gnome-photos'], ['gnome-power-statistics'], ['gnome-screenshot'], ['gnome-session'], ['gnome-session-custom-session'], ['gnome-session-inhibit'], ['gnome-session-properties'], ['gnome-session-quit'], ['gnome-session-remmina'], ['gnome-shell'], ['gnome-shell-extension-prefs'], ['gnome-shell-extension-tool'], ['gnome-shell-perf-tool'], ['gnome-software'], ['gnome-software-editor'], ['gnome-sudoku'], ['gnome-system-monitor'], ['gnome-terminal'], ['gnome-terminal.real'], ['gnome-terminal.wrapper'], ['gnome-text-editor'], ['gnome-thumbnail-font'], ['gnome-todo'], ['gnome-tweaks'], ['gnome-weather'], ['gnome-www-browser'], ['gnroff'], ['gold'], ['google-chrome'], ['google-chrome-stable'], ['gouldtoppm'], ['gpasswd'], ['gpg'], ['gpg-agent'], ['gpg-connect-agent'], ['gpg-error'], ['gpg-wks-server'], ['gpg-zip'], ['gpg2'], ['gpgconf'], ['gpgme-json'], ['gpgparsemail'], ['gpgsm'], ['gpgsplit'], ['gpgv'], ['gpgv2'], ['gpic'], ['gprof'], ['gpu-manager'], ['gr2fonttest'], ['grep'], ['grepdiff'], ['gresource'], ['greytiff'], ['grilo-test-ui-0.3'], ['grl-inspect-0.3'], ['grl-launch-0.3'], ['groff'], ['grog'], ['grops'], ['grotty'], ['groupadd'], ['groupdel'], ['groupmems'], ['groupmod'], ['groups'], ['grpck'], ['grpconv'], ['grpunconv'], ['grub-bios-setup'], ['grub-editenv'], ['grub-file'], ['grub-fstest'], ['grub-glue-efi'], ['grub-install'], ['grub-kbdcomp'], ['grub-macbless'], ['grub-menulst2cfg'], ['grub-mkconfig'], ['grub-mkdevicemap'], ['grub-mkfont'], ['grub-mkimage'], ['grub-mklayout'], ['grub-mknetdir'], ['grub-mkpasswd-pbkdf2'], ['grub-mkrelpath'], ['grub-mkrescue'], ['grub-mkstandalone'], ['grub-mount'], ['grub-ntldr-img'], ['grub-probe'], ['grub-reboot'], ['grub-render-label'], ['grub-script-check'], ['grub-set-default'], ['grub-syslinux2cfg'], ['grub2-bios-setup'], ['grub2-editenv'], ['grub2-file'], ['grub2-fstest'], ['grub2-get-kernel-settings'], ['grub2-glue-efi'], ['grub2-install'], ['grub2-kbdcomp'], ['grub2-macbless'], ['grub2-menulst2cfg'], ['grub2-mkconfig'], ['grub2-mkfont'], ['grub2-mkimage'], ['grub2-mklayout'], ['grub2-mknetdir'], ['grub2-mkpasswd-pbkdf2'], ['grub2-mkrelpath'], ['grub2-mkrescue'], ['grub2-mkstandalone'], ['grub2-ofpathname'], ['grub2-probe'], ['grub2-reboot'], ['grub2-render-label'], ['grub2-rpm-sort'], ['grub2-script-check'], ['grub2-set-bootflag'], ['grub2-set-default'], ['grub2-set-password'], ['grub2-setpassword'], ['grub2-sparc64-setup'], ['grub2-switch-to-blscfg'], ['grub2-syslinux2cfg'], ['grubby'], ['gs'], ['gsbj'], ['gsdj'], ['gsdj500'], ['gsettings'], ['gsettings-data-convert'], ['gsf-office-thumbnailer'], ['gslj'], ['gslp'], ['gsnd'], ['gsoelim'], ['gsound-play'], ['gssproxy'], ['gst-device-monitor-1.0'], ['gst-discoverer-1.0'], ['gst-inspect-1.0'], ['gst-launch-1.0'], ['gst-play-1.0'], ['gst-stats-1.0'], ['gst-typefind-1.0'], ['gstack'], ['gstreamer-codec-install'], ['gtar'], ['gtbl'], ['gtf'], ['gtk-builder-tool'], ['gtk-launch'], ['gtk-query-immodules-2.0-64'], ['gtk-query-immodules-3.0-64'], ['gtk-query-settings'], ['gtk-update-icon-cache'], ['gtroff'], ['guild'], ['guile'], ['guile-tools'], ['guile2'], ['guile2-tools'], ['gunzip'], ['gupnp-dlna-info-2.0'], ['gupnp-dlna-ls-profiles-2.0'], ['gvfs-cat'], ['gvfs-copy'], ['gvfs-info'], ['gvfs-less'], ['gvfs-ls'], ['gvfs-mime'], ['gvfs-mkdir'], ['gvfs-monitor-dir'], ['gvfs-monitor-file'], ['gvfs-mount'], ['gvfs-move'], ['gvfs-open'], ['gvfs-rename'], ['gvfs-rm'], ['gvfs-save'], ['gvfs-set-attribute'], ['gvfs-trash'], ['gvfs-tree'], ['gzexe'], ['gzip'], ['h2ph'], ['h2xs'], ['halt'], ['handle-sshpw'], ['hangul'], ['hardened'], ['hardlink'], ['hash'], ['hbpldecode'], ['hciattach'], ['hciconfig'], ['hcidump'], ['hcitool'], ['hd'], ['hdparm'], ['head'], ['help'], ['helpztags'], ['hex2hcd'], ['hexdump'], ['hfs-bless'], ['highlight'], ['hipercdecode'], ['hipstopgm'], ['history'], ['host'], ['hostid'], ['hostname'], ['hostnamectl'], ['hp-align'], ['hp-check'], ['hp-clean'], ['hp-colorcal'], ['hp-config_usb_printer'], ['hp-diagnose_plugin'], ['hp-diagnose_queues'], ['hp-doctor'], ['hp-fab'], ['hp-firmware'], ['hp-info'], ['hp-levels'], ['hp-logcapture'], ['hp-makeuri'], ['hp-pkservice'], ['hp-plugin'], ['hp-plugin-ubuntu'], ['hp-probe'], ['hp-query'], ['hp-scan'], ['hp-sendfax'], ['hp-setup'], ['hp-testpage'], ['hp-timedate'], ['hp-unload'], ['hpcups-update-ppds'], ['hpijs'], ['htcacheclean'], ['htdbm'], ['htdigest'], ['htpasswd'], ['httpd'], ['httxt2dbm'], ['hunspell'], ['hwclock'], ['hwe-support-status'], ['hypervfcopyd'], ['hypervkvpd'], ['hypervvssd'], ['i386'], ['ibus'], ['ibus-daemon'], ['ibus-setup'], ['ibus-table-createdb'], ['iccdump'], ['iccgamut'], ['icclu'], ['icctest'], ['iceauth'], ['ico'], ['icontopbm'], ['iconv'], ['iconvconfig'], ['id'], ['identify'], ['identify-im6'], ['identify-im6.q16'], ['idiag-socket-details'], ['idn'], ['iecset'], ['if'], ['ifcfg'], ['ifconfig'], ['ifdown'], ['ifenslave'], ['ifquery'], ['ifrename'], ['ifstat'], ['ifup'], ['iio-sensor-proxy'], ['ijs_pxljr'], ['ilbmtoppm'], ['illumread'], ['im-config'], ['im-launch'], ['imagetops'], ['imgtoppm'], ['implantisomd5'], ['import'], ['import-im6'], ['import-im6.q16'], ['in'], ['info'], ['infobrowser'], ['infocmp'], ['infotocap'], ['init'], ['inputattach'], ['insmod'], ['install'], ['install-info'], ['install-printerdriver'], ['installkernel'], ['instmodsh'], ['instperf'], ['intel-virtual-output'], ['interdiff'], ['invoke-rc.d'], ['invprofcheck'], ['ionice'], ['ip'], ['ip6tables'], ['ip6tables-apply'], ['ip6tables-legacy'], ['ip6tables-legacy-restore'], ['ip6tables-legacy-save'], ['ip6tables-restore'], ['ip6tables-save'], ['ipcalc'], ['ipcmk'], ['ipcrm'], ['ipcs'], ['ipmaddr'], ['ipod-read-sysinfo-extended'], ['ipod-time-sync'], ['ippfind'], ['ippserver'], ['ipptool'], ['ippusbxd'], ['ipset'], ['iptables'], ['iptables-apply'], ['iptables-legacy'], ['iptables-legacy-restore'], ['iptables-legacy-save'], ['iptables-restore'], ['iptables-save'], ['iptables-xml'], ['iptc'], ['iptstate'], ['iptunnel'], ['irqbalance'], ['irqbalance-ui'], ['isc-hmac-fixup'], ['ischroot'], ['iscsi-iname'], ['iscsiadm'], ['iscsid'], ['iscsistart'], ['iscsiuio'], ['isdv4-serial-debugger'], ['isdv4-serial-inputattach'], ['iso-info'], ['iso-read'], ['isodebug'], ['isodump'], ['isohybrid'], ['isoinfo'], ['isosize'], ['isovfy'], ['ispell-autobuildhash'], ['ispell-wrapper'], ['iucode-tool'], ['iucode_tool'], ['iw'], ['iwconfig'], ['iwevent'], ['iwgetid'], ['iwlist'], ['iwpriv'], ['iwspy'], ['jackd'], ['jackrec'], ['java'], ['jimsh'], ['jjs'], ['jobs'], ['join'], ['journalctl'], ['jpegtopnm'], ['jpgicc'], ['json_pp'], ['json_reformat'], ['json_verify'], ['jwhois'], ['kbd_mode'], ['kbdinfo'], ['kbdrate'], ['kbxutil'], ['kdumpctl'], ['kernel-install'], ['kerneloops'], ['kerneloops-submit'], ['kexec'], ['key.dns_resolver'], ['keyctl'], ['keyring'], ['keytool'], ['kill'], ['killall'], ['killall5'], ['kmod'], ['kmodsign'], ['kmodtool'], ['kodak2ti3'], ['kpartx'], ['l'], ['l.'], ['l2ping'], ['l2test'], ['la'], ['laptop-detect'], ['last'], ['lastb'], ['lastcomm'], ['lastlog'], ['lavadecode'], ['lcf'], ['lchage'], ['lchfn'], ['lchsh'], ['ld'], ['ld.bfd'], ['ld.gold'], ['ldattach'], ['ldconfig'], ['ldconfig.real'], ['ldd'], ['leaftoppm'], ['less'], ['lessecho'], ['lessfile'], ['lesskey'], ['lesspipe'], ['lesspipe.sh'], ['let'], ['lexgrog'], ['lgroupadd'], ['lgroupdel'], ['lgroupmod'], ['libieee1284_test'], ['libinput'], ['libnetcfg'], ['libreoffice'], ['libtar'], ['libvirtd'], ['libwacom-list-local-devices'], ['lid'], ['link'], ['linkicc'], ['lintian'], ['lintian-info'], ['lintian-lab-tool'], ['linux-boot-prober'], ['linux-check-removal'], ['linux-update-symlinks'], ['linux-version'], ['linux32'], ['linux64'], ['lispmtopgm'], ['listres'], ['liveinst'], ['ll'], ['lldpad'], ['lldptool'], ['ln'], ['lnewusers'], ['lnstat'], ['load_policy'], ['loadkeys'], ['loadunimap'], ['local'], ['localc'], ['locale'], ['locale-check'], ['locale-gen'], ['localectl'], ['localedef'], ['locate'], ['lockdev'], ['lodraw'], ['loffice'], ['lofromtemplate'], ['logger'], ['login'], ['loginctl'], ['logname'], ['logout'], ['logresolve'], ['logrotate'], ['logsave'], ['loimpress'], ['lomath'], ['look'], ['lorder'], ['losetup'], ['loweb'], ['lowntfs-3g'], ['lowriter'], ['lp'], ['lp.cups'], ['lp_solve'], ['lpadmin'], ['lpasswd'], ['lpc'], ['lpc.cups'], ['lpinfo'], ['lpmove'], ['lpoptions'], ['lpq'], ['lpq.cups'], ['lpr'], ['lpr.cups'], ['lprm'], ['lprm.cups'], ['lpstat'], ['lpstat.cups'], ['ls'], ['lsattr'], ['lsb_release'], ['lsblk'], ['lscpu'], ['lsdiff'], ['lshw'], ['lsinitramfs'], ['lsinitrd'], ['lsipc'], ['lslocks'], ['lslogins'], ['lsmem'], ['lsmod'], ['lsns'], ['lsof'], ['lspci'], ['lspcmcia'], ['lspgpot'], ['lsusb'], ['lsusb.py'], ['ltrace'], ['lua'], ['luac'], ['luit'], ['luseradd'], ['luserdel'], ['lusermod'], ['lvchange'], ['lvconvert'], ['lvcreate'], ['lvdisplay'], ['lvextend'], ['lvm'], ['lvmconf'], ['lvmconfig'], ['lvmdiskscan'], ['lvmdump'], ['lvmetad'], ['lvmpolld'], ['lvmsadc'], ['lvmsar'], ['lvreduce'], ['lvremove'], ['lvrename'], ['lvresize'], ['lvs'], ['lvscan'], ['lwp-download'], ['lwp-dump'], ['lwp-mirror'], ['lwp-request'], ['lxpolkit'], ['lz'], ['lz4'], ['lz4c'], ['lz4cat'], ['lzcat'], ['lzcmp'], ['lzdiff'], ['lzegrep'], ['lzfgrep'], ['lzgrep'], ['lzless'], ['lzma'], ['lzmainfo'], ['lzmore'], ['lzop'], ['m17n-conv'], ['m2300w'], ['m2300w-wrapper'], ['m2400w'], ['m4'], ['mac2unix'], ['machinectl'], ['macptopbm'], ['mail'], ['mailq'], ['mailx'], ['make'], ['make-bcache'], ['make-dummy-cert'], ['make-ssl-cert'], ['makedb'], ['makedeltarpm'], ['makedumpfile'], ['man'], ['mandb'], ['manpath'], ['mapfile'], ['mapscrn'], ['matchpathcon'], ['mattrib'], ['mawk'], ['mbadblocks'], ['mbim-network'], ['mbimcli'], ['mcat'], ['mcd'], ['mcelog'], ['mcheck'], ['mclasserase'], ['mcomp'], ['mcookie'], ['mcopy'], ['mcpp'], ['md5sum'], ['md5sum.textutils'], ['mdadm'], ['mdatopbm'], ['mdel'], ['mdeltree'], ['mdig'], ['mdir'], ['mdmon'], ['mdu'], ['memdiskfind'], ['memtest-setup'], ['mergerepo'], ['mergerepo_c'], ['mesg'], ['meshctl'], ['mformat'], ['mgrtopbm'], ['migrate-pubring-from-classic-gpg'], ['mii-diag'], ['mii-tool'], ['mimeopen'], ['mimetype'], ['min12xxw'], ['minfo'], ['mk_modmap'], ['mkdict'], ['mkdir'], ['mkdosfs'], ['mkdumprd'], ['mke2fs'], ['mkfifo'], ['mkfontdir'], ['mkfontscale'], ['mkfs'], ['mkfs.bfs'], ['mkfs.btrfs'], ['mkfs.cramfs'], ['mkfs.ext2'], ['mkfs.ext3'], ['mkfs.ext4'], ['mkfs.fat'], ['mkfs.hfsplus'], ['mkfs.minix'], ['mkfs.msdos'], ['mkfs.ntfs'], ['mkfs.vfat'], ['mkfs.xfs'], ['mkhomedir_helper'], ['mkhybrid'], ['mkinitramfs'], ['mkinitrd'], ['mkisofs'], ['mklost+found'], ['mkmanifest'], ['mknod'], ['mkntfs'], ['mkrfc2734'], ['mkroot'], ['mksquashfs'], ['mkswap'], ['mktemp'], ['mkzftree'], ['mlabel'], ['mlocate'], ['mmc-tool'], ['mmcli'], ['mmd'], ['mmount'], ['mmove'], ['modifyrepo'], ['modifyrepo_c'], ['modinfo'], ['modprobe'], ['module'], ['modulecmd'], ['modulemd-validator-v1'], ['mogrify'], ['mogrify-im6'], ['mogrify-im6.q16'], ['mokutil'], ['monitor-sensor'], ['montage'], ['montage-im6'], ['montage-im6.q16'], ['more'], ['mount'], ['mount.cifs'], ['mount.fuse'], ['mount.glusterfs'], ['mount.lowntfs-3g'], ['mount.nfs'], ['mount.nfs4'], ['mount.ntfs'], ['mount.ntfs-3g'], ['mount.ntfs-fuse'], ['mount.zfs'], ['mountpoint'], ['mountstats'], ['mousetweaks'], ['mpage'], ['mpartition'], ['mpathconf'], ['mpathpersist'], ['mppcheck'], ['mpplu'], ['mppprof'], ['mpris-proxy'], ['mrd'], ['mren'], ['mscompress'], ['msexpand'], ['msgattrib'], ['msgcat'], ['msgcmp'], ['msgcomm'], ['msgconv'], ['msgen'], ['msgexec'], ['msgfilter'], ['msgfmt'], ['msggrep'], ['msginit'], ['msgmerge'], ['msgunfmt'], ['msguniq'], ['mshortname'], ['mshowfat'], ['mt'], ['mt-gnu'], ['mtools'], ['mtoolstest'], ['mtr'], ['mtr-packet'], ['mtvtoppm'], ['mtype'], ['multipath'], ['multipathd'], ['mutter'], ['mv'], ['mvxattr'], ['mxtar'], ['mzip'], ['nail'], ['named-checkzone'], ['named-compilezone'], ['namei'], ['nameif'], ['nano'], ['nautilus'], ['nautilus-autorun-software'], ['nautilus-desktop'], ['nautilus-sendto'], ['nawk'], ['nc'], ['nc.openbsd'], ['ncal'], ['ncat'], ['ndctl'], ['ndptool'], ['neotoppm'], ['neqn'], ['netcat'], ['netkit-ftp'], ['netplan'], ['netstat'], ['nettest'], ['networkctl'], ['networkd-dispatcher'], ['new-kernel-pkg'], ['newgidmap'], ['newgrp'], ['newuidmap'], ['newusers'], ['nf-ct-add'], ['nf-ct-list'], ['nf-exp-add'], ['nf-exp-delete'], ['nf-exp-list'], ['nf-log'], ['nf-monitor'], ['nf-queue'], ['nfnl_osf'], ['nfsconf'], ['nfsdcltrack'], ['nfsidmap'], ['nfsiostat'], ['nfsstat'], ['nft'], ['ngettext'], ['nice'], ['nisdomainname'], ['nl'], ['nl-addr-add'], ['nl-addr-delete'], ['nl-addr-list'], ['nl-class-add'], ['nl-class-delete'], ['nl-class-list'], ['nl-classid-lookup'], ['nl-cls-add'], ['nl-cls-delete'], ['nl-cls-list'], ['nl-fib-lookup'], ['nl-link-enslave'], ['nl-link-ifindex2name'], ['nl-link-list'], ['nl-link-name2ifindex'], ['nl-link-release'], ['nl-link-set'], ['nl-link-stats'], ['nl-list-caches'], ['nl-list-sockets'], ['nl-monitor'], ['nl-neigh-add'], ['nl-neigh-delete'], ['nl-neigh-list'], ['nl-neightbl-list'], ['nl-pktloc-lookup'], ['nl-qdisc-add'], ['nl-qdisc-delete'], ['nl-qdisc-list'], ['nl-route-add'], ['nl-route-delete'], ['nl-route-get'], ['nl-route-list'], ['nl-rule-list'], ['nl-tctree-list'], ['nl-util-addr'], ['nm'], ['nm-applet'], ['nm-connection-editor'], ['nm-online'], ['nmblookup'], ['nmcli'], ['nmtui'], ['nmtui-connect'], ['nmtui-edit'], ['nmtui-hostname'], ['node'], ['nohup'], ['nologin'], ['notify-send'], ['npm'], ['nproc'], ['npx'], ['nroff'], ['nsec3hash'], ['nsenter'], ['nslookup'], ['nstat'], ['nsupdate'], ['ntfs-3g'], ['ntfs-3g.probe'], ['ntfscat'], ['ntfsck'], ['ntfsclone'], ['ntfscluster'], ['ntfscmp'], ['ntfscp'], ['ntfsdecrypt'], ['ntfsdump_logfile'], ['ntfsfallocate'], ['ntfsfix'], ['ntfsinfo'], ['ntfslabel'], ['ntfsls'], ['ntfsmftalloc'], ['ntfsmount'], ['ntfsmove'], ['ntfsrecover'], ['ntfsresize'], ['ntfssecaudit'], ['ntfstruncate'], ['ntfsundelete'], ['ntfsusermap'], ['ntfswipe'], ['numad'], ['numfmt'], ['nvidia-bug-report.sh'], ['nvidia-detector'], ['nvidia-settings'], ['oLschema2ldif'], ['oakdecode'], ['obexctl'], ['objcopy'], ['objdump'], ['oclock'], ['od'], ['oddjob_request'], ['oddjobd'], ['oeminst'], ['oldrdist'], ['on_ac_power'], ['oocalc'], ['oodraw'], ['ooffice'], ['ooimpress'], ['oomath'], ['ooviewdoc'], ['oowriter'], ['open'], ['openconnect'], ['openoffice.org'], ['openssl'], ['openvpn'], ['openvt'], ['opldecode'], ['optirun'], ['orc-bugreport'], ['orca'], ['orca-dm-wrapper'], ['os-prober'], ['osinfo-db-export'], ['osinfo-db-import'], ['osinfo-db-path'], ['osinfo-db-validate'], ['osinfo-detect'], ['osinfo-install-script'], ['osinfo-query'], ['ostree'], ['ownership'], ['p11-kit'], ['pacat'], ['pack200'], ['packer'], ['pacmd'], ['pactl'], ['padsp'], ['padsp-32'], ['pager'], ['palmtopnm'], ['pam-auth-update'], ['pam_console_apply'], ['pam_extrausers_chkpwd'], ['pam_extrausers_update'], ['pam_getenv'], ['pam_tally'], ['pam_tally2'], ['pam_timestamp_check'], ['pamcut'], ['pamdeinterlace'], ['pamdice'], ['pamfile'], ['pamoil'], ['pamon'], ['pamstack'], ['pamstretch'], ['pamstretch-gen'], ['panelctl'], ['pango-list'], ['pango-view'], ['paperconf'], ['paperconfig'], ['paplay'], ['paps'], ['parec'], ['parecord'], ['parsechangelog'], ['parted'], ['partprobe'], ['partx'], ['passwd'], ['paste'], ['pasuspender'], ['patch'], ['pathchk'], ['pathplot'], ['pax'], ['pax11publish'], ['pbmclean'], ['pbmlife'], ['pbmmake'], ['pbmmask'], ['pbmpage'], ['pbmpscale'], ['pbmreduce'], ['pbmtext'], ['pbmtextps'], ['pbmto10x'], ['pbmtoascii'], ['pbmtoatk'], ['pbmtobbnbg'], ['pbmtocmuwm'], ['pbmtoepsi'], ['pbmtoepson'], ['pbmtog3'], ['pbmtogem'], ['pbmtogo'], ['pbmtoicon'], ['pbmtolj'], ['pbmtomacp'], ['pbmtomda'], ['pbmtomgr'], ['pbmtonokia'], ['pbmtopgm'], ['pbmtopi3'], ['pbmtoplot'], ['pbmtoppa'], ['pbmtopsg3'], ['pbmtoptx'], ['pbmtowbmp'], ['pbmtox10bm'], ['pbmtoxbm'], ['pbmtoybm'], ['pbmtozinc'], ['pbmupc'], ['pccardctl'], ['pcimodules'], ['pcxtoppm'], ['pdata_tools'], ['pdb3'], ['pdb3.6'], ['pdf2dsc'], ['pdf2ps'], ['pdfdetach'], ['pdffonts'], ['pdfimages'], ['pdfinfo'], ['pdfseparate'], ['pdfsig'], ['pdftocairo'], ['pdftohtml'], ['pdftoppm'], ['pdftops'], ['pdftotext'], ['pdfunite'], ['peekfd'], ['perl'], ['perl5.26-x86_64-linux-gnu'], ['perl5.26.2'], ['perl5.28.1'], ['perlbug'], ['perldoc'], ['perli11ndoc'], ['perlivp'], ['perlthanks'], ['pf2afm'], ['pfbtopfa'], ['pftp'], ['pgmbentley'], ['pgmcrater'], ['pgmedge'], ['pgmenhance'], ['pgmhist'], ['pgmkernel'], ['pgmnoise'], ['pgmnorm'], ['pgmoil'], ['pgmramp'], ['pgmslice'], ['pgmtexture'], ['pgmtofs'], ['pgmtolispm'], ['pgmtopbm'], ['pgmtoppm'], ['pgrep'], ['pi1toppm'], ['pi3topbm'], ['pic'], ['pico'], ['piconv'], ['pidof'], ['pigz'], ['pinentry'], ['pinentry-curses'], ['pinentry-gnome3'], ['pinentry-gtk'], ['pinentry-gtk-2'], ['pinentry-x11'], ['pinfo'], ['ping'], ['ping4'], ['ping6'], ['pinky'], ['pip-3'], ['pip-3.7'], ['pip3'], ['pip3.7'], ['pipewire'], ['pitchplay'], ['pivot_root'], ['pjtoppm'], ['pkaction'], ['pkcheck'], ['pkcon'], ['pkexec'], ['pkg-config'], ['pkgconf'], ['pkill'], ['pkla-admin-identities'], ['pkla-check-authorization'], ['pkmon'], ['pkttyagent'], ['pl2pm'], ['pldd'], ['plipconfig'], ['plistutil'], ['plog'], ['pluginviewer'], ['plymouth'], ['plymouth-set-default-theme'], ['plymouthd'], ['pmap'], ['pngtopnm'], ['pnm2ppa'], ['pnmalias'], ['pnmarith'], ['pnmcat'], ['pnmcolormap'], ['pnmcomp'], ['pnmconvol'], ['pnmcrop'], ['pnmcut'], ['pnmdepth'], ['pnmenlarge'], ['pnmfile'], ['pnmflip'], ['pnmgamma'], ['pnmhisteq'], ['pnmhistmap'], ['pnmindex'], ['pnminterp'], ['pnminterp-gen'], ['pnminvert'], ['pnmmargin'], ['pnmmontage'], ['pnmnlfilt'], ['pnmnoraw'], ['pnmnorm'], ['pnmpad'], ['pnmpaste'], ['pnmpsnr'], ['pnmquant'], ['pnmremap'], ['pnmrotate'], ['pnmscale'], ['pnmscalefixed'], ['pnmshear'], ['pnmsmooth'], ['pnmsplit'], ['pnmtile'], ['pnmtoddif'], ['pnmtofiasco'], ['pnmtofits'], ['pnmtojpeg'], ['pnmtopalm'], ['pnmtoplainpnm'], ['pnmtopng'], ['pnmtops'], ['pnmtorast'], ['pnmtorle'], ['pnmtosgi'], ['pnmtosir'], ['pnmtotiff'], ['pnmtotiffcmyk'], ['pnmtoxwd'], ['pod2html'], ['pod2man'], ['pod2text'], ['pod2usage'], ['podchecker'], ['podselect'], ['poff'], ['pon'], ['popcon-largest-unused'], ['popd'], ['popularity-contest'], ['post-grohtml'], ['poweroff'], ['ppdc'], ['ppdhtml'], ['ppdi'], ['ppdmerge'], ['ppdpo'], ['pphs'], ['ppm3d'], ['ppmbrighten'], ['ppmchange'], ['ppmcie'], ['ppmcolormask'], ['ppmcolors'], ['ppmdim'], ['ppmdist'], ['ppmdither'], ['ppmfade'], ['ppmflash'], ['ppmforge'], ['ppmhist'], ['ppmlabel'], ['ppmmake'], ['ppmmix'], ['ppmnorm'], ['ppmntsc'], ['ppmpat'], ['ppmquant'], ['ppmquantall'], ['ppmqvga'], ['ppmrainbow'], ['ppmrelief'], ['ppmshadow'], ['ppmshift'], ['ppmspread'], ['ppmtoacad'], ['ppmtobmp'], ['ppmtoeyuv'], ['ppmtogif'], ['ppmtoicr'], ['ppmtoilbm'], ['ppmtojpeg'], ['ppmtoleaf'], ['ppmtolj'], ['ppmtomap'], ['ppmtomitsu'], ['ppmtompeg'], ['ppmtoneo'], ['ppmtopcx'], ['ppmtopgm'], ['ppmtopi1'], ['ppmtopict'], ['ppmtopj'], ['ppmtopuzz'], ['ppmtorgb3'], ['ppmtosixel'], ['ppmtotga'], ['ppmtouil'], ['ppmtowinicon'], ['ppmtoxpm'], ['ppmtoyuv'], ['ppmtoyuvsplit'], ['ppmtv'], ['ppp-watch'], ['pppconfig'], ['pppd'], ['pppdump'], ['pppoe'], ['pppoe-connect'], ['pppoe-discovery'], ['pppoe-relay'], ['pppoe-server'], ['pppoe-setup'], ['pppoe-sniff'], ['pppoe-start'], ['pppoe-status'], ['pppoe-stop'], ['pppoeconf'], ['pppstats'], ['pptp'], ['pptpsetup'], ['pr'], ['pre-grohtml'], ['precat'], ['preconv'], ['preunzip'], ['prezip'], ['prezip-bin'], ['primusrun'], ['print'], ['printafm'], ['printcal'], ['printenv'], ['printer-profile'], ['printerbanner'], ['printf'], ['printtarg'], ['prlimit'], ['profcheck'], ['prove'], ['prtstat'], ['ps'], ['ps2ascii'], ['ps2epsi'], ['ps2pdf'], ['ps2pdf12'], ['ps2pdf13'], ['ps2pdf14'], ['ps2pdfwr'], ['ps2ps'], ['ps2ps2'], ['ps2txt'], ['psfaddtable'], ['psfgettable'], ['psfstriptable'], ['psfxtable'], ['psicc'], ['psidtopgm'], ['pslog'], ['pstack'], ['pstopnm'], ['pstree'], ['pstree.x11'], ['ptar'], ['ptardiff'], ['ptargrep'], ['ptx'], ['pulseaudio'], ['pushd'], ['pvchange'], ['pvck'], ['pvcreate'], ['pvdisplay'], ['pvmove'], ['pvremove'], ['pvresize'], ['pvs'], ['pvscan'], ['pwck'], ['pwconv'], ['pwd'], ['pwdx'], ['pwhistory_helper'], ['pwmake'], ['pwqcheck'], ['pwqgen'], ['pwscore'], ['pwunconv'], ['py3clean'], ['py3compile'], ['py3versions'], ['pydoc3'], ['pydoc3.6'], ['pydoc3.7'], ['pygettext3'], ['pygettext3.6'], ['pyjwt3'], ['python'], ['python3'], ['python3-chardetect'], ['python3-coverage'], ['python3-mako-render'], ['python3-pyinotify'], ['python3.6'], ['python3.6m'], ['python3.7'], ['python3.7m'], ['python3m'], ['pyvenv'], ['pyvenv-3.7'], ['pzstd'], ['qb-blackbox'], ['qdbus'], ['qemu-ga'], ['qemu-img'], ['qemu-io'], ['qemu-keymap'], ['qemu-kvm'], ['qemu-nbd'], ['qemu-pr-helper'], ['qemu-system-i386'], ['qemu-system-x86_64'], ['qmi-firmware-update'], ['qmi-network'], ['qmicli'], ['qpdf'], ['qpdldecode'], ['qrttoppm'], ['quirks-handler'], ['quot'], ['quota'], ['quotacheck'], ['quotaoff'], ['quotaon'], ['quotastats'], ['quotasync'], ['quote'], ['quote_readline'], ['radvd'], ['radvdump'], ['raid-check'], ['ranlib'], ['rapper'], ['rasttopnm'], ['raw'], ['rawtopgm'], ['rawtoppm'], ['rb'], ['rbash'], ['rcp'], ['rctest'], ['rdfproc'], ['rdisc'], ['rdist'], ['rdistd'], ['rdma'], ['rdma-ndd'], ['read'], ['readarray'], ['readelf'], ['readlink'], ['readmult'], ['readonly'], ['readprofile'], ['realm'], ['realpath'], ['reboot'], ['recode-sr-latin'], ['recountdiff'], ['red'], ['rediff'], ['redland-db-upgrade'], ['refine'], ['regdbdump'], ['regdiff'], ['regpatch'], ['regshell'], ['regtree'], ['reject'], ['remmina'], ['remmina-gnome'], ['remove-default-ispell'], ['remove-default-wordlist'], ['remove-shell'], ['rename'], ['rename.ul'], ['rendercheck'], ['renew-dummy-cert'], ['renice'], ['report-cli'], ['report-gtk'], ['reporter-bugzilla'], ['reporter-kerneloops'], ['reporter-print'], ['reporter-systemd-journal'], ['reporter-upload'], ['reporter-ureport'], ['repquota'], ['request-key'], ['reset'], ['resize2fs'], ['resizecons'], ['resizepart'], ['resolvconf'], ['resolvectl'], ['restorecon'], ['restorecon_xattr'], ['return'], ['rev'], ['revfix'], ['rfcomm'], ['rfkill'], ['rgb3toppm'], ['rgrep'], ['rhythmbox'], ['rhythmbox-client'], ['rletopnm'], ['rlogin'], ['rm'], ['rmdir'], ['rmid'], ['rmiregistry'], ['rmmod'], ['rmt'], ['rmt-tar'], ['rnano'], ['rngd'], ['rngtest'], ['rofiles-fuse'], ['roqet'], ['rotatelogs'], ['route'], ['routef'], ['routel'], ['rpc.gssd'], ['rpc.idmapd'], ['rpc.mountd'], ['rpc.nfsd'], ['rpc.statd'], ['rpcbind'], ['rpcclient'], ['rpcdebug'], ['rpcinfo'], ['rpm'], ['rpm2archive'], ['rpm2cpio'], ['rpmargs'], ['rpmbuild'], ['rpmdb'], ['rpmdev-bumpspec'], ['rpmdev-checksig'], ['rpmdev-cksum'], ['rpmdev-diff'], ['rpmdev-extract'], ['rpmdev-md5'], ['rpmdev-newinit'], ['rpmdev-newspec'], ['rpmdev-packager'], ['rpmdev-rmdevelrpms'], ['rpmdev-setuptree'], ['rpmdev-sha1'], ['rpmdev-sha224'], ['rpmdev-sha256'], ['rpmdev-sha384'], ['rpmdev-sha512'], ['rpmdev-sort'], ['rpmdev-sum'], ['rpmdev-vercmp'], ['rpmdev-wipetree'], ['rpmdumpheader'], ['rpmelfsym'], ['rpmfile'], ['rpminfo'], ['rpmkeys'], ['rpmls'], ['rpmpeek'], ['rpmquery'], ['rpmsodiff'], ['rpmsoname'], ['rpmspec'], ['rpmverify'], ['rsh'], ['rstart'], ['rstartd'], ['rsync'], ['rsyslogd'], ['rtacct'], ['rtcwake'], ['rtkitctl'], ['rtmon'], ['rtpr'], ['rtstat'], ['run-mailcap'], ['run-on-binaries-in'], ['run-parts'], ['run-with-aspell'], ['runcon'], ['runlevel'], ['runuser'], ['rvi'], ['rview'], ['rx'], ['rxe_cfg'], ['rygel'], ['rygel-preferences'], ['rz'], ['sa'], ['samba-regedit'], ['sandbox'], ['sane-find-scanner'], ['saned'], ['saslauthd'], ['sasldblistusers2'], ['saslpasswd2'], ['satyr'], ['savelog'], ['sb'], ['sbattach'], ['sbcdec'], ['sbcenc'], ['sbcinfo'], ['sbigtopgm'], ['sbkeysync'], ['sbsiglist'], ['sbsign'], ['sbvarsign'], ['sbverify'], ['scanimage'], ['scanin'], ['scl'], ['scl_enabled'], ['scl_source'], ['scp'], ['scp-dbus-service'], ['screendump'], ['script'], ['scriptreplay'], ['sctp_darn'], ['sctp_status'], ['sctp_test'], ['sdiff'], ['sdptool'], ['seahorse'], ['secon'], ['secret-tool'], ['sed'], ['sedismod'], ['sedispol'], ['see'], ['sefcontext_compile'], ['selabel_digest'], ['selabel_lookup'], ['selabel_lookup_best_match'], ['selabel_partial_match'], ['select'], ['select-default-ispell'], ['select-default-iwrap'], ['select-default-wordlist'], ['select-editor'], ['selinux_check_access'], ['selinuxconlist'], ['selinuxdefcon'], ['selinuxenabled'], ['selinuxexeccon'], ['semanage'], ['semodule'], ['semodule_expand'], ['semodule_link'], ['semodule_package'], ['semodule_unpackage'], ['sendiso'], ['sendmail'], ['sensible-browser'], ['sensible-editor'], ['sensible-pager'], ['seq'], ['service'], ['session-migration'], ['sessreg'], ['sestatus'], ['set'], ['setarch'], ['setcap'], ['setcifsacl'], ['setenforce'], ['setfacl'], ['setfattr'], ['setfiles'], ['setfont'], ['setkeycodes'], ['setleds'], ['setlogcons'], ['setmetamode'], ['setpci'], ['setpriv'], ['setquota'], ['setregdomain'], ['setsebool'], ['setsid'], ['setterm'], ['setup-nsssysinit'], ['setup-nsssysinit.sh'], ['setupcon'], ['setvesablank'], ['setvtrgb'], ['setxkbmap'], ['sfdisk'], ['sftp'], ['sg'], ['sgdisk'], ['sgitopnm'], ['sgpio'], ['sh'], ['sh.distrib'], ['sha1hmac'], ['sha1sum'], ['sha224hmac'], ['sha224sum'], ['sha256hmac'], ['sha256sum'], ['sha384hmac'], ['sha384sum'], ['sha512hmac'], ['sha512sum'], ['shadowconfig'], ['sharesec'], ['shasum'], ['sheep'], ['sheepfs'], ['shepherd'], ['shift'], ['shopt'], ['shotwell'], ['showconsolefont'], ['showkey'], ['showmount'], ['showrgb'], ['shred'], ['shuf'], ['shutdown'], ['simple-scan'], ['simpprof'], ['sirtopnm'], ['size'], ['skdump'], ['skill'], ['sktest'], ['slabtop'], ['slattach'], ['sldtoppm'], ['sleep'], ['slogin'], ['slxdecode'], ['sm-notify'], ['smbcacls'], ['smbclient'], ['smbcquotas'], ['smbget'], ['smbspool'], ['smbtar'], ['smbtree'], ['smproxy'], ['snap'], ['snapctl'], ['snapfuse'], ['sndfile-resample'], ['snice'], ['soelim'], ['soffice'], ['software-properties-gtk'], ['sol'], ['sort'], ['sosreport'], ['sotruss'], ['soundstretch'], ['source'], ['spax'], ['spctoppm'], ['spd-conf'], ['spd-say'], ['speak-ng'], ['speaker-test'], ['spec2cie'], ['specplot'], ['spectool'], ['speech-dispatcher'], ['spellintian'], ['spellout'], ['spice-vdagent'], ['spice-vdagentd'], ['splain'], ['split'], ['splitdiff'], ['splitfont'], ['splitti3'], ['spotread'], ['sprof'], ['sputoppm'], ['sqlite3'], ['sqliterepo_c'], ['ss'], ['ssh'], ['ssh-add'], ['ssh-agent'], ['ssh-argv0'], ['ssh-copy-id'], ['ssh-keygen'], ['ssh-keyscan'], ['sshd'], ['sshpass'], ['sss_cache'], ['sss_ssh_authorizedkeys'], ['sss_ssh_knownhostsproxy'], ['sssd'], ['st4topgm'], ['start-pulseaudio-x11'], ['start-statd'], ['start-stop-daemon'], ['startx'], ['stat'], ['static-sh'], ['stdbuf'], ['strace'], ['strace-log-merge'], ['stream'], ['stream-im6'], ['stream-im6.q16'], ['strings'], ['strip'], ['stty'], ['stunbdc'], ['stund'], ['su'], ['sudo'], ['sudoedit'], ['sudoreplay'], ['sulogin'], ['sum'], ['sushi'], ['suspend'], ['swaplabel'], ['swapoff'], ['swapon'], ['switch_root'], ['switcheroo-control'], ['switchml'], ['sx'], ['symcryptrun'], ['symlinks'], ['sync'], ['synthcal'], ['synthread'], ['sysctl'], ['syslinux'], ['syslinux-legacy'], ['system-config-abrt'], ['system-config-printer'], ['system-config-printer-applet'], ['systemctl'], ['systemd'], ['systemd-analyze'], ['systemd-ask-password'], ['systemd-cat'], ['systemd-cgls'], ['systemd-cgtop'], ['systemd-delta'], ['systemd-detect-virt'], ['systemd-escape'], ['systemd-firstboot'], ['systemd-hwdb'], ['systemd-inhibit'], ['systemd-machine-id-setup'], ['systemd-mount'], ['systemd-notify'], ['systemd-nspawn'], ['systemd-path'], ['systemd-resolve'], ['systemd-run'], ['systemd-socket-activate'], ['systemd-stdio-bridge'], ['systemd-sysusers'], ['systemd-tmpfiles'], ['systemd-tty-ask-password-agent'], ['systemd-umount'], ['sz'], ['t1ascii'], ['t1asm'], ['t1binary'], ['t1disasm'], ['t1mac'], ['t1unmac'], ['tabs'], ['tac'], ['tail'], ['tar'], ['tarcat'], ['targen'], ['taskset'], ['tbl'], ['tc'], ['tcbench'], ['tclsh'], ['tclsh8.6'], ['tcpdump'], ['tcpslice'], ['tcptraceroute'], ['tcsd'], ['teamd'], ['teamdctl'], ['teamnl'], ['tee'], ['telinit'], ['telnet'], ['telnet.netkit'], ['tempfile'], ['test'], ['testlibraw'], ['testsaslauthd'], ['tgatoppm'], ['tgz'], ['then'], ['thermald'], ['thin_check'], ['thin_delta'], ['thin_dump'], ['thin_ls'], ['thin_metadata_size'], ['thin_repair'], ['thin_restore'], ['thin_rmap'], ['thin_trim'], ['thinkjettopbm'], ['thunderbird'], ['tic'], ['tiffgamut'], ['tifftopnm'], ['tificc'], ['time'], ['timedatectl'], ['timedatex'], ['timeout'], ['times'], ['tipc'], ['tload'], ['tmux'], ['toe'], ['top'], ['totem'], ['totem-video-thumbnailer'], ['touch'], ['tpm2-abrmd'], ['tpm2_activatecredential'], ['tpm2_certify'], ['tpm2_create'], ['tpm2_createpolicy'], ['tpm2_createprimary'], ['tpm2_dictionarylockout'], ['tpm2_encryptdecrypt'], ['tpm2_evictcontrol'], ['tpm2_getcap'], ['tpm2_getmanufec'], ['tpm2_getpubak'], ['tpm2_getpubek'], ['tpm2_getrandom'], ['tpm2_hash'], ['tpm2_hmac'], ['tpm2_listpersistent'], ['tpm2_load'], ['tpm2_loadexternal'], ['tpm2_makecredential'], ['tpm2_nvdefine'], ['tpm2_nvlist'], ['tpm2_nvread'], ['tpm2_nvreadlock'], ['tpm2_nvrelease'], ['tpm2_nvwrite'], ['tpm2_pcrevent'], ['tpm2_pcrextend'], ['tpm2_pcrlist'], ['tpm2_quote'], ['tpm2_rc_decode'], ['tpm2_readpublic'], ['tpm2_rsadecrypt'], ['tpm2_rsaencrypt'], ['tpm2_send'], ['tpm2_sign'], ['tpm2_startup'], ['tpm2_takeownership'], ['tpm2_unseal'], ['tpm2_verifysignature'], ['tput'], ['tr'], ['tracepath'], ['tracepath6'], ['traceroute'], ['traceroute6'], ['traceroute6.iputils'], ['tracker'], ['transicc'], ['transmission-gtk'], ['transset'], ['trap'], ['tree'], ['troff'], ['true'], ['truncate'], ['trust'], ['tset'], ['tsig-keygen'], ['tsort'], ['ttfread'], ['tty'], ['tune2fs'], ['txt2ti3'], ['type'], ['typeset'], ['tzconfig'], ['tzselect'], ['u-d-c-print-pci-ids'], ['ua'], ['ubuntu-advantage'], ['ubuntu-bug'], ['ubuntu-core-launcher'], ['ubuntu-drivers'], ['ubuntu-report'], ['ubuntu-software'], ['ubuntu-support-status'], ['ucf'], ['ucfq'], ['ucfr'], ['ucs2any'], ['udevadm'], ['udisksctl'], ['ufw'], ['ul'], ['ulimit'], ['ulockmgr_server'], ['umask'], ['umax_pp'], ['umount'], ['umount.nfs'], ['umount.nfs4'], ['umount.udisks2'], ['unalias'], ['uname'], ['uname26'], ['unattended-upgrade'], ['unattended-upgrades'], ['unbound-anchor'], ['uncompress'], ['unexpand'], ['unicode_start'], ['unicode_stop'], ['uniq'], ['unity-scope-loader'], ['unix2dos'], ['unix2mac'], ['unix_chkpwd'], ['unix_update'], ['unlink'], ['unlz4'], ['unlzma'], ['unmkinitramfs'], ['unoconv'], ['unopkg'], ['unpack200'], ['unpigz'], ['unset'], ['unshare'], ['unsquashfs'], ['until'], ['unwrapdiff'], ['unxz'], ['unzip'], ['unzipsfx'], ['unzstd'], ['update-alternatives'], ['update-ca-certificates'], ['update-ca-trust'], ['update-cracklib'], ['update-crypto-policies'], ['update-default-aspell'], ['update-default-ispell'], ['update-default-wordlist'], ['update-desktop-database'], ['update-dictcommon-aspell'], ['update-dictcommon-hunspell'], ['update-fonts-alias'], ['update-fonts-dir'], ['update-fonts-scale'], ['update-grub'], ['update-grub-gfxpayload'], ['update-grub2'], ['update-gsfontmap'], ['update-gtk-immodules'], ['update-icon-caches'], ['update-inetd'], ['update-info-dir'], ['update-initramfs'], ['update-locale'], ['update-manager'], ['update-mime'], ['update-mime-database'], ['update-notifier'], ['update-passwd'], ['update-pciids'], ['update-perl-sax-parsers'], ['update-rc.d'], ['update-secureboot-policy'], ['update-usbids'], ['updatedb'], ['updatedb.mlocate'], ['upgrade-from-grub-legacy'], ['upower'], ['uptime'], ['usb-creator-gtk'], ['usb-devices'], ['usb_modeswitch'], ['usb_modeswitch_dispatcher'], ['usb_printerid'], ['usbhid-dump'], ['usbmuxd'], ['useradd'], ['userdel'], ['userhelper'], ['usermod'], ['users'], ['usleep'], ['utmpdump'], ['uuidd'], ['uuidgen'], ['uuidparse'], ['uz'], ['validlocale'], ['vconfig'], ['vcstime'], ['vdir'], ['vdptool'], ['vgcfgbackup'], ['vgcfgrestore'], ['vgchange'], ['vgck'], ['vgconvert'], ['vgcreate'], ['vgdisplay'], ['vgexport'], ['vgextend'], ['vgimport'], ['vgimportclone'], ['vglclient'], ['vglconfig'], ['vglconnect'], ['vglgenkey'], ['vgllogin'], ['vglrun'], ['vglserver_config'], ['vglxinfo'], ['vgmerge'], ['vgmknodes'], ['vgreduce'], ['vgremove'], ['vgrename'], ['vgs'], ['vgscan'], ['vgsplit'], ['vi'], ['via_regs_dump'], ['view'], ['viewgam'], ['viewres'], ['vigr'], ['vim.tiny'], ['vipw'], ['virtfs-proxy-helper'], ['virtlockd'], ['virtlogd'], ['visudo'], ['vlock'], ['vm-support'], ['vmcore-dmesg'], ['vmhgfs-fuse'], ['vmstat'], ['vmtoolsd'], ['vmware-checkvm'], ['vmware-guestproxycerttool'], ['vmware-hgfsclient'], ['vmware-namespace-cmd'], ['vmware-rpctool'], ['vmware-toolbox-cmd'], ['vmware-user'], ['vmware-user-suid-wrapper'], ['vmware-vgauth-cmd'], ['vmware-vmblock-fuse'], ['vmware-xferlogs'], ['vmwarectrl'], ['vncconfig'], ['vncpasswd'], ['volname'], ['vpddecode'], ['vpnc'], ['vpnc-disconnect'], ['vstp'], ['w'], ['w.procps'], ['wait'], ['wall'], ['watch'], ['watchgnupg'], ['wavpack'], ['wbmptopbm'], ['wc'], ['wdctl'], ['weak-modules'], ['wget'], ['whatis'], ['whereis'], ['which'], ['while'], ['whiptail'], ['who'], ['whoami'], ['whois'], ['whoopsie'], ['whoopsie-preferences'], ['winicontoppm'], ['wipefs'], ['withsctp'], ['wnck-urgency-monitor'], ['word-list-compress'], ['wpa_action'], ['wpa_cli'], ['wpa_passphrase'], ['wpa_supplicant'], ['write'], ['wvgain'], ['wvtag'], ['wvunpack'], ['x-session-manager'], ['x-terminal-emulator'], ['x-window-manager'], ['x-www-browser'], ['x11perf'], ['x11perfcomp'], ['x86_64'], ['x86_64-linux-gnu-addr2line'], ['x86_64-linux-gnu-ar'], ['x86_64-linux-gnu-as'], ['x86_64-linux-gnu-c++filt'], ['x86_64-linux-gnu-cpp'], ['x86_64-linux-gnu-cpp-8'], ['x86_64-linux-gnu-dwp'], ['x86_64-linux-gnu-elfedit'], ['x86_64-linux-gnu-gold'], ['x86_64-linux-gnu-gprof'], ['x86_64-linux-gnu-ld'], ['x86_64-linux-gnu-ld.bfd'], ['x86_64-linux-gnu-ld.gold'], ['x86_64-linux-gnu-nm'], ['x86_64-linux-gnu-objcopy'], ['x86_64-linux-gnu-objdump'], ['x86_64-linux-gnu-ranlib'], ['x86_64-linux-gnu-readelf'], ['x86_64-linux-gnu-size'], ['x86_64-linux-gnu-strings'], ['x86_64-linux-gnu-strip'], ['x86_64-redhat-linux-gcc'], ['x86_64-redhat-linux-gcc-8'], ['x86_64-redhat-linux-gnu-pkg-config'], ['xargs'], ['xauth'], ['xbiff'], ['xbmtopbm'], ['xbrlapi'], ['xcalc'], ['xclipboard'], ['xclock'], ['xcmsdb'], ['xconsole'], ['xcursorgen'], ['xcutsel'], ['xdg-desktop-icon'], ['xdg-desktop-menu'], ['xdg-email'], ['xdg-icon-resource'], ['xdg-mime'], ['xdg-open'], ['xdg-screensaver'], ['xdg-settings'], ['xdg-user-dir'], ['xdg-user-dirs-gtk-update'], ['xdg-user-dirs-update'], ['xditview'], ['xdpyinfo'], ['xdriinfo'], ['xedit'], ['xev'], ['xeyes'], ['xfd'], ['xfontsel'], ['xfs_admin'], ['xfs_bmap'], ['xfs_copy'], ['xfs_db'], ['xfs_estimate'], ['xfs_freeze'], ['xfs_fsr'], ['xfs_growfs'], ['xfs_info'], ['xfs_io'], ['xfs_logprint'], ['xfs_mdrestore'], ['xfs_metadump'], ['xfs_mkfile'], ['xfs_ncheck'], ['xfs_quota'], ['xfs_repair'], ['xfs_rtcp'], ['xfs_scrub'], ['xfs_scrub_all'], ['xfs_spaceman'], ['xgamma'], ['xgc'], ['xgettext'], ['xhost'], ['xicclu'], ['ximtoppm'], ['xinit'], ['xinput'], ['xkbbell'], ['xkbcomp'], ['xkbevd'], ['xkbprint'], ['xkbvleds'], ['xkbwatch'], ['xkeystone'], ['xkill'], ['xload'], ['xlogo'], ['xlsatoms'], ['xlsclients'], ['xlsfonts'], ['xmag'], ['xman'], ['xmessage'], ['xmlcatalog'], ['xmllint'], ['xmlsec1'], ['xmlwf'], ['xmodmap'], ['xmore'], ['xpmtoppm'], ['xprop'], ['xqmstats'], ['xqxdecode'], ['xrandr'], ['xrdb'], ['xrefresh'], ['xset'], ['xsetmode'], ['xsetpointer'], ['xsetroot'], ['xsetwacom'], ['xsltproc'], ['xsm'], ['xstdcmap'], ['xsubpp'], ['xtables-legacy-multi'], ['xtables-multi'], ['xvidtune'], ['xvinfo'], ['xvminitoppm'], ['xwd'], ['xwdtopnm'], ['xwininfo'], ['xwud'], ['xxd'], ['xz'], ['xzcat'], ['xzcmp'], ['xzdec'], ['xzdiff'], ['xzegrep'], ['xzfgrep'], ['xzgrep'], ['xzless'], ['xzmore'], ['ybmtopbm'], ['yelp'], ['yes'], ['ypdomainname'], ['yum'], ['yuvsplittoppm'], ['yuvtoppm'], ['zcat'], ['zcmp'], ['zdb'], ['zdiff'], ['zdump'], ['zegrep'], ['zeisstopnm'], ['zeitgeist-daemon'], ['zenity'], ['zfgrep'], ['zforce'], ['zfs'], ['zfs-fuse'], ['zfs-fuse-helper'], ['zgrep'], ['zic'], ['zip'], ['zipcloak'], ['zipdetails'], ['zipgrep'], ['zipinfo'], ['zipnote'], ['zipsplit'], ['zjsdecode'], ['zless'], ['zlib-flate'], ['zmore'], ['znew'], ['zpool'], ['zramctl'], ['zramstart'], ['zramstop'], ['zsoelim'], ['zstd'], ['zstdcat'], ['zstdgrep'], ['zstdless'], ['zstdmt'], ['zstreamdump'], ['ztest'], ['{'], ['}'], ['vim'], ['htop']]
};

/***/ }),

/***/ 4057:
/*!*********************************************************!*\
  !*** ./src/app/terminals/bashbrawl/languages/csharp.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   csharpConfig: () => (/* binding */ csharpConfig)
/* harmony export */ });
const csharpConfig = {
  name: 'C#',
  cmds: [['abstract'], ['as'], ['base'], ['bool'], ['break'], ['byte'], ['case'], ['catch'], ['char'], ['checked'], ['class'], ['const'], ['continue'], ['decimal'], ['default'], ['delegate'], ['do'], ['double'], ['else'], ['enum'], ['event'], ['explicit'], ['extern'], ['false'], ['finally'], ['fixed'], ['float'], ['for'], ['foreach'], ['goto'], ['if'], ['implicit'], ['in'], ['int'], ['interface'], ['internal'], ['is'], ['lock'], ['long'], ['namespace'], ['new'], ['null'], ['object'], ['operator'], ['out'], ['override'], ['params'], ['private'], ['protected'], ['public'], ['readonly'], ['ref'], ['return'], ['sbyte'], ['sealed'], ['short'], ['sizeof'], ['stackalloc'], ['static'], ['string'], ['struct'], ['switch'], ['this'], ['throw'], ['true'], ['try'], ['typeof'], ['uint'], ['ulong'], ['unchecked'], ['unsafe'], ['ushort'], ['using'], ['virtual'], ['void'], ['volatile'], ['while'], ['add'], ['allows'], ['alias'], ['and'], ['ascending'], ['args'], ['async'], ['await'], ['by'], ['descending'], ['dynamic'], ['equals'], ['field'], ['file'], ['from'], ['get'], ['global'], ['group'], ['init'], ['into'], ['join'], ['let'], ['managed'], ['nameof'], ['nint'], ['not'], ['notnull'], ['nuint'], ['on'], ['or'], ['orderby'], ['partial'], ['record'], ['remove'], ['required'], ['scoped'], ['select'], ['set'], ['unmanaged'], ['value'], ['var'], ['when'], ['where'], ['with'], ['yield'], ['&'], ['|'], ['+'], ['-'], ['*'], ['/'], ['++'], ['&&'], ['||'], ['^'], ['??'], ['%'], ['?'], [':'], ['=>'], ['<'], ['>'], ['=='], ['+='], ['-='], ['*='], ['/='], ['^='], ['>>='], ['>>>='], ['??='], ['&='], ['|='], ['%='], ['..'], ['<<'], ['>>'], ['>>>'], ['?.'], ['!'], ['::'], ['$'], ['@'], ['"""'], ['///'], ['#']]
};

/***/ }),

/***/ 7266:
/*!******************************************************!*\
  !*** ./src/app/terminals/bashbrawl/languages/css.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   cssConfig: () => (/* binding */ cssConfig)
/* harmony export */ });
/** Taken from https://www.w3schools.com/cssref/index.php **/
const cssConfig = {
  name: 'css',
  cmds: [
  // Properties
  ['accent-color'], ['align-content'], ['align-items'], ['align-self'], ['all'], ['animation'], ['animation-delay'], ['animation-direction'], ['animation-duration'], ['animation-fill-mode'], ['animation-iteration-count'], ['animation-name'], ['animation-play-state'], ['animation-timing-function'], ['aspect-ratio'], ['backdrop-filter'], ['backface-visibility'], ['background'], ['background-attachment'], ['background-blend-mode'], ['background-clip'], ['background-color'], ['background-image'], ['background-origin'], ['background-position'], ['background-position-x'], ['background-position-y'], ['background-repeat'], ['background-size'], ['block-size'], ['border'], ['border-block'], ['border-block-color'], ['border-block-end'], ['border-block-end-color'], ['border-block-end-style'], ['border-block-end-width'], ['border-block-start'], ['border-block-start-color'], ['border-block-start-style'], ['border-block-start-width'], ['border-block-style'], ['border-block-width'], ['border-bottom'], ['border-bottom-color'], ['border-bottom-left-radius'], ['border-bottom-right-radius'], ['border-bottom-style'], ['border-bottom-width'], ['border-collapse'], ['border-color'], ['border-end-end-radius'], ['border-end-start-radius'], ['border-image'], ['border-image-outset'], ['border-image-repeat'], ['border-image-slice'], ['border-image-source'], ['border-image-width'], ['border-inline'], ['border-inline-color'], ['border-inline-end'], ['border-inline-end-color'], ['border-inline-end-style'], ['border-inline-end-width'], ['border-inline-start'], ['border-inline-start-color'], ['border-inline-start-style'], ['border-inline-start-width'], ['border-inline-style'], ['border-inline-width'], ['border-left'], ['border-left-color'], ['border-left-style'], ['border-left-width'], ['border-radius'], ['border-right'], ['border-right-color'], ['border-right-style'], ['border-right-width'], ['border-spacing'], ['border-start-end-radius'], ['border-start-start-radius'], ['border-style'], ['border-top'], ['border-top-color'], ['border-top-left-radius'], ['border-top-right-radius'], ['border-top-style'], ['border-top-width'], ['border-width'], ['bottom'], ['box-decoration-break'], ['box-reflect'], ['box-shadow'], ['box-sizing'], ['break-after'], ['break-before'], ['break-inside'], ['caption-side'], ['caret-color'], ['charset'], ['clear'], ['clip'], ['clip-path'], ['color'], ['color-scheme'], ['column-count'], ['column-fill'], ['column-gap'], ['column-rule'], ['column-rule-color'], ['column-rule-style'], ['column-rule-width'], ['column-span'], ['column-width'], ['columns'], ['container'], ['content'], ['counter-increment'], ['counter-reset'], ['counter-set'], ['counter-style'], ['cursor'], ['direction'], ['display'], ['empty-cells'], ['filter'], ['flex'], ['flex-basis'], ['flex-direction'], ['flex-flow'], ['flex-grow'], ['flex-shrink'], ['flex-wrap'], ['float'], ['font'], ['font-face'], ['font-family'], ['font-feature-settings'], ['font-kerning'], ['font-palette-values'], ['font-size'], ['font-size-adjust'], ['font-stretch'], ['font-style'], ['font-variant'], ['font-variant-caps'], ['font-weight'], ['gap'], ['grid'], ['grid-area'], ['grid-auto-columns'], ['grid-auto-flow'], ['grid-auto-rows'], ['grid-column'], ['grid-column-end'], ['grid-column-start'], ['grid-row'], ['grid-row-end'], ['grid-row-start'], ['grid-template'], ['grid-template-areas'], ['grid-template-columns'], ['grid-template-rows'], ['hanging-punctuation'], ['height'], ['hyphens'], ['hyphenate-character'], ['image-rendering'], ['import'], ['initial-letter'], ['inline-size'], ['inset'], ['inset-block'], ['inset-block-end'], ['inset-block-start'], ['inset-inline'], ['inset-inline-end'], ['inset-inline-start'], ['isolation'], ['justify-content'], ['justify-items'], ['justify-self'], ['keyframes'], ['layer'], ['left'], ['letter-spacing'], ['line-height'], ['list-style'], ['list-style-image'], ['list-style-position'], ['list-style-type'], ['margin'], ['margin-block'], ['margin-block-end'], ['margin-block-start'], ['margin-bottom'], ['margin-inline'], ['margin-inline-end'], ['margin-inline-start'], ['margin-left'], ['margin-right'], ['margin-top'], ['marker'], ['marker-end'], ['marker-mid'], ['marker-start'], ['mask'], ['mask-clip'], ['mask-composite'], ['mask-image'], ['mask-mode'], ['mask-origin'], ['mask-position'], ['mask-repeat'], ['mask-size'], ['mask-type'], ['max-block-size'], ['max-height'], ['max-inline-size'], ['max-width'], ['media'], ['min-block-size'], ['min-inline-size'], ['min-height'], ['min-width'], ['mix-blend-mode'], ['namespace'], ['object-fit'], ['object-position'], ['offset'], ['offset-anchor'], ['offset-distance'], ['offset-path'], ['offset-position'], ['offset-rotate'], ['opacity'], ['order'], ['orphans'], ['outline'], ['outline-color'], ['outline-offset'], ['outline-style'], ['outline-width'], ['overflow'], ['overflow-anchor'], ['overflow-wrap'], ['overflow-x'], ['overflow-y'], ['overscroll-behavior'], ['overscroll-behavior-block'], ['overscroll-behavior-inline'], ['overscroll-behavior-x'], ['overscroll-behavior-y'], ['padding'], ['padding-block'], ['padding-block-end'], ['padding-block-start'], ['padding-bottom'], ['padding-inline'], ['padding-inline-end'], ['padding-inline-start'], ['padding-left'], ['padding-right'], ['padding-top'], ['page'], ['page-break-after'], ['page-break-before'], ['page-break-inside'], ['paint-order'], ['perspective'], ['perspective-origin'], ['place-content'], ['place-items'], ['place-self'], ['pointer-events'], ['position'], ['property'], ['quotes'], ['resize'], ['right'], ['rotate'], ['row-gap'], ['scale'], ['scope'], ['scroll-behavior'], ['scroll-margin'], ['scroll-margin-block'], ['scroll-margin-block-end'], ['scroll-margin-block-start'], ['scroll-margin-bottom'], ['scroll-margin-inline'], ['scroll-margin-inline-end'], ['scroll-margin-inline-start'], ['scroll-margin-left'], ['scroll-margin-right'], ['scroll-margin-top'], ['scroll-padding'], ['scroll-padding-block'], ['scroll-padding-block-end'], ['scroll-padding-block-start'], ['scroll-padding-bottom'], ['scroll-padding-inline'], ['scroll-padding-inline-end'], ['scroll-padding-inline-start'], ['scroll-padding-left'], ['scroll-padding-right'], ['scroll-padding-top'], ['scroll-snap-align'], ['scroll-snap-stop'], ['scroll-snap-type'], ['scrollbar-color'], ['shape-outside'], ['starting-style'], ['supports'], ['tab-size'], ['table-layout'], ['text-align'], ['text-align-last'], ['text-decoration'], ['text-decoration-color'], ['text-decoration-line'], ['text-decoration-style'], ['text-decoration-thickness'], ['text-emphasis'], ['text-emphasis-color'], ['text-emphasis-position'], ['text-emphasis-style'], ['text-indent'], ['text-justify'], ['text-orientation'], ['text-overflow'], ['text-shadow'], ['text-transform'], ['text-underline-offset'], ['text-underline-position'], ['top'], ['transform'], ['transform-origin'], ['transform-style'], ['transition'], ['transition-delay'], ['transition-duration'], ['transition-property'], ['transition-timing-function'], ['translate'], ['unicode-bidi'], ['user-select'], ['vertical-align'], ['visibility'], ['white-space'], ['widows'], ['width'], ['word-break'], ['word-spacing'], ['word-wrap'], ['writing-mode'], ['z-index'], ['zoom'],
  // Combinators
  ['>'], [' '], ['|'], ['+'], [','], ['~'],
  //Pseudo-classes
  ['active'], ['active'], ['any-link'], ['auto-fill'], ['checked'], ['default'], ['defined'], ['dir'], ['disabled'], ['empty'], ['enabled'], ['first'], ['first-child'], ['first-of-type'], ['focus'], ['focus-visible'], ['focus-within'], ['fullscreen'], ['has'], ['hover'], ['in-range'], ['indeterminate'], ['invalid'], ['is'], ['lang'], ['last-child'], ['last-of-type'], ['left'], ['link'], ['modal'], ['not'], ['nth-child'], ['nth-last-child'], ['nth-last-of-type'], ['nth-of-type'], ['only-child'], ['only-of-type'], ['optional'], ['out-of-range'], ['placeholder-shown'], ['popover-open'], ['read-only'], ['read-write'], ['required'], ['right'], ['root'], ['scope'], ['state'], ['target'], ['user-invalid'], ['user-valid'], ['valid'], ['visited'], ['where'],
  //Pseudo-elements
  ['after'], ['backdro'], ['before'], ['file-selector-button'], ['first-letter'], ['first-line'], ['grammar-error'], ['highlight'], ['marker'], ['placeholder'], ['selection'], ['spelling-error'], ['view-transition'], ['view-transition-group'], ['view-transition-image-pair'], ['view-transition-new'], ['view-transition-old'],
  //Functions
  ['acos'], ['asin'], ['atan'], ['atan2'], ['attr'], ['blur'], ['brightness'], ['calc'], ['circle'], ['clamp'], ['color'], ['color-mix'], ['conic-gradient'], ['contrast'], ['cos'], ['counter'], ['counters'], ['cubic-bezier'], ['drop-shadow'], ['ellipse'], ['exp'], ['fit-content'], ['grayscale'], ['hsl'], ['hsla'], ['hue-rotate'], ['hwb'], ['hypot'], ['inset'], ['invert'], ['lab'], ['lch'], ['light-dark'], ['linear-gradient'], ['log'], ['matrix'], ['matrix3d'], ['max'], ['min'], ['minmax'], ['mod'], ['oklab'], ['oklch'], ['opacity'], ['perspective'], ['polygon'], ['pow'], ['radial-gradient'], ['ray'], ['rem'], ['repeat'], ['repeating-conic-gradient'], ['repeating-linear-gradient'], ['repeating-radial-gradient'], ['rgb'], ['rgba'], ['rotate'], ['rotate3d'], ['rotateX'], ['rotateY'], ['rotateZ'], ['round'], ['saturate'], ['scale'], ['scale3d'], ['scaleX'], ['scaleY'], ['sepia'], ['sin'], ['skew'], ['skewX'], ['skewY'], ['sqrt'], ['steps'], ['tan'], ['translate'], ['translateX'], ['translateY'], ['url'], ['var'],
  //Reference Aural
  ['azimuth'], ['cue'], ['cue-after'], ['cue-befor'], ['elevation'], ['pause'], ['pause-after'], ['pause-before'], ['pitch'], ['pitch-range'], ['play-during'], ['richness'], ['speak'], ['speak-header'], ['speak-numeral'], ['speak-punctuation'], ['speech-rate'], ['stress'], ['voice-family'], ['volume'],
  // Units
  ['cm'], ['mm'], ['in'], ['px'], ['pt'], ['pc'], ['em'], ['ex'], ['ch'], ['rem'], ['vw'], ['vh'], ['vmin'], ['vmax'], ['%'],
  //Color names
  ['aliceblue'], ['antiquewhite'], ['aqua'], ['aquamarine'], ['azure'], ['beige'], ['bisque'], ['black'], ['blanchedalmond'], ['blue'], ['blueviolet'], ['brown'], ['burlywood'], ['cadetblue'], ['chartreuse'], ['chocolate'], ['coral'], ['cornflowerblue'], ['cornsilk'], ['crimson'], ['cyan'], ['darkblue'], ['darkcyan'], ['darkgoldenrod'], ['darkgray'], ['darkgrey'], ['darkgreen'], ['darkkhaki'], ['darkmagenta'], ['darkolivegreen'], ['darkorange'], ['darkorchid'], ['darkred'], ['darksalmon'], ['darkseagreen'], ['darkslateblue'], ['darkslategray'], ['darkslategrey'], ['darkturquoise'], ['darkviolet'], ['deeppink'], ['deepskyblue'], ['dimgray'], ['dimgrey'], ['dodgerblue'], ['firebrick'], ['floralwhite'], ['forestgreen'], ['fuchsia'], ['gainsboro'], ['ghostwhite'], ['gold'], ['goldenrod'], ['gray'], ['grey'], ['green'], ['greenyellow'], ['honeydew'], ['hotpink'], ['indianred'], ['indigo'], ['ivory'], ['khaki'], ['lavender'], ['lavenderblush'], ['lawngreen'], ['lemonchiffon'], ['lightblue'], ['lightcoral'], ['lightcyan'], ['lightgoldenrodyellow'], ['lightgray'], ['lightgrey'], ['lightgreen'], ['lightpink'], ['lightsalmon'], ['lightseagreen'], ['lightskyblue'], ['lightslategray'], ['lightslategrey'], ['lightsteelblue'], ['lightyellow'], ['lime'], ['limegreen'], ['linen'], ['magenta'], ['maroon'], ['mediumaquamarine'], ['mediumblue'], ['mediumorchid'], ['mediumpurple'], ['mediumseagreen'], ['mediumslateblue'], ['mediumspringgreen'], ['mediumturquoise'], ['mediumvioletred'], ['midnightblue'], ['mintcream'], ['mistyrose'], ['moccasin'], ['navajowhite'], ['navy'], ['oldlace'], ['olive'], ['olivedrab'], ['orange'], ['orangered'], ['orchid'], ['palegoldenrod'], ['palegreen'], ['paleturquoise'], ['palevioletred'], ['papayawhip'], ['peachpuff'], ['peru'], ['pink'], ['plum'], ['powderblue'], ['purple'], ['rebeccapurple'], ['red'], ['rosybrown'], ['royalblue'], ['saddlebrown'], ['salmon'], ['sandybrown'], ['seagreen'], ['seashell'], ['sienna'], ['silver'], ['skyblue'], ['slateblue'], ['slategray'], ['slategrey'], ['snow'], ['springgreen'], ['steelblue'], ['tan'], ['teal'], ['thistle'], ['tomato'], ['turquoise'], ['violet'], ['wheat'], ['white'], ['whitesmoke'], ['yellow'], ['yellowgreen']]
};

/***/ }),

/***/ 2896:
/*!*******************************************************!*\
  !*** ./src/app/terminals/bashbrawl/languages/html.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   htmlConfig: () => (/* binding */ htmlConfig)
/* harmony export */ });
/** Taken from https://github.com/CommandLineHeroes/clh-bash/tree/master/assets/cmds **/
const htmlConfig = {
  name: 'HTML5',
  cmds: [['html'], ['head'], ['title'], ['base'], ['link'], ['meta'], ['style'], ['body'], ['article'], ['section'], ['nav'], ['aside'], ['h1'], ['h2'], ['h3'], ['h4'], ['h5'], ['h6'], ['header'], ['footer'], ['p'], ['address'], ['hr'], ['pre'], ['blockquote'], ['ol'], ['ul'], ['li'], ['dl'], ['dt'], ['dd'], ['figure'], ['figcaption'], ['main'], ['div'], ['a'], ['em'], ['strong'], ['small'], ['s'], ['cite'], ['q'], ['dfn'], ['abbr'], ['ruby'], ['rb'], ['rt'], ['rtc'], ['rp'], ['data'], ['time'], ['code'], ['var'], ['samp'], ['kbd'], ['sub'], ['sup'], ['i'], ['b'], ['u'], ['mark'], ['bdi'], ['bdo'], ['span'], ['br'], ['wbr'], ['ins'], ['del'], ['picture'], ['source'], ['img'], ['iframe'], ['embed'], ['object'], ['param'], ['video'], ['audio'], ['track'], ['map'], ['area'], ['table'], ['caption'], ['colgroup'], ['col'], ['tbody'], ['thead'], ['tfoot'], ['tr'], ['td'], ['th'], ['form'], ['label'], ['input'], ['button'], ['select'], ['datalist'], ['optgroup'], ['option'], ['textarea'], ['output'], ['progress'], ['meter'], ['fieldset'], ['legend'], ['details'], ['summary'], ['dialog'], ['script'], ['noscript'], ['template'], ['canvas'], ['slot'], ['hr'], ['fieldset'], ['legend'], ['button'], ['details'], ['summary'], ['marquee'], ['meter'], ['progress'], ['select'], ['textarea'], ['marquee']]
};

/***/ }),

/***/ 7414:
/*!*************************************************************!*\
  !*** ./src/app/terminals/bashbrawl/languages/javascript.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   javascriptConfig: () => (/* binding */ javascriptConfig)
/* harmony export */ });
const javascriptConfig = {
  name: 'javascript',
  cmds: [
  // keywords
  ['await'], ['break'], ['case'], ['catch'], ['class'], ['const'], ['continue'], ['debugger'], ['default'], ['delete'], ['do'], ['else'], ['export'], ['extends'], ['finally'], ['for'], ['function'], ['if'], ['import'], ['in'], ['instanceof'], ['new'], ['return'], ['super'], ['switch'], ['this'], ['throw'], ['try'], ['typeof'], ['var'], ['void'], ['while'], ['with'], ['yield'],
  // some literals
  ['null'], ['true'], ['false'],
  // global object properties
  ['Infinity'], ['NaN'], ['undefined'], ['eval'], ['isFinite'], ['isNaN'], ['parseFloat'], ['parseInt'], ['decodeURI'], ['decodeURIComponent'], ['encodeURI'], ['encodeURIComponent'], ['Array'], ['ArrayBuffer'], ['Boolean'], ['DataView'], ['Date'], ['Error'], ['EvalError'], ['Float32Array'], ['Float64Array'], ['Function'], ['Int8Array'], ['Int16Array'], ['Int32Array'], ['Map'], ['Number'], ['Object'], ['Promise'], ['Proxy'], ['RangeError'], ['ReferenceError'], ['RegExp'], ['Set'], ['SharedArrayBuffer'], ['String'], ['Symbol'], ['SyntaxError'], ['TypeError'], ['Uint8Array'], ['Uint8ClampedArray'], ['Uint16Array'], ['Uint32Array'], ['URIError'], ['WeakMap'], ['WeakSet'],
  // fundamental objects (ch 19)
  ['Object'], ['Function'], ['Boolean'], ['Symbol'], ['Error'],
  // numbers and dates (ch 20)
  ['Number'], ['Math'], ['Date'],
  // text processing (ch 21)
  ['String'], ['RegExp'],
  // indexed collections (ch 22)
  ['Array'],
  // keyed collections (ch 23)
  ['Map'], ['Set'], ['WeakMap'], ['WeakSet'],
  // structured data (ch 24)
  ['ArrayBuffer'], ['SharedArrayBuffer'], ['DataView'], ['Atomics'], ['JSON'],
  // control abstraction objects (ch 25)
  ['Generator'], ['AsyncGenerator'], ['Promise'],
  // reflection (ch 26)
  ['Reflect'], ['Proxy'],
  // some curiously hard to find ones in the spec
  ['async'], ['let'], ['static'], ['else'], ['document'], ['window'], ['navigator'], ['then'], ['set'], ['get'], ['of'],
  // Object.keys(window) in chrome
  ['postMessage'], ['blur'], ['focus'], ['close'], ['parent'], ['opener'], ['top'], ['length'], ['frames'], ['closed'], ['location'], ['self'], ['window'], ['document'], ['name'], ['customElements'], ['history'], ['locationbar'], ['menubar'], ['personalbar'], ['scrollbars'], ['statusbar'], ['toolbar'], ['status'], ['frameElement'], ['navigator'], ['origin'], ['external'], ['screen'], ['innerWidth'], ['innerHeight'], ['scrollX'], ['pageXOffset'], ['scrollY'], ['pageYOffset'], ['visualViewport'], ['screenX'], ['screenY'], ['outerWidth'], ['outerHeight'], ['devicePixelRatio'], ['clientInformation'], ['screenLeft'], ['screenTop'], ['defaultStatus'], ['defaultstatus'], ['styleMedia'], ['onanimationend'], ['onanimationiteration'], ['onanimationstart'], ['onsearch'], ['ontransitionend'], ['onwebkitanimationend'], ['onwebkitanimationiteration'], ['onwebkitanimationstart'], ['onwebkittransitionend'], ['isSecureContext'], ['onabort'], ['onblur'], ['oncancel'], ['oncanplay'], ['oncanplaythrough'], ['onchange'], ['onclick'], ['onclose'], ['oncontextmenu'], ['oncuechange'], ['ondblclick'], ['ondrag'], ['ondragend'], ['ondragenter'], ['ondragleave'], ['ondragover'], ['ondragstart'], ['ondrop'], ['ondurationchange'], ['onemptied'], ['onended'], ['onerror'], ['onfocus'], ['oninput'], ['oninvalid'], ['onkeydown'], ['onkeypress'], ['onkeyup'], ['onload'], ['onloadeddata'], ['onloadedmetadata'], ['onloadstart'], ['onmousedown'], ['onmouseenter'], ['onmouseleave'], ['onmousemove'], ['onmouseout'], ['onmouseover'], ['onmouseup'], ['onmousewheel'], ['onpause'], ['onplay'], ['onplaying'], ['onprogress'], ['onratechange'], ['onreset'], ['onresize'], ['onscroll'], ['onseeked'], ['onseeking'], ['onselect'], ['onstalled'], ['onsubmit'], ['onsuspend'], ['ontimeupdate'], ['ontoggle'], ['onvolumechange'], ['onwaiting'], ['onwheel'], ['onauxclick'], ['ongotpointercapture'], ['onlostpointercapture'], ['onpointerdown'], ['onpointermove'], ['onpointerup'], ['onpointercancel'], ['onpointerover'], ['onpointerout'], ['onpointerenter'], ['onpointerleave'], ['onselectstart'], ['onselectionchange'], ['onafterprint'], ['onbeforeprint'], ['onbeforeunload'], ['onhashchange'], ['onlanguagechange'], ['onmessage'], ['onmessageerror'], ['onoffline'], ['ononline'], ['onpagehide'], ['onpageshow'], ['onpopstate'], ['onrejectionhandled'], ['onstorage'], ['onunhandledrejection'], ['onunload'], ['performance'], ['stop'], ['open'], ['alert'], ['confirm'], ['prompt'], ['print'], ['queueMicrotask'], ['requestAnimationFrame'], ['cancelAnimationFrame'], ['captureEvents'], ['releaseEvents'], ['requestIdleCallback'], ['cancelIdleCallback'], ['getComputedStyle'], ['matchMedia'], ['moveTo'], ['moveBy'], ['resizeTo'], ['resizeBy'], ['getSelection'], ['find'], ['webkitRequestAnimationFrame'], ['webkitCancelAnimationFrame'], ['fetch'], ['btoa'], ['atob'], ['setTimeout'], ['clearTimeout'], ['setInterval'], ['clearInterval'], ['createImageBitmap'], ['scroll'], ['scrollTo'], ['scrollBy'], ['onappinstalled'], ['onbeforeinstallprompt'], ['crypto'], ['ondevicemotion'], ['ondeviceorientation'], ['ondeviceorientationabsolute'], ['indexedDB'], ['webkitStorageInfo'], ['sessionStorage'], ['localStorage'], ['chrome'], ['speechSynthesis'], ['webkitRequestFileSystem'], ['webkitResolveLocalFileSystemURL'], ['openDatabase']]
};

/***/ }),

/***/ 4257:
/*!*************************************************************!*\
  !*** ./src/app/terminals/bashbrawl/languages/kubernetes.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   kubernetesConfig: () => (/* binding */ kubernetesConfig)
/* harmony export */ });
const kubernetesConfig = {
  name: 'Kubernetes',
  cmds: [
  // Basics
  ['kubectl', 'k'], ['crictl'],
  // API Methods
  ['get'], ['list'], ['watch'], ['update'], ['patch'], ['delete'], ['create'],
  // kubectl commands
  ['edit'], ['config'], ['apply'], ['explain'], ['describe'], ['diff'], ['rollout'], ['set'], ['label'], ['replace'], ['annotate'], ['autoscale'], ['scale'], ['logs'], ['attach'], ['port-forward'], ['exec'], ['debug'], ['top'], ['cp'], ['cordon'], ['drain'], ['uncordon'], ['cluster-info'], ['taint'],
  // API Versions and Manifests
  ['v1'], ['v1alpha1'], ['v1beta1'], ['apps/v1'], ['apps'], ['batch/v1'], ['batch'], ['k8s.io'], ['apiVersion'], ['kind'], ['name'], ['label'], ['apigroup'], ['verb'], ['verbs'], ['meta'], ['metadata'], ['spec'], ['annotations'], ['annotation'], ['status'],
  // API Resources
  ['binding', 'bindings'], ['componentstatus', 'componentstatuses', 'cs'], ['configmap', 'configmaps', 'cm'], ['deployment', 'deployments', 'deploy'], ['endpoint', 'endpoints', 'ep'], ['event', 'events', 'ev'], ['limitrange', 'limitranges', 'limit'], ['namespace', 'namespaces', 'ns'], ['node', 'nodes', 'no'], ['persistentvolumeclaim', 'persistentvolumeclaims', 'pvc'], ['persistentvolume', 'persistentvolumes', 'pv'], ['pod', 'pods', 'po'], ['podtemplate', 'podtemplates'], ['replicationcontroller', 'replicationcontrollers', 'rc'], ['resourcequota', 'resourcequotas', 'quota'], ['secret', 'secrets'], ['serviceaccounts', 'serviceaccount', 'sa'], ['svc', 'services', 'service'], ['mutatingwebhookconfiguration', 'mutatingwebhookconfigurations'], ['validatingwebhookconfiguration', 'validatingwebhookconfigurations'], ['customresourcedefinition', 'customresourcedefinitions', 'crd', 'crds'], ['controllerrevision', 'controllerrevisions'], ['daemonset', 'daemonsets', 'ds'], ['replicaset', 'replicasets', 'rs'], ['statefulset', 'statefulsets', 'sts'], ['tokenreview', 'tokenreviews'], ['localsubjectaccesreview', 'localsubjectaccesreviews'], ['selfsubjectaccessreview', 'selfsubjectaccessreviews'], ['selfsubjectrulesreview', 'selfsubjectrulesreviews'], ['subjectaccessreview', 'subjectaccessreviews'], ['horizontalpodautoscaler', 'horizontalpodautoscalers', 'hpa', 'autoscaler'], ['cronjob', 'cronjobs', 'cj'], ['job', 'jobs'], ['endpointslice', 'endpointslices'], ['crontab', 'crontabs', 'ct'], ['ingressclass', 'ingressclasses'], ['ingress', 'ingresses', 'ing'], ['networkpolicy', 'netpol', 'networkpolicies'], ['runtimeclass', 'runtimeclasses'], ['poddisruptionbudget', 'poddisruptionbudgets', 'pdb'], ['clusterrolebinding', 'clusterrolebindings'], ['clusterrole', 'clusterroles'], ['rolebinding', 'rolebindings'], ['role', 'roles'], ['priorityclass', 'priorityclasses'], ['csidriver', 'csidrivers'], ['csinode', 'csinodes'], ['csistoragecapacitiy', 'csistoragecapacities'], ['storageclass', 'storageclasses', 'sc'], ['volumeattachement', 'volumeattachements'],
  // Fields
  ['data'], ['type'], ['creationtimestamp'], ['selector'], ['rollingupdate'], ['strategytype'], ['condition'], ['state'], ['mounts'], ['backend'], ['path'], ['paths'], ['http'], ['https'], ['protocol'], ['port'], ['loadbalancer'], ['loadbalancers'], ['uid'], ['resourceversion'], ['ClusterIP'], ['NodePort'], ['LoadBalancer'], ['ExternalName'], ['headless'], ['egress'], ['matchlabel'], ['matchlabels'], ['podselector'], ['namespaceselector'], ['ipBlock'], ['policyTypes'], ['routing'], ['router'], ['routingrule'], ['scope'], ['affinity'], ['affinities'], ['probe'], ['crio'], ['cri-o'],
  // Companies,
  ['cncf'], ['tanzu'], ['gke'], ['aks'], ['rke'], ['rancher'], ['rke2'], ['openshift'], ['kubermatic'], ['k8c'], ['eks'], ['k0s'], ['k3d'], ['microk8s'], ['traefik'], ['nginx'], ['minikube'],
  // other,
  ['image'], ['container'], ['containers'], ['cluster'], ['clusters'], ['runtime'], ['controller'], ['controllers'], ['docker'], ['dockershim'], ['ephemeral'], ['finalizer'], ['yaml'], ['manifest'], ['yml'], ['grpc'], ['webhook'], ['autoscaler'], ['rbac'], ['rule'], ['hostname'], ['wildcard'], ['workload'], ['workloads'], ['csi'], ['cni'], ['interface'], ['storage'], ['servicemesh'], ['microservice'], ['microservices'], ['sig'], ['init'], ['initcontainer'], ['sidecar'], ['healthcheck'], ['static'], ['toleration'], ['eviction'], ['cgroup'], ['cgroups'], ['containerd'], ['dockerd'], ['kubeadm'], ['sysctl'], ['kubeinvaders'],
  // other tools
  ['k9s'], ['loki'], ['thanos'], ['grafana'], ['istio'], ['helm'], ['chart'], ['helmchart'], ['lens'], ['catalog'], ['kompose'], ['portainer'], ['KEDA'], ['vcluster'], ['Jaeger'], ['Kiali'], ['ELK'], ['fluentbit'], ['fluentd'], ['promtail'], ['terraform'], ['ansible'], ['Jaeger'], ['tekton'], ['argo'], ['arcocd'], ['kyverno'], ['falco'], ['vault'], ['calico'], ['cilium'], ['canico'], ['metallb'], ['kong'], ['longhorn'], ['trivy'], ['rook'], ['ebs'], ['openebs'], ['knative']
  // Kubernetes release names
  ]
};

/***/ }),

/***/ 3818:
/*!***************************************************************************!*\
  !*** ./src/app/terminals/bashbrawl/languages/language-command.service.ts ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageCommandService: () => (/* binding */ LanguageCommandService)
/* harmony export */ });
/* harmony import */ var _html__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./html */ 2896);
/* harmony import */ var _kubernetes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./kubernetes */ 4257);
/* harmony import */ var _bash__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./bash */ 9187);
/* harmony import */ var _python__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./python */ 4871);
/* harmony import */ var _javascript__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./javascript */ 7414);
/* harmony import */ var _php__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./php */ 3917);
/* harmony import */ var _css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./css */ 7266);
/* harmony import */ var _mysql__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./mysql */ 755);
/* harmony import */ var _csharp__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./csharp */ 4057);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/core */ 7580);










class LanguageCommandService {
  constructor() {
    this.commands = {
      html: _html__WEBPACK_IMPORTED_MODULE_0__.htmlConfig,
      kubernetes: _kubernetes__WEBPACK_IMPORTED_MODULE_1__.kubernetesConfig,
      bash: _bash__WEBPACK_IMPORTED_MODULE_2__.bashConfig,
      python: _python__WEBPACK_IMPORTED_MODULE_3__.pythonConfig,
      javascript: _javascript__WEBPACK_IMPORTED_MODULE_4__.javascriptConfig,
      php: _php__WEBPACK_IMPORTED_MODULE_5__.phpConfig,
      css: _css__WEBPACK_IMPORTED_MODULE_6__.cssConfig,
      mysql: _mysql__WEBPACK_IMPORTED_MODULE_7__.mysqlConfig,
      csharp: _csharp__WEBPACK_IMPORTED_MODULE_8__.csharpConfig
      // other languages can be added here
    };
  }

  find(cmd, language) {
    const result = {
      cmd: '',
      lang: [],
      found: false
    };
    cmd = cmd.trim(); // Trim the command once outside the loop
    cmd = cmd.toLowerCase();
    if (language && language != 'all') {
      // Only one language specified
      this.getLanguageById(language).cmds.forEach(command => {
        // If command is an array, check if the trimmed command matches any command in the array
        if (command.includes(cmd)) {
          result.cmd = command[0]; // Set result.cmd to the first command in the array
          result.lang.push(language);
          result.found = true;
        }
      });
    } else {
      // Loop through each language's command list
      for (const lang of this.getEnabledLanguageKeys()) {
        // Iterate over each command or command array in the command list
        this.getLanguageById(lang).cmds.forEach(command => {
          // If command is an array, check if the trimmed command matches any command in the array
          if (command.includes(cmd)) {
            result.cmd = command[0]; // Set result.cmd to the first command in the array
            result.lang.push(lang);
            result.found = true;
          }
        });
      }
    }
    return result;
  }
  getLanguageNames() {
    const languages = [];
    this.getEnabledLanguageKeys().forEach(element => {
      languages.push(this.getLanguageNameById(element));
    });
    return languages;
  }
  getAllLanguageKeys() {
    return Object.keys(this.commands);
  }
  getEnabledLanguageKeys() {
    const languages = [];
    Object.keys(this.commands).forEach(element => {
      if (this.isEnabled(element)) {
        languages.push(element);
      }
    });
    return languages;
  }
  getLanguageById(language) {
    return this.commands[language] ?? {};
  }
  getLanguageNameById(language) {
    return this.getAllLanguageKeys().includes(language) ? this.getLanguageById(language).name : language.toUpperCase();
  }
  isEnabled(language) {
    const configEnabled = localStorage.getItem('enabled_' + language);
    if (configEnabled && configEnabled == 'false') {
      return false;
    }
    return true;
  }
  static {
    this.ɵfac = function LanguageCommandService_Factory(t) {
      return new (t || LanguageCommandService)();
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdefineInjectable"]({
      token: LanguageCommandService,
      factory: LanguageCommandService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 755:
/*!********************************************************!*\
  !*** ./src/app/terminals/bashbrawl/languages/mysql.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   mysqlConfig: () => (/* binding */ mysqlConfig)
/* harmony export */ });
/** Taken from https://dev.mysql.com/doc/refman/8.4/en/keywords.html **/
const mysqlConfig = {
  name: 'MySQL',
  cmds: [['accessible'], ['account'], ['action'], ['active'], ['add'], ['admin'], ['after'], ['against'], ['aggregate'], ['algorithm'], ['all'], ['alter'], ['always'], ['analyze'], ['and'], ['any'], ['array'], ['as'], ['asc'], ['ascii'], ['asensitive'], ['at'], ['attribute'], ['authentication'], ['auto'], ['autoextend_size'], ['auto_increment'], ['avg'], ['avg_row_length'], ['backup'], ['before'], ['begin'], ['bernoulli'], ['between'], ['bigint'], ['binary'], ['binlog'], ['bit'], ['blob'], ['block'], ['bool'], ['boolean'], ['both'], ['btree'], ['buckets'], ['bulk'], ['by'], ['byte'], ['cache'], ['call'], ['cascade'], ['cascaded'], ['case'], ['catalog_name'], ['chain'], ['challenge_response'], ['change'], ['changed'], ['channel'], ['char'], ['character'], ['charset'], ['check'], ['checksum'], ['cipher'], ['class_origin'], ['client'], ['clone'], ['close'], ['coalesce'], ['code'], ['collate'], ['collation'], ['column'], ['columns'], ['column_format'], ['column_name'], ['comment'], ['commit'], ['committed'], ['compact'], ['completion'], ['component'], ['compressed'], ['compression'], ['concurrent'], ['condition'], ['connection'], ['consistent'], ['constraint'], ['constraint_catalog'], ['constraint_name'], ['constraint_schema'], ['contains'], ['context'], ['continue'], ['convert'], ['cpu'], ['create'], ['cross'], ['cube'], ['cume_dist'], ['current'], ['current_date'], ['current_time'], ['current_timestamp'], ['current_user'], ['cursor'], ['cursor_name'], ['data'], ['database'], ['databases'], ['datafile'], ['date'], ['datetime'], ['day'], ['day_hour'], ['day_microsecond'], ['day_minute'], ['day_second'], ['deallocate'], ['dec'], ['decimal'], ['declare'], ['default'], ['default_auth'], ['definer'], ['definition'], ['delayed'], ['delay_key_write'], ['delete'], ['dense_rank'], ['desc'], ['describe'], ['description'], ['deterministic'], ['diagnostics'], ['directory'], ['disable'], ['discard'], ['disk'], ['distinct'], ['distinctrow'], ['div'], ['do'], ['double'], ['drop'], ['dual'], ['dumpfile'], ['duplicate'], ['dynamic'], ['each'], ['else'], ['elseif'], ['empty'], ['enable'], ['enclosed'], ['encryption'], ['end'], ['ends'], ['enforced'], ['engine'], ['engines'], ['engine_attribute'], ['enum'], ['error'], ['errors'], ['escape'], ['escaped'], ['event'], ['events'], ['every'], ['except'], ['exchange'], ['exclude'], ['execute'], ['exists'], ['exit'], ['expansion'], ['expire'], ['explain'], ['export'], ['extended'], ['extent_size'], ['factor'], ['failed_login_attempts'], ['false'], ['fast'], ['faults'], ['fetch'], ['fields'], ['file'], ['file_block_size'], ['filter'], ['finish'], ['first'], ['first_value'], ['fixed'], ['float'], ['float4'], ['float8'], ['flush'], ['following'], ['follows'], ['for'], ['force'], ['foreign'], ['format'], ['found'], ['from'], ['full'], ['fulltext'], ['function'], ['general'], ['generate'], ['generated'], ['geomcollection'], ['geometry'], ['geometrycollection'], ['get'], ['get_format'], ['get_source_public_key'], ['global'], ['grant'], ['grants'], ['group'], ['grouping'], ['groups'], ['group_replication'], ['gtids'], ['gtid_only'], ['handler'], ['hash'], ['having'], ['help'], ['high_priority'], ['histogram'], ['history'], ['host'], ['hosts'], ['hour'], ['hour_microsecond'], ['hour_minute'], ['hour_second'], ['identified'], ['if'], ['ignore'], ['ignore_server_ids'], ['import'], ['in'], ['inactive'], ['index'], ['indexes'], ['infile'], ['initial'], ['initial_size'], ['initiate'], ['inner'], ['inout'], ['insensitive'], ['insert'], ['insert_method'], ['install'], ['instance'], ['int'], ['int1'], ['int2'], ['int3'], ['int4'], ['int8'], ['integer'], ['intersect'], ['interval'], ['into'], ['invisible'], ['invoker'], ['io'], ['io_after_gtids'], ['io_before_gtids'], ['io_thread'], ['ipc'], ['is'], ['isolation'], ['issuer'], ['iterate'], ['join'], ['json'], ['json_table'], ['json_value'], ['key'], ['keyring'], ['keys'], ['key_block_size'], ['kill'], ['lag'], ['language'], ['last'], ['last_value'], ['lateral'], ['lead'], ['leading'], ['leave'], ['leaves'], ['left'], ['less'], ['level'], ['like'], ['limit'], ['linear'], ['lines'], ['linestring'], ['list'], ['load'], ['local'], ['localtime'], ['localtimestamp'], ['lock'], ['locked'], ['locks'], ['log'], ['logfile'], ['logs'], ['long'], ['longblob'], ['longtext'], ['loop'], ['low_priority'], ['manual'], ['master'], ['match'], ['maxvalue'], ['max_connections_per_hour'], ['max_queries_per_hour'], ['max_rows'], ['max_size'], ['max_updates_per_hour'], ['max_user_connections'], ['medium'], ['mediumblob'], ['mediumint'], ['mediumtext'], ['member'], ['memory'], ['merge'], ['message_text'], ['microsecond'], ['middleint'], ['migrate'], ['minute'], ['minute_microsecond'], ['minute_second'], ['min_rows'], ['mod'], ['mode'], ['modifies'], ['modify'], ['month'], ['multilinestring'], ['multipoint'], ['multipolygon'], ['mutex'], ['mysql_errno'], ['name'], ['names'], ['national'], ['natural'], ['nchar'], ['ndb'], ['ndbcluster'], ['nested'], ['network_namespace'], ['never'], ['new'], ['next'], ['no'], ['nodegroup'], ['none'], ['not'], ['nowait'], ['no_wait'], ['no_write_to_binlog'], ['nth_value'], ['ntile'], ['null'], ['nulls'], ['number'], ['numeric'], ['nvarchar'], ['of'], ['off'], ['offset'], ['oj'], ['old'], ['on'], ['one'], ['only'], ['open'], ['optimize'], ['optimizer_costs'], ['option'], ['optional'], ['optionally'], ['options'], ['or'], ['order'], ['ordinality'], ['organization'], ['others'], ['out'], ['outer'], ['outfile'], ['over'], ['owner'], ['pack_keys'], ['page'], ['parallel'], ['parser'], ['parse_tree'], ['partial'], ['partition'], ['partitioning'], ['partitions'], ['password'], ['password_lock_time'], ['path'], ['percent_rank'], ['persist'], ['persist_only'], ['phase'], ['plugin'], ['plugins'], ['plugin_dir'], ['point'], ['polygon'], ['port'], ['precedes'], ['preceding'], ['precision'], ['prepare'], ['preserve'], ['prev'], ['primary'], ['privileges'], ['privilege_checks_user'], ['procedure'], ['process'], ['processlist'], ['profile'], ['profiles'], ['proxy'], ['purge'], ['qualify'], ['quarter'], ['query'], ['quick'], ['random'], ['range'], ['rank'], ['read'], ['reads'], ['read_only'], ['read_write'], ['real'], ['rebuild'], ['recover'], ['recursive'], ['redo_buffer_size'], ['redundant'], ['reference'], ['references'], ['regexp'], ['registration'], ['relay'], ['relaylog'], ['relay_log_file'], ['relay_log_pos'], ['relay_thread'], ['release'], ['reload'], ['remove'], ['rename'], ['reorganize'], ['repair'], ['repeat'], ['repeatable'], ['replace'], ['replica'], ['replicas'], ['replicate_do_db'], ['replicate_do_table'], ['replicate_ignore_db'], ['replicate_ignore_table'], ['replicate_rewrite_db'], ['replicate_wild_do_table'], ['replicate_wild_ignore_table'], ['replication'], ['require'], ['require_row_format'], ['reset'], ['resignal'], ['resource'], ['respect'], ['restart'], ['restore'], ['restrict'], ['resume'], ['retain'], ['return'], ['returned_sqlstate'], ['returning'], ['returns'], ['reuse'], ['reverse'], ['revoke'], ['right'], ['rlike'], ['role'], ['rollback'], ['rollup'], ['rotate'], ['routine'], ['row'], ['rows'], ['row_count'], ['row_format'], ['row_number'], ['rtree'], ['s3'], ['savepoint'], ['schedule'], ['schema'], ['schemas'], ['schema_name'], ['second'], ['secondary'], ['secondary_engine'], ['secondary_engine_attribute'], ['secondary_load'], ['secondary_unload'], ['second_microsecond'], ['security'], ['select'], ['sensitive'], ['separator'], ['serial'], ['serializable'], ['server'], ['session'], ['set'], ['share'], ['show'], ['shutdown'], ['signal'], ['signed'], ['simple'], ['skip'], ['slave'], ['slow'], ['smallint'], ['snapshot'], ['socket'], ['some'], ['soname'], ['sounds'], ['source'], ['source_auto_position'], ['source_bind'], ['source_compression_algorithms'], ['source_connect_retry'], ['source_delay'], ['source_heartbeat_period'], ['source_host'], ['source_log_file'], ['source_log_pos'], ['source_password'], ['source_port'], ['source_public_key_path'], ['source_retry_count'], ['source_ssl'], ['source_ssl_ca'], ['source_ssl_capath'], ['source_ssl_cert'], ['source_ssl_cipher'], ['source_ssl_crl'], ['source_ssl_crlpath'], ['source_ssl_key'], ['source_ssl_verify_server_cert'], ['source_tls_ciphersuites'], ['source_tls_version'], ['source_user'], ['source_zstd_compression_level'], ['spatial'], ['specific'], ['sql'], ['sqlexception'], ['sqlstate'], ['sqlwarning'], ['sql_after_gtids'], ['sql_after_mts_gaps'], ['sql_before_gtids'], ['sql_big_result'], ['sql_buffer_result'], ['sql_calc_found_rows'], ['sql_no_cache'], ['sql_small_result'], ['sql_thread'], ['sql_tsi_day'], ['sql_tsi_hour'], ['sql_tsi_minute'], ['sql_tsi_month'], ['sql_tsi_quarter'], ['sql_tsi_second'], ['sql_tsi_week'], ['sql_tsi_year'], ['srid'], ['ssl'], ['stacked'], ['start'], ['starting'], ['starts'], ['stats_auto_recalc'], ['stats_persistent'], ['stats_sample_pages'], ['status'], ['stop'], ['storage'], ['stored'], ['straight_join'], ['stream'], ['string'], ['subclass_origin'], ['subject'], ['subpartition'], ['subpartitions'], ['super'], ['suspend'], ['swaps'], ['switches'], ['system'], ['table'], ['tables'], ['tablesample'], ['tablespace'], ['table_checksum'], ['table_name'], ['temporary'], ['temptable'], ['terminated'], ['text'], ['than'], ['then'], ['thread_priority'], ['ties'], ['time'], ['timestamp'], ['timestampadd'], ['timestampdiff'], ['tinyblob'], ['tinyint'], ['tinytext'], ['tls'], ['to'], ['trailing'], ['transaction'], ['trigger'], ['triggers'], ['true'], ['truncate'], ['type'], ['types'], ['unbounded'], ['uncommitted'], ['undefined'], ['undo'], ['undofile'], ['undo_buffer_size'], ['unicode'], ['uninstall'], ['union'], ['unique'], ['unknown'], ['unlock'], ['unregister'], ['unsigned'], ['until'], ['update'], ['upgrade'], ['url'], ['usage'], ['use'], ['user'], ['user_resources'], ['use_frm'], ['using'], ['utc_date'], ['utc_time'], ['utc_timestamp'], ['validation'], ['value'], ['values'], ['varbinary'], ['varchar'], ['varcharacter'], ['variables'], ['varying'], ['vcpu'], ['view'], ['virtual'], ['visible'], ['wait'], ['warnings'], ['week'], ['weight_string'], ['when'], ['where'], ['while'], ['window'], ['with'], ['without'], ['work'], ['wrapper'], ['write'], ['x509'], ['xa'], ['xid'], ['xml'], ['xor'], ['year'], ['year_month'], ['zerofill'], ['zone']]
};

/***/ }),

/***/ 3917:
/*!******************************************************!*\
  !*** ./src/app/terminals/bashbrawl/languages/php.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   phpConfig: () => (/* binding */ phpConfig)
/* harmony export */ });
const phpConfig = {
  name: 'PHP 8',
  cmds: [
  // keywords
  ['and'], ['array'], ['as'], ['break'], ['callable'], ['case'], ['catch'], ['class'], ['clone'], ['const'], ['continue'], ['declare'], ['default'], ['die'], ['do'], ['echo'], ['else'], ['elseif'], ['empty'], ['enddeclare'], ['endfor'], ['endforeach'], ['endif'], ['endswitch'], ['endwhile'], ['eval'], ['exit'], ['extends'], ['final'], ['finally'], ['for'], ['foreach'], ['function'], ['global'], ['goto'], ['if'], ['implements'], ['include'], ['include_once'], ['instanceof'], ['insteadof'], ['interface'], ['isset'], ['list'], ['namespace'], ['new'], ['or'], ['print'], ['private'], ['protected'], ['public'], ['require'], ['require_once'], ['return'], ['static'], ['switch'], ['throw'], ['trait'], ['try'], ['unset'], ['use'], ['var'], ['while'], ['xor'], ['yield'],
  // standard library functions
  ['abs'], ['array_change_key_case'], ['array_chunk'], ['array_column'], ['array_combine'], ['array_count_values'], ['array_diff'], ['array_filter'], ['array_flip'], ['array_intersect'], ['array_key_exists'], ['array_keys'], ['array_map'], ['array_merge'], ['array_merge_recursive'], ['array_multisort'], ['array_pad'], ['array_pop'], ['array_push'], ['array_rand'], ['array_reduce'], ['array_reverse'], ['array_search'], ['array_shift'], ['array_splice'], ['array_sum'], ['array_udiff'], ['array_uintersect'], ['array_unique'], ['array_unshift'], ['array_values'], ['array_walk'], ['assert'], ['basename'], ['bcadd'], ['bcdiv'], ['bcmul'], ['bcpow'], ['bcsub'], ['bin2hex'], ['chmod'], ['chown'], ['chgrp'], ['clearstatcache'], ['closedir'], ['copy'], ['date'], ['dirname'], ['disk_free_space'], ['disk_total_space'], ['diskfreespace'], ['exec'], ['file'], ['file_get_contents'], ['file_put_contents'], ['fopen'], ['fread'], ['fwrite'], ['fclose'], ['fgets'], ['fgetcsv'], ['flock'], ['filesize'], ['filetype'], ['file_exists'], ['fileatime'], ['filectime'], ['filemtime'], ['fileinode'], ['finfo_file'], ['finfo_open'], ['flock'], ['getcwd'], ['gethostbyaddr'], ['gethostbyname'], ['gethostname'], ['getprotobyname'], ['getservbyname'], ['getservbyport'], ['getmypid'], ['getlastmod'], ['gettype'], ['http_build_query'], ['htmlspecialchars'], ['htmlspecialchars_decode'], ['implode'], ['in_array'], ['ini_get'], ['ini_set'], ['is_array'], ['is_bool'], ['is_callable'], ['is_dir'], ['is_file'], ['is_int'], ['is_numeric'], ['is_object'], ['is_readable'], ['is_writable'], ['is_writeable'], ['json_decode'], ['json_encode'], ['ksort'], ['mkdir'], ['move_uploaded_file'], ['parse_url'], ['pathinfo'], ['print_r'], ['readfile'], ['realpath'], ['rename'], ['rmdir'], ['setcookie'], ['setlocale'], ['sleep'], ['str_replace'], ['strlen'], ['strpos'], ['strtolower'], ['strtoupper'], ['strtr'], ['trim'], ['ucfirst'], ['var_dump'], ['version_compare'], ['vprintf'], ['xml_parser_create'], ['xml_parse'], ['xml_parse_into_struct'], ['xml_set_character_data_handler'], ['xml_set_element_handler'], ['xml_set_end_namespace_decl_handler'], ['xml_set_start_namespace_decl_handler'], ['xmlwriter_end_document'], ['xmlwriter_end_element'], ['xmlwriter_flush'], ['xmlwriter_open_memory'], ['xmlwriter_open_uri'], ['xmlwriter_start_attribute'], ['xmlwriter_start_document'], ['xmlwriter_start_element'], ['xmlwriter_start_element_ns'], ['xmlwriter_write_attribute'], ['xmlwriter_write_cdata'], ['xmlwriter_write_comment'], ['xmlwriter_write_element'], ['xmlwriter_write_element_ns'], ['xmlwriter_write_pi'], ['xmlwriter_write_raw'], ['xmlwriter_write_string']]
};

/***/ }),

/***/ 4871:
/*!*********************************************************!*\
  !*** ./src/app/terminals/bashbrawl/languages/python.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   pythonConfig: () => (/* binding */ pythonConfig)
/* harmony export */ });
/** Taken from https://github.com/CommandLineHeroes/clh-bash/tree/master/assets/cmds **/
const pythonConfig = {
  name: 'Python',
  cmds: [
  // keywords
  ['False'], ['class'], ['finally'], ['is'], ['return'], ['None'], ['continue'], ['for'], ['lambda'], ['try'], ['True'], ['def'], ['from'], ['nonlocal'], ['while'], ['and'], ['del'], ['global'], ['not'], ['with'], ['as'], ['elif'], ['if'], ['or'], ['yield'], ['assert'], ['else'], ['import'], ['pass'], ['break'], ['except'], ['in'], ['raise'],
  // funtions
  ['abs()'], ['delattr()'], ['hash()'], ['memoryview()'], ['set()'], ['all()'], ['dict()'], ['help()'], ['min()'], ['setattr()'], ['any()'], ['dir()'], ['hex()'], ['next()'], ['slice()'], ['ascii()'], ['divmod()'], ['id()'], ['object()'], ['sorted()'], ['bin()'], ['enumerate()'], ['input()'], ['oct()'], ['staticmethod()'], ['bool()'], ['eval()'], ['int()'], ['open()'], ['str()'], ['breakpoint()'], ['exec()'], ['isinstance()'], ['ord()'], ['sum()'], ['bytearray()'], ['filter()'], ['issubclass()'], ['pow()'], ['super()'], ['bytes()'], ['float()'], ['iter()'], ['print()'], ['tuple()'], ['callable()'], ['format()'], ['len()'], ['property()'], ['type()'], ['chr()'], ['frozenset()'], ['list()'], ['range()'], ['vars()'], ['classmethod()'], ['getattr()'], ['locals()'], ['repr()'], ['zip()'], ['compile()'], ['globals()'], ['map()'], ['reversed()'], ['__import__()'], ['complex()'], ['hasattr()'], ['max()'], ['round()'],
  // functions without parens
  ['abs'], ['delattr'], ['hash'], ['memoryview'], ['set'], ['all'], ['dict'], ['help'], ['min'], ['setattr'], ['any'], ['dir'], ['hex'], ['next'], ['slice'], ['ascii'], ['divmod'], ['id'], ['object'], ['sorted'], ['bin'], ['enumerate'], ['input'], ['oct'], ['staticmethod'], ['bool'], ['eval'], ['int'], ['open'], ['str'], ['breakpoint'], ['exec'], ['isinstance'], ['ord'], ['sum'], ['bytearray'], ['filter'], ['issubclass'], ['pow'], ['super'], ['bytes'], ['float'], ['iter'], ['print'], ['tuple'], ['callable'], ['format'], ['len'], ['property'], ['type'], ['chr'], ['frozenset'], ['list'], ['range'], ['vars'], ['classmethod'], ['getattr'], ['locals'], ['repr'], ['zip'], ['compile'], ['globals'], ['map'], ['reversed'], ['__import__'], ['complex'], ['hasattr'], ['max'], ['round']]
};

/***/ }),

/***/ 3092:
/*!********************************************************!*\
  !*** ./src/app/terminals/terminal-themes/BashBrawl.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  foreground: '#9cc2c3',
  background: '#01162a',
  cursor: '#74e2cd',
  black: '#002831',
  brightBlack: '#006488',
  red: '#d11c24',
  brightRed: '#f5163b',
  green: '#6cbe6c',
  brightGreen: '#51ef84',
  yellow: '#a57706',
  brightYellow: '#b27e28',
  blue: '#2176c7',
  brightBlue: '#178ec8',
  magenta: '#c61c6f',
  brightMagenta: '#e24d8e',
  cyan: '#259286',
  brightCyan: '#00b39e',
  white: '#eae3cb',
  brightWhite: '#fcf4dc'
});

/***/ }),

/***/ 737:
/*!**********************************************************!*\
  !*** ./src/app/terminals/terminal-themes/Dichromatic.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  background: '#000000',
  foreground: '#ffffff',
  cursor: '#ffffff',
  black: '#2e3436',
  brightBlack: '#555753',
  red: '#cc0000',
  brightRed: '#ef2929',
  green: '#008700',
  brightGreen: '#5FAF87',
  yellow: '#AFAF00',
  brightYellow: '#DFDF87',
  blue: '#00005F',
  brightBlue: '#729fcf',
  magenta: '#87005F',
  brightMagenta: '#ad7fa8',
  cyan: '#87D7D7',
  brightCyan: '#34e2e2',
  white: '#d3d7cf',
  brightWhite: '#FFFFFF'
});

/***/ }),

/***/ 4263:
/*!*****************************************************!*\
  !*** ./src/app/terminals/terminal-themes/GitHub.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  foreground: '#3e3e3e',
  background: '#f4f4f4',
  cursor: '#3f3f3f',
  cursorAccent: '#c7c7c7',
  black: '#3e3e3e',
  brightBlack: '#666666',
  red: '#970b16',
  brightRed: '#de0000',
  green: '#07962a',
  brightGreen: '#87d5a2',
  yellow: '#f8eec7',
  brightYellow: '#f1d007',
  blue: '#003e8a',
  brightBlue: '#2e6cba',
  magenta: '#e94691',
  brightMagenta: '#ffa29f',
  cyan: '#89d1ec',
  brightCyan: '#1cfafe',
  white: '#ffffff',
  brightWhite: '#ffffff'
});

/***/ }),

/***/ 748:
/*!****************************************************************!*\
  !*** ./src/app/terminals/terminal-themes/Hobbyfarm_Default.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  background: '#292b2e',
  foreground: '#ffffff',
  cursor: '#ffffff',
  cursorAccent: '#505459',
  black: '#2e3436',
  brightBlack: '#555753',
  red: '#cc0000',
  brightRed: '#ef2929',
  green: '#4e9a06',
  brightGreen: '#8ae234',
  yellow: '#c4a000',
  brightYellow: '#fce94f',
  blue: '#3465a4',
  brightBlue: '#729fcf',
  magenta: '#75507b',
  brightMagenta: '#ad7fa8',
  cyan: '#06989a',
  brightCyan: '#34e2e2',
  white: '#d3d7cf',
  brightWhite: '#eeeeec'
});

/***/ }),

/***/ 1740:
/*!*************************************************************!*\
  !*** ./src/app/terminals/terminal-themes/Solarized_Dark.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  foreground: '#708284',
  background: '#001e27',
  cursor: '#708284',
  black: '#002831',
  brightBlack: '#001e27',
  red: '#d11c24',
  brightRed: '#bd3613',
  green: '#738a05',
  brightGreen: '#475b62',
  yellow: '#a57706',
  brightYellow: '#536870',
  blue: '#2176c7',
  brightBlue: '#708284',
  magenta: '#c61c6f',
  brightMagenta: '#5956ba',
  cyan: '#259286',
  brightCyan: '#819090',
  white: '#eae3cb',
  brightWhite: '#fcf4dc'
});

/***/ }),

/***/ 9761:
/*!*****************************************************************************!*\
  !*** ./src/app/terminals/terminal-themes/Solarized_Dark_Higher_Contrast.ts ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  foreground: '#9cc2c3',
  background: '#001e27',
  cursor: '#f34b00',
  black: '#002831',
  brightBlack: '#006488',
  red: '#d11c24',
  brightRed: '#f5163b',
  green: '#6cbe6c',
  brightGreen: '#51ef84',
  yellow: '#a57706',
  brightYellow: '#b27e28',
  blue: '#2176c7',
  brightBlue: '#178ec8',
  magenta: '#c61c6f',
  brightMagenta: '#e24d8e',
  cyan: '#259286',
  brightCyan: '#00b39e',
  white: '#eae3cb',
  brightWhite: '#fcf4dc'
});

/***/ }),

/***/ 390:
/*!**************************************************************!*\
  !*** ./src/app/terminals/terminal-themes/Solarized_Light.ts ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  foreground: '#536870',
  background: '#fcf4dc',
  cursor: '#536870',
  cursorAccent: '#e3dcc7',
  black: '#002831',
  brightBlack: '#001e27',
  red: '#d11c24',
  brightRed: '#bd3613',
  green: '#738a05',
  brightGreen: '#475b62',
  yellow: '#a57706',
  brightYellow: '#536870',
  blue: '#2176c7',
  brightBlue: '#708284',
  magenta: '#c61c6f',
  brightMagenta: '#5956ba',
  cyan: '#259286',
  brightCyan: '#819090',
  white: '#eae3cb',
  brightWhite: '#fcf4dc'
});

/***/ }),

/***/ 1710:
/*!*****************************************************!*\
  !*** ./src/app/terminals/terminal-themes/themes.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   themes: () => (/* binding */ themes)
/* harmony export */ });
/* harmony import */ var _Hobbyfarm_Default__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Hobbyfarm_Default */ 748);
/* harmony import */ var _Solarized_Light__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Solarized_Light */ 390);
/* harmony import */ var _Solarized_Dark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Solarized_Dark */ 1740);
/* harmony import */ var _Solarized_Dark_Higher_Contrast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Solarized_Dark_Higher_Contrast */ 9761);
/* harmony import */ var _GitHub__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./GitHub */ 4263);
/* harmony import */ var _Dichromatic__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Dichromatic */ 737);
/* harmony import */ var _BashBrawl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./BashBrawl */ 3092);







// Themes taken from https://github.com/ysk2014/xterm-theme
const themes = [{
  id: 'BashBrawl',
  name: 'BashBrawl',
  styles: _BashBrawl__WEBPACK_IMPORTED_MODULE_6__["default"]
}, {
  id: 'default',
  name: 'Default Hobbyfarm Terminal',
  styles: _Hobbyfarm_Default__WEBPACK_IMPORTED_MODULE_0__["default"]
}, {
  id: 'Solarized_Light',
  name: 'Solarized Light',
  styles: _Solarized_Light__WEBPACK_IMPORTED_MODULE_1__["default"]
}, {
  id: 'Solarized_Dark',
  name: 'Solarized Dark',
  styles: _Solarized_Dark__WEBPACK_IMPORTED_MODULE_2__["default"]
}, {
  id: 'Solarized_Dark_Higher_Contrast',
  name: 'Solarized Dark Higher Contrast',
  styles: _Solarized_Dark_Higher_Contrast__WEBPACK_IMPORTED_MODULE_3__["default"]
}, {
  id: 'GitHub',
  name: 'GitHub',
  styles: _GitHub__WEBPACK_IMPORTED_MODULE_4__["default"]
}, {
  id: 'Dichromatic',
  name: 'Dichromatic',
  styles: _Dichromatic__WEBPACK_IMPORTED_MODULE_5__["default"]
}];

/***/ }),

/***/ 8801:
/*!****************************!*\
  !*** ./src/app/unicode.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   atou: () => (/* binding */ atou),
/* harmony export */   utoa: () => (/* binding */ utoa)
/* harmony export */ });
function atou(b64) {
  return decodeURIComponent(escape(atob(b64)));
}
function utoa(data) {
  return btoa(unescape(encodeURIComponent(data)));
}

/***/ }),

/***/ 5312:
/*!*****************************************!*\
  !*** ./src/environments/environment.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   environment: () => (/* binding */ environment)
/* harmony export */ });
const environment = {
  production: false,
  server: '',
  imprint: '',
  privacypolicy: ''
};

/***/ }),

/***/ 4429:
/*!*********************!*\
  !*** ./src/main.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/platform-browser */ 436);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _app_app_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app/app.module */ 635);
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./environments/environment */ 5312);




if (_environments_environment__WEBPACK_IMPORTED_MODULE_1__.environment.production) {
  (0,_angular_core__WEBPACK_IMPORTED_MODULE_2__.enableProdMode)();
}
_angular_platform_browser__WEBPACK_IMPORTED_MODULE_3__.platformBrowser().bootstrapModule(_app_app_module__WEBPACK_IMPORTED_MODULE_0__.AppModule).catch(err => console.error(err));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendor"], () => (__webpack_exec__(4429)));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=main.js.map