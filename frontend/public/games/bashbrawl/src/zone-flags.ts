/**
 * Zone.js flags to disable problematic patches
 */

// 禁用导致 "Cannot set property once" 错误的补丁
(window as any).__Zone_disable_addEventListener = true; // 禁用addEventListener补丁
(window as any).__Zone_disable_on_property = true; // 禁用onProperty补丁
(window as any).__zone_symbol__UNPATCHED_EVENTS = ['scroll', 'mousemove', 'click', 'load']; // 禁用特定事件补丁
(window as any).__Zone_enable_cross_context_check = true; // iframe环境兼容性
