{"name": "iframe-games-frontend", "private": true, "version": "1.0.0", "type": "module", "description": "现代化网页游戏集成站前端", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\""}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-dialog": "^1.1.0", "@radix-ui/react-dropdown-menu": "^2.1.0", "@radix-ui/react-select": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.0", "@radix-ui/react-tooltip": "^1.1.0", "@tanstack/react-query": "^5.59.0", "@tiptap/react": "^2.8.0", "@tiptap/starter-kit": "^2.8.0", "@types/three": "^0.178.1", "axios": "^1.7.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^4.1.0", "framer-motion": "^11.11.0", "lucide-react": "^0.460.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.2.0", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.0", "react-router": "^7.0.0", "react-router-dom": "^7.0.0", "recharts": "^2.12.0", "tailwind-merge": "^2.5.0", "tailwindcss-animate": "^1.0.7", "three": "^0.178.0", "xterm": "^5.3.0", "xterm-addon-canvas": "^0.12.0", "xterm-addon-fit": "^0.8.0", "zod": "^3.23.0", "zustand": "^5.0.0"}, "devDependencies": {"@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "@vitejs/plugin-react": "^4.3.0", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "postcss": "^8.4.49", "prettier": "^3.3.0", "tailwindcss": "^3.4.0", "typescript": "~5.6.0", "vite": "^6.0.0"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}}